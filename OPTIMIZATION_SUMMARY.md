# CRM Action 消息处理优化总结

## 问题分析

根据日志分析，发现了以下问题：

1. **限速器位置不合理**：限速器在消息处理的最前面被调用，即使是不需要处理的消息（如 `followSetting is null`）也要消耗限速配额
2. **限速配置过低**：默认 `crmActionLimit = 20 TPS`，`crmActionVIPLimit = 10 TPS`
3. **消息堆积**：16个消费线程但限速器限制每秒只能处理20个消息，导致消息堆积

## 优化方案

### 1. 调整限速器位置

**修改前**：
```java
// 所有消息都要先获取限速令牌
followRateLimiterService.getCrmActionRateLimiter().acquire();
log.info("CrmActionListener msgId:{},messageObj:{}", message.getMsgId(), messageObj);
if (followGrayUtils.isFollowSettingGray(messageObj.getTenantID())) {
    crmActionConsume.consumeMessageGray(messageObj);
}else{
    crmActionConsume.newConsumeMessage(messageObj);
}
```

**修改后**：
```java
log.info("CrmActionListener msgId:{},messageObj:{}", message.getMsgId(), messageObj);

// 先检查是否有跟进配置，避免无效消息消耗限速配额
if (crmActionConsume.hasFollowSetting(messageObj)) {
    // 只有需要处理的消息才进行限速
    followRateLimiterService.getCrmActionRateLimiter().acquire();
    crmActionConsume.consumeMessageGray(messageObj);
}
```

### 2. 移除灰度逻辑

- 移除了 `FollowGrayUtils.isFollowSettingGray()` 的调用
- 统一使用 `consumeMessageGray()` 方法处理消息
- 清理了相关的导入和依赖

### 3. 新增预检查方法

在 `CrmActionConsume` 中新增 `hasFollowSetting()` 方法：

```java
public boolean hasFollowSetting(CrmActionMQMessage messageObj) {
    String tenantId = messageObj.getTenantID();
    String describeApiName = messageObj.getObjectApiName();
    ActionCodeEnum actionCode = ActionCodeEnum.actionCodeOf(messageObj.getActionCode());
    
    if (actionCode == null) {
        return false;
    }
    
    Set<String> settingApiNames = followSettingCacheService.getFollowSetting(tenantId, describeApiName, actionCode);
    boolean hasAccountFollowSetting = settingApiNames != null && settingApiNames.contains(Utils.ACCOUNT_API_NAME);
    boolean hasLeadsFollowSetting = settingApiNames != null && settingApiNames.contains(Utils.LEADS_API_NAME);
    
    // 线索对象即使没有跟进配置也需要处理（计算预计收回时间）
    if (Utils.LEADS_API_NAME.equals(describeApiName)) {
        return true;
    }
    
    // 其他对象需要有跟进配置才处理
    return hasAccountFollowSetting || hasLeadsFollowSetting;
}
```

## 修改的文件

1. **CrmActionListener.java**
   - 移除 `FollowGrayUtils` 导入和依赖
   - 调整限速器调用位置
   - 移除灰度判断逻辑

2. **CrmActionVIPListener.java**
   - 同样的优化调整

3. **CrmActionConsume.java**
   - 新增 `hasFollowSetting()` 预检查方法

## 预期效果

1. **提高处理效率**：无效消息不再消耗限速配额，有效消息的处理速度提升
2. **减少消息堆积**：只有真正需要处理的消息才会被限速，整体吞吐量提升
3. **保持功能完整性**：线索对象的特殊处理逻辑得到保留

## 建议的后续优化

1. **调整限速配置**：根据实际业务量调整 `crmActionLimit` 从 20 提升到 100-200
2. **监控效果**：观察消息堆积情况和处理延迟的改善
3. **性能测试**：在测试环境验证优化效果

## 配置调整建议

在 `fs-crm-task-rate-limit` 配置中建议调整：
```properties
crmActionLimit=100  # 从默认20提升到100
crmActionVIPLimit=50  # 从默认10提升到50
```
