<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.facishare.crm.data.ext.dao.businessrisk.mapper.SFABIOptEntPartnerCnMapper">

    <resultMap id="BaseResultMap" type="com.facishare.crm.data.ext.dao.businessrisk.entity.SFABIOptEntPartnerCnEntity">
        <id property="id" column="id" />
        <result property="companyId" column="eid" />
        <result property="companyName" column="company_name" />
        <result property="stockName" column="stock_name" />
        <result property="stockNameId" column="stock_name_id" />
        <result property="stockProportion" column="stock_proportion" />
        <result property="stockCapital" column="stock_capital" />
        <result property="stockCapitalNum" column="stock_capital_num" />
        <result property="stockType" column="stock_type" />
    </resultMap>

    <select id="selectByCompanyId" parameterType="com.facishare.crm.data.ext.dto.SFABICompanyBaseDTO" resultMap="BaseResultMap">
        SELECT
               argMax(stock_name, bi_industry_version)        AS stock_name,
               argMax(stock_proportion, bi_industry_version)  AS stock_proportion,
               argMax(stock_capital, bi_industry_version)     AS stock_capital,
               argMax(stock_type, bi_industry_version)        AS stock_type,
               argMax(stock_capital_num, bi_industry_version) AS stock_capital_num
        FROM opt_ent_partner_cn oepc
        WHERE company_id = #{companyId}
          AND use_flag = 0
          AND is_history = 0
        GROUP BY company_id, oepc.stock_name
    </select>

    <select id="selectByParam" parameterType="com.facishare.crm.data.ext.dto.SFABICompanyBaseDTO" resultMap="BaseResultMap">
        SELECT
               argMax(id, bi_industry_version)        AS id,
               argMax(company_id, bi_industry_version)        AS eid,
               argMax(company_name, bi_industry_version)        AS company_name,
               argMax(stock_name, bi_industry_version)        AS stock_name,
               argMax(stock_name_id, bi_industry_version)        AS stock_name_id,
               argMax(stock_proportion, bi_industry_version)  AS stock_proportion,
               argMax(stock_capital, bi_industry_version)     AS stock_capital,
               argMax(stock_type, bi_industry_version)        AS stock_type,
               argMax(stock_capital_num, bi_industry_version) AS stock_capital_num
        FROM opt_ent_partner_cn oepc
        WHERE oepc.company_id = #{companyId}
          AND oepc.use_flag = 0
          AND oepc.is_history = 0
        <if test="searchKeyWord != null and searchKeyWord != ''">
            AND oepc.stock_name like CONCAT('%', #{searchKeyWord}, '%')
        </if>
        <if test="notShowId != null and notShowId.size() > 0">
            AND oepc.id NOT IN
            <foreach collection="notShowId" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY oepc.company_id, oepc.stock_name
            LIMIT ${from}, ${limit}
    </select>

    <select id="getTotalByParam" parameterType="com.facishare.crm.data.ext.dto.SFABICompanyBaseDTO" resultType="int">
        select count(1) from (
                                 SELECT  argMax(company_id, bi_industry_version)        AS eid
                                 FROM opt_ent_partner_cn oepc
                                 WHERE oepc.company_id = #{companyId}
                                   AND oepc.use_flag = 0
                                   AND oepc.is_history = 0
                                    <if test="searchKeyWord != null and searchKeyWord != ''">
                                        AND oepc.stock_name like CONCAT('%', #{searchKeyWord}, '%')
                                    </if>
                                    <if test="notShowId != null and notShowId.size() > 0">
                                        AND oepc.id NOT IN
                                        <foreach collection="notShowId" item="id" open="(" separator="," close=")">
                                            #{id}
                                        </foreach>
                                    </if>
                                 GROUP BY oepc.company_id, oepc.stock_name
                             )
    </select>

    <select id="getById" parameterType="com.facishare.crm.data.ext.dto.SFABICompanyBaseDTO" resultMap="BaseResultMap">
        SELECT
        id   AS id,
       company_id      AS eid,
       company_name      AS company_name,
      stock_name       AS stock_name,
        stock_name_id        AS stock_name_id,
       stock_proportion  AS stock_proportion,
        stock_capital    AS stock_capital,
        stock_type      AS stock_type,
        stock_capital_num AS stock_capital_num
        FROM opt_ent_partner_cn oepc
        WHERE id = #{id}
    </select>

</mapper>
