<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.facishare.crm.data.ext.dao.businessrisk.mapper.SFABICompanyBranchMapper">

    <resultMap id="BaseResultMap" type="com.facishare.crm.data.ext.dao.businessrisk.entity.SFABICompanyBranchEntity">
        <id property="id" column="id" />
        <result property="companyId" column="company_id" />
        <result property="branchName" column="branch_name" />
        <result property="branchCompanyId" column="branch_company_id" />
    </resultMap>

    <select id="selectByCompanyId" parameterType="com.facishare.crm.data.ext.dto.SFABICompanyBaseDTO" resultMap="BaseResultMap">
        SELECT argMax(id, bi_industry_version)            AS id,
               argMax(company_id, bi_industry_version)    AS company_id,
               argMax(branch_name, bi_industry_version) AS branch_name,
               argMax(branch_company_id, bi_industry_version)      AS branch_company_id
        FROM company_branch oepc
        WHERE oepc.company_id = #{companyId}
          AND oepc.use_flag = 0
        <if test="searchKeyWord != null and searchKeyWord != ''">
            AND oepc.branch_name like CONCAT('%', #{searchKeyWord}, '%')
        </if>
        <if test="notShowId != null and notShowId.size() > 0">
            AND oepc.id NOT IN
            <foreach collection="notShowId" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY oepc.company_id, oepc.branch_name
            LIMIT ${from}, ${limit}
    </select>

    <select id="getTotalByCompanyId" parameterType="com.facishare.crm.data.ext.dto.SFABICompanyBaseDTO" resultType="int">
        select count(1) from (
                             SELECT argMax(company_id, bi_industry_version)    AS company_id
                             FROM company_branch oepc
                             WHERE oepc.company_id = #{companyId}
                               AND use_flag = 0
                                <if test="searchKeyWord != null and searchKeyWord != ''">
                                    AND oepc.branch_name like CONCAT('%', #{searchKeyWord}, '%')
                                </if>
                                <if test="notShowId != null and notShowId.size() > 0">
                                    AND oepc.id NOT IN
                                    <foreach collection="notShowId" item="id" open="(" separator="," close=")">
                                        #{id}
                                    </foreach>
                                </if>
                             GROUP BY oepc.company_id, oepc.branch_name
                             )
    </select>

    <select id="getById" parameterType="com.facishare.crm.data.ext.dto.SFABICompanyBaseDTO" resultMap="BaseResultMap">
        SELECT id       AS id,
        company_id  AS company_id,
        branch_name AS branch_name,
        branch_company_id      AS branch_company_id
        FROM company_branch oepc
        WHERE oepc.id = #{id}
    </select>

</mapper>
