<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.facishare.crm.data.ext.dao.businessrisk.mapper.SFABIOptEntPartnerCnRevertMapper">

    <resultMap id="BaseResultMap" type="com.facishare.crm.data.ext.dao.businessrisk.entity.SFABIOptEntPartnerCnRevertEntity">
        <id property="id" column="id" />
        <result property="companyId" column="eid" />
        <result property="companyName" column="company_name" />
        <result property="stockName" column="stock_name" />
        <result property="stockNameId" column="stock_name_id" />
        <result property="stockProportion" column="percent" />
        <result property="stockCapitalNum" column="amount" />
    </resultMap>

    <select id="selectByCompanyId" parameterType="com.facishare.crm.data.ext.dto.SFABICompanyBaseDTO" resultMap="BaseResultMap">
        SELECT * from (SELECT argMax(id, bi_industry_version)            AS id,
                              argMax(company_id, bi_industry_version)        AS eid,
                              argMax(company_name, bi_industry_version)      AS company_name,
                              argMax(is_personal, bi_industry_version)       AS type,
                              argMax(stock_capital_num, bi_industry_version) AS amount,
                              argMax(stock_proportion, bi_industry_version)  AS percent,
                              argMax(stock_type, bi_industry_version)        AS sh_type,
                              argMax(stock_name_id, bi_industry_version)     AS stock_id,
                              argMax(stock_name, bi_industry_version)        AS stock_name
                       FROM opt_ent_partner_cn_revert oepc2
                       WHERE oepc2.stock_name_id = #{companyId}
                         AND oepc2.use_flag = 0
                         AND oepc2.is_history = 0
                        <if test="searchKeyWord != null and searchKeyWord != ''">
                            AND oepc2.company_name like CONCAT('%', #{searchKeyWord}, '%')
                        </if>
                        <if test="notShowId != null and notShowId.size() > 0">
                            AND oepc2.id NOT IN
                            <foreach collection="notShowId" item="id" open="(" separator="," close=")">
                                #{id}
                            </foreach>
                        </if>
                       GROUP BY oepc2.company_id, oepc2.stock_name )
                          LIMIT ${from}, ${limit}
    </select>

    <select id="getTotalByCompanyId" parameterType="com.facishare.crm.data.ext.dto.SFABICompanyBaseDTO" resultType="int">
        select count(1) from (
                                 SELECT argMax(company_id, bi_industry_version)        AS eid
                                 FROM opt_ent_partner_cn_revert oepc2
                                 WHERE oepc2.stock_name_id = #{companyId}
                                   AND oepc2.use_flag = 0
                                   AND oepc2.is_history = 0
                                    <if test="searchKeyWord != null and searchKeyWord != ''">
                                        AND oepc2.company_name like CONCAT('%', #{searchKeyWord}, '%')
                                    </if>
                                        <if test="notShowId != null and notShowId.size() > 0">
                                            AND oepc2.id NOT IN
                                            <foreach collection="notShowId" item="id" open="(" separator="," close=")">
                                                #{id}
                                            </foreach>
                                        </if>
                                 GROUP BY oepc2.company_id, oepc2.stock_name
                             )

    </select>

    <select id="getById" parameterType="com.facishare.crm.data.ext.dto.SFABICompanyBaseDTO" resultMap="BaseResultMap">
        SELECT id           AS id,
               company_id       AS eid,
               company_name      AS company_name,
               is_personal       AS type,
               stock_capital_num AS amount,
               stock_proportion  AS percent,
               stock_type      AS sh_type,
               stock_name_id    AS stock_id,
               stock_name        AS stock_name
        FROM opt_ent_partner_cn_revert oepc2
        WHERE id = #{id}
    </select>

</mapper>
