<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.facishare.crm.data.ext.dao.businessrisk.mapper.SFABICompanyBaseMapper">

    <resultMap id="BaseResultMap" type="com.facishare.crm.data.ext.dao.businessrisk.entity.SFABICompanyBaseEntity">
        <id property="id" column="id" />
        <result property="companyId" column="company_id" />
        <result property="establishDate" column="establish_date" />
        <result property="realCapital" column="real_capital" />
        <result property="companyName" column="company_name" />
        <result property="currency" column="currency" />
    </resultMap>

    <select id="selectCompanyBase" resultMap="BaseResultMap" parameterType="com.facishare.crm.data.ext.dto.SFABICompanyBaseDTO">
        SELECT argMax(company_name, bi_industry_version)   AS company_name,
               argMax(id, bi_industry_version)             AS id,
               argMax(company_id, bi_industry_version)     AS company_id,
               argMax(establish_date, bi_industry_version) AS establish_date,
               argMax(real_capital, bi_industry_version)   AS real_capital,
               argMax(currency, bi_industry_version)   AS currency
        FROM opt_company_base ocb
        WHERE ocb.company_name = #{name}
        GROUP BY ocb.company_id;
    </select>
    <select id="selectCompanyBaseByParam" resultMap="BaseResultMap" parameterType="com.facishare.crm.data.ext.dto.SFABICompanyBaseDTO">
        SELECT argMax(company_name, bi_industry_version)   AS company_name,
        argMax(id, bi_industry_version)             AS id,
        argMax(company_id, bi_industry_version)     AS company_id,
        argMax(establish_date, bi_industry_version) AS establish_date,
        argMax(real_capital, bi_industry_version)   AS real_capital,
        argMax(currency, bi_industry_version)   AS currency
        FROM opt_company_base ocb
        WHERE 1=1
        <if test="name != null and name != ''">
            AND  ocb.company_name = #{name}
        </if>
        <if test="uniformSocialCreditCode != null and uniformSocialCreditCode != ''">
            AND  ocb.credit_no = #{uniformSocialCreditCode}
        </if>
        GROUP BY ocb.company_id;
    </select>

    <select id="selectCompanyId" resultType="java.lang.String" parameterType="java.lang.String">
        SELECT argMax(company_id, bi_industry_version) AS company_id
        FROM company_base
        WHERE credit_no = #{unifiedSocialCreditCode}
        GROUP BY credit_no
    </select>

    <select id="isExistTaxpayer" resultType="long" parameterType="com.facishare.crm.data.ext.dto.SFABICompanyBaseDTO">
        SELECT count() FROM opt_general_taxpayer_info WHERE company_id = #{companyId}
    </select>

</mapper>