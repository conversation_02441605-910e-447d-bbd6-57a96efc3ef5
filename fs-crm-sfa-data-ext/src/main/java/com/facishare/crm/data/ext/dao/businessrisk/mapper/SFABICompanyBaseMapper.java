package com.facishare.crm.data.ext.dao.businessrisk.mapper;

import com.facishare.crm.data.ext.dao.businessrisk.entity.SFABICompanyBaseEntity;
import com.facishare.crm.data.ext.dto.SFABICompanyBaseDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR> lik
 * @date : 2024/3/25 18:15
 */
@Mapper
public interface SFABICompanyBaseMapper {
    List<SFABICompanyBaseEntity> selectCompanyBase(SFABICompanyBaseDTO dto);
    List<SFABICompanyBaseEntity> selectCompanyBaseByParam(SFABICompanyBaseDTO dto);

    List<String> selectCompanyId(String unifiedSocialCreditCode);

    /**
     * 是否存在纳税人
     * @param dto
     * @return
     */
    Long isExistTaxpayer(SFABICompanyBaseDTO dto);
}
