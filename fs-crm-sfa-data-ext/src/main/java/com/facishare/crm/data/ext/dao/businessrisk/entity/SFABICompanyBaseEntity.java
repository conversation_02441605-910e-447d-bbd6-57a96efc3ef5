package com.facishare.crm.data.ext.dao.businessrisk.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * <AUTHOR> lik
 * @date : 2024/3/25 18:16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "company_base")
public class SFABICompanyBaseEntity {
    private Long id;
    private String companyId;
    private LocalDateTime establishDate;
    private String realCapital;
    private String companyName;
    private String currency;
}
