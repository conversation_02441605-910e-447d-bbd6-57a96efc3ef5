package com.facishare.crm.data.ext.config;

import com.github.autoconf.ConfigFactory;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * <AUTHOR> lik
 * @date : 2024/3/22 9:30
 */
@Configuration
@MapperScan(basePackages = "com.facishare.crm.data.ext.dao.businessrisk.mapper", sqlSessionFactoryRef = "SFAbusinessRiskSqlSessionFactory")
@Slf4j
public class SFAClickHouseConfiguration {
    private static String url="";
    private static String userName="";
    private static String passWord="";
    static {
        ConfigFactory.getConfig("crm-sfa-risk-brain-config", config -> {
             url = config.get("ClickHouse_Url", "");
            userName = config.get("ClickHouse_UserName", "");
            passWord = config.get("ClickHouse_PassWord", "");
            if(ObjectUtils.isEmpty(url) || ObjectUtils.isEmpty(userName) || ObjectUtils.isEmpty(passWord)){
                log.error("ClickHouseConfiguration error url:{},userName:{},passWord:{}",url,userName,passWord);
            }
            //passWord = PasswordUtil.decode(passWord);
        });
    }
    @Bean(name = {"SFAbusinessRiskDataSource"})
    public DataSource dataSource() {
        HikariDataSource dataSource = new HikariDataSource();
        // dataSource.setDriverClassName("ru.yandex.clickhouse.ClickHouseDriver");
        dataSource.setDriverClassName("com.clickhouse.jdbc.ClickHouseDriver");
        dataSource.setJdbcUrl(url);
        dataSource.setUsername(userName);
        dataSource.setPassword(passWord);
        dataSource.setReadOnly(true);
        return dataSource;
    }

    @Bean(name = {"SFAbusinessRiskSqlSessionFactory"})
    public SqlSessionFactory sqlSessionFactory() throws Exception {
        SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(dataSource());
        sessionFactory.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/*.xml"));
        return sessionFactory.getObject();
    }
    @Bean(name = {"SFAbusinessRiskDataSourceTransactionManager"})
    public DataSourceTransactionManager transactionManager() {
        return new DataSourceTransactionManager(dataSource());
    }
}
