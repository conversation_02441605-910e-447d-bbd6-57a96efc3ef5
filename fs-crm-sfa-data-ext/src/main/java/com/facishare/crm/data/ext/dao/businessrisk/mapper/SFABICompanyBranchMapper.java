package com.facishare.crm.data.ext.dao.businessrisk.mapper;

import com.facishare.crm.data.ext.dao.businessrisk.entity.SFABICompanyBranchEntity;
import com.facishare.crm.data.ext.dto.SFABICompanyBaseDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR> lik
 * @date : 2024/6/24 16:05
 */
@Mapper
public interface SFABICompanyBranchMapper {
    List<SFABICompanyBranchEntity> selectByCompanyId(SFABICompanyBaseDTO dto);
    int getTotalByCompanyId(SFABICompanyBaseDTO dto);

    SFABICompanyBranchEntity getById(SFABICompanyBaseDTO dto);
}
