package com.facishare.crm.data.ext.dao.businessrisk.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "opt_ent_partner_cn")
public class SFABIOptEntPartnerCnEntity {
    private Long id;
    private String companyId;
    private String companyName;
    private String stockName;
    private String stockNameId;
    private Double stockProportion;
    private String stockCapital;
    private Double stockCapitalNum;
    private String stockType;
}