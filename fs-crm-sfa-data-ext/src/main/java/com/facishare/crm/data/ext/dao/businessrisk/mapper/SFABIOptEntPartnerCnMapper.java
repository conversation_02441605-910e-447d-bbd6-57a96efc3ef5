package com.facishare.crm.data.ext.dao.businessrisk.mapper;

import com.facishare.crm.data.ext.dao.businessrisk.entity.SFABIOptEntPartnerCnEntity;
import com.facishare.crm.data.ext.dto.SFABICompanyBaseDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface SFABIOptEntPartnerCnMapper {

    List<SFABIOptEntPartnerCnEntity> selectByCompanyId(SFABICompanyBaseDTO dto);
    List<SFABIOptEntPartnerCnEntity> selectByParam(SFABICompanyBaseDTO dto);
    int getTotalByParam(SFABICompanyBaseDTO dto);

    SFABIOptEntPartnerCnEntity getById(SFABICompanyBaseDTO dto);

}




