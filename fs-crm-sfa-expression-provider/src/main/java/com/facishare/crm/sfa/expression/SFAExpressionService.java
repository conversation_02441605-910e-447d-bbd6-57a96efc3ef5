package com.facishare.crm.sfa.expression;

import com.facishare.crm.sfa.expression.util.Pair;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.List;
import java.util.Map;

public interface SFAExpressionService {
    Boolean evaluate(String wheres, IObjectData objectData, IObjectDescribe objectDescribe);

    Map<String,Boolean> evaluate(String wheres, List<IObjectData> objectData, IObjectDescribe objectDescribe);

    Pair<String, IObjectData> getExpression(String wheres, IObjectData objectData, IObjectDescribe objectDescribe);
}
