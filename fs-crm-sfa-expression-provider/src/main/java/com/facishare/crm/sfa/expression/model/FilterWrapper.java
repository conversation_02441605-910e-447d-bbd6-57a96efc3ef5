package com.facishare.crm.sfa.expression.model;

import com.facishare.paas.metadata.api.search.IFilter;
import lombok.Data;

/**
 * Filter包装类，用于扩展IFilter接口，支持额外的属性
 * 
 * <AUTHOR>
 * @date 2025/01/16
 */
@Data
public class FilterWrapper {
    /**
     * 原始的IFilter对象
     */
    private IFilter filter;
    
    /**
     * 是否级联查询（用于部门字段，表示是否包含子部门）
     */
    private Boolean isCascade;
    
    /**
     * 其他扩展属性（预留）
     */
    private String extProperties;
    
    public FilterWrapper(IFilter filter) {
        this.filter = filter;
        this.isCascade = false;
    }
    
    public FilterWrapper(IFilter filter, Boolean isCascade) {
        this.filter = filter;
        this.isCascade = isCascade;
    }
}
