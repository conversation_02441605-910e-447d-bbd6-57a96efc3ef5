package com.facishare.crm.sfa.expression.model;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019/11/14 15:22
 */

public class Filter {

    private boolean isIndex;
    private int fieldNum;
    private String operator;
    private String connector;
    private String field_name;
    private String operator_name;
    private boolean isObjectReferencea;
    private List<String> field_values;

    public boolean getIsIndex() {
        return isIndex;
    }

    public void setIsIndex(boolean isIndex) {
        this.isIndex = isIndex;
    }

    public int getFieldNum() {
        return fieldNum;
    }

    public void setFieldNum(int fieldNum) {
        this.fieldNum = fieldNum;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getConnector() {
        return connector;
    }

    public void setConnector(String connector) {
        this.connector = connector;
    }

    public String getField_name() {
        return field_name;
    }

    public void setField_name(String field_name) {
        this.field_name = field_name;
    }

    public String getOperator_name() {
        return operator_name;
    }

    public void setOperator_name(String operator_name) {
        this.operator_name = operator_name;
    }

    public boolean getIsObjectReferencea() {
        return isObjectReferencea;
    }

    public void setIsObjectReferencea(boolean isObjectReferencea) {
        this.isObjectReferencea = isObjectReferencea;
    }

    public List<String> getField_values() {
        return field_values;
    }

    public void setField_values(List<String> field_values) {
        this.field_values = field_values;
    }
}
