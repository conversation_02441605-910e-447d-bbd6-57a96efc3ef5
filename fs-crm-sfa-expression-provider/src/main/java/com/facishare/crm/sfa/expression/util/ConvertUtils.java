package com.facishare.crm.sfa.expression.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.appframework.metadata.MetaDataFindServiceImpl;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IObjectReferenceField;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import joptsimple.internal.Strings;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019/11/14 17:18
 */

public class ConvertUtils {

    private static final MetaDataFindService metaDataFindService = SpringUtil.getContext().getBean(MetaDataFindServiceImpl.class);



    public static void convertData(List<Wheres> wheresList, List<IObjectData> objectDataList,IObjectDescribe objectDescribe) {
        if(objectDescribe == null || CollectionUtils.empty(wheresList) || CollectionUtils.empty(objectDataList)) {
            return;
        }

        for (Wheres wheres : wheresList) {
            if(CollectionUtils.empty(wheres.getFilters())) {
                continue;
            }
            Set<String> fieldList = wheres.getFilters().stream().map(x -> x.getFieldName()).collect(Collectors.toSet());
            List<IFieldDescribe> fieldDescribes = objectDescribe.getFieldDescribes().stream()
                    .filter(x -> fieldList.contains(x.getApiName())).collect(Collectors.toList());
            if(CollectionUtils.empty(fieldDescribes)) {
                continue;
            }
            for(IFieldDescribe fieldDescribe : fieldDescribes) {
                for(IObjectData objectData : objectDataList) {
                    if(!ObjectDataExt.of(objectData).containsField(fieldDescribe.getApiName())) {
                        objectData.set(fieldDescribe.getApiName(), null);
                        if(fieldDescribe.getType().equals(IFieldType.OBJECT_REFERENCE)) {
                            String referenceNameApi = String.format("%s__r", fieldDescribe.getApiName());
                            objectData.set(referenceNameApi, null);
                        }
                        continue;
                    }
                    switch (fieldDescribe.getType()) {
                        case IFieldType.NUMBER:
                        case IFieldType.CURRENCY:
						case IFieldType.PERCENTILE:
                            String stringValue = getStringValue(objectData, fieldDescribe.getApiName(), null);
                            if (StringUtils.isBlank(stringValue)) {
                                objectData.set(fieldDescribe.getApiName(), null);
                            } else {
                                try {
                                    Double doubleValue = new BigDecimal(stringValue).doubleValue();
                                    objectData.set(fieldDescribe.getApiName(), doubleValue);
                                } catch (Exception e) {
                                    objectData.set(fieldDescribe.getApiName(), null);
                                }
                            }
                            break;
                        case IFieldType.SELECT_MANY:
                            List<String> listValue = getListValue(objectData, fieldDescribe.getApiName(), null);
                            ArrayList<String> arrayList = null;
                            if (CollectionUtils.empty(listValue)) {
                                objectData.set(fieldDescribe.getApiName(), null);
                            } else {
                                arrayList = new ArrayList(listValue);
                                Collections.sort(arrayList);
                                String[] strArray = new String[arrayList.size()];
                                for (int i = 0; i < arrayList.size(); i++) {
                                    strArray[i] = String.valueOf(arrayList.get(i));
                                }
                                objectData.set(fieldDescribe.getApiName(), strArray);
                            }
                            break;
                        case IFieldType.OBJECT_REFERENCE:
                            String referenceNameApi = String.format("%s__r", fieldDescribe.getApiName());
                            String referenceValue = getStringValue(objectData, fieldDescribe.getApiName(), null);
                            if (StringUtils.isBlank(referenceValue)) {
                                objectData.set(referenceNameApi, null);
                            } else {
                                IObjectReferenceField referenceField = null;
                                if (fieldDescribe instanceof IObjectReferenceField) {
                                    referenceField = (IObjectReferenceField)fieldDescribe;
                                }
                                if(referenceField == null) {
                                    objectData.set(referenceNameApi, null);
                                    continue;
                                }
                                User user = new User(objectData.getTenantId(), User.SUPPER_ADMIN_USER_ID);
                                List<IObjectData> referenceObjectDataList = metaDataFindService.findObjectDataByIdsIncludeDeleted(user, Lists.newArrayList(referenceValue), referenceField.getTargetApiName());

                                if(CollectionUtils.empty(referenceObjectDataList)) {
                                    objectData.set(fieldDescribe.getApiName(), null);
                                    objectData.set(referenceNameApi, null);
                                }else{
                                    objectData.set(referenceNameApi, referenceObjectDataList.get(0).getName());
                                }
                            }
                            break;
                        case IFieldType.EMPLOYEE:
                        case IFieldType.DEPARTMENT:
                            String dataName = null;
                            Object value = objectData.get(fieldDescribe.getApiName());
                            if (value == null || "".equals(value)) {
                                objectData.set(fieldDescribe.getApiName(), Strings.EMPTY);
                                break;
                            } else {
                                String strValue = "";
                                if (value instanceof String) {
                                    strValue = (String) value;
                                } else {
                                    strValue = JSON.toJSONString(value);
                                }
                                if(!strValue.startsWith("[") && !strValue.endsWith("]")) {
                                    break;
                                }
                            }
                            if (fieldDescribe.getApiName().equals("out_owner")) {
                                dataName = getOutOwner(objectData);
//                                User user = new User(objectData.getTenantId(), User.SUPPER_ADMIN_USER_ID);
//                                fillOutUserInfo(objectData, objectDescribe, user);
//                                Object object = JSONObject.parseObject(objectData.get(fieldDescribe.getApiName() +"__r", String.class)).get("name");
//                                if (object != null){
//                                    dataName = object.toString();
//                                }else {
//                                    log.warn("{}__r is null objectId:{}",fieldDescribe.getApiName(),objectData.getId());
//                                }
//                                break;
                            } else{
                                List<String> lstValue = getListValue(objectData,fieldDescribe.getApiName(), Lists.newArrayList());
                                if(CollectionUtils.notEmpty(lstValue)) {
                                    dataName = lstValue.get(0);
                                }
                            }
                            if(StringUtils.isBlank(dataName)) {
                                objectData.set(fieldDescribe.getApiName(), Strings.EMPTY);
                            } else {
								if (!StringUtils.contains(dataName,",")) {
									dataName = String.format(",%s,", dataName);
									objectData.set(fieldDescribe.getApiName(), dataName);
								}
                            }
                            break;
                        case IFieldType.TRUE_OR_FALSE:
                            boolean booleanValue = getBooleanValue(objectData, fieldDescribe.getApiName(), false);
                            objectData.set(fieldDescribe.getApiName(), String.valueOf(booleanValue));
                            break;
                    }
                }
            }
        }
    }

    public static String getStringValue(IObjectData objectData, String key, String defaultValue) {
        if (objectData == null || StringUtils.isEmpty(key)) {
            return defaultValue;
        }
        Object tempValue = objectData.get(key);
        if (tempValue != null) {
            return tempValue.toString();
        }
        return defaultValue;
    }

    public static List<String> getListValue(IObjectData objectData, String key, List<String> defaultValue) {
        Object value = objectData.get(key);
        if (null == value) {
            return Lists.newArrayList();
        } else {
            String str;
            if (value instanceof String) {
                str = (String) value;
                return Lists.newArrayList(str);
            } else {
                str = JSON.toJSONString(value);
            }
            return (List) JSONObject.parseObject(str, List.class);
        }
    }

    public static boolean getBooleanValue(IObjectData objectData, String key, boolean defaultValue) {
        if (objectData == null || StringUtils.isEmpty(key)) {
            return defaultValue;
        }
        Object tempValue = objectData.get(key);
        if (tempValue != null) {
            try {
                Boolean result = Boolean.valueOf(tempValue.toString());
                if (result == null) {
                    return defaultValue;
                }
                return result;
            } catch (Exception e) {
                return defaultValue;
            }
        }
        return defaultValue;
    }

    public static String getOutOwner(IObjectData objectData) {
        List ownerList = objectData.getOutOwner();
        String owner = "";
        if (CollectionUtils.notEmpty(ownerList)) {
            owner = String.valueOf(ownerList.get(0));
        }
        return owner;
    }


    public static List<Wheres> convert2WheresList(String wheresString) {
        if (StringUtils.isEmpty(wheresString)) {
            return null;
        }
        List<Wheres> wheresList = new ArrayList<>();
        List<JSONObject> wheresJSONObjectList = JSON.parseObject(wheresString, List.class);
        if (!org.springframework.util.CollectionUtils.isEmpty(wheresJSONObjectList)) {
            for (JSONObject jsonObject : wheresJSONObjectList) {
                Wheres wheres = JSON.parseObject(jsonObject.toJSONString(), Wheres.class);
                wheresList.add(wheres);
            }
        }
        return wheresList;
    }
    public static List<Wheres> convert2Wheres(String wheresString) {
        //if (StringUtils.isEmpty(wheresString)) {
        //    return null;
        //}
        //
        //List<Wheres> wheresList = new ArrayList<>();
        //Wheres wheres = JSON.parseObject(wheresString, Wheres.class);
        //wheresList.add(wheres);
        //return wheresList;

        if (StringUtils.isEmpty(wheresString)) {
            return null;
        }
        List<Wheres> wheresList = new ArrayList<>();
        // 兼容历史单条规则。
        if(wheresString.length() > 1 && wheresString.startsWith("{") && wheresString.endsWith("}")) {
            Wheres wheres = JSON.parseObject(wheresString, Wheres.class);
            wheresList.add(wheres);
        }else{
            List<JSONObject> wheresJSONObjectList = JSON.parseObject(wheresString, List.class);
            if (com.facishare.paas.appframework.common.util.CollectionUtils.notEmpty(wheresJSONObjectList)) {
                for (JSONObject jsonObject : wheresJSONObjectList) {
                    if (jsonObject.containsKey("filters") && jsonObject.getJSONArray("filters") != null) {

                        jsonObject.getJSONArray("filters").forEach(filter -> {
                            if (filter instanceof JSONObject) {
                                JSONObject filterJsonObject = (JSONObject) filter;
                                if (filterJsonObject.containsKey("_operator")) {
                                    filterJsonObject.remove("_operator");
                                }
                            }
                        });
                    }
                    Wheres wheres = JSON.parseObject(jsonObject.toJSONString(), Wheres.class);
                    if (com.facishare.paas.appframework.common.util.CollectionUtils.notEmpty(wheres.getFilters())) {
                        for (IFilter filter : wheres.getFilters()) {
                            if (filter.getFieldValues() == null) {
                                filter.setFieldValues(Lists.newArrayList());
                            }
                        }
                    }
                    wheresList.add(wheres);
                }
            }
        }
        return wheresList;
    }
}
