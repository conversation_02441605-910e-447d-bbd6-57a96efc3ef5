package com.facishare.crm

import com.facishare.crm.sfa.lto.utils.SearchUtil
import com.facishare.paas.appframework.core.model.ActionContext
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.ServiceContext
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.Lang
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.QueryResult
import com.facishare.paas.metadata.api.search.IFilter
import com.facishare.paas.metadata.api.search.Wheres
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
import com.facishare.paas.metadata.impl.search.Where
import com.facishare.paas.metadata.util.SpringContextUtil
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.google.common.collect.Lists
import org.springframework.context.ApplicationContext
import spock.lang.Shared
import spock.lang.Specification

abstract class BaseGroovyTest extends Specification {

    @Shared
    protected ActionContext actionContext
    @Shared
    protected ServiceContext serviceContext
    @Shared
    protected RequestContext requestContext
    @Shared
    protected String tenantId
    @Shared
    protected String userId
    @Shared
    protected User user
    @Shared
    protected String appId


    def setupSpec() {
        System.setProperty("spring.profiles.active", "fstest");
        System.setProperty("config.mode", "localNoUpdate");
        initI18nClient()
        initRequestContext()
        initSpringContext()
        initActionContext()
    }


    protected initI18nClient() {
        def field = I18nClient.class.getDeclaredField("impl")
        field.setAccessible(true)

        def stub = Stub(I18nServiceImpl)
        field.set(I18nClient.getInstance(), stub)
        stub.getOrDefault(_ as String, _ as Long, _ as String, _ as String) >> {
            String key, Long tenantId, String lang, String defaultVal ->
                return defaultVal
        }
    }

    RequestContext initRequestContext() {
        initUser()
        this.user = new User(tenantId, userId)
        RequestContext.RequestContextBuilder requestContextBuilder = RequestContext.builder()
        requestContextBuilder.tenantId(tenantId)
        requestContextBuilder.user(user)
        requestContextBuilder.appId(appId)
        requestContextBuilder.contentType(RequestContext.ContentType.FULL_JSON)
        requestContextBuilder.requestSource(RequestContext.RequestSource.CEP)
        requestContextBuilder.lang(Lang.zh_CN)
        requestContext = requestContextBuilder.build()
        return requestContext
    }

    protected ServiceContext getServiceContext(String serviceName, String serviceMethod) {
        return new ServiceContext(requestContext, serviceName, serviceMethod)
    }

    void initUser() {
        this.tenantId = "23456";
        this.userId = "1000";
    }

    void initSpringContext() {
        def stubApplicationContext = Stub(ApplicationContext)
        def util = new SpringContextUtil()
        util.setApplicationContext(stubApplicationContext)
        Map<Class, Object> map = new HashMap<>()
        stubApplicationContext.getBean(_ as Class) >> { Class requiredType ->
            map.computeIfAbsent(requiredType, { key ->
                Stub(requiredType)
            })
        }

        Map<String, Object> map2 = new HashMap<>()
        stubApplicationContext.getBean(_ as String, _ as Class) >> { String name, Class requiredType ->
            map2.computeIfAbsent(name, { key ->
                Stub(requiredType)
            })
        }
    }

    ActionContext initActionContext() {
        actionContext = Mock(ActionContext)
        actionContext.requestContext >> requestContext
        actionContext.getTenantId() >> tenantId
        actionContext.getUser() >> user
        actionContext.getAppId() >> appId
    }

    def getDescribe(String apiName) {
        ObjectDescribeBuilder.builder()
                .apiName(apiName)
                .tenantId(tenantId)
                .build()
    }

    def getObjectData(List<String> fieldList) {
        def objectData = new ObjectData();
        fieldList.each {
            objectData.set(it, "xxxx")
        }
        objectData.setTenantId(tenantId)
        objectData
    }

    def getObjectData(List<String> fieldList,List<String> valueList) {
        IObjectData objectData = new ObjectData();
        fieldList.eachWithIndex { item, index ->
            objectData.set(item, valueList[index])
        }
        objectData
    }

    def getObjectDataList(List<String> fieldList, int size) {
        def objectDataList = new ArrayList<ObjectData>();
        for (i in 0..<size) {
            def objectData = new ObjectData();
            fieldList.each {
                objectData.set(it, "xxxx")
            }
            objectData.setTenantId(tenantId)
            objectDataList.add(objectData)
        }
        objectDataList
    }

    def searchQueryAddFieldFilter(List<String> fields, List<Object> values) {
        def query = buildSearchQuery()
        fields.eachWithIndex { String f, int i ->
            List<IFilter> list = Lists.newArrayList();
            SearchUtil.fillFilterEq(list, f, values[i]);
            query.getFilters().addAll(list)
        }
        query
    }

    def buildSearchQueryNoFilter(){
        SearchTemplateQuery query = new SearchTemplateQuery()
        query.setNeedReturnCountNum(false)
        query.setPermissionType(0)
        query.setNeedReturnQuote(false)
        query.setOffset(0)
        query.setLimit(1000)
        query
    }
    def buildSearchQuery() {
        def query = buildSearchQueryNoFilter()
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, "is_deleted", true);
        query.setFilters(filters)

        List<IFilter> filters2 = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters2, "biz_status", "normal");
        query.setFilters(filters2)
        Wheres wheres1 = new Wheres();
        wheres1.setConnector(Where.CONN.OR.toString());
        wheres1.setFilters(filters2);
        query.setWheres([wheres1]);
        query
    }

    def buildQueryResult() {
        QueryResult queryResult = new QueryResult();
        queryResult.setData(getObjectDataList(["account_id", "_id"], 3));
        queryResult
    }

    class OK extends RuntimeException {

    }
}
