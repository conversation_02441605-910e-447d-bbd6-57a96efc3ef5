package com.facishare.crm.follow.model;

import com.alibaba.fastjson.JSON;
import org.junit.Test;
import static org.junit.Assert.*;

/**
 * 测试 CrmActionMQMessage 解析问题的修复
 * <AUTHOR>
 */
public class CrmActionMQMessageTest {

    @Test
    public void testParseMessageWithActionContentString() {
        // 模拟你提供的消息体（简化版）
        String messageBody = "{\n" +
                "  \"version\": \"2\",\n" +
                "  \"ActionCode\": \"Abolish\",\n" +
                "  \"TenantAccount\": \"dj168168\",\n" +
                "  \"AppID\": \"CRM\",\n" +
                "  \"ObjectID\": \"681c57342ad93b0001b87da2\",\n" +
                "  \"action_content\": \"{\\\"executionType\\\":\\\"checkedGroupObject\\\",\\\"executionName\\\":\\\"添加检查组\\\"}\",\n" +
                "  \"ActionContent\": {\n" +
                "    \"tenant_id\": \"804138\",\n" +
                "    \"_id\": \"681c57342ad93b0001b87da2\"\n" +
                "  },\n" +
                "  \"TenantID\": \"804138\",\n" +
                "  \"ObjectApiName\": \"SOPActionInstanceObj\",\n" +
                "  \"OperatorID\": 1377\n" +
                "}";

        try {
            // 测试解析是否成功
            CrmActionMQMessage messageObj = JSON.parseObject(messageBody, CrmActionMQMessage.class);
            
            assertNotNull("消息对象不应为空", messageObj);
            assertEquals("TenantID应该正确", "804138", messageObj.getTenantID());
            assertEquals("ActionCode应该正确", "Abolish", messageObj.getActionCode());
            assertEquals("ObjectID应该正确", "681c57342ad93b0001b87da2", messageObj.getObjectID());
            
            // 测试 ActionContent 对象形式
            assertNotNull("ActionContent对象不应为空", messageObj.getActionContent());
            assertEquals("ActionContent的_id应该正确", "681c57342ad93b0001b87da2", messageObj.getActionContent().getId());
            
            // 测试 action_content 字符串形式
            assertNotNull("actionContentString不应为空", messageObj.getActionContentString());
            assertTrue("actionContentString应该包含executionType", 
                    messageObj.getActionContentString().contains("executionType"));
            
            // 测试智能获取方法
            CrmActionMQMessage.Content smartContent = messageObj.getActionContentSmart();
            assertNotNull("智能获取的Content不应为空", smartContent);
            assertEquals("智能获取的Content的_id应该正确", "681c57342ad93b0001b87da2", smartContent.getId());
            
            System.out.println("测试通过：消息解析成功！");
            
        } catch (Exception e) {
            fail("消息解析失败: " + e.getMessage());
        }
    }

    @Test
    public void testParseMessageWithOnlyActionContentString() {
        // 测试只有 action_content 字符串的情况
        String messageBody = "{\n" +
                "  \"ActionCode\": \"Abolish\",\n" +
                "  \"TenantAccount\": \"dj168168\",\n" +
                "  \"AppID\": \"CRM\",\n" +
                "  \"ObjectID\": \"681c57342ad93b0001b87da2\",\n" +
                "  \"action_content\": \"{\\\"_id\\\":\\\"test123\\\",\\\"executionType\\\":\\\"checkedGroupObject\\\"}\",\n" +
                "  \"TenantID\": \"804138\",\n" +
                "  \"ObjectApiName\": \"SOPActionInstanceObj\",\n" +
                "  \"OperatorID\": 1377\n" +
                "}";

        try {
            CrmActionMQMessage messageObj = JSON.parseObject(messageBody, CrmActionMQMessage.class);
            
            assertNotNull("消息对象不应为空", messageObj);
            
            // ActionContent 对象应该为空
            assertNull("ActionContent对象应该为空", messageObj.getActionContent());
            
            // actionContentString 应该有值
            assertNotNull("actionContentString不应为空", messageObj.getActionContentString());
            
            // 智能获取应该能解析字符串并返回对象
            CrmActionMQMessage.Content smartContent = messageObj.getActionContentSmart();
            assertNotNull("智能获取的Content不应为空", smartContent);
            assertEquals("智能获取的Content的_id应该正确", "test123", smartContent.getId());
            
            System.out.println("测试通过：只有字符串形式的action_content也能正确解析！");
            
        } catch (Exception e) {
            fail("消息解析失败: " + e.getMessage());
        }
    }

    @Test
    public void testParseOriginalProblemMessage() {
        // 测试原始问题消息的简化版本
        String messageBody = "{\n" +
                "  \"version\": \"2\",\n" +
                "  \"sop_enterprise_type\": \"1\",\n" +
                "  \"ActionCode\": \"Abolish\",\n" +
                "  \"action_content\": \"{\\\"executionType\\\":\\\"checkedGroupObject\\\",\\\"executionName\\\":\\\"添加检查组\\\",\\\"checkedGroupDataConfig\\\":{\\\"checkGroupCode\\\":\\\"677268839400a7000106a4a0\\\",\\\"targetObject\\\":\\\"CasesObj\\\",\\\"targetField\\\":\\\"CasesObj\\\",\\\"matchType\\\":2,\\\"autoComplete4UnMatch\\\":false}}\",\n" +
                "  \"TenantAccount\": \"dj168168\",\n" +
                "  \"AppID\": \"CRM\",\n" +
                "  \"ObjectID\": \"681c57342ad93b0001b87da2\",\n" +
                "  \"ActionContent\": {\n" +
                "    \"tenant_id\": \"804138\",\n" +
                "    \"_id\": \"681c57342ad93b0001b87da2\"\n" +
                "  },\n" +
                "  \"TenantID\": \"804138\",\n" +
                "  \"ObjectApiName\": \"SOPActionInstanceObj\",\n" +
                "  \"OperatorID\": 1377\n" +
                "}";

        try {
            CrmActionMQMessage messageObj = JSON.parseObject(messageBody, CrmActionMQMessage.class);
            
            assertNotNull("消息对象不应为空", messageObj);
            assertEquals("ActionCode应该正确", "Abolish", messageObj.getActionCode());
            
            // 验证智能获取功能
            CrmActionMQMessage.Content smartContent = messageObj.getActionContentSmart();
            assertNotNull("智能获取的Content不应为空", smartContent);
            
            System.out.println("测试通过：原始问题消息现在可以正确解析！");
            
        } catch (Exception e) {
            fail("原始问题消息解析失败: " + e.getMessage());
        }
    }

    /**
     * 测试真实的完整问题消息体
     */
    @Test
    public void testParseCompleteOriginalMessage() {
        // 这是你提供的完整消息体
        String messageBody = "{\n" +
                "  \"version\": \"2\",\n" +
                "  \"sop_enterprise_type\": \"1\",\n" +
                "  \"ActionCode\": \"Abolish\",\n" +
                "  \"sop_procedure_template_id__relation_ids\": \"6801bf6b34ec3b0001f33fd8\",\n" +
                "  \"sop_action_template_id\": \"6801bf6b34ec3b0001f340ee\",\n" +
                "  \"execute_required\": true,\n" +
                "  \"sop_procedure_instance_id__relation_ids\": \"681c5734be9e6300010f4d36\",\n" +
                "  \"action_name\": \"注射器\",\n" +
                "  \"TenantAccount\": \"dj168168\",\n" +
                "  \"created_by\": [\"-10000\"],\n" +
                "  \"action_execute_type\": \"common\",\n" +
                "  \"owner\": [\"-10000\"],\n" +
                "  \"sop_instance_id__relation_ids\": \"681c5733d126cf00014703fc\",\n" +
                "  \"record_type\": \"default__c\",\n" +
                "  \"AppID\": \"CRM\",\n" +
                "  \"executor\": [\"1276\"],\n" +
                "  \"sop_procedure_template_id\": \"6801bf6b34ec3b0001f33fd8\",\n" +
                "  \"status\": \"ready\",\n" +
                "  \"sop_instance_id\": \"681c5733d126cf00014703fc\",\n" +
                "  \"sop_source_template_id__relation_ids\": \"67739f11b2e6a8000164bcd2\",\n" +
                "  \"ObjectID\": \"681c57342ad93b0001b87da2\",\n" +
                "  \"action_content\": \"{\\\"executionType\\\":\\\"checkedGroupObject\\\",\\\"executionName\\\":\\\"添加检查组\\\",\\\"checkedGroupDataConfig\\\":{\\\"checkGroupCode\\\":\\\"677268839400a7000106a4a0\\\",\\\"targetObject\\\":\\\"CasesObj\\\",\\\"targetField\\\":\\\"CasesObj\\\",\\\"matchType\\\":2,\\\"autoComplete4UnMatch\\\":false},\\\"executorConfigs\\\":[\\\"CasesObj##field_xcfwry__c\\\"],\\\"assignee\\\":{\\\"extUserType\\\":[\\\"CasesObj##field_xcfwry__c\\\"]},\\\"externalAssignee\\\":{},\\\"actionPreCheckConfig\\\":{\\\"isCheck\\\":false,\\\"preCheckActionOrderIds\\\":[]},\\\"actionCompleteCondition\\\":{\\\"isOpen\\\":false,\\\"conditionType\\\":1,\\\"functionApiName\\\":\\\"\\\",\\\"functionName\\\":\\\"\\\"},\\\"afterActionCompleteProcessConfig\\\":{\\\"isActionAfterOpen\\\":false,\\\"afterActionCompleteFunctionConfigs\\\":[]},\\\"isAutoJump\\\":true,\\\"isShowSkipButton\\\":true,\\\"actionExecuteTip\\\":{\\\"beforeExecuteTipOpen\\\":false,\\\"afterExecuteTipOpen\\\":false,\\\"beforeExecuteTipColor\\\":\\\"#606570\\\",\\\"afterExecuteTipColor\\\":\\\"#606570\\\",\\\"beforeExecuteTip\\\":\\\"\\\",\\\"afterExecuteTip\\\":\\\"\\\"},\\\"actionOrderId\\\":\\\"1734287463469\\\",\\\"actionAssigneeMap\\\":{\\\"exist_extUserType_CasesObj##field_xcfwry__c\\\":[\\\"1276\\\"]}}\",\n" +
                "  \"ActionContent\": {\n" +
                "    \"tenant_id\": \"804138\",\n" +
                "    \"sop_source_template_id__relation_ids\": \"67739f11b2e6a8000164bcd2\",\n" +
                "    \"cases_id\": \"681c5584e6570f0001c1b4a6\",\n" +
                "    \"action_type\": \"4\",\n" +
                "    \"object_data_id\": \"681c5584e6570f0001c1b4a6\",\n" +
                "    \"sop_action_template_id__relation_ids\": \"6801bf6b34ec3b0001f340ee\",\n" +
                "    \"cases_id__relation_ids\": \"681c5584e6570f0001c1b4a6\",\n" +
                "    \"sop_template_id__relation_ids\": \"6801bf6ab232c10001635afa\",\n" +
                "    \"sop_procedure_template_id__relation_ids\": \"6801bf6b34ec3b0001f33fd8\",\n" +
                "    \"action_order\": \"11\",\n" +
                "    \"is_deleted\": true,\n" +
                "    \"executor\": [\"1276\"],\n" +
                "    \"life_status_before_invalid\": \"normal\",\n" +
                "    \"action_name\": \"注射器\",\n" +
                "    \"object_describe_api_name\": \"SOPActionInstanceObj\",\n" +
                "    \"sop_action_template_id\": \"6801bf6b34ec3b0001f340ee\",\n" +
                "    \"searchAfterId\": [\"1746687802277\", \"1746687813059641\", \"681c57342ad93b0001b87da2\"],\n" +
                "    \"object_api_name__r\": \"工单\",\n" +
                "    \"owner\": [\"-10000\"],\n" +
                "    \"sop_template_id\": \"6801bf6ab232c10001635afa\",\n" +
                "    \"last_modified_time\": 1746687802277,\n" +
                "    \"lock_status\": \"0\",\n" +
                "    \"package\": \"CRM\",\n" +
                "    \"sop_instance_id__relation_ids\": \"681c5733d126cf00014703fc\",\n" +
                "    \"create_time\": 1746687796340,\n" +
                "    \"life_status\": \"invalid\",\n" +
                "    \"sop_instance_id\": \"681c5733d126cf00014703fc\",\n" +
                "    \"last_modified_by\": [\"-10000\"],\n" +
                "    \"version\": \"2\",\n" +
                "    \"created_by\": [\"-10000\"],\n" +
                "    \"record_type\": \"default__c\",\n" +
                "    \"relevant_team\": [{\n" +
                "      \"teamMemberEmployee\": [\"-10000\"],\n" +
                "      \"teamMemberType\": \"0\",\n" +
                "      \"teamMemberRole\": \"1\",\n" +
                "      \"teamMemberPermissionType\": \"2\",\n" +
                "      \"teamMemberDeptCascade\": \"0\"\n" +
                "    }],\n" +
                "    \"data_own_department\": [\"999999\"],\n" +
                "    \"sop_procedure_template_id\": \"6801bf6b34ec3b0001f33fd8\",\n" +
                "    \"object_api_name\": \"CasesObj\",\n" +
                "    \"action_content\": \"{\\\"executionType\\\":\\\"checkedGroupObject\\\",\\\"executionName\\\":\\\"添加检查组\\\",\\\"checkedGroupDataConfig\\\":{\\\"checkGroupCode\\\":\\\"677268839400a7000106a4a0\\\",\\\"targetObject\\\":\\\"CasesObj\\\",\\\"targetField\\\":\\\"CasesObj\\\",\\\"matchType\\\":2,\\\"autoComplete4UnMatch\\\":false},\\\"executorConfigs\\\":[\\\"CasesObj##field_xcfwry__c\\\"],\\\"assignee\\\":{\\\"extUserType\\\":[\\\"CasesObj##field_xcfwry__c\\\"]},\\\"externalAssignee\\\":{},\\\"actionPreCheckConfig\\\":{\\\"isCheck\\\":false,\\\"preCheckActionOrderIds\\\":[]},\\\"actionCompleteCondition\\\":{\\\"isOpen\\\":false,\\\"conditionType\\\":1,\\\"functionApiName\\\":\\\"\\\",\\\"functionName\\\":\\\"\\\"},\\\"afterActionCompleteProcessConfig\\\":{\\\"isActionAfterOpen\\\":false,\\\"afterActionCompleteFunctionConfigs\\\":[]},\\\"isAutoJump\\\":true,\\\"isShowSkipButton\\\":true,\\\"actionExecuteTip\\\":{\\\"beforeExecuteTipOpen\\\":false,\\\"afterExecuteTipOpen\\\":false,\\\"beforeExecuteTipColor\\\":\\\"#606570\\\",\\\"afterExecuteTipColor\\\":\\\"#606570\\\",\\\"beforeExecuteTip\\\":\\\"\\\",\\\"afterExecuteTip\\\":\\\"\\\"},\\\"actionOrderId\\\":\\\"1734287463469\\\",\\\"actionAssigneeMap\\\":{\\\"exist_extUserType_CasesObj##field_xcfwry__c\\\":[\\\"1276\\\"]}}\",\n" +
                "    \"sop_procedure_instance_id\": \"681c5734be9e6300010f4d36\",\n" +
                "    \"sop_enterprise_type\": \"1\",\n" +
                "    \"name\": \"2025-05-08001358\",\n" +
                "    \"execute_required\": true,\n" +
                "    \"action_execute_type\": \"common\",\n" +
                "    \"_id\": \"681c57342ad93b0001b87da2\",\n" +
                "    \"sop_source_template_id\": \"67739f11b2e6a8000164bcd2\",\n" +
                "    \"sop_procedure_instance_id__relation_ids\": \"681c5734be9e6300010f4d36\",\n" +
                "    \"status\": \"ready\"\n" +
                "  },\n" +
                "  \"TenantID\": \"804138\",\n" +
                "  \"ObjectApiName\": \"SOPActionInstanceObj\",\n" +
                "  \"OperatorID\": 1377\n" +
                "}";

        try {
            CrmActionMQMessage messageObj = JSON.parseObject(messageBody, CrmActionMQMessage.class);
            
            assertNotNull("完整消息对象不应为空", messageObj);
            assertEquals("TenantID应该正确", "804138", messageObj.getTenantID());
            assertEquals("ActionCode应该正确", "Abolish", messageObj.getActionCode());
            assertEquals("ObjectID应该正确", "681c57342ad93b0001b87da2", messageObj.getObjectID());
            assertEquals("ObjectApiName应该正确", "SOPActionInstanceObj", messageObj.getObjectApiName());
            
            // 验证智能获取功能 - 应该优先返回对象形式的ActionContent
            CrmActionMQMessage.Content smartContent = messageObj.getActionContentSmart();
            assertNotNull("智能获取的Content不应为空", smartContent);
            assertEquals("智能获取的Content的_id应该正确", "681c57342ad93b0001b87da2", smartContent.getId());
            
            // 验证两种格式都可以获取到
            assertNotNull("ActionContent对象不应为空", messageObj.getActionContent());
            assertNotNull("actionContentString不应为空", messageObj.getActionContentString());
            assertTrue("actionContentString应该包含executionType", 
                    messageObj.getActionContentString().contains("executionType"));
            
            System.out.println("测试通过：完整的原始问题消息现在可以正确解析！");
            System.out.println("ActionContent ID: " + smartContent.getId());
            System.out.println("ActionContent tenant_id: " + smartContent.getTenantId());
            
        } catch (Exception e) {
            fail("完整原始问题消息解析失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
