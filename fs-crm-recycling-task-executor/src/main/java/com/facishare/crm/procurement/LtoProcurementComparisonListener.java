package com.facishare.crm.procurement;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.lto.common.LtoRateLimiterService;
import com.facishare.crm.sfa.lto.procurement.LtoProcurementLtoComparisonService;
import com.facishare.crm.sfa.lto.procurement.models.LtoProcurementModel.ProcurementMessage;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.fxiaoke.rocketmq.util.MessageHelper;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

/**
 * @ClassName com.facishare.crm.sfa.lto.procurement.ProcurementComparisonListener
 * @Description
 * <AUTHOR>
 * @Date 2022/10/18 18:26
 * @Version 1.0
 **/
@Slf4j
@Component
public class LtoProcurementComparisonListener implements ApplicationListener<ContextRefreshedEvent> {

    private AutoConfMQPushConsumer consumer;

    @Autowired
    private LtoProcurementLtoComparisonService ltoProcurementComparisonService;

    @Autowired
    private LtoRateLimiterService ltoRateLimiterService;


    @PostConstruct
    public void init() {
        consumer = new AutoConfMQPushConsumer("sfa-recalculate-consumer", "sfa-procurement-comparison",(MessageListenerConcurrently) (msgs, context) -> {
            if (!msgs.isEmpty()) {
                for (MessageExt msg : msgs) {
                    // 取出traceContext
                    MessageHelper.fillContextFromMessage(TraceContext.get(), msg);
                    return consumeMessage(msg);
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        });
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (consumer != null && event.getApplicationContext().getParent() == null) {
            consumer.start();
        }
    }


    @PreDestroy
    public void close() {
        consumer.close();
    }

    private ConsumeConcurrentlyStatus consumeMessage(MessageExt body) {
        try {
            ProcurementMessage message = JSON.parseObject(body.getBody(), ProcurementMessage.class);
            message.setId(body.getMsgId());
            log.info("MsgId:{}, ProcurementMessage:{}", body.getMsgId(), message);
            ltoProcurementComparisonService.execute(message);
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("ProcurementComparisonListener consumeMessage {}", body, e);
            throw new RuntimeException(e);
        }finally {
            TraceContext.remove();
        }
    }
}
