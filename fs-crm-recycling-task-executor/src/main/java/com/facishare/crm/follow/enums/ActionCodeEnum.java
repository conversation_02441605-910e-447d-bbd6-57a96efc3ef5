package com.facishare.crm.follow.enums;

import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import org.apache.commons.lang.StringUtils;

/**
 * 跟进行为监听的actionCode
 */
public enum ActionCodeEnum {

    ADD("Add", "新增"),
    EDIT("Edit", "编辑"),
    IMPORT("Import", "导入"),
    CHANGE_OWNER("ChangeOwner", "更换负责人"),
    ALLOCATE("Allocate", "分配"),
    ALLOCATE_OLD("allocate", "分配"),
    CHOOSE("Choose", "分配"),
    RELATE("Relate", "关联"),//todo
    ASSOCIATE("Associate", "关联客户"),
    CHANGE_STAGE("ChangeStage", "商机阶段变更"),
    FINISH_VISIT("FinishVisit", "完成拜访"),
    ADD_ATTACH("AddAttach", "添加附件"),
    CHANGE_STATUS("ChangeStatus", "确认"),
    CONFIRM("Confirm", "确认"),
    ADD_EVENT("AddEvent", "发布销售记录"),
    CLOSE("Close", "无效"),
    MOVE("Move", "转移"),
    SEND_MAIL("SendMail", "发邮件"),
    Merge("Merge", "合并"),
    CHANGE_PARTNER("ChangePartner", "更换合作伙伴"),
    CHANGE_PARTNER_OWNER("ChangePartnerOwner", "更换外部负责人"),
    FOLLOW_UP("FollowUp", "跟进中"),
    DEAL("Deal", "转换"),
    ABOLISH("Abolish", "作废"),
    RETURN("Return", "退回"),
    TAKEBACK("TakeBack", "收回"),
    FINISH_CHECKIN("FinishCheckin", "完成外勤"),
    RECOVER("Recover", "恢复");


    private String actionCode;
    private String desc;


    public static boolean exist(String actionCode) {
        if (StringUtils.isBlank(actionCode)) {
            return false;
        }
        for (ActionCodeEnum value : ActionCodeEnum.values()) {
            if (value.getActionCode().equals(actionCode)) {
                return true;
            }
        }
        return false;
    }

    public static ActionCodeEnum actionCodeOf(String actionCode) {
        for (ActionCodeEnum value : values()) {
            if (value.getActionCode().equals(actionCode)) {
                return value;
            }
        }
        return null;
    }


    ActionCodeEnum(String actionCode, String desc) {
        this.actionCode = actionCode;
        this.desc = desc;
    }

    public String getActionCode() {
        return actionCode;
    }



    public static ActionCodeEnum convertTriggerType(String triggerTypeCode) {

        ApprovalFlowTriggerType approvalFlowTriggerType = null;

        for (ApprovalFlowTriggerType value : ApprovalFlowTriggerType.values()) {
            if (value.getTriggerTypeCode().equals(triggerTypeCode)) {
                approvalFlowTriggerType = value;
            }
        }
        if (approvalFlowTriggerType == null) {
            return null;
        }

        switch (approvalFlowTriggerType) {
            case CHANGE_OWNER:
                return CHANGE_OWNER;
            case CREATE:
                return ADD;
            case CHOOSE:
                return CHOOSE;
            case UPDATE:
                return EDIT;
            default:
                break;
        }
        return null;
    }

}
