package com.facishare.crm.follow.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-11-12 3:58 PM
 */
@Slf4j
@Data
public class CrmActionMQMessage {

    private String TenantID;
    private String TenantAccount;
    private String AppID;
    private String Package;
    private String ObjectApiName;
    private String ObjectID;
    private String ActionCode;
    private Content ActionContent;

    // 添加对字符串形式的 action_content 字段的支持
    @JSONField(name = "action_content")
    private String actionContentString;

    private String OperatorID;
    private String ActionTime;
    private String Source;
    private String eventId;


    //665 new
    @JSONField(name = "account_id")
    private String accountId;

    @JSONField(name = "leads_id")
    private String leadsId;

    @Data
    public static class Content {
        @Deprecated
        private String OwnerID;
        private String CustomerID;
        private String FollowTime;
        private String DealStatus;

        private String DealTime;
        private String Title;
        private String ContactName;
        private String EventID;

        @JSONField(name = "_id")
        private String id;

        //new
        private List<String> owner;

        @JSONField(name = "biz_status")
        private String bizStatus;

        @JSONField(name = "life_status")
        private String lifeStatus;

        @JSONField(name = "last_deal_closed_time")
        private Long lastDealClosedTime;

        @JSONField(name = "tenant_id")
        private String tenantId;

    }

    @JSONField(name = "life_status")
    private String lifeStatus;

    /**
     * 智能获取 ActionContent，优先使用对象形式，如果为空则尝试解析字符串形式
     * @return ActionContent 对象，如果都为空则返回 null
     */
    public Content getActionContentSmart() {
        // 优先返回对象形式的 ActionContent
        if (ActionContent != null) {
            return ActionContent;
        }

        // 如果对象形式为空，尝试解析字符串形式
        if (StringUtils.isNotBlank(actionContentString)) {
            try {
                return JSON.parseObject(actionContentString, Content.class);
            } catch (Exception e) {
                log.warn("Failed to parse actionContentString to Content object: {}", actionContentString, e);
            }
        }

        return null;
    }

}
