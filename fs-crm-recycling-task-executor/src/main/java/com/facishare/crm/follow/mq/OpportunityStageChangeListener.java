package com.facishare.crm.follow.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.rocketmq.common.message.MessageExt;
import com.facishare.crm.follow.enums.ActionCodeEnum;
import com.facishare.crm.follow.enums.ApiNameEnum;
import com.facishare.crm.follow.enums.EvenTypeEnum;
import com.facishare.crm.follow.model.OpportunityStageChangeMQMessage;
import com.facishare.crm.follow.model.UpdateFollowDealModel;
import com.facishare.crm.follow.service.CustomerFollowService;
import com.facishare.paas.appframework.common.mq.RocketMQMessageListener;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;

/**
 * 商机阶段变更跟进行为MQ监听
 * 消息体 : http://git.firstshare.cn/bpm/fs-stage-propeller/wikis/%E6%B6%88%E6%81%AF%E8%AF%B4%E6%98%8E
 *
 * <AUTHOR>
 * @create 2018-11-02 2:19 PM
 */
@Slf4j
public class OpportunityStageChangeListener implements RocketMQMessageListener {

    @Autowired
    private CustomerFollowService customerFollowService;

    @Autowired
    private IObjectDataService objectDataPgService;


    @Override
    public void consumeMessage(List<MessageExt> messages) {
        for (MessageExt message : messages) {
            try {
                String body = new String(message.getBody(), Charset.forName("UTF-8"));
                JSONObject jsonObject = JSONObject.parseObject(body);
                // 只会处理跳转的
                if (!jsonObject.getString("eventType").equals(EvenTypeEnum.MOVETO.getName())) {
                    continue;
                }
                OpportunityStageChangeMQMessage messageObj = JSON.parseObject(body, OpportunityStageChangeMQMessage.class);
                log.info("OpportunityStageChangeListener start,{},{}", message.getMsgId(),messageObj);
                newConsumeMessage(messageObj);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }


    private void newConsumeMessage(OpportunityStageChangeMQMessage messageObj) {
        String tenantId = messageObj.getTenantId();
        String objectId = messageObj.getObjectId();
        String describeApiName = messageObj.getEntityId();
        String customerId;
        ActionCodeEnum actionCode = ActionCodeEnum.CHANGE_STAGE;

        String settingType = customerFollowService.getObjectFollowDealSettingType(tenantId,
                describeApiName,
                actionCode);
        if (settingType == null || "2".equals(settingType)) {
            return;
        }

        UpdateFollowDealModel updateFollowDealModel = UpdateFollowDealModel.builder().tenantId(tenantId).build();

        List<UpdateFollowDealModel.Content> contents = new ArrayList<>();
        // 计算dealStatus
        UpdateFollowDealModel.Content content;

        try {
            // 查询该条商机的数据，然后找出客户id:(customerId)
            IObjectData objectData = objectDataPgService.findById(objectId, tenantId, describeApiName);

            List<ObjectReferenceFieldDescribe> accountObjs = customerFollowService
                    .objectReferenceFieldDescribe(tenantId, objectId, describeApiName);

            for (ObjectReferenceFieldDescribe accountObj : accountObjs) {
                if (objectData.get(accountObj.getApiName()) == null){
                    continue;
                }
                customerId = objectData.get(accountObj.getApiName()).toString();
                content = UpdateFollowDealModel.Content.builder()
                        .objectId(customerId)
                        .lastFollowTime(System.currentTimeMillis())
                        .apiName(ApiNameEnum.ACCOUNT_OBJ.getApiName())
                        .build();
                contents.add(content);
            }
            updateFollowDealModel.setContents(contents);
            customerFollowService.updateFollowDealTime(updateFollowDealModel);
        } catch (MetadataServiceException e) {
            log.error(e.getMessage(), e);
        }
    }
}
