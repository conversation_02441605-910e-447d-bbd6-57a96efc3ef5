package com.facishare.crm.follow.enums;

/**
 * feedtype
 */
public enum FeedTypeEnums {

    SHARE("1", "分享"),
    PLAN("2", "日志"),
    WORK("3", "指令"),
    APPROVE("4", "审批"),
    EVENT("5", "销售记录"),
    TASK("6", "任务"),
    SCHEDULE("7", "日程"),
    SERVICE("8", "服务记录"),
    SALEACTION("9", "销售流程"),
    PK("10", "PK"),
    EXTFEED("99", "外勤签到"),
    PROJECTTASK("201", "项目管理-任务"),
    WORKNOTICE("2003", "群通知"),
    TASKV2("2006", "任务2.0"),
    TIMINGMESSAGEREMAIND("15", "提醒");

    private String value;
    private String desc;

    FeedTypeEnums(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }
}
