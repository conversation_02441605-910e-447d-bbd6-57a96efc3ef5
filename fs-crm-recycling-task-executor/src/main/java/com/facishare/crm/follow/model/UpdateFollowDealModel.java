package com.facishare.crm.follow.model;

import com.facishare.crm.follow.enums.ActionCodeEnum;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-05-10 14:40
 */
@Data
@Builder
public class UpdateFollowDealModel {

    private String tenantId;

    private String apiName;


    private String dealStatus;

    private List<Content> contents;

    private String follower;

    private String lastModifiedBy; // 更新数据时的最后修改人
    @Data
    @Builder
    public static class Content {
        // 客户、线索
        private String apiName;
        private String dealStatus;
        private String objectId;
        private Long lastFollowTime;
        private Long dealTime;
        // 回收任务的actionCode
        private ActionCodeEnum recyclingActionCode;

        private String eventId;
        private String lastFollower;
        // 导致成交状态变更的源对象
        private String sourceApiName;
        // 导致成交状态变更的源字段
        private String fields;
        // 导致成交状态变更的源对象ID
        private String sourceObjectId;
    }


}
