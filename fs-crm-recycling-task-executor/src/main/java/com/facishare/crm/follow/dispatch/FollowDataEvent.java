package com.facishare.crm.follow.dispatch;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.fxiaoke.dispatcher.model.BaseEvent;
import com.fxiaoke.dispatcher.model.MessageField;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.fxiaoke.dispatcher.common.Constants.STATUS_DONE;
import static com.fxiaoke.dispatcher.common.Constants.STATUS_ERROR;

/**
 * @Description
 * <AUTHOR>
 * @Date 2021/8/20 16:12
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Slf4j
public class FollowDataEvent extends BaseEvent {

    @MessageField(name = "dataId")
    private String dataId;

    @MessageField(name = "apiName")
    private String objectApiName;

    @MessageField(name = "eventId")
    private String eventId;

    @MessageField(name = "fieldNames", addToSet = true)
    private Set<String> fieldApiNames;

    @MessageField(name = "data")
    private Map<String, Object> data;

    @MessageField(name = "omid")
    private String originalMessageId;

    @MessageField(name = "ombt")
    private Long originalBornTimestamp;

    @MessageField(name = "followTime", max = true)
    private Long lastFollowTime;

    @MessageField(name = "dealTime", max = true)
    private Long lastDealClosedTime;

    @MessageField(name = "follower", replace = true)
    private String lastFollowerId;

    @MessageField(name = "dealStatus")
    private String dealStatus;

    @MessageField(name = "overTime")
    private Boolean overTime;

    @MessageField(name = "lastModifiedBy", replace = true)
    private String lastModifiedBy;

    @Override
    public BaseEvent parse(Document doc) {
        super.parse(doc);
        this.setDataId(doc.getString("dataId"));
        this.setObjectApiName(doc.getString("apiName"));
        this.setEventId(doc.getString("eventId"));
        this.setFieldApiNames(Sets.newHashSet(doc.getList("fieldNames", String.class)));
        //this.setData((Map<String, Object>) doc.get("data"));
        this.setOriginalMessageId(doc.getString("omid"));
        this.setOriginalBornTimestamp(doc.getLong("ombt"));
        this.setLastFollowTime(doc.getLong("followTime"));
        this.setLastDealClosedTime(doc.getLong("dealTime"));
        this.setOverTime(doc.getBoolean("overTime"));
        this.setDealStatus(doc.getString("dealStatus"));
        this.setLastFollowerId(doc.getString("follower"));
        this.setLastModifiedBy(doc.getString("lastModifiedBy"));
        return this;
    }

    @Override
    public BaseEvent merge(BaseEvent baseEvent) {
        log.info("FollowDataEvent merge:{}", baseEvent);
        FollowDataEvent event = (FollowDataEvent) baseEvent;
        this.fieldApiNames.addAll(event.getFieldApiNames());
        if (baseEvent.getPriority() < this.getPriority()) {
            this.setPriority(baseEvent.getPriority());
        }
        if (baseEvent.getDispatchTime() < this.getDispatchTime()) {
            this.setDispatchTime(baseEvent.getDispatchTime());
        }
        return this;
    }

    public boolean sameGroup(FollowDataEvent event) {
        if (!getTenantId().equals(event.getTenantId())) {
            return false;
        }
        if (!getObjectApiName().equals(event.getObjectApiName())) {
            return false;
        }
        return CollectionUtils.isEqual(getFieldApiNames(), event.getFieldApiNames());
    }

    public static FollowDataGroup group(List<FollowDataEvent> events) {
        FollowDataGroup group = events.get(0).toGroup();
        for (int i = 1; i < events.size(); i++) {
            events.get(i).merge(group);
        }
        return group;
    }

    private void merge(FollowDataGroup group) {
        log.info("FollowDataEvent group :{}", group);
        group.getDataIds().add(getDataId());
        group.getMessageIds().add(getId().toHexString());
    }

    public static List<FollowDataGroup> groups(List<FollowDataEvent> events) {
        List<List<FollowDataEvent>> eventGroups = Lists.newArrayList();
        events.forEach(event -> {
            Optional<List<FollowDataEvent>> groupOpt = eventGroups.stream().filter(x -> x.get(0).sameGroup(event)).findFirst();
            if (groupOpt.isPresent()) {
                groupOpt.get().add(event);
            } else {
                eventGroups.add(Lists.newArrayList(event));
            }
        });
        return eventGroups.stream().map(FollowDataEvent::group).collect(Collectors.toList());
    }

    private FollowDataGroup toGroup() {
        return FollowDataGroup.builder()
                .tenantId(getTenantId())
                .objectApiName(getObjectApiName())
                .dataIds(Sets.newHashSet(getDataId()))
                .fieldApiNames(Sets.newHashSet(getFieldApiNames()))
                .build();
    }

    public void markDone() {
        this.setStatus(STATUS_DONE);
    }

    public void markError() {
        this.setStatus(STATUS_ERROR);
    }

}
