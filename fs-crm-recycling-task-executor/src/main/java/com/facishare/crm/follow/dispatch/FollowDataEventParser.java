package com.facishare.crm.follow.dispatch;

import com.fxiaoke.dispatcher.model.BaseEvent;
import com.fxiaoke.dispatcher.parser.EventParser;
import org.apache.rocketmq.common.message.MessageExt;
import org.bson.Document;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2021/8/23 17:37
 */

public class FollowDataEventParser implements EventParser {
    @Override
    public BaseEvent parse(MessageExt messageExt) {
        return null;
    }

    @Override
    public List<BaseEvent> parseMany(MessageExt messageExt) {
        return null;
    }

    @Override
    public BaseEvent parse(Document document) {
        return new FollowDataEvent().parse(document);
    }
}
