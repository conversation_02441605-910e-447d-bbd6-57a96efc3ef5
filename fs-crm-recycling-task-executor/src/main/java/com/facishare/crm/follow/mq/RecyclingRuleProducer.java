package com.facishare.crm.follow.mq;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.follow.model.RecalculateMessage;
import com.facishare.crm.follow.util.FollowGrayUtils;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

/**
 * @Description
 * <AUTHOR>
 * @Date 2021/4/7 22:30
 */


@Component
public class RecyclingRuleProducer {
    private static final String MQ_PRODUCER_CONFIG_NAME = "fs-crm-task-sfa-mq.ini";
    private static final String MQ_PRODUCER_CONFIG_SECTION_NAME = "sfa-recycling-recalculate-producer";
    private static final String TAGS = "follow_calculate";
    private static final String TAGS_VIP = "follow_calculate_vip";
    private AutoConfMQProducer producer;
    private static final String NOMON_CALLBACK_TOPIC_PRODUCER = "nomon-callback-producer";
    private static AutoConfMQProducer nomonCallbackProducer;

    //计算联系人、线索tags
    private static final String DEAL_MEMBER_RELATIONSHIP_TAG = "deal_member_relationship_tag";



    @Autowired
    private FollowGrayUtils followGrayUtils;

    @PostConstruct
    public void init() {
        producer = new AutoConfMQProducer(MQ_PRODUCER_CONFIG_NAME, MQ_PRODUCER_CONFIG_SECTION_NAME);
        nomonCallbackProducer = new AutoConfMQProducer(MQ_PRODUCER_CONFIG_NAME, NOMON_CALLBACK_TOPIC_PRODUCER);
    }

    @PreDestroy
    public void destroy() {
        producer.close();
        nomonCallbackProducer.close();
    }

    public void sendNomonProducerMQ(String tag, String messageString, String tenantId) {
        Message message = new Message(nomonCallbackProducer.getDefaultTopic(), tag, messageString.getBytes());
        message.putUserProperty("x-fs-ei", tenantId);
        SendResult sendResult = nomonCallbackProducer.send(message);
        if (sendResult.getSendStatus() != SendStatus.SEND_OK) {
            throw new RuntimeException("send mq failed. " + sendResult.getSendStatus());
        }
    }


    /**
     * 发送消息
     *
     * @param
     */
    private void sendRecalculate(String tags, RecalculateMessage messageObject, String tenantId) {
        String messageString = JSON.toJSONString(messageObject);
        Message message = new Message(producer.getDefaultTopic(), tags, messageString.getBytes());
        message.putUserProperty("x-fs-ei", tenantId);
        message.setTags(getMsgTags(tenantId));
        SendResult sendResult = producer.send(message);
        if (sendResult.getSendStatus() != SendStatus.SEND_OK) {
            throw new RuntimeException("send mq failed. " + sendResult.getSendStatus());
        }
    }

    public void sendRecalculate(RecalculateMessage messageObject, String tenantId) {
        sendRecalculate(TAGS, messageObject, tenantId);
    }

    public String getMsgTags(String tenantId) {
        if (followGrayUtils.isRecalculateVIP(tenantId)) {
            return TAGS_VIP;
        } else {
            return TAGS;
        }
    }
}
