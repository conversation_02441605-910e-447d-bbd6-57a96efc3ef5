package com.facishare.crm.follow.service;

import com.facishare.crm.follow.dispatch.FollowDataEvent;
import com.facishare.crm.follow.enums.ActionCodeEnum;
import com.facishare.crm.follow.model.UpdateFollowDealModel;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;

import java.util.List;

public interface CustomerFollowService {


    void updateFollowDealTime(UpdateFollowDealModel model);

    void updateFollowDealTimeByRefresh(UpdateFollowDealModel model);
    void updateFollowDealTimeByRefreshGray(UpdateFollowDealModel model);


    /**
     * 查询跟进行为配置表
     *
     * @param tenantId
     * @param objectApiName source_object_api_name
     * @param actionCode    ${@link ActionCodeEnum}
     * @return settingType  如果为null 则表中不存在该配置.
     */
    String getObjectFollowDealSettingType(String tenantId, String objectApiName, ActionCodeEnum actionCode);


    /**
     * 6.6.5 增加该方法，新增线索
     * @param tenantId
     * @param sourceObjectApiName 触发跟进的对象
     * @param actionCode  触发的动作
     * @param objectApiName 跟进动作所改变的对象(AccountObj,LeadsObj)
     * @return
     */
    String getObjectFollowDealSettingType(String tenantId, String sourceObjectApiName, ActionCodeEnum actionCode,String objectApiName) ;


    /**
     *
     * @param tenantId
     * @param sourceObjectApiName
     * @param actionCodes
     * @param objectApiName
     * @return
     */
    String getSettingTypeByActionCodes(String tenantId, String sourceObjectApiName, String objectApiName,List<String> actionCodes);


    /**
     * 6.6.5 增加该方法，判断 sourceObjectApiName 对象 是否设置了成交
     * @param tenantId
     * @param sourceObjectApiName  商机赢单、商机2.0赢单、销售订单确认、新建生效合同
     * @param objectApiName  AccountObj
     * @return
     */
    Boolean hasSettingDeal(String tenantId, String sourceObjectApiName,String objectApiName);


    /**
     *
     * @param tenantId
     * @param objectId
     * @param describeApiName
     * @return
     */
    List<ObjectReferenceFieldDescribe> objectReferenceFieldDescribe(String tenantId, String objectId,
                                                                    String describeApiName);

    /**
     *
     * @param tenantId
     * @param objectId
     * @param describeApiName
     * @param targetApiName
     * @return
     */
    List<ObjectReferenceFieldDescribe> objectReferenceFieldDescribe(String tenantId, String objectId,
                                                                    String describeApiName, String targetApiName);





    void sendRecalculateTask(String tenantId,List<UpdateFollowDealModel.Content> contents);

    void sendRecalculateTask(String tenantId,String apiName,String objectId);


    /**
     * 更新线索状态为跟进中
     * @param tenantId
     * @param leadsId
     */
    void updateLeadsStatus(String tenantId, String leadsId, String employeeId, String lastFollowTime);

    void updateLeadsOverTime(String tenantId, String leadsId);

    void updateFields(FollowDataEvent event);
}
