package com.facishare.crm.follow.model;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * @Description 客户/线索 动作变更后，发送重算过期时间的消息体
 * <AUTHOR>
 * @Date 2019-03-13 16:00
 */
@Data
@Builder
public class RecalculateMessage {

    private String tenantId;

    private String objectApiName;

    /**
     * actionCode是规则变更时:
     *      该字段为公海id
     *      其他情况为 数据的id
     */
    private String objectId;

    /**
     * 动作
     */
    private String actionCode;

    /**
     * 目标id
     *      客户为：公海id
     *      线索为：线索池id
     */
    private String targetId;

    /**
     * 延期天数
     */
    private Double extendDays;

    /**
     * false:非公海
     * true:公海
     */
    private Boolean highSeas;

    /**
     * 非公海部门id
     */
    private List<String> deptIds;

}
