package com.facishare.crm.follow.mq.slow;

import com.facishare.crm.follow.service.ObjectDataChangeConsume;
import com.facishare.crm.recycling.task.executor.producer.FollowMessageForwardProducer;
import com.facishare.crm.sfa.audit.log.context.SFALogContext;
import com.facishare.crm.sfa.lto.riskbrain.service.RiskBrainCommonService;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

@Slf4j
@Component
public class ObjectDataSlowListener implements ApplicationListener<ContextRefreshedEvent> {

    private AutoConfMQPushConsumer consumer;

    @Autowired
    private ObjectDataChangeConsume objectDataChangeConsume;

    @Autowired
    private FollowMessageForwardProducer followMessageForwardProducer;


    @PostConstruct
    public void init() {
        consumer = new AutoConfMQPushConsumer("fs-crm-task-sfa-mq.ini", "object-data-forward-slow", (MessageListenerConcurrently) (msgs, context) -> {
            if (!msgs.isEmpty()) {
                try {
                    SFALogContext.putVariable("bizName", "OBJECT_DATA_SLOW");
                    objectDataChangeConsume.consumeMessage(msgs);
                } catch (Exception e) {
                    log.error("msg:{}", msgs, e);
                    throw new RuntimeException(e);
                } finally {
                    SFALogContext.clearContext();
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        });
    }

    @PreDestroy
    public void close() {
        consumer.close();
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (consumer != null && event.getApplicationContext().getParent() == null) {
            consumer.start();
        }
    }
}
