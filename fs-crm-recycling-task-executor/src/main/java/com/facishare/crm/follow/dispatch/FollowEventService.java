package com.facishare.crm.follow.dispatch;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.fxiaoke.dispatcher.store.CollectionRouter;
import com.fxiaoke.dispatcher.store.EventStore;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.fxiaoke.dispatcher.common.Constants.STATUS_READY;

/**
 * @Description
 * <AUTHOR>
 * @Date 2021/8/20 17:54
 */

@Service
@Slf4j
public class FollowEventService {

    @Autowired
    private EventStore eventStore;


    public void upsertEvents(List<FollowDataEvent> events) {
        if (CollectionUtils.empty(events)) {
            return;
        }

        events.forEach(x -> {
            x.setStatus(STATUS_READY);
            x.setTopic(CollectionRouter.getTopicName(x.getTenantId()));
        });


        try {
            eventStore.batchUpsert(events, true);
        } catch (Exception e) {
            log.error("upsertEvents error,events:{}", events, e);
            throw new RuntimeException(e);
        }
    }

}
