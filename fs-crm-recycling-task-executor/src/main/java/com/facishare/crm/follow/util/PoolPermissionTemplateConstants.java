package com.facishare.crm.follow.util;

/**
 * <AUTHOR> gongchunru
 * @date : 2023/12/26 17:53
 * @description:
 */
public interface PoolPermissionTemplateConstants {
    class Field {
        public static final String Id = "id";
        public static final String Last_Modified_By = "last_modified_by";
        public static final String Created_By = "created_by";
        public static final String Last_Modified_Time = "last_modified_time";
        public static final String Create_Time = "create_time";
        public static final String Is_Delete = "is_deleted";
        public static final String Pool_Id = "pool_id";
        public static final String Object_Api_Name = "object_api_name";
        public static final String Field_Name = "field_name";
        public static final String Is_Visible = "is_visible";
    }
}
