package com.facishare.crm.follow.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.rocketmq.common.message.MessageExt;
import com.facishare.crm.follow.model.FieldDescribeChangeMQMessage;
import com.facishare.crm.follow.model.ObjectDescribeChangeMQMessage;
import com.facishare.crm.follow.util.ObjectFollowDealSettingObj;
import com.facishare.crm.recycling.task.executor.util.SearchUtil;
import com.facishare.paas.appframework.common.mq.RocketMQMessageListener;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.MetaDataService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;

import java.nio.charset.Charset;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.facishare.crm.follow.util.ConstantUtils.IS_DELETED;
import static com.facishare.crm.follow.util.ConstantUtils.OBJECT_FOLLOW_DEAL_SETTING_OBJ;
import static com.facishare.crm.recycling.task.executor.util.ConstantUtils.ACCOUNT_OBJ;
import static com.facishare.crm.recycling.task.executor.util.ConstantUtils.LEADS_OBJ;

@Slf4j
public class ObjectDescribeChangeListener implements RocketMQMessageListener {
    @Autowired
    private IObjectDescribeService objectDescribeService;
    @Autowired
    private MetaDataService metaDataService;

    @Override
    public void consumeMessage(List<MessageExt> messageExtList) {
        if (CollectionUtils.empty(messageExtList)) {
            return;
        }

        try {
            for (MessageExt message : messageExtList){
                String messageBody = new String(message.getBody(), Charset.forName("UTF-8"));
                log.debug("ObjectDescribeChangeListener:{}",message);
                if(StringUtils.isBlank(messageBody)){
                    continue;
                }
                List<String> objectApiNameList  = Lists.newArrayList();
                String tenantId = "";
                JSONObject object = JSON.parseObject(messageBody);
                String opName = object.getString("name");
                String op = object.getString("op");
                if(Objects.equals(opName,"object_describe")){
                    if(!(Objects.equals(op, "describe_disable") || Objects.equals(op, "describe_enable") || Objects.equals(op, "describe_delete"))){
                        continue;
                    }
                    ObjectDescribeChangeMQMessage objectDescribeChangeMQMessage = JSON.parseObject(messageBody, ObjectDescribeChangeMQMessage.class);
                    if(CollectionUtils.empty(objectDescribeChangeMQMessage.getBody())){
                        continue;
                    }
                    tenantId = objectDescribeChangeMQMessage.getTenantId();
                    objectDescribeChangeMQMessage.getBody().forEach(x -> {
                        if(!objectApiNameList.contains(x.getObjectApiName())){
                            objectApiNameList.add(x.getObjectApiName());
                        }
                    });
                    if(CollectionUtils.empty(objectApiNameList)){
                        continue;
                    }
                    switch (op){
                        case "describe_disable":
                        case "describe_enable":
                            processObjectFollowDealSetting(tenantId, objectApiNameList);
                            break;
                        case "describe_delete":
                            processDeletedObjectFollowDealSetting(tenantId, objectApiNameList);
                            break;
                    }
                    continue;
                }

                if(!Objects.equals(opName,"object_field")){
                    continue;
                }
                if(!(Objects.equals(op, "field_disable") || Objects.equals(op, "field_enable") || Objects.equals(op, "field_delete") || Objects.equals(op, "field_add"))){
                    continue;
                }
                FieldDescribeChangeMQMessage jsonMessage = JSON.parseObject(messageBody, FieldDescribeChangeMQMessage.class);
                if(CollectionUtils.empty(jsonMessage.getBody())){
                    continue;
                }
                tenantId = jsonMessage.getTenantId();
                for (FieldDescribeChangeMQMessage.Content content : jsonMessage.getBody()){
                    List<FieldDescribeChangeMQMessage.FieldDescribe> referenceFieldList = content.getFields().stream()
                            .filter(x -> Objects.equals(x.getFieldType(), "object_reference"))
                            .collect(Collectors.toList());
                    if(org.apache.commons.collections.CollectionUtils.isEmpty(referenceFieldList)){
                        continue;
                    }
                    for (FieldDescribeChangeMQMessage.FieldDescribe field : referenceFieldList) {
                        if (content.getObjectApiName().equals(field.getReferenceObjectApiName())) {
                            continue;
                        }
                        if(!LEADS_OBJ.equals(field.getReferenceObjectApiName())
                            && !ACCOUNT_OBJ.equals(field.getReferenceObjectApiName())){
                            continue;
                        }
                        String apiName = content.getObjectApiName();
                        objectApiNameList.add(apiName);
                    }
                }
                if(CollectionUtils.empty(objectApiNameList)){
                    continue;
                }
                processObjectFollowDealSetting(tenantId, objectApiNameList);
            }
        }catch (Exception e){
            log.error(e.getMessage());
        }
    }

    private  void  processObjectFollowDealSetting(String tenantId, List<String> objectApiNameList){
        if(CollectionUtils.empty(objectApiNameList)){
            return;
        }
        List<IObjectData> queryData = getObjectFollowDealSetting(tenantId, objectApiNameList);
        if(CollectionUtils.empty(queryData)){
            return;
        }
        try {
            List<IObjectDescribe> objectDescribeList = objectDescribeService.findDescribeListByApiNames(tenantId, objectApiNameList);

            if(CollectionUtils.empty(objectDescribeList)){
                return;
            }
            for(IObjectDescribe objectDescribe : objectDescribeList){
                List<IObjectData> dataList = queryData.stream()
                        .filter(x -> Objects.equals(x.get(ObjectFollowDealSettingObj.SOURCE_OBJECT_API_NAME).toString(), objectDescribe.getApiName()))
                        .collect(Collectors.toList());
                if(CollectionUtils.empty(dataList)){
                    continue;
                }
                if(!objectDescribe.isActive()){
                    for (IObjectData data : dataList){
                        data.set(ObjectFollowDealSettingObj.IS_VISIBLE, false);
                    }
                }else {
                    List<ObjectReferenceFieldDescribe> referenceFiledList = objectDescribe.getFieldDescribes().stream()
                            .filter(x -> Objects.equals(x.getType(), "object_reference") && x.isActive())
                            .map(x -> { return (ObjectReferenceFieldDescribe)x; }).collect(Collectors.toList());

                    if(CollectionUtils.empty(referenceFiledList)){
                        for (IObjectData data : dataList){
                            data.set(ObjectFollowDealSettingObj.IS_VISIBLE, false);
                        }
                    }else {
                        List<String> referenceObjectApiNameList = Lists.newArrayList();
                        for (ObjectReferenceFieldDescribe referenceField : referenceFiledList){
                            if(referenceObjectApiNameList.contains(referenceField.getTargetApiName())){
                                continue;
                            }
                            referenceObjectApiNameList.add(referenceField.getTargetApiName());
                        }
                        for (IObjectData data : dataList){
                            String apiName = data.get(ObjectFollowDealSettingObj.OBJECT_API_NAME).toString();
                            if(referenceObjectApiNameList.contains(apiName)){
                                data.set(ObjectFollowDealSettingObj.IS_VISIBLE, true);
                            }else {
                                data.set(ObjectFollowDealSettingObj.IS_VISIBLE, false);
                            }
                        }
                    }
                }
                metaDataService.batchUpdate(dataList, getSupperUser(tenantId));
            }
        }catch (Exception e){
            log.error(e.getMessage());
        }
    }

    private  void  processDeletedObjectFollowDealSetting(String tenantId, List<String> objectApiNameList){
        if(CollectionUtils.empty(objectApiNameList)){
            return;
        }
        List<IObjectData> queryData = getObjectFollowDealSetting(tenantId, objectApiNameList);
        if(CollectionUtils.empty(queryData)){
            return;
        }
        metaDataService.bulkInvalidAndDeleteWithSuperPrivilege(queryData, getSupperUser(tenantId));
    }

    private List<IObjectData> getObjectFollowDealSetting(String tenantId, List<String> objectApiNameList) {
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(100);
        searchQuery.setOffset(0);
        searchQuery.setNeedReturnCountNum(false);
        searchQuery.setPermissionType(0);
        List filters = Lists.newLinkedList();
        SearchUtil.fillFilterIn(filters, ObjectFollowDealSettingObj.SOURCE_OBJECT_API_NAME, objectApiNameList);
        SearchUtil.fillFilterEq(filters, IS_DELETED, "0");
        searchQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = metaDataService.findBySearchQuery(getSupperUser(tenantId),
                OBJECT_FOLLOW_DEAL_SETTING_OBJ, searchQuery);
        return queryResult.getData();
    }

    @NotNull
    private User getSupperUser(String tenantId) {
        return new User(tenantId, "-10000", "", "");
    }
}
