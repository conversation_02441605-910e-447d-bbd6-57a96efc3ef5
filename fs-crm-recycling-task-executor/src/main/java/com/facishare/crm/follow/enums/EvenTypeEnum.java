package com.facishare.crm.follow.enums;

/**
 * <AUTHOR>
 * @create 2018-11-05 11:06 AM
 */
public enum EvenTypeEnum {

    TRIGGER("trigger","触发消息"),
    MOVETO("moveTo","阶段跳转"),
    CHANGE("change","实例切换");

    private String name;
    private String desc;

    EvenTypeEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    public String getName() {
        return name;
    }
}
