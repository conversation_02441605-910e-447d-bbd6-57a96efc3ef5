package com.facishare.crm.follow.service.impl;

import com.facishare.crm.follow.enums.ActionCodeEnum;
import com.facishare.crm.follow.enums.ApiNameEnum;
import com.facishare.crm.follow.model.CustomObjectDataChangeMQMessage;
import com.facishare.crm.follow.model.UpdateFollowDealModel;
import com.facishare.crm.follow.service.CommonService;
import com.facishare.crm.follow.util.ObjectFollowDealSettingObj;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.recycling.task.executor.util.SearchUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.pod.exception.DbRouterException;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.follow.util.ConstantUtils.*;
import static com.facishare.crm.recycling.task.executor.util.ConstantUtils.CREATE_TIME;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-05-16 17:52
 */
@Slf4j
@Component
public class CommonServiceImpl implements CommonService {

    @Autowired
    private IObjectDataService objectDataPgService;

    @Autowired
    private MetaDataFindService metaDataFindService;

    @Autowired
    private IObjectDescribeService objectDescribeService;

    private FsGrayReleaseBiz sfaGray = FsGrayRelease.getInstance("sfa");


    @Override
    public void getDealStatusAndTime(UpdateFollowDealModel.Content content, String tenantId, String describeApiName) {
        List<Map> newOppoList;
        String sql;
        String accountId = content.getObjectId();
        String eiAccountIdWhere = String.format(" and tenant_id = '%s' and account_id = '%s'", tenantId, accountId);
        if (describeApiName.equals(ApiNameEnum.OPPORTUNITY_OBJ.getApiName())) {
            sql = "select sales_stg_changed_time as confirm_time from biz_opportunity where is_deleted = 0 and life_status = 'normal' and biz_status = 'win' "
                    + eiAccountIdWhere
                    + " order by sales_stg_changed_time desc ";
        } else if (describeApiName.equals(ApiNameEnum.NEW_OPPORTUNITY_OBJ.getApiName())) {
            sql = "select stg_changed_time as confirm_time  from new_opportunity where is_deleted = 0 and life_status = 'normal' and sales_status = '2' "
                    + eiAccountIdWhere
                    + " order by stg_changed_time desc";
        } else if (describeApiName.equals(ApiNameEnum.SALES_ORDER_OBJ.getApiName())) {
            sql = "select case when confirm_time is null then create_time else confirm_time end as confirm_time,create_time from biz_sales_order where is_deleted = 0 and life_status in('normal','in_change') "
                    + eiAccountIdWhere
                    + " order by confirm_time desc,create_time desc ";
        } else if (describeApiName.equals(ApiNameEnum.SALE_CONTRACT_API_NAME.getApiName())) {
            sql = "select create_time from biz_sale_contract where is_deleted = 0 and life_status in('normal','in_change') "
                    + eiAccountIdWhere
                    + " order by create_time desc ";
        } else {
            sql = "select case when confirm_time is null then create_time else confirm_time end as confirm_time,create_time from biz_contract where is_deleted = 0 and life_status = 'normal' "
                    + eiAccountIdWhere
                    + " order by confirm_time desc,create_time desc ";
        }
        sql += " limit 3";

        try {
            newOppoList = objectDataPgService.findBySql(tenantId, sql);
        } catch (MetadataServiceException e) {
            log.error("account dealStatus find error:{}", sql, e);
            throw new RuntimeException(e);
        }
        if (CollectionUtils.empty(newOppoList)) {
            log.info("getDealStatusAndTime is empty sql:{}", sql);
            content.setDealStatus("1");
            content.setDealTime(null);
            return;
        }
        log.info("sql:{},count:{}", sql, newOppoList.size());
        if (newOppoList.size() > 1) {
            content.setDealStatus("3");
        } else if (newOppoList.size() == 1) {
            content.setDealStatus("2");
        } else {
            return;
        }
        if (newOppoList.get(0) == null) {
            return;
        }
        if (newOppoList.get(0).get(CONFIRM_TIME) != null) {
            content.setDealTime((Long) newOppoList.get(0).get(CONFIRM_TIME));
        } else if (newOppoList.get(0).get(CREATE_TIME) != null) {
            content.setDealTime((Long) newOppoList.get(0).get(CREATE_TIME));
        }
    }

    @Override
    public Map<String, String> getObjectFollowDealSettingType(String tenantId, List<String> objectApiName, ActionCodeEnum actionCode) {
        if (CollectionUtils.empty(objectApiName)) {
            return null;
        }
        Map<String, String> map = Maps.newHashMap();
        SearchTemplateQuery searchQuery = getSearchTemplateQuery(2);
        List<IFilter> filters = Lists.newLinkedList();
        SearchUtil.fillFilterIn(filters, ObjectFollowDealSettingObj.SOURCE_OBJECT_API_NAME, objectApiName);
        SearchUtil.fillFilterEq(filters, ObjectFollowDealSettingObj.IS_ENABLE, true);
        SearchUtil.fillFilterEq(filters, ObjectFollowDealSettingObj.ACTION_CODE, actionCode.getActionCode());
        SearchUtil.fillFilterEq(filters, ObjectFollowDealSettingObj.IS_VISIBLE, true);
        SearchUtil.fillFilterEq(filters, IS_DELETED, "0");
        searchQuery.setFilters(filters);
        User user = new User(tenantId, "-10000", "", "");
        QueryResult<IObjectData> queryResult = null;
        try {
            queryResult = metaDataFindService.findBySearchQuery(user, OBJECT_FOLLOW_DEAL_SETTING_OBJ, searchQuery);
        } catch (Exception e) {
            log.error("find ObjectFollowDealSettingObj error,tenantId:{},", tenantId, e);
            throw e;
        }
        if (queryResult == null || CollectionUtils.empty(queryResult.getData())) {
            return null;
        } else {
            for (IObjectData datum : queryResult.getData()) {
                map.put(datum.get(ObjectFollowDealSettingObj.OBJECT_API_NAME).toString(), datum.get(ObjectFollowDealSettingObj.SOURCE_OBJECT_API_NAME).toString());
            }
        }
        return map;
    }

    @Override
    public Map<String, List<String>> getFollowSetting(String tenantId, List<String> objectApiName, ActionCodeEnum actionCode) {
        // todo 1 编辑操作，排除线索、客户以外的对象
        //      2. 收回、退回、转移、等等操作，只需要关注线索和客户.

        if (!isAccountLeadsFollow(objectApiName, actionCode)) {
            return null;
        }
        Map<String, List<String>> map = Maps.newHashMap();
        SearchTemplateQuery searchQuery = getSearchTemplateQuery(objectApiName.size() * 2);
        List<IFilter> filters = Lists.newLinkedList();
        SearchUtil.fillFilterIn(filters, ObjectFollowDealSettingObj.SOURCE_OBJECT_API_NAME, objectApiName);
        SearchUtil.fillFilterEq(filters, ObjectFollowDealSettingObj.ACTION_CODE, actionCode.getActionCode());
        SearchUtil.fillFilterEq(filters, ObjectFollowDealSettingObj.SETTING_TYPE, "1");
        QueryResult<IObjectData> queryResult = getFollowSettingMap(tenantId, searchQuery, filters);
        if (CollectionUtils.empty(queryResult.getData())) {
            return null;
        } else {
            List<String> leads = new ArrayList<>();
            List<String> accounts = new ArrayList<>();
            for (IObjectData datum : queryResult.getData()) {
                if (ACCOUNT_OBJ.equals(datum.get(ObjectFollowDealSettingObj.OBJECT_API_NAME).toString())) {
                    accounts.add(datum.get(ObjectFollowDealSettingObj.SOURCE_OBJECT_API_NAME).toString());
                } else {
                    leads.add(datum.get(ObjectFollowDealSettingObj.SOURCE_OBJECT_API_NAME).toString());
                }
            }
            if (CollectionUtils.notEmpty(accounts)) {
                map.put(ACCOUNT_OBJ, accounts);
            }
            if (CollectionUtils.notEmpty(leads)) {
                map.put(LEADS_OBJ, leads);
            }
        }
        return map;
    }


    @Override
    public Map<String, Map<String, Set<String>>> getFollowSetting(String tenantId) {
        if (StringUtils.isBlank(tenantId)) {
            return null;
        }
        Map<String, Map<String, Set<String>>> map = new HashMap<>();
        SearchTemplateQuery searchQuery = getSearchTemplateQuery(200);
        List<IFilter> filters = Lists.newLinkedList();
        SearchUtil.fillFilterIn(filters, ObjectFollowDealSettingObj.OBJECT_API_NAME, Lists.newArrayList(Utils.ACCOUNT_API_NAME, Utils.LEADS_API_NAME));
        SearchUtil.fillFilterEq(filters, ObjectFollowDealSettingObj.SETTING_TYPE, "1");
        QueryResult<IObjectData> queryResult = getFollowSettingMap(tenantId, searchQuery, filters);
        if (CollectionUtils.empty(queryResult.getData())) {
            return null;
        } else {
            for (IObjectData datum : queryResult.getData()) {
                String sourceObjectApiName = datum.get(ObjectFollowDealSettingObj.SOURCE_OBJECT_API_NAME).toString();
                String objectApiName = datum.get(ObjectFollowDealSettingObj.OBJECT_API_NAME).toString();
                String actionCode = datum.get(ObjectFollowDealSettingObj.ACTION_CODE).toString();
                map.computeIfAbsent(sourceObjectApiName, k -> new HashMap<>())
                        .computeIfAbsent(actionCode, k -> new HashSet<>())
                        .add(objectApiName);
            }
        }
        return map;
    }

    @Override
    public Map<String, Boolean> getFollowDealSetting(String tenantId) {
        Map<String, Boolean> map = new HashMap<>();
        SearchTemplateQuery searchQuery = getSearchTemplateQuery(1);
        List<IFilter> filters = Lists.newLinkedList();
        SearchUtil.fillFilterEq(filters, ObjectFollowDealSettingObj.OBJECT_API_NAME, Utils.ACCOUNT_API_NAME);
        SearchUtil.fillFilterEq(filters, ObjectFollowDealSettingObj.SETTING_TYPE, "2");
        QueryResult<IObjectData> queryResult = getFollowSettingMap(tenantId, searchQuery, filters);
        if (queryResult == null || CollectionUtils.empty(queryResult.getData())) {
            return map;
        } else if (queryResult.getData().size() == 1) {
            Object isUserDefineSetting = queryResult.getData().get(0).get(ObjectFollowDealSettingObj.IS_USER_DEFINE_SETTING);
            String sourceObjectApiName = queryResult.getData().get(0).get(ObjectFollowDealSettingObj.SOURCE_OBJECT_API_NAME) == null
                    ? "" : queryResult.getData().get(0).get(ObjectFollowDealSettingObj.SOURCE_OBJECT_API_NAME).toString();
            // 未自定义，返回 true
            if (isUserDefineSetting == null || !(boolean) isUserDefineSetting) {
                map.put(sourceObjectApiName, true);
                return map;
            }
            // 自定义成交状态，返回false
            map.put(sourceObjectApiName, false);
            return map;
        } else if (queryResult.getData().size() > 1) {
            log.warn("getFollowDealSetting error:{},{}", tenantId, queryResult.getData());
        }
        return map;
    }

    private @NotNull SearchTemplateQuery getSearchTemplateQuery(int limit) {
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(limit);
        searchQuery.setOffset(0);
        searchQuery.setNeedReturnCountNum(false);
        searchQuery.setPermissionType(0);
        return searchQuery;
    }


    @Nullable
    private QueryResult<IObjectData> getFollowSettingMap(String tenantId, SearchTemplateQuery searchQuery, List<IFilter> filters) {
        SearchUtil.fillFilterEq(filters, ObjectFollowDealSettingObj.IS_VISIBLE, true);
        SearchUtil.fillFilterEq(filters, ObjectFollowDealSettingObj.IS_ENABLE, true);
        SearchUtil.fillFilterEq(filters, IS_DELETED, "0");
        searchQuery.setFilters(filters);
        User user = new User(tenantId, "-10000", "", "");
        QueryResult<IObjectData> queryResult = null;
        try {
            queryResult = metaDataFindService.findBySearchQuery(user, OBJECT_FOLLOW_DEAL_SETTING_OBJ, searchQuery);
        } catch (DbRouterException e) {
            log.warn("getFollowSetting error,tenantId:{},", tenantId, e);
        } catch (Exception e) {
            log.error(" getFollowSetting error,tenantId:{},", tenantId, e);
            throw e;
        }

        return queryResult;
    }

    @Override
    public List<ObjectReferenceFieldDescribe> objectReferenceFieldDescribe(String tenantId, String objectId,
                                                                           String describeApiName) {
        List<ObjectReferenceFieldDescribe> accountObjs = new ArrayList<>();
        try {
            IObjectDescribe objectDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, describeApiName);

            // 找出所有的关联类型
            List<ObjectReferenceFieldDescribe> referenceFiledList = objectDescribe.getFieldDescribes().stream()
                    .filter(x -> Objects.equals(x.getType(), "object_reference") && x.isActive())
                    .map(x -> (ObjectReferenceFieldDescribe) x).collect(Collectors.toList());

            // 从所有关联类型中找出关联的是客户的field
            accountObjs = referenceFiledList.stream().filter(x -> Objects.equals(x.getTargetApiName(), Utils.ACCOUNT_API_NAME)
                            || Objects.equals(x.getTargetApiName(), Utils.LEADS_API_NAME))
                    .collect(Collectors.toList());
        } catch (MetadataServiceException e) {
            log.warn("find objectReferenceFieldDescribe throw exception {}", e.getMessage());
        }
        return accountObjs;
    }

    @Override
    public UpdateFollowDealModel.Content buildContent(String objectId, String apiName, Long dealTime, Long lastFollowTime, String eventId) {
        UpdateFollowDealModel.Content content = UpdateFollowDealModel.Content.builder()
                .objectId(objectId)
                .apiName(apiName)
                .lastFollowTime(lastFollowTime)
                .build();

        if (StringUtils.isNotBlank(eventId)) {
            content.setEventId(eventId);
        }
        if (dealTime != null) {
            content.setDealTime(dealTime);
        }
        return content;
    }

    @Override
    public UpdateFollowDealModel.Content buildContent(String objectId, String apiName, Long dealTime, Long lastFollowTime, String eventId, String lastFollower) {
        UpdateFollowDealModel.Content content = buildContent(objectId, apiName, dealTime, lastFollowTime, eventId);
        content.setLastFollower(lastFollower);
        return content;
    }

    /**
     * If the list of API name is not contains account or leads, and the action code is edit, return false
     *
     * @param apiNames   The name of the API that was called.
     * @param actionCode The action code that was passed in.
     * @return The return value is a boolean.
     */
    private boolean isAccountLeadsFollow(List<String> apiNames, ActionCodeEnum actionCode) {
        if (CollectionUtils.empty(apiNames)) {
            return false;
        }
        if (!apiNames.contains(Utils.ACCOUNT_API_NAME) && !apiNames.contains(Utils.LEADS_API_NAME) && !apiNames.contains(Utils.CONTACT_API_NAME)) {
            if (actionCode.getActionCode().equals(ActionCodeEnum.EDIT.getActionCode())) {
                return false;
            }
        }
        return true;
    }
}
