package com.facishare.crm.follow.service.impl;

import com.facishare.crm.follow.common.FollowRateLimiterService;
import com.facishare.crm.follow.enums.ActionCodeEnum;
import com.facishare.crm.follow.enums.ApiNameEnum;
import com.facishare.crm.follow.model.CustomObjectDataChangeMQMessage;
import com.facishare.crm.follow.model.DealStatusChangeLog;
import com.facishare.crm.follow.model.UpdateFollowDealModel;
import com.facishare.crm.follow.service.CommonService;
import com.facishare.crm.follow.service.CustomerFollowService;
import com.facishare.crm.follow.service.DealCacheService;
import com.facishare.crm.follow.service.FollowSettingCacheService;
import com.facishare.crm.follow.util.CommonUtils;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.recycling.task.executor.biz.CommonBiz;
import com.facishare.crm.recycling.task.executor.util.GrayUtils;
import com.facishare.crm.sfa.audit.log.context.SFALogContext;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.AbstractFieldDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.facishare.crm.openapi.Utils.*;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/7/26 16:47
 * @description:
 */
@Component
@Slf4j
public class CalFollowTimeService {

    @Autowired
    private CommonService commonService;

    @Autowired
    private CustomerFollowService customerFollowService;

    @Autowired
    private CommonBiz commonBiz;

    @Autowired
    private FollowRateLimiterService followRateLimiterService;

    @Autowired
    private IObjectDataService objectDataPgService;

    @Autowired
    private GrayUtils grayUtils;

    @Autowired
    private FollowSettingCacheService followSettingCacheService;

    @Autowired
    private DealCacheService dealCacheService;


    public void doCheckInFinish(CustomObjectDataChangeMQMessage messageObj) {
        String tenantId = messageObj.getTenantId();
        UpdateFollowDealModel updateFollowDealModel = UpdateFollowDealModel.builder().tenantId(tenantId).build();
        UpdateFollowDealModel updateLeadsFollowDealModel = UpdateFollowDealModel.builder().tenantId(tenantId).build();
        List<UpdateFollowDealModel.Content> contents = new ArrayList<>();
        List<UpdateFollowDealModel.Content> leadsContents = new ArrayList<>();
        ActionCodeEnum actionCode = ActionCodeEnum.ADD;
        for (CustomObjectDataChangeMQMessage.Content msg : messageObj.getBody()) {
            String describeApiName = msg.getEntityId();
            if (!CHECKINS_API_NAME.equalsIgnoreCase(describeApiName)) {
                continue;
            }
            if ("u".equalsIgnoreCase(messageObj.getOp())) {
                //完成外勤
                if (validateCheckInFinished(msg)) {
                    actionCode = ActionCodeEnum.FINISH_CHECKIN;
                } else {
                    continue;
                }
            }

            Set<String> settingApiNames = followSettingCacheService.getFollowSetting(tenantId, describeApiName, actionCode);
            if (CollectionUtils.isEmpty(settingApiNames)) {
                log.warn("settingApiNames is empty getFollowSetting again:tenantId:{},actionCode:{}", tenantId, actionCode.getActionCode());
                settingApiNames = followSettingCacheService.getFollowSetting(tenantId, describeApiName, ActionCodeEnum.FINISH_CHECKIN);
                if (CollectionUtils.isEmpty(settingApiNames)) {
                    continue;
                }
            }
            boolean hasAccountSetting = settingApiNames.contains(Utils.ACCOUNT_API_NAME);
            boolean hasLeadsSetting = settingApiNames.contains(Utils.LEADS_API_NAME);

            if (!hasAccountSetting && !hasLeadsSetting) {
                continue;
            }
            IObjectData objectData = commonBiz.findById(msg.getObjectId(), tenantId, describeApiName, false);
            if (objectData == null) {
                log.warn("objectData is empty:{}", msg);
                continue;
            }
            if (hasAccountSetting) {
                builderCheckInContents(msg, contents, ACCOUNT_API_NAME, objectData);
            }
            if (hasLeadsSetting) {
                List<ObjectReferenceFieldDescribe> referenceFieldDescribe = customerFollowService
                        .objectReferenceFieldDescribe(tenantId, msg.getObjectId(), describeApiName);
                List<String> referenceLeadsFields = referenceFieldDescribe.stream().filter(x -> x.getTargetApiName().equals(LEADS_API_NAME)).map(AbstractFieldDescribe::getApiName).collect(Collectors.toList());
                for (String fields : referenceLeadsFields) {
                    if (objectData.get(fields) != null) {
                        String leadsId = objectData.get(fields).toString();
                        this.builderCheckInContents(msg, leadsContents, LEADS_API_NAME, objectData, leadsId);
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(contents)) {
            updateFollowDealModel.setContents(contents);
            followRateLimiterService.getCrmObjectDataRateLimiter().acquire();
            customerFollowService.updateFollowDealTime(updateFollowDealModel);
        }
        if (CollectionUtils.isNotEmpty(leadsContents)) {
            updateLeadsFollowDealModel.setContents(leadsContents);
            followRateLimiterService.getCrmObjectDataRateLimiter().acquire();
            customerFollowService.updateFollowDealTime(updateLeadsFollowDealModel);
        }
    }


    public void consumeMessage(CustomObjectDataChangeMQMessage messageObj) {
        String tenantId = messageObj.getTenantId();
        if (shouldSkipMessage(tenantId)) {
            return;
        }

        String describeApiName = getDescribeApiName(messageObj);
        Boolean hasDeal = false;
        // 成交规则
        if (CommonUtils.hasDealApiName(describeApiName)) {
            hasDeal = dealCacheService.getDealSetting(tenantId, describeApiName);
        }
        // 跟进规则
        Set<String> settingApiNames = followSettingCacheService.getFollowSetting(tenantId, describeApiName, ActionCodeEnum.ADD);
        if (CollectionUtils.isEmpty(settingApiNames) && Boolean.FALSE.equals(hasDeal)) {
            log.info("no follow setting and hasDeal is false {}", messageObj.getBody().get(0).getObjectId());
            return;
        }
        SFALogContext.putVariable("status", true);
        followRateLimiterService.getCrmObjectDataRateLimiter().acquire();
        boolean hasAccountSetting = settingApiNames != null && settingApiNames.contains(Utils.ACCOUNT_API_NAME);
        boolean hasLeadsSetting = settingApiNames != null && settingApiNames.contains(Utils.LEADS_API_NAME);
        processUpdate(tenantId, describeApiName, hasDeal, hasAccountSetting, hasLeadsSetting, messageObj);
    }

    private void processUpdate(String tenantId, String describeApiName, Boolean hasDeal, boolean hasAccountSetting,
                               boolean hasLeadsSetting, CustomObjectDataChangeMQMessage messageObj) {
        UpdateFollowDealModel updateFollowDealModel = UpdateFollowDealModel.builder().tenantId(tenantId).build();
        List<UpdateFollowDealModel.Content> contents = new ArrayList<>();
        List<UpdateFollowDealModel.Content> leadsContents = new ArrayList<>();

        for (CustomObjectDataChangeMQMessage.Content msg : messageObj.getBody()) {
            processMessage(msg, describeApiName, hasDeal, hasAccountSetting, hasLeadsSetting, contents, leadsContents, messageObj.getBornTimestamp());
        }
        updateFollowDealTime(updateFollowDealModel, contents);
        updateFollowDealTime(updateFollowDealModel, leadsContents);
    }

    private void processMessage(CustomObjectDataChangeMQMessage.Content msg, String describeApiName,
                                Boolean hasDeal, boolean hasAccountSetting,
                                boolean hasLeadsSetting,
                                List<UpdateFollowDealModel.Content> contents,
                                List<UpdateFollowDealModel.Content> leadsContents,
                                Long bornTimestamp) {
        String tenantId = msg.getContext().getTenantId();
        String objectId = msg.getObjectId();
        UpdateFollowDealModel.Content content = buildInitialContent(msg, bornTimestamp);
        if (isAccountOrLeadsContent(msg, hasAccountSetting, hasLeadsSetting, content)) {
            if (Utils.ACCOUNT_API_NAME.equals(content.getApiName())) {
                contents.add(content);
            } else if (Utils.LEADS_API_NAME.equals(content.getApiName())) {
                leadsContents.add(content);
            }
            return;
        }

        IObjectData sourceObjectData = null;
        if (needToFindData(describeApiName, hasAccountSetting,hasLeadsSetting) || hasDeal) {
            try {
                sourceObjectData = objectDataPgService.findById(objectId, tenantId, describeApiName);
            } catch (MetadataServiceException e) {
                throw new RuntimeException(e);
            }
        }
        if (sourceObjectData == null) {
            log.info("sourceObjectData is null :{},apiName:{},tenantId:{},objectId:{}", objectId, describeApiName, tenantId, msg.getObjectId());
            return;
        }
        log.info("findRefDescribe {},{},{}", tenantId, objectId, describeApiName);
        List<ObjectReferenceFieldDescribe> referenceFieldDescribes = commonService.objectReferenceFieldDescribe(tenantId, objectId, describeApiName);

        // 关联多个客户对象处理
        for (ObjectReferenceFieldDescribe fieldDescribe : referenceFieldDescribes) {
            if (sourceObjectData.get(fieldDescribe.getApiName()) == null || ("").equals(sourceObjectData.get(fieldDescribe.getApiName()))) {
                continue;
            }
            objectId = sourceObjectData.get(fieldDescribe.getApiName()).toString();
            content = UpdateFollowDealModel.Content.builder()
                    .objectId(objectId)
                    .eventId(msg.getEventId())
                    .apiName(fieldDescribe.getTargetApiName())
                    .sourceApiName(describeApiName)
                    .build();
            toDealStatus(content, tenantId, describeApiName, sourceObjectData, hasDeal,msg);
            if (Utils.ACCOUNT_API_NAME.equals(fieldDescribe.getTargetApiName()) && hasAccountSetting) {
                content.setLastFollower(msg.getContext().getUserId());
                content.setLastFollowTime(bornTimestamp);
                contents.add(content);
            } else if (Utils.LEADS_API_NAME.equals(fieldDescribe.getTargetApiName()) && hasLeadsSetting) {
                content.setLastFollower(msg.getContext().getUserId());
                content.setLastFollowTime(bornTimestamp);
                leadsContents.add(content);
            } else {
                contents.add(content);
            }
        }
    }

    private boolean isAccountOrLeadsContent(CustomObjectDataChangeMQMessage.Content msg, boolean hasAccountSetting,
                                            boolean hasLeadsSetting, UpdateFollowDealModel.Content content) {
        if (Utils.ACCOUNT_API_NAME.equals(msg.getEntityId()) && hasAccountSetting) {
            content.setApiName(Utils.ACCOUNT_API_NAME);
            return true;
        } else if (Utils.LEADS_API_NAME.equals(msg.getEntityId()) && hasLeadsSetting) {
            content.setApiName(Utils.LEADS_API_NAME);
            return true;
        }
        return false;
    }




    private UpdateFollowDealModel.Content buildInitialContent(CustomObjectDataChangeMQMessage.Content msg, Long bornTimestamp) {
        return UpdateFollowDealModel.Content.builder()
                .eventId(msg.getEventId())
                .objectId(msg.getObjectId())
                .lastFollowTime(bornTimestamp)
                .lastFollower(msg.getContext().getUserId())
                .build();
    }

    private void updateFollowDealTime(UpdateFollowDealModel updateFollowDealModel, List<UpdateFollowDealModel.Content> contents) {
        if (!contents.isEmpty()) {
            updateFollowDealModel.setContents(contents);
            customerFollowService.updateFollowDealTime(updateFollowDealModel);
        }
    }


    public void calculateFollowAndDealStatus(CustomObjectDataChangeMQMessage messageObj, Map<String, Boolean> consumedObjectMap) {
        String tenantId = messageObj.getTenantId();
        UpdateFollowDealModel updateFollowDealModel = UpdateFollowDealModel.builder().tenantId(tenantId).build();
        for (CustomObjectDataChangeMQMessage.Content msg : messageObj.getBody()) {
            String describeApiName = msg.getEntityId();
            String consumedKey = tenantId + "_" + describeApiName + "_" + msg.getObjectId();
            if (consumedObjectMap.get(consumedKey) != null && consumedObjectMap.get(consumedKey)) {
                log.info("duplicate consumer key: {}", consumedKey);
                continue;
            }
            // 客户成交状态、成交时间变更，则重算后跳过
            if (isChangeDealStatus(msg, tenantId)) {
                continue;
            }
            List<UpdateFollowDealModel.Content> contents = new ArrayList<>();
            // 处理成交状态
            getDealStatus(msg, tenantId, contents);

            if (CollectionUtils.isNotEmpty(contents)) {
                updateFollowDealModel.setContents(contents);
                // 更新客户的跟进时间和成交状态
                followRateLimiterService.getCrmObjectDataRateLimiter().acquire();
                customerFollowService.updateFollowDealTime(updateFollowDealModel);
                consumedObjectMap.put(consumedKey, Boolean.TRUE);
            }

            // 更新新商机(NewOpportunityObj)的最后跟进时间
            if (NEW_OPPORTUNITY_API_NAME.equalsIgnoreCase(describeApiName)) {
                if (msg.getAfterTriggerData().get("sales_stage") != null &&
                        !msg.getAfterTriggerData().get("sales_stage").equals(msg.getBeforeTriggerData().get("sales_stage"))) {
                    updateFollowTime(msg, tenantId, msg.getObjectId(), describeApiName, System.currentTimeMillis(), "StageInstanceObj");
                    consumedObjectMap.put(consumedKey, Boolean.TRUE);
                }
            }
        }
    }


    private DealStatusChangeLog validateDataChangeDealStatusGray(CustomObjectDataChangeMQMessage.Content msg) {

        ArrayList<String> lifeStatus = Lists.newArrayList("normal", "in_change");
        String describeApiName = msg.getEntityId();
        DealStatusChangeLog dealStatusChangeLog = new DealStatusChangeLog();
        dealStatusChangeLog.setApiName(describeApiName);
        if (msg.getAfterTriggerData() == null) {
            return dealStatusChangeLog;
        }

        // 状态变为赢单
        boolean salesStatusWin = false;
        // 生命状态变为正常（审批通过、恢复）
        boolean lifeStatusNormal = false;
        // 生命状态从作废变为正常 recover
        boolean lifeStatusRecover = false;
        // 作废
        boolean lifeStatusInvalid = false;
        // 订单状态确认、
        boolean orderStatusConfirm = false;
        if (msg.getAfterTriggerData().get("sales_status") != null && msg.getAfterTriggerData().get("sales_status").equals("2")) {
            dealStatusChangeLog.setFields("sales_status");
            salesStatusWin = true;
        } else if (msg.getAfterTriggerData().get("biz_status") != null && msg.getAfterTriggerData().get("biz_status").equals("win")) {
            dealStatusChangeLog.setFields("biz_status");
            salesStatusWin = true;
        } else if (msg.getAfterTriggerData().get("life_status") != null && msg.getAfterTriggerData().get("life_status").equals("normal")) {
            lifeStatusNormal = true;
            dealStatusChangeLog.setFields("life_status");
            // 生命状态从 invalid -> normal
            if (msg.getBeforeTriggerData().get("life_status") != null && msg.getBeforeTriggerData().get("life_status").equals("invalid")) {
                dealStatusChangeLog.setFields("life_status");
                lifeStatusRecover = true;
            }
            //作废  从 normal,in_change 变为 invalid
        } else if ((msg.getBeforeTriggerData().get("life_status") != null && lifeStatus.contains(msg.getBeforeTriggerData().get("life_status")))
                && (msg.getAfterTriggerData().get("life_status") != null && msg.getAfterTriggerData().get("life_status").equals("invalid"))) {
            lifeStatusInvalid = true;
            dealStatusChangeLog.setFields("life_status");
        } else if (msg.getAfterTriggerData().get("order_status") != null && msg.getAfterTriggerData().get("order_status").equals("7")) {
            orderStatusConfirm = true;
            dealStatusChangeLog.setFields("order_status");
        }
        if ((NEW_OPPORTUNITY_API_NAME.equals(describeApiName) || OPPORTUNITY_API_NAME.equals(describeApiName))) {
            dealStatusChangeLog.setValidateStatus(salesStatusWin || lifeStatusInvalid || lifeStatusRecover || msg.getAfterTriggerData().get("account_id") != null || msg.getBeforeTriggerData().get("account_id") != null);
        } else if ((CONTRACT_API_NAME.equals(describeApiName) || SALE_CONTRACT_API_NAME.equals(describeApiName))
                && (lifeStatusNormal || lifeStatusInvalid)) {
            dealStatusChangeLog.setValidateStatus(true);
        } else if (SALES_ORDER_API_NAME.equals(describeApiName) &&
                (orderStatusConfirm || lifeStatusNormal || lifeStatusInvalid)) {
            dealStatusChangeLog.setValidateStatus(true);
        }
        return dealStatusChangeLog;
    }


    public void getDealStatus(CustomObjectDataChangeMQMessage.Content msg, String tenantId, List<UpdateFollowDealModel.Content> contents) {
        String describeApiName = msg.getEntityId();
        if (!SALES_ORDER_API_NAME.equalsIgnoreCase(describeApiName) &&
                !CONTRACT_API_NAME.equalsIgnoreCase(describeApiName) &&
                !NEW_OPPORTUNITY_API_NAME.equalsIgnoreCase(describeApiName) &&
                !OPPORTUNITY_API_NAME.equalsIgnoreCase(describeApiName) &&
                !SALE_CONTRACT_API_NAME.equalsIgnoreCase(describeApiName)) {
            return;
        }

        DealStatusChangeLog dealStatusChangeLog = validateDataChangeDealStatusGray(msg);

        if (dealStatusChangeLog.isValidateStatus()) {
            followRateLimiterService.getCrmObjectDataRateLimiter().acquire();
            Boolean hasDeal = dealCacheService.getDealSetting(tenantId, describeApiName);
            if (!hasDeal) {
                return;
            }
            IObjectData objectData = null;
            try {
                objectData = objectDataPgService.findById(msg.getObjectId(), tenantId, CommonUtils.buildContextAllInvalid(tenantId), describeApiName, true);
            } catch (MetadataServiceException e) {
                log.error("CustomObjectDataChangeListener findById error tenantId:{} ", tenantId, e);
                throw new RuntimeException(e);
            }
            if (NEW_OPPORTUNITY_API_NAME.equalsIgnoreCase(describeApiName) || OPPORTUNITY_API_NAME.equalsIgnoreCase(describeApiName)) {
                // 新商机或者商机
                if (msg.getBeforeTriggerData().get("account_id") != null) {
                    UpdateFollowDealModel.Content content = getDealStatusContent(msg, tenantId, describeApiName, msg.getBeforeTriggerData().get("account_id").toString());
                    content.setFields("account_id");
                    content.setSourceApiName(describeApiName);
                    contents.add(content);
                }
            }
            String objectId;
            if (objectData == null || StringUtils.isBlank(objectData.get("account_id", String.class))) {
                log.warn("objectData is empty:{}", msg);
                return;
            } else {
                objectId = objectData.get("account_id").toString();
            }

            UpdateFollowDealModel.Content content = getDealStatusContent(msg, tenantId, describeApiName, objectId);
            content.setSourceApiName(describeApiName);
            content.setSourceObjectId(msg.getObjectId());
            content.setFields(dealStatusChangeLog.getFields());
            contents.add(content);
        }
    }


    private boolean isChangeDealStatus(CustomObjectDataChangeMQMessage.Content content, String tenantId) {
        if ((Utils.ACCOUNT_API_NAME.equals(content.getEntityId()) || LEADS_API_NAME.equals(content.getEntityId())) &&
                content.getAfterTriggerData() != null) {
            boolean isRecalculate = false;
            if (content.getAfterTriggerData().get("deal_status") != null || content.getAfterTriggerData().get("last_deal_closed_time") != null) {
                isRecalculate = true;
            }
            if ((content.getBeforeTriggerData().get("life_status") != null && "under_review".equals(content.getBeforeTriggerData().get("life_status")))
                    && (content.getAfterTriggerData().get("life_status") != null && "normal".equals(content.getAfterTriggerData().get("life_status")))) {
                isRecalculate = true;
            }
            if (isRecalculate) {
                customerFollowService.sendRecalculateTask(tenantId, content.getEntityId(), content.getObjectId());
            }
        }
        return false;
    }

    private boolean validateCheckInFinished(CustomObjectDataChangeMQMessage.Content msg) {
        if (msg.getBeforeTriggerData() == null || msg.getAfterTriggerData() == null) {
            return false;
        }
        return "false".equalsIgnoreCase(String.valueOf(msg.getBeforeTriggerData().get("checkin_status")))
                && "true".equalsIgnoreCase(String.valueOf(msg.getAfterTriggerData().get("checkin_status")));
    }

    private void builderCheckInContents(CustomObjectDataChangeMQMessage.Content msg, List<UpdateFollowDealModel.Content> contents, String describeApiName, IObjectData objectData) {
        String objectId = msg.getObjectId();
        if (describeApiName.equals(ACCOUNT_API_NAME) && StringUtils.isNotBlank(objectData.get("customer_id", String.class))) {
            objectId = objectData.get("customer_id").toString();
        }
        builderCheckInContents(msg, contents, describeApiName, objectData, objectId);
    }

    private void builderCheckInContents(CustomObjectDataChangeMQMessage.Content msg, List<UpdateFollowDealModel.Content> contents, String describeApiName, IObjectData objectData, String objectId) {
        Object finishTime = objectData.get("finish_time");

        UpdateFollowDealModel.Content content = UpdateFollowDealModel.Content.builder()
                .apiName(describeApiName)
                .eventId(msg.getEventId())
                .objectId(objectId)
                .lastFollower(msg.getContext().getUserId())
                .lastFollowTime(finishTime == null ? null : Long.valueOf(finishTime.toString()))
                .build();
        contents.add(content);
    }

    private void updateFollowTime(CustomObjectDataChangeMQMessage.Content msg, String tenantId, String objectId, String apiName, Long lastFolloweTime, String sourceApiName) {
        UpdateFollowDealModel updateFollowDealModel = UpdateFollowDealModel.builder().tenantId(tenantId).build();
        UpdateFollowDealModel.Content content = UpdateFollowDealModel.Content.builder()
                .apiName(apiName)
                .eventId(msg.getEventId())
                .objectId(objectId)
                .lastFollowTime(lastFolloweTime)
                .sourceApiName(sourceApiName)
                .build();
        //todo 最后跟进人
        updateFollowDealModel.setContents(Lists.newArrayList(content));
        followRateLimiterService.getCrmObjectDataRateLimiter().acquire();
        customerFollowService.updateFollowDealTime(updateFollowDealModel);
    }


    private UpdateFollowDealModel.Content getDealStatusContent(CustomObjectDataChangeMQMessage.Content msg, String tenantId, String describeApiName, String objectId) {
        UpdateFollowDealModel.Content content = UpdateFollowDealModel.Content.builder()
                .apiName(ApiNameEnum.ACCOUNT_OBJ.getApiName())
                .eventId(msg.getEventId())
                .objectId(objectId)
                .lastFollower(msg.getContext().getUserId())
                .build();
        commonService.getDealStatusAndTime(content, tenantId, describeApiName);
        return content;
    }

    private boolean needToFindData(String describeApiName, Set<String> settingApiNames) {
        if (CollectionUtils.isEmpty(settingApiNames)) {
            return false;
        }
        return !ACCOUNT_API_NAME.equals(describeApiName) && !LEADS_API_NAME.equals(describeApiName);
    }

    private boolean needToFindData(String describeApiName,boolean hasAccountSetting, boolean hasLeadsSetting) {
        if (!hasAccountSetting  && !hasLeadsSetting) {
            return false;
        }
        return !ACCOUNT_API_NAME.equals(describeApiName) && !LEADS_API_NAME.equals(describeApiName);
    }


    private void toDealStatus(UpdateFollowDealModel.Content content, String tenantId, String describeApiName, IObjectData objectData, Boolean hasDeal, CustomObjectDataChangeMQMessage.Content msg) {
        if (!hasDeal) {
            return;
        }
        boolean calcualteDeaStatus = false;
        if ((CONTRACT_API_NAME.equals(describeApiName) || SALES_ORDER_API_NAME.equals(describeApiName) || SALE_CONTRACT_API_NAME.equals(describeApiName))
                && "normal".equals(objectData.get("life_status", String.class))) {
            calcualteDeaStatus = true;
        } else if (OPPORTUNITY_API_NAME.equals(describeApiName)
                && "win".equals(objectData.get("biz_status", String.class))) {
            calcualteDeaStatus = true;
        } else if (NEW_OPPORTUNITY_API_NAME.equals(describeApiName)
                && "2".equals(objectData.get("sales_status", String.class))) {
            calcualteDeaStatus = true;
        }
        if (calcualteDeaStatus) {
            commonService.getDealStatusAndTime(content, tenantId, describeApiName);
        }
    }


    private boolean shouldSkipMessage(String tenantId) {
        if (grayUtils.skipObjectDataChangeTenantId(tenantId)) {
            log.info("skipObjectDataChangeTenantId:{}", tenantId);
            return true;
        }
        return false;
    }

    private String getDescribeApiName(CustomObjectDataChangeMQMessage messageObj) {
        return CollectionUtils.isNotEmpty(messageObj.getBody()) ? messageObj.getBody().get(0).getEntityId() : "";
    }

}
