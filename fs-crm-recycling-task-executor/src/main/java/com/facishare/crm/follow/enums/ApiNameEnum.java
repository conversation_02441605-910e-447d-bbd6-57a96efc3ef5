package com.facishare.crm.follow.enums;

public enum ApiNameEnum {

    /**
     * 客户
     */
    ACCOUNT_OBJ("AccountObj"),

    /**
     * 销售线索
     */
    LEADS_OBJ("LeadsObj"),

    /**
     * 公海
     */
    HIGH_SEAS_OBJ("HighSeasObj"),

    /**
     * 联系人
     */
    CONTACT_OBJ("ContactObj"),

    /**
     * 线索池
     */
    LEADS_POOL_OBJ("LeadsPoolObj"),

    /**
     * 合同
     */
    CONTRACT_OBJ("ContractObj"),

    /**
     * 销售订单
     */
    SALES_ORDER_OBJ("SalesOrderObj"),

    /**
     * 商机
     */
    OPPORTUNITY_OBJ("OpportunityObj"),

    /**
     * 新商机
     */
    NEW_OPPORTUNITY_OBJ("NewOpportunityObj"),

    /**
     * 新商机
     */
    SALE_CONTRACT_API_NAME("SaleContractObj");

    private String apiName;


    ApiNameEnum(String apiName) {
        this.apiName = apiName;
    }

    public String getApiName() {
        return apiName;
    }

    public void setApiName(String apiName) {
        this.apiName = apiName;
    }


    public static ApiNameEnum apiNameOf(String apiName) {
        for (ApiNameEnum value : values()) {
            if (value.getApiName().equals(apiName)) {
                return value;
            }
        }
        return null;
    }

    // 是否属于成交配置的对象
    public static boolean isRecalculateDeal(String apiName) {
        if (OPPORTUNITY_OBJ.getApiName().equals(apiName)
                || NEW_OPPORTUNITY_OBJ.getApiName().equals(apiName)) {
            return true;
        }
        return false;
    }


}
