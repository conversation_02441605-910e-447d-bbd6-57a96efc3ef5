package com.facishare.crm.follow.mq;

import com.facishare.crm.follow.service.ObjectDataChangeConsume;
import com.facishare.crm.recycling.task.executor.producer.FollowMessageForwardProducer;
import com.facishare.crm.recycling.task.executor.util.GrayUtils;
import com.facishare.crm.sfa.audit.log.context.SFALogContext;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-11-01 2:34 PM
 */
@Slf4j
@Component
public class ObjectDataChangeListener implements ApplicationListener<ContextRefreshedEvent> {

    private AutoConfMQPushConsumer consumer;

    @Autowired
    private ObjectDataChangeConsume objectDataChangeConsume;

    @Autowired
    private FollowMessageForwardProducer followMessageForwardProducer;

    @PostConstruct
    public void init() {
        consumer = new AutoConfMQPushConsumer("fs-crm-follow-customObjectDataChange-consumer", "object-data-tag", (MessageListenerConcurrently) (msgs, context) -> {
            if (!msgs.isEmpty()) {
                try {
                    SFALogContext.putVariable("bizName", "CRM_FOLLOW");
                    if (forward2Slow(msgs)) {
                        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                    }
                    objectDataChangeConsume.consumeMessage(msgs);
                } catch (Exception e) {
                    log.error("msg:{}", msgs, e);
                    throw new RuntimeException(e);
                } finally {
                    SFALogContext.clearContext();
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        });
    }

    @PreDestroy
    public void close() {
        consumer.close();
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (consumer != null && event.getApplicationContext().getParent() == null) {
            consumer.start();
        }
    }

    public boolean forward2Slow(List<MessageExt> msgs) {
        boolean forwarded = false;
        if (msgs == null || msgs.isEmpty()) {
            return false;
        }
        for (MessageExt msg : msgs) {
            String tenantId = msg.getProperties().get("x-fs-ei");
            if (tenantId != null && GrayUtils.isForward2Slow(tenantId)) {
                String body = new String(msg.getBody(), StandardCharsets.UTF_8);
                try {
                    followMessageForwardProducer.sendObjectDataMessage(body);
                    return true;
                } catch (Exception e) {
                    log.error("send object_data slow queue TenantId: {}, MsgId: {}. Exception: ",
                              tenantId, msg.getMsgId(), e);
                }
            }
        }
        return forwarded;
    }
}
