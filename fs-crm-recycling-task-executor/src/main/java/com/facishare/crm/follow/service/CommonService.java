package com.facishare.crm.follow.service;

import com.facishare.crm.follow.enums.ActionCodeEnum;
import com.facishare.crm.follow.model.CustomObjectDataChangeMQMessage;
import com.facishare.crm.follow.model.UpdateFollowDealModel;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface CommonService {

    /**
     * 成交状态回退，根据成交时间查询成交状态
     * @param content
     * @param tenantId
     * @param describeApiName
     */
    void getDealStatusAndTime(UpdateFollowDealModel.Content content, String tenantId, String describeApiName);

    @Deprecated
    Map<String,String> getObjectFollowDealSettingType(String tenantId, List<String> objectApiName, ActionCodeEnum actionCode);

    /**
     * 获取跟进配置
     * {
     *     "AccountObj":
     *          {
     *               "ContactObj",
     *               "AccountObj"
     *          }
     *      LeadsObj:
     *          {
     *               "ContactObj",
     *               "AccountObj"
     *          }
     * }
     * @param tenantId
     * @param objectApiName
     * @param actionCode
     * @return
     */
    Map<String,List<String>> getFollowSetting(String tenantId, List<String> objectApiName, ActionCodeEnum actionCode);


    /**
     * 获取跟进配置
     * {
     *     "ContactObj":
     *          {
     *               "LeadsObj",
     *               "AccountObj"
     *          }
     *      xxxxObj:
     *          {
     *               "LeadsObj",
     *               "AccountObj"
     *          }
     * }
     * @param tenantId
     * @return
     */
    Map<String, Map<String, Set<String>>> getFollowSetting(String tenantId);


    Map<String, Boolean> getFollowDealSetting(String tenantId);


    /**
     *
     * @param tenantId
     * @param objectId
     * @param describeApiName
     * @return
     */
    List<ObjectReferenceFieldDescribe> objectReferenceFieldDescribe(String tenantId, String objectId,
                                                                    String describeApiName);


    UpdateFollowDealModel.Content buildContent(String objectId, String apiName, Long dealTime, Long lastFollowTime, String eventId);

    UpdateFollowDealModel.Content buildContent(String objectId,String apiName,Long dealTime,Long lastFollowTime,String eventId, String lastFollower);
}
