package com.facishare.crm.follow.dispatch;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.google.common.base.Strings;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * @Description
 * <AUTHOR>
 * @Date 2021/8/23 18:15
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FollowDataGroup {
    private String tenantId;
    private String objectApiName;
    private Set<String> fieldApiNames;
    private Set<String> dataIds;
    private String fieldType;
    private String eventId;
    private String action;
    private String queueType;
    private String queueIndex;
    private Set<String> messageIds;
    private Long bornTimestamp;
    private long consumeTimestamp;
    private String originalObjectApiName;
    private String originalMessageId;
    private Long originalBornTimestamp;


    //
    //public LogInfo toLogInfo() {
    //    long now = System.currentTimeMillis();
    //    return LogInfo.builder()
    //            .tenantId(tenantId)
    //            .objectApiName(objectApiName)
    //            .dataIds(Lists.newArrayList(dataIds))
    //            .fieldApiNames(Lists.newArrayList(fieldApiNames))
    //            .fieldType(fieldType)
    //            .action(action)
    //            .queueType(queueType)
    //            .queueIndex(queueIndex)
    //            .originalDescribeApiName(originalObjectApiName)
    //            .extraInfo(AuditLogUtil.ExtraInfo.builder()
    //                    .messageId(Objects.toString(messageIds, ""))
    //                    .bornTimestamp(bornTimestamp)
    //                    .consumeTimestamp(consumeTimestamp)
    //                    .originalMessageId(originalMessageId)
    //                    .originalBornTimestamp(originalBornTimestamp)
    //                    .totalCost(AuditLogUtil.minus(now, originalBornTimestamp))
    //                    .calculateCost(AuditLogUtil.minus(now, consumeTimestamp))
    //                    .pretreatmentCost(AuditLogUtil.minus(bornTimestamp, originalBornTimestamp))
    //                    .build())
    //            .build();
    //}

    public IActionContext buildContext() {
        ActionContextExt actionContextExt = ActionContextExt.of(User.systemUser(getTenantId()))
                .setBatch(true)
                .disableDeepQuote()
                .skipRelevantTeam()
                .doNotDeepCopyDescribe()
                .doSkipConfig();
        if (!Strings.isNullOrEmpty(getEventId())) {
            actionContextExt.setEventId(getEventId());
        }
        return actionContextExt.getContext();
    }
}
