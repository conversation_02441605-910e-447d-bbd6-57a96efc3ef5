package com.facishare.crm.follow.enums;

import com.facishare.paas.metadata.util.GetI18nKeyUtil;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-03-28 11:53
 */

public enum DealStatusEnum {

    /**
     * 未成交
     */
    NON_DEAL("1", "未成交"),
    /**
     * 已成交
     */
    DEALED("2", "已成交"),
    /**
     * 多次成交
     */
    MULTI_DEAL("3", "多次成交");

    private String value;
    private String desc;

    DealStatusEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }


    public static String getDesc(String status){
        for (DealStatusEnum value : values()) {
            if (value.value.equals(status)) {
                return value.desc;
            }
        }
        return null;
    }

    public static String getDealStatusI18nKey(String status){
        for (DealStatusEnum value : values()) {
            if (value.value.equals(status)) {
                return GetI18nKeyUtil.getOptionNameKey("AccountObj", "deal_status", status);
            }
        }
        return null;
    }


    public static DealStatusEnum actionCodeOf(String status) {
        for (DealStatusEnum value : values()) {
            if (value.value.equals(status)) {
                return value;
            }
        }
        return null;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }}
