package com.facishare.crm.follow.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.follow.common.FollowRateLimiterService;
import com.facishare.crm.follow.enums.ActionCodeEnum;
import com.facishare.crm.follow.enums.ApiNameEnum;
import com.facishare.crm.follow.model.CustomObjectDataChangeMQMessage;
import com.facishare.crm.follow.model.DealStatusChangeLog;
import com.facishare.crm.follow.model.UpdateFollowDealModel;
import com.facishare.crm.follow.service.impl.CalFollowTimeService;
import com.facishare.crm.follow.util.CommonUtils;
import com.facishare.crm.follow.util.FollowGrayUtils;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.recycling.task.executor.biz.CommonBiz;
import com.facishare.crm.recycling.task.executor.service.RecyclingRuleCacheService;
import com.facishare.crm.recycling.task.executor.util.GrayUtils;
import com.facishare.crm.sfa.audit.log.SFAAuditLog;
import com.facishare.crm.sfa.audit.log.context.SFALogContext;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.AbstractFieldDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.fxiaoke.rocketmq.util.MessageHelper;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.facishare.crm.openapi.Utils.*;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/1/5 17:17
 */


@Component
@Slf4j
public class ObjectDataChangeConsume {

    @Autowired
    private GrayUtils grayUtils;


    @Autowired
    private FollowGrayUtils followGrayUtils;

    @Autowired
    private CustomerFollowService customerFollowService;

    @Autowired
    private IObjectDataService objectDataPgService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private FollowRateLimiterService followRateLimiterService;

    @Autowired
    private CommonBiz commonBiz;

    @Autowired
    private FollowSettingService followSettingService;

    @Autowired
    private RecyclingRuleCacheService recyclingRuleCacheService;

    @Autowired
    private CalFollowTimeService calFollowTimeService;

    @SFAAuditLog(bizName = "#bizName",  status = "#status", ea = "#ea", ei = "#ei",objectApiName = "#objectApiName",
            messageId = "#msg.msgId", extra = "#msg.reconsumeTimes",  extra1 = "#extra1", extra2 = "#extra2", message = "#message", extra3 = "#extra3",
            condition = "#status == true")
    public void consumeMessage(List<MessageExt> messageExtList) {
        if (CollectionUtils.empty(messageExtList)) {
            return;
        }
        messageExtList = Lists.reverse(messageExtList);
        Map<String, Boolean> consumedObjectMap = new HashMap<>();
        for (MessageExt message : messageExtList) {

            MessageHelper.fillContextFromMessage(TraceContext.get(), message);
            // 没有跳过的消息执行下面的逻辑
            if (grayUtils.isSkipFollowObjectData(message.getProperties().get("x-fs-ei"))) {
                continue;
            }
            if (message.getProperties().get("describe_api_name") != null && grayUtils.isSkipFollowObjectDataByApiName(message.getProperties().get("describe_api_name"))) {
                continue;
            }
            String body = new String(message.getBody(), StandardCharsets.UTF_8);
            CustomObjectDataChangeMQMessage messageObj = JSON.parseObject(body, CustomObjectDataChangeMQMessage.class);
            if (CollectionUtils.empty(messageObj.getBody())) {
                continue;
            }
            messageObj.getBody().removeIf(x -> grayUtils.isSkipObjectDataConsume(messageObj.getTenantId(), x.getEntityId())
                    || grayUtils.isSkipFollowObjectDataByApiName(x.getEntityId()));
            if (CollectionUtils.empty(messageObj.getBody())) {
                continue;
            }
            if (isSkipDeletedAndInvalid(messageObj)) {
                continue;
            }

            if (FollowGrayUtils.isEnableAuditLog){
                SFALogContext.putVariable("status", true);
            }else {
                SFALogContext.putVariable("status", false);
            }
            SFALogContext.putVariable("msg", message);
            SFALogContext.putVariable("ei", messageObj.getTenantId());
            SFALogContext.putVariable("objectApiName", message.getProperties().get("describe_api_name"));
            String ids = getIdsStr(messageObj);
            SFALogContext.putVariable("extra1", ids);
            messageObj.setBornTimestamp(message.getBornTimestamp());
            if (skipNoRecyclingRuleTenant(messageObj)) {
                log.info("skipNoRecyclingRuleTenant:{}", messageObj.getTenantId());
                continue;
            }
            newConsumeMessage(messageObj, consumedObjectMap);
        }
    }

    @NotNull
    private static String getIdsStr(CustomObjectDataChangeMQMessage messageObj) {
        StringBuilder sb = new StringBuilder();
        if (messageObj.getBody() != null) {
            for (CustomObjectDataChangeMQMessage.Content content : messageObj.getBody()) {
                if (content.getObjectId() != null) {
                    if (sb.length() > 0) sb.append(",");
                    sb.append(content.getObjectId());
                }
            }
        }
        return sb.toString();
    }

    private void newConsumeMessage(CustomObjectDataChangeMQMessage messageObj, Map<String, Boolean> consumedObjectMap) {
        calFollowTimeService.doCheckInFinish(messageObj);
        if ("u".equalsIgnoreCase(messageObj.getOp())) {
            calFollowTimeService.calculateFollowAndDealStatus(messageObj, consumedObjectMap);
        } else {
            calFollowTimeService.consumeMessage(messageObj);
        }
    }

    /**
     * 跳过 客户、线索的 删除和作废操作
     * 不跳过合同、商机、商机2.0 和订单的 操作
     *
     */
    private boolean isSkipDeletedAndInvalid(CustomObjectDataChangeMQMessage messageObj) {
        String describeApiName = messageObj.getBody().get(0).getEntityId();
        if (!SALES_ORDER_API_NAME.equalsIgnoreCase(describeApiName) &&
                !CONTRACT_API_NAME.equalsIgnoreCase(describeApiName) &&
                !NEW_OPPORTUNITY_API_NAME.equalsIgnoreCase(describeApiName) &&
                !OPPORTUNITY_API_NAME.equalsIgnoreCase(describeApiName) &&
                !SALE_CONTRACT_API_NAME.equalsIgnoreCase(describeApiName)) {
            return false;
        }
        return "d".equalsIgnoreCase(messageObj.getOp()) || "invalid".equalsIgnoreCase(messageObj.getOp());
    }




    /**
     * 移除没有回收规则的租户，商机、商机2.0、合同、订单 不作移除
     * @param messageObj
     */
    private boolean skipNoRecyclingRuleTenant(CustomObjectDataChangeMQMessage messageObj) {
        if(!grayUtils.isSkipFollowObjectDataSkipNoRule(messageObj.getTenantId())){
            return false;
        }
        messageObj.getBody().removeIf(x -> recyclingRuleCacheService.isSkipWithoutRuleByTenantId(messageObj.getTenantId())
                && !CommonUtils.hasDealApiName(x.getEntityId()));
        return CollectionUtils.empty(messageObj.getBody());
    }


    private void consumeMessage(CustomObjectDataChangeMQMessage messageObj) {
        String tenantId = messageObj.getTenantId();

        if (grayUtils.skipObjectDataChangeTenantId(messageObj.getTenantId())) {
            log.info("skipObjectDataChangeTenantId:{}", messageObj.getTenantId());
            return;
        }
        UpdateFollowDealModel updateFollowDealModel = UpdateFollowDealModel.builder().tenantId(tenantId).build();
        List<UpdateFollowDealModel.Content> contents = new ArrayList<>();
        List<UpdateFollowDealModel.Content> leadsContents = new ArrayList<>();
        String describeApiName = "";
        if (CollectionUtils.notEmpty(messageObj.getBody())) {
            describeApiName = messageObj.getBody().get(0).getEntityId();
        }
        // 处理线索、客户的导入，新建，预制跟进时间
        if (Boolean.TRUE.equals(dealSystemObj(messageObj, describeApiName))) {
            SFALogContext.putVariable("status", true);
            return;
        }
        followRateLimiterService.getCrmObjectDataRateLimiter().acquire();
        Boolean hasDeal = false;
        // 处理合同、订单的成交状态，商机，商机2.0 新建不处理
        if (CommonUtils.hasDealApiName(describeApiName)) {
            // 是否配置成交
            hasDeal = customerFollowService.hasSettingDeal(tenantId, describeApiName, ApiNameEnum.ACCOUNT_OBJ.getApiName());
        }
        Map<String, List<String>> map = newHasFollow(tenantId, describeApiName);
        if (CollectionUtils.empty(map) && Boolean.FALSE.equals(hasDeal)) {
            log.info("no follow setting and hasDeal is false {}", messageObj.getBody().get(0).getObjectId());
            return;
        }
        Long messageObjBornTimestamp = messageObj.getBornTimestamp();
        for (CustomObjectDataChangeMQMessage.Content msg : messageObj.getBody()) {
            String objectId = msg.getObjectId();
            UpdateFollowDealModel.Content content = UpdateFollowDealModel.Content.builder()
                    .eventId(msg.getEventId())
                    .objectId(msg.getObjectId())
                    .lastFollowTime(messageObjBornTimestamp)
                    .lastFollower(msg.getContext().getUserId())
                    .build();
            if (Utils.ACCOUNT_API_NAME.equals(msg.getEntityId()) && map != null && map.get(Utils.ACCOUNT_API_NAME) != null) {
                content.setApiName(Utils.ACCOUNT_API_NAME);
                contents.add(content);
            } else if (Utils.LEADS_API_NAME.equals(msg.getEntityId()) && map != null && map.get(Utils.LEADS_API_NAME) != null) {
                content.setApiName(Utils.LEADS_API_NAME);
                leadsContents.add(content);
            }

            IObjectData sourceObjectData = null;
            try {
                if (needToFindData(describeApiName, map) || hasDeal) {
                    sourceObjectData = objectDataPgService.findById(objectId, tenantId, describeApiName);
                }
                if (sourceObjectData == null) {
                    log.info("sourceObjectData is null :{},apiName:{},tenantId:{},objectId:{}", objectId, describeApiName, tenantId, msg.getObjectId());
                    continue;
                }
                log.info("findRefDescribe {},{},{}", tenantId, objectId, describeApiName);
                List<ObjectReferenceFieldDescribe> referenceFieldDescribes = commonService.objectReferenceFieldDescribe(tenantId, objectId, describeApiName);

                // 关联多个客户对象处理
                for (ObjectReferenceFieldDescribe fieldDescribe : referenceFieldDescribes) {
                    if (sourceObjectData.get(fieldDescribe.getApiName()) == null || ("").equals(sourceObjectData.get(fieldDescribe.getApiName()))) {
                        continue;
                    }
                    objectId = sourceObjectData.get(fieldDescribe.getApiName()).toString();
                    content = UpdateFollowDealModel.Content.builder()
                            .objectId(objectId)
                            .eventId(msg.getEventId())
                            .apiName(fieldDescribe.getTargetApiName())
                            .sourceApiName(describeApiName)
                            .build();
                    toDealStatus(content, tenantId, describeApiName, sourceObjectData, hasDeal,msg);
                    if (Utils.ACCOUNT_API_NAME.equals(fieldDescribe.getTargetApiName()) && map != null && map.get(Utils.ACCOUNT_API_NAME) != null) {
                        content.setLastFollower(msg.getContext().getUserId());
                        content.setLastFollowTime(messageObjBornTimestamp);
                        contents.add(content);
                    } else if (Utils.LEADS_API_NAME.equals(fieldDescribe.getTargetApiName()) && map != null && map.get(Utils.LEADS_API_NAME) != null) {
                        content.setLastFollower(msg.getContext().getUserId());
                        content.setLastFollowTime(messageObjBornTimestamp);
                        leadsContents.add(content);
                    } else {
                        contents.add(content);
                    }
                }
            } catch (MetadataServiceException e) {
                log.error("CrmActionListener error", e);
            }
        }
        updateFollowDealModel.setContents(contents);
        // 更新跟进时间和成交状态
        customerFollowService.updateFollowDealTime(updateFollowDealModel);

        updateFollowDealModel.setContents(leadsContents);
        customerFollowService.updateFollowDealTime(updateFollowDealModel);
    }


    /**
     * 处理外勤跟进时间
     * 1. 新建外勤，
     * 2. 完成外勤
     *
     * @param messageObj
     */
    private void doCheckinFinishNew(CustomObjectDataChangeMQMessage messageObj) {
        String tenantId = messageObj.getTenantId();
        UpdateFollowDealModel updateFollowDealModel = UpdateFollowDealModel.builder().tenantId(tenantId).build();
        UpdateFollowDealModel updateLeadsFollowDealModel = UpdateFollowDealModel.builder().tenantId(tenantId).build();
        List<UpdateFollowDealModel.Content> contents = new ArrayList<>();
        List<UpdateFollowDealModel.Content> leadsContents = new ArrayList<>();

        ActionCodeEnum actionCode = ActionCodeEnum.ADD;
        for (CustomObjectDataChangeMQMessage.Content msg : messageObj.getBody()) {
            String describeApiName = msg.getEntityId();
            if (!CHECKINS_API_NAME.equalsIgnoreCase(describeApiName)) {
                continue;
            }
            if ("u".equalsIgnoreCase(messageObj.getOp())) {
                //完成外勤
                if (validateCheckinFinished(msg)) {
                    actionCode = ActionCodeEnum.FINISH_CHECKIN;
                } else {
                    continue;
                }
            }

            Map<String, List<String>> followSetting = commonService.getFollowSetting(tenantId, Lists.newArrayList(describeApiName), actionCode);
            if (MapUtils.isEmpty(followSetting)) {
                followSetting = commonService.getFollowSetting(tenantId, Lists.newArrayList(describeApiName), actionCode = ActionCodeEnum.FINISH_CHECKIN);
                if (MapUtils.isEmpty(followSetting)) {
                    continue;
                }
            }

            List<String> accountFollowSetting = followSetting.get(ACCOUNT_API_NAME);
            List<String> leadsFollowSetting = followSetting.get(LEADS_API_NAME);
            if (CollectionUtils.empty(accountFollowSetting) && CollectionUtils.empty(leadsFollowSetting)) {
                continue;
            }
            IObjectData objectData = commonBiz.findById(msg.getObjectId(), tenantId, describeApiName, false);
            if (objectData == null) {
                log.warn("objectData is empty:{}", msg);
                continue;
            }
            boolean hasFollow = false;
            if (CollectionUtils.empty(accountFollowSetting)) {
                String followType = customerFollowService.getObjectFollowDealSettingType(tenantId, describeApiName, ActionCodeEnum.FINISH_CHECKIN, ACCOUNT_API_NAME);
                hasFollow = "1".equals(followType);
            }
            if (CollectionUtils.notEmpty(accountFollowSetting) && accountFollowSetting.contains(describeApiName) || hasFollow) {
                builderCheckInContents(msg, contents, ACCOUNT_API_NAME, objectData);
            }
            if (CollectionUtils.notEmpty(leadsFollowSetting) && leadsFollowSetting.contains(describeApiName)) {
                List<ObjectReferenceFieldDescribe> referenceFieldDescribe = customerFollowService
                        .objectReferenceFieldDescribe(tenantId, msg.getObjectId(), describeApiName);
                List<String> referenceLeadsFields = referenceFieldDescribe.stream().filter(x -> x.getTargetApiName().equals(LEADS_API_NAME)).map(AbstractFieldDescribe::getApiName).collect(Collectors.toList());
                for (String fields : referenceLeadsFields) {
                    if (objectData.get(fields) != null) {
                        String leadsId = objectData.get(fields).toString();
                        this.builderCheckInContents(msg, leadsContents, LEADS_API_NAME, objectData, leadsId);
                    }
                }
            }
        }
        if (CollectionUtils.notEmpty(contents)) {
            updateFollowDealModel.setContents(contents);
            followRateLimiterService.getCrmObjectDataRateLimiter().acquire();
            customerFollowService.updateFollowDealTime(updateFollowDealModel);
        }

        if (CollectionUtils.notEmpty(leadsContents)) {
            updateLeadsFollowDealModel.setContents(leadsContents);
            followRateLimiterService.getCrmObjectDataRateLimiter().acquire();
            customerFollowService.updateFollowDealTime(updateLeadsFollowDealModel);
        }
    }

    private void calculateFollowAndDealStatus(CustomObjectDataChangeMQMessage messageObj, Map<String, Boolean> consumedObjectMap) {
        String tenantId = messageObj.getTenantId();
        UpdateFollowDealModel updateFollowDealModel = UpdateFollowDealModel.builder().tenantId(tenantId).build();
        for (CustomObjectDataChangeMQMessage.Content msg : messageObj.getBody()) {
            String describeApiName = msg.getEntityId();
            String consumedKey = tenantId + "_" + describeApiName + "_" + msg.getObjectId();
            if (consumedObjectMap.get(consumedKey) != null && consumedObjectMap.get(consumedKey)) {
                log.info("duplicate consumer key: {}", consumedKey);
                continue;
            }
            // 客户成交状态、成交时间变更，则重算后跳过
            if (isChangeDealStatus(msg, tenantId)) {
                continue;
            }
            List<UpdateFollowDealModel.Content> contents = new ArrayList<>();
            // 处理成交状态
            getDealStatus(msg, tenantId, contents);

            if (CollectionUtils.notEmpty(contents)) {
                updateFollowDealModel.setContents(contents);
                // 更新客户的跟进时间和成交状态
                followRateLimiterService.getCrmObjectDataRateLimiter().acquire();
                customerFollowService.updateFollowDealTime(updateFollowDealModel);
                consumedObjectMap.put(consumedKey, Boolean.TRUE);
            }

            // 更新新商机(NewOpportunityObj)的最后跟进时间
            if (NEW_OPPORTUNITY_API_NAME.equalsIgnoreCase(describeApiName)) {
                if (msg.getAfterTriggerData().get("sales_stage") != null &&
                        !msg.getAfterTriggerData().get("sales_stage").equals(msg.getBeforeTriggerData().get("sales_stage"))) {
                    updateFollowTime(msg, tenantId, msg.getObjectId(), describeApiName, System.currentTimeMillis());
                    consumedObjectMap.put(consumedKey, Boolean.TRUE);
                }
            }
        }
    }


    private void builderCheckInContents(CustomObjectDataChangeMQMessage.Content msg, List<UpdateFollowDealModel.Content> contents, String describeApiName, IObjectData objectData) {
        String objectId = msg.getObjectId();
        if (describeApiName.equals(ACCOUNT_API_NAME) && StringUtils.isNotBlank(objectData.get("customer_id", String.class))) {
            objectId = objectData.get("customer_id").toString();
        }
        builderCheckInContents(msg, contents, describeApiName, objectData, objectId);
    }


    private void builderCheckInContents(CustomObjectDataChangeMQMessage.Content msg, List<UpdateFollowDealModel.Content> contents, String describeApiName, IObjectData objectData, String objectId) {
        Object finishTime = objectData.get("finish_time");

        UpdateFollowDealModel.Content content = UpdateFollowDealModel.Content.builder()
                .apiName(describeApiName)
                .eventId(msg.getEventId())
                .objectId(objectId)
                .lastFollower(msg.getContext().getUserId())
                .lastFollowTime(finishTime == null ? null : Long.valueOf(finishTime.toString()))
                .build();
        contents.add(content);
    }


    private void getDealStatus(CustomObjectDataChangeMQMessage.Content msg, String tenantId, List<UpdateFollowDealModel.Content> contents) {
        String describeApiName = msg.getEntityId();
        if (!SALES_ORDER_API_NAME.equalsIgnoreCase(describeApiName) &&
                !CONTRACT_API_NAME.equalsIgnoreCase(describeApiName) &&
                !NEW_OPPORTUNITY_API_NAME.equalsIgnoreCase(describeApiName) &&
                !OPPORTUNITY_API_NAME.equalsIgnoreCase(describeApiName) &&
                !SALE_CONTRACT_API_NAME.equalsIgnoreCase(describeApiName)) {
            return;
        }

        DealStatusChangeLog dealStatusChangeLog = validateDataChangeDealStatusGray(msg);

        if (dealStatusChangeLog.isValidateStatus()) {
            followRateLimiterService.getCrmObjectDataRateLimiter().acquire();
            Boolean hasDeal = customerFollowService.hasSettingDeal(tenantId, describeApiName, ApiNameEnum.ACCOUNT_OBJ.getApiName());
            if (!hasDeal) {
                return;
            }

            IObjectData objectData = null;
            try {
                objectData = objectDataPgService.findById(msg.getObjectId(), tenantId, CommonUtils.buildContextAllInvalid(tenantId), describeApiName, true);
            } catch (MetadataServiceException e) {
                log.error("CustomObjectDataChangeListener findById error tenantId:{} ", tenantId, e);
                throw new RuntimeException(e);
            }
            if (NEW_OPPORTUNITY_API_NAME.equalsIgnoreCase(describeApiName) || OPPORTUNITY_API_NAME.equalsIgnoreCase(describeApiName)) {
                // 新商机或者商机
                if (msg.getBeforeTriggerData().get("account_id") != null) {
                    UpdateFollowDealModel.Content content = getDealStatusContent(msg, tenantId, describeApiName, msg.getBeforeTriggerData().get("account_id").toString());
                    content.setFields("account_id");
                    content.setSourceApiName(describeApiName);
                    contents.add(content);
                }
            }


            String objectId;
            if (objectData == null || StringUtils.isBlank(objectData.get("account_id", String.class))) {
                log.warn("objectData is empty:{}", msg);
                return;
            } else {
                objectId = objectData.get("account_id").toString();
            }

            UpdateFollowDealModel.Content content = getDealStatusContent(msg, tenantId, describeApiName, objectId);
            content.setSourceApiName(describeApiName);
            content.setSourceObjectId(msg.getObjectId());
            content.setFields(dealStatusChangeLog.getFields());
            contents.add(content);
        }
    }

    private UpdateFollowDealModel.Content getDealStatusContent(CustomObjectDataChangeMQMessage.Content msg, String tenantId, String describeApiName, String objectId) {
        UpdateFollowDealModel.Content content = UpdateFollowDealModel.Content.builder()
                .apiName(ApiNameEnum.ACCOUNT_OBJ.getApiName())
                .eventId(msg.getEventId())
                .objectId(objectId)
                .lastFollower(msg.getContext().getUserId())
                .build();
        commonService.getDealStatusAndTime(content, tenantId, describeApiName);
        return content;
    }

    /**
     * 是否变更了成交状态
     *
     * @param content
     * @param tenantId
     */
    private boolean isChangeDealStatus(CustomObjectDataChangeMQMessage.Content content, String tenantId) {
        if ((Utils.ACCOUNT_API_NAME.equals(content.getEntityId()) || LEADS_API_NAME.equals(content.getEntityId())) &&
                content.getAfterTriggerData() != null) {
            boolean isRecalculate = false;
            if (content.getAfterTriggerData().get("deal_status") != null || content.getAfterTriggerData().get("last_deal_closed_time") != null) {
                isRecalculate = true;
            }
            if ((content.getBeforeTriggerData().get("life_status") != null && "under_review".equals(content.getBeforeTriggerData().get("life_status")))
                    && (content.getAfterTriggerData().get("life_status") != null && "normal".equals(content.getAfterTriggerData().get("life_status")))) {
                isRecalculate = true;
            }
            if (isRecalculate) {
                customerFollowService.sendRecalculateTask(tenantId, content.getEntityId(), content.getObjectId());
            }
        }
        return false;
    }

    /**
     * 校验数据变化，计算成交状态 <p>
     * 1. 商机     赢单 biz_status 变为win <p>
     * 生命状态从 invalid -> normal <p>
     * 生命状态从 normal,in_change -> invalid <p>
     * <p>
     * 2. 商机2.0 赢单 sales_status变为2 <p>
     * 生命状态从 invalid -> normal <p>
     * 生命状态从 normal,in_change -> invalid <p>
     * <p>
     * 3. 合同 <p>
     * life_status 变为 normal <p>
     * life_status 从 normal,in_change 变为 invalid <p>
     * <p>
     * 4. 订单 <p>
     * life_status 变为 normal <p>
     * life_status 从 normal,in_change 变为 invalid <p>
     * order_status 变为7 <p>
     * <p>
     * 5. 其他情况 <p>
     * 合同新建、导入 life_status 为 normal <p>
     * 订单新建、导入 life_status 为 normal <p>
     * 商机，商机2.0 新建、导入赢单状态的数据 <p>
     */

    private DealStatusChangeLog validateDataChangeDealStatusGray(CustomObjectDataChangeMQMessage.Content msg) {

        ArrayList<String> lifeStatus = Lists.newArrayList("normal", "in_change");
        String describeApiName = msg.getEntityId();
        DealStatusChangeLog dealStatusChangeLog = new DealStatusChangeLog();
        dealStatusChangeLog.setApiName(describeApiName);
        if (msg.getAfterTriggerData() == null) {
            return dealStatusChangeLog;
        }

        // 状态变为赢单
        boolean salesStatusWin = false;
        // 生命状态变为正常（审批通过、恢复）
        boolean lifeStatusNormal = false;
        // 生命状态从作废变为正常 recover
        boolean lifeStatusRecover = false;
        // 作废
        boolean lifeStatusInvalid = false;
        // 订单状态确认、
        boolean orderStatusConfirm = false;
        if (msg.getAfterTriggerData().get("sales_status") != null && msg.getAfterTriggerData().get("sales_status").equals("2")) {
            dealStatusChangeLog.setFields("sales_status");
            salesStatusWin = true;
        } else if (msg.getAfterTriggerData().get("biz_status") != null && msg.getAfterTriggerData().get("biz_status").equals("win")) {
            dealStatusChangeLog.setFields("biz_status");
            salesStatusWin = true;
        } else if (msg.getAfterTriggerData().get("life_status") != null && msg.getAfterTriggerData().get("life_status").equals("normal")) {
            lifeStatusNormal = true;
            dealStatusChangeLog.setFields("life_status");
            // 生命状态从 invalid -> normal
            if (msg.getBeforeTriggerData().get("life_status") != null && msg.getBeforeTriggerData().get("life_status").equals("invalid")) {
                dealStatusChangeLog.setFields("life_status");
                lifeStatusRecover = true;
            }
            //作废  从 normal,in_change 变为 invalid
        } else if ((msg.getBeforeTriggerData().get("life_status") != null && lifeStatus.contains(msg.getBeforeTriggerData().get("life_status")))
                && (msg.getAfterTriggerData().get("life_status") != null && msg.getAfterTriggerData().get("life_status").equals("invalid"))) {
            lifeStatusInvalid = true;
            dealStatusChangeLog.setFields("life_status");
        } else if (msg.getAfterTriggerData().get("order_status") != null && msg.getAfterTriggerData().get("order_status").equals("7")) {
            orderStatusConfirm = true;
            dealStatusChangeLog.setFields("order_status");
        }
        if ((NEW_OPPORTUNITY_API_NAME.equals(describeApiName) || OPPORTUNITY_API_NAME.equals(describeApiName))) {
            dealStatusChangeLog.setValidateStatus(salesStatusWin || lifeStatusInvalid || lifeStatusRecover || msg.getAfterTriggerData().get("account_id") != null || msg.getBeforeTriggerData().get("account_id") != null);
        } else if ((CONTRACT_API_NAME.equals(describeApiName) || SALE_CONTRACT_API_NAME.equals(describeApiName))
                && (lifeStatusNormal || lifeStatusInvalid)) {
            dealStatusChangeLog.setValidateStatus(true);
        } else if (SALES_ORDER_API_NAME.equals(describeApiName) &&
                (orderStatusConfirm || lifeStatusNormal || lifeStatusInvalid)) {
            dealStatusChangeLog.setValidateStatus(true);
        }
        return dealStatusChangeLog;
    }


    /**
     * checkin_status
     *
     * @param msg
     * @return 由 false 变为true  返回true
     */
    private boolean validateCheckinFinished(CustomObjectDataChangeMQMessage.Content msg) {
        if (msg.getBeforeTriggerData() == null || msg.getAfterTriggerData() == null) {
            return false;
        }
        return "false".equalsIgnoreCase(String.valueOf(msg.getBeforeTriggerData().get("checkin_status")))
                && "true".equalsIgnoreCase(String.valueOf(msg.getAfterTriggerData().get("checkin_status")));
    }

    /**
     * 是否设置了跟进
     *
     * @param tenantId
     * @param describeApiName
     * @return true 配置了跟进； false 未配置跟进
     */
    private Map<String, List<String>> newHasFollow(String tenantId, String describeApiName) {
        ActionCodeEnum actionCodeEnum = ActionCodeEnum.ADD;
        if (ApiNameEnum.CONTACT_OBJ.getApiName().equals(describeApiName)) {
            actionCodeEnum = ActionCodeEnum.ASSOCIATE;
        }
        Map<String, List<String>> followSetting;
        if (followGrayUtils.isFollowSettingCacheGray(tenantId)) {
            followSetting = followSettingService.getFollowSetting(tenantId, describeApiName, actionCodeEnum);
        } else {
            followSetting = commonService.getFollowSetting(tenantId, Lists.newArrayList(describeApiName), actionCodeEnum);
        }
        return followSetting;
    }

    /**
     * 导入线索时候单独处理，因为这线索没有配置项是否处理最后跟进时间
     * 导入客户先走之前的方案，暂不处理
     *
     * @param messageObj
     * @param describeApiName
     * @return
     */
    private Boolean dealSystemObj(CustomObjectDataChangeMQMessage messageObj, String describeApiName) {
        String tenantId = messageObj.getTenantId();
        if (LEADS_API_NAME.equals(describeApiName) || ACCOUNT_API_NAME.equals(describeApiName)) {
            UpdateFollowDealModel updateFollowDealModel = UpdateFollowDealModel.builder().tenantId(tenantId).build();
            List<UpdateFollowDealModel.Content> contents = new ArrayList<>();
            for (CustomObjectDataChangeMQMessage.Content msg : messageObj.getBody()) {
                UpdateFollowDealModel.Content content = UpdateFollowDealModel.Content.builder()
                        .objectId(msg.getObjectId())
                        .eventId(msg.getEventId())
                        .apiName(describeApiName)
                        .lastFollowTime(System.currentTimeMillis())
                        .build();
                if (LEADS_API_NAME.equals(describeApiName)) {
                    content.setLastFollower(msg.getContext().getUserId());
                }
                contents.add(content);
            }
            updateFollowDealModel.setContents(contents);
            // 更新跟进时间
            customerFollowService.updateFollowDealTime(updateFollowDealModel);
            return true;
        }
        return false;
    }


    private void toDealStatus(UpdateFollowDealModel.Content content, String tenantId, String describeApiName, IObjectData objectData, Boolean hasDeal, CustomObjectDataChangeMQMessage.Content msg) {
        if (!hasDeal) {
            return;
        }
        boolean calcualteDeaStatus = false;
        if ((CONTRACT_API_NAME.equals(describeApiName) || SALES_ORDER_API_NAME.equals(describeApiName) || SALE_CONTRACT_API_NAME.equals(describeApiName))
                && "normal".equals(objectData.get("life_status", String.class))) {
            calcualteDeaStatus = true;
        } else if (OPPORTUNITY_API_NAME.equals(describeApiName)
                && "win".equals(objectData.get("biz_status", String.class))) {
            calcualteDeaStatus = true;
        } else if (NEW_OPPORTUNITY_API_NAME.equals(describeApiName)
                && "2".equals(objectData.get("sales_status", String.class))) {
            calcualteDeaStatus = true;
        }
        if (calcualteDeaStatus) {
            commonService.getDealStatusAndTime(content, tenantId, describeApiName);
        }
    }


    private void updateFollowTime(CustomObjectDataChangeMQMessage.Content msg, String tenantId, String objectId, String apiName, Long lastFolloweTime) {
        UpdateFollowDealModel updateFollowDealModel = UpdateFollowDealModel.builder().tenantId(tenantId).build();
        UpdateFollowDealModel.Content content = UpdateFollowDealModel.Content.builder()
                .apiName(apiName)
                .eventId(msg.getEventId())
                .objectId(objectId)
                .lastFollowTime(lastFolloweTime)
                .build();
        //todo 最后跟进人
        updateFollowDealModel.setContents(Lists.newArrayList(content));
        followRateLimiterService.getCrmObjectDataRateLimiter().acquire();
        customerFollowService.updateFollowDealTime(updateFollowDealModel);
    }

    private boolean needToFindData(String describeApiName, Map<String, List<String>> map) {
        if (CollectionUtils.empty(map)) {
            return false;
        }
        if (!ACCOUNT_API_NAME.equals(describeApiName) && !LEADS_API_NAME.equals(describeApiName)) {
            for (List<String> value : map.values()) {
                if (value.contains(describeApiName)) {
                    return true;
                }
            }
        }
        return false;
    }


}
