package com.facishare.crm.follow.mq;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.follow.common.FollowRateLimiterService;
import com.facishare.crm.follow.enums.ActionCodeEnum;
import com.facishare.crm.follow.model.CrmActionMQMessage;
import com.facishare.crm.follow.service.CrmActionConsume;

import com.facishare.crm.recycling.task.executor.producer.FollowMessageForwardProducer;
import com.facishare.crm.recycling.task.executor.util.GrayUtils;
import com.facishare.crm.sfa.audit.log.context.SFALogContext;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.fxiaoke.rocketmq.util.MessageHelper;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.Charset;
import java.util.List;

/**
 * crm action mq监听  http://wiki.firstshare.cn/pages/viewpage.action?pageId=37088911
 *
 * <AUTHOR>
 * @create 2018-11-12 3:42 PM
 */
@Slf4j
@Component
public class CrmActionListener implements MessageListenerConcurrently {

    @Autowired
    private FollowRateLimiterService followRateLimiterService;



    @Autowired
    private CrmActionConsume crmActionConsume;

    @Autowired
    private FollowMessageForwardProducer followMessageForwardProducer;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        try {
            consumeMessage(msgs);
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("CrmActionListener consumeMessage error:{}", msgs, e);
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        } finally {
            TraceContext.remove();
            SFALogContext.clearContext();
        }
    }

    public void consumeMessage(List<MessageExt> list) {
        if (CollectionUtils.empty(list)) {
            return;
        }
        for (MessageExt message : list) {
            MessageHelper.fillContextFromMessage(TraceContext.get(), message);
            String body = new String(message.getBody(), Charset.forName("UTF-8"));
            CrmActionMQMessage messageObj = JSON.parseObject(body, CrmActionMQMessage.class);
            /**
             * 665 新建除了审批通过的销售订单外 返回 处理逻辑见: ${@link ObjectDataChangeListener#consumerMessageImport(CustomObjectDataChangeMQMessage)}
             */
            if (ActionCodeEnum.ADD.getActionCode().equals(messageObj.getActionCode())) {
                continue;
            }
            if (ActionCodeEnum.RELATE.getActionCode().equals(messageObj.getActionCode())) {
                messageObj.setActionCode(ActionCodeEnum.ASSOCIATE.getActionCode());
            }
            if (ActionCodeEnum.CONFIRM.getActionCode().equals(messageObj.getActionCode())) {
                messageObj.setActionCode(ActionCodeEnum.CHANGE_STATUS.getActionCode());
            }

            if (!ActionCodeEnum.exist(messageObj.getActionCode())) {
                //log.warn("CrmActionListener actionCode not exist msgId:{},actionCode:{},objectId:{}", message.getMsgId(), messageObj.getActionCode(), messageObj.getObjectID());
                continue;
            }
            if (GrayUtils.skipActionTenantId(messageObj.getTenantID())){
                continue;
            }
            // 跳过 VIP 企业
            if (GrayUtils.actionFollowVIPTenantId(messageObj.getTenantID())){
                continue;
            }
            // 跳过配置企业的-10000的编辑
            if (ActionCodeEnum.EDIT.getActionCode().equals(messageObj.getActionCode()) && crmActionConsume.skipTenantId(messageObj.getTenantID()) && messageObj.getOperatorID().equals("-10000")) {
                log.info("CrmActionListener skipTenantId msgId:{},objectId:{},apiName:{}", message.getMsgId(), messageObj.getObjectID(), messageObj.getObjectApiName());
                continue;
            }

            if (GrayUtils.isSkipEditBySystem(messageObj.getTenantID()) && ActionCodeEnum.EDIT.getActionCode().equals(messageObj.getActionCode())) {
                log.info("CrmActionListener skipEditBySystem msgId:{},objectId:{},apiName:{}", message.getMsgId(), messageObj.getObjectID(), messageObj.getObjectApiName());
                continue;
            }
            if (GrayUtils.isForward2Slow(messageObj.getTenantID())) {
                followMessageForwardProducer.sendCrmActionMessage(body);
                continue;
            }
            SFALogContext.putVariable("msg",message);
            SFALogContext.putVariable("cost1", System.currentTimeMillis() - message.getBornTimestamp());
            SFALogContext.putVariable("bizName","crm_action_follow");
            log.info("CrmActionListener msgId:{},messageObj:{}", message.getMsgId(), messageObj);

            // 先检查是否有跟进配置，避免无效消息消耗限速配额
            if (crmActionConsume.hasFollowSetting(messageObj)) {
                // 只有需要处理的消息才进行限速
                followRateLimiterService.getCrmActionRateLimiter().acquire();
                crmActionConsume.consumeMessageGray(messageObj);
            }
        }
    }

}
