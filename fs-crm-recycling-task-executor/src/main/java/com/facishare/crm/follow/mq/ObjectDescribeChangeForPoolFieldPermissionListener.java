package com.facishare.crm.follow.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.follow.model.FieldDescribeChangeMQMessage;
import com.facishare.crm.follow.model.ObjectDescribeChangeMQMessage;
import com.facishare.crm.follow.util.PoolPermissionTemplateConstants;
import com.facishare.crm.recycling.task.executor.proxy.RecyclingRuleProxy;
import com.facishare.crm.recycling.task.executor.util.CommonSqlUtils;
import com.facishare.paas.appframework.common.mq.RocketMQMessageListener;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.service.ICommonSqlService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.CommonSqlOperator;
import com.facishare.paas.metadata.impl.search.WhereParam;
import com.facishare.paas.metadata.service.impl.CommonSqlServiceImpl;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.jetbrains.annotations.NotNull;

import java.nio.charset.Charset;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class ObjectDescribeChangeForPoolFieldPermissionListener implements RocketMQMessageListener {

    private RecyclingRuleProxy recyclingRuleProxy = SpringUtil.getContext().getBean("recyclingRuleProxy", RecyclingRuleProxy.class);

    private static final ICommonSqlService commonSqlService = SpringUtil.getContext().getBean(CommonSqlServiceImpl.class);


    @Override
    public void consumeMessage(List<MessageExt> messageExtList) {
        if (CollectionUtils.empty(messageExtList)) {
            return;
        }

        try {
            for (MessageExt message : messageExtList) {
                String messageBody = new String(message.getBody(), Charset.forName("UTF-8"));
                if (StringUtils.isBlank(messageBody)) {
                    continue;
                }
                ObjectDescribeChangeMQMessage objectDescribeChangeMQMessage = JSON.parseObject(messageBody, ObjectDescribeChangeMQMessage.class);
                if (CollectionUtils.empty(objectDescribeChangeMQMessage.getBody())) {
                    continue;
                }
                String tenantId = objectDescribeChangeMQMessage.getTenantId();


                JSONObject object = JSON.parseObject(messageBody);
                log.info("ObjectDescribeChangeListener:{},{}", message.getMsgId(), object);
                String opName = object.getString("name");
                String op = object.getString("op");

                if (!Objects.equals(opName, "object_field")) {
                    continue;
                }
                if (!(op.equals("field_delete") || op.equals("field_add"))) {
                    continue;
                }
                FieldDescribeChangeMQMessage jsonMessage = JSON.parseObject(messageBody, FieldDescribeChangeMQMessage.class);
                if (CollectionUtils.empty(jsonMessage.getBody())) {
                    continue;
                }

                for (FieldDescribeChangeMQMessage.Content content : jsonMessage.getBody()) {
                    String apiName = content.getObjectApiName();
                    if (!(apiName.equals("AccountObj") || apiName.equals("LeadsObj"))) {
                        continue;
                    }
                    List<String> fieldNameList = content.getFields().stream().map(x -> x.getApiName()).collect(Collectors.toList());
                    processPoolFieldPermission(tenantId, apiName, op, fieldNameList);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    private void processPoolFieldPermission(String tenantId, String objectApiName, String op, List<String> fieldNameList) {
        if (CollectionUtils.empty(fieldNameList)) {
            return;
        }

        try {

            if (op.equals("field_delete")) {
                for (String fieldName : fieldNameList) {
                    deletePoolPermissionField(tenantId, objectApiName, fieldName);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            throw e;
        }
    }

    @NotNull
    private User getSupperUser(String tenantId) {
        return new User(tenantId, "-10000", "", "");
    }


    protected void deletePoolPermissionField(String tenantId, String apiName, String fieldName) {
        ActionContext actionContext = CommonSqlUtils.getActionContext(tenantId);
        List<WhereParam> wheres = Lists.newArrayList();
        CommonSqlUtils.addWhereParam(wheres, Tenantable.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(tenantId));
        CommonSqlUtils.addWhereParam(wheres, PoolPermissionTemplateConstants.Field.Object_Api_Name, CommonSqlOperator.EQ, Lists.newArrayList(apiName));
        CommonSqlUtils.addWhereParam(wheres, PoolPermissionTemplateConstants.Field.Is_Delete, CommonSqlOperator.EQ, Lists.newArrayList(0));
        CommonSqlUtils.addWhereParam(wheres, PoolPermissionTemplateConstants.Field.Field_Name, CommonSqlOperator.EQ, Lists.newArrayList(fieldName));
        Map<String, Object> updateValue = Maps.newHashMap();
        updateValue.put(PoolPermissionTemplateConstants.Field.Is_Delete, 1);
        try {
            commonSqlService.update("biz_pool_permission_field", updateValue, wheres, actionContext);
        } catch (MetadataServiceException e) {
            log.error(e.getMessage(), e);
        }
    }
}
