package com.facishare.crm.follow.service;

import com.facishare.crm.follow.enums.ActionCodeEnum;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/4/29 18:08
 */

@Slf4j
@Component
public class FollowSettingService {

    @Autowired
    private CommonService commonService;

    private LoadingCache<String, FollowSettingCache> cache;

    @PostConstruct
    public void init() {
        cache = CacheBuilder.newBuilder().maximumSize(3000).expireAfterWrite(30, TimeUnit.MINUTES).build(new CacheLoader<String, FollowSettingCache>() {
            @Override
            public FollowSettingCache load(String s) {
                return new FollowSettingCache();
            }
        });
    }


    public FollowSettingCache getCache(String tenantId, String apiName, ActionCodeEnum actionCode) {
        try {
            return cache.get(tenantId + apiName + actionCode.getActionCode());
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
    }

    public Map<String, List<String>> getFollowSetting(String tenantId, String apiName, ActionCodeEnum actionCode) {

        FollowSettingCache settingCache = getCache(tenantId,apiName, actionCode);
        if (settingCache != null && settingCache.getFollowSetting() != null) {
            log.info("getFollowSetting cache ei:{},{},{}",tenantId,apiName,actionCode.getActionCode());
            return settingCache.getFollowSetting();
        }

        if (settingCache == null || settingCache.getFollowSetting() == null) {
            settingCache = new FollowSettingCache();
            Map<String, List<String>> followSetting = commonService.getFollowSetting(tenantId, Lists.newArrayList(apiName), actionCode);
            log.info("getFollowSetting cache from db ei:{},{},{},{}",tenantId,apiName,actionCode.getActionCode(),followSetting);
            if (followSetting == null){
                followSetting = new HashMap<>();
            }
            settingCache.setFollowSetting(followSetting);
            cache.put(tenantId + apiName + actionCode.getActionCode(), settingCache);
            return followSetting;
        }
        return settingCache.getFollowSetting();
    }

//todo getDealSetting()

    @Data
    public static class FollowSettingCache {
        Map<String, List<String>> followSetting;

        Map<String, Boolean> dealSetting;

    }
}
