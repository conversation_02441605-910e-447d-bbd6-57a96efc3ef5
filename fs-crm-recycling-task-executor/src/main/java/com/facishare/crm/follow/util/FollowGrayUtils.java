package com.facishare.crm.follow.util;

import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.github.autoconf.ConfigFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/2/7 19:42
 */
@Component
public class FollowGrayUtils {

    private static final FsGrayReleaseBiz gray = FsGrayRelease.getInstance("sfa-follow");

    //is_enable_audit_log
    public static Boolean isEnableAuditLog = false;


    @PostConstruct
    public void init() {
        ConfigFactory.getConfig("fs-gray-sfa-follow", config -> {
            isEnableAuditLog = config.getBool("is_enable_audit_log", false);
        });
    }

    /**
     * 跟进后计算到期时间白名单
     *
     * @param tenantId
     * @return
     */
    public boolean isRecalculateVIP(String tenantId) {
        return gray.isAllow("recalculate-vip", tenantId);
    }

    public boolean isFollowSettingCacheGray(String tenantId) {
        return gray.isAllow("follow_setting_type_cache", tenantId);
    }


    public boolean isFollowSettingGray(String tenantId) {
        return true;
    }

    /**
     * 线索自动分配发送通知灰度
     *
     * @param tenantId
     * @return
     */
    public Boolean isAutoAllocateImprovedSendNotice(String tenantId) {
        return gray.isAllow("auto_allocate_improved_send_notice", tenantId);
    }
}
