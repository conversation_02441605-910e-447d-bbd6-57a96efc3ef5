package com.facishare.crm.follow.service;

import com.facishare.crm.follow.enums.ActionCodeEnum;
import org.codehaus.jackson.JsonParseException;
import org.codehaus.jackson.map.JsonMappingException;
import org.codehaus.jackson.type.TypeReference;
import com.fxiaoke.helper.StringHelper;
import com.fxiaoke.notifier.support.NotifierClient;
import com.github.jedis.support.JedisCmd;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.jackson.map.ObjectMapper;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/7/25 18:19
 * @description:
 */

@Slf4j
@Component
public class FollowSettingCacheService {


    @Autowired
    private CommonService commonService;

    @Autowired
    protected JedisCmd SFAJedisCmd;

    private LoadingCache<String, SettingCache> localCache;

    public static final String SETTING_RULE_CHANGE = "follow-setting-rule-change";

    private static final String REDIS_KEY_PREFIX = "sfa-follow-setting-rule:";

    // 在类的开头添加这个字段
    private final ObjectMapper objectMapper = new ObjectMapper();




    @PostConstruct
    public void init() {
        localCache = CacheBuilder.newBuilder().maximumSize(1000).expireAfterWrite(30, TimeUnit.MINUTES).build(
                new CacheLoader<String, SettingCache>() {
                    @NotNull
                    @Override
                    public FollowSettingCacheService.SettingCache load(@NotNull String tenantId) {
                        return loadFromRedisOrDb(tenantId);
                    }
                }
        );
        subscribeInvalidMessage();
    }


    private SettingCache loadFromRedisOrDb(String key) {
        String redisKey = REDIS_KEY_PREFIX + key;
        String cacheJson = SFAJedisCmd.get(redisKey);
        if (cacheJson != null) {
            log.debug("followSetting cache from redis ei:{}", key);
            try {
                return deserializeFromJson(cacheJson);
            } catch (Exception e) {
                log.error("值班的你请忽略，Error deserializing followSetting cache from JSON redisKey:{}",redisKey, e);
                SFAJedisCmd.del(redisKey);
            }
        }
        log.debug("followSetting cache from db ei:{}", key);
        SettingCache settingCache = loadFromDb(key);
        String serializedCache = serializeToJson(settingCache);
        SFAJedisCmd.set(redisKey, serializedCache);
        return settingCache;
    }

    private SettingCache loadFromDb(String tenantId) {
        Map<String, Map<String, Set<String>>> followSetting = commonService.getFollowSetting(tenantId);
        log.debug("followSetting cache loaded from db: {},{}", tenantId, followSetting);
        followSetting = (followSetting != null) ? followSetting : new HashMap<>();
        SettingCache settingCache = new SettingCache();
        settingCache.setFollowSetting(followSetting);
        return settingCache;
    }

    private SettingCache deserializeFromJson(String json) throws IOException, JsonParseException, JsonMappingException {
        return objectMapper.readValue(json, new TypeReference<SettingCache>() {});
    }



    private String serializeToJson(SettingCache settingCache) {
        try {
            return objectMapper.writeValueAsString(settingCache);
        } catch (Exception e) {
            log.error("Error serializing followSetting cache to JSON", e);
            return "{}"; // 返回空 JSON 对象或者抛出自定义异常
        }
    }



    public SettingCache getCache(String tenantId) {
        try {
            return localCache.get(tenantId);
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
    }



    public boolean getFollowSetting(String tenantId, String sourceApiName,String apiName, ActionCodeEnum actionCode) {
        Set<String> objectApiNames = getFollowSetting(tenantId, sourceApiName, actionCode);
        return objectApiNames != null && objectApiNames.contains(apiName);
    }
    /**
     *  获取跟进配置
     * @param tenantId
     * @param sourceApiName
     * @param actionCode
     * @return
     */
    public Set<String> getFollowSetting(String tenantId, String sourceApiName, ActionCodeEnum actionCode) {
        Map<String, Map<String, Set<String>>> followSetting = getFollowSetting(tenantId);
        if (followSetting == null) {
            return null;
        }
        Map<String, Set<String>> actionMap = followSetting.get(sourceApiName);
        if (actionMap == null) {
            return null;
        }
        return actionMap.get(actionCode.getActionCode());
    }

    public Map<String, Map<String,Set<String>>> getFollowSetting(String tenantId) {
        SettingCache settingCache = getCache(tenantId);
        //settingCache.getFollowSetting()
        if (settingCache.getFollowSetting() == null || settingCache.getFollowSetting().isEmpty()) {
            settingCache = loadFromRedisOrDb(tenantId);
        }
        log.debug("begin get followSetting cache, tenantId:{}, settingCache:{}", tenantId, settingCache);
        return settingCache.getFollowSetting();
    }

    public boolean isSkipWithoutRuleByTenantId(String tenantId) {
        return SettingCache.EMPTY.equals(getCache(tenantId));
    }

    @SneakyThrows
    public void invalid(String tenantId) {
        log.info("publish invalidate available followSetting cache, tenantId:{}", tenantId);
        localCache.invalidate(tenantId);
        NotifierClient.send(SETTING_RULE_CHANGE, tenantId, null);
    }

    private void subscribeInvalidMessage() {
        NotifierClient.register(SETTING_RULE_CHANGE, message -> {
            String tenantId = message.getContent();
            if (StringHelper.isNotNullOrBlank(tenantId)) {
                log.info("fast-notifier: invalidate followSetting cache, tenantId:{}", tenantId);
                localCache.invalidate(tenantId);
                SFAJedisCmd.del(REDIS_KEY_PREFIX + tenantId);
                localCache.invalidate(tenantId);
            }
        });
    }


    @Data
    public static class SettingCache {

        private boolean empty;

        public static final SettingCache EMPTY = new SettingCache();

        public static final SettingCache NOT_EMPTY = new SettingCache(true);

        Map<String, Map<String, Set<String>>> followSetting;

        public SettingCache() {
        }

        public SettingCache(boolean empty) {
            this.empty = empty;
        }
    }

}
