package com.facishare.crm.follow.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020/5/21 14:52
 */
@Data
public class CrmNewFeedMessage {
    //当前登录用户信息
    private String enterpriseAccount;
    @JSONField(name = "enterpriseId")
    private Integer tenantId;
    private Integer currentEmployeeId;

    //feedId
    private Long feedId;
    //feed类型
    private String feedType;
    //feed正文
    private String feedContent;
    //feed发布人
    private Long senderId;
    //feed创建时间
    private Long createTime;
    //最近更新时间
    private Long lastUpdateTime;
    //uuid
    private String uuid;

    //附件信息
    private List<Attachment> attachments;

    //话题信息
    private List<Topic> topics;

    //业务类型 - 如日志的日计划，月计划，周计划
    private String bizType;
    //业务状态 - 如审批待审批，已审批
    private Integer bizStatus;

    //有feed权限的人员集合
    private List<Integer> permissionEmployees;
    //关注的人员集合
    private List<Integer> followEmployees;
    //抄送范围内的人员集合
    private List<Integer> rangeEmployees;
    //抄送范围内的部门集合
    private List<Integer> rangeDepartments;
    //AT的人员集合
    private List<Integer> atEmployees;
    //AT的部门集合
    private List<Integer> atDepartments;
    //所有需要回执的人员集合
    private List<Integer> allReceipts;
    //未回执的人员集合
    private List<Integer> waitingReceipts;

    //当前时间戳
    private Long version;
    //操作类型，1-ADD 2-MODIFY 3-DELETE
    private Integer operationType;

    //crm对象信息
    private List<CrmObject> crmObjects;

    //crm标签信息
    private List<CrmTag> crmTags;

    private String eventId;

    /**
     * 附件信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Attachment {
        //附件id
        private Long attachmentId;
        //附件名称
        private String attachmentName;
        //附件类型
        private Integer attachmentType;
    }

    /**
     * 话题信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Topic {
        //话题id
        private Long topicId;
        //话题名称
        private String topicName;
    }

    /**
     * crm对象
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CrmObject {
        private String apiName;
        private String dataId;
    }

    /**
     * crm tag
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CrmTag {
        private String dataId;
    }

    @Override
    public String toString() {
        return "CrmNewFeedMessage{" +
                "enterpriseAccount='" + enterpriseAccount + '\'' +
                ", tenantId=" + tenantId +
                ", currentEmployeeId=" + currentEmployeeId +
                ", feedId=" + feedId +
                ", feedType='" + feedType + '\'' +
                ", senderId=" + senderId +
                ", createTime=" + createTime +
                ", lastUpdateTime=" + lastUpdateTime +
                ", uuid='" + uuid + '\'' +
                ", version=" + version +
                ", operationType=" + operationType +
                ", crmObjects=" + crmObjects +
                ", crmTags=" + crmTags +
                ", eventId='" + eventId + '\'' +
                '}';
    }
}
