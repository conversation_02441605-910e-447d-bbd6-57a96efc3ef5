package com.facishare.crm.follow.enums;

public enum SourceTypeEnums {
    PROJECTTASK(2,"项目管理-任务") , //
    PROJECTTASKSETTINGS(4,"项目管理-任务配置") , //
    EMAIL(5,"邮件") , //
    EVENTTAG(100,"销售记录标签") ,    //
    SALESCLUE(101,"线索") , //
    CUSTOMER(102,"客户") , //
    CONTACT(103,"联系人") , //
    PRODUCT(104,"产品") , //
    PAYMENT(105,"回款") , //
    REFUND(106,"退款") , //
    SALEACTION(107,"销售流程") , //
    OPPORTUNITY(108,"机会") , //
    BILL(109,"开票") , //
    TRADE(110,"成交") , //
    CUSTOMERORDER(111,"订单") , //
    RETURNORDER(112,"退货单") , //
    VISIT(113,"拜访") , //
    VISITACTISTUDYON(114,"拜访动作") , //
    INVENTORYACTION(115,"盘点动作") , //
    CONTRACT(116,"合同") , //
    SALESCLUEPOOL(117,"线索池") , //
    HIGHSEAS(118,"公海") , //
    COMPETITOR(119,"竞争对手") , //
    MARKETINGEVENT(120,"市场活动") , //
    INVENTORY(121,"盘点") , //
    SALESORDERPRODUCT(128,"盘点") , //
    METADATA(200,"xx") ,//
    WAIQIN(300,"xx") ,      //
    WAIQINV2(301,"xx");

    private Integer source;

    private String desc;

    SourceTypeEnums(Integer source, String desc) {
        this.source = source;
        this.desc = desc;
    }



}
