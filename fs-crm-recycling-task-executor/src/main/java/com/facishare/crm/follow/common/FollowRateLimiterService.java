package com.facishare.crm.follow.common;

import com.github.autoconf.ConfigFactory;
import com.google.common.util.concurrent.RateLimiter;
import lombok.Data;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-08-11 19:30
 */
@Component
@Data
public class FollowRateLimiterService {

    private double crmActionLimit;
    private double crmActionVIPLimit;
    private double crmFeedLimit;
    private double crmObjectDataLimit;
    private double prmUpdateObjectLimit;
    private RateLimiter crmActionRateLimiter;
    private RateLimiter crmActionVIPRateLimiter;
    private RateLimiter crmFeedRateLimiter;
    private RateLimiter crmObjectDataRateLimiter;
    private RateLimiter prmUpdateObjectLimiter;

    @PostConstruct
    public void init() {
        ConfigFactory.getConfig("fs-crm-task-rate-limit", config -> {
            crmActionLimit = config.getDouble("crmActionLimit", 20);
            crmActionVIPLimit = config.getDouble("crmActionVIPLimit", 10);
            crmFeedLimit = config.getDouble("crmFeedLimit", 20);
            crmObjectDataLimit = config.getDouble("crmObjectDataLimit", 20);
            prmUpdateObjectLimit = config.getDouble("prmUpdateObjectLimit", 10);
            crmActionRateLimiter = RateLimiter.create(crmActionLimit);
            crmActionVIPRateLimiter = RateLimiter.create(crmActionVIPLimit);
            crmFeedRateLimiter = RateLimiter.create(crmFeedLimit);
            crmObjectDataRateLimiter = RateLimiter.create(crmObjectDataLimit);
            prmUpdateObjectLimiter = RateLimiter.create(prmUpdateObjectLimit);
        });
    }
}
