package com.facishare.crm.follow.util;

import com.github.autoconf.spring.reloadable.ReloadableProperty;
import org.springframework.stereotype.Component;

/**
 * @Description
 * <AUTHOR>
 * @Date 2021/12/27 11:52
 */

@Component
public class DelayTimer {

    @ReloadableProperty("SFAFollowDispatchDelayTime")
    private long delayTime;

    public long getDelayTime() {
        return delayTime;
    }

}
