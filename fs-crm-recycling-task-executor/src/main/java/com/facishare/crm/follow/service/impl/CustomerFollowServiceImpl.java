package com.facishare.crm.follow.service.impl;

import com.facishare.crm.follow.dispatch.FollowDataEvent;
import com.facishare.crm.follow.dispatch.FollowEventService;
import com.facishare.crm.follow.enums.ActionCodeEnum;
import com.facishare.crm.follow.enums.ApiNameEnum;
import com.facishare.crm.follow.enums.DealStatusEnum;
import com.facishare.crm.follow.model.ObjectFollowDealSettingEnums;
import com.facishare.crm.follow.model.RecalculateMessage;
import com.facishare.crm.follow.model.UpdateFollowDealModel;
import com.facishare.crm.follow.mq.RecyclingRuleProducer;
import com.facishare.crm.follow.service.CustomerFollowService;
import com.facishare.crm.follow.util.CommonUtils;
import com.facishare.crm.follow.util.ConstantUtils;
import com.facishare.crm.follow.util.DelayTimer;
import com.facishare.crm.follow.util.ObjectFollowDealSettingObj;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.recycling.task.executor.util.GrayUtils;
import com.facishare.crm.recycling.task.executor.util.I18NKey;
import com.facishare.crm.recycling.task.executor.util.SearchUtil;
import com.facishare.crm.sfa.audit.log.context.SFALogContext;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.LogServiceImpl;
import com.facishare.paas.appframework.log.dto.InternationalItem;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.GetI18nKeyUtil;
import com.fxiaoke.dispatcher.common.MessageHelper;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.follow.util.CommonUtils.buildContext;
import static com.facishare.crm.follow.util.ConstantUtils.*;
import static com.facishare.crm.recycling.task.executor.util.ConstantUtils.ACCOUNT_OBJ;
import static com.facishare.crm.recycling.task.executor.util.ConstantUtils.LEADS_OBJ;
import static com.fxiaoke.dispatcher.common.Constants.STATUS_READY;

/**
 * <AUTHOR>
 * @create 2018-11-06 8:16 PM
 */
@Slf4j
@Component
public class CustomerFollowServiceImpl implements CustomerFollowService {


    @Autowired
    private MetaDataFindService metaDataFindService;

    @Autowired
    private IObjectDescribeService objectDescribeService;

    @Autowired
    private IObjectDataService objectDataPgService;

    @Autowired
    protected SpecialTableMapper SPECIAL_TABLE_MAPPER;

    @Autowired
    private RecyclingRuleProducer recyclingRuleProducer;

    @Autowired
    private GrayUtils grayUtils;

    @Autowired
    private FollowEventService followEventService;

    @Autowired
    private DelayTimer delayTimer;
    @Autowired
    private LogServiceImpl logService;
    @Autowired
    private IObjectDescribeService iObjectDescribeService;


    @Override
    public void updateFollowDealTime(UpdateFollowDealModel model) {
        if (model == null || CollectionUtils.empty(model.getContents())) {
            return;
        }
        SFALogContext.putVariable("status", true);
        String tenantId = model.getTenantId();
        log.info(" updateFollowDealTime:{}", model);
        List<String> duplicatedObjectIds = Lists.newArrayList();

        for (UpdateFollowDealModel.Content content : model.getContents()) {
            List<String> updateFieldList = Lists.newArrayList();
            if (StringUtils.isBlank(content.getObjectId())) {
                continue;
            }
            if (duplicatedObjectIds.contains(content.getObjectId())) {
                log.info("skip update duplicated objectIds");
                continue;
            }
            duplicatedObjectIds.add(content.getObjectId());

            IObjectData data = new ObjectData();
            data.setTenantId(tenantId);
            data.setDescribeApiName(content.getApiName());
            data.setId(content.getObjectId());
            data.set("change_type", "(((coalesce(change_type,0) >> 6) # 3) << 6) + 0");
            if (StringUtils.isNotBlank(content.getDealStatus())) {
                data.set("deal_status", content.getDealStatus());
                data.set("last_deal_closed_time", content.getDealTime());
                updateFieldList.add("deal_status");
                updateFieldList.add("last_deal_closed_time");
            }

            IObjectData objectData = null;
            try {
                objectData = objectDataPgService.findById(content.getObjectId(), tenantId, buildContext(buildUser(tenantId)), content.getApiName());
            } catch (MetadataServiceException e) {
                log.error("checkUpdate, objectDataPgService findById error", e);
                throw new RuntimeException(e);
            }
            if (content.getLastFollowTime() != null) {
                if (content.getApiName().equals(ConstantUtils.LEADS_OBJ)) {
                    data.set("is_overtime", Boolean.FALSE);
                    updateFieldList.add("is_overtime");
                }

                if (StringUtils.isNotBlank(content.getLastFollower()) && objectData != null) {
                    if (content.getApiName().equals(ACCOUNT_OBJ) || content.getApiName().equals(LEADS_OBJ)) {
                        if (objectData.get(LAST_FOLLOWER) == null || "".equals(objectData.get(LAST_FOLLOWER)) || !content.getLastFollower().equals(objectData.get(LAST_FOLLOWER))) {
                            data.set(LAST_FOLLOWER, Lists.newArrayList(content.getLastFollower()));
                            updateFieldList.add(LAST_FOLLOWER);
                        }
                    }
                }
                boolean updateLastFollowTime = true;
                // 没有成交状态检查跟进时间
                if (StringUtils.isBlank(content.getDealStatus())) {
                    // 更新跟进时间
                    updateLastFollowTime = checkUpdate(tenantId, content.getApiName(), content.getObjectId(), content.getLastFollowTime(), objectData);
                }
                if (updateLastFollowTime) {
                    if (content.getApiName().equals(ConstantUtils.LEADS_OBJ)) {
                        data.set("last_follow_time", content.getLastFollowTime());
                        updateFieldList.add("last_follow_time");
                    } else {
                        data.set("last_followed_time", content.getLastFollowTime());
                        updateFieldList.add("last_followed_time");
                    }
                } else {
                    updateFieldList.remove("last_followed_time");
                    log.warn("continue update follow_time:{},{} ", content.getObjectId(), content.getApiName());
                }
                if (model.getLastModifiedBy() != null) {
                    data.setLastModifiedBy(model.getLastModifiedBy());
                    updateFieldList.add(DBRecord.LAST_MODIFIED_BY);
                }
            }
            saveChangeDealStatusLog(objectData, content);
            saveNewOpportunityChangeRecordLog(data, objectData, content);
            log.info("batchUpdateIgnoreOther:{},{}", data, updateFieldList);
            try {
                if (CollectionUtils.notEmpty(updateFieldList)) {
                    if (grayUtils.isGrayDispatch(tenantId)) {
                        log.info("dispatch upsertEvents:{},{}", data, updateFieldList);
                        followEventService.upsertEvents(toEvents(data, updateFieldList, tenantId));
                    } else {
                        objectDataPgService.batchUpdateIgnoreOther(Lists.newArrayList(data), updateFieldList, getDefaultActionContext(buildUserForUpdate(tenantId), model.getContents()));
                    }
                } else {
                    log.info("data's is empty:{}", data);
                }
            } catch (MetadataServiceException e) {
                log.error(" {}", data, e);
            }
        }


        sendRecalculateTask(tenantId, model.getContents());
    }

    private void saveNewOpportunityChangeRecordLog(IObjectData data, IObjectData dbData, UpdateFollowDealModel.Content content) {
        String sourceApiName = content.getSourceApiName();
        if (dbData == null || sourceApiName == null || !dbData.getDescribeApiName().equals(Utils.NEW_OPPORTUNITY_API_NAME)) {
            return;
        }
        String tenantId = dbData.getTenantId();
        if (!GrayUtils.isUpdateNewOpportunityLastModified(tenantId)) {
            return;
        }
        String peerName = null;
        List<String> internationalParameters = new ArrayList<>();
        if ("ActiveRecordObj".equals(sourceApiName)) { // 新增{0}
            peerName = "NewOpportunityObj.record.peer_name.add";
            internationalParameters.add(GetI18nKeyUtil.getDescribeDisplayNameKey("ActiveRecordObj"));
        }
        if ("StageInstanceObj".equals(sourceApiName)) { // 变更{0}
            peerName = "NewOpportunityObj.record.peer_name.edit";
            internationalParameters.add(GetI18nKeyUtil.getFieldLabelKey(Utils.NEW_OPPORTUNITY_API_NAME, "sales_stage"));
        }
        if (peerName == null) {
            return;
        }
        IObjectDescribe describe;
        try {
            describe = objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, Utils.NEW_OPPORTUNITY_API_NAME);
        } catch (MetadataServiceException e) {
            throw new RuntimeException(e);
        }
        IObjectData copy = ObjectDataExt.of(data).copy();
        ObjectDataExt.of(copy).merge(dbData);
        Map<String, Object> map = new HashMap<>();
        map.put("last_followed_time", content.getLastFollowTime());
        String peerNameDisplay = I18N.text(peerName);
        InternationalItem peerNameI18N = InternationalItem.builder().internationalKey(peerName).defaultInternationalValue(peerNameDisplay).internationalParameters(internationalParameters).build();
        logService.log(new User(tenantId, copy.getLastModifiedBy()), EventType.MODIFY, ActionType.Modify, describe, copy, map, dbData, peerName, peerNameDisplay, peerNameI18N, data.getId(), new HashMap<>());
    }

    public void saveChangeDealStatusLog(IObjectData objectData, UpdateFollowDealModel.Content content) {
        if (StringUtils.isBlank(content.getDealStatus()) || objectData == null) {
            return;
        }
        if (objectData.get("deal_status") != null && content.getDealStatus().equals(objectData.get("deal_status"))) {
            return;
        }

        String logContent = "下 %s %s发生变更，触发客户成交规则，成交状态被更改为 %s 状态。";
        IObjectDescribe objectDescribe = null;
        IObjectDescribe sourceObjectDescribe;
        String internationalKey = I18NKey.SFA_DEAL_STATUS_CHANGE_LOG_MSG;
        List<String> parameters = Lists.newArrayList();

        try {
            objectDescribe = iObjectDescribeService.findByTenantIdAndDescribeApiName(objectData.getTenantId(), content.getApiName());
            sourceObjectDescribe = iObjectDescribeService.findByTenantIdAndDescribeApiName(objectData.getTenantId(), content.getSourceApiName());
            String displayName = sourceObjectDescribe.getDisplayName();
            if (!Strings.isBlank(content.getFields())) {
                Optional<IFieldDescribe> fieldDescribe = sourceObjectDescribe.getFieldDescribes().stream().filter(field -> field.getApiName().equals(content.getFields())).findFirst();
                String fieldLabel = "";
                if (fieldDescribe.isPresent()) {
                    //fieldLabel = fieldDescribe.get().getLabel();
                    fieldLabel = GetI18nKeyUtil.getFieldLabelKey(content.getSourceApiName(), fieldDescribe.get().getApiName());
                }
                logContent = I18N.text(I18NKey.SFA_DEAL_STATUS_CHANGE_LOG_MSG, displayName, fieldLabel, DealStatusEnum.getDesc(content.getDealStatus()));
                parameters = Lists.newArrayList(displayName, fieldLabel, DealStatusEnum.getDealStatusI18nKey(content.getDealStatus()));
            } else {
                logContent = I18N.text(I18NKey.SFA_DEAL_STATUS_CHANGE_NO_FIELDS_LOG_MSG, displayName, DealStatusEnum.getDesc(content.getDealStatus()));
                internationalKey = I18NKey.SFA_DEAL_STATUS_CHANGE_NO_FIELDS_LOG_MSG;
                parameters = Lists.newArrayList(displayName, DealStatusEnum.getDealStatusI18nKey(content.getDealStatus()));
            }

        } catch (MetadataServiceException e) {
            log.error("saveChangeDealStatusLog error", e);
        }
        log.info("saveChangeDealStatusLog:{},{}", content, logContent);
        InternationalItem internationalItem = InternationalItem.builder()
                .defaultInternationalValue(logContent)
                .internationalKey(internationalKey)
                .internationalParameters(parameters)
                .build();
        logService.logWithInternationalCustomMessage(buildUser(objectData.getTenantId()), EventType.MODIFY, ActionType.None, objectDescribe, objectData,
                logContent, internationalItem);

    }


    /**
     * 一个小时之前的跟进时间， 查询数据库跟进时间：
     * 历史跟进时间为空  更新当前跟进时间
     * 当前跟进时间 <= 历史跟进时间 不更新「最后跟进时间」
     * 当前跟进时间 <= 历史跟进时间 + 1.0 秒 不更新「最后跟进时间」，需要计算到期时间，
     *
     * @param tenantId
     * @param apiName
     * @param objectId
     * @param lastFollowedTime
     * @return
     */
    private boolean checkUpdate(String tenantId, String apiName, String objectId, Long lastFollowedTime, IObjectData objectData) {
        if (objectData == null) {
            log.info("objectData is null :{},{},{}", objectId, tenantId, apiName);
            return false;
        }
        Long oldLastFollowedTime = null;
        if (Utils.LEADS_API_NAME.equals(apiName)) {
            oldLastFollowedTime = objectData.get(LAST_FOLLOW_TIME, Long.class);
        } else {
            oldLastFollowedTime = objectData.get(LAST_FOLLOWED_TIME, Long.class);
        }
        if (oldLastFollowedTime == null) {
            return true;
        } else if (lastFollowedTime <= oldLastFollowedTime) {
            return false;
        } else if (lastFollowedTime <= (oldLastFollowedTime + 1000L)) {
            //recalculateTaskService.send(tenantId, objectId, apiName, com.facishare.crm.sfa.predefine.enums.ActionCodeEnum.FOLLOW);
            sendRecalculateTask(tenantId, apiName, objectId);
            return false;
        } else {
            return true;
        }
    }

    public List<FollowDataEvent> toEvents(IObjectData data, List<String> updateFieldList, String tenantId) {
        List<FollowDataEvent> events = Lists.newArrayList();
        events.add(toEvent(data, updateFieldList, tenantId));
        return events;
    }

    public FollowDataEvent toEvent(IObjectData data, List<String> updateFieldList, String tenantId) {

        // 唯一ID(tenantId + objectDescribeApiName + dataId)
        String uniKey = MessageHelper.md5(tenantId, data.getDescribeApiName(), data.getId(), "follow");
        // 构建分类（tenantId + objectDescribeApiName）
        String category = Joiner.on('^').join(tenantId, data.getDescribeApiName());
        long now = System.currentTimeMillis();
        FollowDataEvent followDataEvent = FollowDataEvent.builder()
                .key(uniKey)
                .status(STATUS_READY)
                .category(category)
                .createTime(now)
                .modifiedTime(now)
                .dispatchTime(now + delayTimer.getDelayTime())
                .objectApiName(data.getDescribeApiName())
                .dataId(data.getId())
                .lastDealClosedTime(data.get(LAST_DEAL_CLOSED_TIME, Long.class))
                .lastFollowerId(data.get(LAST_FOLLOWER, String.class))
                .fieldApiNames(Sets.newHashSet(updateFieldList))
                .tenantId(tenantId).build();

        if (data.get(LAST_FOLLOW_TIME) != null) {
            followDataEvent.setLastFollowTime((Long) data.get(LAST_FOLLOW_TIME));
        }

        if (data.get(LAST_FOLLOWED_TIME) != null) {
            followDataEvent.setLastFollowTime((Long) data.get(LAST_FOLLOWED_TIME));
        }

        if (data.get(LAST_DEAL_CLOSED_TIME) != null) {
            followDataEvent.setLastDealClosedTime((Long) data.get(LAST_DEAL_CLOSED_TIME));
        }
        if (data.get(DEAL_STATUS) != null) {
            followDataEvent.setDealStatus((String) data.get(DEAL_STATUS));
        }

        if (data.get(LAST_FOLLOWER) != null) {
            ArrayList<String> lastFollowers = data.get(LAST_FOLLOWER, ArrayList.class);
            if (lastFollowers.size() > 0) {
                followDataEvent.setLastFollowerId(lastFollowers.get(0).toString());
            }
        }

        if (data.get(IS_OVERTIME) != null) {
            followDataEvent.setOverTime((Boolean) data.get(IS_OVERTIME));
        }

        String lastModifiedBy = data.getLastModifiedBy();
        if (lastModifiedBy != null && GrayUtils.isUpdateNewOpportunityLastModified(tenantId)) {
            followDataEvent.setLastModifiedBy(lastModifiedBy);
        }
        log.info("dispatch toEvent:{}", followDataEvent);
        return followDataEvent;
    }


    @Override
    public void updateFollowDealTimeByRefresh(UpdateFollowDealModel model) {
        UpdateFollowDealModel updateFollowDealModel = UpdateFollowDealModel.builder()
                .tenantId(model.getTenantId())
                .follower(model.getFollower())
                .build();
        List<UpdateFollowDealModel.Content> contents = new ArrayList<>();
        for (UpdateFollowDealModel.Content content : model.getContents()) {
            try {
                IObjectData objectData = objectDataPgService.findById(content.getObjectId(), model.getTenantId(), Utils.ACCOUNT_API_NAME);
                Long lastFollowedTime = objectData.get("last_followed_time", Long.class);
                if (lastFollowedTime != null && lastFollowedTime > 1566478800000L && lastFollowedTime < 1566513000000L) {
                    log.info("in refresh time:{},{}", objectData.getId(), model.getTenantId());
                    contents.add(content);
                } else {
                    log.info("not in refresh time ignore:{}", objectData.getId());
                }
            } catch (MetadataServiceException e) {
                throw new RuntimeException(e);
            }
        }
        updateFollowDealModel.setContents(contents);
        updateFollowDealTime(updateFollowDealModel);
    }

    @Override
    public void updateFollowDealTimeByRefreshGray(UpdateFollowDealModel model) {
        UpdateFollowDealModel updateFollowDealModel = UpdateFollowDealModel.builder()
                .tenantId(model.getTenantId())
                .follower(model.getFollower())
                .build();
        List<UpdateFollowDealModel.Content> contents = new ArrayList<>();
        for (UpdateFollowDealModel.Content content : model.getContents()) {
            try {
                IObjectData objectData = objectDataPgService.findById(content.getObjectId(), model.getTenantId(), Utils.ACCOUNT_API_NAME);
                Long lastFollowedTime = objectData.get("last_followed_time", Long.class);
                if (lastFollowedTime != null && lastFollowedTime.compareTo(content.getLastFollowTime()) < 0) {
                    log.info("gray in refresh lastFollowedTime < feedCreateTime:{},{}", objectData.getId(), model.getTenantId());
                    contents.add(content);
                    continue;
                } else {
                    log.info("not in refresh time ignore:{}", objectData.getId());
                }
            } catch (MetadataServiceException e) {
                throw new RuntimeException(e);
            }
        }
        updateFollowDealModel.setContents(contents);
        updateFollowDealTime(updateFollowDealModel);
    }

    @Override
    public String getObjectFollowDealSettingType(String tenantId,
                                                 String objectApiName,
                                                 ActionCodeEnum actionCode) {
        return getObjectFollowDealSettingType(tenantId, objectApiName, actionCode, "AccountObj");
    }


    @Override
    public String getObjectFollowDealSettingType(String tenantId, String sourceObjectApiName, ActionCodeEnum actionCode, String objectApiName) {
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(3);
        searchQuery.setOffset(0);
        searchQuery.setNeedReturnCountNum(false);
        searchQuery.setPermissionType(0);
        List filters = Lists.newLinkedList();
        SearchUtil.fillFilterEq(filters, ObjectFollowDealSettingObj.SOURCE_OBJECT_API_NAME, sourceObjectApiName);
        SearchUtil.fillFilterEq(filters, ObjectFollowDealSettingObj.OBJECT_API_NAME, objectApiName);
        SearchUtil.fillFilterEq(filters, ObjectFollowDealSettingObj.IS_ENABLE, true);
        SearchUtil.fillFilterEq(filters, ObjectFollowDealSettingObj.ACTION_CODE, actionCode.getActionCode());
        SearchUtil.fillFilterEq(filters, IS_DELETED, "0");
        searchQuery.setFilters(filters);
        User user = new User(tenantId, "-10000", "", "");
        QueryResult<IObjectData> queryResult = null;

        searchQuery.setNeedReturnQuote(false);
        IActionContext actionContext = ActionContextExt.of(user)
                .disableDeepQuote()
                .skipRelevantTeam()
                .getContext();
        actionContext.setDoCalculate(false);


        try {
            queryResult = metaDataFindService.findBySearchQuery(actionContext,
                    OBJECT_FOLLOW_DEAL_SETTING_OBJ, searchQuery);
        } catch (Exception e) {
            log.error("find ObjectFollowDealSettingObj error tenantId:{},", tenantId, e);
            throw e;
        }
        if (queryResult == null) {
            return null;
        }

        if (CollectionUtils.empty(queryResult.getData())) {
            return null;
        } else if (queryResult.getData().size() > 1) {
            return ObjectFollowDealSettingEnums.ObjectFollowDealSettingType.ALL.getValue();
        } else {
            return queryResult.getData().get(0).get("setting_type").toString();
        }
    }

    @Override
    public String getSettingTypeByActionCodes(String tenantId, String sourceObjectApiName, String objectApiName, List<String> actionCodes) {
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(3);
        searchQuery.setOffset(0);
        searchQuery.setNeedReturnCountNum(false);
        searchQuery.setPermissionType(0);
        List filters = Lists.newLinkedList();
        SearchUtil.fillFilterEq(filters, ObjectFollowDealSettingObj.SOURCE_OBJECT_API_NAME, sourceObjectApiName);
        SearchUtil.fillFilterEq(filters, ObjectFollowDealSettingObj.OBJECT_API_NAME, objectApiName);
        SearchUtil.fillFilterEq(filters, ObjectFollowDealSettingObj.IS_ENABLE, true);
        SearchUtil.fillFilterIn(filters, ObjectFollowDealSettingObj.ACTION_CODE, actionCodes);
        SearchUtil.fillFilterEq(filters, IS_DELETED, "0");
        searchQuery.setFilters(filters);
        User user = new User(tenantId, "-10000", "", "");
        QueryResult<IObjectData> queryResult = null;
        IActionContext actionContext = ActionContextExt.of(user)
                .disableDeepQuote()
                .skipRelevantTeam()
                .getContext();
        actionContext.setDoCalculate(false);
        try {
            queryResult = metaDataFindService.findBySearchQuery(actionContext,
                    OBJECT_FOLLOW_DEAL_SETTING_OBJ, searchQuery);
        } catch (Exception e) {
            log.error("find ObjectFollowDealSettingObj error tenantId:{},", tenantId, e);
            throw e;
        }
        if (queryResult == null) {
            return null;
        }

        if (CollectionUtils.empty(queryResult.getData())) {
            return null;
        } else {
            return "1";
        }
    }

    @Override
    public Boolean hasSettingDeal(String tenantId, String sourceObjectApiName, String objectApiName) {
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(2);
        searchQuery.setOffset(0);
        searchQuery.setPermissionType(0);
        searchQuery.setNeedReturnCountNum(false);
        List filters = Lists.newLinkedList();
        SearchUtil.fillFilterEq(filters, ObjectFollowDealSettingObj.SOURCE_OBJECT_API_NAME, sourceObjectApiName);
        SearchUtil.fillFilterEq(filters, ObjectFollowDealSettingObj.OBJECT_API_NAME, objectApiName);
        SearchUtil.fillFilterEq(filters, ObjectFollowDealSettingObj.IS_ENABLE, true);
        SearchUtil.fillFilterEq(filters, ObjectFollowDealSettingObj.SETTING_TYPE, "2");
        SearchUtil.fillFilterEq(filters, ObjectFollowDealSettingObj.IS_VISIBLE, true);
        SearchUtil.fillFilterEq(filters, IS_DELETED, "0");

        searchQuery.setFilters(filters);
        User user = new User(tenantId, "-10000", "", "");
        QueryResult<IObjectData> queryResult = null;
        IActionContext actionContext = ActionContextExt.of(user)
                .disableDeepQuote()
                .skipRelevantTeam()
                .getContext();
        actionContext.setDoCalculate(false);
        try {
            queryResult = metaDataFindService.findBySearchQuery(actionContext,
                    OBJECT_FOLLOW_DEAL_SETTING_OBJ, searchQuery);
        } catch (Exception e) {
            log.error("find ObjectFollowDealSettingObj error tenantId:{},", tenantId, e);
            throw e;
        }
        if (queryResult == null || CollectionUtils.empty(queryResult.getData())) {
            return false;
        } else if (queryResult.getData().size() == 1) {
            Object isUserDefineSetting = queryResult.getData().get(0).get(ObjectFollowDealSettingObj.IS_USER_DEFINE_SETTING);
            // 未自定义
            if (isUserDefineSetting == null || !(boolean) isUserDefineSetting) {
                return true;
            }
            // 自定义成交状态，返回false
            if ((boolean) isUserDefineSetting) {
                return false;
            }
        } else if (queryResult.getData().size() > 1) {
            log.error("ObjectFollowDealSettingObj error:{},{},{}", tenantId, sourceObjectApiName, objectApiName);
        }
        return true;
    }


    @Override
    public List<ObjectReferenceFieldDescribe> objectReferenceFieldDescribe(String tenantId, String objectId,
                                                                           String describeApiName) {
        List<ObjectReferenceFieldDescribe> accountObjs = new ArrayList<>();
        try {
            IObjectDescribe objectDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, describeApiName);

            // 找出所有的关联类型
            List<ObjectReferenceFieldDescribe> referenceFiledList = objectDescribe.getFieldDescribes().stream()
                    .filter(x -> Objects.equals(x.getType(), "object_reference") && x.isActive())
                    .map(x -> (ObjectReferenceFieldDescribe) x).collect(Collectors.toList());

            // 从所有关联类型中找出关联的是客户的field
            accountObjs = referenceFiledList.stream().filter(x -> Objects.equals(x.getTargetApiName(), "AccountObj"))
                    .collect(Collectors.toList());
        } catch (MetadataServiceException e) {
            log.warn("find objectReferenceFieldDescribe throw exception {}", e.getMessage());
        }
        return accountObjs;
    }

    @Override
    public List<ObjectReferenceFieldDescribe> objectReferenceFieldDescribe(String tenantId, String objectId,
                                                                           String describeApiName, String targetApiName) {
        List<ObjectReferenceFieldDescribe> accountObjs = new ArrayList<>();
        try {
            IObjectDescribe objectDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, describeApiName);

            // 找出所有的关联类型
            List<ObjectReferenceFieldDescribe> referenceFiledList = objectDescribe.getFieldDescribes().stream()
                    .filter(x -> Objects.equals(x.getType(), "object_reference") && x.isActive())
                    .map(x -> (ObjectReferenceFieldDescribe) x).collect(Collectors.toList());

            // 从所有关联类型中找出关联的是客户的field
            accountObjs = referenceFiledList.stream().filter(x -> Objects.equals(x.getTargetApiName(), "AccountObj"))
                    .collect(Collectors.toList());
        } catch (MetadataServiceException e) {
            log.error("find objectReferenceFieldDescribe throw exception {}", e);
        }
        return accountObjs;
    }

    private User buildUser(String tenantId) {
        return buildUser(tenantId, "-10000", "", "");
    }

    public User buildUserForUpdate(String tenantId) {
        return buildUser(tenantId, null, "", "");
    }

    public User buildUserForUpdate(String tenantId, String userId) {
        return buildUser(tenantId, userId, "", "");
    }

    private User buildUser(String tenantId, String userId, String outUserId, String outTenantId) {
        return new User(tenantId, userId, outUserId, outTenantId);
    }

    private ActionContext getDefaultActionContext(User user, List<UpdateFollowDealModel.Content> contents) {
        ActionContext actionContext = CommonUtils.buildContextAllInvalid(user);
        if (CollectionUtils.notEmpty(contents)) {
            Optional<UpdateFollowDealModel.Content> optional = contents.stream().filter(x -> x.getEventId() != null).findFirst();
            if (optional.isPresent()) {
                UpdateFollowDealModel.Content content = optional.get();
                actionContext.put("eventId", content.getEventId());
            }
        }
        return actionContext;
    }

    private ActionContext getDefaultActionContext(User user, String eventId) {
        ActionContext actionContext = CommonUtils.buildContextAllInvalid(user);
        actionContext.put("eventId", eventId);
        return actionContext;
    }

    @Override
    public void sendRecalculateTask(String tenantId, List<UpdateFollowDealModel.Content> contents) {
        // 发送对该客户重算到期时间
        for (UpdateFollowDealModel.Content content : contents) {
            if (StringUtils.isBlank(content.getObjectId())) {
                continue;
            }
            log.info("sendRecalculateTask tenantId:{},{}", tenantId, content);
            RecalculateMessage message = RecalculateMessage.builder()
                    .actionCode(FOLLOW)
                    .objectApiName(content.getApiName())
                    .objectId(content.getObjectId())
                    .tenantId(tenantId)
                    .build();
            if (content.getRecyclingActionCode() == null) {
                recyclingRuleProducer.sendRecalculate(message, tenantId);
            } else {
                message.setActionCode(content.getRecyclingActionCode().getActionCode());
                recyclingRuleProducer.sendRecalculate(message, tenantId);
            }
        }
    }

    @Override
    public void sendRecalculateTask(String tenantId, String apiName, String objectId) {
        if (!Utils.ACCOUNT_API_NAME.equals(apiName) && !Utils.LEADS_API_NAME.equals(apiName)) {
            return;
        }
        log.info("sendRecalculateTask :{},{},{}", tenantId, apiName, objectId);
        RecalculateMessage message = RecalculateMessage.builder()
                .actionCode(FOLLOW)
                .objectApiName(apiName)
                .objectId(objectId)
                .tenantId(tenantId)
                .build();
        recyclingRuleProducer.sendRecalculate(message, tenantId);
    }

    @Override
    public void updateLeadsStatus(String tenantId, String leadsId, String lastFollower, String lastFollowTime) {
        try {

            List<IObjectData> datas = new ArrayList<>();
            IObjectData data = new ObjectData();
            data.setId(leadsId);
            data.setDescribeApiName(ApiNameEnum.LEADS_OBJ.getApiName());
            data.setTenantId(tenantId);
            List<String> updateFields = new ArrayList<>();
            if (StringUtils.isNotBlank(lastFollowTime)) {
                data.set(LAST_FOLLOW_TIME, lastFollowTime);
                updateFields.add(LAST_FOLLOW_TIME);
            }
            if (StringUtils.isNotBlank(lastFollower)) {
                data.set(LAST_FOLLOWER, Lists.newArrayList(lastFollower));
                updateFields.add(LAST_FOLLOWER);
            }

            data.set(BIZ_STATUS, PROCESSED);
            updateFields.add(BIZ_STATUS);

            data.set(IS_OVERTIME, false);
            updateFields.add(IS_OVERTIME);

            data.set(LEADS_STATUS, 4);
            updateFields.add(LEADS_STATUS);


            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            searchTemplateQuery.setLimit(100);
            searchTemplateQuery.setOffset(0);
            List filters = Lists.newLinkedList();
            SearchUtil.fillFilterEq(filters, BIZ_STATUS, UN_PROCESSED);
            searchTemplateQuery.setFilters(filters);

            log.info("update leads status with condition {}", data);
            datas.add(data);
            objectDataPgService.batchUpdateIgnoreOther(datas, updateFields, searchTemplateQuery, CommonUtils.buildContextAllInvalid(buildUserForUpdate(tenantId)));
        } catch (MetadataServiceException e) {
            log.error("MetadataServiceException ", e);
        }
    }

    public void updateLeadsOverTime(String tenantId, String leadsId) {
        List<IObjectData> datas = new ArrayList<>();
        IObjectData data = new ObjectData();
        data.setId(leadsId);
        data.setDescribeApiName(ApiNameEnum.LEADS_OBJ.getApiName());
        data.setTenantId(tenantId);

        List<String> updateFields = new ArrayList<>();

        data.set(IS_OVERTIME, false);
        updateFields.add(IS_OVERTIME);

        log.info("update leads status with condition {}", data);
        datas.add(data);
        try {
            objectDataPgService.batchUpdateIgnoreOther(datas, updateFields, CommonUtils.buildContextAllInvalid(buildUserForUpdate(tenantId)));
        } catch (MetadataServiceException e) {
            log.error("MetadataServiceException ", e);
        }
    }


    private void updateBizStatus(List<IObjectData> datas) {
        String tenantId = datas.get(0).getTenantId();
        List<String> ids = datas.stream().map(IObjectData::getId).collect(Collectors.toList());
        List<String> updateFieldList = Lists.newArrayList();
        List<IObjectData> unProcessedDatas;
        updateFieldList.add(LAST_FOLLOW_TIME);
        updateFieldList.add(LAST_FOLLOWER);

        try {
            List<IObjectData> byIds = objectDataPgService.findByIds(ids, tenantId, Utils.LEADS_API_NAME, CommonUtils.buildContextAllInvalid(buildUser(tenantId)));
            List<String> unProcessedIds = byIds.stream().filter(x -> UN_PROCESSED.equals(x.get(BIZ_STATUS))).map(IObjectData::getId).collect(Collectors.toList());
            if (CollectionUtils.notEmpty(unProcessedIds)) {
                unProcessedDatas = datas.stream().filter(x -> unProcessedIds.contains(x.getId())).collect(Collectors.toList());
                datas = datas.stream().filter(x -> !unProcessedIds.contains(x.getId())).collect(Collectors.toList());
                updateFieldList.add(BIZ_STATUS);
                for (IObjectData data : unProcessedDatas) {
                    data.set(BIZ_STATUS, PROCESSED);
                }
                objectDataPgService.batchUpdateIgnoreOther(unProcessedDatas, updateFieldList, CommonUtils.buildContextAllInvalid(buildUserForUpdate(tenantId)));
            }

        } catch (MetadataServiceException e) {
            log.error("updateBizStatus {}", e);
        }
    }

    @Override
    public void updateFields(FollowDataEvent event) {
        String objectApiName = event.getObjectApiName();
        IObjectData data = new ObjectData();
        data.setTenantId(event.getTenantId());
        data.setDescribeApiName(objectApiName);
        data.setId(event.getDataId());
        Set<String> updateFieldList = new HashSet<>();

        Long lastFollowTime = event.getLastFollowTime();
        if (lastFollowTime != null) {
            if (LEADS_OBJ.equals(objectApiName)) {
                data.set(LAST_FOLLOW_TIME, lastFollowTime);
                updateFieldList.add(LAST_FOLLOW_TIME);
            } else {
                data.set(LAST_FOLLOWED_TIME, lastFollowTime);
                updateFieldList.add(LAST_FOLLOWED_TIME);
            }
        }
        if (event.getLastFollowerId() != null) {
            data.set(LAST_FOLLOWER, Lists.newArrayList(event.getLastFollowerId()));
            updateFieldList.add(LAST_FOLLOWER);
        }
        if (event.getDealStatus() != null) {
            data.set(DEAL_STATUS, event.getDealStatus());
            updateFieldList.add(DEAL_STATUS);
        }
        if (event.getLastDealClosedTime() != null) {
            data.set(LAST_DEAL_CLOSED_TIME, event.getLastDealClosedTime());
            updateFieldList.add(LAST_DEAL_CLOSED_TIME);
        }
        if (event.getOverTime() != null) {
            data.set(IS_OVERTIME, event.getOverTime());
            updateFieldList.add(IS_OVERTIME);
        }
        if (event.getLastModifiedBy() != null) {
            data.setLastModifiedBy(event.getLastModifiedBy());
            updateFieldList.add(DBRecord.LAST_MODIFIED_BY);
        }
        try {
            log.info("dispatch update :{},updateFieldList:{}", data, updateFieldList);
            if (CollectionUtils.notEmpty(updateFieldList)) {
                objectDataPgService.batchUpdateIgnoreOther(Lists.newArrayList(data), Lists.newArrayList(updateFieldList),
                        getDefaultActionContext(buildUserForUpdate(event.getTenantId()), event.getEventId()));
                // 重算到期时间
                log.info("dispatch sendRecalculateTask:{},updateFieldList:{}", data, updateFieldList);
                sendRecalculateTask(event.getTenantId(), objectApiName, event.getDataId());
            }
        } catch (MetadataServiceException e) {
            log.error("MetadataServiceException ", e);
        }
    }

}
