package com.facishare.crm.follow.service;

import com.fxiaoke.helper.StringHelper;
import com.fxiaoke.notifier.support.NotifierClient;
import com.github.jedis.support.JedisCmd;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.type.TypeReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/7/29 11:20
 * @description:
 */
@Slf4j
@Component
public class DealCacheService {

    @Autowired
    private CommonService commonService;

    @Autowired
    protected JedisCmd SFAJedisCmd;

    private LoadingCache<String, DealCacheService.DealSettingCache> localCache;

    public static final String DEAL_SETTING_RULE_CHANGE = "deal-setting-rule-change";

    private static final String REDIS_KEY_PREFIX = "sfa-deal-setting-rule:";

    // 在类的开头添加这个字段
    private final ObjectMapper objectMapper = new ObjectMapper();


    @PostConstruct
    public void init() {
        localCache = CacheBuilder.newBuilder().maximumSize(1000).expireAfterWrite(30, TimeUnit.MINUTES).build(
                new CacheLoader<String, DealCacheService.DealSettingCache>() {
                    @NotNull
                    @Override
                    public DealCacheService.DealSettingCache load(@NotNull String tenantId) {
                        return loadFromRedisOrDb(tenantId);
                    }
                }
        );
        subscribeInvalidMessage();
    }


    private DealCacheService.DealSettingCache loadFromRedisOrDb(String key) {
        String redisKey = REDIS_KEY_PREFIX + key;
        String cacheJson = SFAJedisCmd.get(redisKey);
        if (cacheJson != null) {
            log.info("dealSetting cache from redis ei:{}", key);
            try {
                return deserializeFromJson(cacheJson);
            } catch (Exception e) {
                log.error("值班的你请忽略，Error deserializing dealSetting cache from JSON", e);
                SFAJedisCmd.del(redisKey);
            }
        }
        log.info("dealSetting cache from db ei:{}", key);
        DealCacheService.DealSettingCache settingCache = loadFromDb(key);
        String serializedCache = serializeToJson(settingCache);
        SFAJedisCmd.set(redisKey, serializedCache);
        return settingCache;
    }

    private DealCacheService.DealSettingCache loadFromDb(String tenantId) {
        Map<String, Boolean> dealSetting = commonService.getFollowDealSetting(tenantId);
        log.info("dealSetting cache loaded from db: {},{}", tenantId, dealSetting);
        dealSetting = (dealSetting != null) ? dealSetting : new HashMap<>();
        DealCacheService.DealSettingCache settingCache = new DealCacheService.DealSettingCache();
        settingCache.setDealSetting(dealSetting);
        return settingCache;
    }

    private DealCacheService.DealSettingCache deserializeFromJson(String json) throws IOException {
        return objectMapper.readValue(json, new TypeReference<DealSettingCache>() {});
    }

    private String serializeToJson(DealCacheService.DealSettingCache settingCache) {
        try {
            return objectMapper.writeValueAsString(settingCache);
        } catch (Exception e) {
            log.error("Error serializing dealSetting cache to JSON", e);
            return "{}"; // 返回空 JSON 对象或者抛出自定义异常
        }
    }



    public DealCacheService.DealSettingCache getCache(String tenantId) {
        try {
            return localCache.get(tenantId);
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     *  获取跟进配置
     * @param tenantId
     * @param sourceApiName
     * @return
     */
    public Boolean getDealSetting(String tenantId, String sourceApiName) {
        Map<String, Boolean> dealSetting = getDealSetting(tenantId);
        if (dealSetting == null || dealSetting.get(sourceApiName) == null) {
            return false;
        }
        return dealSetting.get(sourceApiName);
    }

    public Map<String, Boolean> getDealSetting(String tenantId) {
        DealCacheService.DealSettingCache settingCache = getCache(tenantId);
        log.info("begin get dealSetting cache, tenantId:{}, settingCache:{}", tenantId, settingCache);
        return settingCache.getDealSetting();
    }

    public boolean isSkipWithoutRuleByTenantId(String tenantId) {
        return DealCacheService.DealSettingCache.EMPTY.equals(getCache(tenantId));
    }

    @SneakyThrows
    public void invalid(String tenantId) {
        log.info("publish invalidate available dealSetting cache, tenantId:{}", tenantId);
        localCache.invalidate(tenantId);
        NotifierClient.send(DEAL_SETTING_RULE_CHANGE, tenantId, null);
    }

    private void subscribeInvalidMessage() {
        NotifierClient.register(DEAL_SETTING_RULE_CHANGE, message -> {
            String tenantId = message.getContent();
            if (StringHelper.isNotNullOrBlank(tenantId)) {
                log.info("fast-notifier: invalidate dealSetting cache, tenantId:{}", tenantId);
                localCache.invalidate(tenantId);
                SFAJedisCmd.del(REDIS_KEY_PREFIX + tenantId);
                localCache.invalidate(tenantId);
            }
        });
    }


    @Data
    public static class DealSettingCache {

        private boolean empty;

        public static final DealCacheService.DealSettingCache EMPTY = new DealCacheService.DealSettingCache();

        public static final DealCacheService.DealSettingCache NOT_EMPTY = new DealCacheService.DealSettingCache(true);

        Map<String, Boolean> dealSetting;

        public DealSettingCache() {
        }

        public DealSettingCache(boolean empty) {
            this.empty = empty;
        }
    }
}
