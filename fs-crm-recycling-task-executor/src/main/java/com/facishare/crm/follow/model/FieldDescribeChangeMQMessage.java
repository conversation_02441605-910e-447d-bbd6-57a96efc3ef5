package com.facishare.crm.follow.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class FieldDescribeChangeMQMessage implements Serializable {
    private String tenantId;
    private String op;
    private String name;
    private List<Content> body;

    @Data
    public static class Content {
        @JSONField(name = "object_describe_api_name")
        private String objectApiName;
        @JSONField(name = "fields")
        private List<FieldDescribe> fields;
    }

    @Data
    public static class FieldDescribe{
        @JSONField(name = "target_describe_api_name")
        private String referenceObjectApiName;
        @JSONField(name = "field_type")
        private String fieldType;
        @JSONField(name = "field_api_name")
        private String apiName;
    }
}
