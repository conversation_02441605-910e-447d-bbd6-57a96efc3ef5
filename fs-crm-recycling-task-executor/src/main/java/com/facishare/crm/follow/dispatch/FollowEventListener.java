package com.facishare.crm.follow.dispatch;

import com.facishare.crm.follow.service.CustomerFollowService;
import com.fxiaoke.dispatcher.processor.EventListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * @Description 聚合消息处理类
 * <AUTHOR>
 * @Date 2021/8/20 17:52
 */

@Slf4j
public class FollowEventListener implements EventListener<FollowDataEvent> {

    @Autowired
    private CustomerFollowService customerFollowService;

    @Override
    public void listen(List<FollowDataEvent> list) {
        log.info("dispatch FollowEventListener listen: list.size:{},{}", list.size(), list);
        list.forEach(this::doConsume);
    }


    private void doConsume(FollowDataEvent event) {
        customerFollowService.updateFields(event);
        event.markDone();
        log.info("dispatch markDone:{}", event);
    }
}
