package com.facishare.crm.recycling.task.executor.util.sql;

import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.impl.search.CommonSqlOperator;
import com.facishare.paas.metadata.impl.search.WhereParam;
import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

import static com.facishare.paas.metadata.impl.search.CommonSqlOperator.*;

public class WhereSql {

	@Getter
	private final List<WhereParam> whereList = new ArrayList<>();
	@Getter
	private String tenantId;
	
	private WhereSql() {}

	public static WhereSql of(String tenantId) {
		WhereSql whereSql = new WhereSql();
		whereSql.tenantId = tenantId;
		whereSql.whereList.add(buildWhereParam(Tenantable.TENANT_ID, EQ, Lists.newArrayList(tenantId)));
		return whereSql;
	}

	public WhereSql andObjectApiNameEq(String apiName) {
		andEq("object_api_name", apiName);
		return this;
	}

	public WhereSql andNotDelete() {
		andEq("is_deleted", 0);
		return this;
	}

	public WhereSql andIdEq(String id) {
		andEq("id", id);
		return this;
	}


	public WhereSql andIdIn(List<String> idList) {
		andIn("id", idList);
		return this;
	}

	public WhereSql andEq(String column, Object value) {
		whereList.add(buildWhereParam(column, EQ, Lists.newArrayList(value)));
		return this;
	}

	public WhereSql andNeq(String column, Object value) {
		whereList.add(buildWhereParam(column, NEQ, Lists.newArrayList(value)));
		return this;
	}

	@SuppressWarnings("unchecked")
	public <O> WhereSql andIn(String column, List<O> valueList) {
		whereList.add(buildWhereParam(column, IN, (List<Object>) valueList));
		return this;
	}

	@SuppressWarnings("unchecked")
	public <O> WhereSql andNin(String column, List<O> valueList) {
		whereList.add(buildWhereParam(column, NIN, (List<Object>) valueList));
		return this;
	}

	public WhereSql andIsNull(String column) {
		whereList.add(buildWhereParam(column, IS, null));
		return this;
	}

	public WhereSql andNotNull(String column) {
		whereList.add(buildWhereParam(column, ISN, null));
		return this;
	}

	private static WhereParam buildWhereParam(String column, CommonSqlOperator op, List<Object> valueList) {
		WhereParam whereParam = new WhereParam();
		whereParam.setColumn(column);
		whereParam.setOperator(op);
		whereParam.setValue(valueList);
		return whereParam;
	}

}
