package com.facishare.crm.recycling.task.executor.consumer;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.lto.relationship.models.RelationshipModels;
import com.facishare.crm.sfa.lto.relationship.service.RelationshiService;
import com.facishare.crm.sfa.lto.relationship.EnumUtil;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.nio.charset.Charset;

/**
 * <AUTHOR> lik
 * @date : 2022/7/14 10:54
 */
@Slf4j
@Component
public class RelationshipListener implements ApplicationListener<ContextRefreshedEvent> {

    private AutoConfMQPushConsumer consumer;
    @Autowired
    private RelationshiService relationshiService;

    @PostConstruct
    public void init() {
        consumer = new AutoConfMQPushConsumer("sfa-recalculate-consumer", "sfa-contact-relationshi",(MessageListenerConcurrently) (msgs, context) -> {
            if (!msgs.isEmpty()) {
                for(MessageExt msg: msgs) {
                    consumeMessage(msg);
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        });
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (consumer != null && event.getApplicationContext().getParent() == null) {
            consumer.start();
        }
    }
    @PreDestroy
    public void close() {
        consumer.close();
    }

    private void consumeMessage(MessageExt messageExt) {
        String body = new String(messageExt.getBody(), Charset.forName("UTF-8"));
        try {
            RelationshipModels.TaskArg arg = JSON.parseObject(body, RelationshipModels.TaskArg.class);
            //判断是否开启联系人与成员关系功能
            if(!relationshiService.getConfigValueByKey(arg.getTenantId())){
                return;
            }
            log.info("dealContactRelationshi-OperationType tenantId:{},apiName:{},dataId:{},OperationType:{},msgId:{},reconsumeTimes:{}",arg.getTenantId()
                    ,arg.getObjectApiName(),arg.getDataIds(), EnumUtil.OperationType.operationTypeOf(arg.getOperationType()),messageExt.getMsgId(),messageExt.getReconsumeTimes());
            if(messageExt.getReconsumeTimes()>2){
                return;
            }
            switch (messageExt.getTags()) {
                case "deal_member_relationship_tag":
                    relationshiService.dealDataRelationshipByDataId(arg);
                    break;
                case "address_book_relationship_tag":
                    relationshiService.dealAddressBookMsg(arg);
                    break;
                case "history_relationship_tags":
                    relationshiService.dealHistoryDataMsg(arg);
                    break;
            }
        } catch (Exception e) {
            log.error("ContactMemberRelationshipListener consumeMessage msgId:{},e:",messageExt.getMsgId(),e);
            throw new RuntimeException(e);
        }
    }
}
