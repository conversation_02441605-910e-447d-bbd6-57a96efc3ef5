package com.facishare.crm.recycling.task.executor.proxy;

import com.facishare.crm.recycling.task.executor.model.HighSeasArgs;
import com.facishare.crm.recycling.task.executor.model.HighSeasResult;
import com.facishare.crm.recycling.task.executor.model.RecyclingRuleArg;
import com.facishare.crm.recycling.task.executor.model.RecyclingRuleResult;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

/**
 *
 */
@RestResource(value = "CRM_SFA", desc = "fs-crm-recycling-task call ", contentType = "application/json")
public interface RecyclingRuleProxy {

    @POST(value = "/crm/basicsetting/getrecyclingrulebydataid", desc = "查询回收规则列表")
    RecyclingRuleResult getRecyclingRuleByDataid(@HeaderMap Map<String, String> headers, @Body RecyclingRuleArg body);

    @POST(value = "/crm/highseas/batchsavehighseasclaimlog", desc = "批量保存公海领取日志")
    HighSeasResult.Result batchsavehighseasclaimlog(@HeaderMap Map<String, String> headers, @Body HighSeasArgs.BatchSaveHighSeasClaimLogArg body);

    @POST(value = "/crm/highseas/processpoolfieldpermission", desc = "处理公海、线索池非公开字段")
    HighSeasResult.Result processpoolfieldpermission(@HeaderMap Map<String, String> headers, @Body HighSeasArgs.ProcessPoolFieldPermissionArg body);

}
