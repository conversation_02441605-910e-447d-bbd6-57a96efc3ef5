package com.facishare.crm.recycling.task.executor.service.impl.forecast.task;

import com.facishare.crm.recycling.task.executor.enums.forecast.ForecastRuleEnums;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastPeriod;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastRule;

import java.time.*;
import java.time.temporal.IsoFields;
import java.util.ArrayList;
import java.util.List;
import java.util.RandomAccess;

public final class ForecastPeriodMatcher {

    private ForecastPeriodMatcher() {

    }

    private static final int MAX_SIZE = 24;

    public static List<ForecastPeriod> initPeriod(ForecastRule rule) {
        List<ForecastPeriod> periods = generatePeriod(rule);
        rule.setForecastPeriods(periods);
        return periods;
    }

    public static List<ForecastPeriod> generatePeriod(ForecastRule rule) {
        return generatePeriod(rule.getForecastStartDate(), rule.getForecastEndDate(), ForecastRuleEnums.DateSplitType.valueOf(rule.getForecastDateSplitType()).getMonthSpan());
    }

    static List<ForecastPeriod> generatePeriod(long startTimestamp, long endTimestamp, int monthSpan) {
        if (startTimestamp >= endTimestamp) {
            return new ArrayList<>();
        }
        ZoneId zone = ZoneId.systemDefault();
        ZonedDateTime startDateTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(startTimestamp), zone);
        ZonedDateTime endDateTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(endTimestamp), zone);
        int allMonthSpan = Period.between(startDateTime.toLocalDate(), endDateTime.toLocalDate()).getMonths();
        ArrayList<ForecastPeriod> result = new ArrayList<>(Math.min(allMonthSpan / monthSpan, 4));
        LocalTime tail = LocalTime.of(23, 59, 59, 999999999); // 补齐纳秒，保证this.right和next.left值只差1
        while (startTimestamp < endTimestamp && result.size() < MAX_SIZE) {
            ZonedDateTime curDateTime = startDateTime.plusMonths(monthSpan).minusDays(1).with(tail);
            long curTimestamp = curDateTime.toInstant().toEpochMilli();
            if (curTimestamp > endTimestamp) {
                curTimestamp = endTimestamp;
            }
            ForecastPeriod period = new ForecastPeriod(curDateTime.getYear(), curDateTime.getMonthValue(), curDateTime.get(IsoFields.QUARTER_OF_YEAR), startTimestamp, curTimestamp);
            result.add(period);
            startDateTime = curDateTime.plusDays(1).withHour(0).withMinute(0).withSecond(0);
            startTimestamp = startDateTime.toInstant().toEpochMilli();
        }
        return result;
    }

    // 因为period.left有序，所以可用二分法优化，要求periods必须是RandomAccess的实现类
    public static ForecastPeriod matchPeriod(Long target, List<ForecastPeriod> periods) {
        if (target == null || periods.isEmpty() || !(periods instanceof RandomAccess)) {
            return null;
        }
        int left = 0;
        int right = periods.size() - 1;
        if (periods.get(left).getLeft().compareTo(target) > 0 || periods.get(right).getRight().compareTo(target) < 0) {
            return null;
        }
        return matchPeriod(target, periods, left, right);
    }

    private static ForecastPeriod matchPeriod(Long target, List<ForecastPeriod> periods, int left, int right) {
        if (left > right) {
            return null;
        }
        int mid = left + ((right - left) >> 1);
        ForecastPeriod midPeriod = periods.get(mid);
        if (midPeriod.getRight() > target && target > midPeriod.getLeft()) {
            return midPeriod;
        } else if (midPeriod.getLeft() > target) {
            return matchPeriod(target, periods, left, mid - 1);
        } else {
            return matchPeriod(target, periods, mid + 1, right);
        }
    }
}
