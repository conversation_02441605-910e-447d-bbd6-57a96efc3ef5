package com.facishare.crm.recycling.task.executor.model;

import io.protostuff.Tag;
import lombok.Data;

import java.util.List;

public interface PartnerModel {
    @Data
    class ChangeOuterOrganizationDepartmentEvent extends ProtoBase {
        public static final int CHANGE_BASIC_FLAG = 201; //部门基本信息更改flag
        public static final int CHANGE_LEVEL_FLAG = 202; //部门层级更改flag
        public static final int CREATE_FLAG = 203; //部门创建flag
        public static final int REMOVE_FLAG = 204; //部门删除flag
        private static final long serialVersionUID = -1773818648783843695L;

        @Tag(1)
        private Integer enterpriseId; //上游企业的Id
        @Tag(2)
        private String departmentId; //部门Id, 对应外部企业Id
    }

    /**
     *  人员部门关系变化事件
     */
    @Data
    class ChangeOuterOrganizationUserDeptEvent extends ProtoBase {
        public static final int ADD_FLAG = 102;
        public static final int REMOVE_FLAG = 103;
        public static final int CHANGE_BELONG_DEPARTMENT_FLAG = 104;
        private static final long serialVersionUID = 379882222846984770L;

        @Tag(1)
        private Long userId; //修改的外部员工id
        @Tag(2)
        private Integer enterpriseId; //外部员工对接的上游企业Id
        @Tag(3)
        private List<String> oldDepartmentIds; //原部门Id列表，103、104 时有值
        @Tag(4)
        private List<String> newDepartmentIds; //原部门Id列表，102、104 时有值
    }

    /**
     * 人员基础信息变化
     */
    @Data
    class ChangeOuterOrganizationUserEvent extends ProtoBase {
        public static final int FLAG = 101;
        private static final long serialVersionUID = 9013812270253722531L;
        @Tag(1)
        private Long userId; //修改后的员工对外名片
        @Tag(2)
        private List<Integer> upstreamEnterpriseIds; //外部员工对接的上游企业列表
    }
}
