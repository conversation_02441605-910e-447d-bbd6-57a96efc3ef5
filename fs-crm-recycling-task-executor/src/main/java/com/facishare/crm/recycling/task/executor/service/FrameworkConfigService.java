package com.facishare.crm.recycling.task.executor.service;

import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.model.User;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.facishare.crm.recycling.task.executor.util.ConstantUtils.ADMIN;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/3/1 16:24
 */

@Component
public class FrameworkConfigService {

    @Autowired
    private ConfigService configService;

    //联系人负责人规则设置 1   客户进行退回、收回时将会同步清空联系人负责人
    public static final String CONTACT_OWNER_RULE_SETTING = "contact_owner_rule_setting";



    public String getConfigValue(String tenantId, String configKey) {
        User user = new User(tenantId, ADMIN);
        String queryRst = configService.findTenantConfig(user, configKey);
        if (StringUtils.isBlank(queryRst)) {
            return "1";
        } else {
            return queryRst;
        }
    }




}
