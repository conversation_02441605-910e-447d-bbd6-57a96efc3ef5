package com.facishare.crm.recycling.task.executor.proxy;

import com.facishare.crm.recycling.task.executor.model.ApprovalInstanceModel;
import com.facishare.crm.recycling.task.executor.model.CancelInstanceModel;
import com.facishare.crm.recycling.task.executor.model.GetCurInstanceStateModel;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

@RestResource(value = "PAAS-FLOW", desc = "审批流服务", contentType = "application/json")
public interface ApprovalInitProxy {
    @POST(value = "fs-crm-workflow/approval/approvalInstance", desc = "查询审批流")
    ApprovalInstanceModel.Result approvalInstance(@Body ApprovalInstanceModel.Arg arg, @HeaderMap Map<String, String> headers);

    @POST(value = "fs-crm-workflow/approval/instance/getCurInstanceStateByObjectIds", desc = "查询当前审批流")
    GetCurInstanceStateModel.Result getCurInstanceStateByObjectIds(@Body GetCurInstanceStateModel.Arg arg, @HeaderMap Map<String, String> headers);


    @POST(value = "fs-crm-workflow/approval/instance/cancel", desc = "取消当前审批流")
    CancelInstanceModel.Result cancelInstance(@Body CancelInstanceModel.Arg arg, @HeaderMap Map<String, String> headers);
}