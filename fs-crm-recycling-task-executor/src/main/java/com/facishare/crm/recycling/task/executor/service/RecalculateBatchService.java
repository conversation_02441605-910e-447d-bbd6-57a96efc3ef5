package com.facishare.crm.recycling.task.executor.service;

import com.facishare.crm.recycling.task.executor.biz.CustomerBiz;
import com.facishare.crm.recycling.task.executor.biz.LeadsBiz;
import com.facishare.crm.recycling.task.executor.biz.RecyclingBiz;
import com.facishare.crm.recycling.task.executor.common.SfaRecyclingTaskRateLimiterService;
import com.facishare.crm.recycling.task.executor.model.RecyclingRuleInfoModel;
import com.facishare.crm.recycling.task.executor.model.RemindRuleModel;
import com.facishare.crm.recycling.task.executor.service.impl.AccountRecalculate;
import com.facishare.crm.recycling.task.executor.service.impl.LeadsRecalculate;
import com.facishare.crm.recycling.task.executor.util.ObjectDataUtils;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

import static com.facishare.crm.recycling.task.executor.util.ConstantUtils.*;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/3/17 17:57
 */

@Component
@Slf4j
public class RecalculateBatchService {


    @Autowired
    private CustomerBiz customerBiz;

    @Autowired
    private LeadsBiz leadsBiz;

    @Autowired
    private RecyclingBiz recyclingBiz;

    @Autowired
    private SfaRecyclingTaskRateLimiterService sfaRecyclingTaskRateLimiterService;

    @Autowired
    private AccountRecalculate accountRecalculate;

    @Autowired
    private LeadsRecalculate leadsRecalculate;


    public void newRecalculateByOwner(String tenantId, List<String> owners) {
        int limit = 100;
        int offset = 0;
        log.info("loopRecalculateByOwner begin tenantId:{},owners:{}", tenantId, owners);
        recalculateAccount(tenantId, owners, limit, offset);
        recalculateLeads(tenantId, owners, limit, offset);
    }
    private void recalculateAccount(String tenantId, List<String> owners, int limit, int offset) {
        HashMap<String, List<RecyclingRuleInfoModel>> recyclingRuleCache = new HashMap<>();
        HashMap<String, List<RemindRuleModel>> remindRuleCache = new HashMap<>();
        for (String owner : owners) {
            //  查询部门下员工走的规则
            String deptId = customerBiz.getDept(tenantId, owner);
            String dataId = deptId;
            List<RecyclingRuleInfoModel> recyclingRules;
            List<RemindRuleModel> remindRules;
            List<String> idLists = customerBiz.getAllIdsByOwnerId(tenantId, owner);
            log.info("recalculateAccount begin tenantId:{},owner:{},idSize:{}", tenantId, owner,idLists.size());
            List<IObjectData> objectDataList;
            do {

                objectDataList = customerBiz.getAllCustomerLimitByOwner(tenantId, owner, limit, offset);
                for (IObjectData objectData : objectDataList) {

                    // 公海数据
                    dataId = getDataId(deptId, objectData);

                    recyclingRules = getRecyclingRuleInfoModels(tenantId, recyclingRuleCache, dataId);

                    remindRules = getRemindRuleModels(tenantId, remindRuleCache, dataId);

                    if (CollectionUtils.isEmpty(recyclingRules) && CollectionUtils.isEmpty(remindRules)) {
                        continue;
                    }
                    accountRecalculate.recalculateNewCustomer(tenantId, objectData, recyclingRules, remindRules, null);
                    idLists.remove(objectData.getId());
                }
                offset = offset + objectDataList.size();
                log.info("do loopNewCalculateDepts tenantId:{},deptId:{},member:{},idList:{},offset:{}", tenantId, deptId, owner, idLists.size(), offset);
            } while (CollectionUtils.isNotEmpty(objectDataList) && CollectionUtils.isNotEmpty(idLists));
            log.info("loopRecalculateByOwner end, tenantId:{},deptId:{},owner:{},offset:{} ", tenantId, deptId, owner, offset);
            for (String id : idLists) {
                IObjectData objectData = customerBiz.getObjectById(tenantId, id, ACCOUNT_OBJ);
                dataId = getDataId(deptId, objectData);
                recyclingRules = getRecyclingRuleInfoModels(tenantId, recyclingRuleCache, dataId);
                remindRules = getRemindRuleModels(tenantId, remindRuleCache, dataId);
                sfaRecyclingTaskRateLimiterService.getCrmRecalculateDeptLimiter().acquire();
                accountRecalculate.recalculateNewCustomer(tenantId, objectData, recyclingRules, remindRules, null);
            }
            offset = 0;
        }

    }

    private List<RemindRuleModel> getRemindRuleModels(String tenantId, HashMap<String, List<RemindRuleModel>> remindRuleCache, String dataId) {
        List<RemindRuleModel> remindRules;
        if (remindRuleCache.containsKey(dataId)) {
            remindRules = remindRuleCache.get(dataId);
        } else {
            remindRules = recyclingBiz.getRemindRulesByDataId(tenantId, dataId);
            remindRuleCache.put(dataId, remindRules);
        }
        return remindRules;
    }

    private List<RecyclingRuleInfoModel> getRecyclingRuleInfoModels(String tenantId, HashMap<String, List<RecyclingRuleInfoModel>> recyclingRuleCache, String dataId) {
        List<RecyclingRuleInfoModel> recyclingRules;
        if (recyclingRuleCache.containsKey(dataId)) {
            recyclingRules = recyclingRuleCache.get(dataId);
        } else {
            recyclingRules = recyclingBiz.getRecyclingRule(tenantId, dataId, HIGH_SEAS_OBJ);
            recyclingRuleCache.put(dataId, recyclingRules);
        }
        return recyclingRules;
    }

    private String getDataId(String deptId, IObjectData objectData) {
        String dataId;
        if (ObjectDataUtils.isHighSeas(objectData)) {
            dataId = recyclingBiz.getDataId(objectData);
        } else {
            dataId = deptId;
        }
        return dataId;
    }


    private void recalculateLeads(String tenantId, List<String> owners, int limit, int offset) {
        HashMap<String, List<RecyclingRuleInfoModel>> recyclingRuleCache = new HashMap<>();
        for (String owner : owners) {
            //  查询部门下员工走的规则
            String dataId;
            List<RecyclingRuleInfoModel> recyclingRules;
            List<String> idLists = leadsBiz.getAllLeadsIdsByOwner(tenantId, owner);
            List<IObjectData> objectDataList;
            do {
                objectDataList = leadsBiz.getLeadsLimitByOwner(tenantId, owner, limit, offset);
                for (IObjectData objectData : objectDataList) {
                    dataId = recyclingBiz.getDataId(objectData);
                    if (recyclingRuleCache.containsKey(dataId)) {
                        recyclingRules = recyclingRuleCache.get(dataId);
                    } else {
                        recyclingRules = recyclingBiz.getRecyclingRule(tenantId, dataId, LEADS_POOL_OBJ);
                        recyclingRuleCache.put(dataId, recyclingRules);
                    }
                    if (CollectionUtils.isEmpty(recyclingRules)) {
                        continue;
                    }
                    leadsRecalculate.newRecalculateLeadsExtend(tenantId, objectData, recyclingRules, null);
                    idLists.remove(objectData.getId());
                }
                offset = offset + objectDataList.size();
                log.info("do LeadsObj loopCalByAttendanceRule tenantId:{},member:{},idList:{},offset:{}", tenantId, owner, idLists.size(), offset);
            } while (CollectionUtils.isNotEmpty(objectDataList) && CollectionUtils.isNotEmpty(idLists));
            log.info("LeadsObj loopCalByAttendanceRule end, tenantId:{},owner:{},offset:{} ", tenantId, owner, offset);

            for (String id : idLists) {
                IObjectData objectData = customerBiz.getObjectById(tenantId, id, LEADS_OBJ);
                dataId = recyclingBiz.getDataId(objectData);
                recyclingRules = getRecyclingRuleInfoModels(tenantId, recyclingRuleCache, dataId);
                sfaRecyclingTaskRateLimiterService.getCrmRecalculateDeptLimiter().acquire();
                leadsRecalculate.newRecalculateLeadsExtend(tenantId, objectData, recyclingRules, null);
            }
            offset = 0;
        }
    }

    //private void recalculateOverTimeFollow(String tenantId, List<String> owners, int limit, int offset) {
    //    HashMap<String, IObjectData> LeadsPools = new HashMap<>();
    //    for (String owner : owners) {
    //        //  查询部门下员工走的规则
    //        String dataId;
    //        List<RecyclingRuleInfoModel> recyclingRules;
    //        List<String> idLists = leadsBiz.getAllLeadsIdsByOwner(tenantId, owner);
    //        List<IObjectData> objectDataList;
    //        do {
    //            objectDataList = leadsBiz.getLeadsLimitByOwner(tenantId, owner, limit, offset);
    //            for (IObjectData objectData : objectDataList) {
    //
    //                IObjectData leadsPool = customerBiz.getObjectById(tenantId, objectData.get(LEADS_POOL_ID, String.class), LEADS_POOL_OBJ);
    //
    //                dataId = recyclingBiz.getDataId(objectData);
    //
    //                if (CollectionUtils.isEmpty(recyclingRules)) {
    //                    continue;
    //                }
    //                leadsRecalculate.newRecalculateLeadsExtend(tenantId, objectData, recyclingRules, null);
    //                idLists.remove(objectData.getId());
    //            }
    //            offset = offset + objectDataList.size();
    //            log.info("do LeadsObj loopCalByAttendanceRule tenantId:{},member:{},idList:{},offset:{}", tenantId, owner, idLists.size(), offset);
    //        } while (CollectionUtils.isNotEmpty(objectDataList) && CollectionUtils.isNotEmpty(idLists));
    //        log.info("LeadsObj loopCalByAttendanceRule end, tenantId:{},owner:{},offset:{} ", tenantId, owner, offset);
    //
    //        for (String id : idLists) {
    //            IObjectData objectData = customerBiz.getObjectById(tenantId, id, LEADS_OBJ);
    //            dataId = recyclingBiz.getDataId(objectData);
    //            recyclingRules = getRecyclingRuleInfoModels(tenantId, recyclingRuleCache, dataId);
    //            rateLimiterService.getCrmRecalculateDeptLimiter().acquire();
    //            leadsRecalculate.newRecalculateLeadsExtend(tenantId, objectData, recyclingRules, null);
    //        }
    //        offset = 0;
    //    }
    //}



}
