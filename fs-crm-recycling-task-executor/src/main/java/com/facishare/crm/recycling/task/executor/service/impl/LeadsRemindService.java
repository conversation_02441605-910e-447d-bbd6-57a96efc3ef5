package com.facishare.crm.recycling.task.executor.service.impl;

import com.facishare.crm.recycling.task.executor.biz.CustomerBiz;
import com.facishare.crm.recycling.task.executor.biz.RemindBiz;
import com.facishare.crm.recycling.task.executor.enums.ApiNameEnum;
import com.facishare.crm.recycling.task.executor.enums.RemindRecordTypeEnum;
import com.facishare.crm.recycling.task.executor.model.RemindMessage;
import com.facishare.crm.recycling.task.executor.model.RuleTypeRemindTime;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.facishare.crm.recycling.task.executor.util.ConstantUtils.EXPIRE_TIME;
import static com.facishare.crm.recycling.task.executor.util.I18NKey.*;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-04-29 15:01
 */
@Slf4j
@Component
public class LeadsRemindService extends AbstractRemindService{


    @Autowired
    private RemindBiz remindBiz;

    @Autowired
    private CustomerBiz customerBiz;

    @Override
    public void execute(RemindMessage remindMessage) {
        super.execute(remindMessage);

        IObjectData leadsData = customerBiz.getObjectById(remindMessage.getTenantId(), remindMessage.getObjectId(), ApiNameEnum.LEADS_OBJ.getApiName());
        if (leadsData == null){
            log.warn("leadsData is null :{}",remindMessage);
            return;
        }

        if (StringUtils.isEmpty(leadsData.get(EXPIRE_TIME,String.class))){
            return;
        }
        Date remindTime = remindMessage.getRemindTime();

        List<RuleTypeRemindTime> remindTimes = remindMessage.getRuleTypeRemindTimes().stream().filter(
                x -> x.getRemindTime().compareTo(remindTime.getTime()) == 0).collect(Collectors.toList());

        String content = "注意，您的 "+leadsData.getName() +" 由于长时间未跟进/未转换， 将在 ";
        Integer days = remindTimes.get(0).getDays();
        String contentKey = SFA_REMIND_DAY_NEW_RECORD;
        List<String> contentParams = Lists.newArrayList(leadsData.getName(),String.valueOf(days/24));
        if ("true".equals(remindMessage.getNewVersion())){
            if (days%24 ==0){
                content += days/24+" 天 后被自动收回，请及时处理";
            }else {
                contentKey = SFA_REMIND_DAY_TIME_NEW_RECORD;
                contentParams = Lists.newArrayList(leadsData.getName(),String.valueOf(days/24),String.valueOf(days%24));
                content += days/24+" 天 "+ days%24 + " 小时后被自动收回，请及时处理";
            }
        }else{
            contentParams =  Lists.newArrayList(leadsData.getName(),remindTimes.get(0).getDays().toString());
            content += remindTimes.get(0).getDays()+"天后被自动收回，请及时处理";
        }
        log.info("objectId:{},tenantId:{},{}",leadsData.getId(),leadsData.getTenantId(),content);
        //发送新crm通知
        remindBiz.sendRemind(leadsData, remindMessage.getTenantId(),content,RemindRecordTypeEnum.LEADS_TAKEBACK,
                "线索即将被收回",SFA_LEADS_WILL_BE_RECOVERD_NEW_RECORD, Lists.newArrayList(),contentKey, contentParams);
    }



}
