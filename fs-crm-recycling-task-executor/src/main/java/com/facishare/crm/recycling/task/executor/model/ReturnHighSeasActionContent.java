package com.facishare.crm.recycling.task.executor.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-07-04 14:38
 */
@Data
@Builder
public class ReturnHighSeasActionContent {

    @JSONField(name = "Name")
    String name;
    @JSONField(name = "CustomerName")
    String customerName;
    @JSONField(name = "CustomerStatus")
    String accountStatus;
    @JSONField(name = "CustomerStatusDescription")
    String accountStatusDescription;
    @J<PERSON>NField(name = "HouseNo")
    String houseNo;
    @JSONField(name = "CustomerIds")
    List<String> accountIds;
    @JSONField(name = "BackReason")
    String backReason;
    @JSONField(name = "BackReasonDescription")
    String backReasonDescription;

    @JSONField(name = "ObjectIds")
    List<String> objectIds;

    @JSONField(name = "Company")
    String company;

}
