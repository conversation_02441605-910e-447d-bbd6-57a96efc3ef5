package com.facishare.crm.recycling.task.executor.consumer;

import com.alibaba.fastjson.JSON;
import org.apache.rocketmq.common.message.MessageExt;
import com.facishare.crm.recycling.task.executor.model.PoolAllocateRuleMemberCalculateModel.Arg;
import com.facishare.crm.recycling.task.executor.service.PoolAllocateRuleMemberCalculateService;
import com.facishare.paas.appframework.common.mq.RocketMQMessageListener;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Slf4j
public class PoolAllocateRuleMemberWheresCalculateListener implements RocketMQMessageListener {

	@Autowired
	private PoolAllocateRuleMemberCalculateService memberCalculateService;



	@Override
	public void consumeMessage(List<MessageExt> list) {
		TraceContext.get().setTraceId(list.get(0).getMsgId());
		try {
			if (CollectionUtils.isNotEmpty(list)) {
				for (MessageExt messageExt : list) {
					Arg arg = JSON.parseObject(messageExt.getBody(), Arg.class);
					log.info("PoolAllocateRuleMemberWheresCalculateListener receive message arg:{}", arg);
					memberCalculateService.calculateMemberWheres(arg);
				}
			}
		} catch (Exception e) {
			log.error("PoolAllocateRuleMemberWheresCalculateListener consume failed!", e);
			throw new RuntimeException(e);
		}
	}

}
