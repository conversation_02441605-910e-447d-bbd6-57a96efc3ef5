package com.facishare.crm.recycling.task.executor.service.impl.forecast.calculate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastObject;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastRule;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastTaskDetail;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.Function;

abstract class BestPracticesForecastRuleFieldCalculator implements ForecastModelCalculator {

    private static final String RULE_VALUES_FILED_NAME = "forecastval";
    private static final String OBJECT_VALUE_FILED_NAME = "forecast_type";

    @Override
    public boolean acquire(ForecastRule rule) {
        Set<String> target = targetGetter().apply(rule);
        if (target != null) {
            return true;
        }
        JSONObject object = JSON.parseObject(ruleGetter().apply(rule));
        if (object != null) {
            JSONArray array = object.getJSONArray(RULE_VALUES_FILED_NAME);
            if (array != null && !array.isEmpty()) {
                target = new HashSet<>(array.toJavaList(String.class));
                targetSetter().accept(rule, target);
                return true;
            }
        }
        return false;
    }

    @Override
    public BigDecimal calculate(ForecastTaskDetail detail, ForecastRule rule) {
        if (involve(Objects.requireNonNull(detail.getForecastObject()), rule)) {
            return detail.getForecastAmount();
        }
        return null;
    }

    private boolean involve(ForecastObject object, ForecastRule rule) {
        Set<String> target = targetGetter().apply(rule);
        return target != null && target.contains(object.get(OBJECT_VALUE_FILED_NAME, String.class));
    }

    abstract Function<ForecastRule, String> ruleGetter();

    abstract Function<ForecastRule, Set<String>> targetGetter();

    abstract BiConsumer<ForecastRule, Set<String>> targetSetter();
}
