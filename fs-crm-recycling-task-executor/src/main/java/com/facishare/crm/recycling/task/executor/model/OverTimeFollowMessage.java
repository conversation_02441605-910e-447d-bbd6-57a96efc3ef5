package com.facishare.crm.recycling.task.executor.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Description 重算的message
 * <AUTHOR>
 * @Date 2022-03-21 17:33
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OverTimeFollowMessage {

    private String tenantId;

    private String objectId;

    private Date executeTime;
}
