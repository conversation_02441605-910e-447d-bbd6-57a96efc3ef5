package com.facishare.crm.recycling.task.executor.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-03-05 15:44
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RecyclingRemindRule {
    /**
     * RecyclingRemindRuleID : dd629df760ac44c1ace8f4d37d571bfb
     * RecyclingRuleID : c96f4b0fb6b64540b1247f6231ffe380
     * DataID : 6fc7745f7cbf4e078dc011dc0f9106f8
     * RuleType : 2
     * RemindDays : 1
     * GroupID : 33ae1cb6b815401cb847b6eeb57e614b
     */

    private String RecyclingRemindRuleID;
    private String RecyclingRuleID;
    private String DataID;
    private int RuleType;
    private int RemindDays;
    private String GroupID;
}
