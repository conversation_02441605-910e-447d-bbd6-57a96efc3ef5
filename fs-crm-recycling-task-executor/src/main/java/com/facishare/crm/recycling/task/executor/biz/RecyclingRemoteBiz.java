package com.facishare.crm.recycling.task.executor.biz;

import com.facishare.crm.recycling.task.executor.model.RecyclingRule;
import com.facishare.crm.recycling.task.executor.model.RecyclingRuleArg;
import com.facishare.crm.recycling.task.executor.model.RecyclingRuleResult;
import com.facishare.crm.recycling.task.executor.proxy.RecyclingRuleProxy;
import com.facishare.crm.recycling.task.executor.util.ProxyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-03-05 16:20
 */

@Component
public class RecyclingRemoteBiz {

    @Autowired
    private RecyclingRuleProxy recyclingRuleProxy;

    /**
     * @param dataId
     */
    public List<RecyclingRule> getRecyclingRuleByDataid(String tenantId, String dataId, String apiName) {

        RecyclingRuleArg arg = RecyclingRuleArg.builder().api_name(apiName).data_id(dataId).build();
        RecyclingRuleResult recyclingRule = recyclingRuleProxy.getRecyclingRuleByDataid(ProxyUtils.getCrmHeader(tenantId), arg);
        if (!recyclingRule.isSuccess()) {
            return null;
        }
        return recyclingRule.getValue().getRecyclingRuleList();
    }
}
