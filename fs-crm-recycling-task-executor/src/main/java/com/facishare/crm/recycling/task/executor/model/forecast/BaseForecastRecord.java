package com.facishare.crm.recycling.task.executor.model.forecast;

import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

abstract class BaseForecastRecord extends ObjectData implements BaseForecast {

    protected BaseForecastRecord() {

    }

    protected BaseForecastRecord(Map<?, ?> map) {
        super(map);
        convertStringToList(OWNER);
        convertStringToList(DATA_OWN_DEPARTMENT);
        convertStringToList(DATA_OWN_ORGANIZATION);
    }

    @SuppressWarnings("unchecked")
    private void convertStringToList(String fieldName) {
        Object o = this.map.get(fieldName);
        if (o instanceof String) { // 与ObjectData保持一致
            List<String> list = new ArrayList<>();
            list.add((String) o);
            this.map.put(fieldName, list);
        }
    }

    protected BaseForecastRecord(IObjectData objectData) {
        super(((ObjectData) objectData).getContainerDocument());
    }

    @Override
    public String getId() {
        Object id = get(BaseForecast.ID);
        return id == null ? super.getId() : id.toString();
    }

    @Override
    public String getOwner0() {
        List<String> owner = getOwner();
        if (owner != null && !owner.isEmpty()) {
            return owner.get(0);
        } else {
            return null;
        }
    }
}