package com.facishare.crm.recycling.task.executor.service.impl.forecast.calculate;

import com.facishare.crm.recycling.task.executor.model.forecast.ForecastRule;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastTask;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastTaskDetail;

import java.math.BigDecimal;
import java.util.function.BiConsumer;
import java.util.function.Function;

class ArtificialCommitmentForecastModelCalculator extends BooleanForecastRuleFieldCalculator {

    @Override
    public Function<ForecastTask, BigDecimal> taskGetter() {
        return ForecastTask::getArtificialCommitmentForecastModel;
    }

    @Override
    public BiConsumer<ForecastTask, BigDecimal> taskSetter() {
        return ForecastTask::setArtificialCommitmentForecastModel;
    }

    @Override
    public Function<ForecastTaskDetail, BigDecimal> detailGetter() {
        return ForecastTaskDetail::getArtificialCommitmentForecastModel;
    }

    @Override
    public BiConsumer<ForecastTaskDetail, BigDecimal> detailSetter() {
        return ForecastTaskDetail::setArtificialCommitmentForecastModel;
    }

    @Override
    public String taskApiName() {
        return ForecastTask.ARTIFICIAL_COMMITMENT_FORECAST_MODEL;
    }

    @Override
    Function<ForecastRule, Boolean> ruleGetter() {
        return ForecastRule::getArtificialCommitmentForecastModel;
    }

    @Override
    public BigDecimal calculate(ForecastTaskDetail detail, ForecastRule rule) {
        if (detail.getInCommitmentForecast() != null && detail.getInCommitmentForecast()) {
            return detail.getForecastAmount();
        }
        return null;
    }

}
