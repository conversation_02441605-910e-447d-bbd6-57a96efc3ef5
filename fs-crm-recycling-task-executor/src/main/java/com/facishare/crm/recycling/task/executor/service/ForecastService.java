package com.facishare.crm.recycling.task.executor.service;

import com.facishare.crm.recycling.task.executor.model.forecast.ForecastExplanation;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastObject;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastPeriod;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastRule;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastTask;
import com.facishare.crm.recycling.task.executor.service.impl.forecast.calculate.ForecastModelCalculator;
import com.facishare.crm.recycling.task.executor.service.impl.forecast.mq.ForecastMessage;
import com.facishare.paas.metadata.exception.MetadataServiceException;

import java.util.List;

public interface ForecastService {

    int QUERY_LIMIT = 2000; // 底层默认最大值=2000

    void initTaskByRule(ForecastRule rule) throws MetadataServiceException;

    void upsertTask(List<ForecastTask> tasks);

    void syncForecastObject(ForecastMessage.ObjectSync message) throws MetadataServiceException;

    void deleteTaskByOwner(String tenantId, String owner);

    void deleteTaskByRule(ForecastRule rule);

    void recalculateTask(ForecastTask task, ForecastModelCalculator calculator) throws MetadataServiceException;

    void addTaskDetailManually(ForecastTask task, List<String> forecastObjectIds) throws MetadataServiceException;

    void removeTaskDetail(ForecastTask task, List<String> forecastObjectIds) throws MetadataServiceException;

    List<ForecastPeriod> getForecastPeriod(ForecastRule rule);

    List<ForecastExplanation> explain(ForecastObject object) throws MetadataServiceException;
}
