package com.facishare.crm.recycling.task.executor.service;

import com.facishare.crm.recycling.task.executor.biz.CustomerBiz;
import com.facishare.crm.recycling.task.executor.model.CustomObjectDataChangeMQMessage;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.facishare.crm.recycling.task.executor.util.ConstantUtils.PERSONNEL_OBJ;

@Slf4j
@Service
public class EmployeeChangeOnChangeService {

	@Autowired
	private CustomerBiz customerBiz;

	@Autowired
	private PoolAllocateRuleMemberCalculateService poolAllocateRuleMemberCalculateService;

	public void onChange(CustomObjectDataChangeMQMessage message) {
		log.info("监听到人员变更同步权重分配规则条件：message:{}", message);
		String tenantId = message.getTenantId();
		if (message.isInsert()) {
			for (CustomObjectDataChangeMQMessage.Content content : message.getBody()) {
				IObjectData personnelData = customerBiz.getObjectById(tenantId, content.getObjectId(), PERSONNEL_OBJ);
				String userId = personnelData.get("user_id").toString();
				poolAllocateRuleMemberCalculateService.insertMemberByWheres(tenantId, userId, "LeadsPoolObj");
			}
		}

		if (message.isDelete()) {
			for (CustomObjectDataChangeMQMessage.Content content : message.getBody()) {
				IObjectData personnelData = customerBiz.getObjectById(tenantId, content.getObjectId(), PERSONNEL_OBJ);
				String userId = personnelData.get("user_id").toString();
				poolAllocateRuleMemberCalculateService.deleteMemberById(tenantId, Lists.newArrayList(userId));
			}
		}

		if (message.isUpdate()) {
			for (CustomObjectDataChangeMQMessage.Content content : message.getBody()) {
				IObjectData personnelData = customerBiz.getObjectById(tenantId, content.getObjectId(), PERSONNEL_OBJ);
				String userId = personnelData.get("user_id", String.class);
				String status = personnelData.get("status", String.class);
				if (!"0".equals(status)) {  // 非正常状态
					poolAllocateRuleMemberCalculateService.deleteMemberById(tenantId, Lists.newArrayList(userId));
				} else {
					poolAllocateRuleMemberCalculateService.recalculateByWheres(tenantId, userId, "LeadsPoolObj");
				}
			}
		}
	}





}
