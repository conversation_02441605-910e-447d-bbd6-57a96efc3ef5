package com.facishare.crm.recycling.task.executor.common;

import com.facishare.crm.recycling.task.executor.enums.ApiNameEnum;
import com.facishare.crm.recycling.task.executor.service.RecyclingService;
import com.facishare.crm.recycling.task.executor.service.impl.AccountRecycling;
import com.facishare.crm.recycling.task.executor.service.impl.LeadsRecycling;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-03-09 18:27
 */

@Component
public class RecyclingFactory {


    @Autowired
    private AccountRecycling accountRecycling;

    @Autowired
    private LeadsRecycling leadsRecycling;

    private static final Map<String, RecyclingService> RECALCULATE_SERVICE = new HashMap<>();

    @PostConstruct
    public void init(){
        RECALCULATE_SERVICE.put(ApiNameEnum.ACCOUNT_OBJ.getApiName(), accountRecycling);
        RECALCULATE_SERVICE.put(ApiNameEnum.LEADS_OBJ.getApiName(), leadsRecycling);
    }


    public RecyclingService getRecyclingService(String apiName){
        return RECALCULATE_SERVICE.get(apiName);
    }
}
