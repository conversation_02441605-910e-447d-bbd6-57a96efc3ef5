package com.facishare.crm.recycling.task.executor.common;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.recycling.task.executor.biz.CustomerBiz;
import com.facishare.crm.recycling.task.executor.enums.CompareTypeEnum;
import com.facishare.crm.recycling.task.executor.enums.FieldTypeEnums;
import com.facishare.crm.recycling.task.executor.model.RecyclingFilter;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.restdriver.ObjectDataConverter;
import com.facishare.paas.appframework.metadata.restdriver.ObjectDataConverterManager;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.Quote;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.facishare.crm.recycling.task.executor.util.ConstantUtils.OUT_OWNER;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-03-01 19:59
 */
@Component
@Slf4j
public class DataConvert {

    @Autowired
    private ObjectDataConverterManager objectDataConverterManager;

    @Autowired
    private CustomerBiz customerBiz;

    @Autowired
    protected MetaDataService metaDataService;

    @Autowired
    private QuoteValueService quoteValueService;

    @Autowired
    private MetaDataMiscService metaDataMiscService;


    /**
     * 由于rule filter 暂未进行写迁移，里面存储的字段为sqlserver字段名称 故构造一个字段名称转换器
     *
     * @return 字段名称转换器
     */
    private ObjectDataConverter getConverter(String apiName) {
        Optional<ObjectDataConverter> converter = Optional.of(objectDataConverterManager.getObjectDataConverter(apiName));
        return converter.get();
    }


    /**
     * 新老字段 FieldName转换
     *
     * @param recyclingFilters filter列表
     * @param objectData       对象值
     * @param apiName
     * @return 表达式的值
     */
    public Map<String, Object> filterDataFileNameConvert(List<RecyclingFilter> recyclingFilters, IObjectData objectData, IObjectDescribe objectDescribe, String apiName) {
        Map<String, Object> ret = new HashMap<>();
        List<String> fieldNames = recyclingFilters.stream().map(x -> x.getFieldName()).collect(Collectors.toList());
        for (String fieldName : fieldNames) {
            String newFieldName = fieldName;
            List<IFieldDescribe> collect = objectDescribe.getFieldDescribes().stream().filter(
                    x -> x.getApiName().equals(fieldName)).collect(Collectors.toList());
            if (collect == null || collect.size() == 0) {
                continue;
            }
            String type = collect.get(0).getType();
            switch (FieldTypeEnums.valueEnumOf(type)) {
                case CURRENCY:
                    if (objectData.get(newFieldName) == null) {
                        ret.put(fieldName, null);
                    } else {
                        ret.put(fieldName, new BigDecimal(objectData.get(newFieldName).toString()).doubleValue());
                    }
                    break;
                case NUMBER:
                    if (objectData.get(newFieldName) == null) {
                        ret.put(fieldName, null);
                    } else {
                        ret.put(fieldName, new Double(objectData.get(newFieldName).toString()));
                    }
                    break;
                case SELECT_MANY:
                    ArrayList<String> arrayList = objectData.get(newFieldName, ArrayList.class);
                    if (CollectionUtils.isEmpty(arrayList)) {
                        ret.put(fieldName, null);
                        break;
                    } else {
                        Collections.sort(arrayList);
                        ret.put(fieldName, arrayList);
                    }
                    //多选中如果是属于、包含
                    Optional<Integer> first = recyclingFilters.stream().filter(x -> x.getFieldName().equals(fieldName)).map(RecyclingFilter::getCompare).findFirst();
                    if (first.isPresent() && (first.get().equals(Integer.parseInt(CompareTypeEnum.BELONGTO.getValue()))
                            || first.get().equals(Integer.parseInt(CompareTypeEnum.NOTBELONGTO.getValue())))) {

                        String[] strArray = new String[arrayList.size()];
                        for (int i = 0; i < arrayList.size(); i++) {
                            strArray[i] = arrayList.get(i);
                        }
                        ret.put(fieldName, strArray);
                    }

                    break;
                case OBJECT_REFERENCE:
                    IObjectData objectById = customerBiz.getObjectById(objectData.getTenantId(),
                            objectData.get(collect.get(0).getApiName(), String.class), collect.get(0).get("target_api_name", String.class));
                    if (objectById != null) {
                        ret.put(fieldName, objectById.getName());
                    } else {
                        ret.put(fieldName, null);
                    }
                    break;
                case EMPLOYEE:
                case DEPARTMENT:
                    String value = "";
                    if (OUT_OWNER.equals(fieldName)){
                        User user = new User(objectData.getTenantId(), User.SUPPER_ADMIN_USER_ID);
//                    Map<String, List<IObjectData>> refObjectDataMap = this.findRefObjectDatas(objectData, objectDescribe, user);
//                    fillQuoteFieldValue(objectData, objectDescribe, refObjectDataMap, user);
//                    fillRefObjectName(objectData, objectDescribe, refObjectDataMap, user);
                        fillOutUserInfo(objectData, objectDescribe, user);
                        Object object = JSONObject.parseObject(objectData.get(newFieldName+"__r",String.class)).get("name");
                        if (object!=null){
                            value = object.toString();
                        }else {
                            log.warn("{}__r is null objectId:{}",newFieldName,objectData.getId());
                        }
                        break;
                    }else{
                        ArrayList<String> values = (ArrayList<String>) objectData.get(fieldName);
                        value = values.get(0);
                    }
                    ret.put(fieldName,value);
                    break;
                default:
                    ret.put(fieldName, objectData.get(newFieldName));
                    break;
            }
        }
        return ret;
    }

    public String toNewFieldName(String fieldName, String apiName) {
        if (fieldName.endsWith("__c")) {
            return fieldName;
        }
        ObjectDataConverter converter = getConverter(apiName);
        return converter.toNewFieldName(fieldName);
    }


    protected void fillOutUserInfo(IObjectData data, IObjectDescribe describe, User user) {
        metaDataMiscService.fillUserInfo(describe, Lists.newArrayList(data), user);
        metaDataMiscService.fillDepartmentInfo(describe, Lists.newArrayList(data), user);
//        metaDataMiscService.fillOutUserInfo(describe, Lists.newArrayList(new IObjectData[]{data}), data, user);
    }

    protected void fillRefObjectName(IObjectData data, IObjectDescribe describe, Map<String, List<IObjectData>> refObjectDataMap, User user) {
        metaDataService.fillObjectDataWithRefObject(describe, Lists.newArrayList(data), user, refObjectDataMap, false);
    }

    private void fillQuoteFieldValue(IObjectData data, IObjectDescribe describe, Map<String, List<IObjectData>> refObjectDataMap, User user) {
        quoteValueService.fillQuoteFieldValue(user, Lists.newArrayList(data), describe, refObjectDataMap, false);
    }

    private Map<String, List<IObjectData>> findRefObjectDatas(IObjectData data, IObjectDescribe describe, User user) {
        List<Quote> quoteFieldList = ObjectDescribeExt.of(describe).getQuoteFieldDescribes();
        if (com.facishare.paas.appframework.common.util.CollectionUtils.empty(quoteFieldList)) {
            return null;
        } else {
            Map<String, List<String>> refObjectDataIdMap = ObjectDataExt.getRefObjectDataIds(describe, Lists.newArrayList(data));
            Map<String, List<IObjectData>> refObjectDataMap = Maps.newConcurrentMap();
            ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
            refObjectDataIdMap.forEach((x, y) -> {
                parallelTask.submit(() -> {
                    List<IObjectData> refObjectDataList = metaDataService.findObjectDataByIdsIncludeDeletedIgnoreFormula(user, y, x);
                    refObjectDataMap.put(x, refObjectDataList);
                });
            });
            try {
                parallelTask.await(5000L, TimeUnit.MILLISECONDS);
            } catch (Exception var7) {
                log.error("findRefObjectDatas error,tenantId:{},apiName:{}", describe.getTenantId(), describe.getApiName(), var7);
            }
            return refObjectDataMap;
        }
    }

}
