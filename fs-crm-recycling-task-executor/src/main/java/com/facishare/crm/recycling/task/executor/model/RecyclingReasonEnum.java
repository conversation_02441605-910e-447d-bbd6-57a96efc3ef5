package com.facishare.crm.recycling.task.executor.model;

import static com.facishare.crm.recycling.task.executor.util.I18NKey.*;

/**
 *
 */
public enum RecyclingReasonEnum {

    /**
     * {0}天未跟进
     */
    FOLLOW(1, SFA_RECYCLING_REASON_FOLLOW),


    /**
     * {0}小时未跟进
     */
    HOURS_FOLLOW(2, SFA_RECYCLING_REASON_HOURS_FOLLOW),

    /**
     * {0}天{1}小时未跟进
     */
    DAYS_HOURS_FOLLOW(3, SFA_RECYCLING_REASON_DAYS_HOURS_FOLLOW),

    /**
     * {0}天未成交
     */
    DEAL(4, SFA_RECYCLING_REASON_DEAL),


    /**
     * {0}天未转换
     */
    TRANS(5, SFA_RECYCLING_REASON_TRANS),

    /**
     * {0}小时未转换
     */
    HOURS_TRANS(6, SFA_RECYCLING_REASON_HOURS_TRANS),

    /**
     * {0}天{1}小时未转换
     */
    DAYS_HOURS_TRANS(7, SFA_RECYCLING_REASON_DAYS_HOURS_TRANS);

    RecyclingReasonEnum(int type, String text) {
        this.type = type;
        this.text = text;
    }

    private int type;

    private String text;

    public int getType() {
        return type;
    }

    public String getText() {
        return text;
    }

    public static String valueOf(Integer type){
        if (type == null){
            return null;
        }
        for (RecyclingReasonEnum value : RecyclingReasonEnum.values()) {
            if (value.getType() == type){
                return value.getText();
            }
        }
        return "";
    }


}
