package com.facishare.crm.recycling.task.executor.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-03-05 15:43
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RecyclingFilter {

    /**
     * RecyclingFilterID : 541f4ef962ff4170a3e62e461e6f6e00
     * RecyclingRuleID : c96f4b0fb6b64540b1247f6231ffe380
     * DataID : 6fc7745f7cbf4e078dc011dc0f9106f8
     * FieldName : Name
     * FieldType : 2
     * FieldOrder : 0
     * Compare : 3
     * FieldValue : gchr回收
     */

    private String RecyclingFilterID;
    private String RecyclingRuleID;
    private String DataID;
    private String FieldName;
    private int FieldType;
    private int FieldOrder;
    private int Compare;
    private String FieldValue;

}



