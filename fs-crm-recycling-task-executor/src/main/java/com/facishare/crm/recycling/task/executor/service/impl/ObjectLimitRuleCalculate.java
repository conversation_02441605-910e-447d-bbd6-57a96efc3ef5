package com.facishare.crm.recycling.task.executor.service.impl;

import com.facishare.crm.recycling.task.executor.model.*;
import com.facishare.crm.recycling.task.executor.service.ObjectLimitRuleCalculateService;
import com.facishare.crm.sfa.lto.exception.ExceptionUtil;
import com.facishare.crm.sfa.lto.objectlimit.ObjectLimitRuleService;
import com.facishare.crm.sfa.lto.objectlimit.models.ObjectLimitRuleModel;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @Description 保有量规则计算
 */

@Slf4j
@Component
public class ObjectLimitRuleCalculate implements ObjectLimitRuleCalculateService {
    @Autowired
    private ObjectLimitRuleService objectLimitBiz;

    @Override
    @Transactional
    public void execute(ObjectLimitRuleCalculateModel.ObjectLimitRuleCalculateMessage message) {
        try {
            String tenantId = message.getTenantId();
            String objectApiName = message.getObjectApiName();
            String groupId = message.getGroupId();
            List<ObjectLimitRuleModel.ObjectLimitRule> objectLimitRuleList = objectLimitBiz.getObjectLimitRuleByGroupId(tenantId, objectApiName, groupId);
            if (CollectionUtils.empty(objectLimitRuleList)) {
                log.warn("ObjectLimitRuleCalculate objectLimitRuleList is null :{}", tenantId);
                return;
            }
            String preCalculateId = message.getCurrentCalculateId();
            String calculateId = objectLimitBiz.generateId();

            List<String> userIds = objectLimitBiz.getObjectLimitRuleUserIds(tenantId, objectLimitRuleList);
            List<String> employeeIds = objectLimitBiz.getDefaultEmployeeRule(tenantId, objectApiName, groupId);
            objectLimitBiz.deleteEmployeeRule(tenantId, objectApiName, groupId);
            objectLimitBiz.insertEmployeeRule(tenantId, objectApiName, groupId, calculateId, userIds);
            objectLimitBiz.dealDefaultEmployeeRule(tenantId, objectApiName, groupId, employeeIds, calculateId);
            objectLimitBiz.updateObjectLimitFilter(tenantId, objectApiName, groupId, "", calculateId);
            objectLimitBiz.updateObjectLimitRule(tenantId, objectApiName, groupId, "", calculateId);
            log.warn(String.format("ObjectLimitRuleCalculate: tenantID-%s, groupId-%s, preCalculateId-%s, calculateId-%s, set default employee limit rule employeeIds: %s", tenantId, groupId, preCalculateId, calculateId, String.join(",", employeeIds)));
        } catch (Exception e) {
            ExceptionUtil.throwCommonBusinessException();
        }
    }
}
