package com.facishare.crm.recycling.task.executor.common;

import com.facishare.crm.sfa.lto.business.BusinessCirclesPlugService;
import com.facishare.crm.sfa.lto.business.PlugInitServer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> lik
 * @date : 2022/12/5 22:14
 */
@Component
public class PlugInitFactory {

    @Autowired
    private BusinessCirclesPlugService businessCirclesPlugServer;

    public static final Map<String, PlugInitServer> REMIND_SERVICE = new HashMap<>();

    @PostConstruct
    public void init(){
        REMIND_SERVICE.put("business_circles_plug", businessCirclesPlugServer);
    }

    public PlugInitServer getPlugInitService(String pluginApiName){
        return REMIND_SERVICE.get(pluginApiName);
    }
}
