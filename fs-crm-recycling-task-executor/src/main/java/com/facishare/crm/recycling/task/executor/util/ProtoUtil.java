package com.facishare.crm.recycling.task.executor.util;

import io.protostuff.LinkedBuffer;
import io.protostuff.ProtobufIOUtil;
import io.protostuff.Schema;
import io.protostuff.runtime.RuntimeSchema;

public class ProtoUtil {
    /**
     * 将pb字节数组反序列化为目标类型的对象
     */
    public static <T> T fromProto(byte[] data, Class<T> clazz) throws Exception {
        Schema schema = RuntimeSchema.getSchema(clazz);
        T object = clazz.newInstance();
        ProtobufIOUtil.mergeFrom(data, object, schema);
        return object;
    }

    /**
     * 将对象序列化为pb字节数组
     */
    public static byte[] toProto(Object data) {
        Schema schema = RuntimeSchema.getSchema(data.getClass());
        return ProtobufIOUtil.toByteArray(data, schema, LinkedBuffer.allocate(512));
    }
}