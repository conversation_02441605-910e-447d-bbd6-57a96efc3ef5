package com.facishare.crm.recycling.task.executor.service.impl.forecast.calculate;

import com.facishare.crm.recycling.task.executor.model.forecast.ForecastRule;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastTask;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastTaskDetail;

import java.math.BigDecimal;
import java.util.function.BiConsumer;
import java.util.function.Function;

public class AIWeightForecastModelCalculator implements ForecastModelCalculator {
    @Override
    public BiConsumer<ForecastTask, BigDecimal> taskSetter() {
        return ForecastTask::setAIWeightForecastModel;
    }

    @Override
    public Function<ForecastTask, BigDecimal> taskGetter() {
        return ForecastTask::getAIWeightForecastModel;
    }

    @Override
    public BiConsumer<ForecastTaskDetail, BigDecimal> detailSetter() {
        return ForecastTaskDetail::setAIWeightForecastModel;
    }

    @Override
    public Function<ForecastTaskDetail, BigDecimal> detailGetter() {
        return ForecastTaskDetail::getAIWeightForecastModel;
    }

    @Override
    public String taskApiName() {
        return ForecastTask.AI_WEIGHT_FORECAST_MODEL;
    }

    @Override
    public boolean acquire(ForecastRule rule) {
        return rule.getAIWeightForecastModel() != null;
    }

    @Override
    public BigDecimal calculate(ForecastTaskDetail detail, ForecastRule rule) {
        BigDecimal aiProbability = detail.getForecastObject().get("ai_probability", BigDecimal.class);
        if (aiProbability != null && aiProbability.compareTo(rule.getAIWeightForecastModel()) > 0) {
            return detail.getForecastAmount();
        }
        return null;
    }
}
