package com.facishare.crm.recycling.task.executor.biz;

import com.facishare.crm.recycling.task.executor.enums.ApiNameEnum;
import com.facishare.crm.sfa.lto.enums.LeadsBizStatusEnum;
import com.facishare.crm.recycling.task.executor.enums.LifeStatusEnum;
import com.facishare.crm.recycling.task.executor.util.SearchUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.facishare.crm.recycling.task.executor.util.ConstantUtils.*;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-03-29 14:48
 */
@Slf4j
@Component
public class LeadsBiz extends BaseBiz {

    @Autowired
    private IObjectDataService objectDataPgService;

    @Autowired
    private MetaDataFindService metaDataFindService;

    /**
     * 根据线索池id 查询线索
     *  生命状态:正常;
     *  线索状态:变更中
     *
     * @param tenantId
     * @param leadsPoolId
     * @param limit
     * @return
     */
    public List<IObjectData> getLeadsLimitByHighSeasId(String tenantId,  String leadsPoolId, Integer limit, Integer offset) {

        ArrayList<String> bizStatus = Lists.newArrayList(
                LeadsBizStatusEnum.UN_PROCESSED.getValue(),
                LeadsBizStatusEnum.PROCESSED.getValue(),
                LeadsBizStatusEnum.CLOSED.getValue());
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(limit);
        searchQuery.setOffset(offset);
        List filters = Lists.newLinkedList();
        SearchUtil.fillFilterEq(filters, LIFE_STATUS, LifeStatusEnum.NORMAL.getValue());
        SearchUtil.fillFilterIn(filters, BIZ_STATUS, bizStatus);
        SearchUtil.fillFilterEq(filters, LEADS_POOL_ID, leadsPoolId);
        searchQuery.setFilters(filters);

        List<OrderBy> orders =  new ArrayList<>();
        orders.add(new OrderBy(CREATE_TIME,Boolean.FALSE));
        searchQuery.setOrders(orders);

        QueryResult<IObjectData> queryResult;


        try {
            queryResult = metaDataFindService.findBySearchQuery(buildUser(tenantId), ApiNameEnum.LEADS_OBJ.getApiName(), searchQuery);
        } catch (Exception e) {
            log.info("find ObjectFollowDealSettingObj throw exception {},tenantId:{},", e.getMessage(), tenantId);
            return null;
        }
        return queryResult == null ? Lists.newArrayList() : queryResult.getData();
    }



    public List<IObjectData> getLeadsLimitByOwner(String tenantId,  String owenrId, Integer limit, Integer offset) {

        ArrayList<String> bizStatus = Lists.newArrayList(
                LeadsBizStatusEnum.UN_PROCESSED.getValue(),
                LeadsBizStatusEnum.PROCESSED.getValue(),
                LeadsBizStatusEnum.CLOSED.getValue());
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(limit);
        searchQuery.setOffset(offset);
        List filters = Lists.newLinkedList();
        SearchUtil.fillFilterEq(filters, LIFE_STATUS, LifeStatusEnum.NORMAL.getValue());
        SearchUtil.fillFilterIn(filters, BIZ_STATUS, bizStatus);
        SearchUtil.fillFilterEq(filters, OWNER, owenrId);
        searchQuery.setFilters(filters);

        List<OrderBy> orders =  new ArrayList<>();
        orders.add(new OrderBy(CREATE_TIME,Boolean.FALSE));
        searchQuery.setOrders(orders);

        QueryResult<IObjectData> queryResult;

        try {
            queryResult = metaDataFindService.findBySearchQuery(buildUser(tenantId), ApiNameEnum.LEADS_OBJ.getApiName(), searchQuery);
        } catch (Exception e) {
            log.info("find ObjectFollowDealSettingObj throw exception {},tenantId:{},", e.getMessage(), tenantId);
            return null;
        }
        return queryResult == null ? Lists.newArrayList() : queryResult.getData();
    }

    public List<String>  getAllLeadsIdsByPoolId(String tenantId,String poolId){
        String sql = "select id from biz_leads where "
                + String.format(" tenant_id = '%s' ", tenantId)
                + String.format(" and leads_pool_id = '%s' ", poolId)
                + " and is_deleted = 0 and biz_status in ('processed','un_processed','closed') and life_status in('normal','in_change') ";
        List<Map> ret = new ArrayList<>();
        try {
            ret = objectDataPgService.findBySql(tenantId, sql);
        } catch (MetadataServiceException e) {
            log.error("find getAllLeadsIdsByOwnerId error:{}",sql,e);
        }
        if (CollectionUtils.empty(ret)){
            return Lists.newArrayList();
        }
        return ret.stream().filter(x -> x.get("id") != null).map(x -> x.get("id").toString()).collect(Collectors.toList());
    }

    public List<String> getAllLeadsIdsByOwner(String tenantId,String ownerId){
        String sql = "select id from biz_leads where "
                + String.format(" tenant_id = '%s' ", tenantId)
                + String.format(" and owner = '%s' ", ownerId)
                + " and is_deleted = 0 and biz_status in ('processed','un_processed','closed') and life_status in('normal','in_change') and leads_pool_id IS NOT NULL ";
        List<Map> ret = new ArrayList<>();
        try {
            ret = objectDataPgService.findBySql(tenantId, sql);
        } catch (MetadataServiceException e) {
            log.error("find getAllLeadsIdsByOwnerId error:{}",sql,e);
        }
        if (CollectionUtils.empty(ret)){
            return Lists.newArrayList();
        }
        return ret.stream().filter(x -> x.get("id") != null).map(x -> x.get("id").toString()).collect(Collectors.toList());
    }
}
