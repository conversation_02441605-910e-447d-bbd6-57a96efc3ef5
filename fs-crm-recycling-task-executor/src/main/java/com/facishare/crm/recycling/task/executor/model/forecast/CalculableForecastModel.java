package com.facishare.crm.recycling.task.executor.model.forecast;

import java.math.BigDecimal;

interface CalculableForecastModel extends ForecastModel {
    BigDecimal getBestPracticesForecastModel1();

    void setBestPracticesForecastModel1(BigDecimal bestPracticesForecastModel1);

    BigDecimal getBestPracticesForecastModel2();

    void setBestPracticesForecastModel2(BigDecimal bestPracticesForecastModel2);

    BigDecimal getBestPracticesForecastModel3();

    void setBestPracticesForecastModel3(BigDecimal bestPracticesForecastModel3);

    BigDecimal getBestPracticesForecastModel4();

    void setBestPracticesForecastModel4(BigDecimal bestPracticesForecastModel4);

    BigDecimal getBestPracticesForecastModel5();

    void setBestPracticesForecastModel5(BigDecimal bestPracticesForecastModel5);

    BigDecimal getArtificialCommitmentForecastModel();

    void setArtificialCommitmentForecastModel(BigDecimal artificialCommitmentForecastModel);

    BigDecimal getStageWeightForecastModel();

    void setStageWeightForecastModel(BigDecimal stageWeightForecastModel);

    BigDecimal getArtificialWeightForecastModel();

    void setArtificialWeightForecastModel(BigDecimal artificialWeightForecastModel);

    BigDecimal getAIWeightForecastModel();

    void setAIWeightForecastModel(BigDecimal aiWeightForecastModel);
}
