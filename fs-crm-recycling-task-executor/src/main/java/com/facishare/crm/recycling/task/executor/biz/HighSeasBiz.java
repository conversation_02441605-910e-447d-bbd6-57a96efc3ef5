package com.facishare.crm.recycling.task.executor.biz;

import com.facishare.crm.recycling.task.executor.common.RecyclingTaskGray;
import com.facishare.crm.recycling.task.executor.enums.ApiNameEnum;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-03-07 17:41
 */
@Component
@Slf4j
public class HighSeasBiz {

    @Autowired
    private IObjectDataService objectDataPgService;

    @Autowired
    private RecyclingTaskGray recyclingTaskGray;

    /**
     * @param tenantId
     * @param objectId
     */
    public IObjectData getHighSeasById(String tenantId, String objectId) {
        return getBizHighSeasById(tenantId,objectId);
    }


    public List<Map> getRemindRule(String tenantId,String objectId){
        IObjectData accountObj;
        String sql = "select * from "
                + "remind_rule"
                + String.format(" where ei = '%s'", tenantId)
                + String.format(" and data_id = '%s' ", objectId);
        try {
            List<Map> result = objectDataPgService.findBySql(tenantId, sql);
            if (CollectionUtils.isNotEmpty(result)) {
                return result;
            } else {
                return null;
            }
        } catch (MetadataServiceException e) {
            log.error("getHighSeasById tenantId:{} message:{}", tenantId, e.getMessage());
            throw new RuntimeException();
        }
    }


    /**
     * 查询线索池信息
     * @param tenantId
     * @param objectId
     */
    public IObjectData getLeadsPoolById(String tenantId, String objectId) {
        IObjectData accountObj;
        String sql = "select * from biz_leads_pool "
                + String.format(" where tenant_id = '%s'", tenantId)
                + String.format(" and is_deleted = 0 and id = '%s' ", objectId);
        try {
            QueryResult<IObjectData> result = objectDataPgService.findBySql(sql, tenantId, ApiNameEnum.LEADS_POOL_OBJ.getApiName());
            if (CollectionUtils.isNotEmpty(result.getData())) {
                accountObj = result.getData().get(0);
            } else {
                return null;
            }
        } catch (MetadataServiceException e) {
            log.error("getHighSeasById tenantId:{} message:{}", tenantId, e.getMessage());
            throw new RuntimeException();
        }
        return accountObj;
    }

    /**
     * @param tenantId
     * @param objectId
     */
    public IObjectData getBizHighSeasById(String tenantId, String objectId) {
        IObjectData accountObj;
        String sql = "select * from "
                + "biz_highseas"
                + String.format(" where is_deleted = 0 and tenant_id = '%s'", tenantId)
                + String.format(" and id = '%s' ", objectId);
        try {
            QueryResult<IObjectData> result = objectDataPgService.findBySql(sql, tenantId, ApiNameEnum.HIGH_SEAS_OBJ.getApiName());
            if (CollectionUtils.isNotEmpty(result.getData())) {
                accountObj = result.getData().get(0);
            } else {
                return null;
            }
        } catch (MetadataServiceException e) {
            log.error("getHighSeasById tenantId:{} message:{}", tenantId, e.getMessage());
            throw new RuntimeException();
        }
        return accountObj;
    }

}
