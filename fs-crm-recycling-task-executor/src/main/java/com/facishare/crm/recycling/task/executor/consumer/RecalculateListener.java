package com.facishare.crm.recycling.task.executor.consumer;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.recycling.task.executor.common.RecalculateFactory;
import com.facishare.crm.recycling.task.executor.common.RecyclingTaskGray;
import com.facishare.crm.recycling.task.executor.common.SfaRecyclingTaskRateLimiterService;
import com.facishare.crm.recycling.task.executor.enums.ActionCodeEnum;
import com.facishare.crm.recycling.task.executor.model.RecalculateMessage;
import com.facishare.crm.sfa.audit.log.context.SFALogContext;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.fxiaoke.rocketmq.util.MessageHelper;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

/**
 * 执行器接收MQ入口
 */
@Slf4j
@Component
public class RecalculateListener implements ApplicationListener<ContextRefreshedEvent> {

    private AutoConfMQPushConsumer consumer;

    @Autowired
    private RecalculateFactory recalculateFactory;

    @Autowired
    private SfaRecyclingTaskRateLimiterService sfaRecyclingTaskRateLimiterService;

    @Autowired
    private RecyclingTaskGray recyclingTaskGray;

    @PostConstruct
    public void init() {
        consumer = new AutoConfMQPushConsumer("fs-crm-recycling-task-recalculate", (MessageListenerConcurrently) (msgs, context) -> {
            if (!msgs.isEmpty()) {
                for(MessageExt msg: msgs) {
                    sfaRecyclingTaskRateLimiterService.getCrmRecalculateLimiter().acquire();
                    // 取出traceContext
                    MessageHelper.fillContextFromMessage(TraceContext.get(), msg);
                    consumeMessage(msg);
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        });
    }


    @PreDestroy
    public void close() {
        consumer.close();
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (consumer != null && event.getApplicationContext().getParent() == null) {
            consumer.start();
        }
    }

    private void consumeMessage(MessageExt body) {
        try {
            RecalculateMessage message = JSON.parseObject(body.getBody(), RecalculateMessage.class);
            log.info("RecalculateMessage, msgId:{},reconsumeTimes:{},RecalculateMessage:{}", body.getMsgId(),body.getReconsumeTimes(),message);
            if (recyclingTaskGray.skipTenantId(message.getTenantId())){
                log.warn("RecalculateListener skipTenantId:{},objectId:{}",message.getTenantId(),message.getObjectId());
                return;
            }
            if(message.getActionCode().equals(ActionCodeEnum.CHANGE_RULE.getActionCode())
                    && body.getReconsumeTimes() > 2) {
                return;
            }
            SFALogContext.putVariable("bizName","crm_object_recycling_task_recalculate");
            SFALogContext.putVariable("msg",body);
            SFALogContext.putVariable("cost1",System.currentTimeMillis() - body.getBornTimestamp());
            recalculateFactory.getRecalculateService(message.getObjectApiName()).execute(message);
        } catch (Exception e) {
            log.error("RecalculateListener consumeMessage msgId:{}",body.getMsgId(),e);
            throw new RuntimeException(e);
        }finally {
            TraceContext.remove();
            SFALogContext.clearContext();
        }
    }


}
