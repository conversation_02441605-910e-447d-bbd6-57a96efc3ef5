package com.facishare.crm.recycling.task.executor.service.impl.forecast.mq;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.recycling.task.executor.service.ForecastService;
import com.facishare.crm.sfa.audit.log.context.SFALogContext;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ObjectSyncForecastMessageListener extends AutoConfigurationMessageListener {

    @Autowired
    private ForecastService forecastService;

    @Override
    public String sectionName() {
        return "forecast-object-sync";
    }

    @Override
    public void consumeMessage(MessageExt msg) throws MetadataServiceException {
        String msgId = msg.getMsgId();
        log.info("Receive message  <== {}", msgId);
        ForecastMessage.ObjectSync message = JSON.parseObject(msg.getBody(), ForecastMessage.ObjectSync.class);
        if (message == null || isInvalid(message)) {
            log.warn("Receive illegal message ==> {}", msg.getBody());
            return;
        }
        try {
            forecastService.syncForecastObject(message);
        } finally {
            SFALogContext.clearContext();
        }
        log.info("Consume message  ==> {}", msgId);
    }

    private static boolean isInvalid(ForecastMessage.ObjectSync message) {
        return message.getTenantId() == null || message.getObjectId() == null || message.getObjectApiName() == null || message.getAction() == null;
    }
}
