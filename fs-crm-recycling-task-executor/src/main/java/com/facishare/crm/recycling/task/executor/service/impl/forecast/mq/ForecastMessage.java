package com.facishare.crm.recycling.task.executor.service.impl.forecast.mq;

import com.facishare.crm.recycling.task.executor.model.forecast.ForecastRule;
import com.facishare.crm.sfa.audit.log.EntityConverter;
import com.facishare.crm.sfa.audit.log.model.AuditArg;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.text.WordUtils;

public interface ForecastMessage {

    String RULE_ENABLE_TOPIC_TAG = "fs-crm-sfa-forecast-rule-enable";
    String OBJECT_SYNC_TOPIC_TAG = "fs-crm-sfa-forecast-object-sync";
    String TASK_LOCK_TOPIC_TAG = "fs-crm-sfa-forecast-task-lock";
    String TASK_REMIND_TOPIC_TAG = "fs-crm-sfa-forecast-task-remind";

    @Setter
    @Getter
    class ObjectSync implements ForecastMessage {
        private String tenantId;
        private String objectId;
        private String objectApiName;
        private String action;
    }

    class ObjectSyncConverter implements EntityConverter<ObjectSync> {
        @Override
        public AuditArg convert(ObjectSync obj) {
            return AuditArg.builder()
                    .actionCode(WordUtils.capitalizeFully(obj.action))
                    .ei(obj.tenantId)
                    .objectApiName(obj.objectApiName)
                    .objectIds(obj.objectId)
                    .build();
        }
    }

    @Setter
    @Getter
    class RuleEnable implements ForecastMessage {
        private String tenantId;
        private String ruleId;
        private Long expectTime;
        private Long createTime;
        private String action;
    }

    class RuleEnableConverter implements EntityConverter<RuleEnable> {
        @Override
        public AuditArg convert(RuleEnable obj) {
            return AuditArg.builder()
                    .actionCode(obj.action)
                    .ei(obj.tenantId)
                    .objectApiName(ForecastRule.FORECAST_RULE_OBJECT_DESCRIBE_API_NAME)
                    .objectIds(obj.ruleId)
                    .extra(String.valueOf(obj.expectTime))
                    .build();
        }
    }
}
