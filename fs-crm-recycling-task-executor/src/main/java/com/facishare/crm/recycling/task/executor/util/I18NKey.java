package com.facishare.crm.recycling.task.executor.util;

public class I18NKey {
    /**
     * 注意，您的 {0} 由于长时间未跟进/未转换， 将在 {1} 天后被自动收回，请及时处理
     */
    public static final String SFA_REMIND_DAY_NEW_RECORD = "sfa.remind.day.new.record";

    /**
     * 注意，您的 {0} 由于长时间未跟进/未转换， 将在 {1} 天 {2} 小时后被自动收回，请及时处理
     */
    public static final String SFA_REMIND_DAY_TIME_NEW_RECORD = "sfa.remind.day.time.new.record";


    /**
     * {0} 满足回收条件 被收回到公海： {1}
     */
    public static final String SFA_RECYCLING_TO_HIGHSEAS_NEW_RECORD = "sfa.recycling.to.high.seas.new.record";


    /**
     * 注意，{0} 满足 {1}， 已经被收回到公海：{2}
     * 注意，{{客户名称}} 满足 {{10天未跟进}}， 已经被收回到公海：{{公海名称}}
     */
    public static final String SFA_RECYCLING_REASON_TO_HIGHSEAS_NEW_RECORD = "sfa.recycling.reason.to.high.seas.new.record";


    /**
     * 注意，{0} 满足 {1} 天未跟进， 已经被收回到公海：{2}
     * 注意，{{客户名称}} 满足 {{10}}天未跟进， 已经被收回到公海：{{公海名称}}
     */
    public static final String SFA_RECYCLING_REASON_TO_HIGHSEAS_NEW_RECORD_FOLLOW = "sfa.recycling.reason.to.high.seas.new.record.follow";

    /**
     *    注意，{0} 满足 {1} 天未成交， 已经被收回到公海：{2}
     *    注意，{{客户名称}} 满足 {{10}}天未成交， 已经被收回到公海：{{公海名称}}
     */
    public static final String SFA_RECYCLING_REASON_TO_HIGHSEAS_NEW_RECORD_DEAL = "sfa.recycling.reason.to.high.seas.new.record.deal";



    /**
     * {0} 已经 {1} 天没有新的成交
     */
    public static final String SFA_NO_HIGH_SEAS_DEAL_DAYS_REMIND_NEW_RECORD = "sfa.no.high.seas.deal.days.remind.new.record";

    /**
     * {0} 已经 {1} 天未跟进
     */
    public static final String SFA_NO_HIGH_SEAS_FOLLOW_UP_DAYS_REMIND_NEW_RECORD = "sfa.no.high.seas.follow.up.days.remind.new.record";
    /**
     * {0} 在公海 {1} 已经 {2} 天没有新的成交
     */
    public static final String SFA_HIGH_SEAS_DEAL_DAYS_REMIND_NEW_RECORD =  "sfa.high.seas.deal.days.remind.new.record";

    /**
     * {0} 在公海 {1} 已经 {2} 天未跟进
     */
    public static final String SFA_HIGH_SEAS_FOLLOW_UP_DAYS_REMIND_NEW_RECORD = "sfa.high.seas.follow.up.days.remind.new.record";

    /**
     * {0} 将在{1}天后被收回
     */
    public static final String SFA_WILL_RECYCLING_NEW_RECORD = "sfa.will.recycling.new.record";
    /**
     * {0} 在公海 {1} 将在{2}天后被收回
     */
    public static final String SFA_HIGH_SEADS_WILL_RECYCLING_NEW_RECORD = "sfa.high.seas.will.recycling.new.record";

    /**
     * 注意，您的 {0}  由于长时间未跟进/未转换，已经被收回到线索池
     */
    public static final String SFA_LEADS_RECYCED_TO_LEADS_POOL_NEW_RECORD = "sfa.leads.recived.to.leads.pool.new.record";

    /**
     * 注意，{0} 满足 {1}，已经被收回到线索池
     * 注意，{{线索姓名}} 满足{{10天未转换}}，已经被收回到线索池
     */
    public static final String SFA_LEADS_RECYClING_REASON = "sfa.leads.recycling.reason";

    /**
     * 注意，{0} 满足 {1}天未跟进，已经被收回到线索池
     * 注意，{{线索姓名}} 满足{{10}}天天未跟进，已经被收回到线索池
     */
    public static final String SFA_LEADS_RECYClING_REASON_FOLLOW = "sfa.leads.recycling.reason.follow";

    /**
     * 注意，{0} 满足 {1}小时未跟进，已经被收回到线索池
     * 注意，{{线索姓名}} 满足{{10}}小时未跟进，已经被收回到线索池
     */
    public static final String SFA_LEADS_RECYClING_REASON_FOLLOW_HOURS = "sfa.leads.recycling.reason.follow.hours";

    /**
     * 注意，{0} 满足 {1}天{2}小时未跟进，已经被收回到线索池
     * 注意，{{线索姓名}} 满足{1}天{2}小时未跟进，已经被收回到线索池
     */
    public static final String SFA_LEADS_RECYClING_REASON_FOLLOW_ALL = "sfa.leads.recycling.reason.follow.all";


    /**
     * 注意，{0} 满足 {1}天未转换，已经被收回到线索池
     * 注意，{{线索姓名}} 满足{{10}}天未转换，已经被收回到线索池
     */
    public static final String SFA_LEADS_RECYClING_REASON_TRANS = "sfa.leads.recycling.reason.trans";

    /**
     * 注意，{0} 满足 {1}小时未转换，已经被收回到线索池
     * 注意，{{线索姓名}} 满足{{10}}小时未转换，已经被收回到线索池
     */
    public static final String SFA_LEADS_RECYClING_REASON_TRANS_HOURS = "sfa.leads.recycling.reason.trans.hours";

    /**
     * 注意，{0} 满足 {1}天{2}小时未转换，已经被收回到线索池
     * 注意，{{线索姓名}} 满足{1}天{2}小时未转换，已经被收回到线索池
     */
    public static final String SFA_LEADS_RECYClING_REASON_TRANS_ALL = "sfa.leads.recycling.reason.trans.all";


    /**
     * 线索已被收回
     */
    public static final String SFA_LEADS_RECOVERD_NEW_RECORD = "sfa.leads.recoverd.new.record";

    /**
     * 线索即将被收回
     */
    public static final String SFA_LEADS_WILL_BE_RECOVERD_NEW_RECORD = "sfa.leads.will.be.recoverd.new.record";


    /**
     * {0}天未跟进
     */
    public static final String SFA_RECYCLING_REASON_FOLLOW = "sfa.recycling.reason.follow";

    /**
     * {0}小时未跟进
     */
    public static final String SFA_RECYCLING_REASON_HOURS_FOLLOW = "sfa.recycling.reason.hours.follow";

    /**
     * {0}天{1}小时未跟进
     */
    public static final String SFA_RECYCLING_REASON_DAYS_HOURS_FOLLOW = "sfa.recycling.reason.days.hours.follow";

    /**
     * {0}天未成交
     */
    public static final String SFA_RECYCLING_REASON_DEAL = "sfa.recycling.reason.deal";

    /**
     * {0}天未转换
     */
    public static final String SFA_RECYCLING_REASON_TRANS = "sfa.recycling.reason.trans";

    /**
     * {0}小时未转换
     */
    public static final String SFA_RECYCLING_REASON_HOURS_TRANS = "sfa.recycling.reason.hours.trans";
    /**
     * {0}天{1}小时未转换
     */
    public static final String SFA_RECYCLING_REASON_DAYS_HOURS_TRANS = "sfa.recycling.reason.days.hours.trans";



    /**
     * 成交状态日志记录：下 %s %s发生变更，触发客户成交规则，成交状态被更改为 %s 状态。
     */
    public static final String SFA_DEAL_STATUS_CHANGE_LOG_MSG = "sfa.deal.status.change.log.msg";

    /**
     * 成交状态日志记录：下 {1} 发生变更，触发客户成交规则，成交状态被更改为 {2} 状态。
     */
    public static final String SFA_DEAL_STATUS_CHANGE_NO_FIELDS_LOG_MSG = "sfa.deal.status.change.no.fields.log.msg";



}
