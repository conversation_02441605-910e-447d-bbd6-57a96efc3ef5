package com.facishare.crm.recycling.task.executor.util;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.helper.ConfigHelper;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.common.Strings;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/4/2 15:16
 */
@Component
@Slf4j
public class GrayUtils {

    /**
     * 读取版本灰度配置文件:fs-gray-sfa-follow
     */
    private static final FsGrayReleaseBiz grayFollow = FsGrayRelease.getInstance("sfa-follow");

    private CopyOnWriteArrayList<String> grayTenants = new CopyOnWriteArrayList();

    private Map<String, CopyOnWriteArrayList<String>> graySkipTenantObjects = new HashMap<>();

    /**
     * 读取版本灰度配置文件:fs-gray-sfa
     */
    private static final FsGrayReleaseBiz gray = FsGrayRelease.getInstance("sfa");

    /***
     * 读取版本灰度配置文件:fs-metadata-gray
     */
    @PostConstruct
    public void init() {
        ConfigFactory.getConfig("fs-metadata-gray", config -> grayTenants = new CopyOnWriteArrayList(Arrays.asList(config.get("gray_tenantIds").split(","))));
        grayTenants.removeIf(x -> Strings.isNullOrEmpty(String.valueOf(x)));


        ConfigFactory.getConfig("fs-gray-sfa-follow", config -> {
            graySkipTenantObjects = new HashMap<>();
            List<String> followSkips = Arrays.asList(config.get("object_data_crm_follow_skip").split(","));
            for (String s : followSkips) {
                String[] split = s.split(":");
                graySkipTenantObjects.put(split[0], new CopyOnWriteArrayList(Arrays.asList(split[1].split("\\|"))));
            }
        });
    }

    /**
     * 空   返回true ，不走灰度
     * 全网 -1或all
     *
     * @param tenantId
     * @return
     */
    public Boolean isFSMetadataGray(String tenantId) {
        if (CollectionUtils.empty(grayTenants)) {
            return true;
        }
        if (grayTenants.size() == 1 && ("-1".equalsIgnoreCase(grayTenants.get(0)) || "all".equalsIgnoreCase(grayTenants.get(0)))) {
            return true;
        }
        if (grayTenants.contains(tenantId)) {
            return true;
        }
        return false;
    }

    public Boolean isSkipObjectDataConsume(String tenantId, String apiName) {
        if (CollectionUtils.empty(graySkipTenantObjects)) {
            return false;
        }
        if (CollectionUtils.empty(graySkipTenantObjects.get(tenantId))) {
            return false;
        }
        if (graySkipTenantObjects.get(tenantId).size() == 1 && graySkipTenantObjects.get(tenantId).get(0).equals("*")) {
            return true;
        }
        if (graySkipTenantObjects.get(tenantId).size() == 1 && graySkipTenantObjects.get(tenantId).get(0).equals("-1")) {
            return false;
        }
        if (graySkipTenantObjects.get(tenantId).contains(apiName)) {
            return true;
        }
        return false;
    }

    public static boolean isGrayPaymentPlanStatus(String tenantId) {
        return gray.isAllow("payment_plan_new_task", tenantId);
    }

    public static boolean isGraySkipAccountToAddress(String tenantId) {
        return gray.isAllow("skip_account_to_address", tenantId);
    }

    public boolean isGrayDispatch(String tenantId) {
        return gray.isAllow("follow_dispatch", tenantId);
    }

    public boolean isSkipFollowObjectData(String tenantId) {
        if (Boolean.TRUE.equals(skipFollowByProfile(tenantId))){
            log.info("skipFollowByProfile isSkipFollowObjectData tenantId:{}", tenantId);
            return true;
        }
        return gray.isAllow("follow_object_data_skip", tenantId);
    }

    public boolean isSkipFollowObjectDataByApiName(String describeApiName) {
        return gray.isAllow("follow_object_data_skip_api_name", describeApiName);
    }

    public boolean isSkipFollowObjectDataSkipNoRule(String describeApiName) {
        return gray.isAllow("follow_object_data_skip_no_rule", describeApiName);
    }

    public Boolean skipObjectDataChangeTenantId(String tenantId) {
        if (Boolean.TRUE.equals(skipFollowByProfile(tenantId))){
            log.info("skipFollowByProfile skipObjectDataChangeTenantId tenantId:{}", tenantId);
            return true;
        }
        return gray.isAllow("skip_object_data_change_tenant_id", tenantId);
    }

    public static Boolean skipActionTenantId(String tenantId) {
        if (Boolean.TRUE.equals(skipFollowByProfile(tenantId))){
            log.info("skipFollowByProfile skipActionTenantId tenantId:{}", tenantId);
            return true;
        }
        return grayFollow.isAllow("skip_action_follow_tenant_id", tenantId);
    }

    public static Boolean actionFollowVIPTenantId(String tenantId) {
        if (Boolean.TRUE.equals(skipFollowByProfile(tenantId))){
            log.info("skipFollowByProfile actionFollowVIPTenantId tenantId:{}", tenantId);
            return true;
        }
        return grayFollow.isAllow("action_follow_vip_tenant_id", tenantId);
    }

    public static Boolean skipFeedTenantId(String tenantId) {
        if (Boolean.TRUE.equals(skipFollowByProfile(tenantId))){
            log.info("skipFollowByProfile skipFeedTenantId tenantId:{}", tenantId);
            return true;
        }
        return grayFollow.isAllow("skip_feed_follow_tenant_id", tenantId);
    }

    public static Boolean skipFollowByProfile(String tenantId) {
        //如果配置了白名单，不跳过
        if (grayFollow.isAllow("skip_follow_by_profile_whitelist", tenantId)){
            return false;
        }
        String profile = ConfigHelper.getProcessInfo().getProfile();
        if (profile == null){
            return false;
        }
        return grayFollow.isAllow("skip_follow_by_profile", profile);
    }


    /**
     * 是否批量分配，走的是MQ 而非XXLJOB
     *
     * @param tenantId
     * @return
     */
    public static boolean isLeadsBatchAllocateMQ(String tenantId) {
        return grayFollow.isAllow("leads_batch_allocate_mq", tenantId);
    }

    /**
     * 是否发送mq，计算关系
     *
     * @param tenantId
     * @return
     */
    public static boolean isSendContactMemberRelationshipMQ(String tenantId) {
        return grayFollow.isAllow("SFA_follow_contact_edit", tenantId);
    }

    /**
     * 是否灰度了主数据管控策略
     *
     * @param tenantId
     * @return
     */
    public static boolean isGrayMainControlStrategy(String tenantId) {
        return gray.isAllow("main_data_control_gray_tenantId", tenantId);
    }

    public static boolean isUpdateNewOpportunityLastModified(String tenantId) {
        return gray.isAllow("update_new_opportunity_last_modified", tenantId);
    }

    /**
     * 跳过 -10000 编辑操作
     * @param tenantId
     * @return
     */
    public static boolean isSkipEditBySystem(String tenantId) {
        return gray.isAllow("skip_edit_by_system", tenantId);
    }

    /**
     * 跟进规则转到慢队列
     * @param tenantId
     * @return
     */
    public static boolean isForward2Slow(String tenantId) {
        return gray.isAllow("follow_forward_to_slow", tenantId);
    }
}
