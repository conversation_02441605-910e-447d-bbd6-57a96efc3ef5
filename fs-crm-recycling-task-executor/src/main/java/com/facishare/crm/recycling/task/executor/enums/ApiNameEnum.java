package com.facishare.crm.recycling.task.executor.enums;

public enum ApiNameEnum {

    /**
     * 客户
     */
    ACCOUNT_OBJ("AccountObj"),

    /**
     * 销售线索
     */
    LEADS_OBJ("LeadsObj"),

    /**
     * 公海
     */
    HIGH_SEAS_OBJ("HighSeasObj"),

    /**
     * 联系人
     */
    CONTACT_OBJ("ContactObj"),

    /**
     * 线索池
     */
    LEADS_POOL_OBJ("LeadsPoolObj");

    private String apiName;


    ApiNameEnum(String apiName) {
        this.apiName = apiName;
    }

    public String getApiName() {
        return apiName;
    }

    public void setApiName(String apiName) {
        this.apiName = apiName;
    }


    public static ApiNameEnum apiNameOf(String apiName) {
        for (ApiNameEnum value : values()) {
            if (value.getApiName().equals(apiName)) {
                return value;
            }
        }
        return null;
    }



}
