package com.facishare.crm.recycling.task.executor.model.forecast;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.metadata.api.IObjectData;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.math.BigDecimal;
import java.util.Map;

public class ForecastTaskDetailRecord extends BaseForecastRecord implements ForecastTaskDetail {
    private static final long serialVersionUID = 1;
    @JsonIgnore
    @JSONField(serialize = false)
    private transient ForecastObject forecastObject;

    public ForecastTaskDetailRecord() {

    }

    public ForecastTaskDetailRecord(Map map) {
        super(map);
    }

    public ForecastTaskDetailRecord(IObjectData objectData) {
        super(objectData);
    }

    @Override
    public String getForecastTaskObjectId() {
        return get(FORECAST_TASK_OBJECT_ID, String.class);
    }

    @Override
    public void setForecastTaskObjectId(String forecastTaskObjectId) {
        set(FORECAST_TASK_OBJECT_ID, forecastTaskObjectId);
    }

    @Override
    public String getForecastRuleObjectId() {
        return get(FORECAST_RULE_OBJECT_ID, String.class);
    }

    @Override
    public void setForecastRuleObjectId(String forecastRuleObjectId) {
        set(FORECAST_RULE_OBJECT_ID, forecastRuleObjectId);
    }

    @Override
    public String getForecastObjectId() {
        return get(FORECAST_OBJECT_ID, String.class);
    }

    @Override
    public void setForecastObjectId(String forecastObjectId) {
        set(FORECAST_OBJECT_ID, forecastObjectId);
    }

    @Override
    public Long getForecastDate() {
        return get(FORECAST_DATE, Long.class);
    }

    @Override
    public void setForecastDate(Long forecastDate) {
        set(FORECAST_DATE, forecastDate);
    }

    @Override
    public BigDecimal getForecastAmount() {
        return get(FORECAST_AMOUNT, BigDecimal.class);
    }

    @Override
    public void setForecastAmount(BigDecimal forecastAmount) {
        set(FORECAST_AMOUNT, forecastAmount);
    }

    @Override
    public Boolean getAddManually() {
        return get(ADD_MANUALLY, Boolean.class);
    }

    @Override
    public void setAddManually(Boolean addManually) {
        set(ADD_MANUALLY, addManually);
    }

    @Override
    public Boolean getInCommitmentForecast() {
        return get(IN_COMMITMENT_FORECAST, Boolean.class);
    }

    @Override
    public void setInCommitmentForecast(Boolean inCommitmentForecast) {
        set(IN_COMMITMENT_FORECAST, inCommitmentForecast);
    }

    @Override
    public BigDecimal getBestPracticesForecastModel1() {
        return get(BEST_PRACTICES_FORECAST_MODEL1, BigDecimal.class);
    }

    @Override
    public void setBestPracticesForecastModel1(BigDecimal bestPracticesForecastModel1) {
        set(BEST_PRACTICES_FORECAST_MODEL1, bestPracticesForecastModel1);
    }

    @Override
    public BigDecimal getBestPracticesForecastModel2() {
        return get(BEST_PRACTICES_FORECAST_MODEL2, BigDecimal.class);
    }

    @Override
    public void setBestPracticesForecastModel2(BigDecimal bestPracticesForecastModel2) {
        set(BEST_PRACTICES_FORECAST_MODEL2, bestPracticesForecastModel2);
    }

    @Override
    public BigDecimal getBestPracticesForecastModel3() {
        return get(BEST_PRACTICES_FORECAST_MODEL3, BigDecimal.class);
    }

    @Override
    public void setBestPracticesForecastModel3(BigDecimal bestPracticesForecastModel3) {
        set(BEST_PRACTICES_FORECAST_MODEL3, bestPracticesForecastModel3);
    }

    @Override
    public BigDecimal getBestPracticesForecastModel4() {
        return get(BEST_PRACTICES_FORECAST_MODEL4, BigDecimal.class);
    }

    @Override
    public void setBestPracticesForecastModel4(BigDecimal bestPracticesForecastModel4) {
        set(BEST_PRACTICES_FORECAST_MODEL4, bestPracticesForecastModel4);
    }

    @Override
    public BigDecimal getBestPracticesForecastModel5() {
        return get(BEST_PRACTICES_FORECAST_MODEL5, BigDecimal.class);
    }

    @Override
    public void setBestPracticesForecastModel5(BigDecimal bestPracticesForecastModel5) {
        set(BEST_PRACTICES_FORECAST_MODEL5, bestPracticesForecastModel5);
    }

    @Override
    public BigDecimal getArtificialCommitmentForecastModel() {
        return get(ARTIFICIAL_COMMITMENT_FORECAST_MODEL, BigDecimal.class);
    }

    @Override
    public void setArtificialCommitmentForecastModel(BigDecimal artificialCommitmentForecastModel) {
        set(ARTIFICIAL_COMMITMENT_FORECAST_MODEL, artificialCommitmentForecastModel);
    }

    @Override
    public BigDecimal getStageWeightForecastModel() {
        return get(STAGE_WEIGHT_FORECAST_MODEL, BigDecimal.class);
    }

    @Override
    public void setStageWeightForecastModel(BigDecimal stageWeightForecastModel) {
        set(STAGE_WEIGHT_FORECAST_MODEL, stageWeightForecastModel);
    }

    @Override
    public BigDecimal getArtificialWeightForecastModel() {
        return get(ARTIFICIAL_WEIGHT_FORECAST_MODEL, BigDecimal.class);
    }

    @Override
    public void setArtificialWeightForecastModel(BigDecimal artificialWeightForecastModel) {
        set(ARTIFICIAL_WEIGHT_FORECAST_MODEL, artificialWeightForecastModel);
    }

    @Override
    public BigDecimal getAIWeightForecastModel() {
        return get(AI_WEIGHT_FORECAST_MODEL, BigDecimal.class);
    }

    @Override
    public void setAIWeightForecastModel(BigDecimal aiWeightForecastModel) {
        set(AI_WEIGHT_FORECAST_MODEL, aiWeightForecastModel);
    }

    @Override
    public ForecastObject getForecastObject() {
        return forecastObject;
    }

    @Override
    public void setForecastObject(ForecastObject forecastObject) {
        this.forecastObject = forecastObject;
    }

    @Override
    public boolean equals(Object obj) {
        return super.equals(obj);
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }
}