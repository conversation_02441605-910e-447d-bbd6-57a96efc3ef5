package com.facishare.crm.recycling.task.executor.consumer;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.recycling.task.executor.common.RecyclingFactory;
import com.facishare.crm.recycling.task.executor.common.RecyclingTaskGray;
import com.facishare.crm.recycling.task.executor.model.RecyclingMessage;
import com.facishare.crm.sfa.audit.log.context.SFALogContext;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.fxiaoke.rocketmq.util.MessageHelper;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-03-12 16:53
 */
@Slf4j
@Component
public class RecyclingBatchListener implements ApplicationListener<ContextRefreshedEvent> {

    private AutoConfMQPushConsumer consumer;

    @Autowired
    private RecyclingFactory recyclingFactory;

    @Autowired
    private RecyclingTaskGray recyclingTaskGray;


    @PostConstruct
    public void init() {
        consumer = new AutoConfMQPushConsumer("sfa-recalculate-consumer", "sfa-batch-recycling", (MessageListenerConcurrently) (msgs, context) -> {
            if (!msgs.isEmpty()) {
                for(MessageExt msg: msgs) {
                    // 取出traceContext
                    MessageHelper.fillContextFromMessage(TraceContext.get(), msg);
                    consumeMessage(msg);
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        });
    }


    @PreDestroy
    public void close() {
        consumer.close();
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (consumer != null && event.getApplicationContext().getParent() == null) {
            consumer.start();
        }
    }

    private void consumeMessage(MessageExt body) {
        try {
            RecyclingMessage message = JSON.parseObject(body.getBody(), RecyclingMessage.class);
            log.info("RecyclingBatchListener,msgId:{},reconsumeTimes:{},{}", body.getMsgId(),body.getReconsumeTimes(),message);

            if (recyclingTaskGray.skipTenantId(message.getTenantId())){
                log.warn("RecyclingBatchListener skipTenantId:{},objectId:{}",message.getTenantId(),message.getObjectId());
                return;
            }
            SFALogContext.putVariable("messageId",body.getMsgId());
            SFALogContext.putVariable("bizName","recycling_batch");
            SFALogContext.putVariable("cost1",System.currentTimeMillis() - body.getBornTimestamp());
            recyclingFactory.getRecyclingService(message.getObjectApiName()).execute(message);
        } catch (Exception e) {
            log.error("RecyclingBatchListener  consumeMessage error msgId:{}",body.getMsgId(),e);
            throw new RuntimeException(e);
        } finally {
            TraceContext.remove();
            SFALogContext.clearContext();
        }
    }


}
