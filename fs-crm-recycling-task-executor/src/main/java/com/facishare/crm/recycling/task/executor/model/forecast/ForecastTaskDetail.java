package com.facishare.crm.recycling.task.executor.model.forecast;

import com.facishare.paas.metadata.api.IObjectData;

import java.math.BigDecimal;

public interface ForecastTaskDetail extends CalculableForecastModel {
    String FORECAST_TASK_DETAIL_OBJECT_DESCRIBE_API_NAME = "ForecastTaskDetailObj";
    String FORECAST_TASK_OBJECT_ID = "forecast_task_object_id";
    String FORECAST_RULE_OBJECT_ID = "forecast_rule_object_id";
    String FORECAST_OBJECT_ID = "forecast_object_id";
    String FORECAST_OBJECT_API_NAME = "forecast_object_api_name";
    String FORECAST_OBJECT_AMOUNT_API_NAME = "forecast_object_amount_api_name";
    String FORECAST_OBJECT_DATE_API_NAME = "forecast_object_date_api_name";
    String FORECAST_OBJECT_FIELDS_SHOW = "forecast_object_fields_show";
    String FORECAST_DATE = "forecast_date";
    String FORECAST_AMOUNT = "forecast_amount";
    String ADD_MANUALLY = "add_manually";
    String IN_COMMITMENT_FORECAST = "in_commitment_forecast";
    String FORECAST_OBJECT = "forecast_object";

    String getForecastTaskObjectId();

    void setForecastTaskObjectId(String forecastTaskObjectId);

    String getForecastRuleObjectId();

    void setForecastRuleObjectId(String forecastRuleObjectId);

    String getForecastObjectId();

    void setForecastObjectId(String forecastObjectId);

    Long getForecastDate();

    void setForecastDate(Long forecastDate);

    BigDecimal getForecastAmount();

    void setForecastAmount(BigDecimal forecastAmount);

    Boolean getAddManually();

    void setAddManually(Boolean addManually);

    Boolean getInCommitmentForecast();

    void setInCommitmentForecast(Boolean inCommitmentForecast);

    ForecastObject getForecastObject();

    void setForecastObject(ForecastObject forecastObject);

    default String uniqueKey() {
        return uniqueKey(getForecastTaskObjectId(), getForecastObjectId());
    }

    static String uniqueKey(IObjectData objectData) {
        return uniqueKey(objectData.get(FORECAST_TASK_OBJECT_ID), objectData.get(FORECAST_OBJECT_ID));
    }

    static String ruleKey(IObjectData objectData) {
        return uniqueKey(objectData.get(FORECAST_RULE_OBJECT_ID), objectData.get(FORECAST_OBJECT_ID));
    }

    /**
     * @param forecastTaskObjectId 预测任务id
     * @param forecastObjectId     被预测对象id
     * @return 判重条件key
     */
    static String uniqueKey(Object forecastTaskObjectId, Object forecastObjectId) {
        return forecastTaskObjectId + UNIQUE_KEY_DELIMITER + forecastObjectId;
    }

    static String ruleKey(Object forecastRuleObjectId, Object forecastObjectId) {
        return forecastRuleObjectId + UNIQUE_KEY_DELIMITER + forecastObjectId;
    }
}
