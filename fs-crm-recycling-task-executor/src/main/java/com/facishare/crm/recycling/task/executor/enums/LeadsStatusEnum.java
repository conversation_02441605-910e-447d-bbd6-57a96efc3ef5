package com.facishare.crm.recycling.task.executor.enums;

/**
 * 线索状态
 */
@Deprecated
public enum LeadsStatusEnum {


    UNALLOCATED("1", "未分配"),
    TODO("2", "待处理"),
    CONVERTED("3", "已经转换"),
    FOLLOWING("4", "跟进中"),
    UNDECEIVE("5", "无效"),
    INVALID("99", "作废"),
    INEFFECTIVE("6", "未生效"),
    UNDER_REVIEW("7", "审核中"),
    IN_CHANGE("8", "变更中");


    private final String value;
    private final String label;

    LeadsStatusEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }


    public String getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }
}
