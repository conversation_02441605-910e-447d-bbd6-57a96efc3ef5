package com.facishare.crm.recycling.task.executor.service.impl;

import com.facishare.crm.recycling.task.executor.biz.CommonBiz;
import com.facishare.crm.recycling.task.executor.service.PartnerDataChangedService;
import com.facishare.crm.sfa.lto.common.LtoOrgCommonService;
import com.facishare.crm.sfa.lto.exception.ExceptionUtil;
import com.facishare.crm.sfa.lto.objectlimit.ObjectLimitRuleService;
import com.facishare.crm.sfa.lto.objectlimit.models.ObjectLimitRuleModel;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @Description 保有量规则计算
 */

@Slf4j
@Component
public class ObjectLimitPartnerDataChangedService implements PartnerDataChangedService {
    @Autowired
    private LtoOrgCommonService ltoOrgCommonService;
    @Autowired
    private ObjectLimitRuleService objectLimitBiz;
    @Autowired
    private CommonBiz commonBiz;

    private static final String PARTNER_API_NAME = "PartnerObj";

    @Override
    public void execute(String tenantId, List<String> partnerIds) {
        try {
            List<IObjectData> partnerDataList = commonBiz.getObjectByIds(tenantId, PARTNER_API_NAME, partnerIds, true);
            List<String> deletedPartnerIds = Lists.newArrayList();
            List<String> changedPartnerIds = Lists.newArrayList();
            if(CollectionUtils.empty(partnerDataList)) {
                deletedPartnerIds.addAll(partnerIds);
            } else {
                for(IObjectData objectData : partnerDataList) {
                    ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
                    if(ObjectLifeStatus.INVALID.getCode().equals(objectDataExt.getLifeStatus().getCode()) && Boolean.TRUE.equals(objectDataExt.isDeleted())) {
                        deletedPartnerIds.add(objectData.getId());
                    } else {
                        changedPartnerIds.add(objectData.getId());
                    }
                }
            }
            if(CollectionUtils.notEmpty(deletedPartnerIds)) {
                List<String> outUserIds = ltoOrgCommonService.getPartnerOutUserIds(tenantId, deletedPartnerIds);
                if(CollectionUtils.notEmpty(outUserIds)) {
                    objectLimitBiz.deleteEmployeeRuleByEmployeeIds(tenantId, outUserIds);
                    log.warn(String.format("ObjectLimitPartnerDataChangedService: tenantID-%s, delete employee limit rule employeeIds: %s", tenantId, String.join(",", outUserIds)));
                }
            }

            if(CollectionUtils.notEmpty(changedPartnerIds)) {
                List<ObjectLimitRuleModel.ObjectLimitRule> objectLimitRuleList = objectLimitBiz.getObjectLimitRuleByDataType(tenantId, ObjectLimitRuleModel.DataTypeEnum.PARTNER_CUSTOM.getCode());
                if(CollectionUtils.notEmpty(objectLimitRuleList)) {
                    for (ObjectLimitRuleModel.ObjectLimitRule objectLimitRule : objectLimitRuleList) {
                        Map<String, Boolean> fitMap =  objectLimitBiz.checkIsFitObjectLimitRule(tenantId, changedPartnerIds, objectLimitRule);
                        List notFitPartnerIds = Lists.newArrayList();
                        List fitPartnerIds = Lists.newArrayList();
                        for (Map.Entry<String, Boolean> entry : fitMap.entrySet()) {
                            if(Boolean.TRUE.equals(entry.getValue())) {
                                fitPartnerIds.add(entry.getKey());
                            } else {
                                notFitPartnerIds.add(entry.getKey());
                            }
                        }

                        if(CollectionUtils.notEmpty(notFitPartnerIds)) {
                            List<String> outUserIds = ltoOrgCommonService.getPartnerOutUserIds(tenantId, notFitPartnerIds);
                            if(CollectionUtils.notEmpty(outUserIds)) {
                                objectLimitBiz.deleteEmployeeRuleByEmployeeIds(tenantId, outUserIds);
                                log.warn(String.format("ObjectLimitPartnerDataChangedService: tenantID-%s, groupId-%s, delete employee limit rule employeeIds: %s", tenantId, objectLimitRule.getGroupId(), String.join(",", outUserIds)));
                            }
                        }

                        if(CollectionUtils.notEmpty(fitPartnerIds)) {
                            List<String> outUserIds = ltoOrgCommonService.getPartnerOutUserIds(tenantId, fitPartnerIds);
                            if(CollectionUtils.notEmpty(outUserIds)) {
                                List<String> existUserIds = objectLimitBiz.getEmployeeRuleByGroupId(tenantId, objectLimitRule.getObjectApiName(), objectLimitRule.getGroupId());
                                List<String> needInsertUserIds = Lists.newArrayList();
                                for(String userId : outUserIds) {
                                    if(!existUserIds.contains(userId)) {
                                        needInsertUserIds.add(userId);
                                    }
                                }
                                if(CollectionUtils.notEmpty(needInsertUserIds)) {
                                    objectLimitBiz.insertEmployeeRule(tenantId, objectLimitRule.getObjectApiName(), objectLimitRule.getGroupId(), objectLimitRule.getCalculateId(), needInsertUserIds);
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            ExceptionUtil.throwCommonBusinessException();
        }
    }
}
