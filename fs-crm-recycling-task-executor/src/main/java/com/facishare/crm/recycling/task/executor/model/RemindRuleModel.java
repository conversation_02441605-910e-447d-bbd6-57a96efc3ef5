package com.facishare.crm.recycling.task.executor.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.api.BackendOrBuilder;
import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020/1/2 16:17
 */
@Data
public class RemindRuleModel {
    @JSONField(name = "remind_rule_id")
    @JsonProperty(value = "remind_rule_id")
    String id;
    @JSONField(name = "data_id")
    @JsonProperty(value = "data_id")
    String dataId;
    @JSONField(name = "data_type")
    @JsonProperty(value = "data_type")
    Integer dataType;
    @JSONField(name = "deal_days")
    @JsonProperty(value = "deal_days")
    Integer dealDays;
    @JSONField(name = "flow_up_days")
    @JsonProperty(value = "flow_up_days")
    Integer flowUpDays;
    @JSONField(name = "rule_type")
    @JsonProperty(value = "rule_type")
    Integer ruleType;
    @JSONField(name = "group_id")
    @JsonProperty(value = "group_id")
    String groupId;

    @JSONField(name = "skip_holidays")
    @JsonProperty(value = "skip_holidays")
    Boolean skipHolidays;
}
