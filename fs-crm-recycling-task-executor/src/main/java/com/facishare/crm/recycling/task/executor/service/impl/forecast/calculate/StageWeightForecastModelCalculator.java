package com.facishare.crm.recycling.task.executor.service.impl.forecast.calculate;

import com.facishare.crm.recycling.task.executor.model.forecast.ForecastObject;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastRule;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastTask;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastTaskDetail;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.function.BiConsumer;
import java.util.function.Function;

class StageWeightForecastModelCalculator extends BooleanForecastRuleFieldCalculator {

    @Override
    public Function<ForecastTask, BigDecimal> taskGetter() {
        return ForecastTask::getStageWeightForecastModel;
    }

    @Override
    public BiConsumer<ForecastTask, BigDecimal> taskSetter() {
        return ForecastTask::setStageWeightForecastModel;
    }

    @Override
    public Function<ForecastTaskDetail, BigDecimal> detailGetter() {
        return ForecastTaskDetail::getStageWeightForecastModel;
    }

    @Override
    public BiConsumer<ForecastTaskDetail, BigDecimal> detailSetter() {
        return ForecastTaskDetail::setStageWeightForecastModel;
    }

    @Override
    public String taskApiName() {
        return ForecastTask.STAGE_WEIGHT_FORECAST_MODEL;
    }

    @Override
    Function<ForecastRule, Boolean> ruleGetter() {
        return ForecastRule::getStageWeightForecastModel;
    }

    @Override
    public BigDecimal calculate(ForecastTaskDetail detail, ForecastRule rule) {
        ForecastObject forecastObject = detail.getForecastObject();
        BigDecimal amount = forecastObject.get(rule.getForecastObjectAmountApiNameTarget(), BigDecimal.class);
        BigDecimal probability = forecastObject.get("probability", BigDecimal.class);
        if (amount != null && probability != null) {
            return amount.multiply(probability).divide(HUNDRED, 2, RoundingMode.HALF_UP);
        }
        return null;
    }

}
