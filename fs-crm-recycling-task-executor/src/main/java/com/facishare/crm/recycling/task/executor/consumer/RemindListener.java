package com.facishare.crm.recycling.task.executor.consumer;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.recycling.task.executor.common.RemindFactory;
import com.facishare.crm.recycling.task.executor.model.RemindMessage;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.fxiaoke.rocketmq.util.MessageHelper;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-03-14 17:14
 */
@Slf4j
@Component
public class RemindListener  implements ApplicationListener<ContextRefreshedEvent> {

    private AutoConfMQPushConsumer consumer;

    @Autowired
    private RemindFactory remindFactory;

    @PostConstruct
    public void init() {
        consumer = new AutoConfMQPushConsumer("fs-crm-recycling-task-remind", (MessageListenerConcurrently) (msgs, context) -> {
            if (!msgs.isEmpty()) {
                for(MessageExt msg: msgs) {
                    // 取出traceContext
                    MessageHelper.fillContextFromMessage(TraceContext.get(), msg);
                    consumeMessage(msg);
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        });
    }


    @PreDestroy
    public void close() {
        consumer.close();
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (consumer != null && event.getApplicationContext().getParent() == null) {
            consumer.start();
        }
    }


    private void consumeMessage(MessageExt body) {
        try {
            RemindMessage message = JSON.parseObject(body.getBody(), RemindMessage.class);
            log.info("RemindMessage msgId:{}:{}",body.getMsgId(),message);
            if (body.getReconsumeTimes() > 2){
                return;
            }
            remindFactory.getRemindService(message.getObjectApiName()).execute(message);
        } catch (Exception e) {
            log.error("RemindListener consumeMessage Exception msgId:{}",body.getMsgId(),e);
        }finally {
            TraceContext.remove();
        }
    }


}
