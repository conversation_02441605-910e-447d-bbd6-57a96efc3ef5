package com.facishare.crm.recycling.task.executor.util;

import com.facishare.paas.appframework.core.model.User;
import com.google.common.collect.Maps;

import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-07-07 23:28
 */

public class ProxyUtils {

    public static Map<String, String> getCrmHeader(String tenantId) {
        Map<String, String> headers = Maps.newHashMap();
        headers.put("x-fs-ei", tenantId);
        headers.put("x-fs-userInfo", User.SUPPER_ADMIN_USER_ID);
        return headers;
    }

}
