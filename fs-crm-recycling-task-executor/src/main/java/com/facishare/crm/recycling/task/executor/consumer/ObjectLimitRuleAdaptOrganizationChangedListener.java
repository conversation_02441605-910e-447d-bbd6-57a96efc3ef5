package com.facishare.crm.recycling.task.executor.consumer;

import com.facishare.crm.recycling.task.executor.common.RecyclingTaskGray;
import com.facishare.crm.recycling.task.executor.common.SfaRecyclingTaskRateLimiterService;
import com.facishare.crm.recycling.task.executor.service.ObjectLimitEmployeeService;
import com.facishare.crm.sfa.lto.objectlimit.ObjectLimitRuleService;
import com.facishare.crm.sfa.lto.objectlimit.models.ObjectLimitRuleModel;
import com.facishare.crm.sfa.lto.utils.TenantUtil;
import com.facishare.organization.api.event.OrganizationChangedListener;
import com.facishare.organization.api.event.organizationChangeEvent.DepartmentChangeEvent;
import com.facishare.organization.api.event.organizationChangeEvent.EmployeeChangeEvent;
import com.facishare.organization.api.event.organizationChangeEvent.TreeChangeEvent;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.type.DepartmentStatus;
import com.facishare.organization.api.model.type.EmployeeEntityStatus;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 执行器接收MQ入口
 */
@Slf4j
public class ObjectLimitRuleAdaptOrganizationChangedListener extends OrganizationChangedListener {
    @Autowired
    private ObjectLimitRuleService objectLimitBiz;
    @Autowired
    private SfaRecyclingTaskRateLimiterService sfaRecyclingTaskRateLimiterService;
    @Autowired
    private ObjectLimitEmployeeService objectLimitEmployeeService;
    @Autowired
    private RecyclingTaskGray recyclingTaskGray;
    @Autowired
    private TenantUtil tenantUtil;

    private static final int ALL_COMPANY_DEPARTMENT_ID = 999999;

    public ObjectLimitRuleAdaptOrganizationChangedListener() {
        super("object_limit_adapt_organization_changed");
    }

    @Override
    protected void onDepartmentChanged(DepartmentChangeEvent event) throws Throwable {
        if (tenantUtil.isExclusiveCloudEnterprise(String.valueOf(event.getEnterpriseId()))) {
            log.warn("{} is exclusive cloud ei", event.getEnterpriseId());
            return;
        }
        sfaRecyclingTaskRateLimiterService.getObjectLimitOrgLimiter().acquire();
        log.warn("onDepartmentChanged,event:{}", event);

        DepartmentDto newDepartment = event.getNewDepartmentDto();
        DepartmentDto oldDepartment = event.getOldDepartmentDto();
        String dataType = ObjectLimitRuleModel.DataTypeEnum.DEPARTMENT.getCode();

        //停用部门
        if (isStopDepartment(newDepartment, oldDepartment)) {
            String tenantId = String.valueOf(event.getEnterpriseId());
            String departmentId = String.valueOf(newDepartment.getDepartmentId());
            List<String> deletedIds = Lists.newArrayList(departmentId);
            List<ObjectLimitRuleModel.ObjectLimitRule> objectLimitRuleList = objectLimitBiz.getObjectLimitRuleByDataIds(tenantId, deletedIds, dataType);
            if (CollectionUtils.empty(objectLimitRuleList)) {
                return;
            }

            Set<String> objectApiNameSet = objectLimitRuleList.stream().map(x -> x.getObjectApiName())
                    .collect(Collectors.toSet());

            for(String objectApiName : objectApiNameSet) {
                Set<String> groupIdSet = objectLimitRuleList.stream()
                        .filter(x -> objectApiName.equals(x.getObjectApiName())).map(x -> x.getGroupId())
                        .collect(Collectors.toSet());
                if(CollectionUtils.empty(groupIdSet)){
                    continue;
                }

                for(String groupId : groupIdSet){
                    List<ObjectLimitRuleModel.ObjectLimitRule> tempLimitRuleList = objectLimitBiz.getObjectLimitRuleByGroupId(tenantId, objectApiName, groupId);
                    if(CollectionUtils.empty(tempLimitRuleList)){
                        continue;
                    }

//                    objectLimitBiz.deleteObjectLimitRuleByDataIds(tenantId, objectApiName, groupId, deletedIds, ObjectLimitRuleModel.DataTypeEnum.DEPARTMENT.getCode());
                    tempLimitRuleList.removeIf(x -> ObjectLimitRuleModel.DataTypeEnum.DEPARTMENT.getCode().equals(x.getDataType()) && deletedIds.contains(x.getDataId()));

                    List<String> limitEmployeeList = objectLimitBiz.getEmployeeRuleByGroupId(tenantId, objectApiName, groupId);
                    List<String> allLimitEmployeeList = objectLimitBiz.getObjectLimitRuleUserIds(tenantId, tempLimitRuleList);

                    List<String> deletedEmployeeList = limitEmployeeList.stream()
                            .filter(x -> !allLimitEmployeeList.contains(x)).collect(Collectors.toList());
                    if(CollectionUtils.notEmpty(deletedEmployeeList)){
                        objectLimitBiz.deleteEmployeeRuleByEmployeeIds(tenantId, objectApiName, groupId, deletedEmployeeList);
                        limitEmployeeList.removeIf(deletedEmployeeList::contains);
                        log.warn(String.format("ObjectLimitRuleAdaptOrganizationChangedListener-onDepartmentChanged: tenantID-%s, groupId-%s, delete employee limit rule employeeIds: %s", tenantId, groupId, String.join(",", deletedEmployeeList)));
                    }
                    List<String> insertEmployeeList = allLimitEmployeeList.stream()
                            .filter(x -> !limitEmployeeList.contains(x)).collect(Collectors.toList());

                    if(CollectionUtils.notEmpty(insertEmployeeList)){
                        String calculateId = tempLimitRuleList.get(0).getCalculateId();
                        objectLimitBiz.insertEmployeeRule(tenantId, objectApiName, groupId, calculateId, Lists.newArrayList(insertEmployeeList));
                    }
                }
            }
        }

        if(isDeleteDepartment(newDepartment)) {
            String tenantId = String.valueOf(event.getEnterpriseId());
            String departmentId = String.valueOf(newDepartment.getDepartmentId());
            objectLimitBiz.deleteObjectLimitRuleByDataIds(tenantId, Lists.newArrayList(departmentId), dataType);
        }
    }

    @Override
    protected void onTreeChanged(TreeChangeEvent event) throws Throwable {
        if (tenantUtil.isExclusiveCloudEnterprise(String.valueOf(event.getEnterpriseId()))) {
            log.warn("{} is exclusive cloud ei", event.getEnterpriseId());
            return;
        }

        sfaRecyclingTaskRateLimiterService.getObjectLimitOrgLimiter().acquire();
        //调整部门层级
        log.warn("onTreeChanged,event:{}", event);
        Set<String> departmentIds = Sets.newHashSet();

        if(CollectionUtils.notEmpty(event.getOldDepartmentMap())) {
            getAllChangedDepartment(event, departmentIds);
        }

        if(CollectionUtils.notEmpty(event.getNewDepartmentMap())) {
            getAllChangedDepartment(event, departmentIds);
        }

        if(CollectionUtils.empty(departmentIds)) {
            return;
        }

        String tenantId = String.valueOf(event.getEnterpriseId());
        objectLimitEmployeeService.calculateDepartment(tenantId, Lists.newArrayList(departmentIds));
    }

    private void getAllChangedDepartment(TreeChangeEvent event, Set<String> departmentIds) {
        for (Map.Entry<Integer, TreeChangeEvent.Department> departmentEntry : event.getOldDepartmentMap().entrySet()) {
            TreeChangeEvent.Department department = departmentEntry.getValue();
            if (department.getDepartmentId() != ALL_COMPANY_DEPARTMENT_ID) {
                departmentIds.add(String.valueOf(department.getDepartmentId()));
            }
            if (CollectionUtils.notEmpty(department.getAncestors())) {
                departmentIds.addAll(department.getAncestors().stream()
                        .filter(x -> x != ALL_COMPANY_DEPARTMENT_ID).map(x -> String.valueOf(x))
                        .collect(Collectors.toSet()));
            }
        }
    }

    @Override
    protected void onEmployeeChanged(EmployeeChangeEvent event) throws Throwable {
        if (tenantUtil.isExclusiveCloudEnterprise(String.valueOf(event.getEnterpriseId()))) {
            log.warn("{} is exclusive cloud ei", event.getEnterpriseId());
            return;
        }

        sfaRecyclingTaskRateLimiterService.getObjectLimitOrgLimiter().acquire();
        log.warn("onEmployeeChanged,event:{}", event);
        EmployeeDto newEmployee = event.getNewEmployeeDto();
        EmployeeDto oldEmployee = event.getOldEmployeeDto();
        String tenantId = String.valueOf(event.getEnterpriseId());
        String userId = String.valueOf(newEmployee.getEmployeeId());

        if (recyclingTaskGray.skipObjectLimitRuleAdaptOrgTenantId(tenantId)) {
            return;
        }

        //停用员工
        if (isStopEmployee(newEmployee, oldEmployee)) {
            log.warn("isStopEmployee->EmployeeId: {}", newEmployee.getEmployeeId());
            objectLimitBiz.deleteEmployeeRuleByEmployeeIds(tenantId, Lists.newArrayList(userId));
            log.warn(String.format("ObjectLimitRuleAdaptOrganizationChangedListener-onEmployeeChanged: tenantID-%s, delete employee limit rule employeeIds: %s", tenantId, userId));
            return;
        }

        if(isDeleteEmployee(newEmployee)) {
            log.warn("isDeleteEmployee->EmployeeId: {}", newEmployee.getEmployeeId());
            objectLimitBiz.deleteObjectLimitRuleByDataIds(tenantId, Lists.newArrayList(userId), ObjectLimitRuleModel.DataTypeEnum.EMPLOYEE.getCode());
            return;
        }

        List<ObjectLimitRuleModel.ObjectLimitEmployeeRule> employeeRuleList = objectLimitBiz.getEmployeeRuleByEmployeeId(tenantId, userId);
        List<ObjectLimitRuleModel.ObjectLimitEmployeeRule> insertEmployeeRuleList = Lists.newArrayList();
        List<ObjectLimitRuleModel.ObjectLimitEmployeeRule> deleteEmployeeRuleList = Lists.newArrayList();

        List<ObjectLimitRuleModel.ObjectLimitRule> objectLimitRuleList = objectLimitBiz.getObjectLimitRuleByDataType(tenantId, ObjectLimitRuleModel.DataTypeEnum.CUSTOM.getCode());
        if (CollectionUtils.notEmpty(objectLimitRuleList)){
            objectLimitEmployeeService.doCustomLimitRule(tenantId, userId, employeeRuleList, objectLimitRuleList,insertEmployeeRuleList, deleteEmployeeRuleList);
        }

        if(!isNewEmployee(newEmployee, oldEmployee) && !isEmployeeDeptChanged(newEmployee, oldEmployee)) {
            return;
        }
        List<String> dataIds = Lists.newArrayList();
        if(oldEmployee != null) {
            if(CollectionUtils.notEmpty(oldEmployee.getDepartmentIds())) {
                dataIds.addAll(oldEmployee.getDepartmentIds().stream().filter(x -> x > 0).map(x -> String.valueOf(x)).collect(Collectors.toList()));
            }
            if(CollectionUtils.notEmpty(oldEmployee.getMainDepartmentIds())) {
                dataIds.addAll(oldEmployee.getMainDepartmentIds().stream().filter(x -> x > 0).map(x -> String.valueOf(x)).collect(Collectors.toList()));
            }
            if(CollectionUtils.notEmpty(oldEmployee.getOtherDepartmentIds())) {
                dataIds.addAll(oldEmployee.getOtherDepartmentIds().stream().filter(x -> x > 0).map(x -> String.valueOf(x)).collect(Collectors.toList()));
            }
        }

        if(newEmployee != null) {
            if(CollectionUtils.notEmpty(newEmployee.getDepartmentIds())) {
                dataIds.addAll(newEmployee.getDepartmentIds().stream().filter(x -> x > 0).map(x -> String.valueOf(x)).collect(Collectors.toList()));
            }
            if(CollectionUtils.notEmpty(newEmployee.getMainDepartmentIds())) {
                dataIds.addAll(newEmployee.getMainDepartmentIds().stream().filter(x -> x > 0).map(x -> String.valueOf(x)).collect(Collectors.toList()));
            }
            if(CollectionUtils.notEmpty(newEmployee.getOtherDepartmentIds())) {
                dataIds.addAll(newEmployee.getOtherDepartmentIds().stream().filter(x -> x > 0).map(x -> String.valueOf(x)).collect(Collectors.toList()));
            }
        }
        objectLimitEmployeeService.resetEmployeeRule(tenantId, userId, employeeRuleList, insertEmployeeRuleList, deleteEmployeeRuleList, dataIds);
    }

    //停用部门
    private boolean isStopDepartment(DepartmentDto newDepartment, DepartmentDto oldDepartment) {
        return newDepartment != null && oldDepartment != null && newDepartment.getStatus() == DepartmentStatus.STOP
                && oldDepartment.getStatus() == DepartmentStatus.NORMAL;
    }

    private boolean isDeleteDepartment(DepartmentDto newDepartment) {
        return newDepartment != null && newDepartment.getStatus() == DepartmentStatus.DELETE;
    }

    //停用员工
    private boolean isStopEmployee(EmployeeDto newEmployee, EmployeeDto oldEmployee) {
        return newEmployee != null && oldEmployee != null && newEmployee.getStatus() == EmployeeEntityStatus.STOP
                && oldEmployee.getStatus() == EmployeeEntityStatus.NORMAL;
    }

    //启用员工
    private boolean isNormalEmployee(EmployeeDto newEmployee, EmployeeDto oldEmployee) {
        return newEmployee != null && oldEmployee != null && newEmployee.getStatus() == EmployeeEntityStatus.NORMAL
                && oldEmployee.getStatus() == EmployeeEntityStatus.STOP;
    }

    //停用员工
    private boolean isDeleteEmployee(EmployeeDto newEmployee) {
        return newEmployee != null && newEmployee.getStatus() == EmployeeEntityStatus.DELETE;
    }

    private boolean isChangeDepartment(Integer newDepartment, Integer oldDepartment) {
        return (newDepartment != null && oldDepartment != null) && (!newDepartment.equals(oldDepartment));
    }

    //新建员工
    private boolean isNewEmployee(EmployeeDto newEmployee, EmployeeDto oldEmployee) {
        return newEmployee != null && oldEmployee == null && !isDeleteEmployee(newEmployee);
    }

    private boolean isEmployeeDeptChanged(EmployeeDto newEmployee, EmployeeDto oldEmployee) {
        Set<String> oldDeptIds = Sets.newHashSet();
        Set<String> newDeptIds = Sets.newHashSet();
        if(oldEmployee != null) {
            if(CollectionUtils.notEmpty(oldEmployee.getDepartmentIds())) {
                oldDeptIds.addAll(oldEmployee.getDepartmentIds().stream().filter(x -> x > 0).map(x -> String.valueOf(x)).collect(Collectors.toList()));
            }
            if(CollectionUtils.notEmpty(oldEmployee.getMainDepartmentIds())) {
                oldDeptIds.addAll(oldEmployee.getMainDepartmentIds().stream().filter(x -> x > 0).map(x -> String.valueOf(x)).collect(Collectors.toList()));
            }
            if(CollectionUtils.notEmpty(oldEmployee.getOtherDepartmentIds())) {
                oldDeptIds.addAll(oldEmployee.getOtherDepartmentIds().stream().filter(x -> x > 0).map(x -> String.valueOf(x)).collect(Collectors.toList()));
            }
        }

        if(newEmployee != null) {
            if(CollectionUtils.notEmpty(newEmployee.getDepartmentIds())) {
                newDeptIds.addAll(newEmployee.getDepartmentIds().stream().filter(x -> x > 0).map(x -> String.valueOf(x)).collect(Collectors.toList()));
            }
            if(CollectionUtils.notEmpty(newEmployee.getMainDepartmentIds())) {
                newDeptIds.addAll(newEmployee.getMainDepartmentIds().stream().filter(x -> x > 0).map(x -> String.valueOf(x)).collect(Collectors.toList()));
            }
            if(CollectionUtils.notEmpty(newEmployee.getOtherDepartmentIds())) {
                newDeptIds.addAll(newEmployee.getOtherDepartmentIds().stream().filter(x -> x > 0).map(x -> String.valueOf(x)).collect(Collectors.toList()));
            }
        }
        if(oldDeptIds.size() != newDeptIds.size()) {
            return true;
        }
        return newDeptIds.stream().anyMatch(x -> !oldDeptIds.contains(x)) && oldDeptIds.stream().anyMatch(x -> !newDeptIds.contains(x));
    }
}
