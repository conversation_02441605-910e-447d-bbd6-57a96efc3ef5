package com.facishare.crm.recycling.task.executor.util;

public interface RecyclingRuleConstans {
    class Field{

        public static final String Tenant_Id = "tenant_id";
        public static final String Id = "id";
        public static final String Last_Modified_By = "last_modified_by";
        public static final String Created_By = "created_by";
        public static final String Last_Modified_Time = "last_modified_time";
        public static final String Create_Time = "create_time";
        public static final String Is_Deleted = "is_deleted";
        public static final String Object_Api_Name = "object_api_name";
        public static final String Target_Pool_Id = "target_pool_id";
        public static final String Recycling_Rule_Id = "recycling_rule_id";
        public static final String Priority = "priority";
        public static final String Data_Id = "data_id";
        public static final String Data_Type = "data_type";
        public static final String Recycling_Rule_Type = "recycling_rule_type";
        public static final String Deal_Days = "deal_days";
        public static final String Follow_Up_Days = "follow_up_days";
        public static final String High_Seas_Id = "high_seas_id";
        public static final String Group_Id = "group_id";
        public static final String Is_Include_Past_Time = "is_include_past_time";
        public static final String Wheres = "wheres";
        public static final String FUNCTION_API_NAME = "function_api_name";
        public static final String FUNCTION_NAME = "function_name";
        public static final String SKIP_HOLIDAYS = "skip_holidays";
    }
}
