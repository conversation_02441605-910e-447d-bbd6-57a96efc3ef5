package com.facishare.crm.recycling.task.executor.service.impl.forecast.calculate;

import com.facishare.crm.recycling.task.executor.model.forecast.ForecastRule;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastTask;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastTaskDetail;
import com.facishare.paas.metadata.api.DBRecord;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Function;

public interface ForecastModelCalculator {

    BiConsumer<ForecastTask, BigDecimal> taskSetter();

    Function<ForecastTask, BigDecimal> taskGetter();

    BiConsumer<ForecastTaskDetail, BigDecimal> detailSetter();

    Function<ForecastTaskDetail, BigDecimal> detailGetter();

    String taskApiName();

    default String detailApiName() {
        return taskApiName();
    }

    boolean acquire(ForecastRule rule);

    BigDecimal calculate(ForecastTaskDetail detail, ForecastRule rule);

    static boolean shouldCalculate(ForecastTaskDetail detail) {
        return detail.getAddManually() == null || !detail.getAddManually();
    }

    /**
     * 把预测结果存到明细相应字段并返回
     *
     * @param detail 预测明细
     * @param rule   预测规则
     * @return 预测结果
     */
    default BigDecimal accept(ForecastTaskDetail detail, ForecastRule rule) {
        BigDecimal result = calculate(detail, rule);
        if (result != null) {
            detailSetter().accept(detail, result);
            return result;
        } else {
            return BigDecimal.ZERO;
        }
    }

    /**
     * 把预测结果覆盖表头
     *
     * @param task 预测任务
     * @param rule 预测规则
     */
    default void accept(ForecastTask task, ForecastRule rule) {
        if (!acquire(rule) || !task.getForecastTaskDetails().isEmpty()) {
            return;
        }
        BigDecimal sum = BigDecimal.ZERO;
        for (ForecastTaskDetail detail : task.getForecastTaskDetails()) {
            if (shouldCalculate(detail)) {
                BigDecimal result = calculate(detail, rule);
                if (result != null) {
                    sum = sum.add(result);
                }
            }
        }
        accept(task, sum);
    }

    /**
     * 把预测结果加到表头
     *
     * @param task   预测任务
     * @param result 预测结果
     */
    default void acceptSum(ForecastTask task, BigDecimal result) {
        BigDecimal old = taskGetter().apply(task);
        if (old == null) {
            accept(task, result);
        } else {
            accept(task, old.add(result));
        }
    }

    /**
     * 把任务详情求和之后用表头减
     *
     * @param task   预测任务
     * @param result 预测结果
     */
    default void acceptDiff(ForecastTask task, BigDecimal result) {
        BigDecimal old = taskGetter().apply(task);
        if (old == null) {
            old = BigDecimal.ZERO;
        }
        accept(task, old.subtract(result));
    }

    /**
     * 把预测结果覆盖表头
     *
     * @param task   预测任务
     * @param result 预测结果
     */
    default void accept(ForecastTask task, BigDecimal result) {
        taskSetter().accept(task, result);
    }

    /**
     * 计算单个详请变化
     *
     * @param task    预测任务
     * @param rule    预测规则
     * @param origin  原始计算金额
     * @param current 最新计算金额
     */
    default boolean acceptChange(ForecastTask task, ForecastRule rule, ForecastTaskDetail origin, ForecastTaskDetail current) {
        BigDecimal cv = shouldCalculate(current) ? accept(current, rule) : BigDecimal.ZERO;
        BigDecimal ov = detailGetter().apply(origin);
        if (ov == null) {
            acceptSum(task, cv);
            return true;
        } else if (ov.compareTo(cv) == 0) {
            return false;
        } else {
            BigDecimal diff = cv.subtract(ov); // 现在 - 之前 = 变化量
            acceptSum(task, diff); // 对变化量做加法
            return true;
        }
    }

    @Getter
    enum Register {
        BEST_PRACTICES_1(new BestPracticesForecastModel1Calculator()),
        BEST_PRACTICES_2(new BestPracticesForecastModel2Calculator()),
        BEST_PRACTICES_3(new BestPracticesForecastModel3Calculator()),
        BEST_PRACTICES_4(new BestPracticesForecastModel4Calculator()),
        BEST_PRACTICES_5(new BestPracticesForecastModel5Calculator()),
        ARTIFICIAL_COMMITMENT(new ArtificialCommitmentForecastModelCalculator()),
        STAGE_WEIGHT(new StageWeightForecastModelCalculator()),
        ARTIFICIAL_WEIGHT(new ArtificialWeightForecastModelCalculator()),
        AI_WEIGHT(new AIWeightForecastModelCalculator()),
        ;

        private final ForecastModelCalculator calculator;

        Register(ForecastModelCalculator calculator) {
            this.calculator = calculator;
        }

        public static ForecastModelCalculator nameOf(String name) {
            for (Register register : values()) {
                if (register.name().equals(name)) {
                    return register.calculator;
                }
            }
            return null;
        }

        /**
         * 计算单个详请变化
         *
         * @param task   预测任务
         * @param rule   预测规则
         * @param origin 最新计算金额
         */
        public static boolean acceptChange(ForecastTask task, ForecastRule rule, ForecastTaskDetail origin, ForecastTaskDetail current) {
            boolean hasChanged = false;
            ForecastModelCalculator[] calculators = acquireForecastModelCalculator(rule);
            for (ForecastModelCalculator calculator : calculators) {
                if (calculator.acquire(rule)) {
                    hasChanged |= calculator.acceptChange(task, rule, origin, current);
                }
            }
            return hasChanged;
        }

        /**
         * 把任务详情求和之后加到表头
         *
         * @param task 预测任务
         * @param rule 预测规则
         */
        public static void acceptSum(ForecastTask task, ForecastRule rule) {
            // 1. 根据calculator.acquire(rule)过滤出有效的calculator->acquired
            ForecastModelCalculator[] acquired = acquireForecastModelCalculator(rule);
            // 2. detail+acquired计算->计算结果赋值给detail->计算结果求和->求和结果operator到task
            if (task.getForecastTaskDetails().isEmpty()) {
                for (ForecastModelCalculator calculator : acquired) {
                    calculator.acceptSum(task, BigDecimal.ZERO);
                }
            } else {
                BigDecimal[] sums = new BigDecimal[acquired.length];
                Arrays.fill(sums, BigDecimal.ZERO);
                task.getForecastTaskDetails().stream().filter(ForecastModelCalculator::shouldCalculate).forEach(detail -> {
                    for (int i = 0; i < acquired.length; i++) {
                        sums[i] = sums[i].add(acquired[i].accept(detail, rule)); // detail+acquired计算->计算结果赋值给detail->计算结果求和
                    }
                });
                for (int i = 0; i < acquired.length; i++) {
                    acquired[i].acceptSum(task, sums[i]);
                }
            }
        }

        /**
         * 把任务详情历史值求和之后用表头减
         *
         * @param task 预测任务
         */
        public static void acceptOriginDiff(ForecastTask task) {
            if (task.getForecastTaskDetails().isEmpty()) {
                return;
            }
            Register[] registers = values();
            BigDecimal[] sums = new BigDecimal[registers.length];
            Arrays.fill(sums, BigDecimal.ZERO);
            task.getForecastTaskDetails().stream().filter(ForecastModelCalculator::shouldCalculate).forEach(detail -> {
                        for (int i = 0; i < registers.length; i++) {
                            BigDecimal value = registers[i].calculator.detailGetter().apply(detail);
                            if (value != null) {
                                sums[i] = sums[i].add(value);
                            }
                        }
                    }
            );
            for (int i = 0; i < registers.length; i++) {
                registers[i].calculator.acceptDiff(task, sums[i]);
            }
        }

        public static ForecastModelCalculator[] acquireForecastModelCalculator(ForecastRule rule) {
            if (rule.getForecastModelCalculators() == null) {
                ForecastModelCalculator[] acquired = new ForecastModelCalculator[values().length];
                int len = 0;
                for (Register register : values()) {
                    if (register.calculator.acquire(rule)) {
                        acquired[len++] = register.calculator;
                    }
                }
                if (len == 0) {
                    throw new IllegalArgumentException("None calculator acquired");
                }
                ForecastModelCalculator[] calculators = Arrays.copyOf(acquired, len);
                rule.setForecastModelCalculators(calculators);
                return calculators;
            } else {
                return rule.getForecastModelCalculators();
            }
        }

        public static List<String> generateUpdateTaskFieldsList() {
            Function<ForecastModelCalculator, String> function = ForecastModelCalculator::taskApiName;
            return generateUpdateFieldsList(values(), function.compose(ForecastModelCalculator.Register::getCalculator));
        }

        public static List<String> generateUpdateDetailFieldsList() {
            Function<ForecastModelCalculator, String> function = ForecastModelCalculator::detailApiName;
            return generateUpdateFieldsList(values(), function.compose(ForecastModelCalculator.Register::getCalculator));
        }

        public static List<String> generateUpdateTaskFieldsList(ForecastRule rule) {
            return generateUpdateFieldsList(acquireForecastModelCalculator(rule), ForecastModelCalculator::taskApiName);
        }

        public static List<String> generateUpdateDetailFieldsList(ForecastRule rule) {
            return generateUpdateFieldsList(acquireForecastModelCalculator(rule), ForecastModelCalculator::detailApiName);
        }

        private static <T> List<String> generateUpdateFieldsList(T[] array, Function<T, String> function) {
            List<String> list = new ArrayList<>();
            for (T t : array) {
                list.add(function.apply(t));
            }
            list.add(DBRecord.LAST_MODIFIED_TIME);
            return list;
        }
    }
}