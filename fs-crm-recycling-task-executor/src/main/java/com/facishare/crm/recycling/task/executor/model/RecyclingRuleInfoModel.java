package com.facishare.crm.recycling.task.executor.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class RecyclingRuleInfoModel {
    @JSONField(name = "recycling_rule_id")
    @JsonProperty(value = "recycling_rule_id")
    String id;
    @JSONField(name = "priority")
    @JsonProperty(value = "priority")
    Integer priority;
    @JSONField(name = "data_id")
    @JsonProperty(value = "data_id")
    String dataId;
    @JSONField(name = "recycling_rule_type")
    @JsonProperty(value = "recycling_rule_type")
    Integer recyclingRuleType;
    @JSONField(name = "deal_days")
    @JsonProperty(value = "deal_days")
    Integer dealDays;
    @JSONField(name = "group_id")
    @JsonProperty(value = "group_id")
    String groupId;
    @JSONField(name = "data_name")
    @JsonProperty(value = "data_name")
    String dataName;
    @JSONField(name = "is_include_past_time")
    @JsonProperty(value = "is_include_past_time")
    Boolean isIncludePastTime;
    @JSONField(name = "follow_up_days")
    @JsonProperty(value = "follow_up_days")
    Integer followUpDays;
    @JSONField(name = "update_time")
    @JsonProperty(value = "update_time")
    Long updateTime;
    @JSONField(name = "wheres")
    @JsonProperty(value = "wheres")
    String wheres;
    @JSONField(name = "target_pool_id")
    @JsonProperty(value = "target_pool_id")
    String targetPoolId;
    @JSONField(name = "target_pool_name")
    @JsonProperty(value = "target_pool_name")
    String targetPoolName;
    @JSONField(name = "data_type")
    @JsonProperty(value = "data_type")
    Integer dataType;
    @JSONField(name = "recycling_remind_rule_list")
    @JsonProperty(value = "recycling_remind_rule_list")
    List<RecyclingRemindRuleModel> recyclingRemindRuleList;

    @JSONField(name = "function_api_name")
    @JsonProperty(value = "function_api_name")
    private String functionApiName;

    @JSONField(name = "function_name")
    @JsonProperty(value = "function_name")
    private String functionName;

    @JSONField(name = "skip_holidays")
    @JsonProperty(value = "skip_holidays")
    private Boolean skipHolidays;

}
