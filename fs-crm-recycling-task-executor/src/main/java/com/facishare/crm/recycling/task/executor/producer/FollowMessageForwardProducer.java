package com.facishare.crm.recycling.task.executor.producer;

import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Optional;

@Slf4j
@Component
public class FollowMessageForwardProducer implements InitializingBean, ApplicationListener<ContextClosedEvent> {
    
    private static final String TOPIC = "sfa-follow";
    private static final String CONFIG_NAME = "fs-crm-task-sfa-mq.ini";
    private static final String CONFIG_SECTION_NAME = "follow-message-forward-producer"; // 请确保配置文件中存在此配置节
    

    public static final String TAG_CRM_ACTION = "CRM_ACTION";
    public static final String TAG_BATCH_OBJECT_DATA = "batch-object-data-tag";
    public static final String TAG_OBJECT_DATA = "object-data-tag";

    private AutoConfMQProducer producer;

    /**
     * 发送 CRM_ACTION 类型的消息
     * @param messageBody 消息内容
     */
    public void sendCrmActionMessage(String messageBody) {
        sendMessage(TOPIC, TAG_CRM_ACTION, messageBody);
    }

    /**
     * 发送批量对象数据变更类型的消息 (batch-object-data-tag)
     * @param messageBody 消息内容
     */
    public void sendBatchObjectDataMessage(String messageBody) {
        sendMessage(TOPIC, TAG_BATCH_OBJECT_DATA, messageBody);
    }

    /**
     * 发送单个对象数据变更类型的消息 (object-data-tag)
     * @param messageBody 消息内容
     */
    public void sendObjectDataMessage(String messageBody) {
        sendMessage(TOPIC, TAG_OBJECT_DATA, messageBody);
    }

    private void sendMessage(String topic, String tags, String messageBody) {
        try {
            Message message = new Message(topic, tags, messageBody.getBytes(StandardCharsets.UTF_8));
            SendResult result = producer.send(message);
            log.info("Message sent successfully to topic [{}], tags [{}], msgId [{}], body [{}]", topic, tags, result.getMsgId(), messageBody);
        } catch (Exception e) {
            log.error("Failed to send message to topic [{}], tags [{}], body [{}]", topic, tags, messageBody, e);
            // 根据实际需求，这里可以抛出自定义异常或进行其他错误处理
        }
    }

    @Override
    public void afterPropertiesSet() {
        try {
            producer = new AutoConfMQProducer(CONFIG_NAME, CONFIG_SECTION_NAME);
            log.info("FollowMessageForwardProducer initialized with config name [{}] and section [{}]", CONFIG_NAME, CONFIG_SECTION_NAME);
        } catch (Exception e) {
            log.error("Failed to initialize FollowMessageForwardProducer with config name [{}] and section [{}]", CONFIG_NAME, CONFIG_SECTION_NAME, e);
            // 阻止 Spring 容器继续加载或者根据策略处理
            throw new RuntimeException("Failed to initialize FollowMessageForwardProducer", e);
        }
    }

    @Override
    public void onApplicationEvent(ContextClosedEvent contextRefreshedEvent) {
        Optional.ofNullable(producer).ifPresent(p -> {
            try {
                p.close();
                log.info("FollowMessageForwardProducer closed.");
            } catch (Exception e) {
                log.error("Error closing FollowMessageForwardProducer", e);
            }
        });
    }
}
