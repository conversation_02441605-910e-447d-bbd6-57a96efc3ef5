package com.facishare.crm.recycling.task.executor.common;

import com.facishare.crm.recycling.task.executor.enums.ApiNameEnum;
import com.facishare.crm.recycling.task.executor.service.RemindService;
import com.facishare.crm.recycling.task.executor.service.impl.AccountRemindService;
import com.facishare.crm.recycling.task.executor.service.impl.LeadsRemindService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-03-20 16:55
 */
@Component
public class RemindFactory {

    @Autowired
    private AccountRemindService accountRemindService;

    @Autowired
    private LeadsRemindService leadsRemindService;


    private static final Map<String, RemindService> REMIND_SERVICE = new HashMap<>();

    @PostConstruct
    public void init(){
        REMIND_SERVICE.put(ApiNameEnum.ACCOUNT_OBJ.getApiName(), accountRemindService);
        REMIND_SERVICE.put(ApiNameEnum.LEADS_OBJ.getApiName(), leadsRemindService);
    }

    public RemindService getRemindService(String apiName){
        return REMIND_SERVICE.get(apiName);
    }
}
