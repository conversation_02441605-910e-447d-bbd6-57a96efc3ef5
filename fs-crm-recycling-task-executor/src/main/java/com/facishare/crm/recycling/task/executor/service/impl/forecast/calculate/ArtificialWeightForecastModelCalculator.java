package com.facishare.crm.recycling.task.executor.service.impl.forecast.calculate;

import com.facishare.crm.recycling.task.executor.model.forecast.ForecastObject;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastRule;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastTask;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastTaskDetail;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.function.BiConsumer;
import java.util.function.Function;

class ArtificialWeightForecastModelCalculator extends BooleanForecastRuleFieldCalculator {

    @Override
    public Function<ForecastTask, BigDecimal> taskGetter() {
        return ForecastTask::getArtificialWeightForecastModel;
    }

    @Override
    public BiConsumer<ForecastTask, BigDecimal> taskSetter() {
        return ForecastTask::setArtificialWeightForecastModel;
    }

    @Override
    public Function<ForecastTaskDetail, BigDecimal> detailGetter() {
        return ForecastTaskDetail::getArtificialWeightForecastModel;
    }

    @Override
    public BiConsumer<ForecastTaskDetail, BigDecimal> detailSetter() {
        return ForecastTaskDetail::setArtificialWeightForecastModel;
    }

    @Override
    public String taskApiName() {
        return ForecastTask.ARTIFICIAL_WEIGHT_FORECAST_MODEL;
    }

    @Override
    Function<ForecastRule, Boolean> ruleGetter() {
        return ForecastRule::getArtificialWeightForecastModel;
    }

    @Override
    public BigDecimal calculate(ForecastTaskDetail detail, ForecastRule rule) {
        ForecastObject forecastObject = detail.getForecastObject();
        BigDecimal amount = forecastObject.get(rule.getForecastObjectAmountApiNameTarget(), BigDecimal.class);
        BigDecimal promisedProbability = forecastObject.get("promised_probability", BigDecimal.class);
        if (amount != null && promisedProbability != null) {
            return amount.multiply(promisedProbability).divide(HUNDRED, 2, RoundingMode.HALF_UP);
        }
        return null;
    }

}