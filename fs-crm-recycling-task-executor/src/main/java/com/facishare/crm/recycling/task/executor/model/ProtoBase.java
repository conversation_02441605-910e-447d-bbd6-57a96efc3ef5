package com.facishare.crm.recycling.task.executor.model;

import com.facishare.common.fsi.CanProto;
import io.protostuff.LinkedBuffer;
import io.protostuff.ProtobufIOUtil;
import io.protostuff.Schema;
import io.protostuff.runtime.RuntimeSchema;
import java.io.Serializable;

public class ProtoBase implements CanProto, Serializable {
    public ProtoBase() {
    }

    public byte[] toProto() {
        Schema schema = RuntimeSchema.getSchema(this.getClass());
        return ProtobufIOUtil.toByteArray(this, schema, LinkedBuffer.allocate(256));
    }

    public void fromProto(byte[] bytes) {
        Schema schema = RuntimeSchema.getSchema(this.getClass());
        ProtobufIOUtil.mergeFrom(bytes, this, schema);
    }
}
