package com.facishare.crm.recycling.task.executor.consumer;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.lto.utils.TenantUtil;
import org.apache.rocketmq.common.message.MessageExt;
import com.facishare.crm.recycling.task.executor.common.SfaRecyclingTaskRateLimiterService;
import com.facishare.crm.recycling.task.executor.service.impl.ObjectLimitUserGroupChangedService;
import com.facishare.crm.sfa.lto.rest.models.UserGroupModel;
import com.facishare.paas.appframework.common.mq.RocketMQMessageListener;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 执行器接收MQ入口
 */
@Slf4j
public class ObjectLimitRuleAdaptUserGroupChangedListener implements RocketMQMessageListener {
    @Autowired
    private ObjectLimitUserGroupChangedService objectLimitUserGroupChangedService;
    @Autowired
    private SfaRecyclingTaskRateLimiterService sfaRecyclingTaskRateLimiterService;
    @Autowired
    private TenantUtil tenantUtil;

    @Override
    public void consumeMessage(List<MessageExt> messages) {
        if (CollectionUtils.empty(messages)) {
            return;
        }

        for (MessageExt message : messages) {
            sfaRecyclingTaskRateLimiterService.getObjectLimitUserGroupLimiter().acquire();
            log.info("message:{}", message);
            consumeMessage(message);
        }
    }

    private void consumeMessage(MessageExt body) {
        try {
            UserGroupModel.UserGroupChangedMessage message = JSON.parseObject(body.getBody(),
                    UserGroupModel.UserGroupChangedMessage.class);
            log.info("msgId:{},ObjectLimitUserGroupMessage:{}", body.getMsgId(),message);
            if (tenantUtil.isExclusiveCloudEnterprise(message.getTenantId())) {
                log.info("{} is exclusive cloud ei", message.getTenantId());
                return;
            }
            objectLimitUserGroupChangedService.execute(message);
        } catch (Exception e) {
            log.error("ObjectLimitRuleAdaptUserGroupChangedListener consumeMessage {}",e);
            throw new RuntimeException(e);
        }
    }
}
