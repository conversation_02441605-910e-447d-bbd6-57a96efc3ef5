package com.facishare.crm.recycling.task.executor.model.forecast;

interface ForecastModel extends BaseForecast {
    String BEST_PRACTICES_FORECAST_MODEL1 = "best_practices_forecast_model1";
    String BEST_PRACTICES_FORECAST_MODEL2 = "best_practices_forecast_model2";
    String BEST_PRACTICES_FORECAST_MODEL3 = "best_practices_forecast_model3";
    String BEST_PRACTICES_FORECAST_MODEL4 = "best_practices_forecast_model4";
    String BEST_PRACTICES_FORECAST_MODEL5 = "best_practices_forecast_model5";
    String ARTIFICIAL_COMMITMENT_FORECAST_MODEL = "artificial_commitment_forecast_model";
    String ARTIFICIAL_WEIGHT_FORECAST_MODEL = "artificial_weight_forecast_model";
    String STAGE_WEIGHT_FORECAST_MODEL = "stage_weight_forecast_model";
    String AI_WEIGHT_FORECAST_MODEL = "ai_weight_forecast_model";
}
