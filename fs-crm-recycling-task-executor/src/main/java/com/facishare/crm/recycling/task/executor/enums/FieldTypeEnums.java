package com.facishare.crm.recycling.task.executor.enums;


public enum FieldTypeEnums {
    SELECT_ONE("select_one"),
    CURRENCY("currency"),
    DATE("date"),
    DATE_TIME("date_time"),
    EMAIL("email"),
    EMBEDDED_OBJECT("embedded_object"),
    FILE_ATTACHMENT("file_attachment"),
    IMAGE("image"),
    LONG_TEXT("long_text"),
    NUMBER("number"),
    OBJECT_REFERENCE("object_reference"),
    PERCENTILE("percentile"),
    PHONE_NUMBER("phone_number"),
    SELECT_MANY("select_many"),
    TEXT("text"),
    TIME("time"),
    TRUE_OR_FALSE("true_or_false"),
    URL("url"),
    TAG("tag"),
    EMBEDDED_OBJECT_LIST("embedded_object_list"),
    FORMULA("formula"),
    ARRAY("array"),
    COMPOSITE_ARRAY("composite_array"),
    AUTO_NUMBER("auto_number"),
    E<PERSON>L<PERSON><PERSON><PERSON>("employee"),
    DEPARTMENT("department"),
    LOCATION("location"),
    MULTI_LEVEL_SELECT_ONE("multi_level_select_one"),
    RECORD_TYPE("record_type"),
    SUMMARY("summary"),
    COUNTRY("country"),
    PROVINCE("province"),
    CITY("city"),
    DISTRICT("district"),
    QUOTE("quote"),
    GROUP("group"),
    COUNT("count"), //统计字段
    SIGNATURE("signature"),  //签字
    MASTER_DETAIL("master_detail"),  //主从字段
    LOCK_RULE("lock_rule"),  //锁定规则
    UseScope("use_scope"), //对象使用范围
    RICH_TEXT("rich_text");

    public String type;

    FieldTypeEnums(String type) {
        this.type = type;
    }

    public static FieldTypeEnums valueEnumOf(String value) {
        for (FieldTypeEnums compareType : values()) {
            if (compareType.type.equals(value)) {
                return compareType;
            }
        }
        return null;
    }
}
