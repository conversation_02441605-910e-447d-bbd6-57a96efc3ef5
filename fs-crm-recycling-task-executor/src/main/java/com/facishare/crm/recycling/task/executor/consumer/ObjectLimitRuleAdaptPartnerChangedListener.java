package com.facishare.crm.recycling.task.executor.consumer;

import org.apache.rocketmq.common.message.MessageExt;
import com.facishare.crm.recycling.task.executor.common.SfaRecyclingTaskRateLimiterService;
import com.facishare.crm.recycling.task.executor.service.impl.ObjectLimitPartnerChangedService;
import com.facishare.paas.appframework.common.mq.RocketMQMessageListener;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 执行器接收MQ入口
 */
@Slf4j
public class ObjectLimitRuleAdaptPartnerChangedListener implements RocketMQMessageListener {
    @Autowired
    private ObjectLimitPartnerChangedService objectLimitPartnerChangedService;
    @Autowired
    private SfaRecyclingTaskRateLimiterService sfaRecyclingTaskRateLimiterService;

    @Override
    public void consumeMessage(List<MessageExt> messages) {
        if (CollectionUtils.empty(messages)) {
            return;
        }

        for (MessageExt message : messages) {
            sfaRecyclingTaskRateLimiterService.getObjectLimitPartnerLimiter().acquire();
            log.info("message:{}", message);
            consumeMessage(message);
        }
    }

    private void consumeMessage(MessageExt message) {
        try {
            log.info("msgId:{},ObjectLimitRuleAdaptPartnerChangedListener message:{}", message.getMsgId(), message);
            objectLimitPartnerChangedService.execute(message);
        } catch (Exception e) {
            log.error("ObjectLimitRuleAdaptPartnerChangedListener consumeMessage，{}", message.getMsgId(),e);
            throw new RuntimeException(e);
        }
    }
}
