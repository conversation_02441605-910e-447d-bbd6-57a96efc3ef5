//package com.facishare.crm.recycling.task.executor.consumer;
//
//import com.alibaba.fastjson.JSON;
//import com.facishare.crm.sfa.lto.equityrelationship.model.EquityRelationshipDataModel;
//import com.facishare.crm.sfa.lto.equityrelationship.service.EquityRelationshipDataSyncService;
//import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
//import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
//import org.apache.rocketmq.common.message.MessageExt;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.ApplicationListener;
//import org.springframework.context.event.ContextRefreshedEvent;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.PostConstruct;
//import javax.annotation.PreDestroy;
//import java.nio.charset.Charset;
//import java.nio.charset.StandardCharsets;
//
///**
// * <AUTHOR> lik
// * @date : 2023/7/8 16:17
// */
//
//@Component
//@Slf4j
//public class EquityRelationshipDataSyncListener implements ApplicationListener<ContextRefreshedEvent> {
//
//    private AutoConfMQPushConsumer consumer;
//    @Autowired
//    private EquityRelationshipDataSyncService equityRelationshipDataSyncService;
//
//    @PostConstruct
//    public void init() {
//        consumer = new AutoConfMQPushConsumer("sfa-recalculate-consumer", "sfa-equity-relationship-data-sync-consumer",(MessageListenerConcurrently) (msgs, context) -> {
//            if (!msgs.isEmpty()) {
//                for(MessageExt msg: msgs) {
//                    consumeMessage(msg);
//                }
//            }
//            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
//        });
//    }
//
//    @Override
//    public void onApplicationEvent(ContextRefreshedEvent event) {
//        if (consumer != null && event.getApplicationContext().getParent() == null) {
//            consumer.start();
//        }
//    }
//    @PreDestroy
//    public void close() {
//        consumer.close();
//    }
//
//    private void consumeMessage(MessageExt messageExt) {
//
//        if(messageExt.getReconsumeTimes()>2){
//            return;
//        }
//        String body = new String(messageExt.getBody(), StandardCharsets.UTF_8);
//        try {
//            EquityRelationshipDataModel.DataSyncArg arg = JSON.parseObject(body, EquityRelationshipDataModel.DataSyncArg.class);
//            equityRelationshipDataSyncService.execute(arg);
//        } catch (Exception e) {
//            log.error("EquityRelationshipDataSyncListener consumeMessage msgId:{},e:",messageExt.getMsgId(),e);
//            throw new RuntimeException(e);
//        }
//    }
//}
