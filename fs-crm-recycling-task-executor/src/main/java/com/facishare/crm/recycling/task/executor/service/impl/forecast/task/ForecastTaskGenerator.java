package com.facishare.crm.recycling.task.executor.service.impl.forecast.task;

import com.facishare.crm.recycling.task.executor.enums.forecast.ForecastTaskEnums;
import com.facishare.crm.recycling.task.executor.model.forecast.*;
import com.facishare.crm.recycling.task.executor.service.impl.forecast.calculate.ForecastModelCalculator;
import com.facishare.paas.metadata.api.MultiRecordType;

import java.util.*;

public final class ForecastTaskGenerator {

    private ForecastTaskGenerator() {

    }

    public static List<ForecastTask> generateTaskWithoutDetail(ForecastRule rule, Map<String, String> uniqueKeyMap) {
        List<ForecastPeriod> periods = rule.getForecastPeriods();
        if (periods == null) {
            periods = ForecastPeriodMatcher.initPeriod(rule);
        }
        List<ForecastTask> result = new ArrayList<>(rule.getForecastApplyRangeTarget().size() * periods.size());
        for (Map.Entry<String, String> entry : rule.getForecastApplyRangeTarget().entrySet()) {
            String owner = entry.getKey();
            for (ForecastPeriod period : periods) {
                if (!uniqueKeyMap.containsKey(ForecastTask.uniqueKey(rule.getId(), owner, period.getLeft()))) {
                    ForecastTask task = generateTask(rule, owner, period);
                    task.setForecastTaskDetails(Collections.emptyList());
                    task.setDataOwnDepartment(Collections.singletonList(entry.getValue()));
                    ForecastModelCalculator.Register.acceptSum(task, rule);
                    result.add(task);
                }
            }
        }
        return result;
    }

    public static Map<String, List<ForecastTaskDetail>> generateTaskDetail(List<ForecastObject> objects, List<ForecastRule> rules, Map<String, String> uniqueKeyMap) {
        Map<String, List<ForecastTaskDetail>> result = new HashMap<>();
        for (ForecastObject object : objects) {
            for (ForecastRule rule : rules) {
                List<ForecastPeriod> periods = rule.getForecastPeriods();
                if (periods == null) {
                    periods = ForecastPeriodMatcher.initPeriod(rule);
                }
                ForecastPeriod period = ForecastPeriodMatcher.matchPeriod(object.getForecastDate(rule.getForecastObjectDateApiName()), periods);
                List<String> owners = object.getOwner();
                if (period != null && owners != null && !owners.isEmpty()) {
                    String taskId = uniqueKeyMap.get(ForecastTask.uniqueKey(rule.getId(), owners.get(0), period.getLeft()));
                    if (taskId != null) {
                        ForecastTaskDetail detail = generateTaskDetail(object, rule);
                        detail.setForecastTaskObjectId(taskId);
                        detail.setOwner(owners);
                        List<ForecastTaskDetail> details = result.computeIfAbsent(taskId, k -> new ArrayList<>());
                        details.add(detail);
                    }
                }
            }
        }
        return result;
    }

    public static ForecastTask generateTask(ForecastRule rule, String owner, ForecastPeriod period) {
        ForecastTaskRecord task = new ForecastTaskRecord();
        task.setForecastRuleObjectId(rule.getId());
        task.setForecastDateSplitType(rule.getForecastDateSplitType());
        task.setForecastStartDate(period.getLeft());
        task.setForecastEndDate(period.getRight());
        task.setForecastMonthGroup(period.toMonthGroupString());
        task.setForecastQuarterGroup(period.toQuarterGroupString());
        task.setBizStatus(ForecastTaskEnums.BizStatus.UNCOMMITTED.getValue());
        task.setForecastRule(rule);
        task.setOwner(Collections.singletonList(owner));
        task.setTenantId(rule.getTenantId());
        task.setDescribeApiName(ForecastTask.FORECAST_TASK_OBJECT_DESCRIBE_API_NAME);
        task.setRecordType(MultiRecordType.RECORD_TYPE_DEFAULT);
        task.setPackage("CRM");
        task.setDeleted(Boolean.FALSE);
        task.set("life_status", "normal");
        task.set("lock_status", "0");
        return task;
    }

    public static ForecastTaskDetail generateTaskDetail(ForecastObject object, ForecastRule rule, ForecastTask task) {
        ForecastTaskDetail detail = generateTaskDetail(object, rule);
        detail.setForecastTaskObjectId(task.getId());
        detail.setOwner(task.getOwner());
        detail.setDataOwnDepartment(task.getDataOwnDepartment());
        return detail;
    }

    public static ForecastTaskDetail generateTaskDetail(ForecastObject object, ForecastRule rule) {
        ForecastTaskDetailRecord detail = new ForecastTaskDetailRecord();
        detail.setTenantId(rule.getTenantId());
        detail.setForecastRuleObjectId(rule.getId());
        detail.setForecastObjectId(object.getId());
        detail.setForecastDate(object.getForecastDate(rule.getForecastObjectDateApiName()));
        detail.setForecastAmount(object.getForecastAmount(rule.getForecastObjectAmountApiName()));
        detail.setForecastObject(object);
        detail.setAddManually(Boolean.FALSE);
        detail.setDescribeApiName(ForecastTaskDetail.FORECAST_TASK_DETAIL_OBJECT_DESCRIBE_API_NAME);
        detail.setRecordType(MultiRecordType.RECORD_TYPE_DEFAULT);
        detail.setPackage("CRM");
        detail.setDeleted(Boolean.FALSE);
        detail.set("life_status", "normal");
        detail.set("lock_status", "0");
        return detail;
    }

}
