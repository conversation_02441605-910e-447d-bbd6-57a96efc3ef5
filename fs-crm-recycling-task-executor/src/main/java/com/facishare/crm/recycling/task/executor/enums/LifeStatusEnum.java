package com.facishare.crm.recycling.task.executor.enums;

/**
 * 生命状态
 */
public enum LifeStatusEnum {


    /**
     * 未生效
     */
    INEFFECTIVE("ineffective", "未生效"),

    /**
     * 审核中
     */
    UNDER_REVIEW("under_review", "审核中"),

    /**
     * 变更中
     */
    IN_CHANGE("in_change", "变更中"),

    /**
     * 正常
     */
    NORMAL("normal", "正常"),


    /**
     * 作废
     */
    INVALID("invalid", "作废");


    private final String value;
    private final String label;

    LifeStatusEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }


    public String getValue() {
        return value;
    }


    public String getLabel() {
        return label;
    }
}


