package com.facishare.crm.recycling.task.executor.model.forecast;

import java.util.Objects;

public final class ForecastPeriod {

    private Integer year;
    private Integer month;
    private Integer quarter;
    private Long left;
    private Long right;

    public ForecastPeriod(Integer year, Integer month, Integer quarter, Long left, Long right) {
        this.year = year;
        this.month = month;
        this.quarter = quarter;
        this.left = left;
        this.right = right;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Integer getMonth() {
        return month;
    }

    public void setMonth(Integer month) {
        this.month = month;
    }

    public Integer getQuarter() {
        return quarter;
    }

    public void setQuarter(Integer quarter) {
        this.quarter = quarter;
    }

    public Long getLeft() {
        return left;
    }

    public void setLeft(Long left) {
        this.left = left;
    }

    public Long getRight() {
        return right;
    }

    public void setRight(Long right) {
        this.right = right;
    }

    @Override // 谨慎修改
    public boolean equals(Object obj) {
        if (obj == this) {
            return true;
        } else if (!(obj instanceof ForecastPeriod)) {
            return false;
        } else {
            ForecastPeriod other = (ForecastPeriod) obj;
            return Objects.equals(this.getLeft(), other.getLeft()) && Objects.equals(this.getRight(), other.getRight());
        }
    }

    @Override // 谨慎修改
    public int hashCode() {
        return Objects.hashCode(this.getLeft()) ^ Objects.hashCode(this.getRight());
    }

    // 产品暂定不多语
    public String toMonthGroupString() {
        Integer monthInt = getMonth();
        String monthStr = String.valueOf(monthInt);
        if (monthInt != null && monthInt > 0 && monthInt < 10) {
            monthStr = "0" + monthStr;
        }
        return getYear() + "-" + monthStr;
    }

    // 产品暂定不多语
    public String toQuarterGroupString() {
        return getYear() + "Q" + getQuarter();
    }

}
