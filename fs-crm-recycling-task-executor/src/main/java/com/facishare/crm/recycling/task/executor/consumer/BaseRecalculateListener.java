package com.facishare.crm.recycling.task.executor.consumer;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.recycling.task.executor.common.RecalculateFactory;
import com.facishare.crm.recycling.task.executor.common.RecyclingTaskGray;
import com.facishare.crm.recycling.task.executor.model.RecalculateMessage;
import com.facishare.crm.recycling.task.executor.service.RecyclingRuleCacheService;
import com.facishare.crm.recycling.task.executor.util.CommonUtils;
import com.facishare.crm.sfa.audit.log.context.SFALogContext;
import com.facishare.crm.sfa.lto.common.SFAJedisLock;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.fxiaoke.rocketmq.util.MessageHelper;
import com.github.jedis.support.JedisCmd;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/2/11 15:07
 */

@Component
@Slf4j
public abstract class BaseRecalculateListener {

    public static final String CONFIG_NAME = "sfa-recalculate-consumer";

    public static final String TAG = "follow_calculate";
    public static final String TAG_VIP = "follow_calculate_vip";
    public static final String SFA_OVERTIME_FOLLOW_TAG = "sfa_overtime_follow";


    public static final String SECTION_NAME = "sfa-recalculate";
    public static final String SECTION_NAME_VIP = "sfa-recalculate-vip";
    public static final String SFA_OVERTIME_FOLLOW_SECTION = "sfa-overtime-follow";

    @Autowired
    private RecalculateFactory recalculateFactory;

    @Autowired
    private RecyclingTaskGray recyclingTaskGray;

    @Autowired
    private RecyclingRuleCacheService recyclingRuleCacheService;

    @Autowired
    protected JedisCmd SFAJedisCmd;

    public AutoConfMQPushConsumer getConsumer(String sectionName) {
        return new AutoConfMQPushConsumer(CONFIG_NAME, sectionName, (MessageListenerConcurrently) (msgs, context) -> {
            if (!msgs.isEmpty()) {
                for (MessageExt msg : msgs) {
                    // 取出traceContext
                    MessageHelper.fillContextFromMessage(TraceContext.get(), msg);
                    consumeMessage(msg);
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        });
    }

    abstract String getConsumerName();

    public void consumeMessage(MessageExt body) {
        try {
            RecalculateMessage message = JSON.parseObject(body.getBody(), RecalculateMessage.class);
            log.info("newRecalculateMessage, msgId:{},reconsumeTimes:{},RecalculateMessage:{}", body.getMsgId(), body.getReconsumeTimes(), message);
            if (recyclingTaskGray.skipTenantId(message.getTenantId())) {
                log.warn("newRecalculateListener skipTenantId:{},objectId:{}", message.getTenantId(), message.getObjectId());
                return;
            }
            if (recalculateFactory.getRecalculateService(message.getObjectApiName()) == null) {
                return;
            }

            if (recyclingRuleCacheService.isSkipWithoutRule(message)){
                log.warn("BaseRecalculateListener skipWithoutRule:{},objectId:{}", message.getTenantId(), message.getObjectId());
                return;
            }
            SFALogContext.putVariable("bizName", getConsumerName());
            SFALogContext.putVariable("msg",body);
            if (Boolean.TRUE.equals(recyclingTaskGray.recalculateLockGray(message.getTenantId(), message.getObjectApiName()))) {
                String lockKey = CommonUtils.getRecalculateLockKey(message);
                SFAJedisLock jedisLock = new SFAJedisLock(SFAJedisCmd, lockKey);
                boolean locked = lock(jedisLock, lockKey);
                log.info("jedisLock lock:{},lockKey:{}", locked, lockKey);
                recalculateFactory.getRecalculateService(message.getObjectApiName()).execute(message);
                if (locked) {
                    unLock(jedisLock);
                    log.info("jedisLock unLock lockKey:{}", lockKey);
                }
            } else {
                recalculateFactory.getRecalculateService(message.getObjectApiName()).execute(message);
            }
        } catch (Exception e) {
            log.error("newRecalculateListener consumeMessage msgId:{}", body.getMsgId(), e);
            throw new RuntimeException(e);
        } finally {
            TraceContext.remove();
            SFALogContext.clearContext();
        }
    }

    /**
     * 未获取到锁，说明，同一数据已经在计算中，可延迟一秒继续计算。
     *
     * @param lock
     * @param lockKey
     * @return
     */
    protected boolean lock(SFAJedisLock lock, String lockKey) {
        if (!lock.tryLock()) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("lock error:{}", lockKey, e);
            }
            return false;
        }
        return true;
    }

    protected void unLock(SFAJedisLock lock) {
        lock.unlock();
    }


}
