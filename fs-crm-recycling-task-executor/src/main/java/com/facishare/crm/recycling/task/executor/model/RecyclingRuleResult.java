package com.facishare.crm.recycling.task.executor.model;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-03-05 15:33
 */
@Data
public class RecyclingRuleResult {

    /**
     * value : {"RecyclingRuleList":[{"RecyclingRuleID":"c96f4b0fb6b64540b1247f6231ffe380","Priority":1,"DataID":"6fc7745f7cbf4e078dc011dc0f9106f8","DataType":1,"RecyclingRuleType":2,"DealDays":1,"FollowUpDays":1,"HighSeasID":"6fc7745f7cbf4e078dc011dc0f9106f8","GroupID":"33ae1cb6b815401cb847b6eeb57e614b","RecyclingFilterList":[{"RecyclingFilterID":"541f4ef962ff4170a3e62e461e6f6e00","RecyclingRuleID":"c96f4b0fb6b64540b1247f6231ffe380","DataID":"6fc7745f7cbf4e078dc011dc0f9106f8","FieldName":"Name","FieldType":2,"FieldOrder":0,"Compare":3,"FieldValue":"gchr回收"},{"RecyclingFilterID":"89e934db91f24cafb31c99ee79766c13","RecyclingRuleID":"c96f4b0fb6b64540b1247f6231ffe380","DataID":"6fc7745f7cbf4e078dc011dc0f9106f8","FieldName":"Source","FieldType":8,"FieldOrder":0,"Compare":1,"FieldValue":"1"}],"DataName":null,"HighSeasName":"gchr_test","IsIncludePastTime":true,"RecyclingRemindRuleList":[{"RecyclingRemindRuleID":"dd629df760ac44c1ace8f4d37d571bfb","RecyclingRuleID":"c96f4b0fb6b64540b1247f6231ffe380","DataID":"6fc7745f7cbf4e078dc011dc0f9106f8","RuleType":2,"RemindDays":1,"GroupID":"33ae1cb6b815401cb847b6eeb57e614b"}]},{"RecyclingRuleID":"b609384cb47e4266ba449c3407229d0f","Priority":2,"DataID":"6fc7745f7cbf4e078dc011dc0f9106f8","DataType":1,"RecyclingRuleType":1,"DealDays":0,"FollowUpDays":0,"HighSeasID":"6fc7745f7cbf4e078dc011dc0f9106f8","GroupID":"33ae1cb6b815401cb847b6eeb57e614b","RecyclingFilterList":[{"RecyclingFilterID":"1a0d960dec7f4398b99c00b6cd166006","RecyclingRuleID":"b609384cb47e4266ba449c3407229d0f","DataID":"6fc7745f7cbf4e078dc011dc0f9106f8","FieldName":"Source","FieldType":8,"FieldOrder":0,"Compare":1,"FieldValue":"6"},{"RecyclingFilterID":"77f7428b77cd482bb31969c14b970096","RecyclingRuleID":"b609384cb47e4266ba449c3407229d0f","DataID":"6fc7745f7cbf4e078dc011dc0f9106f8","FieldName":"Name","FieldType":2,"FieldOrder":0,"Compare":3,"FieldValue":"gchr收回"}],"DataName":null,"HighSeasName":"gchr_test","IsIncludePastTime":true,"RecyclingRemindRuleList":[]}]}
     * success : true
     * message :
     * errorCode : 0
     */

    private Content value;
    private boolean success;
    private String message;
    private int errorCode;


    @Data
    @Builder
    public static class Content {
        private List<RecyclingRule> RecyclingRuleList;
    }
}
