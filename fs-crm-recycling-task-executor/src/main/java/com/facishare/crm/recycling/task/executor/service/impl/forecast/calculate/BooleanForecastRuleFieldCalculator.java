package com.facishare.crm.recycling.task.executor.service.impl.forecast.calculate;

import com.facishare.crm.recycling.task.executor.model.forecast.ForecastRule;

import java.math.BigDecimal;
import java.util.function.Function;

abstract class BooleanForecastRuleFieldCalculator implements ForecastModelCalculator {

    protected static final BigDecimal HUNDRED = new BigDecimal(100);

    @Override
    public boolean acquire(ForecastRule rule) {
        Boolean acquired = ruleGetter().apply(rule);
        boolean flag = acquired != null && acquired;
        if (flag && rule.getForecastObjectAmountApiNameTarget() == null) {
            rule.setForecastObjectAmountApiNameTarget(rule.get(ForecastRule.FORECAST_OBJECT_AMOUNT_API_NAME, String.class));
        }
        return flag;
    }

    abstract Function<ForecastRule, Boolean> ruleGetter();
}
