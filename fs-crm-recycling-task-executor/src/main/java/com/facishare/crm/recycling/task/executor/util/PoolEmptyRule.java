package com.facishare.crm.recycling.task.executor.util;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.recycling.task.executor.model.PoolEmptyRuleModel;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/3/10 10:28
 */
public class PoolEmptyRule {
    private String poolApiName;

    private Map<String, EmptyRule> emptyRuleMap;

    private PoolEmptyRule() {
        this.emptyRuleMap = Maps.newHashMap();
    }
    public Map<String, EmptyRule> getEmptyRuleMap() {
        return emptyRuleMap;
    }

    public String getPoolIdByObjectData(IObjectData objectData) {
        String describeApiName = objectData.getDescribeApiName();
        if (Utils.HIGHSEAS_API_NAME.equals(describeApiName)) {
            return getStringValue(objectData, "high_seas_id", "");

        } else if (Utils.LEADS_POOL_API_NAME.equals(describeApiName)) {
            return getStringValue(objectData, "leads_pool_id", "");
        }
        return null;
    }

    private String getStringValue(IObjectData objectData, String key, String defaultValue) {
        if (objectData == null || StringUtils.isEmpty(key)) {
            return defaultValue;
        }
        Object tempValue = objectData.get(key);
        if (tempValue != null) {
            return tempValue.toString();
        }
        return defaultValue;
    }

    public EmptyRule getEmptyRuleById(String poolId) {
        return this.emptyRuleMap.get(poolId);
    }

    /**
     * @param data 客户线或线索数据
     * @return
     */
    public EmptyRule getEmptyRule(IObjectData data) {
        String poolId = getPoolIdByObjectData(data);
        if (StringUtils.isBlank(poolId)) {
            return null;
        }
        return getEmptyRuleById(poolId);
    }

    /**
     * 详情页和列表页
     */
    public void fillEmptyRule() {
        Collection<EmptyRule> emptyRules = getEmptyRuleMap().values();
        for (EmptyRule emptyRule : emptyRules) {
            IObjectData poolData = emptyRule.getPoolData();
            poolData.set(PoolEmptyRuleModel.IS_RECYCLING_OUT_TEAM_MEMBER, emptyRule.getRecyclingOutTeamMember());
            poolData.set(PoolEmptyRuleModel.IS_RECYCLING_OUT_ORDINARY_TEAM_MEMBER, emptyRule.getRecyclingOutOrdinaryMember());
            poolData.set(PoolEmptyRuleModel.IS_CLEAN_OUT_OWNER, emptyRule.getCleanOutOwner());
        }
    }

    public static class Builder {
        private List<IObjectData> poolDataList;

        public Builder poolData(@NotNull List<IObjectData> poolDataList) {
            this.poolDataList = poolDataList;
            return this;
        }

        public PoolEmptyRule build() {
            PoolEmptyRule poolEmptyRule = new PoolEmptyRule();
            for (IObjectData poolData : poolDataList) {
                Boolean recyclingTeamMembers = ObjectDataUtils.getBooleanOrDefault(poolData, PoolEmptyRuleModel.IS_RECYCLING_TEAM_MEMBER, null);
                Boolean recyclingOutOwnerMember = ObjectDataUtils.getBooleanOrDefault(poolData, PoolEmptyRuleModel.IS_RECYCLING_OUT_TEAM_MEMBER, null);
                Boolean recyclingOutOrdinaryMembers = ObjectDataUtils.getBooleanOrDefault(poolData, PoolEmptyRuleModel.IS_RECYCLING_OUT_ORDINARY_TEAM_MEMBER, null);
                Boolean cleanOwner = ObjectDataUtils.getBooleanOrDefault(poolData, PoolEmptyRuleModel.IS_CLEAN_OWNER, null);
                Boolean cleanOutOwner = ObjectDataUtils.getBooleanOrDefault(poolData, PoolEmptyRuleModel.IS_CLEAN_OUT_OWNER, null);
                if (recyclingOutOwnerMember == null || recyclingOutOrdinaryMembers == null) {
                    if (Boolean.TRUE.equals(recyclingTeamMembers)) {
                        recyclingOutOwnerMember = true;
                        recyclingOutOrdinaryMembers = true;
                    } else {
                        recyclingOutOwnerMember = false;
                        recyclingOutOrdinaryMembers = false;
                    }
                }
                if (cleanOutOwner == null) {
                    cleanOutOwner = Boolean.TRUE.equals(cleanOwner);
                }
                EmptyRule emptyRule = new EmptyRule(poolData.getId(),
                        recyclingTeamMembers,
                        recyclingOutOwnerMember,
                        recyclingOutOrdinaryMembers,
                        cleanOwner,
                        cleanOutOwner,
                        poolData,
                        poolData.getDescribeApiName());
                poolEmptyRule.getEmptyRuleMap().put(emptyRule.poolId, emptyRule);
            }
            return poolEmptyRule;
        }
    }

    public static class EmptyRule {
        private String poolId;
        private Boolean recyclingTeamMember;
        private Boolean recyclingOutTeamMember;
        private Boolean recyclingOutOrdinaryMember;
        private Boolean cleanOwner;
        private Boolean cleanOutOwner;
        private IObjectData poolData;
        private String poolApiName;

        public EmptyRule(String poolId,
                         Boolean recyclingTeamMember,
                         Boolean recyclingOutTeamMember,
                         Boolean recyclingOutOrdinaryMember,
                         Boolean cleanOwner,
                         Boolean cleanOutOwner,
                         IObjectData poolData,
                         String poolApiName) {
            this.recyclingTeamMember = recyclingTeamMember;
            this.recyclingOutTeamMember = recyclingOutTeamMember;
            this.recyclingOutOrdinaryMember = recyclingOutOrdinaryMember;
            this.cleanOwner = cleanOwner;
            this.cleanOutOwner = cleanOutOwner;
            this.poolData = poolData;
            this.poolId = poolId;
            this.poolApiName = poolApiName;
        }

        @Override
        public String toString() {
            return "EmptyRule{" +
                    "poolId='" + poolId + '\'' +
                    ", recyclingTeamMember=" + recyclingTeamMember +
                    ", recyclingOutTeamMember=" + recyclingOutTeamMember +
                    ", recyclingOutOrdinaryMember=" + recyclingOutOrdinaryMember +
                    ", cleanOwner=" + cleanOwner +
                    ", cleanOutOwner=" + cleanOutOwner +
                    ", poolData=" + poolData +
                    ", poolApiName='" + poolApiName + '\'' +
                    '}';
        }

        public Boolean getRecyclingTeamMember() {
            return recyclingTeamMember;
        }

        public String getPoolId() {
            return poolId;
        }

        public Boolean getRecyclingOutTeamMember() {
            return recyclingOutTeamMember;
        }

        public Boolean getRecyclingOutOrdinaryMember() {
            return recyclingOutOrdinaryMember;
        }

        public Boolean getCleanOwner() {
            return cleanOwner;
        }

        public Boolean getCleanOutOwner() {
            return cleanOutOwner;
        }

        public IObjectData getPoolData() {
            return poolData;
        }

        public String getPoolApiName() {
            return poolApiName;
        }
    }
}
