package com.facishare.crm.recycling.task.executor.consumer;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.follow.mq.RecyclingRuleProducer;
import com.facishare.crm.recycling.task.executor.common.RecyclingFactory;
import com.facishare.crm.recycling.task.executor.common.RecyclingTaskGray;
import com.facishare.crm.recycling.task.executor.common.SfaRecyclingTaskRateLimiterService;
import com.facishare.crm.recycling.task.executor.model.RecyclingMessage;
import com.facishare.crm.recycling.task.executor.service.NomonTaskService;
import com.facishare.crm.sfa.audit.log.context.SFALogContext;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.fxiaoke.rocketmq.util.MessageHelper;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageConst;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

import static com.facishare.crm.recycling.task.executor.util.ConstantUtils.SFA_BATCH_RECYCLING_TAG;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-03-12 16:53
 */
@Slf4j
@Component
public class RecyclingListener implements ApplicationListener<ContextRefreshedEvent> {

    private AutoConfMQPushConsumer consumer;

    @Autowired
    private RecyclingFactory recyclingFactory;

    @Autowired
    private RecyclingTaskGray recyclingTaskGray;

    @Autowired
    private NomonTaskService nomonTaskService;

    @Autowired
    private RecyclingRuleProducer recyclingRuleProducer;

    @Autowired
    private SfaRecyclingTaskRateLimiterService sfaRecyclingTaskRateLimiterService;

    @PostConstruct
    public void init() {
        consumer = new AutoConfMQPushConsumer("sfa-recalculate-consumer", "sfa-recycling", (MessageListenerConcurrently) (msgs, context) -> {
            if (!msgs.isEmpty()) {
                for (MessageExt msg : msgs) {
                    // 取出traceContext
                    MessageHelper.fillContextFromMessage(TraceContext.get(), msg);
                    consumeMessage(msg);
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        });
    }


    @PreDestroy
    public void close() {
        consumer.close();
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (consumer != null && event.getApplicationContext().getParent() == null) {
            consumer.start();
        }
    }

    private void consumeMessage(MessageExt body) {
        try {
            RecyclingMessage message = JSON.parseObject(body.getBody(), RecyclingMessage.class);
            log.info("RecyclingListener,msgId:{},reconsumeTimes:{},{}", body.getMsgId(), body.getReconsumeTimes(), message);

            if (recyclingTaskGray.skipTenantId(message.getTenantId())) {
                log.warn("RecyclingListener skipTenantId:{},objectId:{}", message.getTenantId(), message.getObjectId());
                return;
            }
            if (recyclingTaskGray.recyclingForwardBatch(message.getTenantId())) {
                long offset = body.getQueueOffset();
                String maxOffset = body.getProperty(MessageConst.PROPERTY_MAX_OFFSET);
                int diff = (int) (StringUtils.isBlank(maxOffset) ? 0L : (Long.parseLong(maxOffset) - offset));
                if (diff > sfaRecyclingTaskRateLimiterService.DiffOffsetLimit) {
                    log.warn("RecyclingListener forwardRecycling batch: msgId:{},{},{}", body.getMsgId(), message.getTenantId(), message.getObjectId());
                    recyclingRuleProducer.sendNomonProducerMQ(SFA_BATCH_RECYCLING_TAG, JSON.toJSONString(message), message.getTenantId());
                    return;
                }
                log.warn("RecyclingListener not reach DiffOffsetLimit: msgId:{},{},{},diff:{} offset:{}, maxOffset:{}",
                        body.getMsgId(), message.getTenantId(), message.getObjectId(), diff , offset, maxOffset);
            }
            SFALogContext.putVariable("bizName","crm_object_recycling_task_recycling");
            SFALogContext.putVariable("messageId",body.getMsgId());
            SFALogContext.putVariable("cost1",System.currentTimeMillis() - body.getBornTimestamp());
            recyclingFactory.getRecyclingService(message.getObjectApiName()).execute(message);
        } catch (Exception e) {
            log.error("RecyclingListener  consumeMessage error msgId:{}", body.getMsgId(), e);
            throw new RuntimeException(e);
        } finally {
            TraceContext.remove();
            SFALogContext.clearContext();
        }
    }


}
