package com.facishare.crm.recycling.task.executor.model.forecast;

import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;

public interface ForecastTask extends CalculableForecastModel {
    String FORECAST_TASK_OBJECT_DESCRIBE_API_NAME = "ForecastTaskObj";
    String BIZ_STATUS = "biz_status";
    String FORECAST_RULE_OBJECT_ID = "forecast_rule_object_id";
    String FORECAST_DATE_SPLIT_TYPE = "forecast_date_split_type";
    String FORECAST_START_DATE = "forecast_start_date";
    String FORECAST_END_DATE = "forecast_end_date";
    String FORECAST_MONTH_GROUP = "forecast_month_group";
    String FORECAST_QUARTER_GROUP = "forecast_quarter_group";
    String FORECAST_TASK_DETAILS = "forecast_task_details";
    String FORECAST_RULE = "forecast_rule";

    String getForecastRuleObjectId();

    void setForecastRuleObjectId(String forecastRuleObjectId);

    Long getForecastStartDate();

    void setForecastStartDate(Long forecastStartDate);

    Long getForecastEndDate();

    void setForecastEndDate(Long forecastEndDate);

    Integer getForecastDateSplitType();

    void setForecastDateSplitType(Integer forecastDateSplitType);

    Integer getBizStatus();

    void setBizStatus(Integer bizStatus);

    String getForecastMonthGroup();

    void setForecastMonthGroup(String forecastMonthGroup);

    String getForecastQuarterGroup();

    void setForecastQuarterGroup(String forecastQuarterGroup);

    List<ForecastTaskDetail> getForecastTaskDetails();

    void setForecastTaskDetails(List<ForecastTaskDetail> forecastTaskDetails);

    ForecastRule getForecastRule();

    void setForecastRule(ForecastRule forecastRule);

    default String uniqueKey() {
        return uniqueKey(getForecastRuleObjectId(), getOwner0(), getForecastStartDate());
    }

    static String uniqueKeyOfObjectData(IObjectData data) {
        return uniqueKey(data.get(ForecastTask.FORECAST_RULE_OBJECT_ID), data.getOwner().get(0), data.get(ForecastTask.FORECAST_START_DATE));
    }

    /**
     * @param ruleId               预测规则id
     * @param owner                负责人id
     * @param getForecastStartDate 预测统计起始时间
     * @return 判重条件key
     */

    static String uniqueKey(Object ruleId, Object owner, Object getForecastStartDate) {
        return ruleId + UNIQUE_KEY_DELIMITER + owner + UNIQUE_KEY_DELIMITER + getForecastStartDate;
    }

}