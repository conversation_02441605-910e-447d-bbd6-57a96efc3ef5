package com.facishare.crm.recycling.task.executor.util;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.service.ICommonSqlService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.CommonSqlOperator;
import com.facishare.paas.metadata.impl.search.WhereParam;
import com.facishare.paas.metadata.service.impl.CommonSqlServiceImpl;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class CommonSqlUtils {

    private static final ICommonSqlService commonSqlService = SpringUtil.getContext().getBean(CommonSqlServiceImpl.class);

    public static List<WhereParam> getCommonWhereParams(String tenantId, String apiName) {
        List<WhereParam> wheres = Lists.newArrayList();
        CommonSqlUtils.addWhereParam(wheres, RecyclingRuleConstans.Field.Object_Api_Name, CommonSqlOperator.EQ, Lists.newArrayList(apiName));
        CommonSqlUtils.addWhereParam(wheres, RecyclingRuleConstans.Field.Tenant_Id, CommonSqlOperator.EQ, Lists.newArrayList(tenantId));
        CommonSqlUtils.addWhereParam(wheres, RecyclingRuleConstans.Field.Is_Deleted, CommonSqlOperator.EQ, Lists.newArrayList(0));
        return wheres;
    }


    public static void addWhereParam(List<WhereParam> whereParamList, String columnName, CommonSqlOperator op, List<Object> values){
        WhereParam whereParam = getWhereParam(columnName, op, values);
        whereParamList.add(whereParam);
    }

    public static WhereParam getWhereParam (String columnName, CommonSqlOperator op, List<Object> values) {
        WhereParam whereParam = new WhereParam();
        whereParam.setColumn(columnName);
        whereParam.setOperator(op);
        whereParam.setValue(values);
        return  whereParam;
    }

    public static List<Map> queryData(String tenantId, String tableName, List<WhereParam> whereParams) throws MetadataServiceException {
        ActionContext actionContext = getActionContext(tenantId);
        return commonSqlService.select(tableName, whereParams, actionContext);
    }

    public static List<IObjectData> queryObjectData(String tenantId, String tableName, List<WhereParam> whereParams) {
		ActionContext actionContext = getActionContext(tenantId);
		List<Map> result;
		try {
			result = commonSqlService.select(tableName, whereParams, actionContext);
		} catch (MetadataServiceException e) {
			throw new RuntimeException(e);
		}
		return ConvertUtils.convertMapToObjectData(result);
	}

	public static List<IObjectData> queryObjectDataById(String tenantId, String tableName, String apiName, List<String> idList) {
		ActionContext actionContext = getActionContext(tenantId);
		List<WhereParam> whereParamList = getCommonWhereParams(tenantId, apiName);
		addWhereParam(whereParamList, "id", CommonSqlOperator.IN, idList.stream().map(o -> (Object) o).collect(Collectors.toList()));
		List<Map> result;
		try {
			result = commonSqlService.select(tableName, whereParamList, actionContext);
		} catch (MetadataServiceException e) {
			throw new RuntimeException(e);
		}
		return ConvertUtils.convertMapToObjectData(result);
	}

    public static void insertDataBySql(String tenantId, String tableName, List<Map<String, Object>> insertMap) {
        if (CollectionUtils.empty(insertMap)) {
            return;
        }
        IActionContext actionContext = getActionContext(tenantId);
        try {
            commonSqlService.insert(tableName, insertMap, actionContext);
        } catch (MetadataServiceException e) {
            log.error("insert error:{},data:{}",tableName,insertMap,e);
            throw new RuntimeException();
        }
    }

    public static void batchDeleteDataBySql(String tenantId, String tableName, List<Map<String, Object>> columnAndValueMapList, List<String> primaryKeyList) {
        if (CollectionUtils.empty(columnAndValueMapList)) {
            return;
        }
        IActionContext actionContext = getActionContext(tenantId);
        try {
            commonSqlService.batchDelete(tableName, columnAndValueMapList, primaryKeyList, actionContext);
        } catch (MetadataServiceException e) {
            log.error("{} delete error", tableName, e);
        }
    }

	public static void deleteDataBySql(String tenantId, String tableName, List<WhereParam> whereParamList) {
		if (CollectionUtils.empty(whereParamList)) {
			return;
		}
		IActionContext actionContext = getActionContext(tenantId);
		try {
			commonSqlService.delete(tableName, whereParamList, actionContext);
		} catch (MetadataServiceException e) {
			log.error("{} delete error", tableName, e);
		}
	}

	public static int updateData(String tenantId, String tableName, Map<String, Object> dataMap, List<WhereParam> whereParams) {
		ActionContext actionContext = getActionContext(tenantId);
		int result;
		try {
			result = commonSqlService.update(tableName, dataMap, whereParams, actionContext);
		} catch (MetadataServiceException e) {
			throw new RuntimeException(e);
		}
		return result;
	}

    public static ActionContext getActionContext(String tenantId) {
        ActionContext actionContext = new ActionContext();
        actionContext.setEnterpriseId(tenantId);
        actionContext.setUserId(User.SUPPER_ADMIN_USER_ID);
        actionContext.setDbType("pg");
        actionContext.setAllowUpdateInvalid(true);
        actionContext.put("not_validate", true);
        actionContext.setPrivilegeCheck(false);
        return actionContext;
    }

}
