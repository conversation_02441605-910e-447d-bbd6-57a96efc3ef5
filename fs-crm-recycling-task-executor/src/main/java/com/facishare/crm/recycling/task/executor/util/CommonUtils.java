package com.facishare.crm.recycling.task.executor.util;

import com.facishare.crm.recycling.task.executor.model.RecalculateMessage;
import org.apache.commons.lang3.StringUtils;

import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2021/8/4 11:01
 */

public class CommonUtils {

    public static String getLockKey(RecalculateMessage message) {
        String idKey = StringUtils.isBlank(message.getObjectId()) ? message.getDeptIds().stream().collect(Collectors.joining("-")) : message.getObjectId();
        return String.format("%s-%s-%s-%s", message.getTenantId(), message.getObjectApiName(), message.getActionCode(), idKey);
    }

    /**
     * 计算 单数据redis 锁的key
     * 格式：tenantId-apiName-objectId
     *
     * @param message
     * @return
     */
    public static String getRecalculateLockKey(RecalculateMessage message) {
        return String.format("%s-%s-%s", message.getTenantId(), message.getObjectApiName(), message.getObjectId());
    }

}
