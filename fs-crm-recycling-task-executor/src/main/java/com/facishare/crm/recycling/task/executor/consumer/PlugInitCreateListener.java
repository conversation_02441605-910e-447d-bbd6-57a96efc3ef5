package com.facishare.crm.recycling.task.executor.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.recycling.task.executor.common.PlugInitFactory;
import com.facishare.crm.sfa.lto.business.models.PlugInitCreateModels;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.nio.charset.Charset;

/**
 * <AUTHOR> lik
 * @date : 2022/12/5 15:00
 */
@Slf4j
@Component
public class PlugInitCreateListener implements ApplicationListener<ContextRefreshedEvent> {

    private AutoConfMQPushConsumer consumer;
    @Autowired
    private PlugInitFactory plugInitFactory;

    @PostConstruct
    public void init() {
        consumer = new AutoConfMQPushConsumer("sfa-recalculate-consumer", "domain-plugin-instance-event",(MessageListenerConcurrently) (msgs, context) -> {
            if (!msgs.isEmpty()) {
                for(MessageExt msg: msgs) {
                    consumeMessage(msg);
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        });
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (consumer != null && event.getApplicationContext().getParent() == null) {
            consumer.start();
        }
    }
    @PreDestroy
    public void close() {
        consumer.close();
    }

    private void consumeMessage(MessageExt messageExt) {
        String body = new String(messageExt.getBody(), Charset.forName("UTF-8"));
        try {
            PlugInitCreateModels.TaskArg arg = JSON.parseObject(body, PlugInitCreateModels.TaskArg.class);
            if(!PlugInitFactory.REMIND_SERVICE.containsKey(arg.getPluginApiName())){
                return;
            }
            log.warn("PlugInitCreateListener consumeMessage arg:{}", JSONObject.toJSONString(arg));
            if (messageExt.getReconsumeTimes() > 2){
                log.info("PlugInitCreateListener,msgId:{},reconsumeTimes > 2 return:{}", messageExt.getMsgId(),messageExt.getReconsumeTimes());
                return;
            }
            plugInitFactory.getPlugInitService(arg.getPluginApiName()).execute(arg);
        } catch (Exception e) {
            log.error("PlugInitCreateListener consumeMessage msgId:{},e:",messageExt.getMsgId(),e);
            throw new RuntimeException(e);
        }
    }
}
