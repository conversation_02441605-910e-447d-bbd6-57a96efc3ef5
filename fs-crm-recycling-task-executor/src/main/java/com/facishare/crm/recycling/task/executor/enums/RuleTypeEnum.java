package com.facishare.crm.recycling.task.executor.enums;

/**
 * 提醒类型
 */
public enum RuleTypeEnum {


    /**
     * 回收
     */

    RECYCLING("0"),

    /**
     * 回收提醒
     */
    RECYCLING_REMIND("1"),

    /**
     * 未成交
     */
    DEAL_DAYS_REMIND("2"),

    /**
     * 未跟进
     */
    FOLLOW_UP_DAYS_REMIND("3"),

    /**
     * 线索未转换提醒
     */
    TRANSFORM_DAYS_REMIND("4");

    private final String value;


    RuleTypeEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static RuleTypeEnum ruleTypeOf(String ruleType) {
        for (RuleTypeEnum value : values()) {
            if (value.getValue().equals(ruleType)) {
                return value;
            }
        }
        return null;
    }

}
