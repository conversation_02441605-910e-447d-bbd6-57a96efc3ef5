package com.facishare.crm.recycling.task.executor.util;

import com.facishare.crm.recycling.task.executor.model.RecyclingMessage;
import com.facishare.crm.sfa.audit.log.EntityConverter;
import com.facishare.crm.sfa.audit.log.model.AuditArg;

/**
 * <AUTHOR> gongchunru
 * @date : 2023/6/9 18:50
 * @description:
 */
public class SFAAuditLogRecyclingConvert implements EntityConverter<RecyclingMessage> {
    @Override
    public AuditArg convert(RecyclingMessage message) {
        return AuditArg.builder()
                .appName("fs-crm-recycling-task")
                .bizName("account_recycling")
                .ei(message.getTenantId())
                .extra(message.getRecyclingReasonType() == null ? "" : message.getRecyclingReasonType().toString())
                .message(message.toString())
                .objectIds(message.getObjectId())
                .objectApiName(message.getObjectApiName())
                .build();
    }
}
