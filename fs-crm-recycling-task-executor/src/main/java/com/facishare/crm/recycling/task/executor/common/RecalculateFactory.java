package com.facishare.crm.recycling.task.executor.common;

import com.facishare.crm.recycling.task.executor.enums.ApiNameEnum;
import com.facishare.crm.recycling.task.executor.service.RecalculateService;
import com.facishare.crm.recycling.task.executor.service.impl.AccountRecalculate;
import com.facishare.crm.recycling.task.executor.service.impl.LeadsRecalculate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-03-09 18:27
 */

@Component
public class RecalculateFactory {


    @Autowired
    private AccountRecalculate accountRecalculate;
    @Autowired
    private LeadsRecalculate leadsRecalculate;

    private static final Map<String, RecalculateService> RECALCULATE_SERVICE = new HashMap<>();

    @PostConstruct
    public void init(){
        RECALCULATE_SERVICE.put(ApiNameEnum.ACCOUNT_OBJ.getApiName(), accountRecalculate);
        RECALCULATE_SERVICE.put(ApiNameEnum.HIGH_SEAS_OBJ.getApiName(), accountRecalculate);
        RECALCULATE_SERVICE.put(ApiNameEnum.LEADS_OBJ.getApiName(), leadsRecalculate);
        RECALCULATE_SERVICE.put(ApiNameEnum.LEADS_POOL_OBJ.getApiName(), leadsRecalculate);
    }


    public RecalculateService getRecalculateService(String apiName){
        return RECALCULATE_SERVICE.get(apiName);
    }
}
