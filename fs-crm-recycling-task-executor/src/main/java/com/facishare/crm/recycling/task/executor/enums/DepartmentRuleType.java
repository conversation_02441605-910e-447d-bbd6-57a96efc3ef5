package com.facishare.crm.recycling.task.executor.enums;

/**
 * <AUTHOR> lik
 * @date : 2024/5/14 16:18
 */

public enum DepartmentRuleType {
    MAIN_DEPARTMENT("main","仅匹配主属部门"),
    MAIN_AND_SUP_DEPARTMENT("mainAndSup","匹配主属部门及上级部门");
    private  String value;
    private  String label;
    DepartmentRuleType(String value, String label){
        this.value = value;
        this.label = label;
    }
    public String getValue(){
        return this.value;
    }
    public static String departmentRuleTypeOf(String value) {
        for (DepartmentRuleType v : values()) {
            if (v.getValue().equals(value)) {
                return v.label;
            }
        }
        return null;
    }
}
