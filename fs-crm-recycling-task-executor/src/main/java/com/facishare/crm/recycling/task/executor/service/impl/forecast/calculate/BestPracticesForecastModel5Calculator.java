package com.facishare.crm.recycling.task.executor.service.impl.forecast.calculate;

import com.facishare.crm.recycling.task.executor.model.forecast.ForecastRule;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastTask;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastTaskDetail;

import java.math.BigDecimal;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.Function;

class BestPracticesForecastModel5Calculator extends BestPracticesForecastRuleFieldCalculator {

    @Override
    public BiConsumer<ForecastTask, BigDecimal> taskSetter() {
        return ForecastTask::setBestPracticesForecastModel5;
    }

    @Override
    public Function<ForecastTask, BigDecimal> taskGetter() {
        return ForecastTask::getBestPracticesForecastModel5;
    }

    @Override
    public String taskApiName() {
        return ForecastTask.BEST_PRACTICES_FORECAST_MODEL5;
    }

    @Override
    public Function<ForecastTaskDetail, BigDecimal> detailGetter() {
        return ForecastTaskDetail::getBestPracticesForecastModel5;
    }

    @Override
    public BiConsumer<ForecastTaskDetail, BigDecimal> detailSetter() {
        return ForecastTaskDetail::setBestPracticesForecastModel5;
    }

    @Override
    Function<ForecastRule, String> ruleGetter() {
        return ForecastRule::getBestPracticesForecastModel5;
    }

    @Override
    Function<ForecastRule, Set<String>> targetGetter() {
        return ForecastRule::getBestPracticesForecastModel5Target;
    }

    @Override
    BiConsumer<ForecastRule, Set<String>> targetSetter() {
        return ForecastRule::setBestPracticesForecastModel5Target;
    }
}
