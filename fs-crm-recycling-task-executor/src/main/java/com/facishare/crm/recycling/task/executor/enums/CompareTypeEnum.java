package com.facishare.crm.recycling.task.executor.enums;

public enum CompareTypeEnum {

    /**
     *
     */
    NONE("0"),
    /**
     * 等于
     */
    EQ("1"),
    /**
     * 不等于
     */
    NEQ("2"),
    /**
     * 大于
     */
//    IN("3"),
    GT("3"),

    /**
     * 大于等于
     */
    GET("4"),

    /**
     * 小于
     */
    LT("5"),

    /**
     * 小于等于
     */
    LET("6"),
    /**
     * 包含
     */
    IN("7"),

    /**
     * 不包含
     */
    NIN("8"),
    /**
     * 为空
     */
    ISNULL("9"),

    /**
     * 不为空
     */
    NOTNULL("10"),


    /**
     * 早于
     */
    EARLY("11"),

    /**
     * 晚于
     */
    LATER("12"),


    /**
     * 属于
     */
    BELONGTO("13"),
    /**
     * 不属于
     */
    NOTBELONGTO("14"),

    /**
     * 自定义 介于
     */
    CUSTOM("19");


    private final String value;


    CompareTypeEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }


    public static CompareTypeEnum valueEnumOf(String value) {
        for (CompareTypeEnum compareType : values()) {
            if (compareType.getValue().equals(value)) {
                return compareType;
            }
        }
        return null;
    }


}
