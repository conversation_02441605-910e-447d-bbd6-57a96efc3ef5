package com.facishare.crm.recycling.task.executor.model.forecast;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ForecastExplanation {
    String dataId;
    String objectApiName;
    String tenantId;
    String ruleId;
    String ruleName;
    boolean isInclusive;
    boolean isDateCompliant;
    boolean isApplyRangeCompliant;
    boolean isDataFilterCompliant;

    public String brief() {
        return flag(isInclusive) + flag(isDateCompliant) + flag(isApplyRangeCompliant) + flag(isDataFilterCompliant);
    }

    private String flag(boolean flag) {
        return flag ? "[✓]" : "[×]";
    }
}
