package com.facishare.crm.recycling.task.executor.service.impl.forecast;

import com.facishare.crm.recycling.task.executor.biz.ForecastBiz;
import com.facishare.crm.recycling.task.executor.enums.forecast.ForecastObjectEnums;
import com.facishare.crm.recycling.task.executor.enums.forecast.ForecastRuleEnums;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastExplanation;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastObject;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastObjectRecord;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastPeriod;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastRule;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastRuleRecord;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastTask;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastTaskDetail;
import com.facishare.crm.recycling.task.executor.model.forecast.IllegalForecastRuleException;
import com.facishare.crm.recycling.task.executor.service.ForecastService;
import com.facishare.crm.recycling.task.executor.service.ForecastTaskAutomationService;
import com.facishare.crm.recycling.task.executor.service.impl.forecast.calculate.ForecastModelCalculator;
import com.facishare.crm.recycling.task.executor.service.impl.forecast.mq.ForecastMessage;
import com.facishare.crm.recycling.task.executor.service.impl.forecast.task.ForecastPeriodMatcher;
import com.facishare.crm.recycling.task.executor.service.impl.forecast.task.ForecastTaskGenerator;
import com.facishare.crm.sfa.audit.log.SFAAuditLog;
import com.facishare.crm.sfa.audit.log.context.SFALogContext;
import com.facishare.crm.sfa.expression.SFAExpressionService;
import com.facishare.paas.appframework.common.service.dto.QueryMemberInfosByDeptIds;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.dao.pg.config.MetadataTransactional;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.crm.recycling.task.executor.service.impl.forecast.task.ForecastTaskGenerator.generateTaskDetail;

@Service
@Slf4j
public class ForecastServiceImpl implements ForecastService {

    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private ForecastBiz forecastBiz;
    @Autowired
    private ForecastTaskAutomationService forecastTaskAutomationService;
    @Autowired
    @Qualifier("SFAExpressionServiceImpl")
    private SFAExpressionService expressionService;

    /*
     * **WARNING**
     * 为什么不用 @Transactional
     * 答：如果不这么做，参照com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl#shardCreate里的afterCommit实现，
     * 如果事务一直不提交，所有参与保存或更新的数据会一直被Spring事务管理器内部的ThreadLocal引用，造成无法GC，在数据过大的场景下OOM
     */
    @Override
    public void initTaskByRule(ForecastRule rule) throws MetadataServiceException {
        log.info("Start to init task by rule [id:{}|tenant:{}]", rule.getId(), rule.getTenantId());
        if (!ForecastRuleEnums.BizStatus.ENABLING.getValue().equals(rule.getBizStatus())) {
            log.warn("Skip init tasks by rule because biz status is not ENABLING [{}|{}|{}]", rule.getTenantId(), rule.getId(), rule.getBizStatus());
            return;
        }
        try {
            validateRule(rule);
        } catch (IllegalForecastRuleException e) {
            log.warn("Skip init tasks by rule because validate failed [{}|{}|{}]", rule.getTenantId(), rule.getId(), e.getMessage());
            return;
        }
        Map<String, String> uniqueKeyMap = initTask(rule);
        initTaskDetail(rule, uniqueKeyMap);
        rule.setBizStatus(ForecastRuleEnums.BizStatus.ENABLE.getValue());
        updateRuleStatus(rule);
        forecastTaskAutomationService.afterRuleEnable(rule);
    }

    private void validateRule(ForecastRule rule) throws IllegalForecastRuleException {
        try {
            ForecastModelCalculator.Register.acquireForecastModelCalculator(rule);
        } catch (Exception e) {
            throw new IllegalForecastRuleException(e.getMessage());
        }
    }

    private Map<String, String> initTask(ForecastRule rule) {
        initForecastApplyRangTarget(rule);
        List<ForecastPeriod> periods = ForecastPeriodMatcher.initPeriod(rule);
        int expectedSize = rule.getForecastApplyRangeTarget().size() * periods.size();
        Map<String, String> uniqueKeyMap = forecastBiz.lookupTaskKeyMap(rule.getTenantId(), rule.getId(), expectedSize);
        if (expectedSize > uniqueKeyMap.size()) {
            List<ForecastTask> tasks = ForecastTaskGenerator.generateTaskWithoutDetail(rule, uniqueKeyMap);
            forecastBiz.upsertTask(tasks);
            for (ForecastTask task : tasks) {
                uniqueKeyMap.put(task.uniqueKey(), task.getId());
            }
        }
        rule.setForecastApplyRangeTarget(null); // help GC
        return uniqueKeyMap;
    }

    private void initTaskDetail(ForecastRule rule, Map<String, String> uniqueKeyMap) throws MetadataServiceException {
        SearchTemplateQuery searchTemplateQuery = rule.toForecastObjectQuery();
        List<OrderBy> orderByList = new ArrayList<>(2);
        orderByList.add(new OrderBy(DBRecord.ID, Boolean.TRUE)); // ===OrderBy必须要有，否则分批配查询会不准===
        searchTemplateQuery.setOrders(orderByList);
        searchTemplateQuery.setLimit(QUERY_LIMIT); // 涉及自定义字段查询易超时(>25s)
        searchTemplateQuery.setOffset(0);
        User user = User.systemUser(rule.getTenantId());
        String forecastObjectApiName = rule.getForecastObjectApiName();
        List<ForecastObject> forecastObjects = queryForecastObject(user, forecastObjectApiName, searchTemplateQuery);
        while (!forecastObjects.isEmpty()) {
            int size = forecastObjects.size();
            filterByObjectExist(rule.getTenantId(), rule, forecastObjects);
            Map<String, List<ForecastTaskDetail>> detailMap = generateTaskDetail(forecastObjects, Collections.singletonList(rule), uniqueKeyMap);
            upsertTaskByRule(detailMap, rule);
            if (size < QUERY_LIMIT) {
                break;
            }
            searchTemplateQuery.setOffset(searchTemplateQuery.getOffset() + searchTemplateQuery.getLimit());
            forecastObjects = queryForecastObject(user, forecastObjectApiName, searchTemplateQuery);
        }
    }

    private void upsertTaskByRule(Map<String, List<ForecastTaskDetail>> detailMap, ForecastRule rule) throws MetadataServiceException {
        if (detailMap.isEmpty()) {
            return;
        }
        List<String> taskIds = new ArrayList<>(detailMap.keySet());
        List<ForecastTask> tasks = forecastBiz.queryTaskByIds(rule.getTenantId(), taskIds);
        for (ForecastTask task : tasks) {
            List<ForecastTaskDetail> details = detailMap.get(task.getId());
            for (ForecastTaskDetail detail : details) {
                detail.setDataOwnDepartment(task.getDataOwnDepartment());
            }
            task.setForecastTaskDetails(details);
            ForecastModelCalculator.Register.acceptSum(task, rule);
        }
        forecastBiz.upsertTaskByRule(tasks, rule);
    }

    private void upsertTaskByRule(Map<String, List<ForecastTaskDetail>> detailMap, List<ForecastRule> rules) throws MetadataServiceException {
        if (detailMap.isEmpty()) {
            return;
        }
        String tenantId = rules.get(0).getTenantId();
        Map<String, ForecastRule> ruleMap = rules.stream().collect(Collectors.toMap(ForecastRule::getId, Function.identity(), (v1, v2) -> v2));
        List<String> taskIds = new ArrayList<>(detailMap.keySet());
        List<ForecastTask> tasks = forecastBiz.queryTaskByIds(tenantId, taskIds);
        for (ForecastTask task : tasks) {
            List<ForecastTaskDetail> details = detailMap.get(task.getId());
            for (ForecastTaskDetail detail : details) {
                detail.setDataOwnDepartment(task.getDataOwnDepartment());
            }
            task.setForecastTaskDetails(details);
            ForecastModelCalculator.Register.acceptSum(task, ruleMap.get(task.getForecastRuleObjectId()));
        }
        forecastBiz.upsertTask(tasks);
    }

    @Override
    public void upsertTask(List<ForecastTask> tasks) {
        forecastBiz.upsertTask(tasks);
    }

    private void upsertTask(ForecastTask task) {
        forecastBiz.upsertTask(task);
    }

    @Override
    @MetadataTransactional
    @SFAAuditLog(bizName = "forecast_object_sync", extra1 = "#rules", extra2 = "#tasks",
            entityClass = ForecastMessage.ObjectSync.class, convertClass = ForecastMessage.ObjectSyncConverter.class)
    public void syncForecastObject(ForecastMessage.ObjectSync message) throws MetadataServiceException {
        ForecastObjectEnums.SyncAction action = ForecastObjectEnums.SyncAction.valueOf(message.getAction());
        IObjectData objectData = null;
        if (ForecastObjectEnums.SyncAction.DELETE == action) {
            objectData = new ObjectData();
            objectData.setDescribeApiName(message.getObjectApiName());
            objectData.setId(message.getObjectId());
            objectData.setTenantId(message.getTenantId());
        } else {
            List<IObjectData> objectDataList = serviceFacade.findObjectDataByIds(message.getTenantId(), Collections.singletonList(message.getObjectId()), message.getObjectApiName());
            if (!objectDataList.isEmpty()) {
                objectData = objectDataList.get(0);
            }
        }
        if (objectData != null) {
            log.info("Start to sync forecast object [id:{}|api:{}|tenant:{}|action:{}]", objectData.getId(), objectData.getDescribeApiName(), objectData.getTenantId(), message.getAction());
            syncForecastObject(new ForecastObjectRecord(objectData), action);
        } else {
            log.info("Can not find forecast object [id:{}|api:{}|tenant:{}|action:{}]", message.getObjectId(), message.getObjectApiName(), message.getTenantId(), message.getAction());
        }
    }

    void syncForecastObject(ForecastObject object, ForecastObjectEnums.SyncAction action) throws MetadataServiceException {
        switch (action) {
            case INSERT:
                syncForecastObjectInsert(object);
                break;
            case DELETE:
                syncForecastObjectDelete(object);
                break;
            case UPDATE:
                syncForecastObjectUpdate(object);
                break;
            case CHANGE_OWNER:
                syncForecastObjectChangeOwner(object);
                break;
            default:
                break;
        }
    }

    @Override
    public void deleteTaskByOwner(String tenantId, String owner) {
        forecastBiz.deleteTaskByOwner(tenantId, owner);
    }

    @Override
    public void deleteTaskByRule(ForecastRule rule) {
        forecastBiz.deleteTaskByRuleId(rule.getTenantId(), rule.getId());
    }

    @Override
    public void recalculateTask(ForecastTask task, ForecastModelCalculator calculator) throws MetadataServiceException {
        if (task.getForecastTaskDetails() == null) {
            task.setForecastTaskDetails(forecastBiz.queryDetailByTaskId(task.getTenantId(), task.getId()));
        }
        ForecastRule rule = forecastBiz.queryRuleById(task.getTenantId(), task.getForecastRuleObjectId());
        Objects.requireNonNull(rule);
        calculator.accept(task, rule);
        Map<String, Object> data = new HashMap<>();
        data.put(calculator.taskApiName(), calculator.taskGetter().apply(task));
        forecastBiz.updateTaskById(task.getTenantId(), task.getId(), data);
    }

    @Override
    public void addTaskDetailManually(ForecastTask task, List<String> forecastObjectIds) throws MetadataServiceException {
        ForecastRule rule = forecastBiz.queryRuleById(task.getTenantId(), task.getForecastRuleObjectId());
        List<ForecastObject> forecastObjects = queryForecastObjectByIds(task.getTenantId(), rule.getForecastObjectApiName(), forecastObjectIds);
        List<ForecastTaskDetail> details = new ArrayList<>(forecastObjects.size());
        for (ForecastObject forecastObject : forecastObjects) {
            ForecastTaskDetail detail = generateTaskDetail(forecastObject, rule, task);
            detail.setAddManually(Boolean.TRUE);
            details.add(detail);
        }
        task.setForecastTaskDetails(details);
        task.setForecastRule(rule);
        upsertTask(task);
    }

    @Override
    @MetadataTransactional
    public void removeTaskDetail(ForecastTask task, List<String> detailIds) throws MetadataServiceException {
        List<ForecastTaskDetail> details = forecastBiz.queryDetailByIds(task.getTenantId(), detailIds);
        detailIds.clear();
        boolean needRecalculate = false;
        for (ForecastTaskDetail detail : details) {
            if (ForecastModelCalculator.shouldCalculate(detail)) {
                needRecalculate = true;
            }
            detailIds.add(detail.getId());
        }
        if (needRecalculate) {
            task.setForecastTaskDetails(details);
            ForecastModelCalculator.Register.acceptOriginDiff(task);
            updateAllCalculateFields(task);
        }
        deleteTaskDetailByIds(task.getTenantId(), detailIds);
    }

    @Override
    public List<ForecastPeriod> getForecastPeriod(ForecastRule rule) {
        return ForecastPeriodMatcher.generatePeriod(rule);
    }

    @Override
    public List<ForecastExplanation> explain(ForecastObject object) throws MetadataServiceException {
        if (object == null) {
            return Collections.emptyList();
        }
        Pair<List<ForecastRule>, List<ForecastTask>> pair = queryRulesAndTasksForSync(new ForecastObjectRecord(object));
        List<ForecastRule> ruleList = pair.getLeft();
        List<ForecastTask> existTasks = pair.getRight();
        List<ForecastExplanation> explanations = explainRuleMatch(object, ruleList);
        Set<String> set = existTasks.stream().map(ForecastTask::getForecastRuleObjectId).collect(Collectors.toSet());
        explanations.stream().filter(e -> set.contains(e.getRuleId())).forEach(e -> e.setInclusive(true));
        return explanations;
    }

    private void syncForecastObjectInsert(ForecastObject object) throws MetadataServiceException {
        ForecastRule condition = new ForecastRuleRecord();
        String tenantId = object.getTenantId();
        condition.setTenantId(tenantId);
        condition.setForecastObjectApiName(object.getDescribeApiName());
        List<ForecastRule> rules = queryRulesForSync(condition);
        rules = filterByRuleMatch(object, rules);
        filterByRuleExist(tenantId, object, rules);
        SFALogContext.putVariable("rules", rules.size());
        if (rules.isEmpty()) {
            log.warn("Skip sync forecast object INSERT [id:{}|api:{}|tenant:{}]", object.getId(), object.getDescribeApiName(), tenantId);
            return;
        }
        Map<String, String> uniqueKeyMap = forecastBiz.lookupTaskKeyMapForSync(tenantId, rules.stream().map(ForecastRule::getId).collect(Collectors.toList()), Collections.singletonList(object.getOwner().get(0)));
        Map<String, List<ForecastTaskDetail>> detailMap = generateTaskDetail(Collections.singletonList(object), rules, uniqueKeyMap);
        upsertTaskByRule(detailMap, rules);
    }

    private void syncForecastObjectDelete(ForecastObject object) throws MetadataServiceException {
        Pair<List<ForecastRule>, List<ForecastTask>> pair = queryRulesAndTasksForSync(object);
        List<ForecastRule> ruleList = pair.getLeft();
        List<ForecastTask> existTasks = pair.getRight();
        SFALogContext.putVariable("rules", ruleList.size());
        SFALogContext.putVariable("tasks", existTasks.size());
        if (ruleList.isEmpty() || existTasks.isEmpty()) {
            log.info("Skip sync forecast object DELETE [id:{}|api:{}|tenant:{}]", object.getId(), object.getDescribeApiName(), object.getTenantId());
            return;
        }
        Map<String, ForecastRule> ruleMap = ruleList.stream().collect(Collectors.toMap(ForecastRule::getId, Function.identity()));
        List<String> deleteDetailIds = new ArrayList<>();
        for (ForecastTask task : existTasks) {
            ForecastRule rule = ruleMap.get(task.getForecastRuleObjectId());
            task.setForecastRule(rule);
            for (ForecastTaskDetail detail : task.getForecastTaskDetails()) {
                deleteDetailIds.add(detail.getId());
            }
            ForecastModelCalculator.Register.acceptOriginDiff(task);
        }
        updateAllCalculateFields(object.getTenantId(), existTasks);
        deleteTaskDetailByIds(object.getTenantId(), deleteDetailIds);
    }

    // 经过与产品沟通，逻辑不涉及预测任务的增删，只是预测明细发生改变及预测任务计算结果的联动
    private void syncForecastObjectUpdate(ForecastObject object) throws MetadataServiceException {
        syncForecastObjectUpdate0(object, ForecastObjectEnums.SyncAction.UPDATE, false, false);
    }

    private void syncForecastObjectChangeOwner(ForecastObject object) throws MetadataServiceException {
        syncForecastObjectUpdate0(object, ForecastObjectEnums.SyncAction.CHANGE_OWNER, true, true);
    }

    private void syncForecastObjectUpdate0(ForecastObject object, ForecastObjectEnums.SyncAction syncAction, boolean assertDateNotChange, boolean assertOwnerChange) throws MetadataServiceException {
        Pair<List<ForecastRule>, List<ForecastTask>> pair = queryRulesAndTasksForSync(object);
        if (pair.getLeft().isEmpty() || pair.getRight().isEmpty()) {
            log.info("Skip sync forecast object {} [id:{}|api:{}|tenant:{}]", syncAction, object.getId(), object.getDescribeApiName(), object.getTenantId());
            return;
        }
        List<ForecastTask> existTasks = pair.getRight();
        List<ForecastRule> ruleList = filterByRuleMatch(object, pair.getLeft());
        SFALogContext.putVariable("rules", ruleList.size());
        SFALogContext.putVariable("tasks", existTasks.size());
        Map<String, ForecastRule> ruleMap = ruleList.stream().collect(Collectors.toMap(ForecastRule::getId, Function.identity()));
        List<String> deleteDetailIds = new ArrayList<>();
        List<ForecastTask> uniqueKeyChangedTasks = new ArrayList<>();
        for (int i = existTasks.size() - 1; i >= 0; i--) {
            ForecastTask task = existTasks.get(i);
            ForecastTaskDetail detail = task.getForecastTaskDetails().get(0);
            ForecastRule rule = ruleMap.get(task.getForecastRuleObjectId());
            if (rule == null) { // 预测对象原来符合规则现在不符合规则
                ForecastModelCalculator.Register.acceptOriginDiff(task);
                deleteDetailIds.add(detail.getId());
                continue;
            }
            if (isUniqueKeyChange(object, task, detail, rule, assertDateNotChange, assertOwnerChange)) { // 检查uniqueKey是否发生改变
                task.setForecastRule(rule);
                uniqueKeyChangedTasks.add(task);
                existTasks.remove(i);
            } else {
                if (!recalculateAmount(object, task, detail, rule)) { // 检查金额是否发生改变
                    existTasks.remove(i);
                }
            }
        }
        if (!uniqueKeyChangedTasks.isEmpty()) {
            recalculateUniqueKey(object, uniqueKeyChangedTasks, deleteDetailIds); // uniqueKey发生改变，需要把明细转移到别的任务上
            existTasks.addAll(uniqueKeyChangedTasks);
        }
        updateAllCalculateFields(object.getTenantId(), existTasks);
        deleteTaskDetailByIds(object.getTenantId(), deleteDetailIds);
    }

    private boolean isUniqueKeyChange(ForecastObject object, ForecastTask task, ForecastTaskDetail detail, ForecastRule rule, boolean assertDateNotChange, boolean assertOwnerChange) {
        ForecastPeriod period = null;
        if (!assertDateNotChange && !detail.getForecastDate().equals(object.getForecastDate(rule.getForecastObjectDateApiName()))) {
            ForecastPeriodMatcher.initPeriod(rule);
            period = ForecastPeriodMatcher.matchPeriod(object.getForecastDate(rule.getForecastObjectApiName()), rule.getForecastPeriods());
        }
        String uniqueKey = ForecastTask.uniqueKey(rule.getId(), object.getOwner().get(0), period != null ? period.getLeft() : task.getForecastStartDate());
        return assertOwnerChange || !task.uniqueKey().equals(uniqueKey);
    }

    private boolean recalculateAmount(ForecastObject object, ForecastTask task, ForecastTaskDetail detail, ForecastRule rule) {
        List<ForecastTaskDetail> details = task.getForecastTaskDetails();
        ForecastTaskDetail current = generateTaskDetail(object, rule, task);
        current.setInCommitmentForecast(detail.getInCommitmentForecast());
        boolean amountChanged = ForecastModelCalculator.Register.acceptChange(task, rule, detail, current);
        current.setId(detail.getId());
        details.set(0, current); // 赋最新值更新数据库
        return amountChanged;
    }

    // 这个方法里的tasks是存在多个rule的，每个rule下object的预测指标同，注意区分
    private void recalculateUniqueKey(ForecastObject object, List<ForecastTask> tasks, List<String> deleteDetailIds) throws MetadataServiceException {
        Map<String, ForecastTask> existTasks = forecastBiz.lookupExistTask(object, tasks, ForecastTask::uniqueKey);
        List<ForecastTask> detailTransferredTasks = new ArrayList<>(tasks.size());
        for (ForecastTask task : tasks) {
            ForecastRule rule = task.getForecastRule();
            String nowKey;
            if (rule.getForecastPeriods() != null) {
                ForecastPeriod period = ForecastPeriodMatcher.matchPeriod(object.getForecastDate(rule.getForecastObjectApiName()), rule.getForecastPeriods());
                nowKey = ForecastTask.uniqueKey(rule.getId(), object.getOwner().get(0), period != null ? period.getLeft() : "null"); // 理论上period不可能为空
            } else {
                nowKey = ForecastTask.uniqueKey(rule.getId(), object.getOwner().get(0), task.getForecastStartDate());
            }
            if (nowKey.equals(task.uniqueKey())) {
                continue;
            }
            ForecastTask target = existTasks.get(nowKey);
            ForecastTaskDetail detail = task.getForecastTaskDetails().get(0); // 理论上就一个detail
            ForecastModelCalculator.Register.acceptOriginDiff(task);
            if (target == null) {
                deleteDetailIds.add(detail.getId()); // 1. 任务生成时此人不合规，之后此人进入到适用范围合规，合规后找不到任务 2. 更换负责人，从未锁定的任务换到已锁定的任务 3. 预测时间指标匹配不上
            } else {
                detail.setForecastTaskObjectId(target.getId());
                detail.setForecastObject(object);
                detail.setForecastAmount(object.getForecastAmount(rule.getForecastObjectAmountApiName()));
                detail.setForecastDate(object.getForecastDate(rule.getForecastObjectDateApiName()));
                detail.setOwner(object.getOwner());
                detail.setDataOwnDepartment(object.getDataOwnDepartment());
                target.setForecastRule(rule);
                target.setForecastTaskDetails(task.getForecastTaskDetails());
                task.setForecastTaskDetails(Collections.emptyList());
                ForecastModelCalculator.Register.acceptSum(target, rule);
                detailTransferredTasks.add(target);
            }
        }
        tasks.addAll(detailTransferredTasks);
    }

    private Pair<List<ForecastRule>, List<ForecastTask>> queryRulesAndTasksForSync(ForecastObject object) throws MetadataServiceException {
        ForecastRule condition = new ForecastRuleRecord();
        condition.setTenantId(object.getTenantId());
        condition.setForecastObjectApiName(object.getDescribeApiName());
        condition.setBizStatus(ForecastRuleEnums.BizStatus.ENABLE.getValue());
        List<ForecastRule> rules = queryRulesForSync(condition);
        if (rules.isEmpty()) {
            return new ImmutablePair<>(rules, Collections.emptyList());
        }
        Set<String> ruleIds = rules.stream().map(ForecastRule::getId).collect(Collectors.toSet());
        List<ForecastTask> existTasks = forecastBiz.queryTasksForSync(object.getTenantId(), ruleIds, object.getId());
        return new ImmutablePair<>(rules, existTasks);
    }

    private List<ForecastRule> queryRulesForSync(ForecastRule condition) throws MetadataServiceException {
        List<ForecastRule> rules = forecastBiz.queryRulesForSync(condition);
        for (int i = rules.size() - 1; i >= 0; i--) {
            ForecastRule rule = rules.get(i);
            try {
                validateRule(rule);
            } catch (IllegalForecastRuleException e) {
                log.warn("remove rule because validate failed [{}|{}|{}]", rule.getTenantId(), rule.getId(), e.getMessage());
                rules.remove(i);
            }
        }
        return rules;
    }

    private void updateAllCalculateFields(ForecastTask task) {
        forecastBiz.updateAllCalculateFields(task.getTenantId(), Collections.singletonList(task));
    }

    private void updateAllCalculateFields(String tenantId, List<ForecastTask> existTasks) {
        if (existTasks.isEmpty()) {
            return;
        }
        forecastBiz.updateAllCalculateFields(tenantId, existTasks);
    }

    private List<ForecastObject> queryForecastObjectByIds(String tenantId, String objectApiName, List<String> forecastObjectIds) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        List<IFilter> filters = new ArrayList<>();
        IFilter filter = new Filter();
        filter.setFieldName(Tenantable.TENANT_ID);
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Collections.singletonList(tenantId));
        filters.add(filter);
        filter = new Filter();
        filter.setFieldName(DBRecord.ID);
        filter.setOperator(Operator.IN);
        filter.setFieldValues(forecastObjectIds);
        filters.add(filter);
        Wheres wheres = new Wheres();
        wheres.setFilters(filters);
        searchTemplateQuery.setWheres(Collections.singletonList(wheres));
        searchTemplateQuery.setPermissionType(0);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setFindExplicitTotalNum(false);
        searchTemplateQuery.setSearchSource("db");
        return queryForecastObject(User.systemUser(tenantId), objectApiName, searchTemplateQuery);
    }

    // ===循环调用===
    private List<ForecastObject> queryForecastObject(User user, String objectApiName, SearchTemplateQuery searchTemplateQuery) {
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, objectApiName, searchTemplateQuery);
        List<IObjectData> dataList = queryResult.getData();
        List<ForecastObject> result = new ArrayList<>(dataList.size());
        for (IObjectData objectData : dataList) {
            result.add(new ForecastObjectRecord(objectData));
        }
        return result;
    }

    private void filterByObjectExist(String tenantId, ForecastRule rule, List<ForecastObject> forecastObjects) {
        Set<String> keySet = forecastBiz.lookupExistDetailId(tenantId, Collections.singletonList(rule), forecastObjects);
        for (int i = forecastObjects.size() - 1; i >= 0; i--) {
            ForecastObject object = forecastObjects.get(i);
            if (keySet.contains(ForecastTaskDetail.ruleKey(rule.getId(), object.getId()))) {
                removeElement(forecastObjects, i);
            }
        }
    }

    private void filterByRuleExist(String tenantId, ForecastObject forecastObject, List<ForecastRule> rules) {
        if (!rules.isEmpty()) {
            Set<String> keySet = forecastBiz.lookupExistDetailId(tenantId, rules, Collections.singletonList(forecastObject));
            for (int i = rules.size() - 1; i >= 0; i--) {
                ForecastRule rule = rules.get(i);
                if (keySet.contains(ForecastTaskDetail.ruleKey(rule.getId(), forecastObject.getId()))) {
                    removeElement(rules, i);
                }
            }
        }
    }

    private <T> void removeElement(List<T> list, int i) {
        if (list instanceof ArrayList && list.size() - i - 1 > 0) {
            list.set(i, list.get(list.size() - 1));
            list.remove(list.size() - 1); // 避免JDK8中ArrayList.remove(i)底层的System.arraycopy()，但会破坏有序性
        } else {
            list.remove(i);
        }
    }

    private void updateRuleStatus(ForecastRule rule) throws MetadataServiceException {
        Map<String, Object> data = new HashMap<>();
        data.put(ForecastRule.BIZ_STATUS, rule.getBizStatus());
        forecastBiz.updateRuleById(rule.getTenantId(), rule.getId(), data);
    }

    private void deleteTaskDetailByIds(String tenantId, List<String> ids) throws MetadataServiceException {
        if (ids.isEmpty()) {
            return;
        }
        Map<String, Object> data = new HashMap<>();
        data.put(DBRecord.IS_DELETED, 1);
        forecastBiz.updateTaskDetailByIds(tenantId, ids, data);
    }

    private void initForecastApplyRangTarget(ForecastRule rule) {
        List<String> range = rule.getForecastApplyRange();
        if (range != null) {
            Map<String, List<QueryMemberInfosByDeptIds.Member>> map = serviceFacade.getMemberInfoMapByDeptIds(User.systemUser(rule.getTenantId()), range, Boolean.FALSE, 0, 1);
            Map<String, String> target = new HashMap<>();
            for (Map.Entry<String, List<QueryMemberInfosByDeptIds.Member>> entry : map.entrySet()) {
                for (QueryMemberInfosByDeptIds.Member member : entry.getValue()) {
                    target.put(member.getId(), entry.getKey());
                }
            }
            rule.setForecastApplyRangeTarget(target);
        }
    }

    private List<ForecastRule> filterByRuleMatch(ForecastObject object, List<ForecastRule> rules) {
        if (rules.isEmpty()) {
            return rules;
        } else {
            IObjectDescribe objectDescribe = serviceFacade.findObject(object.getTenantId(), object.getDescribeApiName());
            return rules.stream().filter(rule -> testApplyRange(rule, object) && testDate(rule, object) && testData(rule, object, objectDescribe)).collect(Collectors.toList());
        }
    }

    private List<ForecastExplanation> explainRuleMatch(ForecastObject object, List<ForecastRule> rules) {
        if (rules.isEmpty()) {
            return Collections.emptyList();
        }
        IObjectDescribe objectDescribe = serviceFacade.findObject(object.getTenantId(), object.getDescribeApiName());
        List<ForecastExplanation> res = new ArrayList<>();
        for (ForecastRule rule : rules) {
            ForecastExplanation explanation = new ForecastExplanation();
            explanation.setDataId(object.getId());
            explanation.setObjectApiName(object.getDescribeApiName());
            explanation.setTenantId(object.getTenantId());
            explanation.setRuleId(rule.getId());
            explanation.setRuleName(rule.getName());
            explanation.setDateCompliant(testDate(rule, object));
            explanation.setApplyRangeCompliant(testApplyRange(rule, object));
            explanation.setDataFilterCompliant(testData(rule, object, objectDescribe));
            res.add(explanation);
        }
        return res;
    }

    private boolean testDate(ForecastRule rule, IObjectData objectData) {
        Long forecastDate = objectData.get(rule.getForecastObjectDateApiName(), Long.class, 0L);
        return forecastDate.compareTo(rule.getForecastStartDate()) >= 0 && forecastDate.compareTo(rule.getForecastEndDate()) <= 0;
    }

    private boolean testApplyRange(ForecastRule rule, IObjectData objectData) {
        List<String> range = rule.getForecastApplyRange();
        if (range == null) {
            return false;
        }
        for (String deptId : range) {
            for (String ownDept : objectData.getDataOwnDepartment()) {
                if (deptId.equals(ownDept)) {
                    return true;
                }
            }
        }
        return false;
    }

    private boolean testData(ForecastRule rule, IObjectData objectData, IObjectDescribe objectDescribe) {
        String expression = rule.getForecastDataWheresExpression();
        if (expression == null || "ALL".equals(expression)) {
            return true;
        }
        try {
            return expressionService.evaluate(expression, objectData, objectDescribe);
        } catch (Exception e) {
            log.error("Evaluate rule data condition error", e);
            return false;
        }
    }

}
