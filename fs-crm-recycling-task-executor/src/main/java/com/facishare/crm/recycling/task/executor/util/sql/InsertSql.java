package com.facishare.crm.recycling.task.executor.util.sql;

import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Map;

@Slf4j
@SuppressWarnings("unchecked")
public class InsertSql extends BaseSql {
	private final List<Map<String, Object>> insertList = Lists.newArrayList();

	public InsertSql add(Map<String, Object> map) {
		this.insertList.add(map);
		return this;
	}

	public InsertSql addAll(List<Map<String, Object>> insertList) {
		this.insertList.addAll(insertList);
		return this;
	}

	public static InsertSql to(String tenantId, String tableName) {
		InsertSql insertSql = new InsertSql();
		insertSql.tenantId = tenantId;
		insertSql.setTableName(tableName);
		return insertSql;
	}

	@Override
	protected void checked() {
		Assert.notNull(actionContext, "ActionContext不能为空");
		Assert.notNull(tableName,"表名不能为空");
		Assert.notNull(tenantId, "企业ID不能为空");
		Assert.notEmpty(insertList, "插入数据不能为空");
	}

	public int insertSql() {
		checked();
		try {
			return commonSqlService.insert(tableName, insertList, actionContext);
		} catch (MetadataServiceException e) {
			if (ignoreException) {
				log.error("插入异常", e);
				return 0;
			}
			throw new RuntimeException(e);
		}
	}

	@Override
	public InsertSql withContext(IActionContext context) {
		return super.withContext(context);
	}

	@Override
	public InsertSql withAdminContext() {
		return super.withAdminContext();
	}

	@Override
	public InsertSql ignoreException() {
		return super.ignoreException();
	}
}
