package com.facishare.crm.recycling.task.executor.service;

import com.facishare.crm.recycling.task.executor.model.forecast.ForecastRule;
import com.facishare.crm.recycling.task.executor.service.impl.forecast.mq.ForecastMessage;

public interface ForecastTaskAutomationService {

    void submitForecastTaskRemindBeforeEndMessage(ForecastRule rule);

    void handleForecastTaskRemindBeforeEndMessage(ForecastRule rule, ForecastMessage.RuleEnable message);

    void submitForecastTaskAutoLockAfterEndMessage(ForecastRule rule);

    void handleForecastTaskAutoLockAfterEndMessage(ForecastRule rule, ForecastMessage.RuleEnable message);

    void afterRuleEnable(ForecastRule rule);
}
