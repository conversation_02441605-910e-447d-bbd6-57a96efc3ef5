package com.facishare.crm.recycling.task.executor.consumer;

import com.facishare.converter.EIEAConverter;
import com.facishare.crm.recycling.task.executor.common.RecyclingTaskGray;
import com.facishare.crm.recycling.task.executor.model.AttendanceRuleMessage;
import com.facishare.crm.recycling.task.executor.service.RecalculateBatchService;
import com.facishare.crm.recycling.task.executor.util.ProtoUtil;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.fxiaoke.rocketmq.util.MessageHelper;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/3/17 10:11
 */
@Component
@Slf4j
public class AttendanceRuleChangeListener implements ApplicationListener<ContextRefreshedEvent> {

    private AutoConfMQPushConsumer consumer;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private RecalculateBatchService recalculateBatchService;

    @Autowired
    private RecyclingTaskGray recyclingTaskGray;

    @PostConstruct
    public void init() {
        consumer = new AutoConfMQPushConsumer("rocketmq-consumer.ini", "name_server_08,consumer_attendance_rule", (MessageListenerConcurrently) (msgs, context) -> {
            if (!msgs.isEmpty()) {
                for (MessageExt msg : msgs) {
                    // 取出traceContext
                    MessageHelper.fillContextFromMessage(TraceContext.get(), msg);
                    consumeMessage(msg);
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        });
    }


    @PreDestroy
    public void close() {
        consumer.close();
    }

    private ConsumeConcurrentlyStatus consumeMessage(MessageExt msg) {
        AttendanceRuleMessage attendanceRuleMessage = new AttendanceRuleMessage();
        try {
            attendanceRuleMessage = ProtoUtil.fromProto(msg.getBody(), AttendanceRuleMessage.class);
        } catch (Exception e) {
            log.error("AttendanceRuleChangeListener consumeMessage error", e);
        }
        String tenantId = getTenantId(attendanceRuleMessage);
        log.info("AttendanceRuleChangeListener,{},{}", msg.getMsgId(), attendanceRuleMessage);
        if (msg.getReconsumeTimes() > 1) {
            log.warn("AttendanceRuleChangeListener,consumeMessage reconsumeTimes > 1,ea:{},{}", attendanceRuleMessage.getEa(), attendanceRuleMessage);
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }
        if (!recyclingTaskGray.expireTimeSkipHolidays(tenantId)) {
            log.info("AttendanceRuleChangeListener,is not gray skip holidays {},{}", msg.getMsgId(), attendanceRuleMessage);
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }
        List<String> userIds = new ArrayList<>();
        attendanceRuleMessage.getUserIdLists().stream().map(String::valueOf).forEach(userIds::add);
        recalculateBatchService.newRecalculateByOwner(tenantId, userIds);
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (consumer != null && event.getApplicationContext().getParent() == null) {
            consumer.start();
        }
    }


    private String getTenantId(AttendanceRuleMessage msg) {
        String ea = msg.getEa();
        int tenantId = eieaConverter.enterpriseAccountToId(ea);
        return String.valueOf(tenantId);
    }


}
