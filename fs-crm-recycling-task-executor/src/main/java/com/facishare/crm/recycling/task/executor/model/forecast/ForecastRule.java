package com.facishare.crm.recycling.task.executor.model.forecast;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.recycling.task.executor.service.impl.forecast.calculate.ForecastModelCalculator;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

public interface ForecastRule extends ForecastModel {
    String FORECAST_RULE_OBJECT_DESCRIBE_API_NAME = "ForecastRuleObj";
    String NAME = "name";
    String DESCRIPTION = "description";
    String BIZ_STATUS = "biz_status";
    String FORECAST_OBJECT_API_NAME = "forecast_object_api_name";
    String FORECAST_OBJECT_AMOUNT_API_NAME = "forecast_object_amount_api_name";
    String FORECAST_OBJECT_DATE_API_NAME = "forecast_object_date_api_name";
    String FORECAST_DATE_SPLIT_TYPE = "forecast_date_split_type";
    String FORECAST_START_DATE = "forecast_start_date";
    String FORECAST_END_DATE = "forecast_end_date";
    String AUTO_LOCK_AFTER_END = "auto_lock_after_end";
    String AUTO_REMIND_BEFORE_END = "auto_remind_before_end";
    String REMIND_DAYS_BEFORE_END = "remind_days_before_end";
    String FORECAST_DATA_CONDITION = "forecast_data_condition";
    String FORECAST_OBJECT_FIELDS_SHOW = "forecast_object_fields_show";
    String FORECAST_APPLY_RANGE = "forecast_apply_range";

    String getBizStatus();

    void setBizStatus(String bizStatus);

    String getForecastObjectApiName();

    void setForecastObjectApiName(String forecastObjectApiName);

    String getForecastObjectDateApiName();

    String getForecastObjectAmountApiName();

    Long getForecastStartDate();

    Long getForecastEndDate();

    Integer getForecastDateSplitType();

    Boolean getAutoLockAfterEnd();

    Boolean getAutoRemindBeforeEnd();

    Integer getRemindDaysBeforeEnd();

    String getForecastDataCondition();

    List<String> getForecastApplyRange();

    String getBestPracticesForecastModel1();

    String getBestPracticesForecastModel2();

    String getBestPracticesForecastModel3();

    String getBestPracticesForecastModel4();

    String getBestPracticesForecastModel5();

    Boolean getArtificialCommitmentForecastModel();

    Boolean getStageWeightForecastModel();

    Boolean getArtificialWeightForecastModel();

    BigDecimal getAIWeightForecastModel();

    void setForecastPeriods(List<ForecastPeriod> forecastPeriods);

    List<ForecastPeriod> getForecastPeriods();

    void setForecastObjectAmountApiNameTarget(String forecastObjectAmountApiNameTarget);

    String getForecastObjectAmountApiNameTarget();

    void setForecastApplyRangeTarget(Map<String, String> forecastApplyRangeTarget);

    /**
     * @return key 员工ID<p>
     * value 主属部门ID
     */
    Map<String, String> getForecastApplyRangeTarget();

    void setBestPracticesForecastModel1Target(Set<String> target);

    Set<String> getBestPracticesForecastModel1Target();

    void setBestPracticesForecastModel2Target(Set<String> target);

    Set<String> getBestPracticesForecastModel2Target();

    void setBestPracticesForecastModel3Target(Set<String> target);

    Set<String> getBestPracticesForecastModel3Target();

    void setBestPracticesForecastModel4Target(Set<String> target);

    Set<String> getBestPracticesForecastModel4Target();

    void setBestPracticesForecastModel5Target(Set<String> target);

    Set<String> getBestPracticesForecastModel5Target();

    void setForecastModelCalculators(ForecastModelCalculator[] forecastModelCalculators);

    ForecastModelCalculator[] getForecastModelCalculators();

    void setForecastObjectSearchSource(String forecastObjectSearchSource);

    String getForecastObjectSearchSource();

    default List<Wheres> getForecastDataWheres() {
        String expression = getForecastDataWheresExpression();
        if (expression != null && !"ALL".equals(expression)) {
            return JSON.parseArray(expression, Wheres.class);
        }
        return new ArrayList<>();
    }

    default void filterForecastDate(List<IFilter> filters) {
        Long forecastStartDate = getForecastStartDate();
        Long forecastEndDate = getForecastEndDate();
        if (forecastStartDate != null && forecastEndDate != null) {
            String fieldName = getForecastObjectDateApiName();
            IFilter filter = new Filter();
            filter.setFieldName(fieldName);
            filter.setOperator(Operator.GTE);
            filter.setFieldValues(Collections.singletonList(forecastStartDate.toString()));
            filters.add(filter);
            filter = new Filter();
            filter.setFieldName(fieldName);
            filter.setOperator(Operator.LTE);
            filter.setFieldValues(Collections.singletonList(forecastEndDate.toString()));
            filters.add(filter);
        }
    }

    default void filterForecastApplyRange(List<IFilter> filters) {
        IFilter filter = new Filter();
        filter.setFieldName(DBRecord.DATA_OWN_DEPARTMENT);
        filter.setOperator(Operator.HASANYOF);
        filter.setFieldValues(getForecastApplyRange());
        filters.add(filter);
    }

    default String getForecastDataWheresExpression() {
        String str = getForecastDataCondition();
        if (str != null) {
            JSONObject obj = JSON.parseObject(str);
            return obj.getString("value");
        }
        return null;
    }

    default SearchTemplateQuery toForecastObjectQuery() {
        Objects.requireNonNull(getTenantId());
        List<Wheres> wheres = getForecastDataWheres();
        if (wheres.isEmpty()) {
            Wheres w = new Wheres();
            w.setFilters(new ArrayList<>());
            wheres.add(w);
        }
        List<IFilter> filters = wheres.get(0).getFilters();
        filterForecastDate(filters);
        filterForecastApplyRange(filters);
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setWheres(wheres);
        searchTemplateQuery.setPermissionType(0);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setFindExplicitTotalNum(false);
        searchTemplateQuery.setNeedReturnQuote(false);
        if (getForecastObjectSearchSource() != null) {
            searchTemplateQuery.setSearchSource(getForecastObjectSearchSource());
        }
        return searchTemplateQuery;
    }

}
