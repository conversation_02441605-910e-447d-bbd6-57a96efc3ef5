package com.facishare.crm.recycling.task.executor.consumer;

import com.alibaba.fastjson.JSON;
import org.apache.rocketmq.common.message.MessageExt;
import com.facishare.crm.sfa.lto.rfm.RFMService;
import com.facishare.crm.sfa.lto.rfm.models.RFMModels;
import com.facishare.paas.appframework.common.mq.RocketMQMessageListener;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.nio.charset.Charset;
import java.util.List;

@Slf4j
public class TaskListener implements RocketMQMessageListener {
    @Autowired
    private RFMService rfmService;

    @Override
    public void consumeMessage(List<MessageExt> list) {
        if (CollectionUtils.empty(list)) {
            return;
        }

        for (MessageExt message : list) {
            log.info("sfa_rfm_rule message:{}",message);
            consumeMessage(message);
        }
    }

    private void consumeMessage(MessageExt message) {
        String body = new String(message.getBody(), Charset.forName("UTF-8"));
        log.info("sfa_rfm_rule message tags:{}, body:{}",message.getTags(), body);
        switch (message.getTags()) {
            case "sfa_create_rfm_rule_calculate_task":
                rfmService.createRFMProcessTask();
                rfmService.createRFMCreateCalculateTask();
                break;
            case "sfa_rfm_rule_calculate_task":
                RFMModels.CalculateRFMTaskArg calculateRFMTaskArg = JSON.parseObject(body,
                        RFMModels.CalculateRFMTaskArg.class);
                rfmService.processRFMRule(new User(calculateRFMTaskArg.getTenantId(), User.SUPPER_ADMIN_USER_ID),
                        calculateRFMTaskArg.getObjectApiName());
                break;
            case "sfa_rfm_calculate_by_ids_task":
                RFMModels.RecalculateRFMByIdsArg recalculateRFMByIdsArg = JSON.parseObject(body,
                        RFMModels.RecalculateRFMByIdsArg.class);
                for(String dataId : recalculateRFMByIdsArg.getObjectIds()) {
                    rfmService.processRFMRule(new User(recalculateRFMByIdsArg.getTenantId(), User.SUPPER_ADMIN_USER_ID),
                            recalculateRFMByIdsArg.getObjectApiName(), dataId);
                }
                break;
        }
    }
}
