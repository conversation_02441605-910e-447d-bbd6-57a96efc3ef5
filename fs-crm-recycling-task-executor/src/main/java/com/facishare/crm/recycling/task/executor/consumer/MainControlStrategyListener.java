package com.facishare.crm.recycling.task.executor.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.recycling.task.executor.util.GrayUtils;
import com.facishare.crm.sfa.lto.maincontrolstrategy.MainControlStrategySerivce;
import com.facishare.crm.sfa.lto.maincontrolstrategy.models.MainControlStrategyModels;
import com.facishare.crm.sfa.lto.relationship.models.RelationshipModels;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.nio.charset.Charset;

/**
 * 主数据管控
 * <AUTHOR> lik
 * @date : 2023/4/11 15:03
 */
@Slf4j
@Component
public class MainControlStrategyListener implements ApplicationListener<ContextRefreshedEvent> {

    private AutoConfMQPushConsumer consumer;
    @Autowired
    private MainControlStrategySerivce mainControlStrategySerivce;

    @PostConstruct
    public void init() {
        consumer = new AutoConfMQPushConsumer("sfa-recalculate-consumer", "sfa-describe-change-consumer",(MessageListenerConcurrently) (msgs, context) -> {
            if (!msgs.isEmpty()) {
                for(MessageExt msg: msgs) {
                    consumeMessage(msg);
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        });
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (consumer != null && event.getApplicationContext().getParent() == null) {
            consumer.start();
        }
    }
    @PreDestroy
    public void close() {
        consumer.close();
    }

    private void consumeMessage(MessageExt messageExt) {

        if (!GrayUtils.isGrayMainControlStrategy(messageExt.getProperties().get("x-fs-ei"))){
            return;
        }
        String body = new String(messageExt.getBody(), Charset.forName("UTF-8"));
        try {
            if(messageExt.getReconsumeTimes()>2){
                return;
            }
            MainControlStrategyModels.TaskArg arg = JSON.parseObject(body, MainControlStrategyModels.TaskArg.class);
            if("object-describe-tag".equals(messageExt.getTags())){


                if(ObjectUtils.isEmpty(arg.getTenantId()) || ObjectUtils.isEmpty(arg.getName()) ||  !"object_field".equals(arg.getName())
                        || ObjectUtils.isEmpty(arg.getOp())) {
                    return;
                }
                mainControlStrategySerivce.execute(arg);
            }
        } catch (Exception e) {
            log.error("MainControlStrategyListener consumeMessage msgId:{},e:",messageExt.getMsgId(),e);
            throw new RuntimeException(e);
        }
    }
}