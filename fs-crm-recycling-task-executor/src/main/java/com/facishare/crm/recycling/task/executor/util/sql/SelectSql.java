package com.facishare.crm.recycling.task.executor.util.sql;

import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@SuppressWarnings({"rawtypes", "unchecked"})
public  class SelectSql extends BaseSql {

	public static SelectSql from(String tableName) {
		SelectSql select = new SelectSql();
		select.setTableName(tableName);
		return select;
	}

	public SelectSql where(WhereSql whereSql) {
		return super.where(whereSql);
	}

	public List<Map> select() {
		checked();
		try {
			return commonSqlService.select(tableName, whereList, actionContext);
		} catch (MetadataServiceException e) {
			if (ignoreException) {
				log.error("查询异常", e);
				return Lists.newArrayList();
			}
			throw new RuntimeException("查询异常", e);
		}
	}

	public List<IObjectData> selectToObjectData() {
		return select().stream().map(o -> ObjectDataExt.of(o).getObjectData()).collect(Collectors.toList());
	}

	@Override
	public SelectSql withContext(IActionContext context) {
		return super.withContext(context);
	}

	@Override
	public SelectSql withAdminContext() {
		return super.withAdminContext();
	}

	@Override
	public SelectSql ignoreException() {
		return super.ignoreException();
	}
}