package com.facishare.crm.recycling.task.executor.util;

public interface RemindRuleConstants {
    class Field{
        public static final String Tenant_Id = "tenant_id";
        public static final String Id = "id";
        public static final String Last_Modified_By = "last_modified_by";
        public static final String Created_By = "created_by";
        public static final String Last_Modified_Time = "last_modified_time";
        public static final String Create_Time = "create_time";
        public static final String Is_Deleted = "is_deleted";
        public static final String Object_Api_Name = "object_api_name";
        public static final String Data_Id = "data_id";
        public static final String Data_Type = "data_type";
        public static final String Rule_Type = "rule_type";
        public static final String Deal_Days = "deal_days";
        public static final String Follow_Up_Days = "follow_up_days";
        public static final String Group_Id = "group_id";
        public static final String SKIP_HOLIDAYS = "skip_holidays";
    }
}
