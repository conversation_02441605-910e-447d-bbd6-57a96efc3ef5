package com.facishare.crm.recycling.task.executor.biz;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.follow.util.CommonUtils;
import com.facishare.crm.recycling.task.executor.common.RecyclingTaskGray;
import com.facishare.crm.recycling.task.executor.enums.ApiNameEnum;
import com.facishare.crm.recycling.task.executor.enums.BizStatusEnum;
import com.facishare.crm.recycling.task.executor.enums.DepartmentRuleType;
import com.facishare.crm.recycling.task.executor.enums.LifeStatusEnum;
import com.facishare.crm.recycling.task.executor.model.PrmModel;
import com.facishare.crm.recycling.task.executor.model.RecyclingRuleInfoModel;
import com.facishare.crm.recycling.task.executor.util.PoolEmptyRule;
import com.facishare.crm.recycling.task.executor.util.SearchUtil;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.service.dto.DeptInfo;
import com.facishare.paas.appframework.common.service.dto.QueryAllSuperDeptsByDeptIds;
import com.facishare.paas.appframework.common.service.dto.QueryDeptByName;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByUserIds;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.common.SqlEscaper;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.crm.recycling.task.executor.util.ConstantUtils.*;


/**
 * @Description
 * <AUTHOR>
 * @Date 2019-02-25 21:19
 */
@Component
@Slf4j
public class CustomerBiz extends BaseBiz {

    @Autowired
    private IObjectDataService objectDataPgService;


    @Autowired
    private MetaDataFindService metaDataFindService;

    @Autowired
    private OrgService orgService;

    @Autowired
    private MetaDataActionService metaDataActionService;

    @Autowired
    private RecyclingTaskGray recyclingTaskGray;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private ConfigService configService;

    /**
     * 根据公海id 和客户状态 查下客户
     * 生命状态:正常
     * biz_status: 已分配
     *
     * @param tenantId
     * @param highSeasId
     * @param limit
     * @return
     */
    public List<IObjectData> getCustomerLimitByHighSeasId(String tenantId, String highSeasId, Integer limit, Integer offset) {

        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(limit);
        searchQuery.setOffset(offset);
        List filters = Lists.newLinkedList();
        SearchUtil.fillFilterEq(filters, BIZ_STATUS, BizStatusEnum.ALLOCATED.getValue());
        SearchUtil.fillFilterEq(filters, LIFE_STATUS, LifeStatusEnum.NORMAL.getValue());
        SearchUtil.fillFilterEq(filters, HIGH_SEAS_ID, highSeasId);
        searchQuery.setFilters(filters);
        searchQuery.setNeedReturnCountNum(false);
        searchQuery.setPermissionType(0);

        List<OrderBy> orders = new ArrayList<>();
        orders.add(new OrderBy(CREATE_TIME, Boolean.TRUE));
        searchQuery.setOrders(orders);

        QueryResult<IObjectData> queryResult;
        try {
            queryResult = metaDataFindService.findBySearchQuery(buildUser(tenantId), ApiNameEnum.ACCOUNT_OBJ.getApiName(), searchQuery);
        } catch (Exception e) {
            log.error(" getCustomerLimitByHighSeasId throw exception {},tenantId:{},", e.getMessage(), tenantId);
            throw new RuntimeException();
        }
        return queryResult == null ? Lists.newArrayList() : queryResult.getData();
    }


    public List<IObjectData> getCustomerLimitByOwner(String tenantId, String owenrId, Integer limit, Integer offset) {

        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(limit);
        searchQuery.setOffset(offset);
        List filters = Lists.newLinkedList();
        SearchUtil.fillFilterEq(filters, BIZ_STATUS, BizStatusEnum.ALLOCATED.getValue());
        SearchUtil.fillFilterIn(filters, LIFE_STATUS, Lists.newArrayList(LifeStatusEnum.NORMAL.getValue(), LifeStatusEnum.IN_CHANGE.getValue()));
        SearchUtil.fillFilterEq(filters, OWNER, owenrId);

        Filter filter = new Filter();
        filter.setOperator(Operator.IS);
        filter.setFieldValues(null);
        filter.setFieldName(HIGH_SEAS_ID);

        searchQuery.setFilters(filters);

        List<OrderBy> orders = new ArrayList<>();
        orders.add(new OrderBy(CREATE_TIME, Boolean.TRUE));
        searchQuery.setOrders(orders);

        QueryResult<IObjectData> queryResult;
        try {
            queryResult = metaDataFindService.findBySearchQuery(buildUser(tenantId), ApiNameEnum.ACCOUNT_OBJ.getApiName(), searchQuery);
        } catch (Exception e) {
            log.error(" getCustomerLimitByOwner throw exception {},tenantId:{},", e.getMessage(), tenantId);
            throw new RuntimeException();
        }
        return queryResult == null ? Lists.newArrayList() : queryResult.getData();
    }


    public List<IObjectData> getAllCustomerLimitByOwner(String tenantId, String owenrId, Integer limit, Integer offset) {

        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(limit);
        searchQuery.setOffset(offset);
        List filters = Lists.newLinkedList();
        SearchUtil.fillFilterEq(filters, BIZ_STATUS, BizStatusEnum.ALLOCATED.getValue());
        SearchUtil.fillFilterIn(filters, LIFE_STATUS, Lists.newArrayList(LifeStatusEnum.NORMAL.getValue(), LifeStatusEnum.IN_CHANGE.getValue()));
        SearchUtil.fillFilterEq(filters, OWNER, owenrId);

        Filter filter = new Filter();
        filter.setOperator(Operator.IS);
        filter.setFieldValues(Lists.newArrayList("0"));
        filter.setFieldName("is_deleted");

        searchQuery.setFilters(filters);

        List<OrderBy> orders = new ArrayList<>();
        orders.add(new OrderBy(CREATE_TIME, Boolean.TRUE));
        searchQuery.setOrders(orders);

        QueryResult<IObjectData> queryResult;
        try {
            queryResult = metaDataFindService.findBySearchQuery(buildUser(tenantId), ApiNameEnum.ACCOUNT_OBJ.getApiName(), searchQuery);
        } catch (Exception e) {
            log.error(" getCustomerLimitByOwner throw exception {},tenantId:{},", e.getMessage(), tenantId);
            throw new RuntimeException();
        }
        return queryResult == null ? Lists.newArrayList() : queryResult.getData();
    }


    /**
     * 根据部门id  递归查下所有员工
     * 生命状态:正常
     * biz_status: 已分配
     *
     * @param tenantId
     * @param deptId
     * @param
     * @return
     */
    public List<String> getMembersByDeptId(String tenantId, String deptId) {
        return getMembersByDeptIds(tenantId, Lists.newArrayList(deptId));
    }

    public List<String> getMembersByDeptIds(String tenantId, List<String> deptIds) {
        List<String> membersByDeptIds;
        try {
            membersByDeptIds = orgService.getMembersByDeptIds(buildUser(tenantId), Lists.newArrayList(deptIds));

        } catch (Exception e) {
            log.error("find ObjectFollowDealSettingObj throw exception {},tenantId:{},", e.getMessage(), tenantId);
            throw new RuntimeException();
        }
        return membersByDeptIds;
    }

    /**
     * 查询需要提醒的公海客户
     * EXPIRE_TIME notnull
     */
    public List<IObjectData> getRemindCustomerLimitByHighSeas(String tenantId, String highSeasId, Integer limit, Integer offset) {
        User user = new User(tenantId, "-10000", "", "");

        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(limit);
        searchQuery.setOffset(offset);
        List filters = Lists.newLinkedList();
        SearchUtil.fillFilterEq(filters, HIGH_SEAS_ID, highSeasId);

        IFilter iFilter = new Filter();
        iFilter.setFieldName(EXPIRE_TIME);
        iFilter.setFieldValues(Lists.newArrayList());
        iFilter.setOperator(Operator.ISN);
        filters.add(iFilter);

        searchQuery.setFilters(filters);
        List<OrderBy> orders = new ArrayList<>();
        orders.add(new OrderBy(CREATE_TIME, Boolean.FALSE));
        searchQuery.setOrders(orders);

        QueryResult<IObjectData> queryResult;
        try {
            queryResult = metaDataFindService.findBySearchQuery(user, ApiNameEnum.ACCOUNT_OBJ.getApiName(), searchQuery);
        } catch (Exception e) {
            log.error("getRemindCustomer throw exception {},tenantId:{},", e.getMessage(), tenantId);
            throw new RuntimeException();
        }
        return queryResult == null ? Lists.newArrayList() : queryResult.getData();
    }

    public List<IObjectData> getRemindCustomerLimitByOwner(String tenantId, String owenrId, Integer limit, Integer offset) {

        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(limit);
        searchQuery.setOffset(offset);
        List filters = Lists.newLinkedList();
        SearchUtil.fillFilterEq(filters, BIZ_STATUS, BizStatusEnum.ALLOCATED.getValue());
        SearchUtil.fillFilterEq(filters, LIFE_STATUS, LifeStatusEnum.NORMAL.getValue());
        SearchUtil.fillFilterEq(filters, OWNER, owenrId);
//        IFilter iFilter = new Filter();
//        iFilter.setFieldName(EXPIRE_TIME);
//        iFilter.setFieldValues(Lists.newArrayList());
//        iFilter.setOperator(Operator.ISN);
//        filters.add(iFilter);
        IFilter iFilter = new Filter();
        iFilter.setFieldName(LIFE_STATUS);
        iFilter.setFieldValues(Lists.newArrayList(LifeStatusEnum.NORMAL.getValue(), LifeStatusEnum.IN_CHANGE.getValue()));
        iFilter.setOperator(Operator.IN);
        filters.add(iFilter);

        IFilter iFilter2 = new Filter();
        iFilter2.setFieldName(HIGH_SEAS_ID);
        iFilter2.setFieldValues(Lists.newArrayList());
        iFilter2.setOperator(Operator.IS);
        filters.add(iFilter2);

        searchQuery.setFilters(filters);
        List<OrderBy> orders = new ArrayList<>();
        orders.add(new OrderBy(CREATE_TIME, Boolean.FALSE));
        searchQuery.setOrders(orders);
        searchQuery.setNeedReturnCountNum(false);
        searchQuery.setPermissionType(0);
        QueryResult<IObjectData> queryResult;
        try {
            queryResult = metaDataFindService.findBySearchQuery(buildUser(tenantId), ApiNameEnum.ACCOUNT_OBJ.getApiName(), searchQuery);
        } catch (Exception e) {
            log.error(" getRemindCustomerLimitByOwner throw exception {},tenantId:{},", e.getMessage(), tenantId);
            throw new RuntimeException();
        }
        return queryResult == null ? Lists.newArrayList() : queryResult.getData();
    }


    /**
     * @param objectId 客户id
     * @return
     */
    public IObjectData getObjectById(String tenantId, String objectId, String describeApiName) {
        IObjectData accountObj;
        try {
            if (objectId == null || tenantId == null || describeApiName == null) {
                log.warn("param is null objectId:{},tenantId:{},describeApiName:{}", objectId, tenantId, describeApiName);
                return null;
            }
            accountObj = objectDataPgService.findById(objectId, tenantId, buildContext(buildUser(tenantId)), describeApiName, true);
        } catch (Exception e) {
            log.error("getObjectById tenantId:{} objectId:{},describeApiName:{},", tenantId, objectId, describeApiName, e);
            throw new RuntimeException();
        }
        return accountObj;
    }

    /**
     * @param objectIds 客户ids
     * @return
     */
    public List<IObjectData> getObjectByIds(String tenantId, List<String> objectIds, String describeApiName) {
        List<IObjectData> objectDatas;
        try {
            objectDatas = objectDataPgService.findByIds(objectIds, tenantId, describeApiName, buildContext(buildUser(tenantId)));
        } catch (Exception e) {
            log.error("getObjectByIds tenantId:{},objectIds:{},describeApiName:{},message:{}", tenantId, objectIds, describeApiName, e);
            throw new RuntimeException();
        }
        return objectDatas;
    }


    public List<IObjectData> getObjectBySearchQuery(String tenantId, List<String> objectIds, String describeApiName) {
        User user = new User(tenantId, "-10000", "", "");

        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(objectIds.size());
        searchQuery.setOffset(0);
        List filters = Lists.newLinkedList();
        List status = Lists.newArrayList(LifeStatusEnum.NORMAL.getValue(), LifeStatusEnum.IN_CHANGE.getValue());
        SearchUtil.fillFilterEq(filters, BIZ_STATUS, BizStatusEnum.ALLOCATED.getValue());
        SearchUtil.fillFilterIn(filters, LIFE_STATUS, status);
        SearchUtil.fillFilterIn(filters, ID, objectIds);
        searchQuery.setFilters(filters);
        List<OrderBy> orders = new ArrayList<>();
        orders.add(new OrderBy(CREATE_TIME, Boolean.FALSE));
        searchQuery.setOrders(orders);

        QueryResult<IObjectData> queryResult;
        try {
            queryResult = metaDataFindService.findBySearchQuery(user, describeApiName, searchQuery);
        } catch (Exception e) {
            log.error("getObjectBySearchQuery throw exception {}", e);
            throw new RuntimeException();
        }
        return queryResult == null ? Lists.newArrayList() : queryResult.getData();
    }


    public List<IObjectData> getContact(String tenantId, String accountId) {
        User user = new User(tenantId, "-10000", "", "");

        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        List filters = Lists.newLinkedList();
        SearchUtil.fillFilterEq(filters, "account_id", accountId);
        searchQuery.setFilters(filters);

        QueryResult<IObjectData> queryResult;
        try {
            queryResult = metaDataFindService.findBySearchQuery(user, ApiNameEnum.CONTACT_OBJ.getApiName(), searchQuery);
        } catch (Exception e) {
            log.info(" getContact by accountId throw exception {},tenantId:{},", e.getMessage(), tenantId);
            log.error("", e);
            throw new RuntimeException();
        }
        return queryResult == null ? Lists.newArrayList() : queryResult.getData();

    }


    /**
     * 清空联系人负责人，联系人归属部门
     *
     * @param tenantId
     * @param objectIds
     */
    public void removeContactOwner(String tenantId, List<String> objectIds, String apiName) {
        List<IObjectData> dataList = new ArrayList<>();
        List<String> updateFieldList = Lists.newArrayList();
        updateFieldList.add(OWNER);
        updateFieldList.add(DATA_OWN_DEPARTMENT);
        updateFieldList.add(OWNER_CHANGED_TIME);

        for (String objectId : objectIds) {
            IObjectData data = new ObjectData();
            data.setTenantId(tenantId);
            data.setDescribeApiName(apiName);
            data.setId(objectId);
            data.setOwner(null);
            data.set(DATA_OWN_DEPARTMENT, Lists.newArrayList());
            data.set(OWNER_CHANGED_TIME, System.currentTimeMillis());
            dataList.add(data);
        }
        try {
            log.info("removeContactOwner dataList:{},updateFieldList:{}", dataList, updateFieldList);
            objectDataPgService.batchUpdateIgnoreOther(dataList, updateFieldList, getDefaultActionContextNotValidate(buildUserForUpdate(tenantId)));
        } catch (MetadataServiceException e) {
            log.error("batchUpdateIgnoreOther error", e);
            throw new RuntimeException();
        }

    }

    /**
     * 根据userId获取部门id
     * 1. 查询employee_rule 获取部门id
     * 2. employee_rule 中没有，查询主属部门id
     *
     * @param tenantId
     * @param userId   用户id
     * @return
     */
    public String getDept(String tenantId, String userId) {

        String dataIdByEmployeeRule = getDataIdByEmployeeRuleNew(tenantId, userId);
        if (StringUtils.isNotBlank(dataIdByEmployeeRule)) {
            // 查询用户是否还在这个部门，
            List<String> members = orgService.getMembersByDeptIds(buildUser(tenantId), Lists.newArrayList(dataIdByEmployeeRule));
            if (CollectionUtils.notEmpty(members) && members.contains(userId)) {
                return dataIdByEmployeeRule;
            }
        }
        Map<String, QueryDeptInfoByUserIds.MainDeptInfo> deptInfo = orgService.getMainDeptInfo(tenantId, userId, Lists.newArrayList(userId));
        if(ObjectUtils.isEmpty(deptInfo) || !deptInfo.containsKey(userId) || ObjectUtils.isEmpty(deptInfo.get(userId).getDeptId())){
            log.warn("deptInfo is empty :{},userId:{}", tenantId, userId);
            return null;
        }
        if(isNeedUserSupDeptRule(tenantId)){
            return getHaveRuleOfDept(tenantId,userId,deptInfo.get(userId).getDeptId());
        }else{
           return deptInfo.get(userId).getDeptId();
        }
    }

    public String getHaveRuleOfDept(String tenantId,String userId,String mainDept){
        //新逻辑主部门没有，就看上级部门有没有
        //获取所有的上级部门以及本身
        Map<String, List<QueryAllSuperDeptsByDeptIds.DeptInfo>> deptInfoMap =orgService.getAllSuperDeptsByDeptIds(tenantId,userId,Lists.newArrayList(mainDept));
        if(ObjectUtils.isEmpty(deptInfoMap) || !deptInfoMap.containsKey(mainDept) || CollectionUtils.empty(deptInfoMap.get(mainDept))){
            log.warn("getHaveRuleOfDept deptInfoMap is null deptInfoMap:{} userId:{}", JSONObject.toJSON(deptInfoMap),userId);
            return null;
        }
        if(deptInfoMap.get(mainDept).size()==1){
            log.info("getHaveRuleOfDept deptInfoMap no have SuperDepts");
            return mainDept;
        }
        List<QueryAllSuperDeptsByDeptIds.DeptInfo> deptInfoList = deptInfoMap.get(mainDept);
        List<String> depyIds = deptInfoList.stream().map(QueryAllSuperDeptsByDeptIds.DeptInfo::getId).collect(Collectors.toList());
       //deptInfoList中只有主部门的上级，不包含当前主部门，要加进去
        depyIds.add(mainDept);
        List<RecyclingRuleInfoModel> recyclingRuleInfoList = commonBiz.getRecyclingRules(tenantId,depyIds,HIGH_SEAS_OBJ);
        if(CollectionUtils.empty(recyclingRuleInfoList)){
            log.info("getHaveRuleOfDept recyclingRuleInfoList is null");
            return mainDept;
        }
        Map<String,RecyclingRuleInfoModel> recyclingRuleInfoMap = recyclingRuleInfoList.stream().collect(Collectors.toMap(RecyclingRuleInfoModel::getDataId, Function.identity(),(v1,v2)->v1));
        //先判断主部门是否有规则，没有则从上级部门中获取
        if(recyclingRuleInfoMap.containsKey(mainDept) && ObjectUtils.isNotEmpty(recyclingRuleInfoMap.get(mainDept))){
            return mainDept;
        }
        //从上级部门中获取，deptInfoList是倒序存放的上级部门
        for(int i=deptInfoList.size()-1;i>=0;i--){
            QueryAllSuperDeptsByDeptIds.DeptInfo dept = deptInfoList.get(i);
            if(recyclingRuleInfoMap.containsKey(dept.getId()) && ObjectUtils.isNotEmpty(recyclingRuleInfoMap.get(dept.getId()))){
                return dept.getId();
            }
        }
        log.error("getHaveRuleOfDept 没有找到回收规则");
        return mainDept;
    }

    public boolean isNeedUserSupDeptRule(String tenantId){
        String findSql = String.format("select id,tenant_id,object_api_name,department_rule from biz_recycling_rule where tenant_id='%s' and object_api_name='HighSeasObj'  and data_type = 2 and is_deleted =0 limit 1;", SqlEscaper.pg_escape(tenantId));
        List<Map> bySql = new ArrayList<>();
        try {
            bySql = objectDataPgService.findBySql(tenantId, findSql);
            if(CollectionUtils.notEmpty(bySql) && bySql.get(0).containsKey("department_rule") && ObjectUtils.isNotEmpty(bySql.get(0).get("department_rule"))){
                Map<String,Object> map = bySql.get(0);
                String departmentRule = map.get("department_rule").toString();
                if(DepartmentRuleType.MAIN_AND_SUP_DEPARTMENT.getValue().equals(departmentRule)){
                    return true;
                }
            }
        } catch (MetadataServiceException e) {
            log.error("isNeedUserSupDeptRule error:", e);
        }
        return false;
    }

    //    todo 灰度控制
    public String getDataIdByEmployeeRule(String tenantId, String userId) {
        String sql = "select data_id from employee_rule where data_type = 1 "
                + String.format("and ei = '%s' ", tenantId)
                + String.format("and employee_id = %s ", Integer.parseInt(userId));
        List<Map> bySql = new ArrayList<>();
        try {
            bySql = objectDataPgService.findBySql(tenantId, sql);
        } catch (MetadataServiceException e) {
            log.error("find employee_rule error:{}", e.getMessage());
        }

        if (CollectionUtils.notEmpty(bySql)) {
            return bySql.get(0).get("data_id").toString();
        }
        return null;
    }


    /**
     * 移除相关团队
     *
     * @param tenantId
     * @param objectDataList
     */
    public void removeInnerRelevantTeam(String tenantId, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        objectDataList.forEach(objectData -> {
            ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
            List<TeamMember> outMemberList = objectDataExt.getTeamMembers().stream().filter(TeamMember::isOutMember).collect(Collectors.toList());
            List<TeamMember> teamMembers = Lists.newArrayList();
            if (CollectionUtils.notEmpty(outMemberList)) {
                teamMembers.addAll(outMemberList);
            }
            objectDataExt.setTeamMembers(teamMembers);
        });
        metaDataActionService.batchUpdateRelevantTeam(buildUser(tenantId), objectDataList, false);
    }

    /**
     * @param tenantId
     * @param objectDataList
     */
    public void removeObjectOwner(String tenantId, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        removeObjectOwner(objectDataList);
        removeObjectTeamMember(buildUser(tenantId), objectDataList);
    }

    public void removeObjectInnerOwner(String tenantId, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        removeObjectInnerOwner(objectDataList);
        removeObjectTeamMember(buildUser(tenantId), objectDataList);
    }

    private void removeObjectInnerOwner(List<IObjectData> objectDataList) {
        objectDataList.forEach(objectData -> {
            ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
            List<TeamMember> teamMembers = objectDataExt.getTeamMembers();
            List<TeamMember> deleteMember = teamMembers.stream()
                    .filter(f -> TeamMember.Role.OWNER.equals(f.getRole()) && !f.isOutMember()).collect(Collectors.toList());
            teamMembers.removeAll(deleteMember);
            objectDataExt.setTeamMembers(teamMembers);
        });
    }

    public void removeObjectAllTeamMember(User user, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        objectDataList.forEach(objectData -> {
            ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
            List<TeamMember> teamMembers = Lists.newArrayList();
            objectDataExt.setTeamMembers(teamMembers);
        });
        metaDataActionService.batchUpdateRelevantTeam(user, objectDataList, false);
    }

    public void removeObjectTeamMember(User user, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        metaDataActionService.batchUpdateRelevantTeam(user, objectDataList, false);
    }


    private void removeObjectOwner(List<IObjectData> objectDataList) {
        objectDataList.forEach(objectData -> {
            ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
            List<TeamMember> teamMembers = objectDataExt.getTeamMembers();
            List<TeamMember> deleteMember = teamMembers.stream()
                    .filter(f -> TeamMember.Role.OWNER.equals(f.getRole())).collect(Collectors.toList());
            teamMembers.removeAll(deleteMember);
            objectDataExt.setTeamMembers(teamMembers);
        });
    }


    public void removeObjectTeamMember(User user, List<String> employeeIDs, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        objectDataList.forEach(objectData -> removeObjectTeamMember(employeeIDs, objectData));
        metaDataActionService.batchUpdateRelevantTeam(user, objectDataList, false);
    }


    private void removeObjectTeamMember(List<String> employeeIDs, IObjectData objectData) {
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        List<TeamMember> teamMembers = objectDataExt.getTeamMembers();
        for (String teamMemberId : employeeIDs) {
            List<TeamMember> deleteMember = teamMembers.stream()
                    .filter(f -> f.getEmployee().equals(teamMemberId)).collect(Collectors.toList());
            teamMembers.removeAll(deleteMember);
        }
        objectDataExt.setTeamMembers(teamMembers);
    }


    private IActionContext buildContext(User user) {
        return this.buildContext(user, false);
    }


    private IActionContext buildContext(User user, boolean allowUpdateInvalid) {
        return ActionContextExt.of(user, RequestContextManager.getContext())
                .allowUpdateInvalid(allowUpdateInvalid)
                .disableDeepQuote()
                .getContext();
    }


    /**
     * rule_type :1 回收，2保有量
     *
     * @param tenantId
     * @param userId
     * @return
     */
    public String getDataIdByEmployeeRuleNew(String tenantId, String userId) {
        String sql = "select data_id from biz_employee_rule where rule_type = '1' "
                + String.format("and tenant_id = '%s' ", tenantId)
                + String.format("and employee_id = '%s' ", userId);
        List<Map> bySql = new ArrayList<>();
        try {
            bySql = objectDataPgService.findBySql(tenantId, sql);
        } catch (MetadataServiceException e) {
            log.error("find employee_rule error:{}", e.getMessage());
        }

        if (CollectionUtils.notEmpty(bySql)) {
            return bySql.get(0).get("data_id").toString();
        }
        return null;
    }

    public List<String> getAllAccountIdsByhighSeasId(String tenantId, String highSeasId) {
        String sql = "select id from biz_account where "
                + String.format(" tenant_id = '%s' ", tenantId)
                + String.format("and high_seas_id = '%s' ", highSeasId)
                + " and is_deleted = 0 and biz_status = 'allocated' and life_status in('normal','in_change') ";
        List<Map> ret = new ArrayList<>();
        try {
            ret = objectDataPgService.findBySql(tenantId, sql);
        } catch (MetadataServiceException e) {
            log.error("find getAllAccountIdsByhighSeasId error:{}", sql, e);
        }
        if (CollectionUtils.empty(ret)) {
            return Lists.newArrayList();
        }
        return ret.stream().filter(x -> x.get("id") != null).map(x -> x.get("id").toString()).collect(Collectors.toList());
    }

    public List<String> getAllIdsByOwnerId(String tenantId, String ownerId) {
        String sql = "select id from biz_account where "
                + String.format(" tenant_id = '%s' ", tenantId)
                + String.format("and owner = '%s' ", ownerId)
                + " and is_deleted = 0 and biz_status = 'allocated' and life_status in('normal','in_change')  ";
        List<Map> ret = new ArrayList<>();
        try {
            ret = objectDataPgService.findBySql(tenantId, sql);
        } catch (MetadataServiceException e) {
            log.error("find getAllAccountIdsByOwnerId error:{}", sql, e);
        }
        if (CollectionUtils.empty(ret)) {
            return Lists.newArrayList();
        }
        return ret.stream().filter(x -> x.get("id") != null).map(x -> x.get("id").toString()).collect(Collectors.toList());
    }

    public List<String> getAllAccountIdsByOwnerId(String tenantId, String ownerId) {
        String sql = "select id from biz_account where "
                + String.format(" tenant_id = '%s' ", tenantId)
                + String.format("and owner = '%s' ", ownerId)
                + " and is_deleted = 0 and biz_status = 'allocated' and life_status in('normal','in_change') and high_seas_id is null  ";
        List<Map> ret = new ArrayList<>();
        try {
            ret = objectDataPgService.findBySql(tenantId, sql);
        } catch (MetadataServiceException e) {
            log.error("find getAllAccountIdsByOwnerId error:{}", sql, e);
        }
        if (CollectionUtils.empty(ret)) {
            return Lists.newArrayList();
        }
        return ret.stream().filter(x -> x.get("id") != null).map(x -> x.get("id").toString()).collect(Collectors.toList());
    }


    public void objectOutTeamHandle(String tenantId, IObjectData poolData, List<IObjectData> dataList) {
        PoolEmptyRule.EmptyRule emptyRule = new PoolEmptyRule.Builder().poolData(Lists.newArrayList(poolData)).build().getEmptyRuleById(poolData.getId());
        if (emptyRule == null || !Boolean.TRUE.equals(emptyRule.getRecyclingOutTeamMember())) {
            log.debug("未满足清空规则， api:{}, tenant:{}, emptyRule:{}", poolData.getDescribeApiName(), tenantId, emptyRule == null ? null : emptyRule.toString());
            return;
        }
        User user = buildUser(tenantId);
        log.debug("begin objectOutTeamHandle, apiName:{}, tenant:{}", poolData.getDescribeApiName(), user.getTenantId());
        List<String> updateFields = Lists.newArrayList();
        // 移除合作伙伴id
        if (openPartner(tenantId)) {
            removeObjectPartnerByOutOwnerNotUpdate(user, dataList);
            updateFields.add(ObjectDataExt.PARTNER_ID);
        }
        // 移除外部负责人
        boolean updateOutOwner = removeObjectOutOwnerNotUpdate(dataList);
        if (updateOutOwner) {
            updateFields.addAll(Lists.newArrayList(ObjectDataExt.OUTER_TENANT, ObjectDataExt.OUTER_OWNER));
        }
        if (CollectionUtils.notEmpty(updateFields)) {
            metaDataActionService.batchUpdateByFields(user, dataList, updateFields);
            if (updateOutOwner) {
                log.debug("updateOutOwner, dataList:{}, tenant:{}", dataList, user.getTenantId());
                metaDataActionService.batchUpdateRelevantTeam(user, dataList, false);
            }
        }
        if (Boolean.TRUE.equals(emptyRule.getRecyclingOutOrdinaryMember())) {
            // 移除普通成员
            boolean update = removedOutOrdinaryMemberNotUpdate(dataList);
            if (update) {
                log.debug("objectOutTeamHandle removedOutOrdinaryMember, apiName:{}, tenant:{}", poolData.getDescribeApiName(), user.getTenantId());
                metaDataActionService.batchUpdateRelevantTeam(user, dataList, false);
            }
        }
    }

    private boolean openPartner(String tenantId) {
        User user = new User(tenantId, "-10000");
        String config1 = configService.findTenantConfig(user, "37");
        if ("1".equals(config1)) {
            return true;
        }
        String config2 = configService.findTenantConfig(user, "config_partner_open");
        return "open".equals(config2);
    }

    private boolean removedOutOrdinaryMemberNotUpdate(List<IObjectData> removedOutOwnerDataList) {
        boolean update = false;
        for (IObjectData data : removedOutOwnerDataList) {
            ObjectDataExt objectDataExt = ObjectDataExt.of(data);
            List<TeamMember> teamMembers = objectDataExt.getTeamMembers();
            List<TeamMember> outOrdinaryMembers = teamMembers.stream()
                    .filter(f -> !TeamMember.Role.OWNER.equals(f.getRole())
                            && f.isOutMember()).collect(Collectors.toList());
            if (CollectionUtils.notEmpty(outOrdinaryMembers)) {
                update = true;
            }
            teamMembers.removeAll(outOrdinaryMembers);
            objectDataExt.setTeamMembers(teamMembers);
        }
        return update;
    }

    private boolean removeObjectOutOwnerNotUpdate(List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return false;
        }
        objectDataList.forEach(objectData -> {
            objectData.setOutTenantId(null);
            objectData.setOutOwner(Lists.newArrayList());
            ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
            List<TeamMember> teamMembers = objectDataExt.getTeamMembers();
            List<TeamMember> outOwnerMembers = teamMembers.stream()
                    .filter(f -> TeamMember.Role.OWNER.equals(f.getRole())
                            && f.isOutMember()).collect(Collectors.toList());
            teamMembers.removeAll(outOwnerMembers);
            objectDataExt.setTeamMembers(teamMembers);
        });
        return true;
    }

    private void removeObjectPartnerByOutOwnerNotUpdate(User user, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        List<IObjectData> removedPartnerObjectDataList = objectDataList.stream()
                .filter(d -> StringUtil.isNotBlank(d.getOutTenantId())
                        && StringUtil.isNotBlank(d.get("partner_id", String.class, "")))
                .collect(Collectors.toList());
        PrmModel.MatchOutOwnerResult matchOutOwnerResult = commonBiz.matchOutOwner(user, removedPartnerObjectDataList);
        List<String> notMatchIds = matchOutOwnerResult.getNotMatchIds();
        objectDataList.stream().filter(o -> !notMatchIds.contains(o.getId())).forEach(objectData -> {
            objectData.set("partner_id", null);
        });
    }

    public void removeRelevantTeam(String tenantId, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        objectDataList.forEach(objectData -> {
            ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
            objectDataExt.setTeamMembers(Lists.newArrayList());
        });
        metaDataActionService.batchUpdateRelevantTeam(buildUser(tenantId), objectDataList, false);
    }
}
