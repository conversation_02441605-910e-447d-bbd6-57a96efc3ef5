package com.facishare.crm.recycling.task.executor.consumer;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.lto.common.SFAJedisLock;
import com.facishare.paas.appframework.common.mq.RocketMQException;
import org.apache.rocketmq.common.message.MessageExt;
import com.facishare.crm.recycling.task.executor.common.SfaRecyclingTaskRateLimiterService;
import com.facishare.crm.recycling.task.executor.model.ObjectLimitRuleCalculateModel;
import com.facishare.crm.recycling.task.executor.service.impl.ObjectLimitRuleCalculate;
import com.facishare.paas.appframework.common.mq.RocketMQMessageListener;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.github.jedis.support.JedisCmd;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 执行器接收MQ入口
 */
@Slf4j
public class ObjectLimitRuleCalculateListener implements RocketMQMessageListener {

    @Autowired
    private ObjectLimitRuleCalculate objectLimitRuleCalculate;

    @Autowired
    private SfaRecyclingTaskRateLimiterService sfaRecyclingTaskRateLimiterService;

    @Autowired
    protected JedisCmd SFAJedisCmd;

    @Override
    public void consumeMessage(List<MessageExt> messages) {
        sfaRecyclingTaskRateLimiterService.getObjectLimitRuleLimiter().acquire();
        if (CollectionUtils.empty(messages)) {
            return;
        }

        for (MessageExt message : messages) {
            log.info("message:{}", message);
            consumeMessage(message);
        }
    }

    private void consumeMessage(MessageExt body) {
        try {
            ObjectLimitRuleCalculateModel.ObjectLimitRuleCalculateMessage message = JSON.parseObject(body.getBody(),
                    ObjectLimitRuleCalculateModel.ObjectLimitRuleCalculateMessage.class);
            log.warn("msgId:{},ObjectLimitRuleCalculateMessage:{}", body.getMsgId(),message);
            String redisKey = getLimitRuleRedisKey(message);
            SFAJedisLock jedisLock = new SFAJedisLock(SFAJedisCmd, redisKey,5 * 1000);
            if (jedisLock.waitTryLock()){
                objectLimitRuleCalculate.execute(message);
            }
            jedisLock.close();
        } catch (Exception e) {
            log.error("ObjectLimitRuleCalculateListener consumeMessage {}", body.getMsgId(),e);
            throw new RocketMQException(e.getMessage());
        }
    }

    private String getLimitRuleRedisKey(ObjectLimitRuleCalculateModel.ObjectLimitRuleCalculateMessage message){
        return String.format("%s-%s-%s-%s","limit", message.getTenantId(), message.getObjectApiName(), message.getGroupId());
    }
}
