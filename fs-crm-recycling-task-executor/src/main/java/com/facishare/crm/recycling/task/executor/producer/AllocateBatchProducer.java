package com.facishare.crm.recycling.task.executor.producer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.github.autoconf.ConfigFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.common.message.Message;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

/**
 * @Description 回收后批量分配的MQ
 * <AUTHOR>
 * @Date 2021/4/7 22:30
 */


@Component
@Slf4j
public class AllocateBatchProducer {
    private static final String MQ_PRODUCER_CONFIG_NAME = "sfa-recycling-recalculate-producer";
    private static final String MQ_TOPIC_CONFIG_KEY = "recycling.rule.topic";
    private static final String TAGS = "leads_allocate_batch";
    private String topic;
    private AutoConfMQProducer producer;

    @PostConstruct
    public void init() {
        producer = new AutoConfMQProducer(MQ_PRODUCER_CONFIG_NAME);
        ConfigFactory.getConfig(MQ_PRODUCER_CONFIG_NAME, c -> topic = c.get(MQ_TOPIC_CONFIG_KEY));
    }

    @PreDestroy
    public void destroy() {
        producer.close();
    }

    /**
     * 发送消息
     *
     * @param
     */
    public void sendAllocateMQ(String tags, JSONObject messageObject, String tenantId) {
        log.info("sendAllocateMQ:{},msg:{}", tags, messageObject);
        String messageString = JSON.toJSONString(messageObject);
        Message message = new Message(topic, tags, messageString.getBytes());
        message.putUserProperty("x-fs-ei", tenantId);
        message.setTags(tags);

        SendResult sendResult = producer.send(message, (mqs, msg, arg) -> mqs.get(Math.abs(arg.hashCode() & 0x7fffffff) % mqs.size()), tenantId);

        if (sendResult.getSendStatus() != SendStatus.SEND_OK) {
            throw new RuntimeException("send mq failed. " + sendResult.getSendStatus());
        }
    }

    public void sendAllocateTask(String tenantId, String leadsId, String poolId) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("tenantId", tenantId);
        jsonObject.put("objectId", leadsId);
        jsonObject.put("allocateOperationSource", 2);
        jsonObject.put("batch", 0);
        jsonObject.put("leadsPoolId", poolId);
        sendAllocateMQ(TAGS, jsonObject, tenantId);
    }

}
