package com.facishare.crm.recycling.task.executor.model;

import com.facishare.paas.appframework.core.exception.ValidateException;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface ObjectLimitRuleCalculateModel {

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class ObjectLimitRuleCalculateMessage {
        private String tenantId;
        private String objectApiName;
        private String groupId;
        private String currentCalculateId;
    }
}
