package com.facishare.crm.recycling.task.executor.service.impl;

import com.facishare.crm.recycling.task.executor.biz.CustomerBiz;
import com.facishare.crm.recycling.task.executor.biz.RecyclingBiz;
import com.facishare.crm.recycling.task.executor.biz.RecyclingRemoteBiz;
import com.facishare.crm.recycling.task.executor.biz.RemindBiz;
import com.facishare.crm.recycling.task.executor.enums.ApiNameEnum;
import com.facishare.crm.recycling.task.executor.enums.LifeStatusEnum;
import com.facishare.crm.recycling.task.executor.enums.RemindRecordTypeEnum;
import com.facishare.crm.recycling.task.executor.model.RecyclingRemindRule;
import com.facishare.crm.recycling.task.executor.model.RemindMessage;
import com.facishare.crm.recycling.task.executor.model.RuleTypeRemindTime;
import com.facishare.crm.recycling.task.executor.util.DateUtils;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.fxiaoke.paas.gnomon.api.NomonProducer;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.facishare.crm.recycling.task.executor.enums.RuleTypeEnum.ruleTypeOf;
import static com.facishare.crm.recycling.task.executor.util.ConstantUtils.*;
import static com.facishare.crm.recycling.task.executor.util.I18NKey.*;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-03-14 17:18
 * todo 优化，去掉补丁
 */
@Slf4j
@Component
public class AccountRemindService extends AbstractRemindService {


    @Autowired
    private CustomerBiz customerBiz;


    @Autowired
    private NomonProducer nomonProducer;

    @Autowired
    private RecyclingRemoteBiz recyclingRemoteBiz;

    @Autowired
    private AccountRecalculate accountRecalculate;

    @Autowired
    private IObjectDescribeService objectDescribeService;

    @Autowired
    private RemindBiz remindBiz;

    @Autowired
    private RecyclingBiz recyclingBiz;


    private static final Long CURRENT_TIME = 0L;

    @Override
    public void execute(RemindMessage remindMessage) {
        super.execute(remindMessage);


        log.info("AccountRemindService execute:{}", remindMessage);
        Date remindTime = remindMessage.getRemindTime();
        if (remindTime == null){
            return;
        }

        List<RuleTypeRemindTime> remindTimes = remindMessage.getRuleTypeRemindTimes().stream().filter(
                x -> x.getRemindTime().compareTo(remindTime.getTime()) == 0).collect(Collectors.toList());


        IObjectData customer = customerBiz.getObjectById(remindMessage.getTenantId(), remindMessage.getObjectId(), ApiNameEnum.ACCOUNT_OBJ.getApiName());
        if (customer == null){
            log.warn("AccountRemindService customer is null {}",remindMessage);
            return;
        }

        if (LifeStatusEnum.INVALID.getValue().equals(customer.get(LIFE_STATUS))){
            log.info("customer is invalid :{},{}",remindMessage.getTenantId(),remindMessage.getObjectId());
            return;
        }

        if (customer.get(EXPIRE_TIME)==null || StringUtils.isBlank(customer.get(EXPIRE_TIME).toString())){
            log.info("expire_time is null ,{}",remindMessage.getObjectId());
            return;
        }

        String content = customer.getName() + " ";
        StringBuilder sb = new StringBuilder(content);
        boolean isHighSeas = false;
        RemindRecordTypeEnum recordType = RemindRecordTypeEnum.NO_HIGH_SEAS_REMIND;
        String highSeasName = null;
        if (StringUtils.isNotBlank(customer.get(HIGH_SEAS_ID, String.class))) {
            IObjectData highSea = customerBiz.getObjectById(remindMessage.getTenantId(), remindMessage.getObjectId(), ApiNameEnum.HIGH_SEAS_OBJ.getApiName());
            if (highSea != null) {
                highSeasName = highSea.getName();
                content += "在公海 " + highSea.getName() + " ";
            }
            recordType = RemindRecordTypeEnum.HIGH_SEAS_REMIND;
            isHighSeas = true;
        }
        for (RuleTypeRemindTime time : remindTimes) {
            String contentKey = "";
            List<String> contentParams =null;
            if(highSeasName == null){
                highSeasName = "";
            }
            switch (ruleTypeOf(time.getRuleType())) {
                case DEAL_DAYS_REMIND:
                    content += "已经" + time.getDays() + "天没有新的成交";
                    contentKey = SFA_NO_HIGH_SEAS_DEAL_DAYS_REMIND_NEW_RECORD;
                    contentParams = Lists.newArrayList(customer.getName(),time.getDays().toString());
                    if(isHighSeas){
                        contentKey = SFA_HIGH_SEAS_DEAL_DAYS_REMIND_NEW_RECORD;
                        contentParams = Lists.newArrayList(customer.getName(),highSeasName,time.getDays().toString());
                    }
                    break;
                case FOLLOW_UP_DAYS_REMIND:
                    content += "已经" + time.getDays() + "天未跟进";
                    contentKey = SFA_NO_HIGH_SEAS_FOLLOW_UP_DAYS_REMIND_NEW_RECORD;
                    contentParams = Lists.newArrayList(customer.getName(),time.getDays().toString());
                    if(isHighSeas){
                        contentKey = SFA_HIGH_SEAS_FOLLOW_UP_DAYS_REMIND_NEW_RECORD;
                        contentParams = Lists.newArrayList(customer.getName(),highSeasName,time.getDays().toString());
                    }
                    break;
                case RECYCLING:
                    sb.append("将在").append(time.getDays()).append("天后被收回");
                    content = sb.toString();
                    recordType = RemindRecordTypeEnum.CUSTOMER_RECYCLING;
                    contentKey = SFA_WILL_RECYCLING_NEW_RECORD;
                    contentParams = Lists.newArrayList(customer.getName(),time.getDays().toString());
                    if(isHighSeas){
                        contentKey = SFA_HIGH_SEADS_WILL_RECYCLING_NEW_RECORD;
                        contentParams =Lists.newArrayList(customer.getName(),highSeasName,time.getDays().toString());
                    }
                    break;
                default:
                    break;
            }
            //发送新题型
            remindBiz.sendRemind(customer,remindMessage.getTenantId(),content,recordType,
                    null, null,null, contentKey,contentParams);
        }

        //还未提醒的（提醒时间大于此次提醒时间）,更新该提醒task，下次到期提醒
        List<RuleTypeRemindTime> nonRemindTimes = remindMessage.getRuleTypeRemindTimes().stream().filter(
                x -> x.getRemindTime().compareTo(remindTime.getTime()) > 0)
                .sorted(Comparator.comparing(RuleTypeRemindTime::getRemindTime)).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(nonRemindTimes)) {
            return;
        }

        remindMessage.setRuleTypeRemindTimes(nonRemindTimes);
        remindMessage.setRemindTime(new Date(nonRemindTimes.get(0).getRemindTime()));
        // 发送未到时间的提醒任务到数据库
        accountRecalculate.sendRemindTask(remindMessage);
    }


    public boolean needRemind(IObjectData objectData, List<RecyclingRemindRule> rules) {
        Object expireTime = objectData.get("expire_time").toString();
        int remindDays = rules.get(0).getRemindDays();
        boolean isTime = isTime(expireTime, remindDays);
        //需要提醒
        return isTime;
    }



    /**
     * 是否到提醒时间
     *
     * @param expireTime
     * @param remindDays
     * @return
     */
    private static boolean isTime(Object expireTime, int remindDays) {

        Long aLong = DateUtils.beforeDays((Long) expireTime, remindDays);

        return CURRENT_TIME.compareTo(aLong) >= 0;
    }

//    public void sendRemindTask(RemindMessage remindMessage, Date expireTime) {
//
//        List<RuleTypeRemindTime> ruleTypeRemindTimes = remindMessage.getRuleTypeRemindTimes();
//        List<RuleTypeRemindTime> remindTimes = ruleTypeRemindTimes.stream().filter(
//                x -> x.getRemindTime().compareTo(expireTime.getTime()) <= 0).collect(Collectors.toList());
//
//        RuleTypeRemindTime ruleTypeRemindTime = RuleTypeRemindTime.builder()
//                .remindTime(expireTime.getTime())
//                .ruleType(RuleTypeEnum.RECYCLING.getValue())
//                .build();
//
//        remindTimes.add(ruleTypeRemindTime);
//        remindMessage.setRuleTypeRemindTimes(remindTimes);
//        NomonMessage nomonMessage = NomonMessage.builder()
//                .biz("crm_object_recycling_task_remind")
//                .tenantId(remindMessage.getTenantId())
//                .dataId(remindMessage.getObjectId())
//                .executeTime(remindMessage.getRemindTime())
//                .callArg(JSONObject.toJSON(remindMessage).toString())
//                .build();
//        nomonProducer.send(nomonMessage);
//        log.info("send,nomonMessage:{}", nomonMessage.toString());
//    }

}
