package com.facishare.crm.recycling.task.executor.service;

import com.alibaba.druid.util.StringUtils;
import com.facishare.crm.recycling.task.executor.util.ProxyUtils;
import com.facishare.crm.sfa.lto.rest.models.PAASContext;
import com.facishare.crm.sfa.lto.rest.models.UserRoleModel;
import com.facishare.crm.sfa.lto.rest.PaasUserRoleProxy;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.service.OrgServiceProxy;
import com.facishare.paas.appframework.common.service.dto.QueryAllSuperDeptsByDeptIds;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByUserId;
import com.facishare.paas.appframework.common.service.dto.QueryGroupByIds;
import com.facishare.paas.appframework.common.service.dto.QueryMembersByGroupIds;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.facishare.crm.recycling.task.executor.util.ConstantUtils.ALL_COMPANY_ID;
import static com.facishare.crm.recycling.task.executor.util.ConstantUtils.CRM_APP_ID;


/**
 * @Description 组织架构接口数据处理
 * <AUTHOR>
 * @Date 2020/11/26 16:12
 */
@Slf4j
@Component
public class OrgServiceCommonService {

    @Autowired
    private OrgServiceProxy orgServiceProxy;

    @Autowired
    private PaasUserRoleProxy paasUserRoleProxy;

    @Autowired
    private OrgService orgService;

    /**
     *
     * @param tenantId
     * @param userGroupIds
     * @return
     */
    public List<String> getMembersByGroupIds(String tenantId,List<String> userGroupIds){
        List<String> members = Lists.newArrayList();
        if(CollectionUtils.empty(userGroupIds)){
            return members;
        }
        QueryMembersByGroupIds.Arg arg = QueryMembersByGroupIds.Arg.builder().appId(CRM_APP_ID)
                .userId(User.SUPPER_ADMIN_USER_ID)
                .groupIdList(userGroupIds)
                .tenantId(tenantId).build();
        QueryMembersByGroupIds.Result rstResult = orgServiceProxy.getMembersByGroupIds(arg);
        Set<Integer> result = rstResult.getResult();
        if (CollectionUtils.empty(result)){
            return members;
        }
        for (Integer i : result) {
            members.add(i.toString());
        }
        return members;
    }

    public Set<String> getUserIdsByRoleIds(String tenantId, List<String> userRoleIds) {
        if(CollectionUtils.empty(userRoleIds)){
            return Sets.newHashSet();
        }
        Set<String> members = Sets.newHashSet();
        Map<String, List<String>> roleUsersByRoleIds = getRoleUsersByRoleIds(tenantId, userRoleIds);
        if (CollectionUtils.notEmpty(roleUsersByRoleIds)) {
            for (Map.Entry<String, List<String>> entry : roleUsersByRoleIds.entrySet()) {
                members.addAll(entry.getValue());
            }
        }
        return members;
    }

    public  Map<String,List<String>> getRoleUsersByRoleIds(String tenantId, List<String> userRoleIds) {
        if(CollectionUtils.empty(userRoleIds)){
            return Maps.newHashMap();
        }
        Map<String, List<String>> members;
        try {
            PAASContext contextArg = buildPAASContext(tenantId);
            UserRoleModel.UserRoleListWithContextArg arg = UserRoleModel.UserRoleListWithContextArg.builder().authContext(contextArg)
                    .roles(userRoleIds).build();
            UserRoleModel.UserRoleMapResult rstResult = paasUserRoleProxy.queryRoleUsersByRoles(ProxyUtils.getCrmHeader(tenantId), arg);
            members = rstResult.getResult();
        } catch (Exception e) {
            throw new RuntimeException();
        }
        return members;
    }

    /**
     * 获取部门树
     * @param tenantId
     * @param userId
     * @return
     */
    public List<String> getNDeptPathByUserId(String tenantId, String userId){
        List<String> deptIds = getDepartmentByUserId(tenantId, userId);
        List<String> allSuperDepartmentByIds = getAllSuperDepartmentByIds(tenantId, deptIds);
        return allSuperDepartmentByIds;
    }


    public List<String> getDepartmentByUserId(String tenantId, String userId) {
        List<QueryDeptInfoByUserId.DeptInfo> deptInfos = orgService.getDeptInfoByUserId(tenantId, User.SUPPER_ADMIN_USER_ID, userId);
        List<String> result = deptInfos.stream().map(x -> x.getDeptId()).collect(Collectors.toList());
        return result;
    }

    public List<String> getAllSuperDepartmentByIds(String tenantId, List<String> departmentIds) {
        Map<String, List<QueryAllSuperDeptsByDeptIds.DeptInfo>> deptInfosMap = orgService.getAllSuperDeptsByDeptIds(tenantId, User.SUPPER_ADMIN_USER_ID, departmentIds);
        Set<String> result = Sets.newHashSet();
        for(Map.Entry<String, List<QueryAllSuperDeptsByDeptIds.DeptInfo>> keyValue : deptInfosMap.entrySet()){
            result.addAll(keyValue.getValue().stream().filter(x-> !ALL_COMPANY_ID.equals(x.getId())).map(x -> x.getId()).collect(Collectors.toList()));
        }

        return Lists.newArrayList(result);
    }

    public List<String> getUserGroupIdsByMemberId(String tenantId, String memberId) {
        if(StringUtils.isEmpty(memberId)){
            return Lists.newArrayList();
        }
        List<String> userGroups = new ArrayList<>();
        try {
            QueryGroupByIds.Arg arg = QueryGroupByIds.Arg.builder().appId(CRM_APP_ID)
                    .isPublic(true).status(0).userId(User.SUPPER_ADMIN_USER_ID).tenantId(tenantId).build();
            QueryGroupByIds.Result rstResult = orgServiceProxy.queryGroupByIds(arg);
            List<QueryGroupByIds.UserGroupInfo> result = rstResult.getResult();
            if (CollectionUtils.notEmpty(result)){
                result.forEach(x-> userGroups.add(x.getId()));
            }
        } catch (Exception e) {
            log.error("getUserGroupIdsByMemberId throw exception {},tenantId:{}, memberId:{}", e.getMessage(), tenantId, memberId,e);
            throw new RuntimeException();
        }
        return userGroups;
    }


    private static PAASContext buildPAASContext(String tenantId) {
        PAASContext context = PAASContext.builder().appId(CRM_APP_ID)
                .tenantId(tenantId).userId(User.SUPPER_ADMIN_USER_ID).build();
        return context;
    }
}
