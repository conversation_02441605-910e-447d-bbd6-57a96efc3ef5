package com.facishare.crm.recycling.task.executor.consumer;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.lto.equityrelationship.model.EquityRelationshipDataModel;
import com.facishare.crm.sfa.lto.equityrelationship.service.EquityRelationshipService;
import com.facishare.crm.sfa.lto.marketingattribution.MarketingAttributionService;
import com.facishare.crm.sfa.lto.objectpool.IObjectPoolService;
import com.facishare.crm.sfa.lto.objectpool.ObjectPoolFactory;
import com.facishare.crm.sfa.lto.objectpool.models.ObjectPoolActionModels;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.nio.charset.Charset;
import java.util.UUID;

@Slf4j
@Component
public class EquityRelationshipCreateListener implements ApplicationListener<ContextRefreshedEvent> {

    private AutoConfMQPushConsumer consumer;
    @Autowired
    private EquityRelationshipService equityRelationshipService;
    @Autowired
    private MarketingAttributionService marketingAttributionService;
    @Autowired
    protected ObjectPoolFactory objectPoolFactory;

    @PostConstruct
    public void init() {
        consumer = new AutoConfMQPushConsumer("sfa-recalculate-consumer", "sfa-equity-relationship-create", (MessageListenerConcurrently) (msgs, context) -> {
            if (!msgs.isEmpty()) {
                for (MessageExt msg : msgs) {
                    consumeMessage(msg);
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        });
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (consumer != null && event.getApplicationContext().getParent() == null) {
            consumer.start();
        }
    }

    @PreDestroy
    public void close() {
        consumer.close();
    }

    private void consumeMessage(MessageExt messageExt) {
        try {
            String body = new String(messageExt.getBody(), Charset.forName("UTF-8"));
            if (messageExt.getReconsumeTimes() > 2) {
                return;
            }
            TraceContext.get().setTraceId(UUID.randomUUID()+"-EquityRelationshipCreateListener-"+System.currentTimeMillis());
            switch (messageExt.getTags()) {
                case "sfa-equity-relationship-create":
                    EquityRelationshipDataModel.RelationshipCreateMsg msg = JSON.parseObject(body, EquityRelationshipDataModel.RelationshipCreateMsg.class);
                    log.info("EquityRelationshipCreateListener sfa-equity-relationship-create msg:{}",msg);
                    equityRelationshipService.handleEquityRelationship(msg);
                    break;
                case "sfa-marketingEvent-influenceRecalculate":
                    EquityRelationshipDataModel.RelationshipCreateMsg msg1 = JSON.parseObject(body, EquityRelationshipDataModel.RelationshipCreateMsg.class);
                    log.info("EquityRelationshipCreateListener sfa-marketingEvent-influenceRecalculate msg:{}",msg1);
                    marketingAttributionService.handleMq(msg1.getTenantId(), msg1.getOpportunityIds(), msg1.getSalesOrderIds());
                    break;
                case "sfa-pool-replace-poolId":
                    ObjectPoolActionModels.ReplacePoolIdMsg msg2 = JSON.parseObject(body, ObjectPoolActionModels.ReplacePoolIdMsg.class);
                    log.info("EquityRelationshipCreateListener sfa-pool-replace-poolId msg:{}",msg2);
                    IObjectPoolService objectPoolService = objectPoolFactory.getObjectPoolService(msg2.getObjectApiName());
                    objectPoolService.replacePoolId(msg2);
                    break;
                case "sfa-pool-delete-all-poolData":
                    ObjectPoolActionModels.ReplacePoolIdMsg msg3 = JSON.parseObject(body, ObjectPoolActionModels.ReplacePoolIdMsg.class);
                    log.info("EquityRelationshipCreateListener sfa-pool-delete-all-poolData msg:{}",msg3);
                    IObjectPoolService objectPoolService1 = objectPoolFactory.getObjectPoolService(msg3.getObjectApiName());
                    objectPoolService1.deleteAllPoolData(msg3);
                    break;
            }

        } catch (Exception e) {
            log.error("EquityRelationshipCreateListener consumeMessage msgId:{},e:", messageExt.getMsgId(), e);
            throw new RuntimeException(e);
        }
    }
}