package com.facishare.crm.recycling.task.executor.service.impl;

import com.facishare.crm.recycling.task.executor.service.UserRoleChangedService;
import com.facishare.crm.sfa.lto.common.LtoOrgCommonService;
import com.facishare.crm.sfa.lto.exception.ExceptionUtil;
import com.facishare.crm.sfa.lto.objectlimit.ObjectLimitRuleService;
import com.facishare.crm.sfa.lto.objectlimit.models.ObjectLimitRuleModel;
import com.facishare.crm.sfa.lto.rest.models.UserRoleModel;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description 保有量规则计算
 */

@Slf4j
@Component
public class ObjectLimitUserRoleChangedService implements UserRoleChangedService {
    @Autowired
    private ObjectLimitRuleService objectLimitBiz;
    @Autowired
    private LtoOrgCommonService ltoOrgCommonService;

    @Override
    public void execute(UserRoleModel.UserRoleChangedMessage message) {
        try {
            String tenantId = message.getTenantId();
            if(message.getType() != UserRoleModel.UserRoleChangedType.DELETE_ROLE && message.getType() != UserRoleModel.UserRoleChangedType.ACCURATE_UPDATE_LINK){
                return;
            }
            List<String> userRoleIds = Lists.newArrayList();
            if(message.getType() == UserRoleModel.UserRoleChangedType.DELETE_ROLE) {
                userRoleIds.addAll(message.getRoles());
            } else {
                if(CollectionUtils.notEmpty(message.getDelLink())) {
                    userRoleIds.addAll(message.getDelLink().stream().map(UserRoleModel.UserRoleChangeMap::getRoleId).collect(Collectors.toList()));
                }

                if(CollectionUtils.notEmpty(message.getInsLink())) {
                    userRoleIds.addAll(message.getInsLink().stream().map(UserRoleModel.UserRoleChangeMap::getRoleId).collect(Collectors.toList()));
                }
            }
            if (CollectionUtils.empty(userRoleIds)) {
                return;
            }

            List<ObjectLimitRuleModel.ObjectLimitRule> objectLimitRuleList = objectLimitBiz.getObjectLimitRuleByDataIds(tenantId, userRoleIds, ObjectLimitRuleModel.DataTypeEnum.USER_ROLE.getCode());
            if (CollectionUtils.empty(objectLimitRuleList)) {
                return;
            }

            List<String> deletedRoleList = Lists.newArrayList();
            if(message.getType() == UserRoleModel.UserRoleChangedType.DELETE_ROLE){
                deletedRoleList.addAll(userRoleIds);
            }

            Set<String> objectApiNameSet = objectLimitRuleList.stream().map(ObjectLimitRuleModel.ObjectLimitRule::getObjectApiName)
                    .collect(Collectors.toSet());

            for(String objectApiName : objectApiNameSet) {
                Set<String> groupIdSet = objectLimitRuleList.stream()
                        .filter(x -> objectApiName.equals(x.getObjectApiName())).map(ObjectLimitRuleModel.ObjectLimitRule::getGroupId)
                        .collect(Collectors.toSet());
                if(CollectionUtils.empty(groupIdSet)){
                    continue;
                }

                for(String groupId : groupIdSet){
                    List<ObjectLimitRuleModel.ObjectLimitRule> tempLimitRuleList = objectLimitBiz.getObjectLimitRuleByGroupId(tenantId, objectApiName, groupId);
                    if(CollectionUtils.empty(tempLimitRuleList)){
                        continue;
                    }

                    if(CollectionUtils.notEmpty(deletedRoleList)){
                        objectLimitBiz.deleteObjectLimitRuleByDataIds(tenantId, objectApiName, groupId, deletedRoleList, ObjectLimitRuleModel.DataTypeEnum.USER_ROLE.getCode());
                        tempLimitRuleList.removeIf(x -> ObjectLimitRuleModel.DataTypeEnum.USER_ROLE.getCode().equals(x.getDataType()) && deletedRoleList.contains(x.getDataId()));
                    }

                    List<String> limitEmployeeList = objectLimitBiz.getEmployeeRuleByGroupId(tenantId, objectApiName, groupId);
                    List<String> limitRoleIds = tempLimitRuleList.stream()
                            .filter(x -> ObjectLimitRuleModel.DataTypeEnum.USER_ROLE.getCode().equals(x.getDataType()))
                            .map(ObjectLimitRuleModel.ObjectLimitRule::getDataId).collect(Collectors.toList());
                    if(CollectionUtils.notEmpty(limitRoleIds)) {
                        List<String> tempUserIds = ltoOrgCommonService.batchGetRoleUsersByRoleIds(tenantId, limitRoleIds, true);
                        List<String> deletedEmployeeList = limitEmployeeList.stream().filter(x -> !tempUserIds.contains(x)).collect(Collectors.toList());
                        if(CollectionUtils.notEmpty(deletedEmployeeList)) {
                            objectLimitBiz.deleteEmployeeRuleByEmployeeIds(tenantId, objectApiName, groupId, deletedEmployeeList);
                            limitEmployeeList.removeIf(deletedEmployeeList::contains);
                            log.warn(String.format("ObjectLimitUserRoleChangedService: tenantID-%s, groupId-%s, delete employee limit rule employeeIds: %s", tenantId, groupId, String.join(",", deletedEmployeeList)));
                        }
                    }

                    List<String> allLimitEmployeeList = objectLimitBiz.getObjectLimitRuleUserIds(tenantId, tempLimitRuleList);

                    List<String> deletedEmployeeList = limitEmployeeList.stream()
                            .filter(x -> !allLimitEmployeeList.contains(x)).collect(Collectors.toList());
                    if(CollectionUtils.notEmpty(deletedEmployeeList)){
                        objectLimitBiz.deleteEmployeeRuleByEmployeeIds(tenantId, objectApiName, groupId, deletedEmployeeList);
                        limitEmployeeList.removeIf(deletedEmployeeList::contains);
                        log.warn(String.format("ObjectLimitUserRoleChangedService: tenantID-%s, groupId-%s, delete employee limit rule employeeIds: %s", tenantId, groupId, String.join(",", deletedEmployeeList)));
                    }
                    List<String> insertEmployeeList = allLimitEmployeeList.stream()
                            .filter(x -> !limitEmployeeList.contains(x)).collect(Collectors.toList());

                    if(CollectionUtils.notEmpty(insertEmployeeList)){
                        String calculateId = tempLimitRuleList.get(0).getCalculateId();
                        objectLimitBiz.insertEmployeeRule(tenantId, objectApiName, groupId, calculateId, Lists.newArrayList(insertEmployeeList));
                    }
                }
            }
        } catch (Exception e) {
            ExceptionUtil.throwCommonBusinessException();
        }
    }
}
