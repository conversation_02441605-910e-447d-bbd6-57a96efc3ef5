package com.facishare.crm.recycling.task.executor.consumer;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.lto.utils.TenantUtil;
import org.apache.rocketmq.common.message.MessageExt;
import com.facishare.crm.recycling.task.executor.common.SfaRecyclingTaskRateLimiterService;
import com.facishare.crm.recycling.task.executor.service.impl.ObjectLimitUserRoleChangedService;
import com.facishare.crm.sfa.lto.rest.models.UserRoleModel;
import com.facishare.paas.appframework.common.mq.RocketMQMessageListener;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 执行器接收MQ入口
 */
@Slf4j
public class ObjectLimitRuleAdaptUserRoleChangedListener implements RocketMQMessageListener {
    @Autowired
    private ObjectLimitUserRoleChangedService objectLimitUserRoleChangedService;
    @Autowired
    private SfaRecyclingTaskRateLimiterService sfaRecyclingTaskRateLimiterService;
    @Autowired
    private TenantUtil tenantUtil;

    @Override
    public void consumeMessage(List<MessageExt> messages) {
        if (CollectionUtils.empty(messages)) {
            return;
        }

        for (MessageExt message : messages) {
            sfaRecyclingTaskRateLimiterService.getObjectLimitUserRuleLimiter().acquire();
            log.info("message:{}", message);
            consumeMessage(message);
        }
    }

    private void consumeMessage(MessageExt body) {
        try {
            UserRoleModel.UserRoleChangedMessage message = JSON.parseObject(body.getBody(),
                    UserRoleModel.UserRoleChangedMessage.class);
            log.info("msgId:{},UserRoleChangedMessage:{}", body.getMsgId(),message);
            if (tenantUtil.isExclusiveCloudEnterprise(message.getTenantId())) {
                log.info("{} is exclusive cloud ei", message.getTenantId());
                return;
            }
            objectLimitUserRoleChangedService.execute(message);
        } catch (Exception e) {
            log.error("ObjectLimitRuleCalculateListener consumeMessage {}",e);
            throw new RuntimeException(e);
        }
    }
}
