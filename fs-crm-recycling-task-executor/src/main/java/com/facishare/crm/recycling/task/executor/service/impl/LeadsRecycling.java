package com.facishare.crm.recycling.task.executor.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.recycling.task.executor.biz.*;
import com.facishare.crm.recycling.task.executor.common.RecyclingTaskGray;
import com.facishare.crm.recycling.task.executor.common.SfaRecyclingTaskRateLimiterService;
import com.facishare.crm.recycling.task.executor.enums.ApiNameEnum;
import com.facishare.crm.recycling.task.executor.enums.RemindRecordTypeEnum;
import com.facishare.crm.recycling.task.executor.model.RecyclingMessage;
import com.facishare.crm.recycling.task.executor.model.RecyclingReasonEnum;
import com.facishare.crm.recycling.task.executor.model.ReturnHighSeasActionContent;
import com.facishare.crm.recycling.task.executor.producer.AllocateBatchProducer;
import com.facishare.crm.recycling.task.executor.service.NomonTaskService;
import com.facishare.crm.recycling.task.executor.service.SFAOpenApiMqService;
import com.facishare.crm.recycling.task.executor.util.GrayUtils;
import com.facishare.crm.recycling.task.executor.util.SFAAuditLogRecyclingConvert;
import com.facishare.crm.recycling.task.executor.util.SearchUtil;
import com.facishare.crm.sfa.audit.log.SFAAuditLog;
import com.facishare.crm.sfa.lto.enums.LeadsBizStatusEnum;
import com.facishare.crm.sfa.lto.relationship.EnumUtil;
import com.facishare.crm.sfa.lto.relationship.models.RelationshipModels;
import com.facishare.crm.sfa.lto.relationship.service.RelationshiService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.Pair;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.LogServiceImpl;
import com.facishare.paas.appframework.log.dto.InternationalItem;
import com.facishare.paas.appframework.metadata.MetaDataServiceImpl;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.util.GetI18nKeyUtil;
import com.fxiaoke.paas.gnomon.api.NomonProducer;
import com.fxiaoke.paas.gnomon.api.entity.NomonMessage;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import static com.facishare.crm.recycling.task.executor.util.ConstantUtils.*;
import static com.facishare.crm.recycling.task.executor.util.I18NKey.*;

/**
 * @Description 线索回收器
 * <AUTHOR>
 * @Date 2019-02-18 22:03
 */
@Component
@Slf4j
public class LeadsRecycling extends AbstractRecycling {

    @Autowired
    private CustomerBiz customerBiz;

    @Autowired
    private RemindBiz remindBiz;

    @Autowired
    private LogServiceImpl logService;
    @Autowired
    private IObjectDescribeService objectDescribeService;
    @Autowired
    private HighSeasBiz highSeasBiz;
    @Autowired
    private NomonProducer nomonProducer;
    @Autowired
    private RecyclingTaskGray recyclingTaskGray;
    @Autowired
    private LeadsRecalculate leadsRecalculate;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private ObjectDataServiceImpl objectDataService;
    @Autowired
    private RecyclingBiz recyclingBiz;

    @Autowired
    private NomonTaskService nomonTaskService;

    @Autowired
    private AllocateBatchProducer allocateBatchProducer;

    @Autowired
    private SFAOpenApiMqService sfaOpenApiMqService;

    @Autowired
    private MetaDataServiceImpl metaDataService;
    @Autowired
    private RelationshiService relationshiService;

    @Autowired
    private SfaRecyclingTaskRateLimiterService sfaRecyclingTaskRateLimiterService;

    private static final String leadsDisplayNameKey = GetI18nKeyUtil.getDescribeDisplayNameKey("LeadsObj");
    private static final String leadsPoolDisplayNameKey = GetI18nKeyUtil.getDescribeDisplayNameKey("LeadsPoolObj");

    @Override
    @SFAAuditLog(bizName = "#bizName", entityClass = RecyclingMessage.class, convertClass = SFAAuditLogRecyclingConvert.class,
            status = "#status", messageId = "#messageId", cost1 = "#cost1")
    public void execute(RecyclingMessage message) {
        super.execute(message);
        IObjectData objectData = message.getObjectData();
        try {
            if (objectData == null) {
                log.warn("LeadsRecycling objectData is null:{}", message);
                return;
            }
            leadsRecycling(message);
        } catch (Exception e) {
            log.error("LeadsRecycling error {}", message, e);
            throw new RuntimeException(e);
        }
    }


    public void leadsRecycling(RecyclingMessage message) {
        String tenantId = message.getTenantId();
        String objectId = message.getObjectId();
        String targetLeadsPoolId = message.getTargetId();
        IObjectData objectData = message.getObjectData();

        IObjectData leadsPoolData = highSeasBiz.getLeadsPoolById(tenantId, targetLeadsPoolId);

        if (leadsPoolData == null) {
            customerBiz.clearExpireTime(objectData);
            log.warn("leadsPoolData is null :{},{},{}", tenantId, objectId, targetLeadsPoolId);
            return;
        }

        if (CollectionUtils.isEmpty(objectData.getOwner())) {
            if (StringUtils.isNotBlank(objectData.get(EXPIRE_TIME, String.class))) {
                customerBiz.clearExpireTime(objectData);
            }
            return;
        }

        if (objectData.get(BIZ_STATUS) != null && objectData.get(BIZ_STATUS, String.class).equals(LeadsBizStatusEnum.TRANSFORMED.getValue())) {
            if (StringUtils.isNotBlank(objectData.get(EXPIRE_TIME, String.class))) {
                customerBiz.clearExpireTime(objectData);
            }
            return;
        }

        if (StringUtils.isBlank(objectData.get(EXPIRE_TIME, String.class)) || (System.currentTimeMillis() - objectData.get(EXPIRE_TIME, Long.class)) < 0) {
            log.warn("expire_time isnull sendRecalculate objectId:{},tenantId:{}", objectId, tenantId);
            leadsRecalculate.sendRecalculate(tenantId, objectId, ApiNameEnum.LEADS_OBJ.getApiName(), new Date());
            return;
        }

        if (recyclingTaskGray.toSlowRecyclingTenantId(message.getTenantId())){
            sfaRecyclingTaskRateLimiterService.getCrmSlowRecyclingLimiter().acquire();
        }else {
            sfaRecyclingTaskRateLimiterService.getCrmRecyclingLimiter().acquire();
        }

        updateFields(message);
        IObjectDescribe objectDescribe = null;
        try {
            objectDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, ApiNameEnum.LEADS_OBJ.getApiName());

            String ownerName = customerBiz.getOwnerName(tenantId, objectData, objectDescribe);
            String content = "线索被收回到线索池：" + leadsPoolData.getName() + "，原负责人：" + ownerName;
            log.info("发送线索修改记录：{},objectId:{}", content, objectId);
            InternationalItem item = InternationalItem.builder()
                    .defaultInternationalValue(content)
                    .internationalKey("sfa.leads.recycling.msg") //{0}被收回到{1}：{2}，原负责人：{3}
                    .internationalParameters(Lists.newArrayList(leadsDisplayNameKey, leadsPoolDisplayNameKey, leadsPoolData.getName(), ownerName))
                    .build();
            logService.logWithInternationalCustomMessage(customerBiz.buildUser(tenantId), EventType.MODIFY, ActionType.AutoTakeBack, objectDescribe, objectData, content, item);
        } catch (MetadataServiceException e) {
            log.error("findByTenantIdAndDescribeApiName,MetadataServiceException", e);
            throw new RuntimeException(e);
        }
        if (leadsPoolData.get(IS_RECYCLING_TEAM_MEMBER) != null && (Boolean) leadsPoolData.get(IS_RECYCLING_TEAM_MEMBER)) {
            customerBiz.removeInnerRelevantTeam(tenantId, Lists.newArrayList(objectData));
        } else {
            customerBiz.removeObjectInnerOwner(tenantId, Lists.newArrayList(objectData));
        }
        customerBiz.objectOutTeamHandle(tenantId, leadsPoolData, Lists.newArrayList(objectData));


        addFlowRecord(tenantId, Lists.newArrayList(objectData));

        if(GrayUtils.isLeadsBatchAllocateMQ(tenantId)){
            allocateBatchProducer.sendAllocateTask(tenantId, objectId, targetLeadsPoolId);
        } else {
            sendAllocateTask(tenantId, objectId, targetLeadsPoolId);
        }
        nomonTaskService.deleteTask("leads_over_time", tenantId, objectId);
        createAllocateOverTimeTask(tenantId, objectId, leadsPoolData);
        recyclingBiz.addPoolClaimLog(tenantId, targetLeadsPoolId, Lists.newArrayList(objectData));
        //updatePoolCustomerCount(tenantId,targetLeadsPoolId);
        super.executeFunction(tenantId, LEADS_OBJ, message.getFunctionApiName(), objectData);
        super.cancelInstance(message);

        ReturnHighSeasActionContent returnHighSeasActionContent = ReturnHighSeasActionContent.builder().accountIds(Lists.newArrayList(objectData.getId()))
                .name(objectData.getName()).build();
        returnHighSeasActionContent.setObjectIds(Lists.newArrayList(objectId));
        if (objectData.get("company") != null && !"".equals(objectData.get("company"))) {
            returnHighSeasActionContent.setCompany(objectData.get("company").toString());
        }
        String littleTitle = getLittleTitle(message);
        log.info("sendRemind objectId:{},:{}", objectId, message);
        // 发送新crm提醒
        Pair<String, List<String>> keyAndParam = getKeyAndParam(message, objectData.getName());
        remindBiz.sendRemind(objectData, message.getTenantId(), "注意，您的 " + objectData.getName() + " 由于长时间未跟进/未转换，已经被收回到线索池",
                RemindRecordTypeEnum.LEADS_RECYCLING_NEW, "线索已被收回", SFA_LEADS_RECOVERD_NEW_RECORD, Lists.newArrayList(),
                keyAndParam.getKey(), keyAndParam.getValue());
        returnHighSeasActionContent.setBackReasonDescription(littleTitle);
        log.info("发送线索池mq,objectId:{},leadsPoolId:{},tenantId:{}", objectId, targetLeadsPoolId, tenantId);
        sfaOpenApiMqService.sendOpenApiMq(customerBiz.buildUser(tenantId), ObjectAction.TAKE_BACK.getActionCode(),
                ApiNameEnum.LEADS_POOL_OBJ.getApiName(), targetLeadsPoolId, returnHighSeasActionContent);

        metaDataService.sendActionMq(customerBiz.buildUser(tenantId), Lists.newArrayList(objectData), ObjectAction.TAKE_BACK);
        dealContactMemberRelationship(tenantId,objectId);
    }

    public void createAllocateOverTimeTask(String tenantId, String dataId, IObjectData leadsPool) {
        Integer overTimeHours = 0;
        Integer overTimeMinutes = 0;
        Object objValue = leadsPool.get("allocate_overtime_hours");
        if (objValue != null) {
            overTimeHours = Integer.parseInt(objValue.toString());
        }
        objValue = leadsPool.get("allocate_overtime_minutes");
        if (objValue != null) {
            overTimeMinutes = Integer.parseInt(objValue.toString());
        }
        if (overTimeHours <= 0 && overTimeMinutes <= 0) {
            return;
        }

        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.add(Calendar.HOUR, overTimeHours);
        cal.add(Calendar.MINUTE, overTimeMinutes);
        Date executeTime = cal.getTime();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("tenantId", tenantId);
        jsonObject.put("objectId", dataId);
        nomonTaskService.createOrUpdateTask("leads_allocate_over_time", tenantId, dataId, executeTime, jsonObject.toJSONString(), 0);
    }

    private void addFlowRecord(String tenantId, List<IObjectData> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        String objectApiName = "LeadsFlowRecordObj";
        List<IObjectData> oldFlowRecordDataList = Lists.newArrayList();
        for (IObjectData leadsData : dataList) {
            String oldOwnerId = getOwner(leadsData);
            if (StringUtils.isBlank(oldOwnerId)) {
                continue;
            }
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            searchTemplateQuery.setLimit(1);
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, "leads_owner", oldOwnerId);
            SearchUtil.fillFilterEq(filters, "leads_id", leadsData.getId());
            searchTemplateQuery.setFilters(filters);
            List<OrderBy> orderByList = Lists.newArrayList();
            orderByList.add(new OrderBy("last_modified_time", false));
            searchTemplateQuery.setOrders(orderByList);
            searchTemplateQuery.setNeedReturnCountNum(false);
            QueryResult<IObjectData> queryResult = commonBiz.queryBySearchTemplate(tenantId, objectApiName, searchTemplateQuery);
            if (queryResult != null && org.apache.commons.collections.CollectionUtils.isNotEmpty(queryResult.getData())) {
                IObjectData oldFlowRecordData = queryResult.getData().get(0);
                oldFlowRecordData.set("leads_status", "returned");
                oldFlowRecordData.set("leads_status_changed_time", System.currentTimeMillis());
                oldFlowRecordData.set("leads_back_reason", "满足规则，自动收回");
                oldFlowRecordData.set("last_modified_by", Lists.newArrayList(User.SUPPER_ADMIN_USER_ID));
                oldFlowRecordData.set("last_modified_time", System.currentTimeMillis());
                oldFlowRecordDataList.add(oldFlowRecordData);
            }
        }
        if (CollectionUtils.isNotEmpty(oldFlowRecordDataList)) {
            List<String> updateFieldList = Lists.newArrayList("leads_status", "leads_status_changed_time", "last_modified_by",
                    "last_modified_time");
            try {
//            serviceFacade.batchUpdateByFields(user, objectDataList, updateFieldList);
                User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
                objectDataService.batchUpdateWithField(oldFlowRecordDataList, updateFieldList, getDefaultActionContext(user, objectApiName));
            } catch (MetadataServiceException metadataError) {
                log.info("addFlowRecord warn", metadataError);
                throw new RuntimeException(metadataError.getMessage(), metadataError);
            } catch (Exception e) {
                log.error("addFlowRecord error", e);
                throw new RuntimeException(e);
            }
        }
    }

    private String getOwner(IObjectData objectData) {
        List<String> ownerList = objectData.getOwner();
        String owner = "";
        if (com.facishare.paas.appframework.common.util.CollectionUtils.notEmpty(ownerList)) {
            owner = ownerList.get(0);
        }
        return owner;
    }

    private ActionContext getDefaultActionContext(User user, String apiName) {
        IObjectDescribe objectDescribe = null;
        try {
            objectDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(user.getTenantId(), apiName);
        } catch (Exception e) {

        }
        ActionContext actionContext = new ActionContext();
        actionContext.setEnterpriseId(user.getTenantId());
        actionContext.setUserId(user.getUserId());
        actionContext.setDbType("pg");
        actionContext.setAllowUpdateInvalid(true);
        actionContext.put("not_validate", true);
        actionContext.setPrivilegeCheck(false);
        actionContext.setObjectDescribe(objectDescribe);
        return actionContext;
    }

    /**
     * 更新字段信息
     * <p>
     * 第一：负责人清空，
     * 第二：状态设置未分配，
     * 第三：过期时间清空 ,
     * 第四：转移线索池
     * 领取时间清空，
     * 退回时间变为当前时间
     */
    private void updateFields(RecyclingMessage message) {
        List<String> updateFieldList = Lists.newArrayList();
        IObjectData data = new ObjectData();

        data.setTenantId(message.getTenantId());
        data.setDescribeApiName(message.getObjectApiName());
        data.setId(message.getObjectId());

        data.setOwner(null);
        updateFieldList.add(OWNER);

        data.setDataOwnDepartment(null);
        updateFieldList.add(DATA_OWN_DEPARTMENT);

        data.set(BIZ_STATUS, LeadsBizStatusEnum.UN_ASSIGNED.getValue());
        updateFieldList.add(BIZ_STATUS);

        //到期时间清空
        data.set(EXPIRE_TIME, "");
        updateFieldList.add(EXPIRE_TIME);

        data.set(LEADS_POOL_ID, message.getTargetId());
        updateFieldList.add(LEADS_POOL_ID);

        //领取/分配时间清空
        data.set(ASSIGNED_TIME, "");
        updateFieldList.add(ASSIGNED_TIME);

        data.set(REMIND_DAYS, null);
        updateFieldList.add(REMIND_DAYS);

        Long currentTime = System.currentTimeMillis();
        // 负责人变更时间 设置为系统时间
        data.set(OWNER_CHANGE_TIME, currentTime);
        updateFieldList.add(OWNER_CHANGE_TIME);

        data.set(RETURNED_TIME, currentTime);
        updateFieldList.add(RETURNED_TIME);

        log.info("batchUpdateWithField :{},{}", data, updateFieldList);
        customerBiz.updateFieldForRecycling(Lists.newArrayList(data), updateFieldList, message.getObjectApiName(), message.getTenantId());

    }

    private void sendAllocateTask(String tenantId, String leadsId, String poolId) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("tenantId", tenantId);
        jsonObject.put("objectId", leadsId);
        jsonObject.put("leadsPoolId", poolId);
        jsonObject.put("allocateOperationSource", 2);
        jsonObject.put("batch", 0);

        Integer callQueueMod = Math.abs(tenantId.concat(LEADS_ALLOCATE_BIZ).concat(SINGLE).concat(poolId).hashCode());
        NomonMessage message = NomonMessage
                .builder()
                .biz(LEADS_ALLOCATE_BIZ)
                .tenantId(tenantId)
                .dataId(leadsId)
                .executeTime(new Date())
                .callArg(String.format(jsonObject.toJSONString(), tenantId, leadsId))
                .callQueueMod(callQueueMod)
                .build();
        log.info("send leads_allocate task:{}", message.toString());
        nomonProducer.send(message);
    }

    public void updatePoolCustomerCount(String tenantId, String poolId) {
        Integer poolCustomerCount = getPoolCustomerCount(tenantId, poolId, LEADS_OBJ);

        if (poolCustomerCount == 0) {
            return;
        }
        List<IObjectData> objectDataList = Lists.newArrayList();
        IObjectData objectData = new ObjectData();
        objectData.setDescribeApiName(Utils.LEADS_POOL_API_NAME);
        objectData.setTenantId(tenantId);
        objectData.setId(poolId);
        objectData.set("leads_count", poolCustomerCount);
        objectDataList.add(objectData);
        if (com.facishare.paas.appframework.common.util.CollectionUtils.notEmpty(objectDataList)) {
            List<String> updateField = Arrays.asList("leads_count");
            serviceFacade.batchUpdateByFields(customerBiz.buildUser(tenantId), objectDataList, updateField);
        }
    }


    private String getLittleTitle(RecyclingMessage message) {
        String recyclingCondition = "";
        Integer recyclingDays = message.getRecyclingDays();
        Integer messageType = message.getRecyclingReasonType();

        if (recyclingDays == null || recyclingDays == 0) {
            return recyclingCondition;
        }
        int days = recyclingDays / 24;
        int hours = recyclingDays % 24;

        if (days > 0 && hours > 0) {
            messageType = messageType + 2;
            //message.setRecyclingReasonType(message.getRecyclingReasonType() + 2);
            return I18N.text(RecyclingReasonEnum.valueOf(messageType), days, hours);
        } else if (days == 0 && hours > 0) {
            messageType = messageType + 1;
        } else {
            //{0}天未跟进、{0}天未转换
            recyclingDays = days;
        }
        String s = RecyclingReasonEnum.valueOf(messageType);
        if (StringUtils.isNotBlank(s)) {
            recyclingCondition = I18N.text(s, recyclingDays);
        }
        return recyclingCondition;
    }

    /**
     * 处理联系人与成员关系图谱
     * @param tenantId
     * @param dataId
     */
    private void dealContactMemberRelationship(String tenantId,String dataId){
        if(!GrayUtils.isSendContactMemberRelationshipMQ(tenantId)){
            return;
        }
        //判断是否开启联系人与成员关系功能
        if(!relationshiService.getConfigValueByKey(tenantId)){
            return;
        }
        RelationshipModels.TaskArg arg = new RelationshipModels.TaskArg();
        arg.setTenantId(tenantId);
        arg.setDataIds(Lists.newArrayList(dataId));
        arg.setObjectApiName("LeadsObj");
        arg.setOperationType(EnumUtil.OperationType.AutoTakeBack.getValue());
        try {
            ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
            task.submit(() -> {
                relationshiService.dealDataRelationshipByDataId(arg);
            });
            task.run();
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    private String getFullContentInternationalKey(RecyclingMessage message) {
    if (message == null || message.getRecyclingReasonType() == null) {
            return SFA_LEADS_RECOVERD_NEW_RECORD;
        }
        switch (message.getRecyclingReasonType()) {
            case 1:
                return SFA_LEADS_RECYClING_REASON_FOLLOW;
            case 2:
                return SFA_LEADS_RECYClING_REASON_FOLLOW_HOURS;
            case 3:
                return SFA_LEADS_RECYClING_REASON_FOLLOW_ALL;
            case 5:
                return SFA_LEADS_RECYClING_REASON_TRANS;
            case 6:
                return SFA_LEADS_RECYClING_REASON_TRANS_HOURS;
            case 7:
                return SFA_LEADS_RECYClING_REASON_TRANS_ALL;
            default:
                return SFA_LEADS_RECYClING_REASON;
        }
    }

    private Pair<String, List<String>> getKeyAndParam(RecyclingMessage message, String objectDataName) {
        String i18nKey = getFullContentInternationalKey(message);
        Pair<String, List<String>> pair = new Pair<>();
        pair.setKey(i18nKey);
        List<String> value = Lists.newArrayList();
        value.add(objectDataName);
        pair.setValue(value);
        Integer recyclingDays = message.getRecyclingDays();
        if (recyclingDays == null || recyclingDays == 0) {
            return pair;
        }
        int days = recyclingDays / 24;
        int hours = recyclingDays % 24;

        if (days > 0 && hours > 0) {
            message.setRecyclingReasonType(message.getRecyclingReasonType() + 2);
            i18nKey = getFullContentInternationalKey(message);
            pair.setKey(i18nKey);
            value.add(String.valueOf(days));
            value.add(String.valueOf(hours));
            return pair;
        } else if (days == 0 && hours > 0) {
            message.setRecyclingReasonType(message.getRecyclingReasonType() + 1);
        } else {
            //{0}天未跟进、{0}天未转换
            recyclingDays = days;
        }
        i18nKey = getFullContentInternationalKey(message);
        pair.setKey(i18nKey);
        value.add(String.valueOf(recyclingDays));
        return pair;
    }

}
