package com.facishare.crm.recycling.task.executor.biz;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.recycling.task.executor.common.DataConvert;
import com.facishare.crm.recycling.task.executor.common.RecyclingTaskGray;
import com.facishare.crm.recycling.task.executor.model.*;
import com.facishare.crm.recycling.task.executor.util.*;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.CommonSqlOperator;
import com.facishare.paas.metadata.impl.search.WhereParam;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.InvocationTargetException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

import static com.facishare.crm.recycling.task.executor.util.ConstantUtils.*;
import static com.facishare.crm.recycling.task.executor.util.TableNameConstants.BIZ_DATA_CLAIM_LOG;

/**
 * @Description 从PG查询规则
 * <AUTHOR>
 * @Date 2019-02-25 21:18
 */

@Component
@Slf4j
public class RecyclingBiz {


    @Autowired
    private IObjectDataService objectDataPgService;

    @Autowired
    private CustomerBiz customerBiz;

    @Autowired
    private DataConvert dataConvert;

    @Autowired
    private RecyclingTaskGray recyclingTaskGray;

    @Autowired
    private ServiceFacade serviceFacade;

    /**
     * @param tenantId
     * @param objectId 公海,部门 id
     * @param dataType 1 公海规则，2 非公海规则
     * @return
     */

    public List<Map> getRecyclingRule(String tenantId, String objectId, String dataType, String apiName) {
        List<Map> recyclingRule = new ArrayList<>();

        String sql = "select * from recycling_rule"
                + String.format(" where ei = '%s'", tenantId)
                + String.format(" and data_type = '%s'", dataType)
                + String.format(" and api_name = '%s' ", apiName);

        if (StringUtils.isNotBlank(objectId)) {
            sql += String.format(" and data_id = '%s'", objectId);
        }

        try {
            recyclingRule = objectDataPgService.findBySql(tenantId, sql);
        } catch (MetadataServiceException e) {
            log.error("getRecyclingRule :{}", e.getMessage());
        }

        return recyclingRule;
    }


    /**
     * @param tenantId
     * @param recyclingRuleIds recycling_rule_id
     * @return
     */

    public List<Map> getRecyclingFilter(String tenantId, List<String> recyclingRuleIds) {
        List<Map> recyclingFilters = new ArrayList<>();

        String sql = "select * from "
                + "recycling_filter"
                + String.format(" where ei = '%s'", tenantId)
                + String.format(" and recycling_rule_id in ('%s')", String.join("','", recyclingRuleIds));
        try {
            recyclingFilters = objectDataPgService.findBySql(tenantId, sql);
        } catch (MetadataServiceException e) {
            log.error("getRecyclingFilter :{}", e.getMessage());
        }

        return recyclingFilters;
    }

    /**
     * @param tenantId
     * @param recyclingRuleIds recycling_rule_id
     * @return
     */

    public List<Map> getRecyclingReminds(String tenantId, List<String> recyclingRuleIds) {
        List<Map> recyclingFilters = new ArrayList<>();

        String sql = "select * from "
                + "recycling_remind_rule"
                + String.format(" where ei = '%s'", tenantId)
                + String.format(" and recycling_rule_id in ('%s')", String.join("','", recyclingRuleIds));
        try {
            recyclingFilters = objectDataPgService.findBySql(tenantId, sql);
        } catch (MetadataServiceException e) {
            log.error("getRecyclingFilter :{}", e.getMessage());
        }

        return recyclingFilters;
    }

    /**
     * @param tenantId
     * @param dataId
     * @return
     */
    public List<IObjectData> getRemindRule(String tenantId, String dataId, String dataType) {
        List<Map> remindRules = new ArrayList<>();
        String  sql =  "select * from biz_remind_rule "
                    + String.format(" where tenant_id = '%s' and is_deleted = 0 ", tenantId)
                    + String.format(" and data_type = %s", dataType)
                    + String.format(" and data_id = '%s' ",dataId);
        try {
            remindRules = objectDataPgService.findBySql(tenantId, sql);
        } catch (MetadataServiceException e) {
            log.error("getRemindRule :{}", e.getMessage());
        }
        return ConvertUtils.convertMapToObjectData(remindRules);
    }


    public List<RecyclingRule> getListRecyclingRule(String tenantId, String objectId, String dataType, String apiName) {
        List<Map> recyclingRuleMaps = getRecyclingRule(tenantId, objectId, dataType, apiName);

        List<RecyclingRule> recyclingRules = new ArrayList<>();
        List<String> recyclingRuleIds = new ArrayList<>();
        for (Map recyclingRuleMap : recyclingRuleMaps) {
            RecyclingRule recyclingRule = new RecyclingRule();
            convertRecyclingRule(recyclingRuleMap, recyclingRule);
            recyclingRules.add(recyclingRule);
            recyclingRuleIds.add(recyclingRule.getRecyclingRuleID());
        }

        List<RecyclingFilter> filters = new ArrayList<>();
        List<RecyclingRemindRule> remindRules = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(recyclingRuleIds)) {
            List<Map> recyclingFilters = getRecyclingFilter(tenantId, recyclingRuleIds);
            for (Map map : recyclingFilters) {
                RecyclingFilter filter = new RecyclingFilter();
                convertRecyclingFilter(map, filter);
                filters.add(filter);
            }

            List<Map> recyclingReminds = getRecyclingReminds(tenantId, recyclingRuleIds);
            for (Map map : recyclingReminds) {
                RecyclingRemindRule recyclingRemindRule = new RecyclingRemindRule();
                convertRecyclingRemind(map, recyclingRemindRule);
                remindRules.add(recyclingRemindRule);
            }

            for (RecyclingRule recyclingRule : recyclingRules) {
                List<RecyclingRemindRule> ruleRemindRules = new ArrayList<>();
                for (RecyclingRemindRule remindRule : remindRules) {
                    if (recyclingRule.getRecyclingRuleID().equals(remindRule.getRecyclingRuleID())) {
                        ruleRemindRules.add(remindRule);
                    }
                }
                recyclingRule.setRecyclingRemindRuleList(ruleRemindRules);
                List<RecyclingFilter> rulefilters = new ArrayList<>();
                for (RecyclingFilter recyclingFilter : filters) {
                    if (recyclingRule.getRecyclingRuleID().equals(recyclingFilter.getRecyclingRuleID())) {
                        rulefilters.add(recyclingFilter);
                    }
                }
                recyclingRule.setRecyclingFilterList(rulefilters);
            }
        }
        return recyclingRules;


//        for (Map recyclingRuleMap : recyclingRuleMaps) {
//            RecyclingRule recyclingRule = new RecyclingRule();
//            recyclingRule = ConvertUtils.mapToBean(recyclingRuleMap, recyclingRule);
//            if (recyclingRule == null){
//                continue;
//            }
//            recyclingRules.add(recyclingRule);
//        }
//
//
//        List<String> collect = recyclingRules.stream().map(RecyclingRule::getRecyclingRuleID).collect(Collectors.toList());
//        return recyclingRules;
    }

    private void convertRecyclingFilter(Map map, RecyclingFilter filter) {
        filter.setRecyclingFilterID(map.get("recycling_filter_id").toString());
        filter.setRecyclingRuleID(map.get("recycling_rule_id").toString());
        filter.setDataID(map.get("data_id").toString());
        filter.setFieldName(map.get("field_name").toString());
        filter.setFieldType((int) map.get("field_type"));
        filter.setCompare((int) map.get("compare"));
        filter.setFieldValue(map.get("field_value").toString());
        filter.setFieldOrder((int) map.get("field_order"));
    }

    private void convertRecyclingRemind(Map map, RecyclingRemindRule remindRule) {
        remindRule.setRecyclingRemindRuleID(map.get("recycling_remind_rule_id").toString());
        remindRule.setRecyclingRuleID(map.get("recycling_rule_id").toString());
        remindRule.setDataID(map.get("data_id").toString());
        remindRule.setRuleType((int) map.get("rule_type"));
        remindRule.setRemindDays((int) map.get("remind_days"));
        remindRule.setGroupID(map.get("group_id").toString());
    }


    public void convertRecyclingRule(Map map, RecyclingRule recyclingRule) {
        recyclingRule.setEi(map.get("ei").toString());
        recyclingRule.setRecyclingRuleID(map.get("recycling_rule_id").toString());
        recyclingRule.setPriority((int) map.get("priority"));
        recyclingRule.setDataID(map.get("data_id").toString());
        recyclingRule.setDataType((int) map.get("data_type"));
        recyclingRule.setRecyclingRuleType((int) map.get("recycling_rule_type"));
        recyclingRule.setDealDays((int) map.get("deal_days"));
        recyclingRule.setFollowUpDays((int) map.get("follow_up_days"));
        recyclingRule.setHighSeasID(map.get("high_seas_id").toString());
        recyclingRule.setGroupID(map.get("group_id").toString());
        recyclingRule.setCreatorID(map.get("creator_id").toString());
        recyclingRule.setUpdateTime(((Timestamp) map.get("update_time")).getTime());
        recyclingRule.setIsIncludePastTime((Boolean) map.get("is_include_past_time"));
    }


    /**
     * @param clazz 要传成的JavaBean对象(数据Map中的key包含下划线,转成JavaBean的驼峰字段属性)
     * @param map   要转的Map(包含Key,Value)
     * @return 将一个Map转换成一个JavaBean(Map中的Key包含下划线, 下划线转成驼峰)
     */
    public static <T> T mapCamelToBean(Class<T> clazz, Map map) {
        T result = null;
        try {
            BeanInfo beanInfo = Introspector.getBeanInfo(clazz);
            result = clazz.newInstance();
            PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
            for (PropertyDescriptor descriptor : propertyDescriptors) {
                String propertyName = descriptor.getName();
                String camelToUnderlinePropertyName = camelToUnderline(propertyName);
                if (map.containsKey(propertyName) || map.containsKey(camelToUnderlinePropertyName)) {
                    Object value = map.get(propertyName);
                    if (value == null) value = map.get(camelToUnderlinePropertyName);
                    if ("".equals(value)) value = null;
                    Object[] args = new Object[1];
                    args[0] = value;
                    descriptor.getWriteMethod().invoke(result, args);
                }
            }
        } catch (IllegalAccessException | InstantiationException | IntrospectionException | IllegalArgumentException | InvocationTargetException e) {

        }
        return result;
    }

    /**
     * @return 将驼峰字符串转换成下划线 如: userId 转成 user_id
     */
    public static String camelToUnderline(String origin) {
        return stringProcess(origin, (prev, c) -> {
                    if (Character.isUpperCase(c)) {
                        return "" + "_" + Character.toLowerCase(c);
                    }
                    return "" + c;
                }
        );
    }

    /**
     * @return 将下划线转成驼峰字符串 如: user_id转成userId
     */
    public static String underlineToCamel(String origin) {
        return stringProcess(origin, (prev, c) -> {
                    if (prev == '_' && Character.isLowerCase(c)) return "" + Character.toUpperCase(c);
                    if (c == '_') return "";
                    return "" + c;
                }
        );
    }

    public static String stringProcess(String origin, BiFunction<Character, Character, String> convertFunc) {
        if (origin == null || "".equals(origin.trim())) return "";
        String newOrigin = "0" + origin;
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < newOrigin.length() - 1; i++) {
            char prev = newOrigin.charAt(i);
            char c = newOrigin.charAt(i + 1);
            sb.append(convertFunc.apply(prev, c));
        }
        return sb.toString();
    }

    /**
     *
     * @param tenantId
     * @param dataId
     * @param apiName HighSeasObj,LeadsPoolObj
     * @return
     */
    public List<RecyclingRuleInfoModel> getRecyclingRule(String tenantId, String dataId, String apiName) {
        if (StringUtils.isBlank(dataId)) {
            return null;
        }
        List<RecyclingRuleInfoModel> rst = Lists.newArrayList();
        try {
            List<WhereParam> recyclingRuleWheres = CommonSqlUtils.getCommonWhereParams(tenantId, apiName);
            CommonSqlUtils.addWhereParam(recyclingRuleWheres, RecyclingRuleConstans.Field.Data_Id, CommonSqlOperator.EQ, Lists.newArrayList(Lists.newArrayList(dataId)));
            CommonSqlUtils.addWhereParam(recyclingRuleWheres, RecyclingRuleConstans.Field.Object_Api_Name, CommonSqlOperator.EQ, Lists.newArrayList(apiName));
            List<Map> queryRst = CommonSqlUtils.queryData(tenantId, TableNameConstants.RECYCLING_RULE, recyclingRuleWheres);
            if (CollectionUtils.isNotEmpty(queryRst)) {
                List<RecyclingRemindRuleModel> remindRules = getRecyclingRemindRuleListByDataId(tenantId, dataId, apiName);
                ConvertUtils.convert2RecyclingRule(rst, queryRst);
                rst.forEach(item -> item.setRecyclingRemindRuleList(remindRules.stream().filter(x -> x.getRecyclingRuleId().equals(item.getId()))
                        .collect(Collectors.toList())));
            }
        } catch (MetadataServiceException e) {
            log.error("getRecyclingRule findById error", e);
            throw new RuntimeException(e);
        }
        return rst;
    }

    public List<RecyclingRuleInfoModel> getRecyclingRules(String tenantId, List<String> dataIds, String apiName) {
        if (CollectionUtils.isEmpty(dataIds)) {
            return null;
        }
        List<RecyclingRuleInfoModel> rst = Lists.newArrayList();
        try {
            List<WhereParam> recyclingRuleWheres = CommonSqlUtils.getCommonWhereParams(tenantId, apiName);
            CommonSqlUtils.addWhereParam(recyclingRuleWheres, RecyclingRuleConstans.Field.Data_Id, CommonSqlOperator.IN, Lists.newArrayList(dataIds));
            CommonSqlUtils.addWhereParam(recyclingRuleWheres, RecyclingRuleConstans.Field.Object_Api_Name, CommonSqlOperator.EQ, Lists.newArrayList(apiName));
            CommonSqlUtils.addWhereParam(recyclingRuleWheres, RecyclingRuleConstans.Field.Data_Type, CommonSqlOperator.EQ, Lists.newArrayList(2));
            List<Map> queryRst = CommonSqlUtils.queryData(tenantId, TableNameConstants.RECYCLING_RULE, recyclingRuleWheres);
            if (CollectionUtils.isNotEmpty(queryRst)) {
                ConvertUtils.convert2RecyclingRule(rst, queryRst);
            }
        } catch (MetadataServiceException e) {
            log.error("getRecyclingRules findById error e:", e);
            throw new RuntimeException(e);
        }
        return rst;
    }

    public boolean getRecyclingRuleExistes(String tenantId) {
        if (StringUtils.isBlank(tenantId)) {
            return false;
        }
        List<Map> recyclingRule = new ArrayList<>();

        String sql =  "select id from biz_recycling_rule "
                + String.format(" where tenant_id = '%s' and is_deleted = 0 limit 1 ", tenantId);

        try {
            recyclingRule = objectDataPgService.findBySql(tenantId, sql);
        } catch (MetadataServiceException e) {
            log.error("getRecyclingRule :{}", e.getMessage());
        }
        if (CollectionUtils.isNotEmpty(recyclingRule)){
            return true;
        }
        return false;
    }

    /**
     *
     * @param objectData
     * @param apiName HighSeasObj,LeadsPoolObj
     * @return
     */
    public List<RecyclingRuleInfoModel> getRecyclingRule(IObjectData objectData, String apiName) {
        String dataId = getDataId(objectData);
        log.info("recyclingBiz getRecyclingRule {},dataId:{},apiName {}",objectData.getId(),dataId,apiName);
        List<RecyclingRuleInfoModel> recyclingRuleInfoModels = getRecyclingRule(objectData.getTenantId(), dataId, apiName);
        return recyclingRuleInfoModels;
    }


    public List<RecyclingRemindRuleModel> getRecyclingRemindRuleListByDataId(String tenantId, String dataId, String apiName) {
        List<WhereParam> wheres = CommonSqlUtils.getCommonWhereParams(tenantId, apiName);
        CommonSqlUtils.addWhereParam(wheres, RecyclingRemindRuleConstants.Field.Data_Id, CommonSqlOperator.EQ, Lists.newArrayList(dataId));
        List<RecyclingRemindRuleModel> rst = Lists.newArrayList();
        try {
            List<Map> queryRst = CommonSqlUtils.queryData(tenantId, HighSeasConstants.TabelName.BIZ_RECYCLING_REMIND_RULE, wheres);
            if (com.facishare.paas.appframework.common.util.CollectionUtils.notEmpty(queryRst)) {
                ConvertUtils.convert2RecyclingRemindRule(rst, queryRst);
            }
        } catch (MetadataServiceException e) {
            log.error("",e);
        }
        return rst;
    }

    public List<RemindRuleModel> getRemindRulesByDataId(String tenantId, String dataId) {
        List<RemindRuleModel> remindRuleModels = Lists.newArrayList();
        List<WhereParam> wheres = Lists.newArrayList();
        CommonSqlUtils.addWhereParam(wheres, RecyclingRuleConstans.Field.Tenant_Id, CommonSqlOperator.EQ, Lists.newArrayList(tenantId));
        CommonSqlUtils.addWhereParam(wheres, RemindRuleConstants.Field.Data_Id, CommonSqlOperator.EQ, Lists.newArrayList(dataId));
        CommonSqlUtils.addWhereParam(wheres, RemindRuleConstants.Field.Is_Deleted, CommonSqlOperator.EQ, Lists.newArrayList(0));
        try {
            List<Map> maps = CommonSqlUtils.queryData(tenantId, HighSeasConstants.TabelName.BIZ_REMIND_RULE, wheres);
            maps.forEach(m -> {
                RemindRuleModel item = new RemindRuleModel();
                item.setDataId(m.get(RemindRuleConstants.Field.Data_Id).toString());
                item.setDataType((Integer) m.get(RemindRuleConstants.Field.Data_Type));
                item.setDealDays((Integer) m.get(RemindRuleConstants.Field.Deal_Days));
                item.setFlowUpDays((Integer) m.get(RemindRuleConstants.Field.Follow_Up_Days));
                item.setId(m.get(RemindRuleConstants.Field.Id).toString());
                item.setRuleType((Integer) m.get(RemindRuleConstants.Field.Rule_Type));
                item.setGroupId(m.get(RemindRuleConstants.Field.Group_Id).toString());
                item.setSkipHolidays(m.get(RemindRuleConstants.Field.SKIP_HOLIDAYS) != null && (Boolean) m.get(RemindRuleConstants.Field.SKIP_HOLIDAYS));
                remindRuleModels.add(item);
            });
        } catch (MetadataServiceException e) {
            log.error(e.getMessage());
        }
        return remindRuleModels;
    }

    public List<RemindRuleModel> getRemindRules(IObjectData objectData) {
        String dataId = getDataId(objectData);
        return getRemindRulesByDataId(objectData.getTenantId(), dataId);
    }

    /**
     * 公海客户返回公海id，非公海返回部门id
     *
     * @param objectData
     * @return
     */
    public String getDataId(IObjectData objectData) {
        String dataId = "";
        if (Utils.ACCOUNT_API_NAME.equals(objectData.getDescribeApiName())) {
            if (objectData.get(HIGH_SEAS_ID) == null || StringUtils.isBlank(objectData.get(HIGH_SEAS_ID).toString())) {
                if(objectData.get(OWNER) == null || "".equals(objectData.get(OWNER))){
                    log.info("owner is null:{},{}",objectData.getId(),objectData.getTenantId());
                    return null;
                }
                String ownerId = StringUtils.strip(objectData.get(OWNER).toString(), "[]");
                dataId = customerBiz.getDept(objectData.getTenantId(), ownerId);
                if (StringUtils.isBlank(dataId)) {
                    return null;
                }
            } else {
                dataId = objectData.get(HIGH_SEAS_ID, String.class);
            }
        } else if (Utils.LEADS_API_NAME.equals(objectData.getDescribeApiName())) {
            dataId = objectData.get(LEADS_POOL_ID, String.class);
        }
        log.info("recyclingrule getDataId :{},{},{}",objectData.getId(),objectData.getTenantId(),dataId);
        return dataId;
    }

    public void addPoolClaimLog(String tenantId,String targetId,List<IObjectData> objectDataList){
        List<Map<String,Object>> lists = new ArrayList();
        List<Map<String, Object>> deleteMap = Lists.newArrayList();
        for (IObjectData iObjectData : objectDataList) {
            Map<String, Object> insertData = Maps.newHashMap();
            String id = serviceFacade.generateId();
            insertData.put("id", id);
            insertData.put("name", id);
            insertData.put("tenant_id", tenantId);
            insertData.put("pool_id", targetId);
            insertData.put("object_id", iObjectData.getId());
            insertData.put("employee_id", ObjectDataUtils.getOwner(iObjectData));
            insertData.put("operation_time", System.currentTimeMillis());
            insertData.put("api_name", iObjectData.getDescribeApiName());
            insertData.put("created_by", ADMIN);
            insertData.put("create_time", System.currentTimeMillis());
            insertData.put("last_modified_by", ADMIN);
            insertData.put("action_code", TAKE_BACK);
            insertData.put("last_modified_time", System.currentTimeMillis());
            insertData.put("is_deleted", 0);
            lists.add(insertData);

            Map<String, Object> deleteData = Maps.newHashMap();
            deleteData.put("tenant_id", tenantId);
            deleteData.put("api_name", iObjectData.getDescribeApiName());
            deleteData.put("pool_id", targetId);
            deleteData.put("object_id", iObjectData.getId());
            deleteData.put("employee_id", ObjectDataUtils.getOwner(iObjectData));
            deleteMap.add(deleteData);

        }
        try {
            CommonSqlUtils.batchDeleteDataBySql(tenantId, "biz_data_claim_log", deleteMap, Lists.newArrayList("id", "tenant_id"));
            CommonSqlUtils.insertDataBySql(tenantId, BIZ_DATA_CLAIM_LOG, lists);
        } catch (Exception e) {
            log.error("addPoolClaimLog:{}",objectDataList,e);
        }
    }


    public  List<RecyclingRuleInfoModel> getCacheRecyclingRule(String tenantId, Map<String,List<RecyclingRuleInfoModel>> ruleCacheMap,
                                                                  String key,String apiName){
        List<RecyclingRuleInfoModel> recyclingRules;
        if (CollectionUtils.isNotEmpty(ruleCacheMap.get(key))){
            return ruleCacheMap.get(key);
        }else {
            recyclingRules = getRecyclingRule(tenantId, key, apiName);
            ruleCacheMap.put(key,recyclingRules);
        }
        return recyclingRules;
    }

    public  List<RemindRuleModel> getCacheRemindRule(String tenantId, Map<String,List<RemindRuleModel>> remindRuleCacheMap,
                                                            String key){
        List<RemindRuleModel> remindRules;
        if (CollectionUtils.isNotEmpty(remindRuleCacheMap.get(key))){
            return remindRuleCacheMap.get(key);
        }else {
            remindRules = getRemindRulesByDataId(tenantId, key);
            remindRuleCacheMap.put(key,remindRules);
        }
        return remindRules;
    }

}
