package com.facishare.crm.recycling.task.executor.service.impl.forecast.mq;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.recycling.task.executor.biz.ForecastBiz;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastRule;
import com.facishare.crm.recycling.task.executor.service.ForecastService;
import com.facishare.crm.recycling.task.executor.service.ForecastTaskAutomationService;
import com.facishare.crm.sfa.audit.log.context.SFALogContext;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component
public class RuleEnableForecastMessageListener extends AutoConfigurationMessageListener {

    @Autowired
    private ForecastService forecastService;
    @Autowired
    private ForecastTaskAutomationService forecastTaskAutomationService;
    @Autowired
    private ForecastBiz forecastBiz;

    @Override
    public String sectionName() {
        return "forecast-rule-enable";
    }

    @Override
    public void consumeMessage(MessageExt msg) throws MetadataServiceException {
        String msgId = msg.getMsgId();
        log.info("Receive message  <== {}", msgId);
        ForecastMessage.RuleEnable message = JSON.parseObject(msg.getBody(), ForecastMessage.RuleEnable.class);
        Objects.requireNonNull(message.getRuleId());
        Objects.requireNonNull(message.getTenantId());
        ForecastRule rule = forecastBiz.queryRuleById(message.getTenantId(), message.getRuleId());
        if (rule == null) {
            log.warn("Can not find rule [id:{}|tenant:{}]", message.getRuleId(), message.getTenantId());
            return;
        }
        String tags = msg.getTags();
        try {
            switch (tags) {
                case ForecastMessage.RULE_ENABLE_TOPIC_TAG:
                    forecastService.initTaskByRule(rule);
                    break;
                case ForecastMessage.TASK_LOCK_TOPIC_TAG:
                    message.setAction("LockTask");
                    forecastTaskAutomationService.handleForecastTaskAutoLockAfterEndMessage(rule, message);
                    break;
                case ForecastMessage.TASK_REMIND_TOPIC_TAG:
                    message.setAction("RemindTask");
                    forecastTaskAutomationService.handleForecastTaskRemindBeforeEndMessage(rule, message);
                    break;
                default:
                    break;
            }
        } finally {
            SFALogContext.clearContext();
        }
        log.info("Consume message  ==> {}", msgId);
    }
}
