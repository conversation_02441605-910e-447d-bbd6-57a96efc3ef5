package com.facishare.crm.recycling.task.executor.enums;

public enum RemindRecordTypeEnum {


    /**
     * 无通知
     */
    NON(0,""),
    /**
     * 销售线索超时
     */

    LEADS_TIME_OUT(1,"销售线索超时"),

    /**
     * 销售线索被收回
     */
    LEADS_RECYCLING(2,"销售线索被收回"),


    /**
     * 客户将被收回
     */
    CUSTOMER_RECYCLING(3,"客户将被收回"),

    /**
     * 非公海客户提醒
     */
    NO_HIGH_SEAS_REMIND(42,"非公海客户提醒"),

    /**
     * 公海客户提醒
     */
    HIGH_SEAS_REMIND(43,"公海客户提醒"),

    /**
     * 客户被收回到公海
     */
    CUSTOMER_TAKEBACK(6,"客户被收回到公海"),

    /**
     * 线索即将被自动收回
     */
    LEADS_TAKEBACK(120,"线索即将被收回"),
    /**
     * 线索被自动收回
     *
     */
    LEADS_RECYCLING_NEW(121,"线索已被收回");


    private final int value;
    private final String desc;

    RemindRecordTypeEnum(int value,String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
