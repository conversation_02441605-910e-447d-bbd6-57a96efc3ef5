package com.facishare.crm.recycling.task.executor.util;

import com.facishare.crm.recycling.task.executor.model.RecalculateMessage;
import com.facishare.crm.sfa.audit.log.EntityConverter;
import com.facishare.crm.sfa.audit.log.model.AuditArg;


/**
 * <AUTHOR> gongchun<PERSON>
 * @date : 2023/6/9 18:50
 * @description:
 */
public class SFAAuditLogRecalculateConvert implements EntityConverter<RecalculateMessage> {
    @Override
    public AuditArg convert(RecalculateMessage message) {
        return AuditArg.builder()
                .ei(message.getTenantId())
                .objectIds(message.getObjectId())
                .objectApiName(message.getObjectApiName())
                .actionCode(message.getActionCode())
                .build();
    }
}
