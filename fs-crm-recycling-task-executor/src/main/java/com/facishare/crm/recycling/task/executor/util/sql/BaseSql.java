package com.facishare.crm.recycling.task.executor.util.sql;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.service.ICommonSqlService;
import com.facishare.paas.metadata.impl.search.WhereParam;
import com.facishare.paas.metadata.service.impl.CommonSqlServiceImpl;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.util.List;

@Slf4j
@SuppressWarnings("unchecked")
public abstract class BaseSql {

	protected static final ICommonSqlService commonSqlService = SpringUtil.getContext()
			.getBean(CommonSqlServiceImpl.class);

	protected String tableName;
	protected String tenantId;
	protected List<WhereParam> whereList;
	protected boolean ignoreException = false;
	protected IActionContext actionContext;


	protected  <B extends BaseSql> B setTableName(String tableName) {
		this.tableName = tableName;
		return (B) this;
	}

	public <B extends BaseSql> B where(WhereSql whereSql) {
		this.whereList = whereSql.getWhereList();
		this.tenantId = whereSql.getTenantId();
		return (B) this;
	}

	public <B extends BaseSql> B ignoreException() {
		this.ignoreException = true;
		return (B) this;
	}

	public <B extends BaseSql> B withContext(IActionContext context) {
		this.actionContext = context;
		return (B) this;
	}

	@SuppressWarnings({"DuplicatedCode"})
	public <B extends BaseSql> B withAdminContext() {
		ActionContext actionContext = new ActionContext();
		actionContext.setEnterpriseId(tenantId);
		actionContext.setUserId(User.SUPPER_ADMIN_USER_ID);
		actionContext.setDbType("pg");
		actionContext.setAllowUpdateInvalid(true);
		actionContext.put("not_validate", true);
		actionContext.setPrivilegeCheck(false);
		this.actionContext = actionContext;
		return (B) this;
	}


	protected void checked() {
		Assert.notNull(tableName,"表名不能为空");
		Assert.notNull(tenantId, "企业ID不能为空");
		Assert.notNull(actionContext, "ActionContext不能为空");
		Assert.notEmpty(whereList, "where条件不能为空");
	}


}
