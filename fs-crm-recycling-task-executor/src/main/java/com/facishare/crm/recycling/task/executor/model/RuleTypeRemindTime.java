package com.facishare.crm.recycling.task.executor.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-03-21 17:10
 */

@Data
@Builder
@Component
@NoArgsConstructor
@AllArgsConstructor
public class RuleTypeRemindTime implements Comparable<RuleTypeRemindTime>{

    /**
     * {@link com.facishare.crm.recycling.task.executor.enums.RuleTypeEnum}
     */
    String ruleType;

    Long remindTime;

    Integer days;

    @Override
    public int compareTo(@NotNull RuleTypeRemindTime o) {
        return remindTime.compareTo(o.getRemindTime());
    }
}
