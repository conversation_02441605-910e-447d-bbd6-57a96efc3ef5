package com.facishare.crm.recycling.task.executor.service;

import com.facishare.crm.recycling.task.executor.biz.CustomerBiz;
import com.facishare.crm.recycling.task.executor.model.CustomObjectDataChangeMQMessage;
import com.facishare.crm.sfa.lto.common.LtoOrgCommonService;
import com.facishare.crm.sfa.lto.exception.ExceptionUtil;
import com.facishare.crm.sfa.lto.objectlimit.ObjectLimitRuleService;
import com.facishare.crm.sfa.lto.objectlimit.models.ObjectLimitRuleModel;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.facishare.crm.recycling.task.executor.util.ConstantUtils.PERSONNEL_OBJ;

/**
 * @Description
 * <AUTHOR>
 * @Date 2021/1/26 14:29
 */

@Slf4j
@Component
public class ObjectLimitEmployeeService {
    @Autowired
    private ObjectLimitRuleService objectLimitBiz;

    @Autowired
    private CustomerBiz customerBiz;

    @Autowired
    private LtoOrgCommonService ltoOrgCommonService;

    public void employeeCustomOnChanged(CustomObjectDataChangeMQMessage message) throws MetadataServiceException {
        String tenantId = message.getTenantId();
        List<ObjectLimitRuleModel.ObjectLimitRule> objectLimitRuleList = objectLimitBiz.getObjectLimitRuleByDataType(tenantId, ObjectLimitRuleModel.DataTypeEnum.CUSTOM.getCode());
        if (CollectionUtils.empty(objectLimitRuleList)){
            return;
        }
        List<ObjectLimitRuleModel.ObjectLimitEmployeeRule> insertEmployeeRuleList = Lists.newArrayList();
        List<ObjectLimitRuleModel.ObjectLimitEmployeeRule> deleteEmployeeRuleList = Lists.newArrayList();
        for (CustomObjectDataChangeMQMessage.Content content : message.getBody()) {
            String objectId = content.getObjectId();
            IObjectData personnelData = customerBiz.getObjectById(tenantId, objectId, PERSONNEL_OBJ);
            String userId = personnelData.get("user_id").toString();
            List<ObjectLimitRuleModel.ObjectLimitEmployeeRule> employeeRuleList = objectLimitBiz.getEmployeeRuleByEmployeeId(tenantId, userId);
            ArrayList mainDepartment = (ArrayList) personnelData.get("main_department");
            ArrayList viceDepartments = (ArrayList) personnelData.get("vice_departments");
            doCustomLimitRule(tenantId, userId, employeeRuleList, objectLimitRuleList,insertEmployeeRuleList, deleteEmployeeRuleList);
            List dataIds = Lists.newArrayList();
            if (CollectionUtils.notEmpty(mainDepartment)) {
                dataIds.addAll(mainDepartment);
            }
            if (CollectionUtils.notEmpty(viceDepartments)){
                dataIds.addAll(viceDepartments);
            }
            resetEmployeeRule(tenantId, userId, employeeRuleList, insertEmployeeRuleList, deleteEmployeeRuleList, dataIds);
        }
    }

    public void resetEmployeeRule(String tenantId, String userId, List<ObjectLimitRuleModel.ObjectLimitEmployeeRule> employeeRuleList, List<ObjectLimitRuleModel.ObjectLimitEmployeeRule> insertEmployeeRuleList, List<ObjectLimitRuleModel.ObjectLimitEmployeeRule> deleteEmployeeRuleList, List<String> dataIds) throws MetadataServiceException {
        if(CollectionUtils.notEmpty(dataIds)) {
            log.info("resetEmployeeRule departmentIds: {}", dataIds);
            dataIds = dataIds.stream().distinct().collect(Collectors.toList());
            List<String> allSuperDepartmentIds = ltoOrgCommonService.getAllSuperDepartmentByIds(tenantId, dataIds);
            allSuperDepartmentIds.add("999999");
            List<String> allSubDepartmentIds = ltoOrgCommonService.getAllSubDepartmentByIds(tenantId, dataIds);
            Set<String> allDeptIds = Sets.newHashSet();
            allDeptIds.addAll((allSuperDepartmentIds));
            allDeptIds.addAll(allSubDepartmentIds);
            allDeptIds.addAll(dataIds);
            log.info("resetEmployeeRule all departmentIds: {}", allDeptIds);
            calculateDepartment(tenantId, Lists.newArrayList(allDeptIds));
        }

        dataIds = ltoOrgCommonService.getUserGroupIdsByMemberId(tenantId, userId);
        if(CollectionUtils.notEmpty(dataIds)) {
            getNeedInsertEmployeeRule(tenantId, userId, employeeRuleList, insertEmployeeRuleList, dataIds, ObjectLimitRuleModel.DataTypeEnum.USER_GROUP.getCode());
        }

        dataIds = ltoOrgCommonService.getUserRoleIdsByUserId(tenantId, userId);
        if(CollectionUtils.notEmpty(dataIds)) {
            getNeedInsertEmployeeRule(tenantId, userId, employeeRuleList, insertEmployeeRuleList, dataIds, ObjectLimitRuleModel.DataTypeEnum.USER_ROLE.getCode());
        }

        if(CollectionUtils.notEmpty(deleteEmployeeRuleList)) {
            objectLimitBiz.deleteEmployeeRule(tenantId, deleteEmployeeRuleList);
            Set<String> groupIds = deleteEmployeeRuleList.stream().map(ObjectLimitRuleModel.ObjectLimitEmployeeRule::getGroupId).collect(Collectors.toSet());
            for(String groupId : groupIds) {
                List<String> deletedEmployeeList = deleteEmployeeRuleList.stream().filter(x -> groupId.equals(x.getGroupId()))
                        .map(ObjectLimitRuleModel.ObjectLimitEmployeeRule::getEmployeeId).collect(Collectors.toList());
                log.warn(String.format("ObjectLimitEmployeeService-resetEmployeeRule: tenantID-%s, groupId-%s, delete employee limit rule employeeIds: %s", tenantId, groupId, String.join(",", deletedEmployeeList)));
            }
        }

        if(CollectionUtils.notEmpty(insertEmployeeRuleList)) {
            objectLimitBiz.insertEmployeeRule(tenantId, insertEmployeeRuleList);
        }
    }


    public void getNeedInsertEmployeeRule(String tenantId, String userId,
                                           List<ObjectLimitRuleModel.ObjectLimitEmployeeRule> employeeRuleList,
                                           List<ObjectLimitRuleModel.ObjectLimitEmployeeRule> insertEmployeeRuleList,
                                           List<String> dataIds, String dataType) {
        List<ObjectLimitRuleModel.ObjectLimitRule> objectLimitRuleList;
        Optional<ObjectLimitRuleModel.ObjectLimitEmployeeRule> optionalObjectLimitEmployeeRule;
        objectLimitRuleList = objectLimitBiz.getObjectLimitRuleByDataIds(tenantId, dataIds, dataType);
        if (CollectionUtils.notEmpty(objectLimitRuleList)) {
            for (ObjectLimitRuleModel.ObjectLimitRule limitRule : objectLimitRuleList) {
                optionalObjectLimitEmployeeRule = employeeRuleList.stream()
                        .filter(x -> x.getObjectApiName().equals(limitRule.getObjectApiName()) && x.getGroupId().equals(limitRule.getGroupId()))
                        .findFirst();

                if(optionalObjectLimitEmployeeRule.isPresent()){
                    continue;
                }
                optionalObjectLimitEmployeeRule = insertEmployeeRuleList.stream()
                        .filter(x -> x.getObjectApiName().equals(limitRule.getObjectApiName()) && x.getGroupId().equals(limitRule.getGroupId()))
                        .findFirst();
                if(optionalObjectLimitEmployeeRule.isPresent()){
                    continue;
                }
                ObjectLimitRuleModel.ObjectLimitEmployeeRule limitEmployeeRule = ObjectLimitRuleModel.ObjectLimitEmployeeRule.builder()
                        .employeeId(userId).groupId(limitRule.getGroupId()).objectApiName(limitRule.getObjectApiName())
                        .calculateId(limitRule.getCalculateId()).build();
                insertEmployeeRuleList.add(limitEmployeeRule);
            }
        }
    }


    public void calculateDepartment(String tenantId, List<String> departmentIds) {
        String dataType = ObjectLimitRuleModel.DataTypeEnum.DEPARTMENT.getCode();
        List<ObjectLimitRuleModel.ObjectLimitRule> objectLimitRuleList = objectLimitBiz.getObjectLimitRuleByDataIds(tenantId, Lists.newArrayList(departmentIds), dataType);
        if (CollectionUtils.empty(objectLimitRuleList)) {
            return;
        }

        Set<String> objectApiNameSet = objectLimitRuleList.stream().map(x -> x.getObjectApiName())
                .collect(Collectors.toSet());

        try {
            for (String objectApiName : objectApiNameSet) {
                Set<String> groupIdSet = objectLimitRuleList.stream()
                        .filter(x -> objectApiName.equals(x.getObjectApiName())).map(x -> x.getGroupId())
                        .collect(Collectors.toSet());
                if (CollectionUtils.empty(groupIdSet)) {
                    continue;
                }

                for (String groupId : groupIdSet) {
                    List<ObjectLimitRuleModel.ObjectLimitRule> tempLimitRuleList = objectLimitBiz.getObjectLimitRuleByGroupId(tenantId, objectApiName, groupId);
                    if (CollectionUtils.empty(tempLimitRuleList)) {
                        continue;
                    }

                    List<String> limitEmployeeList = objectLimitBiz.getEmployeeRuleByGroupId(tenantId, objectApiName, groupId);
                    List<String> allLimitEmployeeList = objectLimitBiz.getObjectLimitRuleUserIds(tenantId, tempLimitRuleList);

                    List<String> deletedEmployeeList = limitEmployeeList.stream()
                            .filter(x -> !allLimitEmployeeList.contains(x)).collect(Collectors.toList());
                    if (CollectionUtils.notEmpty(deletedEmployeeList)) {
                        objectLimitBiz.deleteEmployeeRuleByEmployeeIds(tenantId, objectApiName, groupId, deletedEmployeeList);
                        limitEmployeeList.removeIf(deletedEmployeeList::contains);
                        log.warn(String.format("ObjectLimitEmployeeService-calculateDepartment: tenantID-%s, groupId-%s, delete employee limit rule employeeIds: %s", tenantId, groupId, String.join(",", deletedEmployeeList)));
                    }
                    List<String> insertEmployeeList = allLimitEmployeeList.stream()
                            .filter(x -> !limitEmployeeList.contains(x)).collect(Collectors.toList());

                    if (CollectionUtils.notEmpty(insertEmployeeList)) {
                        String calculateId = tempLimitRuleList.get(0).getCalculateId();
                        objectLimitBiz.insertEmployeeRule(tenantId, objectApiName, groupId, calculateId, Lists.newArrayList(insertEmployeeList));
                    }
                }
            }
        } catch (Exception e) {
            ExceptionUtil.throwCommonBusinessException();
        }
    }


    /**
     * 处理自定义保有量规则
     * @param tenantId
     * @param userId
     * @param employeeRuleList
     * @param insertEmployeeRuleList
     * @param deleteEmployeeRuleList
     */
    public void doCustomLimitRule(String tenantId, String userId, List<ObjectLimitRuleModel.ObjectLimitEmployeeRule> employeeRuleList,
                                  List<ObjectLimitRuleModel.ObjectLimitRule> objectLimitRuleList,
                                  List<ObjectLimitRuleModel.ObjectLimitEmployeeRule> insertEmployeeRuleList, List<ObjectLimitRuleModel.ObjectLimitEmployeeRule> deleteEmployeeRuleList) {
        if(CollectionUtils.notEmpty(objectLimitRuleList)) {
            for (ObjectLimitRuleModel.ObjectLimitRule objectLimitRule : objectLimitRuleList) {
                boolean isFit =  objectLimitBiz.checkIsFitObjectLimitRule(tenantId, userId, objectLimitRule);
                Optional<ObjectLimitRuleModel.ObjectLimitEmployeeRule> objectLimitEmployeeRuleOptional = employeeRuleList.stream()
                        .filter(x -> x.getObjectApiName().equals(objectLimitRule.getObjectApiName())
                                && x.getGroupId().equals(objectLimitRule.getGroupId())).findFirst();
                if(objectLimitEmployeeRuleOptional.isPresent()) {
                    if(!isFit) {
                        employeeRuleList.removeIf(x -> x.getGroupId().equals(objectLimitRule.getGroupId()) && x.getObjectApiName().equals(objectLimitRule.getObjectApiName()));
                        deleteEmployeeRuleList.add(objectLimitEmployeeRuleOptional.get());
                    }
                } else {
                    if(isFit) {
                        ObjectLimitRuleModel.ObjectLimitEmployeeRule objectLimitEmployeeRule = ObjectLimitRuleModel.ObjectLimitEmployeeRule.builder()
                                .employeeId(userId).objectApiName(objectLimitRule.getObjectApiName())
                                .groupId(objectLimitRule.getGroupId()).calculateId(objectLimitRule.getCalculateId())
                                .build();
                        insertEmployeeRuleList.add(objectLimitEmployeeRule);
                    }
                }
            }
        }
    }

}
