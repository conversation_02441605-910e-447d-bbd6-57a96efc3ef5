package com.facishare.crm.recycling.task.executor.biz;

import com.facishare.crm.follow.util.CommonUtils;
import com.facishare.crm.recycling.task.executor.model.*;
import com.facishare.crm.recycling.task.executor.util.ObjectDataUtils;
import com.facishare.crm.sfa.lto.common.LtoOrgCommonService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.enterpriserelation2.result.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class CommonBiz {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private LtoOrgCommonService ltoOrgCommonService;
    @Autowired
    private RecyclingBiz recyclingBiz;
    @Autowired
    private IObjectDataService objectDataPgService;

    public IObjectDescribe getObjectDescribe(String tenantId, String objectApiName){
        return serviceFacade.findObject(tenantId, objectApiName);
    }

    public QueryResult<IObjectData> queryBySearchTemplate(String tenantId, String objectApiName, SearchTemplateQuery searchQuery) {
        QueryResult<IObjectData> result;
        try {
            FindByQuery.QueryBySearchTemplateArg arg  = FindByQuery.QueryBySearchTemplateArg.builder()
                    .queryJson(searchQuery).build();
            result = serviceFacade.findBySearchQuery(CommonUtils.buildUser(tenantId), objectApiName, searchQuery);
        } catch (Exception e) {
            log.error("queryObjectData throw exception,tenantId:{}, objectApiName:{}",  tenantId, objectApiName,e);
            throw new RuntimeException();
        }
        return result;
    }

    public List<IObjectData> getObjectByIds(String tenantId, String objectApiName, List<String> objectIds, boolean includeDeletedData) {
        if(includeDeletedData) {
            return serviceFacade.findObjectDataByIdsIncludeDeleted(CommonUtils.buildUser(tenantId), objectIds, objectApiName);
        } else {
            return serviceFacade.findObjectDataByIds(tenantId, objectIds, objectApiName);
        }
    }

    public PrmModel.MatchOutOwnerResult matchOutOwner(User user, List<IObjectData> dataList) {
        PrmModel.MatchOutOwnerResult result = new PrmModel.MatchOutOwnerResult();
        result.setAllMatch(true);
        result.setNotMatchIds(Lists.newArrayList());
        List<IObjectData> hasOutOwnerDataList = dataList.stream().filter(d -> org.apache.commons.collections.CollectionUtils.isNotEmpty(d.getOutOwner())).collect(Collectors.toList());
        List<IObjectData> needCheckDataList = hasOutOwnerDataList.stream().filter(d -> org.apache.commons.lang3.StringUtils.isNotBlank(ObjectDataUtils.getValueOrDefault(d, "partner_id", ""))).collect(Collectors.toList());
        List<IObjectData> notMatchDataList = hasOutOwnerDataList.stream().filter(d -> org.apache.commons.lang3.StringUtils.isBlank(ObjectDataUtils.getValueOrDefault(d, "partner_id", ""))).collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(notMatchDataList)) {
            result.setAllMatch(false);
            List<String> ids = notMatchDataList.stream().map(IObjectData::getId).collect(Collectors.toList());
            result.getNotMatchIds().addAll(ids);
        }
        List<String> partnerIds = needCheckDataList.stream().map(d -> d.get("partner_id", String.class)).collect(Collectors.toList());

        Map<String, RelationDownstreamResult> relationDataMap = ltoOrgCommonService.findRelationDownstreamVoByPartnerIds(user, Sets.newHashSet(partnerIds));
        for (IObjectData data : needCheckDataList) {
            String outOwner = data.getOutOwner().get(0).toString();
            String partnerId = data.get("partner_id", String.class);
            RelationDownstreamResult downstreamResult = relationDataMap.get(partnerId);
            Long ownerOuterUid = null;
            if (downstreamResult != null) {
                ownerOuterUid = downstreamResult.getRelationOwnerOuterUid();
            }
            if (!Long.valueOf(outOwner).equals(ownerOuterUid)) {
                result.setAllMatch(false);
                result.getNotMatchIds().add(data.getId());
            }
        }
        return result;
    }


    public IObjectData findById(String objectId,String tenantId,String describeApiName,boolean includeInvalid) {
        IObjectData objectData;
        try {
            objectData = objectDataPgService.findById(objectId, tenantId, CommonUtils.buildContextAllInvalid(tenantId), describeApiName, includeInvalid);
        } catch (MetadataServiceException e) {
            log.error("CustomObjectDataChangeListener findById error tenantId:{} ", tenantId, e);
            throw new RuntimeException(e);
        }
        return objectData;
    }

    public List<RecyclingRuleInfoModel> getRecyclingRules(String tenantId, List<String> dataIds, String apiName) {
        return recyclingBiz.getRecyclingRules(tenantId,dataIds,apiName);
    }
}