package com.facishare.crm.recycling.task.executor.common;

import com.facishare.crm.recycling.task.executor.util.GrayUtils;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.github.autoconf.ConfigFactory;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.common.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.concurrent.CopyOnWriteArrayList;

import static com.facishare.enterprise.common.constant.TenantTemplateConstants.ACCOUNT_API_NAME;

/**
 * @Description 回收灰度控制类
 * <AUTHOR>
 * @Date 2019/9/23 10:57
 */
@Component
@Slf4j
public class RecyclingTaskGray {
    private final FsGrayReleaseBiz dataSfagray = FsGrayRelease.getInstance("sfa");
    private final FsGrayReleaseBiz followGray = FsGrayRelease.getInstance("sfa-follow");
    private CopyOnWriteArrayList<String> grayTenants = new CopyOnWriteArrayList();

    public static final String RECALCULATE_LOCK_LEADS_GRAY = "recalculate_lock_leads_gray";
    public static final String RECALCULATE_LOCK_ACCOUNT_GRAY = "recalculate_lock_account_gray";

    @Autowired
    private GrayUtils grayUtils;

    public FsGrayReleaseBiz getDataSfagray() {
        return dataSfagray;
    }

    public CopyOnWriteArrayList getGrayTenants() {
        return grayTenants;
    }

    /**
     * fs-metadata-gray
     * @param tenantId
     * @return
     */
    public Boolean isFSMetadataGray(String tenantId){
        if (CollectionUtils.empty(grayTenants)){
            return false;
        }
        if (grayTenants.size() == 1 && "all".equalsIgnoreCase(grayTenants.get(0))){
            return true;
        }
        return grayTenants.contains(tenantId);
    }


    /**
     * -1 不跳过
     * white:* 全部跳过
     * white:1|2 跳过1，2
     * @param tenantId
     * @return
     */
    public boolean skipTenantId(String tenantId){
        if (GrayUtils.skipFollowByProfile(tenantId)){
            log.info("skipFollowByProfile skipTenantId tenantId:{}",tenantId);
            return true;
        }
        return dataSfagray.isAllow("skip_tenant_id",tenantId);
    }

    @PostConstruct
    public void init() {
        ConfigFactory.getConfig("fs-metadata-gray", config -> grayTenants = new CopyOnWriteArrayList(Arrays.asList(config.get("gray_tenantIds").split(","))));
        grayTenants.removeIf(x -> Strings.isNullOrEmpty(String.valueOf(x)));
    }

    /**
     * 申请延期 灰度
     * @param tenantId
     * @return True:灰度；false:非灰度
     */
    public Boolean isExpireTimeExtend(String tenantId){
        return dataSfagray.isAllow("expire_time_extend",tenantId);
    }

    /**
     * 放入限速比较慢的队列
     * white:* 全部跳过
     * white:1|2 跳过1，2
     *
     * @param tenantId
     * @return
     */
    public Boolean toSlowRecyclingTenantId(String tenantId) {
        return dataSfagray.isAllow("slow_recycling_tenant_id", tenantId);
    }


    /**
     * 775收回的提醒优化
     * white:* 全部跳过
     * white:1|2 跳过1，2
     *
     * @param tenantId
     * @return
     */
    public Boolean recyclingForwardBatch(String tenantId) {
        return dataSfagray.isAllow("recycling_forward_batch", tenantId);
    }

    /**
     * 到期时间，跳过节假日
     * white:* 全部跳过
     * white:1|2 跳过1，2
     *
     * @param tenantId
     * @return
     */
    public Boolean expireTimeSkipHolidays(String tenantId) {
        return followGray.isAllow("expire_time_skip_holidays", tenantId);
    }


    /**
     * @param tenantId
     * @return
     */
    public Boolean recalculateLockGray(String tenantId, String objectApiName) {
        if (ACCOUNT_API_NAME.equals(objectApiName)) {
            dataSfagray.isAllow(RECALCULATE_LOCK_ACCOUNT_GRAY, tenantId);
        }
        return dataSfagray.isAllow(RECALCULATE_LOCK_LEADS_GRAY, tenantId);
    }

    public boolean skipObjectLimitRuleAdaptOrgTenantId(String tenantId){
        return followGray.isAllow("skip_limit_rule_org_tenant_id", tenantId);
    }
}
