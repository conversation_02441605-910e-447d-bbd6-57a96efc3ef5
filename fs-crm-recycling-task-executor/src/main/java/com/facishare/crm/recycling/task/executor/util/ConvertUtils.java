package com.facishare.crm.recycling.task.executor.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.recycling.task.executor.model.*;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Operator;
import com.google.common.collect.Lists;
import net.sf.cglib.beans.BeanMap;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-03-01 17:27
 */

public class ConvertUtils {

    /**
     * 通过findbysql 返回的map转换成List<IObjectData>
     * @param map List<map>参数
     * @return 返回List<IObjectData>数据
     */

    public static List<IObjectData> convertMapToObjectData(List<Map> map){
        List<IObjectData> rst = new ArrayList<>();
        if(CollectionUtils.notEmpty(map)){
            rst = map.stream().map(o-> ObjectDataExt.of(o).getObjectData()).collect(Collectors.toList());
        }
        return rst;
    }


    public static RecyclingMessage convertRecalculate(RecalculateMessage message){
        RecyclingMessage recyclingMessage = RecyclingMessage.builder()
                .objectApiName(message.getObjectApiName())
                .objectData(message.getObjectData())
                .objectId(message.getObjectId())
                .targetId(message.getTargetId())
                .tenantId(message.getTenantId())
                .build();
        return recyclingMessage;
    }


    /**
     * 将map装换为javabean对象
     * @param map
     * @param bean
     * @return
     */
    public static <T> T mapToBean(Map<String, Object> map,T bean) {
        BeanMap beanMap = BeanMap.create(bean);
        beanMap.putAll(map);
        return bean;
    }


    public static void convert2RecyclingRule(List<RecyclingRuleInfoModel> ruleList, List<Map> dbRuleList) {
        if(com.facishare.paas.appframework.common.util.CollectionUtils.empty(dbRuleList)) {
            return;
        }
        dbRuleList.forEach(r -> {
            RecyclingRuleInfoModel item = new RecyclingRuleInfoModel();
            item.setDataId(r.get(RecyclingRuleConstans.Field.Data_Id).toString());
            item.setDealDays((Integer) r.get(RecyclingRuleConstans.Field.Deal_Days));
            item.setFollowUpDays((Integer) r.get(RecyclingRuleConstans.Field.Follow_Up_Days));
            item.setGroupId(r.get(RecyclingRuleConstans.Field.Group_Id).toString());
            item.setPriority((Integer) r.get(RecyclingRuleConstans.Field.Priority));
            item.setIsIncludePastTime((Boolean) r.get(RecyclingRuleConstans.Field.Is_Include_Past_Time));
            item.setId(r.get(RecyclingRuleConstans.Field.Id).toString());
            item.setRecyclingRuleType((Integer) r.get(RecyclingRuleConstans.Field.Recycling_Rule_Type));
            item.setWheres(r.get(RecyclingRuleConstans.Field.Wheres).toString());
            item.setUpdateTime((long) r.get(RecyclingRuleConstans.Field.Last_Modified_Time));
            item.setTargetPoolId(r.get(RecyclingRuleConstans.Field.Target_Pool_Id).toString());
            item.setDataType((Integer) r.get(RecyclingRuleConstans.Field.Data_Type));
            item.setFunctionApiName(r.get(RecyclingRuleConstans.Field.FUNCTION_API_NAME) == null ? null : r.get(RecyclingRuleConstans.Field.FUNCTION_API_NAME).toString());
            item.setFunctionName(r.get(RecyclingRuleConstans.Field.FUNCTION_NAME) == null ? null : r.get(RecyclingRuleConstans.Field.FUNCTION_NAME).toString());
            item.setSkipHolidays(r.get(RecyclingRuleConstans.Field.SKIP_HOLIDAYS) != null && (Boolean) r.get(RecyclingRuleConstans.Field.SKIP_HOLIDAYS));
            ruleList.add(item);
        });
    }

    public static void convert2RecyclingRemindRule(List<RecyclingRemindRuleModel> ruleList, List<Map> dbRuleList) {
        if(CollectionUtils.empty(dbRuleList)) {
            return;
        }
        dbRuleList.forEach(x -> {
            RecyclingRemindRuleModel item = new RecyclingRemindRuleModel();
            item.setDataId(x.get(RecyclingRemindRuleConstants.Field.Data_Id).toString());
            item.setGroupId(x.get(RecyclingRemindRuleConstants.Field.Group_Id).toString());
            item.setRecyclingRuleId(x.get(RecyclingRemindRuleConstants.Field.Recycling_Rule_Id).toString());
            item.setId(x.get(RecyclingRemindRuleConstants.Field.Id).toString());
            item.setRemindDays((Integer) x.get(RecyclingRemindRuleConstants.Field.Remind_Days));
            item.setRuleType(x.get(RecyclingRemindRuleConstants.Field.Rule_Type).toString());
            ruleList.add(item);
        });
    }

    /**
     *  新表的数据 转为老表的数据格式，做一下兼容
     * @param model
     * @return
     */
    public static RecyclingRule convert2Old(RecyclingRuleInfoModel model){
        RecyclingRule recyclingRule = new RecyclingRule();
        recyclingRule.setDataID(model.getDataId());
        recyclingRule.setDataType(model.getDataType());
        recyclingRule.setDealDays(model.getDealDays());
        recyclingRule.setFollowUpDays(model.getFollowUpDays());
        recyclingRule.setIsIncludePastTime(model.getIsIncludePastTime());
        recyclingRule.setUpdateTime(model.getUpdateTime());
        recyclingRule.setHighSeasID(model.getTargetPoolId());
        List<RecyclingRemindRule> recyclingRemindRuleList = new ArrayList<>();
        for (RecyclingRemindRuleModel x : model.getRecyclingRemindRuleList()) {
            RecyclingRemindRule recyclingRemindRule = new RecyclingRemindRule();
            recyclingRemindRule.setDataID(x.getDataId());
            recyclingRemindRule.setGroupID(x.getGroupId());
            recyclingRemindRule.setRecyclingRemindRuleID(x.getId());
            recyclingRemindRule.setRemindDays(x.getRemindDays());
            recyclingRemindRule.setRuleType(Integer.parseInt(x.getRuleType()));
            recyclingRemindRuleList.add(recyclingRemindRule);
        }
        recyclingRule.setRecyclingRemindRuleList(recyclingRemindRuleList);
        return recyclingRule;
    }

	public static void convert2WheresList(String wheresString, List<Wheres> wheresList) {
		if(StringUtils.isEmpty(wheresString)) {
			return;
		}
		List<JSONObject> wheresJSONObjectList = JSON.parseObject(wheresString, List.class);
		if(CollectionUtils.notEmpty(wheresJSONObjectList)){
			for(JSONObject jsonObject : wheresJSONObjectList){
				Wheres wheres = JSON.parseObject(jsonObject.toJSONString(), Wheres.class);
				if(CollectionUtils.notEmpty(wheres.getFilters())) {
					for(IFilter filter : wheres.getFilters()) {
						if(filter.getFieldValues() == null) {
							filter.setFieldValues(Lists.newArrayList());
						}
					}
				}
				wheresList.add(wheres);
			}
		}
	}

	public static void convertFilter(List<Wheres> wheresList, IObjectDescribe objectDescribe) {
		if(CollectionUtils.empty(wheresList) || objectDescribe == null) {
			return;
		}

		for (Wheres wheres : wheresList) {
			if(CollectionUtils.empty(wheres.getFilters())) {
				continue;
			}
			for (IFilter filter : wheres.getFilters()) {
				IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(filter.getFieldName());
				if(fieldDescribe == null) {
					continue;
				}
				if(IFieldType.OBJECT_REFERENCE.equals(fieldDescribe.getType())) {
					filter.setFieldName(String.format("%s.name", filter.getFieldName()));
				} else if(IFieldType.SELECT_MANY.equals(fieldDescribe.getType())) {
					if(Operator.IN.equals(filter.getOperator())) {
						filter.setOperator(Operator.HASANYOF);
					} else if(Operator.NIN.equals(filter.getOperator())) {
						filter.setOperator(Operator.NHASANYOF);
					}
				}
			}
		}
	}

}
