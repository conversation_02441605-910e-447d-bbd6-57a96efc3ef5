package com.facishare.crm.recycling.task.executor.enums;

/**
 * 老字段类型枚举类
 */
public enum OldFieldTypeEnums {


    CutoffRule(1, "分割线"),
    SingleLineText(2, "单行文本"),
    MultiLineText(3, "多行文本"),
    Integere(4, "整数"),
    Decimall(5, "小数"),
    Money(6, "金额"),
    <PERSON><PERSON>(7, "日期型"),
    SingleSelection(8, "单选"),
    MultiSelection(9, "多选");


    private final Integer value;
    private final String desc;


    OldFieldTypeEnums(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }
}
