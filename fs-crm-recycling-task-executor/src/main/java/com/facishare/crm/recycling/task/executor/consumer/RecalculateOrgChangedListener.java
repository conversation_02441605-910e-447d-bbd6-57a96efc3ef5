package com.facishare.crm.recycling.task.executor.consumer;

import com.facishare.crm.recycling.task.executor.biz.CustomerBiz;
import com.facishare.crm.recycling.task.executor.biz.RecyclingBiz;
import com.facishare.crm.recycling.task.executor.common.RecyclingTaskGray;
import com.facishare.crm.recycling.task.executor.common.SfaRecyclingTaskRateLimiterService;
import com.facishare.crm.recycling.task.executor.model.RecyclingRuleInfoModel;
import com.facishare.crm.recycling.task.executor.model.RemindRuleModel;
import com.facishare.crm.recycling.task.executor.service.impl.AccountRecalculate;
import com.facishare.crm.sfa.lto.utils.TenantUtil;
import com.facishare.organization.api.event.OrganizationChangedListener;
import com.facishare.organization.api.event.organizationChangeEvent.EmployeeChangeEvent;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static com.facishare.crm.recycling.task.executor.util.ConstantUtils.HIGH_SEAS_ID;
import static com.facishare.crm.recycling.task.executor.util.ConstantUtils.HIGH_SEAS_OBJ;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020/2/7 17:30
 */
@Slf4j
@Component
public class RecalculateOrgChangedListener extends OrganizationChangedListener {
    @Autowired
    private CustomerBiz customerBiz;

    @Autowired
    private RecyclingBiz recyclingBiz;

    @Autowired
    SfaRecyclingTaskRateLimiterService sfaRecyclingTaskRateLimiterService;

    @Autowired
    private AccountRecalculate accountRecalculate;

    @Autowired
    private RecyclingTaskGray recyclingTaskGray;

    @Autowired
    private TenantUtil tenantUtil;

    public RecalculateOrgChangedListener() {
        super("recalculate_org_changed");
    }

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        for (MessageExt msg : msgs) {
            log.info("RecalculateOrgChangedListener,msgId{}", msg.getMsgId());
        }
        return super.consumeMessage(msgs, context);
    }

    @Override
    protected void onEmployeeChanged(EmployeeChangeEvent event) throws Throwable {
        String tenantId = String.valueOf(event.getEnterpriseId());
        if (tenantUtil.isExclusiveCloudEnterprise(tenantId)) {
            log.info("{} is exclusive cloud ei", tenantId);
            return;
        }

        if (recyclingTaskGray.skipTenantId(tenantId)){
            log.info("RecalculateOrgChangedListener skipTenantId tenantId:{}", tenantId);
            return;
        }

        // 限流
        log.info("RecalculateOrgChangedListener onEmployeeChanged,event:{}", event);
        EmployeeDto newEmployee = event.getNewEmployeeDto();
        EmployeeDto oldEmployee = event.getOldEmployeeDto();

        if (newEmployee != null && oldEmployee != null) {
            String employeeId = Integer.toString(newEmployee.getEmployeeId());
            if (CollectionUtils.notEmpty(newEmployee.getDepartmentIds()) && CollectionUtils.notEmpty(oldEmployee.getDepartmentIds())) {
                Set<Integer> newMainDepartmentIds = new HashSet<>(newEmployee.getMainDepartmentIds());
                Set<Integer> oldMainDepartmentIds = new HashSet<>(oldEmployee.getMainDepartmentIds());
                // 主属部门没有变更,判断附属部门
                if (newMainDepartmentIds.containsAll(oldMainDepartmentIds) && oldMainDepartmentIds.containsAll(newMainDepartmentIds)) {
                } else {
                    recalculateByOwner(tenantId, employeeId);
                }
            }
        }
    }

    private void recalculateByOwner(String tenantId, String owner) {
        newRecalculateByOwner(tenantId, Lists.newArrayList(owner));
    }

    /**
     * 计算非公海成员的到期时间
     *
     * @param tenantId
     * @param owners
     */
    private void newRecalculateByOwner(String tenantId, List<String> owners) {
        int limit = 100;
        int offset = 0;
        log.info("loopRecalculateByOwner begin tenantId:{},owners:{}", tenantId, owners);
        for (String owner : owners) {
            List<IObjectData> objectDataList;
            //  查询部门下员工走的规则
            String deptId = customerBiz.getDept(tenantId, owner);
            List<RecyclingRuleInfoModel> recyclingRules = recyclingBiz.getRecyclingRule(tenantId, deptId, HIGH_SEAS_OBJ);
            List<RemindRuleModel> remindRules = recyclingBiz.getRemindRulesByDataId(tenantId, deptId);
            do {
                objectDataList = customerBiz.getCustomerLimitByOwner(tenantId, owner, limit, offset);
                if (CollectionUtils.empty(objectDataList)) {
                    log.warn("this dept no objectDatas tenantId:{},owner:{}, deptId:{}", tenantId, owner, deptId);
                    break;
                }

                log.info("loopRecalculateByOwner tenant_id:{},offset:{},customerSize:{},owner:{},", tenantId, offset, objectDataList.size(), owner);

                for (IObjectData objectData : objectDataList) {
                    // 跳过公海客户
                    if (objectData.get(HIGH_SEAS_ID) != null && StringUtils.isNotBlank(objectData.get(HIGH_SEAS_ID, String.class))) {
                        continue;
                    }
                    if (recyclingTaskGray.skipTenantId(tenantId)){
                        log.info("RecalculateOrgChangedListener skipTenantId tenantId:{}", tenantId);
                        return;
                    }

                    if (CollectionUtils.empty(recyclingRules)) {
                        customerBiz.clearExpireTime(objectData);
                        continue;
                    }
                    sfaRecyclingTaskRateLimiterService.getCrmRecalculateDeptLimiter().acquire();
                    accountRecalculate.recalculateNewCustomer(tenantId, objectData, recyclingRules, remindRules, null);
                }
                offset += objectDataList.size();
            } while (objectDataList.size() == limit);
            log.info("loopRecalculateByOwner end, tenantId:{},deptId:{},owner:{},offset:{} ", tenantId, deptId, owner, offset);
            offset = 0;
        }
    }

}
