package com.facishare.crm.recycling.task.executor.model;

import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-03-20 15:42
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
public class RemindMessage {

    private String tenantId;

    private String objectApiName;

    private String objectId;

    /**
     * 公海规则提醒时间
     */
    private Date remindTime;

    List<RuleTypeRemindTime> ruleTypeRemindTimes;

    // 历史数据迁移使用
    private String batchRemind;

    // 有值表示计算过，避免再次计算
    private String calculate;

    /**
     * 提醒创建时间
     */
    private Date createTime;

    /**
     * 新版本增加一个标识位
     */
    private String newVersion;

}

