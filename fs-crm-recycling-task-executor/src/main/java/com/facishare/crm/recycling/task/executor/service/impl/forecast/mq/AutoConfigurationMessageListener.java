package com.facishare.crm.recycling.task.executor.service.impl.forecast.mq;

import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.fxiaoke.rocketmq.util.MessageHelper;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;

@Slf4j
abstract class AutoConfigurationMessageListener implements InitializingBean, ApplicationListener<ContextClosedEvent> {

    private AutoConfMQPushConsumer consumer;

    abstract String sectionName();

    abstract void consumeMessage(MessageExt msg) throws MetadataServiceException;

    @Override
    public void afterPropertiesSet() {
        this.consumer = new AutoConfMQPushConsumer("fs-crm-task-sfa-mq.ini", sectionName(), messageListenerConcurrently());
        consumer.start();
    }

    private MessageListenerConcurrently messageListenerConcurrently() {
        return (msgs, context) -> {
            for (int i = 0; i < msgs.size(); i++) {
                MessageExt msg = msgs.get(i);
                try {
                    MessageHelper.fillContextFromMessage(TraceContext.get(), msg);
                    consumeMessage(msg);
                } catch (Exception e) {
                    log.error("Consume message error [id:{}|body:{}]", msg.getMsgId(), new String(msg.getBody()), e);
                    context.setAckIndex(i - 1); // 指明最后一个正常消费的索引号
                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        };
    }

    @Override
    public void onApplicationEvent(ContextClosedEvent event) {
        log.info("Receive context closed event {}", event);
        if (consumer != null) {
            consumer.close();
            consumer = null;
        }
    }
}
