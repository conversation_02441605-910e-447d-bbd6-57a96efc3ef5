//package com.facishare.crm.recycling.task.executor.consumer;
//
//import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
//import com.fxiaoke.rocketmq.util.MessageHelper;
//import com.github.trace.TraceContext;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
//import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
//import org.apache.rocketmq.common.message.MessageExt;
//import org.springframework.context.ApplicationListener;
//import org.springframework.context.event.ContextRefreshedEvent;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.PostConstruct;
//import javax.annotation.PreDestroy;
//
//@Slf4j
//@Component
//public class SFATodoListener implements ApplicationListener<ContextRefreshedEvent> {
//
//    private AutoConfMQPushConsumer consumer;
//
//    @PostConstruct
//    public void init() {
//        consumer = new AutoConfMQPushConsumer("fs-crm-task-sfa-mq.ini", "sfa-todo-remark-consumer", (MessageListenerConcurrently) (msgs, context) -> {
//            if (!msgs.isEmpty()) {
//                for (MessageExt msg : msgs) {
//                    // 取出traceContext
//                    MessageHelper.fillContextFromMessage(TraceContext.get(), msg);
//                    consumeMessage(msg);
//                }
//            }
//            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
//        });
//    }
//
//
//    @PreDestroy
//    public void close() {
//        consumer.close();
//    }
//
//    @Override
//    public void onApplicationEvent(ContextRefreshedEvent event) {
//        if (consumer != null && event.getApplicationContext().getParent() == null) {
//            consumer.start();
//        }
//    }
//
//    private void consumeMessage(MessageExt body) {
//
//    }
//
//
//}
//
