package com.facishare.crm.recycling.task.executor.service;

import com.facishare.crm.recycling.task.executor.biz.RecyclingBiz;
import com.facishare.crm.recycling.task.executor.enums.ActionCodeEnum;
import com.facishare.crm.recycling.task.executor.model.RecalculateMessage;
import com.fxiaoke.helper.StringHelper;
import com.fxiaoke.notifier.support.NotifierClient;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/4/29 18:08
 */

@Slf4j
@Component
public class RecyclingRuleCacheService {

    @Autowired
    private RecyclingBiz recyclingBiz;

    private LoadingCache<String, RecyclingRuleCache> cache;

    public static final String JVM_CACHE_CLEAR = "recycling-rule-jvm-cache-clear";


    @PostConstruct
    public void init() {
        cache = CacheBuilder.newBuilder().maximumSize(10000).expireAfterWrite(30, TimeUnit.MINUTES).build(new CacheLoader<String, RecyclingRuleCache>() {
            @Override
            public RecyclingRuleCache load(String tenantId) throws Exception {
                boolean recyclingRuleExists;
                try {
                    recyclingRuleExists = recyclingBiz.getRecyclingRuleExistes(tenantId);
                    log.info("Checked rule existence for tenantId: {}. Found: {}. This state will be cached.", tenantId, recyclingRuleExists);
                } catch (Exception e) {
                    log.error("Failed to check recycling rule existence for tenantId: {} during cache load. This will cause cache load to fail.", tenantId, e);
                    throw e; // Propagate exception to be wrapped in ExecutionException by Guava
                }

                if (recyclingRuleExists) {
                    return RecyclingRuleCache.NOT_EMPTY;
                } else {
                    return RecyclingRuleCache.EMPTY;
                }
            }
        });
        subscribeInvalidMessage();
    }


    public RecyclingRuleCache getCache(String tenantId) {
        try {
            return cache.get(tenantId);
        } catch (ExecutionException e) {
            // ExecutionException wraps the actual exception thrown by the CacheLoader's load method
            log.warn("Failed to load rule from cache for tenantId: {}. Propagating as RuntimeException.", tenantId, e.getCause());
            throw new RuntimeException("Failed to load rule for tenantId: " + tenantId, e.getCause());
        }
    }

    public boolean isSkipWithoutRule(RecalculateMessage message) {
        if (ActionCodeEnum.CHANGE_RULE.getActionCode().equals(message.getActionCode())){
            invalid(message.getTenantId()); // Invalidate cache first if rule changed
        }
        
        RecyclingRuleCache ruleState;
        try {
            ruleState = getCache(message.getTenantId());
        } catch (Exception e) { 
            // This means loading the rule state failed (e.g., recyclingBiz.getRecyclingRuleExistes threw an exception).
            // To prevent incorrectly skipping when a rule might exist but couldn't be verified,
            // we default to NOT skipping.
            log.warn("Failed to get recycling rule status for tenantId: {} from cache (load attempt failed). " +
                     "Proceeding as if rule might exist (not skipping).", message.getTenantId(), e);
            return false; // false means "do not skip"
        }

        // If cache lookup was successful (no exception)
        if (RecyclingRuleCache.EMPTY.equals(ruleState)) {
            // Cache says no rule exists.
            return true; // true means "skip"
        }
        
        // Cache says rule exists (NOT_EMPTY) or some other non-EMPTY state
        return false; // false means "do not skip"
    }

    public boolean isSkipWithoutRuleByTenantId(String tenantId) {
        RecyclingRuleCache ruleState;
        try {
            ruleState = getCache(tenantId);
        } catch (Exception e) {
            log.warn("Failed to get recycling rule status for tenantId: {} from cache (load attempt failed). " +
                     "Proceeding as if rule might exist (not skipping).", tenantId, e);
            return false; // Do not skip if determination fails
        }
        return RecyclingRuleCache.EMPTY.equals(ruleState);
    }


    @SneakyThrows
    public void invalid(String tenantId) {
        log.info("publish invalidate available cache, tenantId:{}", tenantId);
        cache.invalidate(tenantId);
        NotifierClient.send(JVM_CACHE_CLEAR, tenantId);
    }

    private void subscribeInvalidMessage() {
        NotifierClient.register(JVM_CACHE_CLEAR, message -> {
            String tenantId = message.getContent();
            if (StringHelper.isNotNullOrBlank(tenantId)) {
                log.info("fast-notifier: invalidate recycling rule cache, tenantId:{}", tenantId);
                cache.invalidate(tenantId);
            }
        });
    }


    @Data
    public static class RecyclingRuleCache {

        private boolean empty;
        public static final  RecyclingRuleCache EMPTY = new RecyclingRuleCache();

        public static final  RecyclingRuleCache NOT_EMPTY = new RecyclingRuleCache(true);

        public RecyclingRuleCache() {
        }

        public RecyclingRuleCache(boolean empty) {
            this.empty = empty;
        }
    }
}
