package com.facishare.crm.recycling.task.executor.biz;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.recycling.task.executor.common.RecyclingTaskGray;
import com.facishare.crm.recycling.task.executor.enums.ApiNameEnum;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.MetaDataMiscService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.facishare.crm.recycling.task.executor.util.ConstantUtils.*;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-03-29 14:52
 */

@Slf4j
@Component
public class BaseBiz {

    @Autowired
    private IObjectDataService objectDataPgService;
    @Autowired
    private MetaDataMiscService metaDataMiscService;

    @Autowired
    private RecyclingTaskGray recyclingTaskGray;

    public void clearExpireTime(IObjectData objectData) {
        List<String> updateFieldList = Lists.newArrayList();

        String apiName = objectData.getDescribeApiName();
        String tenantId = objectData.getTenantId();
        String objectId = objectData.getId();

        IObjectData data = new ObjectData();
        data.setTenantId(tenantId);
        data.setDescribeApiName(apiName);
        data.setId(objectId);
        if (StringUtils.isNotBlank(objectData.get(EXPIRE_TIME, String.class))) {
            data.set(EXPIRE_TIME, "");
            updateFieldList.add(EXPIRE_TIME);
        }
        if (objectData.get(REMIND_DAYS, Integer.class) != null ) {
            data.set(REMIND_DAYS, null);
            updateFieldList.add(REMIND_DAYS);
        }
        if (recyclingTaskGray.isExpireTimeExtend(tenantId) && StringUtils.isNotBlank(objectData.get(EXTEND_DAYS, String.class))){
            data.set(EXTEND_DAYS, null);
            updateFieldList.add(EXTEND_DAYS);
        }
        if (CollectionUtils.isEmpty(updateFieldList)){
            return;
        }
        log.info("batchUpdateWithField :{},{}", data, updateFieldList);
        updateField(Lists.newArrayList(data), updateFieldList, apiName, tenantId);
    }



    /**
     * 更新到期时间和remindDays 如果值没有变化则不更新
     *
     * @param objectData
     * @param expireTime
     * @param remindDays
     */

    public void updateExpireTime(IObjectData objectData, Long expireTime, Integer remindDays) {
        boolean needUpdateExpireTime = false;
        Long currentExpireTime = objectData.get(EXPIRE_TIME, Long.class);
        Integer currentRemindDays = objectData.get(REMIND_DAYS, Integer.class);

        if (currentExpireTime == null || (currentRemindDays == null && remindDays != null)
                || !currentExpireTime.equals(expireTime)
                || ( remindDays != null && !remindDays.equals(currentRemindDays))) {
            needUpdateExpireTime = true;
        }
        if (needUpdateExpireTime || Boolean.TRUE.equals(recyclingTaskGray.expireTimeSkipHolidays(objectData.getTenantId()))) {
            updateExpireTime(objectData.getDescribeApiName(), objectData.getTenantId(), objectData.getId(),
                    String.valueOf(expireTime), remindDays);
        } else {
            log.info("don't needUpdateExpireTime:{},{},{}", objectData.getId(), objectData.getTenantId(), objectData.getDescribeApiName());
        }
    }

    /**
     * 更新到期时间
     *
     * @param apiName
     * @param tenantId
     * @param objectId
     * @param expireTime
     */
    public void updateExpireTime(String apiName, String tenantId, String objectId, String expireTime, Integer remindDays) {
        List<String> updateFieldList = Lists.newArrayList();

        IObjectData data = new ObjectData();
        data.setTenantId(tenantId);
        data.setDescribeApiName(apiName);
        data.setId(objectId);
        data.set(EXPIRE_TIME, expireTime);
        updateFieldList.add(EXPIRE_TIME);

        data.set("change_type", "(((coalesce(change_type,0) >> 6) # 3) << 6) + 0");
        data.set(REMIND_DAYS, remindDays);
        updateFieldList.add(REMIND_DAYS);

        log.info("updateExpireTime :{},{}", data, updateFieldList);
        updateField(Lists.newArrayList(data), updateFieldList, apiName, tenantId);
    }



    /**
     * 不涉及权限，只是时间和状态的更新不更新version
     *
     * @param dataLists
     * @param updateFieldList
     * @param apiName
     * @param tenantId
     */
    public void updateField(List<IObjectData> dataLists, List<String> updateFieldList, String apiName, String tenantId) {

        try {
            if (ApiNameEnum.ACCOUNT_OBJ.getApiName().equals(apiName)) {
                objectDataPgService.batchUpdateIgnoreOther(dataLists, updateFieldList, getActionContextNotVersion(buildUserForUpdate(tenantId)));
            } else if (ApiNameEnum.LEADS_OBJ.getApiName().equals(apiName)) {
                objectDataPgService.batchUpdateIgnoreOther(dataLists, updateFieldList, getActionContextNotVersion(buildUserForUpdate(tenantId)));
            }
        } catch (MetadataServiceException e) {
            log.error("{},batchUpdateIgnoreOther error", dataLists, e);
            throw new RuntimeException();
        }
    }

    public void updateFieldForRecycling(List<IObjectData> dataLists, List<String> updateFieldList, String apiName, String tenantId) {

        try {
            if (ApiNameEnum.ACCOUNT_OBJ.getApiName().equals(apiName)) {
                objectDataPgService.batchUpdateIgnoreOther(dataLists, updateFieldList, getDefaultActionContext(buildUserForUpdate(tenantId)));
            } else if (ApiNameEnum.LEADS_OBJ.getApiName().equals(apiName)) {
                objectDataPgService.batchUpdateIgnoreOther(dataLists, updateFieldList, getDefaultActionContextNotValidate(buildUserForUpdate(tenantId)));
            }
        } catch (MetadataServiceException e) {
            log.error("{},updateFieldForRecycling", dataLists, e);
            throw new RuntimeException();
        }
    }


    public User buildUser(String tenantId) {
        return buildUser(tenantId, "-10000", "", "");
    }

    public User buildUserForUpdate(String tenantId) {
        return buildUser(tenantId, null, "", "");
    }

    public User buildUser(String tenantId, String userId) {
        return buildUser(tenantId, userId, "", "");
    }

    protected User buildUser(String tenantId, String userId, String outUserId, String outTenantId) {
        return new User(tenantId, userId, outUserId, outTenantId);
    }

    protected ActionContext getActionContextNotVersion(User user) {
        ActionContext actionContext = getDefaultActionContextNotValidate(user);
        actionContext.put("skip_version_change", true);
        actionContext.put("specify_time", true);//不更新最后修改时间
        return actionContext;
    }

    protected ActionContext getDefaultActionContextNotValidate(User user) {
        ActionContext actionContext = new ActionContext();
        actionContext.setEnterpriseId(user.getTenantId());
        actionContext.setUserId(user.getUserId());
        actionContext.setDbType("pg");
        actionContext.setPrivilegeCheck(false);
        actionContext.put("not_validate", true);
        actionContext.put("batch", true);
        return actionContext;
    }

    protected ActionContext getDefaultActionContext(User user) {
        ActionContext actionContext = new ActionContext();
        actionContext.setEnterpriseId(user.getTenantId());
        actionContext.setUserId(user.getUserId());
        actionContext.setDbType("pg");
        actionContext.setPrivilegeCheck(false);
        actionContext.put("specify_time", true);//不更新最后修改时间
        actionContext.put("batch", true);// 批量，防止工作流程阻塞
        return actionContext;
    }


    public String getOwnerName(String tenantId, IObjectData objectData, IObjectDescribe objectDescribe) {
        try {
            metaDataMiscService.fillUserInfo(objectDescribe, Lists.newArrayList(objectData), buildUser(tenantId));
            return JSONObject.parseObject(objectData.get("owner__r", String.class)).get("name").toString();
        } catch (Exception e) {
            log.info("AccountRecycling exception customer:{}", objectData);
            throw new RuntimeException(e);
        }
    }


}
