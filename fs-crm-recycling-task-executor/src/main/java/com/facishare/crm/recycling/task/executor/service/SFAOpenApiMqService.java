package com.facishare.crm.recycling.task.executor.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.appframework.common.mq.AppDefaultRocketMQProducer;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.support.GDSHandler;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-07-04 14:29
 */
@Slf4j
@Component
public class SFAOpenApiMqService {

    @Autowired
    private AppDefaultRocketMQProducer mqMessageProducer;
    @Autowired
    private GDSHandler gdsHandler;

    public void sendOpenApiMq(User user, String actionCode, String objectApiName, String objectId, Object content){
        mqMessageProducer.sendMessage(toMessageData(user, actionCode, objectApiName,objectId, content));
    }

    private byte[] toMessageData(User user, String actionCode, String objectApiName, String objectId, Object actionContent) {
        Map<String, Object> template = Maps.newHashMap();
        template.put("TenantID", user.getTenantId());
        template.put("TenantAccount", gdsHandler.getEAByEI(user.getTenantId()));
        template.put("AppID", "CRM");
        template.put("Package", "CRM");
        template.put("ObjectApiName", objectApiName);
        template.put("ObjectID", objectId);
        template.put("ActionCode", actionCode);
        template.put("ActionContent", actionContent == null ? "" : JSONObject.parseObject(JSON.toJSONString(actionContent)));
        template.put("OperatorID", Integer.parseInt(user.getUserId()));
        template.put("ActionTime", System.currentTimeMillis());
        template.put("Source", "CRM");

        return JSONObject.toJSONString(template).getBytes(StandardCharsets.UTF_8);
    }

}
