package com.facishare.crm.recycling.task.executor.model;

import com.facishare.paas.metadata.api.IObjectData;
import lombok.*;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
public class RecyclingMessage {

    private String tenantId;

    private String objectApiName;

    private String objectId;

    /**
     * 转移目标， 公海id
     *          线索池id
     */
    private String targetId;


    /**
     * 客户/线索 对象数据
     */
    private IObjectData objectData;

    /**
     * 函数apiname
     */
    private String functionApiName;


    /**
     * 回收原因类型的定义
     * 具体内容参考 {@link RecyclingReasonEnum}
     */
    private Integer recyclingReasonType;

    /**
     * 回收设置的天数
     */
    private Integer recyclingDays;
}
