package com.facishare.crm.recycling.task.executor.consumer;

import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;


/**
 * @Description
 * <AUTHOR>
 * @Date 2021/3/26 11:00
 */

@Slf4j
@Component
public class NewRecalculateVIPListener extends BaseRecalculateListener implements ApplicationListener<ContextRefreshedEvent> {

    private AutoConfMQPushConsumer consumer;

    @PostConstruct
    public void init() {
        consumer = getConsumer(SECTION_NAME_VIP);
    }


    @PreDestroy
    public void close() {
        consumer.close();
    }

    @Override
    String getConsumerName() {
        return SECTION_NAME_VIP;
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (consumer != null && event.getApplicationContext().getParent() == null) {
            consumer.start();
        }
    }
}
