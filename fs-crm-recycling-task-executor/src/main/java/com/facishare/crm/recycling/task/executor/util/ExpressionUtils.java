package com.facishare.crm.recycling.task.executor.util;

import com.facishare.crm.recycling.task.executor.enums.CompareTypeEnum;
import com.facishare.crm.recycling.task.executor.enums.FieldTypeEnums;
import com.facishare.crm.recycling.task.executor.model.RecyclingFilter;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 表达式工具类
 * <AUTHOR>
 * @Date 2019-03-01 17:12
 */

@Component
@Slf4j
public class ExpressionUtils {

    @Autowired
    private IObjectDescribeService objectDescribeService;

    public String getExpression(List<RecyclingFilter> recyclingFilters, IObjectDescribe objectDescribe) {


        List<String> expressionList = new ArrayList<>();
        recyclingFilters.stream().forEach(
                recyclingFilter -> {
                    expressionList.add(getSingelFilterExpression(recyclingFilter, objectDescribe));
                }
        );
        return String.join(" && ", expressionList);
    }


    private String getSingelFilterExpression(RecyclingFilter recyclingFilter, IObjectDescribe objectDescribe) {


        List<IFieldDescribe> collect = objectDescribe.getFieldDescribes().stream().filter(
                x -> x.getApiName().equals(recyclingFilter.getFieldName())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(collect)) {
            return "false ";
        }
        String type = collect.get(0).getType();
        switch (FieldTypeEnums.valueEnumOf(type)) {
            case CURRENCY:
            case NUMBER:
                return getNumberExpression(recyclingFilter);
            case DATE:
            case DATE_TIME:
                return getDateExpression(recyclingFilter);
            case RECORD_TYPE:
            case SELECT_ONE:
                return getSelectOneExpression(recyclingFilter);
            case SELECT_MANY:
                return getSelectMany(recyclingFilter);
            case QUOTE:
                formatQuote(recyclingFilter, objectDescribe, collect);
            default:
                return getTextExpression(recyclingFilter);
        }
    }

    // 多选
    private static String getSelectMany(RecyclingFilter recyclingFilter) {
        int operator = recyclingFilter.getCompare();
        String rst;
        switch (Objects.requireNonNull(CompareTypeEnum.valueEnumOf(String.valueOf(operator)))) {
            case EQ:
                StringBuilder sb = new StringBuilder("(" + recyclingFilter.getFieldName() + " == [");
                String[] split = recyclingFilter.getFieldValue().split("\\|");
                Arrays.sort(split);
                if (split.length > 0) {
                    sb.append("'" + split[0] + "'");
                }
                for (int i = 1; i < split.length; i++) {
                    sb.append(",'" + split[i] + "'");
                }
                sb.append("])");
                rst = sb.toString();
                break;
            case NEQ:
                sb = new StringBuilder("(" + recyclingFilter.getFieldName() + " != [");
                split = recyclingFilter.getFieldValue().split("\\|");
                Arrays.sort(split);
                if (split.length > 0) {
                    sb.append("'" + split[0] + "'");
                }
                for (int i = 1; i < split.length; i++) {
                    sb.append(",'" + split[i] + "'");
                }
                sb.append("])");
                rst = sb.toString();
                break;
            case BELONGTO:
                sb = new StringBuilder("INCLUDES(" + recyclingFilter.getFieldName() + " ");
                split = recyclingFilter.getFieldValue().split("\\|");
                Arrays.sort(split);
                for (int i = 0; i < split.length; i++) {
                    sb.append(",'" + split[i] + "'");
                }
                sb.append(")");
                rst = sb.toString();
                break;
            case NOTBELONGTO:
                sb = new StringBuilder("!INCLUDES(" + recyclingFilter.getFieldName() + " ");
                split = recyclingFilter.getFieldValue().split("\\|");
                Arrays.sort(split);
                for (int i = 0; i < split.length; i++) {
                    sb.append(",'" + split[i] + "'");
                }
                sb.append(")");
                rst = sb.toString();
                break;
            case ISNULL:
                rst = String.format(" (%s == null) ", recyclingFilter.getFieldName());
                break;
            case NOTNULL:
                rst = String.format(" (%s != null) ", recyclingFilter.getFieldName());
                break;
            default:
                rst = "";
                break;
        }
        return rst;

    }

    // 单选
    //
    private static String getSelectOneExpression(RecyclingFilter recyclingFilter) {
        int operator = recyclingFilter.getCompare();
        String rst;
        switch (Objects.requireNonNull(CompareTypeEnum.valueEnumOf(String.valueOf(operator)))) {
            case EQ:
            case IN:
                StringBuilder sb = new StringBuilder("ISSelectOptionVAL(" + recyclingFilter.getFieldName());
                String[] split = recyclingFilter.getFieldValue().split("\\|");
                for (int i = 0; i < split.length; i++) {
                    sb.append(",'" + split[i] + "'");
                }
                sb.append(")");
                rst = sb.toString();
                break;
            case NEQ:
            case NIN:
                sb = new StringBuilder("!ISSelectOptionVAL(" + recyclingFilter.getFieldName());
                split = recyclingFilter.getFieldValue().split("\\|");
                for (int i = 0; i < split.length; i++) {
                    sb.append(",'" + split[i] + "'");
                }
                sb.append(")");
                rst = sb.toString();
                break;
            case ISNULL:
                rst = String.format(" (%s == null) ", recyclingFilter.getFieldName());
                break;
            case NOTNULL:
                rst = String.format(" (%s != null) ", recyclingFilter.getFieldName());
                break;
            default:
                rst = "";
                break;
        }
        return rst;
    }

    // 日期
    // 等于、早于、晚于、自定义
    private static String getDateExpression(RecyclingFilter recyclingFilter) {

        long time = 0L;

        String fieldValue = recyclingFilter.getFieldValue();
        if ("2019-02-01".equals(fieldValue)){
            fieldValue = "1548950400000";
        }
        int operator = recyclingFilter.getCompare();

        if (operator != 19){
            try {
                time = Long.parseLong(fieldValue);
            } catch (NumberFormatException e) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                try {
                    fieldValue = sdf.parse(fieldValue).getTime()+"";
                } catch (ParseException ex) {
                    log.error("",ex);
                }
            }
        }

        String rst;
        switch (Objects.requireNonNull(CompareTypeEnum.valueEnumOf(String.valueOf(operator)))) {
            case EQ:
                time = Long.parseLong(fieldValue);
                rst = String.format(" (%s >= %d)  && (%s <= %d)", recyclingFilter.getFieldName(),
                        time, recyclingFilter.getFieldName(), time + 24 * 60 * 60 * 1000 - 1);
                break;
            case NEQ:
                time = Long.parseLong(fieldValue);
                rst = String.format(" (%s < %d)  ||  (%s > %d)", recyclingFilter.getFieldName(),
                        time, recyclingFilter.getFieldName(), time + 24 * 60 * 60 * 1000 - 1);
                break;
            case EARLY:
            case LT:
                time = Long.parseLong(fieldValue);
                rst = String.format(" (%s < %d)  ", recyclingFilter.getFieldName(), time);
                break;
            case LATER: // 晚于当天最后一毫秒
            case GT:
                rst = String.format(" (%s > %d)  ", recyclingFilter.getFieldName(), time + 24 * 60 * 60 * 1000 - 1);
                break;
            case CUSTOM:
                String[] split = fieldValue.split("\\|");
                rst = String.format(" (%s >= %d)  && (%s <= %d)", recyclingFilter.getFieldName(),
                        Long.parseLong(split[0]), recyclingFilter.getFieldName(), Long.parseLong(split[1]) + 24 * 60 * 60 * 1000 - 1);
                break;
            default:
                rst = "";
                break;
        }
        return rst;
    }

    // 金额、整数
    // 等于、不等于、小于、大于、大于等于、小于等于
    private static String getNumberExpression(RecyclingFilter recyclingFilter) {
        int operator = recyclingFilter.getCompare();
        String rst;
        switch (Objects.requireNonNull(CompareTypeEnum.valueEnumOf(String.valueOf(operator)))) {
            case EQ:
                rst = String.format(" (%s ==  ", recyclingFilter.getFieldName());
                break;
            case NEQ:
                rst = String.format(" NOT(%s == ", recyclingFilter.getFieldName());
                break;
            case LT:
                rst = String.format(" (%s <  ", recyclingFilter.getFieldName());
                break;
            case LET:
                rst = String.format(" (%s <= ", recyclingFilter.getFieldName());
                break;
            case GT:
                rst = String.format(" (%s > ", recyclingFilter.getFieldName());
                break;
            case GET:
                rst = String.format(" (%s >=  ", recyclingFilter.getFieldName());
                break;
            default:
                rst = "";
                break;
        }
        rst += Double.valueOf(recyclingFilter.getFieldValue());
        rst += " ) ";
        return rst;
    }

    // 等于、不等于、包含、不包含、为空、不为空
    private static String getTextExpression(RecyclingFilter recyclingFilter) {
        int operator = recyclingFilter.getCompare();
        String rst;
        String filedName = recyclingFilter.getFieldName();
        String fieldValue = recyclingFilter.getFieldValue();

        if(StringUtils.isNotBlank(fieldValue) && fieldValue.contains("'")){
            fieldValue = fieldValue.replaceAll("\'","\\\\'");
        }
        switch (Objects.requireNonNull(CompareTypeEnum.valueEnumOf(String.valueOf(operator)))) {
            case EQ:
                rst = String.format(" EQUALS(%s,'%s') ", filedName, fieldValue);
                break;
            case NEQ:
                rst = String.format(" !EQUALS(%s,'%s') ", filedName, fieldValue);
                break;
            case IN:
                rst = String.format(" CONTAINS(%s,'%s')", filedName, fieldValue);
                break;
            case NIN:
                rst = String.format(" !CONTAINS(%s,'%s') ", filedName, fieldValue);
                break;
            case ISNULL:
                rst = String.format(" (%s == null) ", filedName);
                break;
            case NOTNULL:
                rst = String.format(" (%s != null) ", filedName);
                break;
            default:
                rst = "";
                break;
        }
        return rst;
    }


    // todo 解析引用类型字段
    private void formatQuote(RecyclingFilter recyclingFilter, IObjectDescribe objectDescribe, List<IFieldDescribe> collect) {

        String quoteField1 = collect.get(0).get("quote_field").toString().split("__r.")[0];
        String quoteField2 = collect.get(0).get("quote_field").toString().split("__r.")[1];
        List<IFieldDescribe> fieldDescribes1 = objectDescribe.getFieldDescribes().stream().filter(x -> x.getApiName().equals(quoteField1)).collect(Collectors.toList());
        List<IFieldDescribe> fieldDescribes2 = objectDescribe.getFieldDescribes().stream().filter(x -> x.getApiName().equals(quoteField2)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(fieldDescribes1) && CollectionUtils.isNotEmpty(fieldDescribes2)) {

            if (fieldDescribes1.get(0).get("target_api_name").toString().equals(objectDescribe.getApiName())) {
                if (FieldTypeEnums.SELECT_ONE.type.equals(fieldDescribes2.get(0).getType())) {
                    List<ISelectOption> selectOptions = ((SelectOneFieldDescribe) fieldDescribes2.get(0)).getSelectOptions();
                    Optional<ISelectOption> first = selectOptions.stream().filter(x -> x.getLabel().equals(recyclingFilter.getFieldValue())).findFirst();
                    recyclingFilter.setFieldValue(first.get().getValue());
                }
            }
        }
    }
}
