package com.facishare.crm.recycling.task.executor.biz;

import com.facishare.crm.recycling.task.executor.model.CancelInstanceModel;
import com.facishare.crm.recycling.task.executor.model.GetCurInstanceStateModel;
import com.facishare.crm.recycling.task.executor.proxy.ApprovalInitProxy;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-03-09 11:54
 */
@Component
@Slf4j
public class ApprovalRemoteBiz {


    @Autowired
    private ApprovalInitProxy approvalInitProxy;


    public List<GetCurInstanceStateModel.IntanceStatus> getCurInstanceStateByObjectIds(String tenantId, String objectId) {
        return getCurInstanceStateByObjectIds(tenantId,objectId,"-10000");
    }


    public List<GetCurInstanceStateModel.IntanceStatus> getCurInstanceStateByObjectIds(String tenantId, String objectId, String userId) {
        GetCurInstanceStateModel.Arg arg = new GetCurInstanceStateModel.Arg();
        arg.setObjectIds(Lists.newArrayList(objectId));
        GetCurInstanceStateModel.Result result = approvalInitProxy.getCurInstanceStateByObjectIds(arg, getApprovalInitHeaders(tenantId, userId));
        log.debug("result:{}", result);
        if (CollectionUtils.notEmpty(result.getData())) {
            return result.getData();
        }
        return null;
    }


    private Map<String, String> getApprovalInitHeaders(String tenantId, String fsUserId) {
        Map<String, String> headers = Maps.newHashMap();
        headers.put("x-user-id", fsUserId);
        headers.put("x-tenant-id", tenantId);
        return headers;
    }


    public void cancelInstance(String tenantId, String objectId,String entityId){
        CancelInstanceModel.Arg arg = new CancelInstanceModel.Arg();
        arg.setObjectId(objectId);
        arg.setEntityId(entityId);
        arg.setCancelReason("自动收回撤回");
        approvalInitProxy.cancelInstance(arg,getApprovalInitHeaders(tenantId,"-10000"));
    }

}
