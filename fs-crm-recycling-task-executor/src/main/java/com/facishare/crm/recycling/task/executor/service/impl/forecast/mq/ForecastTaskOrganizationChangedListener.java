package com.facishare.crm.recycling.task.executor.service.impl.forecast.mq;

import com.facishare.crm.recycling.task.executor.service.ForecastService;
import com.facishare.organization.api.event.OrganizationChangedListener;
import com.facishare.organization.api.event.organizationChangeEvent.EmployeeChangeEvent;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.type.EmployeeEntityStatus;

//@Component
public class ForecastTaskOrganizationChangedListener extends OrganizationChangedListener {

    private final ForecastService forecastService;

    public ForecastTaskOrganizationChangedListener(ForecastService forecastService) {
        super("fs-crm-forecast-task-org-changed");
        this.forecastService = forecastService;
    }

    @Override
    protected void onEmployeeChanged(EmployeeChangeEvent event) {
        EmployeeDto newEmployeeDto = event.getNewEmployeeDto();
        EmployeeDto oldEmployeeDto = event.getOldEmployeeDto();
        if (isStopEmployee(newEmployeeDto, oldEmployeeDto) || isDeleteEmployee(newEmployeeDto)) {
            forecastService.deleteTaskByOwner(String.valueOf(newEmployeeDto.getEnterpriseId()), String.valueOf(newEmployeeDto.getEmployeeId()));
        }
    }

    private boolean isStopEmployee(EmployeeDto newEmployee, EmployeeDto oldEmployee) {
        return newEmployee != null && oldEmployee != null && newEmployee.getStatus() == EmployeeEntityStatus.STOP && oldEmployee.getStatus() == EmployeeEntityStatus.NORMAL;
    }

    private boolean isDeleteEmployee(EmployeeDto newEmployee) {
        return newEmployee != null && newEmployee.getStatus() == EmployeeEntityStatus.DELETE;
    }

}
