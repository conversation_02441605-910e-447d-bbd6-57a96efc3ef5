package com.facishare.crm.recycling.task.executor.service.impl;

import com.facishare.crm.recycling.task.executor.biz.ApprovalRemoteBiz;
import com.facishare.crm.recycling.task.executor.biz.CustomerBiz;
import com.facishare.crm.recycling.task.executor.enums.ApiNameEnum;
import com.facishare.crm.recycling.task.executor.model.GetCurInstanceStateModel;
import com.facishare.crm.recycling.task.executor.model.RecyclingMessage;
import com.facishare.crm.recycling.task.executor.service.RecyclingService;
import com.facishare.crm.recycling.task.executor.util.ObjectDataUtils;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.flow.ApprovalFlowStatus;
import com.facishare.paas.appframework.function.FunctionLogicService;
import com.facishare.paas.appframework.function.dto.RunResult;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefFunction;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.facishare.crm.recycling.task.executor.util.ConstantUtils.*;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-02-18 22:04
 */
@Slf4j
public abstract class AbstractRecycling implements RecyclingService {

    @Autowired
    private CustomerBiz customerBiz;

    @Autowired
    private ApprovalRemoteBiz approvalRemoteBiz;

    @Autowired
    private ObjectDataServiceImpl objectDataService;

    @Autowired
    protected ServiceFacade serviceFacade;

    @Autowired
    private FunctionLogicService functionLogicService;





    @Override
    public void execute(RecyclingMessage message) {
        log.info("AbstractRecycling message:{}",message);
        IObjectData objectData;
        if (ApiNameEnum.ACCOUNT_OBJ.getApiName().equals(message.getObjectApiName())) {
            objectData = customerBiz.getObjectById(message.getTenantId(), message.getObjectId(), ApiNameEnum.ACCOUNT_OBJ.getApiName());
        } else if (ApiNameEnum.LEADS_OBJ.getApiName().equals(message.getObjectApiName())) {
            objectData = customerBiz.getObjectById(message.getTenantId(), message.getObjectId(), ApiNameEnum.LEADS_OBJ.getApiName());
            if (objectData != null) {
                log.info("recycling leadsData name:{}", objectData.getName());
                message.setObjectData(objectData);
                if (objectData.get(LEADS_POOL_ID) == null || StringUtils.isBlank(objectData.get(LEADS_POOL_ID).toString())) {
                    log.info("recycling leads_pool_id is null :{}", objectData.getId());
                    return;
                }
            }
        }else {
            return;
        }

        if (objectData == null){
            return;
        }
        message.setObjectData(objectData);
    }


    public Integer getPoolCustomerCount(String tenantId, String poolId,String apiName) {

        String querySql;
        Integer totalCount = 0;
        if (ACCOUNT_OBJ.equals(apiName)){
            querySql = "SELECT COUNT(1) AS totalcount FROM biz_account WHERE tenant_id='" + tenantId +
                    "' AND is_deleted=0 AND high_seas_id = '"+poolId + "'";
        }else if (LEADS_OBJ.equals(apiName)){
            querySql = "SELECT COUNT(1) AS totalcount FROM biz_leads WHERE tenant_id='" + tenantId +
                    "' AND is_deleted=0 AND leads_pool_id = '"+poolId + "'";
        }else {
            return totalCount;
        }

        List<Map> queryResult = null;
        try {
            queryResult = objectDataService.findBySql(tenantId, querySql);
        } catch (MetadataServiceException e) {
            log.error("findBySql:{}",querySql,e);
        }
        if (CollectionUtils.notEmpty(queryResult)){
            totalCount = Integer.parseInt(queryResult.get(0).get("totalcount").toString());
        }

        return totalCount;
    }

    public void executeFunction(String tenantId,String apiName,String functionApiName,IObjectData objectData){
        if (StringUtils.isEmpty(functionApiName)){
            return;
        }
        User user = ObjectDataUtils.buildUser(tenantId);
        IUdefFunction function = serviceFacade.getFunctionLogicService().findUDefFunction(user, functionApiName, apiName);
        if (function == null) {
            log.warn("leads_duplicated_processing function is null");
            return;
        }
        if (!function.isActive()) {
            log.warn("leads_duplicated_processing function is not active");
            return;
        }

        if (!"recovery".equals(function.getNameSpace())) {
            log.warn("leads_duplicated_processing function name space is not button");
            return;
        }

        List<String> fieldNames = function.getParameters();
        Map<String, Object> paramMap = Maps.newHashMap();
        for (String fieldName : fieldNames) {
            paramMap.put(fieldName, objectData.get(fieldName));
        }
        log.info("executeUDefFunction:tenantId:{},paramMap:{},objectId:{},functionApiName:{}",tenantId,paramMap,objectData.getId(),functionApiName);
        RunResult runResult = serviceFacade.getFunctionLogicService().executeUDefFunction(user, function, paramMap, objectData, Maps.newHashMap());
        log.info("executeUDefFunction:result:{}",runResult);
    }

    protected void cancelInstance(RecyclingMessage message){
        List<GetCurInstanceStateModel.IntanceStatus> statuses = approvalRemoteBiz.getCurInstanceStateByObjectIds(message.getTenantId(), message.getObjectId());
        if (statuses == null) {
            log.info("没有可以取消的流程,tenantId:{},objectId:{}", message.getTenantId(), message.getObjectId());
            return;
        }
        List<GetCurInstanceStateModel.IntanceStatus> collect = statuses.stream().filter(x -> ApprovalFlowStatus.isInProgress(ApprovalFlowStatus.of(x.getStatus()))).collect(Collectors.toList());
        if (CollectionUtils.notEmpty(collect)) {
            // 取消流程
            approvalRemoteBiz.cancelInstance(message.getTenantId(), message.getObjectId(), message.getObjectApiName());
        }
    }
}
