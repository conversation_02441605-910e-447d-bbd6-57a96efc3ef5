package com.facishare.crm.recycling.task.executor.util;

public interface HighSeasConstants {
    class Field {
        public static final String NAME = "name";
        public static final String CLAIM_LIMIT_NUM = "claim_limit_num";
        public static final String CLAIM_INTERVAL_DAYS = "claim_interval_days";
        public static final String IS_VISIBLE_TO_MEMBER = "is_visible_to_Member";
        public static final String ALLOW_MEMBER_RELATION = "allow_member_relation";
        public static final String ALLOW_MEMBER_VIEW_FEED = "allow_member_view_feed";
        public static final String ALLOW_MEMBER_SEND_FEED = "allow_member_send_feed";
        public static final String IS_CLAIM_LIMIT_INCLUDE_DEALED_CUSTOMERS = "is_claim_limit_include_dealed_customers";
        public static final String IS_RECYCLING_TEAM_MEMBER = "is_recycling_team_member";
        public static final String ALLOW_MEMBER_MOVE = "allow_member_move";
        public static final String IS_CLEAN_OWNER = "is_clean_owner";
        public static final String ALLOW_MEMBER_VIEW_LOG = "allow_member_view_log";
        public static final String POOL_TYPE = "pool_type";
        public static final String PARTNER_ID = "partner_id";
        public static final String LIMIT_TYPE = "limit_type";
        public static final String ACCOUNTS_COUNT = "accounts_count";
    }
    class TabelName{
        public static final String BIZ_POOL_PERMISSION = "biz_pool_permission";
        public static final String BIZ_REMIND_RULE = "biz_remind_rule";
        public static final String BIZ_RECYCLING_RULE = "biz_recycling_rule";
        public static final String BIZ_RECYCLING_REMIND_RULE = "biz_recycling_remind_rule";
        public static final String FIELD_PERMISSION_TEMPLATE = "biz_pool_permission_field";
        public static final String BIZ_REMIND_RULE_LOG = "biz_remind_rule_log";
        public static final String ALLOCATE_RULE = "biz_pool_allocate_rule";
        public static final String ALLOCATE_RULE_MEMBER = "biz_pool_allocate_rule_member";
    }
}
