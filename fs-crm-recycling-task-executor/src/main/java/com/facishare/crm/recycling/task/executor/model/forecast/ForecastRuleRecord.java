package com.facishare.crm.recycling.task.executor.model.forecast;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.recycling.task.executor.service.impl.forecast.calculate.ForecastModelCalculator;
import com.facishare.paas.metadata.api.IObjectData;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class ForecastRuleRecord extends BaseForecastRecord implements ForecastRule {
    private static final long serialVersionUID = 1;
    @JsonIgnore
    @JSONField(serialize = false)
    private transient String forecastObjectAmountApiNameTarget;
    @JsonIgnore
    @JSONField(serialize = false)
    private transient Map<String, String> forecastApplyRangeTarget;
    @JsonIgnore
    @JSONField(serialize = false)
    private transient Set<String> bestPracticesForecastModel1Target;
    @JsonIgnore
    @JSONField(serialize = false)
    private transient Set<String> bestPracticesForecastModel2Target;
    @JsonIgnore
    @JSONField(serialize = false)
    private transient Set<String> bestPracticesForecastModel3Target;
    @JsonIgnore
    @JSONField(serialize = false)
    private transient Set<String> bestPracticesForecastModel4Target;
    @JsonIgnore
    @JSONField(serialize = false)
    private transient Set<String> bestPracticesForecastModel5Target;
    @JsonIgnore
    @JSONField(serialize = false)
    private transient List<ForecastPeriod> forecastPeriods;
    @JsonIgnore
    @JSONField(serialize = false)
    private transient ForecastModelCalculator[] forecastModelCalculators;
    @JsonIgnore
    @JSONField(serialize = false)
    private transient String forecastObjectSearchSource;

    public ForecastRuleRecord() {

    }

    public ForecastRuleRecord(Map map) {
        super(map);
    }

    public ForecastRuleRecord(IObjectData objectData) {
        super(objectData);
    }

    @Override
    public String getDescribeApiName() {
        String describeApiName = super.getDescribeApiName();
        return describeApiName == null ? FORECAST_RULE_OBJECT_DESCRIBE_API_NAME : describeApiName;
    }

    @Override
    public String getBizStatus() {
        return get(BIZ_STATUS, String.class);
    }

    @Override
    public void setBizStatus(String bizStatus) {
        set(BIZ_STATUS, bizStatus);
    }

    @Override
    public String getForecastObjectApiName() {
        return get(FORECAST_OBJECT_API_NAME, String.class);
    }

    @Override
    public void setForecastObjectApiName(String forecastObjectApiName) {
        set(FORECAST_OBJECT_API_NAME, forecastObjectApiName);
    }

    @Override
    public String getForecastObjectDateApiName() {
        return get(FORECAST_OBJECT_DATE_API_NAME, String.class);
    }

    @Override
    public String getForecastObjectAmountApiName() {
        return get(FORECAST_OBJECT_AMOUNT_API_NAME, String.class);
    }

    @Override
    public Long getForecastStartDate() {
        return get(FORECAST_START_DATE, Long.class);
    }

    @Override
    public Long getForecastEndDate() {
        return get(FORECAST_END_DATE, Long.class);
    }

    @Override
    public Integer getForecastDateSplitType() {
        return get(FORECAST_DATE_SPLIT_TYPE, Integer.class);
    }

    @Override
    public Boolean getAutoLockAfterEnd() {
        return get(AUTO_LOCK_AFTER_END, Boolean.class, Boolean.FALSE);
    }

    @Override
    public Boolean getAutoRemindBeforeEnd() {
        return get(AUTO_REMIND_BEFORE_END, Boolean.class, Boolean.FALSE);
    }

    @Override
    public Integer getRemindDaysBeforeEnd() {
        return get(REMIND_DAYS_BEFORE_END, Integer.class, 7);
    }

    @Override
    public String getForecastDataCondition() {
        return get(FORECAST_DATA_CONDITION, String.class);
    }

    @Override
    public List<String> getForecastApplyRange() {
        Object raw = get(FORECAST_APPLY_RANGE);
        if (raw instanceof List) {
            @SuppressWarnings("unchecked")
            List<String> list = (List<String>) raw;
            return list;
        } else if (raw instanceof String[]) {
            return Arrays.asList((String[]) raw);
        } else if (raw instanceof String) {
            return JSON.parseArray((String) raw, String.class);
        } else {
            return Collections.emptyList();
        }
    }

    @Override
    public String getBestPracticesForecastModel1() {
        return get(BEST_PRACTICES_FORECAST_MODEL1, String.class);
    }

    @Override
    public String getBestPracticesForecastModel2() {
        return get(BEST_PRACTICES_FORECAST_MODEL2, String.class);
    }

    @Override
    public String getBestPracticesForecastModel3() {
        return get(BEST_PRACTICES_FORECAST_MODEL3, String.class);
    }

    @Override
    public String getBestPracticesForecastModel4() {
        return get(BEST_PRACTICES_FORECAST_MODEL4, String.class);
    }

    @Override
    public String getBestPracticesForecastModel5() {
        return get(BEST_PRACTICES_FORECAST_MODEL5, String.class);
    }

    @Override
    public BigDecimal getAIWeightForecastModel() {
        return get(AI_WEIGHT_FORECAST_MODEL, BigDecimal.class);
    }

    @Override
    public Boolean getArtificialCommitmentForecastModel() {
        return get(ARTIFICIAL_COMMITMENT_FORECAST_MODEL, Boolean.class);
    }

    @Override
    public Boolean getStageWeightForecastModel() {
        return get(STAGE_WEIGHT_FORECAST_MODEL, Boolean.class);
    }

    @Override
    public Boolean getArtificialWeightForecastModel() {
        return get(ARTIFICIAL_WEIGHT_FORECAST_MODEL, Boolean.class);
    }

    @Override
    public String getForecastObjectAmountApiNameTarget() {
        return forecastObjectAmountApiNameTarget;
    }

    @Override
    public void setForecastObjectAmountApiNameTarget(String forecastObjectAmountApiNameTarget) {
        this.forecastObjectAmountApiNameTarget = forecastObjectAmountApiNameTarget;
    }

    @Override
    public Map<String, String> getForecastApplyRangeTarget() {
        return forecastApplyRangeTarget;
    }

    @Override
    public void setForecastApplyRangeTarget(Map<String, String> forecastApplyRangeTarget) {
        this.forecastApplyRangeTarget = forecastApplyRangeTarget;
    }

    @Override
    public Set<String> getBestPracticesForecastModel1Target() {
        return bestPracticesForecastModel1Target;
    }

    @Override
    public void setBestPracticesForecastModel1Target(Set<String> bestPracticesForecastModel1Target) {
        this.bestPracticesForecastModel1Target = bestPracticesForecastModel1Target;
    }

    @Override
    public Set<String> getBestPracticesForecastModel2Target() {
        return bestPracticesForecastModel2Target;
    }

    @Override
    public void setBestPracticesForecastModel2Target(Set<String> bestPracticesForecastModel2Target) {
        this.bestPracticesForecastModel2Target = bestPracticesForecastModel2Target;
    }

    @Override
    public Set<String> getBestPracticesForecastModel3Target() {
        return bestPracticesForecastModel3Target;
    }

    @Override
    public void setBestPracticesForecastModel3Target(Set<String> bestPracticesForecastModel3Target) {
        this.bestPracticesForecastModel3Target = bestPracticesForecastModel3Target;
    }

    @Override
    public Set<String> getBestPracticesForecastModel4Target() {
        return bestPracticesForecastModel4Target;
    }

    @Override
    public void setBestPracticesForecastModel4Target(Set<String> bestPracticesForecastModel4Target) {
        this.bestPracticesForecastModel4Target = bestPracticesForecastModel4Target;
    }

    @Override
    public Set<String> getBestPracticesForecastModel5Target() {
        return bestPracticesForecastModel5Target;
    }

    @Override
    public void setBestPracticesForecastModel5Target(Set<String> bestPracticesForecastModel5Target) {
        this.bestPracticesForecastModel5Target = bestPracticesForecastModel5Target;
    }

    @Override
    public List<ForecastPeriod> getForecastPeriods() {
        return forecastPeriods;
    }

    @Override
    public void setForecastPeriods(List<ForecastPeriod> forecastPeriods) {
        this.forecastPeriods = forecastPeriods;
    }

    @Override
    public ForecastModelCalculator[] getForecastModelCalculators() {
        return forecastModelCalculators;
    }

    @Override
    public void setForecastModelCalculators(ForecastModelCalculator[] forecastModelCalculators) {
        this.forecastModelCalculators = forecastModelCalculators;
    }

    @Override
    public String getForecastObjectSearchSource() {
        return forecastObjectSearchSource;
    }

    @Override
    public void setForecastObjectSearchSource(String forecastObjectSearchSource) {
        this.forecastObjectSearchSource = forecastObjectSearchSource;
    }

    @Override
    public boolean equals(Object obj) {
        return super.equals(obj);
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }
}