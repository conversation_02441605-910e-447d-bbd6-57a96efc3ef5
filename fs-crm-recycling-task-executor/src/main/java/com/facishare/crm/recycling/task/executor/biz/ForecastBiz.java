package com.facishare.crm.recycling.task.executor.biz;

import com.facishare.crm.recycling.task.executor.enums.forecast.ForecastRuleEnums;
import com.facishare.crm.recycling.task.executor.model.forecast.BaseForecast;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastObject;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastRule;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastRuleRecord;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastTask;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastTaskDetail;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastTaskDetailRecord;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastTaskRecord;
import com.facishare.crm.recycling.task.executor.service.ForecastService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.action.ActionContextKey;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.IGroupByParameter;
import com.facishare.paas.metadata.api.service.ICommonSqlService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.CommonSqlOperator;
import com.facishare.paas.metadata.impl.search.CommonSqlSearchTemplate;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.GroupByParameter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.impl.search.WhereParam;
import com.fxiaoke.api.IdGenerator;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.crm.recycling.task.executor.service.impl.forecast.calculate.ForecastModelCalculator.Register.generateUpdateDetailFieldsList;
import static com.facishare.crm.recycling.task.executor.service.impl.forecast.calculate.ForecastModelCalculator.Register.generateUpdateTaskFieldsList;

@SuppressWarnings({"rawtypes", "unchecked"})
@Component
public class ForecastBiz implements InitializingBean {

    private static final int LIMIT = ForecastService.QUERY_LIMIT;
    private static final String FORECAST_RULE_TABLE_NAME = "biz_forecast_rule";
    private static final String FORECAST_TASK_TABLE_NAME = "biz_forecast_task";
    private static final String FORECAST_TASK_DETAIL_TABLE_NAME = "biz_forecast_task_detail";
    public static final String ID = BaseForecast.ID;
    private IActionContext prototypeActionContext;

    @Autowired
    private ICommonSqlService commonSqlService;
    @Autowired
    private ServiceFacade serviceFacade;

    @Override
    public void afterPropertiesSet() {
        prototypeActionContext = new ActionContext();
        prototypeActionContext.setUserId(User.SUPPER_ADMIN_USER_ID);
        prototypeActionContext.setDbType("pg");
        prototypeActionContext.setAllowUpdateInvalid(true);
        prototypeActionContext.setPrivilegeCheck(false);
        prototypeActionContext.put(ActionContextKey.NOT_VALIDATE, true);
    }

    // *改前需知*
    // 需要考虑可能没有ForecastRuleObj描述的场景
    public List<ForecastRule> queryRulesForSync(ForecastRule condition) throws MetadataServiceException {
        String tenantId = Objects.requireNonNull(condition.getTenantId());
        String forecastObjectApiName = Objects.requireNonNull(condition.getForecastObjectApiName());
        List<WhereParam> whereParams = buildBaseWhereParams(tenantId, ForecastRule.FORECAST_RULE_OBJECT_DESCRIBE_API_NAME);
        whereParams.add(buildWhereParam(ForecastRule.BIZ_STATUS, CommonSqlOperator.EQ, Collections.singletonList(ForecastRuleEnums.BizStatus.ENABLE.getValue())));
        whereParams.add(buildWhereParam(ForecastRule.FORECAST_OBJECT_API_NAME, CommonSqlOperator.EQ, Collections.singletonList(forecastObjectApiName)));
        List<OrderBy> orderByList = new ArrayList<>();
        OrderBy orderBy = new OrderBy();
        orderBy.setFieldName(DBRecord.LAST_MODIFIED_TIME);
        orderBy.setIsAsc(false);
        orderByList.add(orderBy);
        CommonSqlSearchTemplate template = new CommonSqlSearchTemplate();
        template.setWhereParamList(whereParams);
        template.setOrderByList(orderByList);
        template.setTableName(FORECAST_RULE_TABLE_NAME);
        template.setLimit(100);
        List<Map> list = commonSqlService.select(template, getActionContext(tenantId));
        return convertMapList(ForecastRuleRecord::new, list);
    }

    public List<ForecastTask> queryTasksForSync(String tenantId, Collection<String> ruleIds, String objectId) throws MetadataServiceException {
        List<WhereParam> whereParams = buildBaseWhereParams(tenantId, ForecastTaskDetail.FORECAST_TASK_DETAIL_OBJECT_DESCRIBE_API_NAME);
        whereParams.add(buildWhereParam(ForecastTaskDetail.FORECAST_RULE_OBJECT_ID, CommonSqlOperator.IN, new ArrayList<>(ruleIds)));
        whereParams.add(buildWhereParam(ForecastTaskDetail.FORECAST_OBJECT_ID, CommonSqlOperator.IN, Collections.singletonList(objectId)));
        whereParams.add(buildWhereParam(ForecastTaskDetail.ADD_MANUALLY, CommonSqlOperator.EQ, Collections.singletonList(Boolean.FALSE)));
        IActionContext actionContext = getActionContext(tenantId);
        List<Map> existDetailMapList = commonSqlService.select(FORECAST_TASK_DETAIL_TABLE_NAME, whereParams, actionContext);
        if (!existDetailMapList.isEmpty()) {
            Map<String, List<ForecastTaskDetail>> detailGroup = new HashMap<>();
            List<String> taskIds = new ArrayList<>(existDetailMapList.size());
            for (Map map : existDetailMapList) {
                ForecastTaskDetailRecord detail = new ForecastTaskDetailRecord(map);
                String taskId = detail.getForecastTaskObjectId();
                detailGroup.computeIfAbsent(taskId, k -> {
                    ArrayList list = new ArrayList(2);
                    list.add(detail);
                    return list;
                });
                taskIds.add(taskId);
            }
            whereParams = buildIdInWhereParams(tenantId, ForecastTask.FORECAST_TASK_OBJECT_DESCRIBE_API_NAME, taskIds);
            whereParams.add(buildWhereParam(BaseForecast.LOCK_STATUS, CommonSqlOperator.EQ, Collections.singletonList("0")));
            List<Map> existTaskMapList = commonSqlService.select(FORECAST_TASK_TABLE_NAME, whereParams, actionContext);
            List<ForecastTask> existTasks = new ArrayList<>(existTaskMapList.size());
            for (Map map : existTaskMapList) {
                ForecastTask task = new ForecastTaskRecord(map);
                task.setForecastTaskDetails(detailGroup.get(task.getId()));
                existTasks.add(task);
            }
            return existTasks;
        } else {
            return Collections.emptyList();
        }
    }

    public Set<Integer> queryOwnerForTaskRemind(ForecastRule rule, Long createTime, Long forecastEndDate) {
        SearchTemplateQuery searchTemplateQuery = getSearchTemplateQuery(rule, createTime, forecastEndDate);
        IGroupByParameter groupByParameter = new GroupByParameter();
        String columnName = IObjectData.OWNER;
        groupByParameter.setGroupBy(Collections.singletonList(columnName));
        searchTemplateQuery.setGroupByParameter(groupByParameter);
        List<IObjectData> list = rollingFindBySearchTemplateQueryWithFields(getActionContext(rule.getTenantId()), ForecastTask.FORECAST_TASK_OBJECT_DESCRIBE_API_NAME, searchTemplateQuery, Collections.singletonList(columnName));
        if (list.isEmpty()) {
            return Collections.emptySet();
        } else {
            return list.stream().map(data -> data.getOwner().get(0)).map(Integer::valueOf).collect(Collectors.toSet());
        }
    }

    public List<String> queryIdForTaskLock(ForecastRule rule, Long createTime, Long forecastEndDate) {
        SearchTemplateQuery searchTemplateQuery = getSearchTemplateQuery(rule, createTime, forecastEndDate);
        List<IObjectData> list = rollingFindBySearchTemplateQueryWithFields(getActionContext(rule.getTenantId()), ForecastTask.FORECAST_TASK_OBJECT_DESCRIBE_API_NAME, searchTemplateQuery, Collections.singletonList(DBRecord.ID));
        if (list.isEmpty()) {
            return Collections.emptyList();
        } else {
            return list.stream().map(IObjectData::getId).collect(Collectors.toCollection(() -> new ArrayList<>(list.size())));
        }
    }

    @Transactional
    public void upsertTaskByRule(List<ForecastTask> tasks, ForecastRule rule) {
        if (tasks.isEmpty()) {
            return;
        }
        upsertTask0(rule.getTenantId(), tasks, rule);
    }

    @Transactional
    public void upsertTask(List<ForecastTask> tasks) {
        if (tasks.isEmpty()) {
            return;
        }
        upsertTask0(tasks.get(0).getTenantId(), tasks, null);
    }

    @Transactional
    public void upsertTask(ForecastTask task) {
        upsertTask0(task.getTenantId(), Collections.singletonList(task), task.getForecastRule());
    }

    private void upsertTask0(String tenantId, List<ForecastTask> tasks, ForecastRule rule) {
        List<IObjectData> insertTasks = null;
        List<IObjectData> insertDetails = null;
        List<IObjectData> updateTasks = null;
        for (ForecastTask task : tasks) {
            if (task.getId() == null) {
                task.setId(IdGenerator.get());
                insertTasks = lazyInit(insertTasks);
                insertTasks.add(task);
            } else {
                updateTasks = lazyInit(updateTasks);
                updateTasks.add(task);
            }
            for (ForecastTaskDetail detail : task.getForecastTaskDetails()) {
                if (detail.getId() == null) {
                    detail.setId(IdGenerator.get());
                    detail.setForecastTaskObjectId(task.getId());
                    insertDetails = lazyInit(insertDetails);
                    insertDetails.add(detail);
                }
            }
        }
        if (insertTasks != null || insertDetails != null) {
            insert(tenantId, insertTasks, insertDetails);
        }
        if (updateTasks != null) {
            rollingUpdateTaskCalculateFields(getActionContext(tenantId), updateTasks, rule);
        }
    }

    private <T> List<T> lazyInit(List<T> list) {
        if (list == null) {
            list = new ArrayList<>();
        }
        return list;
    }

    private void insert(String tenantId, List<IObjectData> insertTasks, List<IObjectData> insertDetails) {
        User user = User.systemUser(tenantId);
        if (insertTasks != null) {
            rollingInsert(insertTasks, user);
        }
        if (insertDetails != null) {
            rollingInsert(insertDetails, user);
        }
    }

    private void rollingInsert(List<IObjectData> list, User user) {
        if (list.size() > LIMIT) {
            int fromIndex = 0;
            int toIndex = LIMIT;
            while (fromIndex < toIndex) {
                serviceFacade.bulkSaveObjectData(list.subList(fromIndex, toIndex), user);
                fromIndex = toIndex;
                toIndex = Math.min(toIndex + LIMIT, list.size());
            }
        } else {
            serviceFacade.bulkSaveObjectData(list, user);
        }
    }

    @Transactional
    public void updateAllCalculateFields(String tenantId, List<ForecastTask> tasks) {
        updateAllCalculateFields(tenantId, tasks, null);
    }

    // 比updateTaskCalculateFields(tenantId, tasks)需要更新的列少
    @Transactional
    public void updateAllCalculateFieldsByRule(ForecastRule rule, List<ForecastTask> tasks) {
        updateAllCalculateFields(rule.getTenantId(), tasks, rule);
    }

    private void updateAllCalculateFields(String tenantId, List<ForecastTask> tasks, ForecastRule rule) {
        List<IObjectData> allDetails = new ArrayList<>();
        List<String> taskFieldList = rule == null ? generateUpdateTaskFieldsList() : generateUpdateTaskFieldsList(rule);
        List<String> detailFieldList = rule == null ? generateUpdateDetailFieldsList() : generateUpdateDetailFieldsList(rule);
        detailFieldList.add(DBRecord.DATA_OWN_DEPARTMENT);
        detailFieldList.add(DBRecord.DATA_OWN_ORGANIZATION);
        detailFieldList.add(IObjectData.OWNER);
        detailFieldList.add(ForecastTaskDetail.FORECAST_AMOUNT);
        detailFieldList.add(ForecastTaskDetail.FORECAST_DATE);
        detailFieldList.add(ForecastTaskDetail.FORECAST_TASK_OBJECT_ID);
        for (ForecastTask task : tasks) {
            allDetails.addAll(task.getForecastTaskDetails());
        }
        IActionContext actionContext = getActionContext(tenantId);
        serviceFacade.batchUpdateByFields(actionContext, Collections.unmodifiableList(tasks), taskFieldList);
        serviceFacade.batchUpdateByFields(actionContext, allDetails, detailFieldList);
    }

    private void rollingUpdateTaskCalculateFields(IActionContext actionContext, List<IObjectData> tasks, ForecastRule rule) {
        List<String> fields = rule != null ? generateUpdateTaskFieldsList(rule) : generateUpdateTaskFieldsList();
        if (tasks.size() > LIMIT) {
            int fromIndex = 0;
            int toIndex = LIMIT;
            while (fromIndex < toIndex) {
                serviceFacade.batchUpdateByFields(actionContext, tasks.subList(fromIndex, toIndex), fields);
                fromIndex = toIndex;
                toIndex = Math.min(toIndex + LIMIT, tasks.size());
            }
        } else {
            serviceFacade.batchUpdateByFields(actionContext, tasks, fields);
        }
    }

    @Transactional
    public void deleteTaskByOwner(String tenantId, String owner) {
        List<WhereParam> whereParams = buildBaseWhereParams(tenantId, ForecastTask.FORECAST_TASK_OBJECT_DESCRIBE_API_NAME);
        whereParams.add(buildWhereParam(IObjectData.OWNER, CommonSqlOperator.EQ, Collections.singletonList(owner)));
        rollingDeleteTaskByWhereParams(tenantId, whereParams);
    }

    @Transactional
    public void deleteTaskByRuleId(String tenantId, String ruleId) {
        List<WhereParam> whereParams = buildBaseWhereParams(tenantId, ForecastTask.FORECAST_TASK_OBJECT_DESCRIBE_API_NAME);
        whereParams.add(buildWhereParam(ForecastTask.FORECAST_RULE_OBJECT_ID, CommonSqlOperator.EQ, Collections.singletonList(ruleId)));
        rollingDeleteTaskByWhereParams(tenantId, whereParams);
    }

    private void rollingDeleteTaskByWhereParams(String tenantId, List<WhereParam> whereParams) {
        rollingUpdateTaskByWhereParams(tenantId, whereParams, this::deleteTaskByTemplate);
    }

    private int deleteTaskByTemplate(CommonSqlSearchTemplate template, IActionContext actionContext) {
        try {
            List<Map> existTasks = commonSqlService.select(template, actionContext);
            int deleted = existTasks.size();
            if (!existTasks.isEmpty()) {
                List<String> existTasksIds = new ArrayList<>(existTasks.size());
                for (Map map : existTasks) {
                    existTasksIds.add(String.valueOf(map.get(ID)));
                }
                existTasks.clear(); // clear to let GC do its work before request database
                deleteTaskByIds(actionContext.getEnterpriseId(), existTasksIds);
            }
            return deleted;
        } catch (MetadataServiceException e) {
            throw new IllegalStateException(e);
        }
    }

    private void deleteTaskByIds(String tenantId, List<String> ids) throws MetadataServiceException {
        Map<String, Object> data = new HashMap<>();
        data.put(DBRecord.IS_DELETED, 1);
        IActionContext actionContext = getActionContext(tenantId);
        updateTaskByIds(actionContext, ids, data);
        List<IObjectData> list = selectTaskDetailId(tenantId, ids);
        List<String> existTaskDetailIds = list.stream().map(map -> String.valueOf(map.get(ID))).collect(Collectors.toCollection(() -> new ArrayList<>(list.size())));
        if (!existTaskDetailIds.isEmpty()) {
            updateTaskDetailByIds(actionContext, existTaskDetailIds, data);
        }
    }

    @Transactional
    public void lockTaskByIds(String tenantId, List<String> ids) {
        List<WhereParam> whereParams = buildIdInWhereParams(tenantId, ForecastTask.FORECAST_TASK_OBJECT_DESCRIBE_API_NAME, ids);
        whereParams.add(buildWhereParam(BaseForecast.LOCK_STATUS, CommonSqlOperator.EQ, Collections.singletonList("0")));
        rollingUpdateTaskByWhereParams(tenantId, whereParams, this::lockTaskByTemplate);
    }

    private int lockTaskByTemplate(CommonSqlSearchTemplate template, IActionContext actionContext) {
        try {
            List<Map> existTaskMapList = commonSqlService.select(template, actionContext);
            int locked = existTaskMapList.size();
            if (!existTaskMapList.isEmpty()) {
                List<IObjectData> dataList = new ArrayList<>(existTaskMapList.size());
                for (Map map : existTaskMapList) {
                    dataList.add(new ForecastTaskRecord(map));
                }
                bulkLockObjectData(actionContext.getEnterpriseId(), dataList);
            }
            return locked;
        } catch (MetadataServiceException e) {
            throw new IllegalStateException(e);
        }
    }

    private void bulkLockObjectData(String tenantId, List<IObjectData> dataList) {
        serviceFacade.bulkLockObjectData(dataList, true, "default_lock_rule", User.systemUser(tenantId));
    }

    private void rollingUpdateTaskByWhereParams(String tenantId, List<WhereParam> whereParams, BiFunction<CommonSqlSearchTemplate, IActionContext, Integer> updater) {
        CommonSqlSearchTemplate template = new CommonSqlSearchTemplate();
        template.setTableName(FORECAST_TASK_TABLE_NAME);
        template.setWhereParamList(whereParams);
        template.setOrderByList(Collections.singletonList(new OrderBy(ID, Boolean.TRUE)));
        template.setLimit(LIMIT);
        template.setOffset(0);
        IActionContext actionContext = getActionContext(tenantId);
        int modified = updater.apply(template, actionContext);
        while (modified == template.getLimit()) {
            template.setOffset(template.getOffset() + template.getLimit());
            modified = updater.apply(template, actionContext);
        }
    }

    public ForecastRule queryRuleById(String tenantId, String ruleId) throws MetadataServiceException {
        List<ForecastRule> rules = queryByIds(getActionContext(tenantId), ForecastRule.FORECAST_RULE_OBJECT_DESCRIBE_API_NAME, FORECAST_RULE_TABLE_NAME, Collections.singletonList(ruleId), ForecastRuleRecord::new);
        return rules.isEmpty() ? null : rules.get(0);
    }

    public List<ForecastTask> queryTaskByIds(String tenantId, List<String> taskIds) throws MetadataServiceException {
        return queryByIds(getActionContext(tenantId), ForecastTask.FORECAST_TASK_OBJECT_DESCRIBE_API_NAME, FORECAST_TASK_TABLE_NAME, taskIds, ForecastTaskRecord::new);
    }

    public List<ForecastTaskDetail> queryDetailByIds(String tenantId, List<String> detailIds) throws MetadataServiceException {
        return queryByIds(getActionContext(tenantId), ForecastTaskDetail.FORECAST_TASK_DETAIL_OBJECT_DESCRIBE_API_NAME, FORECAST_TASK_DETAIL_TABLE_NAME, detailIds, ForecastTaskDetailRecord::new);
    }

    private <T> List<T> queryByIds(IActionContext actionContext, String objectDescribeApiName, String tableName, List<String> ids, Function<Map, T> mapper) throws MetadataServiceException {
        List<Map> list = commonSqlService.select(tableName, buildIdInWhereParams(actionContext.getEnterpriseId(), objectDescribeApiName, ids), actionContext);
        return convertMapList(mapper, list);
    }

    private <T> List<T> convertMapList(Function<Map, T> mapper, List<Map> list) {
        return list.isEmpty() ? Collections.emptyList() : list.stream().map(mapper).collect(Collectors.toCollection(() -> new ArrayList<>(list.size())));
    }

    @Transactional
    public void updateRuleById(String tenantId, String id, Map<String, Object> data) throws MetadataServiceException {
        updateById(getActionContext(tenantId), ForecastRule.FORECAST_RULE_OBJECT_DESCRIBE_API_NAME, id, FORECAST_RULE_TABLE_NAME, data);
    }

    @Transactional
    public void updateTaskById(String tenantId, String id, Map<String, Object> data) throws MetadataServiceException {
        updateById(getActionContext(tenantId), ForecastTask.FORECAST_TASK_OBJECT_DESCRIBE_API_NAME, id, FORECAST_TASK_TABLE_NAME, data);
    }

    private void updateById(IActionContext actionContext, String objectDescribeApiName, String id, String tableName, Map<String, Object> data) throws MetadataServiceException {
        commonSqlService.update(tableName, data, buildIdEqualWhereParams(actionContext.getEnterpriseId(), objectDescribeApiName, id), actionContext);
    }

    private void updateTaskByIds(IActionContext actionContext, List<String> ids, Map<String, Object> data) throws MetadataServiceException {
        updateByIds(actionContext, ForecastTask.FORECAST_TASK_OBJECT_DESCRIBE_API_NAME, ids, FORECAST_TASK_TABLE_NAME, data);
    }

    @Transactional
    public void updateTaskDetailByIds(String tenantId, List<String> ids, Map<String, Object> data) throws MetadataServiceException {
        updateTaskDetailByIds(getActionContext(tenantId), ids, data);
    }

    private void updateTaskDetailByIds(IActionContext actionContext, List<String> ids, Map<String, Object> data) throws MetadataServiceException {
        updateByIds(actionContext, ForecastTaskDetail.FORECAST_TASK_DETAIL_OBJECT_DESCRIBE_API_NAME, ids, FORECAST_TASK_DETAIL_TABLE_NAME, data);
    }

    private void updateByIds(IActionContext actionContext, String objectDescribeApiName, List<String> ids, String tableName, Map<String, Object> data) throws MetadataServiceException {
        commonSqlService.update(tableName, data, buildIdInWhereParams(actionContext.getEnterpriseId(), objectDescribeApiName, ids), actionContext);
    }

    public Map<String, ForecastTask> lookupExistTask(ForecastObject object, List<ForecastTask> requestTasks, Function<ForecastTask, String> keyGetter) throws MetadataServiceException {
        Set<String> ruleIds = new HashSet<>();
        Set<String> ownerIds = new HashSet<>();
        for (ForecastTask task : requestTasks) {
            ruleIds.add(task.getForecastRuleObjectId());
            ownerIds.add(task.getOwner0());
        }
        ownerIds.add(object.getOwner().get(0));
        String tenantId = object.getTenantId();
        List<WhereParam> whereParams = buildColumnInWhereParams(tenantId, ForecastTask.FORECAST_TASK_OBJECT_DESCRIBE_API_NAME, ForecastTask.FORECAST_RULE_OBJECT_ID, new ArrayList<>(ruleIds));
        whereParams.add(buildWhereParam(IObjectData.OWNER, CommonSqlOperator.IN, new ArrayList<>(ownerIds)));
        whereParams.add(buildWhereParam(BaseForecast.LOCK_STATUS, CommonSqlOperator.EQ, Collections.singletonList("0")));
        List<Map> existTasks = commonSqlService.select(FORECAST_TASK_TABLE_NAME, whereParams, getActionContext(tenantId));
        if (!existTasks.isEmpty()) {
            return existTasks.stream().map(ForecastTaskRecord::new).collect(Collectors.toMap(keyGetter, Function.identity(), (v1, v2) -> v2));
        }
        return Collections.emptyMap();
    }

    public Map<String, String> lookupTaskKeyMap(String tenantId, String ruleId, int expectedSize) {
        HashMap map = Maps.newHashMapWithExpectedSize(expectedSize);
        List<IObjectData> dataList = selectTaskKeyColumns(tenantId, Collections.singletonList(ruleId)); // 只查部分列提高性能
        for (IObjectData data : dataList) {
            map.put(ForecastTask.uniqueKeyOfObjectData(data), data.getId());
        }
        return map;
    }

    public Map<String, String> lookupTaskKeyMapForSync(String tenantId, List<String> ruleIds, List<String> ownerIds) {
        List<IObjectData> dataList = selectTaskKeyColumnsForSync(tenantId, ruleIds, ownerIds); // 只查部分列提高性能
        return dataList.isEmpty() ? new HashMap<>() : dataList.stream().collect(Collectors.toMap(ForecastTask::uniqueKeyOfObjectData, IObjectData::getId, (v1, v2) -> v2));
    }

    public Set<String> lookupExistDetailId(String tenantId, List<ForecastRule> rules, List<ForecastObject> forecastObjects) {
        List<String> ruleIds = rules.stream().map(ForecastRule::getId).collect(Collectors.toList());
        List<String> forecastObjectIds = forecastObjects.stream().map(IObjectData::getId).collect(Collectors.toList());
        List<IObjectData> dataList = selectTaskDetailKeyColumns(tenantId, ruleIds, forecastObjectIds); // 只查部分列提高性能
        Set<String> existDetailIdLookup = new HashSet<>();
        for (IObjectData data : dataList) {
            existDetailIdLookup.add(ForecastTaskDetail.ruleKey(data));
        }
        return existDetailIdLookup;
    }

    public List<ForecastTaskDetail> queryDetailByTaskId(String tenantId, String taskId) throws MetadataServiceException {
        List<WhereParam> whereParams = buildBaseWhereParams(tenantId, ForecastTaskDetail.FORECAST_TASK_DETAIL_OBJECT_DESCRIBE_API_NAME);
        whereParams.add(buildWhereParam(ForecastTaskDetail.FORECAST_TASK_OBJECT_ID, CommonSqlOperator.EQ, Collections.singletonList(taskId)));
        whereParams.add(buildWhereParam(ForecastTaskDetail.ADD_MANUALLY, CommonSqlOperator.EQ, Collections.singletonList(Boolean.FALSE)));
        List<Map> existTaskDetails = commonSqlService.select(FORECAST_TASK_DETAIL_TABLE_NAME, whereParams, getActionContext(tenantId));
        List<ForecastTaskDetail> result = new ArrayList<>(existTaskDetails.size());
        for (Map map : existTaskDetails) {
            result.add(new ForecastTaskDetailRecord(map));
        }
        return result;
    }

    private List<IObjectData> selectTaskKeyColumns(String tenantId, List<String> ruleIds) {
        SearchTemplateQuery searchTemplateQuery = getSearchTemplateQuery();
        searchTemplateQuery.setSearchSource("db");
        List<IFilter> filters = new ArrayList<>();
        addFilter(ForecastTask.FORECAST_RULE_OBJECT_ID, Operator.IN, ruleIds, filters);
        addFilter(IObjectData.OWNER, Operator.NIN, Collections.singletonList(User.SUPPER_ADMIN_USER_ID), filters);
        searchTemplateQuery.setFilters(filters);
        return rollingFindBySearchTemplateQueryWithFields(getActionContext(tenantId), ForecastTask.FORECAST_TASK_OBJECT_DESCRIBE_API_NAME, searchTemplateQuery, Lists.newArrayList(ForecastTask.FORECAST_RULE_OBJECT_ID, IObjectData.OWNER, ForecastTask.FORECAST_START_DATE));
    }

    private List<IObjectData> selectTaskKeyColumnsForSync(String tenantId, List<String> ruleIds, List<String> ownerIds) {
        SearchTemplateQuery searchTemplateQuery = getSearchTemplateQuery();
        searchTemplateQuery.setSearchSource("db");
        List<IFilter> filters = new ArrayList<>();
        addFilter(ForecastTask.FORECAST_RULE_OBJECT_ID, Operator.IN, ruleIds, filters);
        addFilter(IObjectData.OWNER, Operator.IN, ownerIds, filters);
        addFilter(BaseForecast.LOCK_STATUS, Operator.EQ, Collections.singletonList("0"), filters);
        searchTemplateQuery.setFilters(filters);
        return rollingFindBySearchTemplateQueryWithFields(getActionContext(tenantId), ForecastTask.FORECAST_TASK_OBJECT_DESCRIBE_API_NAME, searchTemplateQuery, Lists.newArrayList(ForecastTask.FORECAST_RULE_OBJECT_ID, IObjectData.OWNER, ForecastTask.FORECAST_START_DATE));
    }

    private List<IObjectData> selectTaskDetailKeyColumns(String tenantId, Collection<String> ruleIds, Collection<String> objectIds) {
        SearchTemplateQuery searchTemplateQuery = getSearchTemplateQuery();
        searchTemplateQuery.setSearchSource("db");
        List<IFilter> filters = new ArrayList<>();
        addFilter(ForecastTaskDetail.FORECAST_RULE_OBJECT_ID, Operator.IN, new ArrayList<>(ruleIds), filters);
        addFilter(ForecastTaskDetail.FORECAST_OBJECT_ID, Operator.IN, new ArrayList<>(objectIds), filters);
        addFilter(ForecastTaskDetail.ADD_MANUALLY, Operator.EQ, Collections.singletonList("false"), filters);
        searchTemplateQuery.setFilters(filters);
        return rollingFindBySearchTemplateQueryWithFields(getActionContext(tenantId), ForecastTaskDetail.FORECAST_TASK_DETAIL_OBJECT_DESCRIBE_API_NAME, searchTemplateQuery, Lists.newArrayList(ForecastTaskDetail.FORECAST_RULE_OBJECT_ID, ForecastTaskDetail.FORECAST_TASK_OBJECT_ID, ForecastTaskDetail.FORECAST_OBJECT_ID));
    }

    private List<IObjectData> selectTaskDetailId(String tenantId, List<String> taskIds) {
        SearchTemplateQuery searchTemplateQuery = getSearchTemplateQuery();
        IFilter filter = new Filter();
        filter.setFieldName(ForecastTaskDetail.FORECAST_TASK_OBJECT_ID);
        filter.setOperator(Operator.IN);
        filter.setFieldValues(taskIds);
        searchTemplateQuery.setFilters(Collections.singletonList(filter));
        return rollingFindBySearchTemplateQueryWithFields(getActionContext(tenantId), ForecastTaskDetail.FORECAST_TASK_DETAIL_OBJECT_DESCRIBE_API_NAME, searchTemplateQuery, Collections.singletonList(DBRecord.ID));
    }

    private List<IObjectData> rollingFindBySearchTemplateQueryWithFields(IActionContext actionContext, String objectApiName, SearchTemplateQuery searchTemplateQuery, List<String> fieldList) {
        List<IObjectData> list = serviceFacade.findBySearchTemplateQueryWithFields(actionContext, objectApiName, searchTemplateQuery, fieldList).getData();
        List<IObjectData> result = list;
        while (list.size() == LIMIT) {
            searchTemplateQuery.setOffset(searchTemplateQuery.getOffset() + searchTemplateQuery.getLimit());
            list = serviceFacade.findBySearchTemplateQueryWithFields(actionContext, objectApiName, searchTemplateQuery, fieldList).getData();
            if (!list.isEmpty()) {
                result.addAll(list);
            }
        }
        return result;
    }

    private void addFilter(String fieldName, Operator operator, List<String> fieldValues, List<IFilter> filters) {
        IFilter filter = new Filter();
        filter.setFieldName(fieldName);
        filter.setOperator(operator);
        filter.setFieldValues(fieldValues);
        filters.add(filter);
    }

    private WhereParam buildWhereParam(String column, CommonSqlOperator operator, List<Object> value) {
        WhereParam whereParam = new WhereParam();
        whereParam.setColumn(column);
        whereParam.setOperator(operator);
        whereParam.setValue(value);
        return whereParam;
    }

    private List<WhereParam> buildIdEqualWhereParams(String tenantId, String objectDescribeApiName, String id) {
        List<WhereParam> whereParams = buildBaseWhereParams(tenantId, objectDescribeApiName);
        whereParams.add(buildWhereParam(ID, CommonSqlOperator.EQ, Collections.singletonList(id)));
        return whereParams;
    }

    private List<WhereParam> buildIdInWhereParams(String tenantId, String objectDescribeApiName, List<String> ids) {
        return buildColumnInWhereParams(tenantId, objectDescribeApiName, ID, ids);
    }

    private List<WhereParam> buildColumnInWhereParams(String tenantId, String objectDescribeApiName, String column, List<String> ids) {
        List<WhereParam> whereParams = buildBaseWhereParams(tenantId, objectDescribeApiName);
        whereParams.add(buildWhereParam(column, CommonSqlOperator.IN, Collections.unmodifiableList(ids)));
        return whereParams;
    }

    private List<WhereParam> buildBaseWhereParams(String tenantId, String objectDescribeApiName) {
        List<WhereParam> whereParams = new ArrayList<>();
        whereParams.add(buildWhereParam(DBRecord.IS_DELETED, CommonSqlOperator.EQ, Collections.singletonList(0)));
        whereParams.add(buildWhereParam(Tenantable.TENANT_ID, CommonSqlOperator.EQ, Collections.singletonList(tenantId)));
        whereParams.add(buildWhereParam(IObjectData.DESCRIBE_API_NAME, CommonSqlOperator.EQ, Collections.singletonList(objectDescribeApiName)));
        return whereParams;
    }

    private IActionContext getActionContext(String tenantId) {
        IActionContext actionContext = prototypeActionContext.clone(); // 原型模式避免重复new
        actionContext.setEnterpriseId(tenantId);
        return actionContext;
    }

    private SearchTemplateQuery getSearchTemplateQuery() {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setPermissionType(0);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setFindExplicitTotalNum(false);
        searchTemplateQuery.setNeedReturnQuote(false);
        searchTemplateQuery.setLimit(LIMIT);
        return searchTemplateQuery;
    }

    private SearchTemplateQuery getSearchTemplateQuery(ForecastRule rule, Long createTime, Long forecastEndDate) {
        SearchTemplateQuery searchTemplateQuery = getSearchTemplateQuery();
        List<IFilter> filters = new ArrayList<>();
        addFilter(ForecastTask.FORECAST_RULE_OBJECT_ID, Operator.EQ, Collections.singletonList(rule.getId()), filters);
        addFilter(ForecastTask.FORECAST_END_DATE, Operator.EQ, Collections.singletonList(forecastEndDate.toString()), filters);
        addFilter(DBRecord.CREATE_TIME, Operator.LT, Collections.singletonList(createTime.toString()), filters);
        addFilter(ForecastTask.BIZ_STATUS, Operator.EQ, Collections.singletonList("0"), filters);
        addFilter(BaseForecast.LOCK_STATUS, Operator.EQ, Collections.singletonList("0"), filters);
        addFilter(IObjectData.OWNER, Operator.NIN, Collections.singletonList(User.SUPPER_ADMIN_USER_ID), filters);
        searchTemplateQuery.setFilters(filters);
        return searchTemplateQuery;
    }
}