package com.facishare.crm.recycling.task.executor.service.impl;

import com.facishare.crm.sfa.lto.exception.ExceptionUtil;
import com.facishare.crm.sfa.lto.objectlimit.ObjectLimitRuleService;
import com.facishare.crm.sfa.lto.objectlimit.models.ObjectLimitRuleModel;
import com.facishare.crm.sfa.lto.utils.TenantUtil;
import org.apache.rocketmq.common.message.MessageExt;
import com.facishare.crm.recycling.task.executor.model.PartnerModel;
import com.facishare.crm.recycling.task.executor.service.PartnerChangedService;
import com.facishare.crm.recycling.task.executor.util.ProtoUtil;
import com.facishare.crm.sfa.lto.common.LtoOrgCommonService;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description 保有量规则计算
 */

@Slf4j
@Component
public class ObjectLimitPartnerChangedService implements PartnerChangedService {
    @Autowired
    private LtoOrgCommonService ltoOrgCommonService;
    @Autowired
    private ObjectLimitRuleService objectLimitBiz;
    @Autowired
    private TenantUtil tenantUtil;

    @Override
    public void execute(MessageExt message) {
        try {
            PartnerModel.ChangeOuterOrganizationDepartmentEvent departmentEvent;
            PartnerModel.ChangeOuterOrganizationUserDeptEvent userDeptEvent;
            byte[] body = message.getBody();

            if (message.getFlag() > 200 && message.getFlag() < 205) {
                departmentEvent = ProtoUtil.fromProto(body, PartnerModel.ChangeOuterOrganizationDepartmentEvent.class);
                doDepartmentEvent(departmentEvent);
            } else if (message.getFlag() > 101) {
                userDeptEvent = ProtoUtil.fromProto(body, PartnerModel.ChangeOuterOrganizationUserDeptEvent.class);
                doUserDeptEvent(userDeptEvent);
            }
            else {
                log.warn("consuming message topic:{} msgId:{} unknowns flag:{} body:{}", message.getTopic(), message.getMsgId(), message.getFlag(), body);
            }
        } catch (Exception e) {
            ExceptionUtil.throwCommonBusinessException();
        }
    }

    private void doDepartmentEvent(PartnerModel.ChangeOuterOrganizationDepartmentEvent departmentEvent) {
        if (departmentEvent == null) {
            return;
        }
        String tenantId = String.valueOf(departmentEvent.getEnterpriseId());
        if (tenantUtil.isExclusiveCloudEnterprise(tenantId)) {
            log.info("{} is exclusive cloud ei", tenantId);
            return;
        }
        if (StringUtils.isBlank(departmentEvent.getDepartmentId())) {
            return;
        }
        String outTenantId = departmentEvent.getDepartmentId();
        List<String> outTenantIds = Lists.newArrayList(outTenantId);

        List<Long> outTenantLongIds = Lists.newArrayList(Long.parseLong(outTenantId));
        List<String> existOutTenantIds = ltoOrgCommonService.getOuterTenantsByOuterTenantIds(tenantId, outTenantLongIds);
        if (existOutTenantIds.contains(departmentEvent.getDepartmentId())) {
            try {
                calculateOuterTenantRule(tenantId, outTenantIds);
            } catch (MetadataServiceException e) {
                log.error("calculateOuterTenantRule error:{}", departmentEvent, e);
            }
        } else {
            try {
                objectLimitBiz.deleteObjectLimitRuleByDataIds(tenantId, outTenantIds, ObjectLimitRuleModel.DataTypeEnum.PARTNER.getCode());
            } catch (MetadataServiceException e) {
                log.error("deleteObjectLimitRuleByDataIds error:{}", departmentEvent, e);
            }
        }
    }

    private void doUserDeptEvent(PartnerModel.ChangeOuterOrganizationUserDeptEvent userDeptEvent) {
        if (userDeptEvent == null) {
            return;
        }
        String tenantId = String.valueOf(userDeptEvent.getEnterpriseId());
        if (tenantUtil.isExclusiveCloudEnterprise(tenantId)) {
            log.info("{} is exclusive cloud ei", tenantId);
            return;
        }
        List<String> outTenantIds = Lists.newArrayList();

        if (CollectionUtils.notEmpty(userDeptEvent.getNewDepartmentIds())) {
            outTenantIds.addAll(userDeptEvent.getNewDepartmentIds().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList()));
        }
        if (CollectionUtils.notEmpty(userDeptEvent.getOldDepartmentIds())) {
            outTenantIds.addAll(userDeptEvent.getOldDepartmentIds().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList()));
        }

        if (CollectionUtils.notEmpty(outTenantIds)) {
            try {
                calculateOuterTenantRule(tenantId, outTenantIds);
            } catch (MetadataServiceException e) {
                log.error("calculateOuterTenantRule error:{}", userDeptEvent, e);
            }
            List<Long> outTenantLongIds = outTenantIds.stream().map(Long::valueOf).collect(Collectors.toList());
            if (userDeptEvent.getUserId() > 0) {
                String outUserId = String.valueOf(userDeptEvent.getUserId());
                List<String> outerUserIds = ltoOrgCommonService.batchGetOuterTenantsUserIds(tenantId, outTenantLongIds);
                if (!outerUserIds.contains(outUserId)) {
                    try {
                        objectLimitBiz.deleteObjectLimitRuleByDataIds(tenantId, Lists.newArrayList(outUserId), ObjectLimitRuleModel.DataTypeEnum.PARTNER_EMPLOYEE.getCode());
                    } catch (MetadataServiceException e) {
                        log.error("deleteObjectLimitRuleByDataIds error:{}", userDeptEvent, e);
                    }
                }
            }
        }
    }

    private void calculateOuterTenantRule(String tenantId, List<String> outTenantIds) throws MetadataServiceException {
        String dataType = ObjectLimitRuleModel.DataTypeEnum.PARTNER.getCode();
        List<ObjectLimitRuleModel.ObjectLimitRule> objectLimitRuleList = objectLimitBiz.getObjectLimitRuleByDataIds(tenantId, outTenantIds, dataType);
        if (CollectionUtils.empty(objectLimitRuleList)) {
            return;
        }
        Set<String> objectApiNameSet = objectLimitRuleList.stream().map(x -> x.getObjectApiName())
                .collect(Collectors.toSet());

        for (String objectApiName : objectApiNameSet) {
            Set<String> groupIdSet = objectLimitRuleList.stream()
                    .filter(x -> objectApiName.equals(x.getObjectApiName())).map(x -> x.getGroupId())
                    .collect(Collectors.toSet());
            if (CollectionUtils.empty(groupIdSet)) {
                continue;
            }

            for (String groupId : groupIdSet) {
                List<ObjectLimitRuleModel.ObjectLimitRule> tempLimitRuleList = objectLimitBiz.getObjectLimitRuleByGroupId(tenantId, objectApiName, groupId);
                if (CollectionUtils.empty(tempLimitRuleList)) {
                    continue;
                }

                List<String> limitEmployeeList = objectLimitBiz.getEmployeeRuleByGroupId(tenantId, objectApiName, groupId);
                List<String> allLimitEmployeeList = objectLimitBiz.getObjectLimitRuleUserIds(tenantId, tempLimitRuleList);

                List<String> deletedEmployeeList = limitEmployeeList.stream()
                        .filter(x -> !allLimitEmployeeList.contains(x)).collect(Collectors.toList());
                if (CollectionUtils.notEmpty(deletedEmployeeList)) {
                    objectLimitBiz.deleteEmployeeRuleByEmployeeIds(tenantId, objectApiName, groupId, deletedEmployeeList);
                    limitEmployeeList.removeIf(deletedEmployeeList::contains);
                    log.warn(String.format("ObjectLimitPartnerChangedService: tenantID-%s, groupId-%s, delete employee limit rule employeeIds: %s", tenantId, groupId, String.join(",", deletedEmployeeList)));
                }
                List<String> insertEmployeeList = allLimitEmployeeList.stream()
                        .filter(x -> !limitEmployeeList.contains(x)).collect(Collectors.toList());

                if (CollectionUtils.notEmpty(insertEmployeeList)) {
                    String calculateId = tempLimitRuleList.get(0).getCalculateId();
                    objectLimitBiz.insertEmployeeRule(tenantId, objectApiName, groupId, calculateId, Lists.newArrayList(insertEmployeeList));
                }
            }
        }

        Map<String, String>  outTenantPartnerMap = ltoOrgCommonService.getPartnersByOutTenantIds(tenantId, outTenantIds);
        if(CollectionUtils.notEmpty(outTenantPartnerMap)) {
            List<String> partnerIds = Lists.newArrayList(outTenantPartnerMap.values());
            objectLimitRuleList = objectLimitBiz.getObjectLimitRuleByDataType(tenantId, ObjectLimitRuleModel.DataTypeEnum.PARTNER_CUSTOM.getCode());
            if(CollectionUtils.notEmpty(objectLimitRuleList)) {
                for (ObjectLimitRuleModel.ObjectLimitRule objectLimitRule : objectLimitRuleList) {
                    Map<String, Boolean> fitMap =  objectLimitBiz.checkIsFitObjectLimitRule(tenantId, partnerIds, objectLimitRule);
                    List notFitPartnerIds = Lists.newArrayList();
                    List fitPartnerIds = Lists.newArrayList();
                    for (Map.Entry<String, Boolean> entry : fitMap.entrySet()) {
                        if(Boolean.TRUE.equals(entry.getValue())) {
                            fitPartnerIds.add(entry.getKey());
                        } else {
                            notFitPartnerIds.add(entry.getKey());
                        }
                    }

                    if(CollectionUtils.notEmpty(notFitPartnerIds)) {
                        List<String> outUserIds = ltoOrgCommonService.getPartnerOutUserIds(tenantId, notFitPartnerIds);
                        if(CollectionUtils.notEmpty(outUserIds)) {
                            objectLimitBiz.deleteEmployeeRuleByEmployeeIds(tenantId, outUserIds);
                            log.warn(String.format("ObjectLimitPartnerChangedService: tenantID-%s, groupId-%s, delete employee limit rule employeeIds: %s", tenantId, objectLimitRule.getGroupId(), String.join(",", outUserIds)));
                        }
                    }

                    if(CollectionUtils.notEmpty(fitPartnerIds)) {
                        List<String> outUserIds = ltoOrgCommonService.getPartnerOutUserIds(tenantId, fitPartnerIds);
                        if(CollectionUtils.notEmpty(outUserIds)) {
                            List<String> existUserIds = objectLimitBiz.getEmployeeRuleByGroupId(tenantId, objectLimitRule.getObjectApiName(), objectLimitRule.getGroupId());
                            List<String> needInsertUserIds = Lists.newArrayList();
                            for(String userId : outUserIds) {
                                if(!existUserIds.contains(userId)) {
                                    needInsertUserIds.add(userId);
                                }
                            }
                            if(CollectionUtils.notEmpty(needInsertUserIds)) {
                                objectLimitBiz.insertEmployeeRule(tenantId, objectLimitRule.getObjectApiName(), objectLimitRule.getGroupId(), objectLimitRule.getCalculateId(), needInsertUserIds);
                            }
                        }
                    }
                }
            }
        }
    }
}
