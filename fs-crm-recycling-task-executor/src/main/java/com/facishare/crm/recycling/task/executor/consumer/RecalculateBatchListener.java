package com.facishare.crm.recycling.task.executor.consumer;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.recycling.task.executor.common.RecalculateFactory;
import com.facishare.crm.recycling.task.executor.common.RecyclingTaskGray;
import com.facishare.crm.recycling.task.executor.common.SfaRecyclingTaskRateLimiterService;
import com.facishare.crm.recycling.task.executor.enums.ActionCodeEnum;
import com.facishare.crm.recycling.task.executor.model.RecalculateMessage;
import com.facishare.crm.recycling.task.executor.service.NomonTaskService;
import com.facishare.crm.recycling.task.executor.service.RecyclingRuleCacheService;
import com.facishare.crm.recycling.task.executor.util.CommonUtils;
import com.facishare.crm.sfa.audit.log.context.SFALogContext;
import com.facishare.crm.sfa.lto.common.SFAJedisLock;
import com.facishare.crm.sfa.lto.exception.ExceptionUtil;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.fxiaoke.rocketmq.util.MessageHelper;
import com.github.jedis.support.JedisCmd;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

/**
 * 执行器接收MQ入口
 */
@Slf4j
@Component
public class RecalculateBatchListener implements ApplicationListener<ContextRefreshedEvent> {
    private AutoConfMQPushConsumer consumer;

    @Autowired
    protected JedisCmd SFAJedisCmd;

    @Autowired
    private RecalculateFactory recalculateFactory;

    @Autowired
    private SfaRecyclingTaskRateLimiterService sfaRecyclingTaskRateLimiterService;

    @Autowired
    private RecyclingTaskGray recyclingTaskGray;

    @Autowired
    private NomonTaskService nomonTaskService;

    @Autowired
    protected RecyclingRuleCacheService recyclingRuleCacheService;

    @PostConstruct
    public void init() {
        consumer = new AutoConfMQPushConsumer("recycling-recalculate-batch", (MessageListenerConcurrently) (msgs, context) -> {
            if (!msgs.isEmpty()) {
                for (MessageExt msg : msgs) {
                    sfaRecyclingTaskRateLimiterService.getCrmRecalculateLimiter().acquire();
                    // 取出traceContext
                    MessageHelper.fillContextFromMessage(TraceContext.get(), msg);
                    consumeMessage(msg);
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        });
    }


    @PreDestroy
    public void close() {
        consumer.close();
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (consumer != null && event.getApplicationContext().getParent() == null) {
            consumer.start();
        }
    }

    private ConsumeConcurrentlyStatus consumeMessage(MessageExt body) {
        RecalculateMessage message = JSON.parseObject(body.getBody(), RecalculateMessage.class);
        if (recyclingTaskGray.skipTenantId(message.getTenantId())) {
            log.warn("RecalculateBatchListener skipTenantId:{},objectId:{}", message.getTenantId(), message.getObjectId());
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }
        if (ActionCodeEnum.CHANGE_RULE.getActionCode().equals(message.getActionCode())) {
            recyclingRuleCacheService.invalid(message.getTenantId());
            log.warn("RecalculateBatchListener invalid cache,msgId:{},tenantId:{},msg:{}", body.getMsgId(), message.getTenantId(), message);
        }
        String lockKey = CommonUtils.getLockKey(message);

        try (SFAJedisLock jedisLock = new SFAJedisLock(SFAJedisCmd, lockKey)) {
            log.info("RecalculateBatchMessage, msgId:{},reconsumeTimes:{},RecalculateMessage:{}", body.getMsgId(), body.getReconsumeTimes(), message);
            if (StringUtils.isBlank(message.getObjectId()) && CollectionUtils.isEmpty(message.getDeptIds())) {
                log.warn("RecalculateBatchMessage data is empty : msgId:{},{},{}", body.getMsgId(), message.getTenantId(), message.getObjectId());
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }
            //long offset = body.getQueueOffset();
            //String maxOffset = body.getProperty(MessageConst.PROPERTY_MAX_OFFSET);
            //if (StringUtils.isNotBlank(maxOffset)
            //        && ((Long.parseLong(maxOffset) - offset) > sfaRecyclingTaskRateLimiterService.DiffOffsetLimit)) {
            //    log.warn("resendRecalculate changerule: msgId:{},{},{}", body.getMsgId(), message.getTenantId(), message.getObjectId());
            //    nomonTaskService.sendRecalculate(message.getTenantId(), message.getObjectId(),
            //            message.getObjectApiName(), new Date(), ActionCodeEnum.CHANGE_RULE);
            //    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            //}
            boolean b = jedisLock.tryLock();
            // 未获取到锁，返回后面重试消息。
            if (!b) {
                log.info("RecalculateBatchMessage lock tryLock failed, msgId:{},reconsumeTimes:{},RecalculateMessage:{},lockKey:{}", body.getMsgId(), body.getReconsumeTimes(), message, lockKey);
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }

            SFALogContext.putVariable("bizName","recycling-recalculate-batch");
            SFALogContext.putVariable("msg",body);
            long startTimes = System.currentTimeMillis();
            SFALogContext.putVariable("cost1", startTimes - body.getBornTimestamp());
            log.info("RecalculateBatchMessage lock tryLock success, msgId:{},reconsumeTimes:{},RecalculateMessage:{},lockKey:{}", body.getMsgId(), body.getReconsumeTimes(), message, lockKey);
            // 如果这个操作执行时间很长 会导致后续消息堆积， 或者消息执行超时 会导致消息重试 重试的消息是否会导致重复执行
            recalculateFactory.getRecalculateService(message.getObjectApiName()).execute(message);
            jedisLock.unlock();
            long endTimes = System.currentTimeMillis();
            log.info("RecalculateBatchMessage lock unLock success, msgId:{},reconsumeTimes:{},RecalculateMessage:{},lockKey:{},executeTimes:{}", body.getMsgId(), body.getReconsumeTimes(), message, lockKey, endTimes - startTimes);
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("RecalculateBatchListener consumeMessage msgId:{}", body.getMsgId(), e);
            ExceptionUtil.throwCommonBusinessException();
        } finally {
            TraceContext.remove();
            SFALogContext.clearContext();
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }


}
