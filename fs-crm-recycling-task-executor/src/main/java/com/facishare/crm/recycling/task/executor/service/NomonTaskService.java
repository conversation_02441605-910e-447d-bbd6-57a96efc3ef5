package com.facishare.crm.recycling.task.executor.service;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.recycling.task.executor.enums.ActionCodeEnum;
import com.facishare.crm.recycling.task.executor.model.RecalculateMessage;
import com.fxiaoke.paas.gnomon.api.NomonProducer;
import com.fxiaoke.paas.gnomon.api.entity.NomonDeleteMessage;
import com.fxiaoke.paas.gnomon.api.entity.NomonMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

import static com.facishare.crm.recycling.task.executor.util.ConstantUtils.OBJ_RECALCULATE_BIZ;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020/4/21 09:08
 */
@Slf4j
@Component
public class NomonTaskService {
    @Autowired
    private NomonProducer nomonProducer;

    /**
     * 新建/更新 到时间收回的task
     *
     * @param tenantId
     * @param objectId    客户id
     * @param apiName
     * @param executeTime 任务执行时间
     */
    public void sendRecalculate(String tenantId, String objectId, String apiName, Date executeTime) {
        sendRecalculate(tenantId,objectId,apiName,executeTime,ActionCodeEnum.FOLLOW);
    }

    public void sendRecalculate(String tenantId, String objectId, String apiName, Date executeTime,ActionCodeEnum actionCodeEnum) {
        RecalculateMessage recalculateMessage = new RecalculateMessage();
        recalculateMessage.setTenantId(tenantId);
        recalculateMessage.setActionCode(actionCodeEnum.getActionCode());
        recalculateMessage.setObjectId(objectId);
        recalculateMessage.setObjectApiName(apiName);

        NomonMessage nomonMessage = NomonMessage.builder()
                .biz(OBJ_RECALCULATE_BIZ)
                .tenantId(tenantId)
                .dataId(objectId)
                .executeTime(executeTime)
                .callArg(JSONObject.toJSON(recalculateMessage).toString())
                .build();
        log.info("send,nomonMessage:{}", nomonMessage.toString());
        nomonProducer.send(nomonMessage);
    }

    public void deleteTask(String biz, String tenantId, String dataId) {
        NomonDeleteMessage message = NomonDeleteMessage
                .builder()
                .biz(biz)
                .tenantId(tenantId)
                .dataId(dataId)
                .build();
        nomonProducer.send(message);
    }

    public void createOrUpdateTask(String biz, String tenantId, String dataId, Date executeTime, String callArg, Integer callQueueMod) {
        NomonMessage message = NomonMessage
                .builder()
                .biz(biz)
                .tenantId(tenantId)
                .dataId(dataId)
                .executeTime(executeTime)
                .callArg(String.format(callArg, tenantId, dataId))
                .callQueueMod(callQueueMod)
                .build();
        nomonProducer.send(message);
    }
}
