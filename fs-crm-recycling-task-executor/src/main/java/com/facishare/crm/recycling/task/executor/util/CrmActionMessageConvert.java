package com.facishare.crm.recycling.task.executor.util;

import com.facishare.crm.follow.model.CrmActionMQMessage;
import com.facishare.crm.sfa.audit.log.EntityConverter;
import com.facishare.crm.sfa.audit.log.model.AuditArg;

/**
 * <AUTHOR> gongchunru
 * @date : 2023/12/1 16:24
 * @description:
 */
public class CrmActionMessageConvert implements EntityConverter<CrmActionMQMessage> {
    @Override
    public AuditArg convert(CrmActionMQMessage message) {
        return AuditArg.builder()
                .ei(message.getTenantID())
                .objectIds(message.getObjectID())
                .objectApiName(message.getObjectApiName())
                .actionCode(message.getActionCode())
                .build();
    }
}
