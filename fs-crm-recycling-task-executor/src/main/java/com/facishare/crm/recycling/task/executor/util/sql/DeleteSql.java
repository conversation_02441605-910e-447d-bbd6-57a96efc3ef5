package com.facishare.crm.recycling.task.executor.util.sql;

import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@SuppressWarnings("unchecked")
public  class DeleteSql extends BaseSql {

	public static DeleteSql from(String tableName) {
		DeleteSql deleteSql = new DeleteSql();
		deleteSql.setTableName(tableName);
		return deleteSql;
	}

	public int logicDelete() {
		checked();
		Map<String, Object> updateFieldMap = new HashMap<>();
		updateFieldMap.put(DBRecord.IS_DELETED, 1);
		try {
			return commonSqlService.update(tableName, updateFieldMap, whereList, actionContext);
		} catch (MetadataServiceException e) {
			if (ignoreException) {
				log.error("逻辑删除异常，e", e);
				return 0;
			}
			throw new RuntimeException("逻辑删除异常", e);
		}
	}

	public int physicalDelete() {
		checked();
		try {
			return commonSqlService.delete(tableName, whereList, actionContext);
		} catch (MetadataServiceException e) {
			if (ignoreException) {
				log.error("物理删除异常，e", e);
				return 0;
			}
			throw new RuntimeException("物理删除异常", e);
		}
	}

	@Override
	public DeleteSql where(WhereSql whereSql) {
		return super.where(whereSql);
	}

	@Override
	public DeleteSql withContext(IActionContext context) {
		return super.withContext(context);
	}

	@Override
	public DeleteSql withAdminContext() {
		return super.withAdminContext();
	}

	@Override
	public DeleteSql ignoreException() {
		return super.ignoreException();
	}
}