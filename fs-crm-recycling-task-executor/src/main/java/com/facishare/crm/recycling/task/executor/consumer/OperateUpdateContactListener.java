package com.facishare.crm.recycling.task.executor.consumer;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.lto.objectpool.AccountPoolActionServiceImpl;
import com.facishare.crm.sfa.lto.objectpool.models.ObjectPoolActionModels;
import com.facishare.crm.sfa.lto.relationship.EnumUtil;
import com.facishare.crm.sfa.lto.relationship.models.RelationshipModels;
import com.facishare.crm.sfa.lto.relationship.service.RelationshiService;
import com.facishare.paas.appframework.core.model.User;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.nio.charset.Charset;

@Slf4j
@Component
public class OperateUpdateContactListener implements ApplicationListener<ContextRefreshedEvent> {

    private AutoConfMQPushConsumer consumer;
    @Autowired
    private AccountPoolActionServiceImpl accountPoolActionService;

    @PostConstruct
    public void init() {
        consumer = new AutoConfMQPushConsumer("sfa-recalculate-consumer", "sfa-account-pool-operate-upd-contact", (MessageListenerConcurrently) (msgs, context) -> {
            if (!msgs.isEmpty()) {
                for (MessageExt msg : msgs) {
                    consumeMessage(msg);
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        });
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (consumer != null && event.getApplicationContext().getParent() == null) {
            consumer.start();
        }
    }

    @PreDestroy
    public void close() {
        consumer.close();
    }

    private void consumeMessage(MessageExt messageExt) {
        String body = new String(messageExt.getBody(), Charset.forName("UTF-8"));
        try {
            if (messageExt.getReconsumeTimes() > 1) {
                return;
            }
            ObjectPoolActionModels.ObjectOperateMsg msg = JSON.parseObject(body, ObjectPoolActionModels.ObjectOperateMsg.class);
            log.warn("OperateUpdateContactListener msg:{}",msg);
            switch (messageExt.getTags()) {
                case "sfa-account-pool-operate":
                    handleUpdateContactOwner(msg);
                    break;
            }
        } catch (Exception e) {
            log.error("OperateUpdateContactListener consumeMessage msgId:{},e:", messageExt.getMsgId(), e);
            throw new RuntimeException(e);
        }
    }

    public void handleUpdateContactOwner(ObjectPoolActionModels.ObjectOperateMsg msg) {
        User user = new User(msg.getTenantId(), msg.getUserId());
        accountPoolActionService.updateContactOwner(user,msg.getAccountIds(),msg.getOwner(),msg.getDataOwnerDpt(),msg.isCleanTeamMember(),msg.isFollowConfig());
    }
}