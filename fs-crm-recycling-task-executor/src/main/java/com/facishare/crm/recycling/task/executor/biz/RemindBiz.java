package com.facishare.crm.recycling.task.executor.biz;

import com.facishare.crm.follow.util.CommonUtils;
import com.facishare.crm.recycling.task.executor.enums.RemindRecordTypeEnum;
import com.facishare.crm.recycling.task.executor.service.OrgServiceCommonService;
import com.facishare.crm.recycling.task.executor.util.ObjectDataUtils;
import com.facishare.paas.appframework.common.service.CRMNotificationServiceImpl;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.service.dto.InternationalItem;
import com.facishare.paas.appframework.common.service.model.CRMNotification;
import com.facishare.paas.appframework.common.service.model.NewCrmNotification;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.TeamMember;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.util.set.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-06-20 15:42
 */


@Slf4j
@Component
public class RemindBiz {

    @Autowired
    private CRMNotificationServiceImpl crmNotificationService;

    @Autowired
    protected OrgService orgService;

    @Autowired
    private OrgServiceCommonService orgServiceCommonService;


    public void sendRemind(IObjectData objectData, String content, RemindRecordTypeEnum recordType,String title) {
        log.info("build sendRemind,apiName:{},id:{}",objectData.getDescribeApiName(),objectData.getId());
        String tenantId = objectData.getTenantId();
        List<String> owners = objectData.getOwner();
        Set<Integer> receiverIds = new HashSet<>();
        for (String owner : owners) {
            receiverIds.add(Integer.parseInt(owner));
        }

        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        List<TeamMember> teamMembers = objectDataExt.getTeamMembers();
        if (CollectionUtils.isNotEmpty(teamMembers)){
            for (TeamMember teamMember : teamMembers) {
                String memberId = teamMember.getEmployee();
                getMembers(tenantId, receiverIds, teamMember, memberId);
            }
        }

        CRMNotification crmNotification = CRMNotification.builder().sender(User.SUPPER_ADMIN_USER_ID)
                .remindRecordType(recordType.getValue())
                .content(content)
                .dataId(objectData.getId())
                .receiverIds(receiverIds)
                .objectApiName(objectData.getDescribeApiName())
                .title(title)
                .build();

        if (StringUtils.isBlank(title)){
            crmNotification.setTitle(recordType.getDesc());
        }

        log.info("sendCRMNotification {}", crmNotification.toString());
        try {
            crmNotificationService.sendCRMNotification(ObjectDataUtils.buildUser(tenantId), crmNotification);
        } catch (Exception e) {
            log.error("sendCRMNotification error:{}",crmNotification,e);
        }
    }

    private void getMembers(String tenantId, Set<Integer> receiverIds, TeamMember teamMember, String memberId) {
        try {
            if(TeamMember.MemberType.EMPLOYEE.equals(teamMember.getMemberType())){
                receiverIds.add(Integer.parseInt(teamMember.getEmployee()));
            }else if(TeamMember.MemberType.DEPARTMENT.equals(teamMember.getMemberType())) {
                List<String> deptMemberIds = orgService.getMembersByDeptIds(CommonUtils.buildUser(tenantId), Lists.newArrayList(memberId), 0);
                if (com.facishare.paas.appframework.common.util.CollectionUtils.notEmpty(deptMemberIds)){
                    List<Integer> members = deptMemberIds.stream().map(Integer::parseInt).collect(Collectors.toList());
                    receiverIds.addAll(members);
                }
            }else if(TeamMember.MemberType.GROUP.equals(teamMember.getMemberType())){
                List<String> memberIdsByGroupIds = orgServiceCommonService.getMembersByGroupIds(CommonUtils.buildUser(tenantId).getTenantId(), Lists.newArrayList(memberId));
                if (com.facishare.paas.appframework.common.util.CollectionUtils.notEmpty(memberIdsByGroupIds)){
                    List<Integer> members = memberIdsByGroupIds.stream().map(Integer::parseInt).collect(Collectors.toList());
                    receiverIds.addAll(members);
                }
            }else if(TeamMember.MemberType.ROLE.equals(teamMember.getMemberType())){
                Set<String> memberIdsByRoleIds = orgServiceCommonService.getUserIdsByRoleIds(CommonUtils.buildUser(tenantId).getTenantId(), Lists.newArrayList(memberId));
                if (com.facishare.paas.appframework.common.util.CollectionUtils.notEmpty(memberIdsByRoleIds)){
                    List<Integer> members = memberIdsByRoleIds.stream().map(Integer::parseInt).collect(Collectors.toList());
                    receiverIds.addAll(members);
                }
            }
        }catch (Exception e){
            log.warn("flat memberId error:",e);
        }
    }

    public void sendRemind(IObjectData objectData, String tenantId,
                           String content,
                           RemindRecordTypeEnum recordType,
                           String title,
                           String titleInternationalKey,
                           List<String> internationalParameters,
                           String fullContentInternationalKey,
                           List<String> fullContentInternationalParameters) {
        User user = User.systemUser(tenantId);

        Map<String,String> urlParameter = Maps.newHashMap();
        if(objectData != null){
            urlParameter.put("objectApiName", objectData.getDescribeApiName());
            urlParameter.put("objectId", objectData.getId());
        }
        Set<Integer> receiverIds = new HashSet<>();
        List<String> owners = objectData.getOwner();
        for (String owner : owners) {
            receiverIds.add(Integer.parseInt(owner));
        }

        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        List<TeamMember> teamMembers = objectDataExt.getTeamMembers();
        if (CollectionUtils.isNotEmpty(teamMembers)){
            for (TeamMember teamMember : teamMembers) {
                String memberId = teamMember.getEmployee();
                getMembers(tenantId, receiverIds, teamMember, memberId);
            }
        }
        sendNewCRMRecord(user,recordType.getValue(), Lists.newArrayList(receiverIds),null,title,content,titleInternationalKey,
                internationalParameters,fullContentInternationalKey,fullContentInternationalParameters,urlParameter);

    }


    public void sendNewCRMRecord(User user ,
                                        int type,
                                        List<Integer> receiverIds,
                                        String senderId,
                                        String title,
                                        String fullContent,
                                        String titleInternationalKey,
                                 List<String> internationalParameters,
                                        String fullContentInternationalKey,
                                 List<String> fullContentInternationalParameters,
                                        Map<String,String> urlParameter){
        InternationalItem titleInternationalItem = null;
        InternationalItem contentInternationalItem = null;
        if(!Strings.isNullOrEmpty(titleInternationalKey)){
            titleInternationalItem = new InternationalItem();
            titleInternationalItem.setInternationalParameters(internationalParameters);
            titleInternationalItem.setInternationalKey(titleInternationalKey);
        }
        if(!Strings.isNullOrEmpty(fullContentInternationalKey)){
            contentInternationalItem = new InternationalItem();
            contentInternationalItem.setInternationalParameters(fullContentInternationalParameters);
            contentInternationalItem.setInternationalKey(fullContentInternationalKey);
        }
        sendNewCRMRecord(user,type,receiverIds,senderId,title,fullContent,titleInternationalItem,contentInternationalItem,urlParameter);
    }
    public void sendNewCRMRecord(User user,
                                        int type,
                                        List<Integer> receiverIds,
                                        String senderId,
                                        String title,
                                        String fullContent,
                                        InternationalItem titleInternational,
                                        InternationalItem fullContentInternational,
                                        Map<String,String> urlParameter){
        try {
            NewCrmNotification newCrmNotification = NewCrmNotification.builder()
                    .receiverIDs(Sets.newHashSet(receiverIds))
                    .sourceId(UUID.randomUUID().toString())
                    .type(type)
                    .build();
            if(!Strings.isNullOrEmpty(senderId)){
                newCrmNotification.setSenderId(senderId);
            }
            if(urlParameter != null){
                newCrmNotification.setUrlParameter(urlParameter);
                newCrmNotification.setObjectApiName(urlParameter.get("objectApiName"));
                newCrmNotification.setObjectId(urlParameter.get("objectId"));
                newCrmNotification.setUrlType(1);
            }
            if(!Strings.isNullOrEmpty(title)){
                newCrmNotification.setTitle(title);
            }
            if(!Strings.isNullOrEmpty(fullContent)){
                newCrmNotification.setFullContent(fullContent);
            }
            if(titleInternational != null){
                newCrmNotification.setTitleInfo(titleInternational);
            }
            if(fullContentInternational != null){
                newCrmNotification.setFullContentInfo(fullContentInternational);
            }
            log.info("sendNewCrmNotification:{}", newCrmNotification);
            crmNotificationService.sendNewCrmNotification(user, newCrmNotification);
        }
        catch (Exception ex){
            log.error(ex.getMessage(),ex);
        }
    }




}
