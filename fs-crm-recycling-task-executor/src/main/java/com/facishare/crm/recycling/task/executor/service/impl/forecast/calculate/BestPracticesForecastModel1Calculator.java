package com.facishare.crm.recycling.task.executor.service.impl.forecast.calculate;

import com.facishare.crm.recycling.task.executor.model.forecast.ForecastRule;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastTask;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastTaskDetail;

import java.math.BigDecimal;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.Function;

class BestPracticesForecastModel1Calculator extends BestPracticesForecastRuleFieldCalculator {

    @Override
    public BiConsumer<ForecastTask, BigDecimal> taskSetter() {
        return ForecastTask::setBestPracticesForecastModel1;
    }

    @Override
    public Function<ForecastTask, BigDecimal> taskGetter() {
        return ForecastTask::getBestPracticesForecastModel1;
    }

    @Override
    public Function<ForecastTaskDetail, BigDecimal> detailGetter() {
        return ForecastTaskDetail::getBestPracticesForecastModel1;
    }

    @Override
    public BiConsumer<ForecastTaskDetail, BigDecimal> detailSetter() {
        return ForecastTaskDetail::setBestPracticesForecastModel1;
    }

    @Override
    public String taskApiName() {
        return ForecastTask.BEST_PRACTICES_FORECAST_MODEL1;
    }

    @Override
    Function<ForecastRule, String> ruleGetter() {
        return ForecastRule::getBestPracticesForecastModel1;
    }

    @Override
    Function<ForecastRule, Set<String>> targetGetter() {
        return ForecastRule::getBestPracticesForecastModel1Target;
    }

    @Override
    BiConsumer<ForecastRule, Set<String>> targetSetter() {
        return ForecastRule::setBestPracticesForecastModel1Target;
    }
}
