package com.facishare.crm.recycling.task.executor.model.forecast;

import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;

import java.math.BigDecimal;

public class ForecastObjectRecord extends ObjectData implements ForecastObject {

    public ForecastObjectRecord(IObjectData objectData) {
        super(((ObjectData) objectData).getContainerDocument());
    }

    @Override
    public Long getForecastDate(String forecastObjectDateApiName) {
        return get(forecastObjectDateApiName, Long.class, 0L);
    }

    @Override
    public BigDecimal getForecastAmount(String forecastObjectAmountApiName) {
        return get(forecastObjectAmountApiName, BigDecimal.class, BigDecimal.ZERO);
    }

    @Override
    public boolean equals(Object o) {
        return super.equals(o);
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }

    @Override
    public String toString() {
        return super.toString();
    }
}