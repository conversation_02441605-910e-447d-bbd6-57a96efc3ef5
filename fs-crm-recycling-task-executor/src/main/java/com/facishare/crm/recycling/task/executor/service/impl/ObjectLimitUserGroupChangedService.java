package com.facishare.crm.recycling.task.executor.service.impl;

import com.facishare.crm.recycling.task.executor.service.UserGroupChangedService;
import com.facishare.crm.sfa.lto.common.LtoOrgCommonService;
import com.facishare.crm.sfa.lto.exception.ExceptionUtil;
import com.facishare.crm.sfa.lto.objectlimit.ObjectLimitRuleService;
import com.facishare.crm.sfa.lto.objectlimit.models.ObjectLimitRuleModel;
import com.facishare.crm.sfa.lto.rest.models.UserGroupModel;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description 保有量规则计算
 */

@Slf4j
@Component
public class ObjectLimitUserGroupChangedService implements UserGroupChangedService {
    @Autowired
    private LtoOrgCommonService ltoOrgCommonService;
    @Autowired
    private ObjectLimitRuleService objectLimitBiz;

    @Override
    public void execute(UserGroupModel.UserGroupChangedMessage message) {
        try {
            String tenantId = message.getTenantId();
            List<String> userGroupIds = message.getRelationIds();
            if (CollectionUtils.empty(userGroupIds)) {
                return;
            }

            List<ObjectLimitRuleModel.ObjectLimitRule> objectLimitRuleList = objectLimitBiz.getObjectLimitRuleByDataIds(tenantId, userGroupIds, ObjectLimitRuleModel.DataTypeEnum.USER_GROUP.getCode());
            if (CollectionUtils.empty(objectLimitRuleList)) {
                return;
            }

            List<UserGroupModel.UserGroupPoto> userGroupList = ltoOrgCommonService.getUserGroupByIds(tenantId, userGroupIds);
            List<String> inactiveUserGroupList = userGroupList.stream()
                    .filter(x -> UserGroupModel.UserGroupStatusEnum.Inactive.getCode() == x.getStatus())
                    .map(x -> x.getId()).collect(Collectors.toList());

            Set<String> objectApiNameSet = objectLimitRuleList.stream().map(x -> x.getObjectApiName())
                    .collect(Collectors.toSet());

            for(String objectApiName : objectApiNameSet) {
                Set<String> groupIdSet = objectLimitRuleList.stream()
                        .filter(x -> objectApiName.equals(x.getObjectApiName())).map(x -> x.getGroupId())
                        .collect(Collectors.toSet());
                if(CollectionUtils.empty(groupIdSet)){
                    continue;
                }

                for(String groupId : groupIdSet){
                    List<ObjectLimitRuleModel.ObjectLimitRule> tempLimitRuleList = objectLimitBiz.getObjectLimitRuleByGroupId(tenantId, objectApiName, groupId);
                    if(CollectionUtils.empty(tempLimitRuleList)){
                        continue;
                    }

                    if(CollectionUtils.notEmpty(inactiveUserGroupList)){
                        tempLimitRuleList.removeIf(x -> ObjectLimitRuleModel.DataTypeEnum.USER_GROUP.getCode().equals(x.getDataType()) && inactiveUserGroupList.contains(x.getDataId()));
                    }

                    List<String> limitEmployeeList = objectLimitBiz.getEmployeeRuleByGroupId(tenantId, objectApiName, groupId);
                    List<String> allLimitEmployeeList = objectLimitBiz.getObjectLimitRuleUserIds(tenantId, tempLimitRuleList);

                    List<String> deletedEmployeeList = limitEmployeeList.stream()
                            .filter(x -> !allLimitEmployeeList.contains(x)).collect(Collectors.toList());
                    if(CollectionUtils.notEmpty(deletedEmployeeList)){
                        objectLimitBiz.deleteEmployeeRuleByEmployeeIds(tenantId, objectApiName, groupId, deletedEmployeeList);
                        limitEmployeeList.removeIf(deletedEmployeeList::contains);
                        log.warn(String.format("ObjectLimitUserGroupChangedService: tenantID-%s, groupId-%s, delete employee limit rule employeeIds: %s", tenantId, groupId, String.join(",", deletedEmployeeList)));
                    }
                    List<String> insertEmployeeList = allLimitEmployeeList.stream()
                            .filter(x -> !limitEmployeeList.contains(x)).collect(Collectors.toList());

                    if(CollectionUtils.notEmpty(insertEmployeeList)){
                        String calculateId = tempLimitRuleList.get(0).getCalculateId();
                        objectLimitBiz.insertEmployeeRule(tenantId, objectApiName, groupId, calculateId, Lists.newArrayList(insertEmployeeList));
                    }
                }
            }
        } catch (Exception e) {
            ExceptionUtil.throwCommonBusinessException();
        }
    }
}
