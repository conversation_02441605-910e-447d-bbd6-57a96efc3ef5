package com.facishare.crm.recycling.task.executor.service.impl.forecast;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.recycling.task.executor.biz.ForecastBiz;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastPeriod;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastRule;
import com.facishare.crm.recycling.task.executor.service.ForecastTaskAutomationService;
import com.facishare.crm.recycling.task.executor.service.impl.forecast.mq.ForecastMessage;
import com.facishare.crm.recycling.task.executor.service.impl.forecast.task.ForecastPeriodMatcher;
import com.facishare.crm.sfa.audit.log.SFAAuditLog;
import com.facishare.crm.sfa.audit.log.context.SFALogContext;
import com.facishare.paas.appframework.common.service.CRMNotificationService;
import com.facishare.paas.appframework.common.service.dto.InternationalItem;
import com.facishare.paas.appframework.common.service.model.NewCrmNotification;
import com.facishare.paas.appframework.core.model.User;
import com.fxiaoke.paas.gnomon.api.NomonProducer;
import com.fxiaoke.paas.gnomon.api.entity.NomonMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.UUID;

@Service
@Slf4j
public class ForecastTaskAutomationServiceImpl implements ForecastTaskAutomationService {

    @Autowired
    private CRMNotificationService crmNotificationService;
    @Autowired
    private NomonProducer nomonProducer;
    @Autowired
    private ForecastBiz forecastBiz;

    public void submitForecastTaskRemindBeforeEndMessage(ForecastRule rule) {
        List<ForecastPeriod> periods = rule.getForecastPeriods();
        if (periods == null) {
            periods = ForecastPeriodMatcher.generatePeriod(rule);
        }
        ZoneId zone = ZoneId.systemDefault();
        Integer remindDaysBeforeEnd = rule.getRemindDaysBeforeEnd();
        long currentTimeMillis = System.currentTimeMillis();
        ZonedDateTime now = ZonedDateTime.ofInstant(Instant.ofEpochMilli(currentTimeMillis), zone);
        LocalTime localTime = LocalTime.of(3, 0, 0);
        for (ForecastPeriod period : periods) {
            Long endTimestamp = period.getRight();
            ZonedDateTime end = ZonedDateTime.ofInstant(Instant.ofEpochMilli(endTimestamp), zone);
            ZonedDateTime expected = end.minusDays(remindDaysBeforeEnd);
            ZonedDateTime actual;
            if (expected.isBefore(now)) {
                actual = now; // 当提醒时间早于当前时间当下就发消息
            } else {
                actual = expected.with(localTime); // 否则提醒当天3点触发定时任务发消息
            }
            ForecastMessage.RuleEnable callArg = new ForecastMessage.RuleEnable();
            callArg.setRuleId(rule.getId());
            callArg.setTenantId(rule.getTenantId());
            callArg.setExpectTime(endTimestamp);
            callArg.setCreateTime(currentTimeMillis);
            NomonMessage message = NomonMessage.builder()
                    .biz(ForecastMessage.TASK_REMIND_TOPIC_TAG)
                    .tenantId(rule.getTenantId())
                    .dataId(rule.getId())
                    .taskId(String.valueOf(rule.getVersion()))
                    .executeTime(Date.from(actual.toInstant()))
                    .callArg(JSON.toJSONString(callArg))
                    .build();
            nomonProducer.send(message);
        }
    }

    @Override
    @SFAAuditLog(bizName = "forecast_rule", extra1 = "#count",
            entityClass = ForecastMessage.RuleEnable.class, convertClass = ForecastMessage.RuleEnableConverter.class)
    public void handleForecastTaskRemindBeforeEndMessage(ForecastRule rule, ForecastMessage.RuleEnable message) {
        if (rule.getAutoRemindBeforeEnd() == null || !rule.getAutoRemindBeforeEnd()) {
            log.warn("Rule auto_remind_before_end is false [id:{}|tenant:{}]", message.getRuleId(), message.getTenantId());
            return;
        }
        List<ForecastPeriod> periods = ForecastPeriodMatcher.generatePeriod(rule);
        Long expected = message.getExpectTime();
        for (ForecastPeriod period : periods) {
            if (expected.equals(period.getRight())) {
                sendForecastTaskRemindBeforeEndMessage(rule, message.getCreateTime(), expected);
                return;
            }
        }
        log.warn("Rule can NOT match period with expected [id:{}|tenant:{}|expected:{}]", message.getRuleId(), message.getTenantId(), expected);
    }

    private void sendForecastTaskRemindBeforeEndMessage(ForecastRule rule, Long createTime, Long forecastEndDate) {
        log.info("Start to send remind before end message [rule:{}|tenant:{}|expected:{}]", rule.getId(), rule.getTenantId(), forecastEndDate);
        Set<Integer> owners = forecastBiz.queryOwnerForTaskRemind(rule, createTime, forecastEndDate);
        SFALogContext.putVariable("count", owners.size());
        if (!owners.isEmpty()) {
            Integer remindDaysBeforeEnd = rule.getRemindDaysBeforeEnd();
            InternationalItem titleInfo = new InternationalItem();
            titleInfo.setInternationalKey("sfa.forecast.task.remind.before.end.title");
            InternationalItem fullContentInfo = new InternationalItem();
            fullContentInfo.setInternationalKey("sfa.forecast.task.remind.before.end.content");
            fullContentInfo.setInternationalParameters(Collections.singletonList(remindDaysBeforeEnd.toString()));
            NewCrmNotification newCrmNotification = NewCrmNotification.builder()
                    .type(NewCrmNotification.CUSTOM_REMIND_RECORD_TYPE)
                    .title("预测任务结束前自动提醒")
                    .titleInfo(titleInfo)
                    .receiverIDs(owners)
                    .fullContent("您有预测任务将在" + remindDaysBeforeEnd + "天后结束，请尽快处理")
                    .fullContentInfo(fullContentInfo)
                    .sourceId(UUID.randomUUID().toString())
                    .build();
            crmNotificationService.sendNewCrmNotification(User.systemUser(rule.getTenantId()), newCrmNotification);
        } else {
            log.info("Query owner result is empty [rule:{}|tenant:{}|createTime:{}|endDate:{}]", rule.getId(), rule.getTenantId(), createTime, forecastEndDate);
        }
    }

    @Override
    public void submitForecastTaskAutoLockAfterEndMessage(ForecastRule rule) {
        List<ForecastPeriod> periods = rule.getForecastPeriods();
        if (periods == null) {
            periods = ForecastPeriodMatcher.generatePeriod(rule);
        }
        long currentTimestamp = System.currentTimeMillis();
        for (ForecastPeriod period : periods) {
            Long endTimestamp = period.getRight();
            if (endTimestamp > currentTimestamp) { // 只有结束时间晚于当前时间才会发出定时任务
                ForecastMessage.RuleEnable callArg = new ForecastMessage.RuleEnable();
                callArg.setRuleId(rule.getId());
                callArg.setTenantId(rule.getTenantId());
                callArg.setExpectTime(endTimestamp);
                callArg.setCreateTime(currentTimestamp);
                NomonMessage message = NomonMessage.builder()
                        .biz(ForecastMessage.TASK_LOCK_TOPIC_TAG)
                        .tenantId(rule.getTenantId())
                        .dataId(rule.getId())
                        .taskId(String.valueOf(rule.getVersion()))
                        .executeTime(new Date(endTimestamp))
                        .callArg(JSON.toJSONString(callArg))
                        .build();
                nomonProducer.send(message);
            }
        }
    }

    @Override
    @SFAAuditLog(bizName = "forecast_rule", extra1 = "#count",
            entityClass = ForecastMessage.RuleEnable.class, convertClass = ForecastMessage.RuleEnableConverter.class)
    public void handleForecastTaskAutoLockAfterEndMessage(ForecastRule rule, ForecastMessage.RuleEnable message) {
        if (rule.getAutoLockAfterEnd() == null || !rule.getAutoLockAfterEnd()) {
            log.warn("Rule auto_lock_after_end is false [id:{}|tenant:{}]", message.getRuleId(), message.getTenantId());
            return;
        }
        List<ForecastPeriod> periods = ForecastPeriodMatcher.generatePeriod(rule);
        Long expected = message.getExpectTime();
        for (ForecastPeriod period : periods) {
            if (period.getRight().equals(expected)) {
                lockTask(rule, message, expected);
                return;
            }
        }
        log.warn("Rule can NOT match period with expected [id:{}|tenant:{}|expected:{}]", message.getRuleId(), message.getTenantId(), expected);
    }

    private void lockTask(ForecastRule rule, ForecastMessage.RuleEnable message, Long expected) {
        log.info("Start to lock task [rule:{}|tenant:{}|expected:{}]", rule.getId(), rule.getTenantId(), expected);
        List<String> ids = forecastBiz.queryIdForTaskLock(rule, message.getCreateTime(), expected);
        SFALogContext.putVariable("count", ids.size());
        if (!ids.isEmpty()) {
            forecastBiz.lockTaskByIds(rule.getTenantId(), ids);
        } else {
            log.info("Query lock task result is empty [rule:{}|tenant:{}|expected:{}]", rule.getId(), rule.getTenantId(), expected);
        }
    }

    @Override
    public void afterRuleEnable(ForecastRule rule) {
        Boolean autoRemindBeforeEnd = rule.getAutoRemindBeforeEnd();
        if (autoRemindBeforeEnd != null && autoRemindBeforeEnd) {
            try {
                submitForecastTaskRemindBeforeEndMessage(rule);
            } catch (Exception e) {
                log.error("Submit forecast task remind before end message error", e);
            }
        }
        Boolean autoLockAfterEnd = rule.getAutoLockAfterEnd();
        if (autoLockAfterEnd != null && autoLockAfterEnd) {
            try {
                submitForecastTaskAutoLockAfterEndMessage(rule);
            } catch (Exception e) {
                log.error("Submit forecast task lock after end message error", e);
            }
        }
    }
}
