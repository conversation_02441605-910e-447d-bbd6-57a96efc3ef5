package com.facishare.crm.recycling.task.executor.enums.forecast;


public interface ForecastRuleEnums {

    enum DateSplitType {

        BY_MONTH("按月", 1, 1),
        BY_QUARTER("按季度", 2, 3),
        ;

        private final String name;
        private final Integer value;
        private final Integer monthSpan;

        DateSplitType(String name, Integer value, Integer monthSpan) {
            this.name = name;
            this.value = value;
            this.monthSpan = monthSpan;
        }

        public String getName() {
            return name;
        }

        public Integer getValue() {
            return value;
        }

        public Integer getMonthSpan() {
            return monthSpan;
        }

        public static DateSplitType valueOf(Integer value) {
            DateSplitType[] values = values();
            for (DateSplitType type : values) {
                if (type.value.equals(value)) {
                    return type;
                }
            }
            throw new UnsupportedOperationException("Unsupported forecast date split type");
        }
    }

    enum BizStatus {
        ENABLE("启用", "1"),
        DISABLE("停用", "0"),
        ENABLING("启用中", "2"),
        ;

        private final String name;
        private final String value;

        BizStatus(String name, String value) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }
}
