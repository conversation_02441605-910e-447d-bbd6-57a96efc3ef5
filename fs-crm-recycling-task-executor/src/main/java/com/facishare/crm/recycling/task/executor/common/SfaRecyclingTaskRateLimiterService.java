package com.facishare.crm.recycling.task.executor.common;

import com.github.autoconf.ConfigFactory;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.util.concurrent.RateLimiter;
import lombok.Data;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019/9/12 10:51
 */
@Component
@Data
public class SfaRecyclingTaskRateLimiterService {


    /**
     * 每个队列限制消息堆积数量
     */
    @ReloadableProperty("DiffOffsetLimit")
    public  int DiffOffsetLimit = 1000;

    private double crmRecalculateLimit;
    private double crmRecalculateDeptLimit;
    private double crmRecyclingLimit;
    private double crmSlowRecyclingLimit;
    private double objectLimitRuleLimit;
    private double objectLimitUserRuleLimit;
    private double objectLimitUserGroupLimit;
    private double objectLimitPartnerLimit;
    private double objectLimitObjectDataLimit;
    private double objectLimitOrgLimit;
    private RateLimiter crmRecalculateLimiter;
    private RateLimiter crmRecalculateDeptLimiter;
    private RateLimiter crmRecyclingLimiter;
    private RateLimiter crmSlowRecyclingLimiter;
    private RateLimiter objectLimitRuleLimiter;
    private RateLimiter objectLimitUserRuleLimiter;
    private RateLimiter objectLimitUserGroupLimiter;
    private RateLimiter objectLimitPartnerLimiter;
    private RateLimiter objectLimitObjectDataLimiter;
    private RateLimiter objectLimitOrgLimiter;


    @PostConstruct
    public void init() {
        ConfigFactory.getConfig("fs-crm-task-rate-limit", config -> {
            crmRecalculateLimit = config.getDouble("crmRecalculateLimit", 10);
            crmRecalculateDeptLimit = config.getDouble("crmRecalculateDeptLimit", 10);
            crmRecyclingLimit = config.getDouble("crmRecyclingLimit", 10);
            crmSlowRecyclingLimit = config.getDouble("crmSlowRecyclingLimit", 1d);
            objectLimitRuleLimit = config.getDouble("objectLimitRuleLimit", 10);
            objectLimitUserRuleLimit = config.getDouble("objectLimitUserRuleLimit", 10);
            objectLimitUserGroupLimit = config.getDouble("objectLimitUserGroupLimit", 10);
            objectLimitPartnerLimit = config.getDouble("objectLimitPartnerLimit", 10);
            objectLimitObjectDataLimit = config.getDouble("objectLimitObjectDataLimit", 30);
            objectLimitOrgLimit = config.getDouble("objectLimitOrgLimit", 2);
            crmRecalculateLimiter = RateLimiter.create(crmRecalculateLimit);
            crmRecalculateDeptLimiter = RateLimiter.create(crmRecalculateDeptLimit);
            crmRecyclingLimiter = RateLimiter.create(crmRecyclingLimit);
            crmSlowRecyclingLimiter = RateLimiter.create(crmSlowRecyclingLimit);
            objectLimitRuleLimiter = RateLimiter.create(objectLimitRuleLimit);
            objectLimitUserRuleLimiter = RateLimiter.create(objectLimitUserRuleLimit);
            objectLimitUserGroupLimiter = RateLimiter.create(objectLimitUserGroupLimit);
            objectLimitPartnerLimiter = RateLimiter.create(objectLimitPartnerLimit);
            objectLimitObjectDataLimiter = RateLimiter.create(objectLimitObjectDataLimit);
            objectLimitOrgLimiter = RateLimiter.create(objectLimitOrgLimit);
        });
    }

}
