package com.facishare.crm.recycling.task.executor.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Stream;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-03-02 15:14
 */

public class DateUtils {


    public static final String DATE_FORMAT_YYYY_MM_DD = "yyyy-MM-dd";

    public static final ZoneId chinaZone = ZoneId.systemDefault();


    public static Date dateParse(String dateStr, String pattern) {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        try {
            return sdf.parse(dateStr);
        } catch (Exception e) {
        }
        return null;
    }

    /**
     * 获取当天零点
     * 传入： LocalDate.now()  2019-03-02
     * 返回：                  Sat Mar 02 00:00:00 CST 2019
     *
     * @param localDate
     * @return
     */
    public static Date asDate(LocalDate localDate) {
        return Date.from(localDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * LocalDateTime 转 Date
     *
     * @param localDateTime
     * @return
     */
    public static Date asDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     *
     * @param localDateTime
     * @param days
     * @return
     */
    public static Date afterDays(LocalDateTime localDateTime, long days) {
        LocalDateTime dateTime = localDateTime.plusDays(days);
        return asDate(dateTime);
    }

    public static Long afterDays(Long time, Integer days) {
        return time + days * 24 * 60 * 60 * 1000L;
    }


    /**
     *
     * @param date
     * @param days
     * @return
     */
    public static Date afterDays(Date date, long days) {
        LocalDateTime dateTime = asLocalDateTime(date).plusDays(days);
        return asDate(dateTime);
    }

    /**
     *
     * @param date
     * @param days 精确到1位小数的天
     * @return
     */
    public static Date afterDays(Date date, Double days) {
        LocalDateTime dateTime = asLocalDateTime(date).plusSeconds(new Double(days * 24 * 60 * 60).longValue());
        return asDate(dateTime);
    }

    public static Date afterDays(Date date, double days) {
        LocalDateTime dateTime = asLocalDateTime(date).plusSeconds(Math.round(days * 24 * 60 * 60));
        return asDate(dateTime);
    }

    /**
     *
     * @param date
     * @param days 精确到1位小数的天
     * @return
     */
    public static String afterDaysFormat(Date date, long days) {
        LocalDateTime dateTime = asLocalDateTime(date).plusSeconds(new Double(days * 24 * 60 * 60).longValue());
        return dateFormat(asDate(dateTime), DATE_FORMAT_YYYY_MM_DD);
    }



    /**
     *
     * @param date
     * @param hours
     * @return
     */
    public static Date afterHours(Date date, long hours) {
        LocalDateTime dateTime = asLocalDateTime(date).plusHours(hours);
        return asDate(dateTime);
    }



    /**
     * 计算两个日期之间相差的天数
     *
     * @param date1 起始日期
     * @param date2 结束日期
     * @return
     */
    public static int daysBetween(Date date1, Date date2) {
        Instant instantDate1 = date1.toInstant();
        Instant instantDate2 = date2.toInstant();
        LocalDate localDate1 = instantDate1.atZone(chinaZone).toLocalDate();
        LocalDate localDate2 = instantDate2.atZone(chinaZone).toLocalDate();
        instantDate1.atZone(chinaZone);
        Period period = Period.between(localDate1, localDate2);
        return period.getDays();
    }

    /**
     * 计算两个日期(LocalDate)之间相差的天数
     *
     * @param date1 起始日期
     * @param date2 结束日期
     * @return
     */
    public static int daysBetween(LocalDate date1, LocalDate date2) {
        Period period = Period.between(date1, date2);
        return period.getDays();
    }


    /**
     * @param date
     * @return
     */
    public static LocalDate asLocalDate(Date date) {
        return Instant.ofEpochMilli(date.getTime()).atZone(ZoneId.systemDefault()).toLocalDate();
    }

    public static LocalDateTime asLocalDateTime(Date date) {
        return Instant.ofEpochMilli(date.getTime()).atZone(ZoneId.systemDefault()).toLocalDateTime();
    }


    public static Date parse(String date,String pattern){
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        Date parse = null;
        try {
            parse = sdf.parse(date);
        } catch (ParseException e) {

        }
        return parse;
    }


    public static Long beforeDays(Long time,int days){
        return time - days * 24 * 60 * 60 * 1000L;
    }

    public static Date beforeDays(Date time,int days){
        LocalDateTime dateTime = asLocalDateTime(time).minusDays(days);
        return asDate(dateTime);
    }


    /**
     *
     * @param date
     * @param hours
     * @return
     */
    public static Date beforeHours(Date date, long hours) {
        LocalDateTime dateTime = asLocalDateTime(date).minusHours(hours);
        return asDate(dateTime);
    }

    public static String dateFormat(Date date, String pattern) {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.format(date);
    }

    public static List<String> getBetweenDaysStr(Date beginDate, Date endDate) {
        List<String> list = new ArrayList<>();
        String beginStr = dateFormat(beginDate, DATE_FORMAT_YYYY_MM_DD);
        String endStr = dateFormat(endDate, DATE_FORMAT_YYYY_MM_DD);

        if (beginStr.equals(endStr)) {
            return list;
        }

        LocalDateTime begin = asLocalDateTime(beginDate);
        LocalDateTime end = asLocalDateTime(endDate);
        long between = ChronoUnit.DAYS.between(begin, end);
        for (int i = 0; i <= between; i++) {
            LocalDateTime dateTime = begin.plusDays(i);
            list.add(dateFormat(asDate(dateTime), DATE_FORMAT_YYYY_MM_DD));
        }
        if (list.contains(dateFormat(asDate(end), DATE_FORMAT_YYYY_MM_DD))){
            return list;
        } else {
            list.add(dateFormat(asDate(end), DATE_FORMAT_YYYY_MM_DD));
        }
        return list;
    }


    /**
     * Given a start date and end date, return the number of days between them.
     * if the same day, return 0
     * else return the number of days between them  plus 1
     * example:
     *  startDate: 2018-01-01
     *  endDate: 2018-01-02
     *  return 2
     *
     * @param beginDate The start date
     * @param endDate The end date of the range.
     * @return The number of days between the two dates.
     */
    public static Long getBetweenDays(Date beginDate, Date endDate) {
        Date begin = dateParse(dateFormat(beginDate, DATE_FORMAT_YYYY_MM_DD), DATE_FORMAT_YYYY_MM_DD);
        Date end = dateParse(dateFormat(endDate, DATE_FORMAT_YYYY_MM_DD), DATE_FORMAT_YYYY_MM_DD);
        Duration between = Duration.between(asLocalDateTime(begin), asLocalDateTime(end));
        long days = between.toDays();
        if (days == 0) {
            return 0L;
        }
        return days + 1;
    }


    // 辅助方法：判断两个日期是否是同一天
    public static boolean isSameDay(Date date1, Date date2) {
        Calendar cal1 = Calendar.getInstance();
        Calendar cal2 = Calendar.getInstance();
        cal1.setTime(date1);
        cal2.setTime(date2);
        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
                cal1.get(Calendar.DAY_OF_YEAR) == cal2.get(Calendar.DAY_OF_YEAR);
    }


    public static List<String> getBetweenDateListsStr(Date start, Date end) {
        List<String> list = new ArrayList<>();
        // LocalDate默认的时间格式为2020-02-02
        LocalDate startDate = asLocalDate(start);
        LocalDate endDate = asLocalDate(end);
        long distance = ChronoUnit.DAYS.between(startDate, endDate);
        if (distance < 1) {
            return list;
        }
        Stream.iterate(startDate, d -> d.plusDays(1)).limit(distance + 1).forEach(f -> list.add(f.toString()));
        return list;
    }
}
