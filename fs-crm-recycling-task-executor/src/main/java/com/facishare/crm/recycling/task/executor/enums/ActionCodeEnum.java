package com.facishare.crm.recycling.task.executor.enums;

/**
 * 客户动作
 */
public enum ActionCodeEnum {


    /**
     * 新建
     */
    ADD("Add", "新建"),

    /**
     * 跟进动作
     */
    FOLLOW("follow", "跟进动作"),
    /**
     * 作废
     */
    INVALID("invalid", "作废"),

    RECOVER("recover", "恢复"),

    MOVE("move", "转移"),

    REMOVE("remove", "移除公海"),

    /**
     * 领取
     */
    CHOOSE("Choose", "领取"),

    CHANGE_OWNER("changeOwner", "更换负责人"),


    RETURN("return", "退回"),

    ALLOCATE("allocate", "分配"),

    CHANGE_RULE("changeRule", "收回规则更新"),

    CHANGE_DEAL("changeDeal", "客户成交状态修改"),

    CHANGE_REMIND("changeRemind","提醒修改"),

    //------------线索特有的---------
    /**
     * 线索跟进中 动作
     */
    DEAL("deal", "转换"),

    RECYCLING("recycling", "收回"),


    CLOSE("Close","无效"),
    EXTEND("Extend","延期");



    private String actionCode;
    private String desc;

    ActionCodeEnum(String actionCode, String desc) {
        this.actionCode = actionCode;
        this.desc = desc;
    }


    public static ActionCodeEnum actionCodeOf(String actionCode) {
        for (ActionCodeEnum value : values()) {
            if (value.getActionCode().equalsIgnoreCase(actionCode)) {
                return value;
            }
        }
        return FOLLOW;
    }

    public String getActionCode() {
        return actionCode;
    }

    public void setActionCode(String actionCode) {
        this.actionCode = actionCode;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
