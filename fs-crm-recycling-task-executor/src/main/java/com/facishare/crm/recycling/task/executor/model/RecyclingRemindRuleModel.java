package com.facishare.crm.recycling.task.executor.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class RecyclingRemindRuleModel {
    @JSONField(name = "recycling_remind_rule_id")
    @JsonProperty(value = "recycling_remind_rule_id")
    String id;
    @JSONField(name = "recycling_rule_id")
    @JsonProperty(value = "recycling_rule_id")
    String recyclingRuleId;
    @JSONField(name = "data_id")
    @JsonProperty(value = "data_id")
    String dataId;
    @JSONField(name = "rule_type")
    @JsonProperty(value = "rule_type")
    String ruleType;
    @JSONField(name = "remind_days")
    @JsonProperty(value = "remind_days")
    Integer remindDays;
    @JSONField(name = "group_id")
    @JsonProperty(value = "group_id")
    String groupId;
}
