package com.facishare.crm.recycling.task.executor.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.recycling.task.executor.biz.CustomerBiz;
import com.facishare.crm.recycling.task.executor.model.OverTimeFollowMessage;
import com.facishare.crm.recycling.task.executor.service.NomonTaskService;
import com.facishare.crm.recycling.task.executor.service.impl.LeadsRecalculate;
import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.Date;

import static com.facishare.crm.recycling.task.executor.util.ConstantUtils.LEADS_OBJ;


/**
 * @Description
 * <AUTHOR>
 * @Date 2021/3/26 11:00
 */

@Slf4j
@Component
public class OverTimeFollowListener extends BaseRecalculateListener implements ApplicationListener<ContextRefreshedEvent> {

    private AutoConfMQPushConsumer consumer;

    @Autowired
    private LeadsRecalculate leadsRecalculate;

    @Autowired
    protected CustomerBiz customerBiz;

    @Autowired
    private NomonTaskService nomonTaskService;

    @PostConstruct
    public void init() {
        consumer = getConsumer(SFA_OVERTIME_FOLLOW_SECTION);
    }

    @Override
    String getConsumerName() {
        return SFA_OVERTIME_FOLLOW_SECTION;
    }

    @Override
    public void consumeMessage(MessageExt body) {
        OverTimeFollowMessage msg = JSON.parseObject(body.getBody(), OverTimeFollowMessage.class);
        log.info("收到超时跟进消息：{}", msg);
        String dataId = msg.getObjectId();
        String tenantId = msg.getTenantId();
        IObjectData objectData = customerBiz.getObjectById(tenantId, dataId, LEADS_OBJ);
        Date executeTime = leadsRecalculate.getExpireTimeSkipHolidays(msg.getExecuteTime(), objectData);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("tenantId", tenantId);
        jsonObject.put("objectId", dataId);
        log.info("收到超时跟进消息 计算结束 {}, executeTime：{}", msg,executeTime);
        log.debug("LeadsOverTimeTaskService.createOrUpdateTask,tenantId:{},objectId:{}", tenantId, dataId);
        nomonTaskService.createOrUpdateTask("leads_over_time", tenantId, dataId, executeTime, jsonObject.toJSONString(), 0);
    }

    @PreDestroy
    public void close() {
        consumer.close();
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (consumer != null && event.getApplicationContext().getParent() == null) {
            consumer.start();
        }
    }
}
