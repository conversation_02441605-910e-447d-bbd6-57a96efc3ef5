package com.facishare.crm.recycling.task.executor.model;

import com.facishare.paas.metadata.api.IObjectData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * @Description 重算的message
 * <AUTHOR>
 * @Date 2019-02-23 17:33
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RecalculateMessage {

    private String tenantId;

    private String objectApiName;

    /**
     * actionCode是规则变更时: 该字段为公海id
     * 其他情况为 数据的id
     */
    private String objectId;

    /**
     * 动作
     */
    private String actionCode;

    @Deprecated
    private Date executeTime;


    /**
     * 目标id
     */
    private String targetId;


    /**
     * 移除公海并更换负责人
     */
    private String movedOwnerId;

    private IObjectData objectData;

    /**
     * 延期天数
     */
    private Double extendDays;

    /**
     * false:非公海
     * true:公海
     */
    private Boolean highSeas;

    /**
     * 非公海部门id
     */
    private List<String> deptIds;

}
