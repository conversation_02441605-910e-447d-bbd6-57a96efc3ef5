package com.facishare.crm.recycling.task.executor.util;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020/4/9 16:04
 */
@Slf4j
public class QueueStrategyUtil {

    private static final LoadingCache<String, AtomicInteger> recorder = CacheBuilder.newBuilder()
            .expireAfterAccess(1, TimeUnit.MINUTES).maximumSize(20000)
            .build(new CacheLoader<String, AtomicInteger>() {
                @Override
                public AtomicInteger load(String s) {
                    return new AtomicInteger(0);
                }
            });

    /**
     * x 上一分钟触发次数
     *
     * @param fastQueueAllowCount  一分钟,快队列允许的最大次数
     * @param maxTotalTriggerCount 一分钟内,触发允许在快队列的最大触发次数
     * @param x                    当前和前一分钟 触发的次数
     * @return 快队列允许的最大的次数
     */
    private static double getLimit(double fastQueueAllowCount, double maxTotalTriggerCount, double x) {
        double rst = fastQueueAllowCount * (-Math.sqrt(x) / Math.sqrt(maxTotalTriggerCount) + 1);
        return rst < 0 ? 0 : rst;
    }

    /**
     * x 记录一分钟触发次数
     *
     * @param tenantId 企业id
     * @return
     */
    public static void count(String tenantId) {
        try {
            recorder.get(tenantId + "-" + format(LocalDateTime.now())).incrementAndGet();
        } catch (Exception e) {
        }
    }

    /**
     * x 上一分钟触发次数
     *
     * @param tenantId             企业id
     * @param fastQueueAllowCount  快队列允许的最大次数/一分钟
     * @param maxTotalTriggerCount 一分钟内,触发允许在快队列的最大触发次数
     * @return 快队列允许的最大的次数
     */
    public static boolean toSlow(String tenantId, double fastQueueAllowCount, double maxTotalTriggerCount) {
        LocalDateTime now = LocalDateTime.now();
        String currentKey = tenantId + "-" + format(now);
        String lastKey = tenantId + "-" + format(now.minusMinutes(1));
        try {
            int count = recorder.get(currentKey).get();
            double limit = getLimit(fastQueueAllowCount, maxTotalTriggerCount, Math.max(recorder.get(lastKey).get(), count));
            log.info("limit:{},currentCount:{}", limit, count);
            return count > limit;
        } catch (ExecutionException e) {
        }
        return false;
    }

    private final static DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd_HH_mm");

    private static String format(LocalDateTime date) {
        return dateTimeFormatter.format(date);
    }



}
