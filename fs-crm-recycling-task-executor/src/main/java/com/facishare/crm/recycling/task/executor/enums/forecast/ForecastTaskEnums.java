package com.facishare.crm.recycling.task.executor.enums.forecast;


public interface ForecastTaskEnums {

    String getName();

    Integer getValue();

    enum BizStatus implements ForecastTaskEnums {
        COMMITTED("已提交", 1),
        UNCOMMITTED("未提交", 0),
        ;

        private final String name;
        private final Integer value;

        BizStatus(String name, Integer value) {
            this.name = name;
            this.value = value;
        }

        @Override
        public String getName() {
            return name;
        }

        @Override
        public Integer getValue() {
            return value;
        }
    }
}
