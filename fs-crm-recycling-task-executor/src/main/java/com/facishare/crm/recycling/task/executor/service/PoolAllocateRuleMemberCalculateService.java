package com.facishare.crm.recycling.task.executor.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.recycling.task.executor.biz.CommonBiz;
import com.facishare.crm.recycling.task.executor.model.PoolAllocateRuleMemberCalculateModel;
import com.facishare.crm.recycling.task.executor.util.CommonSqlUtils;
import com.facishare.crm.recycling.task.executor.util.ConvertUtils;
import com.facishare.crm.recycling.task.executor.util.ObjectDataUtils;
import com.facishare.crm.recycling.task.executor.util.SearchUtil;
import com.facishare.crm.recycling.task.executor.util.TableNameConstants;
import com.facishare.crm.recycling.task.executor.util.sql.DeleteSql;
import com.facishare.crm.recycling.task.executor.util.sql.InsertSql;
import com.facishare.crm.recycling.task.executor.util.sql.SelectSql;
import com.facishare.crm.recycling.task.executor.util.sql.UpdateSql;
import com.facishare.crm.recycling.task.executor.util.sql.WhereSql;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.MetaDataMiscServiceImpl;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.CommonSqlOperator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.impl.search.WhereParam;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PoolAllocateRuleMemberCalculateService {

	@Autowired
	private CommonBiz commonBiz;
	@Autowired
	private MetaDataMiscServiceImpl metaDataMiscService;

	public void calculateMemberWheres(PoolAllocateRuleMemberCalculateModel.Arg arg) {
		String ruleId = arg.getRuleId();
		String tenantId = arg.getTenantId();
		List<String> memberIdList = arg.getMemberIdList();
		String objectApiName = arg.getObjectApiName();
		String poolId = arg.getPoolId();


		// 查询分配规则成员条件
		List<IObjectData> memberList = CommonSqlUtils.queryObjectDataById(tenantId, TableNameConstants.BIZ_POOL_ALLOCATE_RULE_MEMBER, objectApiName, memberIdList);
		if (CollectionUtils.isEmpty(memberList)) {
			log.warn("查询分配规则成员为空，arg:{}", arg);
			return;
		}

		String calculateId = metaDataMiscService.generateId();
		List<Map<String, Object>> weightMemberList = Lists.newArrayList();

		// 根据条件查询匹配的人员
		List<String> idList = Lists.newArrayList();
		for (IObjectData ruleMember : memberList) {
			String memberWheres = ruleMember.get("member_wheres").toString();
			List<String> matchedMemberIdList = getMemberIdListByWheres(tenantId, memberWheres);
			if (CollectionUtils.isEmpty(matchedMemberIdList)) {
				log.warn("member_wheres没有匹配的人员数据，arg:{}, memberId:{}, member_wheres:{}", arg, ruleMember.getId(), memberWheres);
				continue;
			}
			idList.addAll(matchedMemberIdList);
			for (String id : matchedMemberIdList) {
				Map<String, Object> initWeightMember = getInitWeightMember(tenantId, ruleId, objectApiName, poolId);
				initWeightMember.put("member_weight_rule_id", ruleMember.getId());
				initWeightMember.put("calculate_id", calculateId);
				initWeightMember.put("weight", ruleMember.get("weight", Integer.class));
				initWeightMember.put("member_id", id);
				initWeightMember.put("priority", ObjectDataUtils.getIntegerOrDefault(ruleMember, "priority", 0));
				weightMemberList.add(initWeightMember);
			}
		}

		// 如果是更新，找到上次匹配，这次不匹配的record表记录，将已分配额归零
		// 删除老的计算出来的权重成员
		if (arg.isUpdate()) {
			makeZeroByMismatched(tenantId, ruleId, objectApiName, idList);
			deleteOldWeightMember(tenantId, arg);
		}

		// 插入匹配人员记录, 更新rule表calculate_id字段
		CommonSqlUtils.insertDataBySql(tenantId, TableNameConstants.BIZ_POOL_ALLOCATE_RULE_MEMBER_WEIGHT, weightMemberList);

		List<WhereParam> updateRuleWhereParamList = CommonSqlUtils.getCommonWhereParams(tenantId, objectApiName);
		CommonSqlUtils.addWhereParam(updateRuleWhereParamList, "id", CommonSqlOperator.EQ, Lists.newArrayList(ruleId));
		Map<String, Object> updateFieldMap = Maps.newHashMap();
		updateFieldMap.put("calculate_id", calculateId);
		CommonSqlUtils.updateData(tenantId, TableNameConstants.BIZ_POOL_ALLOCATE_RULE, updateFieldMap, updateRuleWhereParamList);
	}

	private void makeZeroByMismatched(String tenantId, String ruleId, String objectApiName, List<String> matchedIdList) {
		List<WhereParam> whereParams = CommonSqlUtils.getCommonWhereParams(tenantId, objectApiName);
		CommonSqlUtils.addWhereParam(whereParams, "allocate_rule_id", CommonSqlOperator.EQ, Lists.newArrayList(ruleId));
		List<IObjectData> oldWeightMemberList = CommonSqlUtils.queryObjectData(tenantId, TableNameConstants.BIZ_POOL_ALLOCATE_RULE_MEMBER_WEIGHT, whereParams);
		List<Object> oldIdList = oldWeightMemberList.stream().map(m -> m.get("member_id")).collect(Collectors.toList());
		oldIdList.removeIf(matchedIdList::contains);

		if (CollectionUtils.isEmpty(oldIdList)) {
			return;
		}

		List<WhereParam> updateWhereList = Lists.newArrayList();
		CommonSqlUtils.addWhereParam(updateWhereList, Tenantable.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(tenantId));
		CommonSqlUtils.addWhereParam(updateWhereList, "allocate_rule_id", CommonSqlOperator.EQ, Lists.newArrayList(ruleId));
		CommonSqlUtils.addWhereParam(updateWhereList, "employee_id", CommonSqlOperator.IN, oldIdList);
		Map<String, Object> updateField = new HashMap<>();
		updateField.put("allocated_number", 0);
		CommonSqlUtils.updateData(tenantId, TableNameConstants.BIZ_POOL_ALLOCATE_RULE_MEMBER_RECORD, updateField, updateWhereList);
	}

	private void deleteOldWeightMember(String tenantId, PoolAllocateRuleMemberCalculateModel.Arg arg) {
		List<IObjectData> allocateRuleList = CommonSqlUtils.queryObjectDataById(tenantId, TableNameConstants.BIZ_POOL_ALLOCATE_RULE, arg.getObjectApiName(), Lists.newArrayList(arg.getRuleId()));
		IObjectData allocateRule;
		if (CollectionUtils.isNotEmpty(allocateRuleList)) {
			allocateRule = allocateRuleList.get(0);
			String oldCalculateId = allocateRule.get("calculate_id", String.class);
			if (StringUtils.isNotEmpty(oldCalculateId)) {
				List<WhereParam> whereParams = Lists.newArrayList();
				CommonSqlUtils.addWhereParam(whereParams,"tenant_id", CommonSqlOperator.EQ, Lists.newArrayList(tenantId));
				CommonSqlUtils.addWhereParam(whereParams,"is_deleted", CommonSqlOperator.EQ, Lists.newArrayList(0));
				CommonSqlUtils.addWhereParam(whereParams,"calculate_id", CommonSqlOperator.EQ, Lists.newArrayList(oldCalculateId));
				CommonSqlUtils.addWhereParam(whereParams,"allocate_rule_id", CommonSqlOperator.EQ, Lists.newArrayList(arg.getRuleId()));

				Map<String, Object> updateFieldList = Maps.newHashMap();
				updateFieldList.put("is_deleted", 1);
				CommonSqlUtils.updateData(tenantId, TableNameConstants.BIZ_POOL_ALLOCATE_RULE_MEMBER_WEIGHT, updateFieldList, whereParams);
			}
		} else {
			log.error("分配规则对象为空，arg:{}", arg);
			throw new RuntimeException("查询分配规则对象为空");
		}
	}


	private Map<String, Object> getInitWeightMember(String tenantId, String ruleId, String objectApiName,
			String poolId) {
		Map<String, Object> initWeightMember = Maps.newHashMap();
		initWeightMember.put("id", metaDataMiscService.generateId());
		initWeightMember.put("tenant_id", tenantId);
		initWeightMember.put("object_api_name", objectApiName);
		initWeightMember.put("pool_id", poolId);
		initWeightMember.put("allocate_rule_id", ruleId);
		initWeightMember.put("is_deleted", 0);
		initWeightMember.put("created_by", User.SUPPER_ADMIN_USER_ID);
		initWeightMember.put("create_time", System.currentTimeMillis());
		return initWeightMember;
	}


	private List<String> getMemberIdListByWheres(String tenantId, String memberWheres) {
		int pageSize = 100;
		int offset = 0;
		SearchTemplateQuery searchTemplateQuery = getPersonnelObjSearchTemplateByWheres(tenantId, memberWheres);
		searchTemplateQuery.setLimit(pageSize);
		searchTemplateQuery.setOffset(offset);
		List<String> dataIds = Lists.newArrayList();
		int maxCount = 1000;
		while (maxCount >= 0) {
			--maxCount;
			QueryResult<IObjectData> queryResult = commonBiz
					.queryBySearchTemplate(tenantId, "PersonnelObj", searchTemplateQuery);
			if (queryResult.getData() == null) {
				break;
			}
			if (CollectionUtils.isEmpty(queryResult.getData())) {
				break;
			}
			dataIds.addAll(queryResult.getData().stream().map(x -> x.get("user_id").toString()).collect(Collectors.toSet()));
			offset += pageSize;
			if (queryResult.getTotalNumber() <= offset) {
				break;
			}
			searchTemplateQuery.setOffset(offset);
		}
		return dataIds;
	}

	private SearchTemplateQuery getPersonnelObjSearchTemplateByWheres(String tenantId, String memberWheres) {
		List<Wheres> wheresList = Lists.newArrayList();
		ConvertUtils.convert2WheresList(memberWheres, wheresList);
		IObjectDescribe objectDescribe = commonBiz.getObjectDescribe(tenantId, "PersonnelObj");
		ConvertUtils.convertFilter(wheresList, objectDescribe);
		SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
		searchTemplateQuery.setWheres(wheresList);
		searchTemplateQuery.setPermissionType(0);
		searchTemplateQuery.setOrders(Lists.newArrayList(new OrderBy("id", true)));
		return searchTemplateQuery;
	}

	/**
	 * 停用删除员工 ，删除分配规则中的数据
	 */
	public void deleteMemberById(String tenantId, List<String> memberIdList) {
		List<WhereParam> whereParams = Lists.newArrayList();
		CommonSqlUtils.addWhereParam(whereParams,"tenant_id", CommonSqlOperator.EQ, Lists.newArrayList(tenantId));
		CommonSqlUtils.addWhereParam(whereParams,"is_deleted", CommonSqlOperator.EQ, Lists.newArrayList(0));
		CommonSqlUtils.addWhereParam(whereParams,"member_id", CommonSqlOperator.IN, Lists.newArrayList(memberIdList));

		Map<String, Object> updateFieldList = Maps.newHashMap();
		updateFieldList.put("is_deleted", 1);
		log.info("停用删除成员delete:{}", JSON.toJSONString(whereParams));
		CommonSqlUtils.updateData(tenantId, TableNameConstants.BIZ_POOL_ALLOCATE_RULE_MEMBER_WEIGHT, updateFieldList, whereParams);
		CommonSqlUtils.updateData(tenantId, TableNameConstants.BIZ_POOL_ALLOCATE_RULE_MEMBER, updateFieldList, whereParams);

		List<WhereParam> deleteWheres = Lists.newArrayList();
		CommonSqlUtils.addWhereParam(deleteWheres,"tenant_id", CommonSqlOperator.EQ, Lists.newArrayList(tenantId));
		CommonSqlUtils.addWhereParam(deleteWheres,"employee_id", CommonSqlOperator.IN, Lists.newArrayList(memberIdList));
		CommonSqlUtils.deleteDataBySql(tenantId, TableNameConstants.BIZ_POOL_ALLOCATE_RULE_MEMBER_RECORD, deleteWheres);
	}

	/**
	 * 新增员工，若匹配分配规则条件，插入数据
	 */
	public void insertMemberByWheres(String tenantId, String memberId, String apiName) {
		// 查询该企业 member_wheres 不为空的线索分配规则成员
		List<IObjectData> ruleMemberList = this.getByWheresMember(tenantId, apiName);
		if (CollectionUtils.isEmpty(ruleMemberList)) {
			return;
		}
		List<IFilter> userIdFilterList = Lists.newArrayList();
		SearchUtil.fillFilterEq(userIdFilterList, "user_id", memberId);
		List<Map<String, Object>> insertWeightMemberList = Lists.newArrayList();
		for (IObjectData ruleMember : ruleMemberList) {
			// 匹配该成员有没有满足 当前ruleMember  wheres
			String memberWheres = ruleMember.get("member_wheres").toString();
			String ruleId = ruleMember.get("allocate_rule_id", String.class);
			String poolId = ruleMember.get("pool_id", String.class);

			SearchTemplateQuery searchTemplateQuery = getPersonnelObjSearchTemplateByWheres(tenantId, memberWheres);
			searchTemplateQuery.addFilters(userIdFilterList);
			QueryResult<IObjectData> queryResult = commonBiz
					.queryBySearchTemplate(tenantId, "PersonnelObj", searchTemplateQuery);

			if (CollectionUtils.isNotEmpty(queryResult.getData())) {
				Map<String, Object> initWeightMember = getInitWeightMember(tenantId, ruleId, "LeadsPoolObj", poolId);
				initWeightMember.put("member_weight_rule_id", ruleMember.getId());
				initWeightMember.put("calculate_id", getCalculateIdByRuleId(tenantId, ruleId, apiName));
				initWeightMember.put("weight", ruleMember.get("weight", Integer.class));
				initWeightMember.put("priority", ObjectDataUtils.getIntegerOrDefault(ruleMember, "priority", 0));
				initWeightMember.put("member_id", memberId);
				insertWeightMemberList.add(initWeightMember);
			}
		}
		if (CollectionUtils.isNotEmpty(insertWeightMemberList)) {
			CommonSqlUtils.insertDataBySql(tenantId, TableNameConstants.BIZ_POOL_ALLOCATE_RULE_MEMBER_WEIGHT, insertWeightMemberList);
		}
	}

	public void recalculateByWheres(String tenantId, String memberId, String apiName) {
		// 查询该企业 member_wheres 不为空的线索分配规则成员
		List<IObjectData> ruleMemberList = this.getByWheresMember(tenantId, apiName);
		if (CollectionUtils.isEmpty(ruleMemberList)) {
			return;
		}

		List<IFilter> userIdFilterList = Lists.newArrayList();
		SearchUtil.fillFilterEq(userIdFilterList, "user_id", memberId);
		List<Map<String, Object>> insertWeightMemberList = Lists.newArrayList();
		for (IObjectData ruleMember : ruleMemberList) {
			// 查询该rule下该成员是否之前匹配
			List<IObjectData> weightMemberList = SelectSql.from(TableNameConstants.BIZ_POOL_ALLOCATE_RULE_MEMBER_WEIGHT)
					.where(WhereSql.of(tenantId).andObjectApiNameEq(apiName).andNotDelete().andEq("member_id", memberId)
							.andEq("member_weight_rule_id", ruleMember.getId())).withAdminContext()
					.selectToObjectData();

			// 匹配该成员有没有满足 当前ruleMember  wheres
			String memberWheres = ruleMember.get("member_wheres").toString();
			String ruleId = ruleMember.get("allocate_rule_id", String.class);
			String poolId = ruleMember.get("pool_id", String.class);

			SearchTemplateQuery searchTemplateQuery = getPersonnelObjSearchTemplateByWheres(tenantId, memberWheres);
			searchTemplateQuery.addFilters(userIdFilterList);
			QueryResult<IObjectData> queryResult = commonBiz
					.queryBySearchTemplate(tenantId, "PersonnelObj", searchTemplateQuery);
			// 前后都匹配
			if (CollectionUtils.isNotEmpty(weightMemberList) && CollectionUtils.isNotEmpty(queryResult.getData())) {
				continue;
			}
			// 前后都不匹配
			if (CollectionUtils.isEmpty(weightMemberList) && CollectionUtils.isEmpty(queryResult.getData())) {
				continue;
			}
			// 之前不匹配，修改后匹配
			if (CollectionUtils.isEmpty(weightMemberList) && CollectionUtils.isNotEmpty(queryResult.getData())) {
				Map<String, Object> initWeightMember = getInitWeightMember(tenantId, ruleId, "LeadsPoolObj", poolId);
				initWeightMember.put("member_weight_rule_id", ruleMember.getId());
				initWeightMember.put("calculate_id", getCalculateIdByRuleId(tenantId, ruleId, apiName));
				initWeightMember.put("weight", ruleMember.get("weight", Integer.class));
				initWeightMember.put("priority", ObjectDataUtils.getIntegerOrDefault(ruleMember, "priority", 0));
				initWeightMember.put("member_id", memberId);
				insertWeightMemberList.add(initWeightMember);
			}
			// 之前匹配，修改后不匹配
			if (CollectionUtils.isNotEmpty(weightMemberList) && CollectionUtils.isEmpty(queryResult.getData())) {
				// 删除weight表记录
				DeleteSql.from(TableNameConstants.BIZ_POOL_ALLOCATE_RULE_MEMBER_WEIGHT).where(
						WhereSql.of(tenantId).andEq("member_weight_rule_id", ruleMember.getId())
						.andEq("member_id", memberId).andObjectApiNameEq(apiName).andNotDelete()
				).withAdminContext().logicDelete();
				// 已分配归零
				UpdateSql.table(TableNameConstants.BIZ_POOL_ALLOCATE_RULE_MEMBER_RECORD)
						.set("allocated_number", 0)
						.where(WhereSql.of(tenantId).andEq("allocate_rule_id", ruleId).andEq("employee_id", memberId)
				).withAdminContext().update();
			}
		}
		if (CollectionUtils.isNotEmpty(insertWeightMemberList)) {
			InsertSql.to(tenantId, TableNameConstants.BIZ_POOL_ALLOCATE_RULE_MEMBER_WEIGHT)
					.addAll(insertWeightMemberList).withAdminContext()
					.insertSql();
		}
	}

	private List<IObjectData> getByWheresMember(String tenantId, String apiName) {
		List<WhereParam> queryRuleMemberParamList = CommonSqlUtils.getCommonWhereParams(tenantId, apiName);
		CommonSqlUtils.addWhereParam(queryRuleMemberParamList, "member_wheres", CommonSqlOperator.ISN, Lists.newArrayList());
		return CommonSqlUtils.queryObjectData(tenantId, TableNameConstants.BIZ_POOL_ALLOCATE_RULE_MEMBER, queryRuleMemberParamList);
	}

	private String getCalculateIdByRuleId(String tenantId, String ruleId, String objectApiName) {
		List<IObjectData> ruleList = CommonSqlUtils.queryObjectDataById(tenantId, TableNameConstants.BIZ_POOL_ALLOCATE_RULE, objectApiName, Lists.newArrayList(ruleId));
		if (CollectionUtils.isEmpty(ruleList)) {
			log.error("数据不存在，tenantId:{}, ruleId:{}", tenantId, ruleId);
			throw new RuntimeException("数据不存在");
		} else {
			return ruleList.get(0).get("calculate_id", String.class);
		}
	}

}
