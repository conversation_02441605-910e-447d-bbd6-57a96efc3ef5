package com.facishare.crm.recycling.task.executor.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.recycling.task.executor.biz.CustomerBiz;
import com.facishare.crm.recycling.task.executor.biz.HighSeasBiz;
import com.facishare.crm.recycling.task.executor.biz.RecyclingBiz;
import com.facishare.crm.recycling.task.executor.biz.RemindBiz;
import com.facishare.crm.recycling.task.executor.common.RecyclingTaskGray;
import com.facishare.crm.recycling.task.executor.common.SfaRecyclingTaskRateLimiterService;
import com.facishare.crm.recycling.task.executor.enums.*;
import com.facishare.crm.recycling.task.executor.model.*;
import com.facishare.crm.recycling.task.executor.service.FrameworkConfigService;
import com.facishare.crm.recycling.task.executor.service.SFAOpenApiMqService;
import com.facishare.crm.recycling.task.executor.util.SFAAuditLogRecyclingConvert;
import com.facishare.crm.sfa.audit.log.SFAAuditLog;
import com.facishare.crm.sfa.audit.log.context.SFALogContext;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.LogServiceImpl;
import com.facishare.paas.appframework.metadata.MetaDataMiscService;
import com.facishare.paas.appframework.metadata.MetaDataServiceImpl;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.support.GDSHandler;
import com.facishare.qixin.plugin.model.arg.BatchSessionSandwichUpdatedArg;
import com.facishare.qixin.plugin.model.arg.SessionSandwichUpdatedArg;
import com.facishare.qixin.plugin.service.SessionSandwichApiService;
import com.fxiaoke.paas.gnomon.api.NomonProducer;
import com.fxiaoke.paas.gnomon.api.entity.NomonDeleteMessage;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.recycling.task.executor.service.FrameworkConfigService.CONTACT_OWNER_RULE_SETTING;
import static com.facishare.crm.recycling.task.executor.util.ConstantUtils.*;
import static com.facishare.crm.recycling.task.executor.util.I18NKey.*;

/**
 * @Description 客户回收
 * <AUTHOR>
 * @Date 2019-02-18 22:01
 */

@Component
@Slf4j
public class AccountRecycling extends AbstractRecycling {


    @Autowired
    private CustomerBiz customerBiz;

    @Autowired
    private HighSeasBiz highSeasBiz;


    @Autowired
    private GDSHandler gdsHandler;

    @Autowired
    private SessionSandwichApiService sessionSandwichApiService;

    @Autowired
    private NomonProducer nomonProducer;

    @Autowired
    private AccountRecalculate accountRecalculate;

    @Autowired
    private IObjectDescribeService objectDescribeService;

    @Autowired
    private RemindBiz remindBiz;

    @Autowired
    private LogServiceImpl logService;

    @Autowired
    private MetaDataMiscService metaDataMiscService;

    @Autowired
    private MetaDataServiceImpl metaDataService;

    @Autowired
    private SFAOpenApiMqService sfaOpenApiMqService;

    @Autowired
    private RecyclingBiz recyclingBiz;

    @Autowired
    private RecyclingTaskGray recyclingTaskGray;

    @Autowired
    private FrameworkConfigService frameworkConfigService;

    @Autowired
    private SfaRecyclingTaskRateLimiterService sfaRecyclingTaskRateLimiterService;



    @SFAAuditLog(bizName = "#bizName", entityClass = RecyclingMessage.class, convertClass = SFAAuditLogRecyclingConvert.class,
            status = "#status", messageId = "#messageId", cost1 = "#cost1")
    @Override
    public void execute(RecyclingMessage message) {
        super.execute(message);
        try {
            IObjectData objectData = message.getObjectData();
            if (objectData == null) {
                return;
            }
            log.info("AccountRecycling:{}", message);
            highSeasRecycling(message);
        } catch (Exception e) {
            SFALogContext.putVariable("status", false);
            log.error("AccountRecycling:{}", message, e);
            throw new RuntimeException(e);
        }
    }


    /**
     * 公海客户回收规则
     *
     * @param message
     */
    public void highSeasRecycling(RecyclingMessage message) {
        String tenantId = message.getTenantId();
        String objectId = message.getObjectId();
        String highSeasId = null;
        IObjectData customer = message.getObjectData();
        boolean isHighSeas = true;
        String targetId = message.getTargetId();
        List<RecyclingRule> recyclingRules = null;
        List<RecyclingRuleInfoModel> recyclingRuleInfoModelList = new ArrayList<>();
        SFALogContext.putVariable("status", true);
        if (StringUtils.isBlank(customer.get(HIGH_SEAS_ID, String.class))) {
            isHighSeas = false;
        } else {
            highSeasId = customer.get(HIGH_SEAS_ID).toString();
        }

        if (customer.get(EXPIRE_TIME) == null || StringUtils.isBlank(customer.get(EXPIRE_TIME).toString())) {
            log.warn("highSeasRecycling expire_time is null:{},{}", customer.getId(), tenantId);
            accountRecalculate.sendRecalculate(tenantId, objectId, ACCOUNT_OBJ, new Date());
            return;
        }

        if (System.currentTimeMillis() - customer.get(EXPIRE_TIME, Long.class) < 0) {
            log.warn("is not time to Recycling sendRecalculate:{},{}", tenantId, objectId);
            accountRecalculate.sendRecalculate(tenantId, objectId, ApiNameEnum.ACCOUNT_OBJ.getApiName(), new Date());
            return;
        }

        if (LifeStatusEnum.INVALID.getValue().equals(customer.get(LIFE_STATUS))) {
            log.info("customer is invalid :{},{}", tenantId, objectId);
            return;
        }


        if (CollectionUtils.empty(customer.getOwner()) || BizStatusEnum.UNALLOCATED.getValue().equals(customer.get(BIZ_STATUS, String.class))) {
            log.warn("highSeasRecycling owner is null ConvertUtilsor biz_status is unallocated:{}", customer);
            customerBiz.clearExpireTime(customer);
            return;
        }
        if (!isHighSeas) {
            String ownerId = customer.getOwner().get(0);
            String deptId = customerBiz.getDept(tenantId, ownerId);
            if (StringUtils.isBlank(deptId)) {
                log.warn("非公海客户回收 deptId is null ,tenantId:{},ownerId:{}", tenantId, ownerId);
                customerBiz.clearExpireTime(customer);
                return;
            }
            log.warn("nonHighSeas recycling tenantId:{},ownerId:{},deptId:{}", tenantId, ownerId, deptId);
            recyclingRuleInfoModelList = recyclingBiz.getRecyclingRule(tenantId, deptId, ApiNameEnum.HIGH_SEAS_OBJ.getApiName());
            if (CollectionUtils.empty(recyclingRuleInfoModelList)) {
                log.info("nonHighSeas Customer recyclingRules is empty clearExpireTime tenantId:{},objectId:{},deptId:{} ", tenantId, objectId, deptId);
                customerBiz.clearExpireTime(customer);
                return;
            }
        }

        if (recyclingTaskGray.toSlowRecyclingTenantId(message.getTenantId())){
            sfaRecyclingTaskRateLimiterService.getCrmSlowRecyclingLimiter().acquire();
        }else {
            sfaRecyclingTaskRateLimiterService.getCrmRecyclingLimiter().acquire();
        }

        // 回收的目标公海
        IObjectData highSeasObjData = null;
        IObjectDescribe objectDescribe = null;
        if (StringUtils.isBlank(targetId)) {
            if (isHighSeas) {
                recyclingRuleInfoModelList = recyclingBiz.getRecyclingRule(tenantId, customer.get(HIGH_SEAS_ID, String.class), HIGH_SEAS_OBJ);
                log.info(" targetId is null getrecyclingRule tenantId:{},objectId:{}", tenantId, objectId);
            } else {
                log.info("历史非公海客户回收:tenantId:{},objectId:{}", tenantId, objectId);
            }
            try {
                objectDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, ApiNameEnum.ACCOUNT_OBJ.getApiName());
            } catch (MetadataServiceException e) {
                log.error("findByTenantIdAndDescribeApiName {}", message, e);
                throw new RuntimeException(e);
            }

            String mappingRuleHighSeasId = getHighSeasIdMappingRule(recyclingRuleInfoModelList, customer, ApiNameEnum.ACCOUNT_OBJ.getApiName(), objectDescribe);
            if (StringUtils.isNotBlank(mappingRuleHighSeasId)) {
                message.setTargetId(mappingRuleHighSeasId);
                log.info("历史客户回收,tenantId:{},mappingRuleHighSeasId:{}", tenantId, mappingRuleHighSeasId);
                highSeasObjData = highSeasBiz.getHighSeasById(tenantId, mappingRuleHighSeasId);
            }
        } else {
            highSeasObjData = highSeasBiz.getHighSeasById(tenantId, targetId);
        }


        if (highSeasObjData == null) {
            if (customer.get(EXPIRE_TIME) != null && StringUtils.isNotBlank(customer.get(EXPIRE_TIME).toString())) {
                if (System.currentTimeMillis() - Long.parseLong(customer.get(EXPIRE_TIME).toString()) > 0) {
                    log.info("找不到回收规则 清空到期时间:{},{},{}", customer.getId(), tenantId, customer.get(EXPIRE_TIME));
                    customerBiz.clearExpireTime(customer);
                    return;
                }
            }
            log.warn("公海不存在,highSeasId:{},message:{}", highSeasId, message);
            return;
        }

        ReturnHighSeasActionContent returnHighSeasActionContent = ReturnHighSeasActionContent.builder().accountIds(Lists.newArrayList(customer.getId()))
                .customerName(customer.getName()).build();

        String littleTitle = getLittleTitle(message, customer.getName(), highSeasObjData.getName());
        log.info("recycling sendRemind objectId:{},:{}", objectId, littleTitle);
        // 发送提醒
        //remindBiz.sendRemind(customer, littleTitle, RemindRecordTypeEnum.CUSTOMER_TAKEBACK, null);
        // 发送新crm提醒
        if (StringUtils.isBlank(littleTitle)) {
            log.info("recycling sendRemind objectId:{}", objectId);
            // 发送新crm提醒
            remindBiz.sendRemind(customer, message.getTenantId(), customer.getName() + " 被收回到公海：" + highSeasObjData.getName(), RemindRecordTypeEnum.CUSTOMER_TAKEBACK, null,
                    null, null, SFA_RECYCLING_TO_HIGHSEAS_NEW_RECORD, Lists.newArrayList(customer.getName(), highSeasObjData.getName()));
        } else {
            returnHighSeasActionContent.setBackReasonDescription(littleTitle);
            remindBiz.sendRemind(customer, message.getTenantId(), littleTitle, RemindRecordTypeEnum.CUSTOMER_TAKEBACK, null,
                    null,
                    null,
                    getFullContentInternationalKey(message),
                    Lists.newArrayList(customer.getName(), String.valueOf(message.getRecyclingDays()), highSeasObjData.getName()));
        }

        //客户：第一：负责人清空，第二：状态设置未分配，第三：过期时间清空 ,第四：转移公海  第五：领取时间清空 第六：退回时间变为当前时间
        updateFields(message);

        // 记录修改记录
        try {
            if (objectDescribe == null) {
                objectDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, ApiNameEnum.ACCOUNT_OBJ.getApiName());
            }
            metaDataMiscService.fillUserInfo(objectDescribe, Lists.newArrayList(customer), customerBiz.buildUser(tenantId));
            Object ownerNameObj;
            try {
                ownerNameObj = JSONObject.parseObject(customer.get("owner__r", String.class)).get("name");
            } catch (Exception e) {
                log.info("AccountRecycling exception customer:{}", customer);
                throw new RuntimeException(e);
            }
            if (ownerNameObj != null) {
                String ownerName = ownerNameObj.toString();
                String content = " 被收回到公海：" + highSeasObjData.getName() + "，原负责人：" + ownerName;
                log.info("send modify message tenantId:{},objectId:{},name:{},oldOwner:{},{}", tenantId, objectId, customer.getName(), customer.getOwner(), ownerName);
                logService.logWithCustomMessage(customerBiz.buildUser(tenantId), EventType.MODIFY, ActionType.AutoTakeBack, objectDescribe, customer, content);
            }
        } catch (MetadataServiceException e) {
            log.error("findByTenantIdAndDescribeApiName,MetadataServiceException {}", message, e);
            throw new RuntimeException(e);
        }

        log.info("savePoolClaimLog objectId:{}", objectId);
        recyclingBiz.addPoolClaimLog(tenantId, highSeasObjData.get("id", String.class), Lists.newArrayList(customer));

        List<String> contactIds = new ArrayList<>();
        List<String> userIds = new ArrayList<>();
        List<IObjectData> contacts = customerBiz.getContact(tenantId, objectId);

        if (CollectionUtils.notEmpty(contacts)) {
            for (IObjectData data : contacts) {
                contactIds.add(data.getId());
                if (CollectionUtils.notEmpty(data.getOwner())) {
                    userIds.addAll(data.getOwner());
                }
            }
            // 清空联系人负责人，归属部门
//            log.info("ContactObj 清空联系人，负责人、归属部门:{}, {}", tenantId, contactIds);
//            customerBiz.removeContactOwner(tenantId, contactIds, ApiNameEnum.CONTACT_OBJ.getApiName());
            // 清空联系人负责人，归属部门，默认清空
            if (frameworkConfigService.getConfigValue(tenantId, CONTACT_OWNER_RULE_SETTING).equals("1")) {
                log.info("ContactObj 清空联系人，负责人、归属部门:{}, {}", tenantId, contactIds);
                customerBiz.removeContactOwner(tenantId, contactIds, ApiNameEnum.CONTACT_OBJ.getApiName());
            }
        }
        // 公海规则配置 清空相关团队
        if ((Boolean) highSeasObjData.get(IS_RECYCLING_TEAM_MEMBER)) {
            log.info("recyclingRule AccountObj removeRelevantTeam :{},{},{}", tenantId, customer.getName(), customer.getId());
            customerBiz.removeInnerRelevantTeam(tenantId, Lists.newArrayList(customer));
            if (CollectionUtils.notEmpty(contacts)) {
                log.info("recyclingRule ContactObj removeRelevantTeam :{}, {}", tenantId, contactIds);
                customerBiz.removeInnerRelevantTeam(tenantId, contacts);
            }
        } else {
            // 移除客户相关团队负责人
            customerBiz.removeObjectInnerOwner(tenantId, Lists.newArrayList(customer));
            if (CollectionUtils.notEmpty(contacts)) {
                //清空联系人相关团队负责人
                log.info("清空联系人相关团队负责人:objectId:{},{},{},", objectId, tenantId, contactIds);
                customerBiz.removeObjectInnerOwner(tenantId, contacts);
            }
        }
        customerBiz.objectOutTeamHandle(tenantId, highSeasObjData, Lists.newArrayList(customer));
        if (CollectionUtils.notEmpty(userIds)) {
            batchSessionSandwichUpdated(SessionSandwichUpdateTypeEnum.CONTACT, tenantId, userIds, getContent());
        }

        deleteNomonRemind(tenantId, objectId);

        IObjectData newCustomer = customerBiz.getObjectById(tenantId, objectId, ApiNameEnum.ACCOUNT_OBJ.getApiName());
        log.info("发送客户群mq objectId:{},highSeasId:{},tenantId:{}", objectId, targetId, tenantId);
        metaDataService.sendActionMq(customerBiz.buildUser(tenantId), fillOldData(Lists.newArrayList(newCustomer), Lists.newArrayList(customer)), ObjectAction.TAKE_BACK);

        log.info("发送公海群mq,objectId:{},highSeasId:{},tenantId:{}", objectId, targetId, tenantId);
        sfaOpenApiMqService.sendOpenApiMq(customerBiz.buildUser(tenantId), ObjectAction.TAKE_BACK.getActionCode(),
                ApiNameEnum.HIGH_SEAS_OBJ.getApiName(), targetId, returnHighSeasActionContent);

        //updatePoolCustomerCount(tenantId,highSeasObjData.get("id",String.class));

        super.executeFunction(tenantId, ACCOUNT_OBJ, message.getFunctionApiName(), customer);
        super.cancelInstance(message);
    }

    private String getLittleTitle(RecyclingMessage message, String objectDataName, String highSeasName) {
        String s = RecyclingReasonEnum.valueOf(message.getRecyclingReasonType());
        String recyclingCondition = "";
        if (StringUtils.isNotBlank(s)) {
            recyclingCondition = I18N.text(s, message.getRecyclingDays());
        }
        return recyclingCondition;
    }

    private String getFullContentInternationalKey(RecyclingMessage message) {
        switch (message.getRecyclingReasonType()) {
            case 1:
                return SFA_RECYCLING_REASON_TO_HIGHSEAS_NEW_RECORD_FOLLOW;
            case 4:
                return SFA_RECYCLING_REASON_TO_HIGHSEAS_NEW_RECORD_DEAL;
            default:
                return SFA_RECYCLING_REASON_TO_HIGHSEAS_NEW_RECORD;
        }
    }


    /**
     * 更新字段信息
     * <p>
     * 第一：负责人清空，
     * 第二：状态设置未分配，
     * 第三：过期时间清空 ,
     * 第四：转移公海
     * 领取时间清空，
     * 退回时间变为当前时间
     */
    private void updateFields(RecyclingMessage message) {
        List<String> updateFieldList = Lists.newArrayList();
        IObjectData data = new ObjectData();
        Long currentTime = System.currentTimeMillis();

        data.setTenantId(message.getTenantId());
        data.setDescribeApiName(message.getObjectApiName());
        data.setId(message.getObjectId());

        data.setOwner(null);
        updateFieldList.add(OWNER);

        data.setDataOwnDepartment(null);
        updateFieldList.add(DATA_OWN_DEPARTMENT);

        data.set(OWNER_MODIFIED_TIME, currentTime);
        updateFieldList.add(OWNER_MODIFIED_TIME);

        data.set(BIZ_STATUS, BizStatusEnum.UNALLOCATED.getValue());
        updateFieldList.add(BIZ_STATUS);

        //到期时间清空
        data.set(EXPIRE_TIME, "");
        updateFieldList.add(EXPIRE_TIME);

        data.set(HIGH_SEAS_ID, message.getTargetId());
        updateFieldList.add(HIGH_SEAS_ID);

        //领取/退回时间清空
        data.set(CLAIMED_TIME, "");
        updateFieldList.add(CLAIMED_TIME);

        // 回收时间 设置为系统时间
        data.set(RETURNED_TIME, currentTime);
        updateFieldList.add(RETURNED_TIME);

        data.set(REMIND_DAYS, null);
        updateFieldList.add(REMIND_DAYS);

        data.set(RECYCLED_REASON, "未成交/未跟进收回");
        updateFieldList.add(RECYCLED_REASON);

        if (recyclingTaskGray.isExpireTimeExtend(message.getTenantId())) {
            data.set(EXTEND_DAYS, null);
            updateFieldList.add(EXTEND_DAYS);
        }

        log.info("batchUpdateWithField :{},{}", data, updateFieldList);
        customerBiz.updateFieldForRecycling(Lists.newArrayList(data), updateFieldList, message.getObjectApiName(), message.getTenantId());
    }


    private void batchSessionSandwichUpdated(SessionSandwichUpdateTypeEnum type, String tenantId,
                                             List<String> userIds, String content) {
        BatchSessionSandwichUpdatedArg arg = new BatchSessionSandwichUpdatedArg();
        String enterpriseAccount = gdsHandler.getEAByEI(tenantId);
        arg.setEnterpriseAccount(enterpriseAccount);
        List<SessionSandwichUpdatedArg> args = Lists.newArrayList();
        userIds.forEach(userId -> {
            SessionSandwichUpdatedArg sessionSandwichUpdatedArg = new SessionSandwichUpdatedArg();
            sessionSandwichUpdatedArg.setEmployeeId(Integer.parseInt(userId));
            sessionSandwichUpdatedArg.setEnterpriseAccount(enterpriseAccount);
            sessionSandwichUpdatedArg.setType(type.getValue());
            sessionSandwichUpdatedArg.setContent(content);
            args.add(sessionSandwichUpdatedArg);
        });
        arg.setArgs(args);
        sessionSandwichApiService.batchSessionSandwichUpdated(arg);
    }


    private String getContent() {
        Map<String, Object> content = Maps.newHashMap();
        content.put("timestamp", System.currentTimeMillis());
        return JSONObject.toJSONString(content);
    }

    private void deleteNomonRemind(String tenantId, String objectId) {
        NomonDeleteMessage nomonDeleteRemindMessage = NomonDeleteMessage.builder()
                .biz(OBJ_REMIND_BIZ)
                .tenantId(tenantId)
                .dataId(objectId)
                .build();
        nomonProducer.send(nomonDeleteRemindMessage);
    }


    public String getHighSeasIdMappingRule(List<RecyclingRuleInfoModel> recyclingRules, IObjectData objectData, String apiName, IObjectDescribe objectDescribe) {

        try {
            if (objectDescribe == null) {
                objectDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(objectData.getTenantId(), apiName);
            }
        } catch (MetadataServiceException e) {
            log.error("findByTenantIdAndDescribeApiName is null {}", objectData.getId(), e);
        }
        if (objectDescribe == null) {
            return null;
        }
        recyclingRules = recyclingRules.stream().filter(x -> x.getRecyclingRuleType() != 1)
                .sorted(Comparator.comparing(RecyclingRuleInfoModel::getPriority)).collect(Collectors.toList());

        for (RecyclingRuleInfoModel recyclingRule : recyclingRules) {
            //获取公海规则filter
            if (accountRecalculate.newMappingRule(objectData, objectDescribe, recyclingRule)) {
                log.warn("recycling-needRecycling-dataId={},recyclingRuleId={},priority={},mq-c",objectData.getId(),recyclingRule.getId(),recyclingRule.getPriority());
                // 满足规则，
                if (recyclingRule.getRecyclingRuleType() == 1) {
                    return null;
                }
                return recyclingRule.getTargetPoolId();
            } else {
                continue;
            }
        }
        return null;
    }

    public static List<IObjectData> fillOldData(List<IObjectData> updatedDataList, List<IObjectData> oldDataList) {
        if (!CollectionUtils.empty(updatedDataList) && !CollectionUtils.empty(oldDataList)) {
            Map<String, Map> map = oldDataList.stream().collect(Collectors.toMap(DBRecord::getId, AccountRecycling::of));
            List<IObjectData> list = Lists.newArrayList();
            updatedDataList.forEach((a) -> {
                Map<String, Object> data = Maps.newHashMap(of(a));
                data.put("old_data", map.get(a.getId()));
                list.add(ObjectDataExt.of(data).getObjectData());
            });
            return list;
        } else {
            return updatedDataList;
        }
    }

    public static Map of(IObjectData objectData) {
        return objectData == null ? null : ObjectDataExt.toMap(objectData);
    }


    public void updatePoolCustomerCount(String tenantId, String poolId) {
        Integer poolCustomerCount = getPoolCustomerCount(tenantId, poolId, ACCOUNT_OBJ);

        if (poolCustomerCount == 0) {
            return;
        }
        List<IObjectData> objectDataList = Lists.newArrayList();
        IObjectData objectData = new ObjectData();
        objectData.setDescribeApiName(Utils.HIGHSEAS_API_NAME);
        objectData.setTenantId(tenantId);
        objectData.setId(poolId);
        objectData.set("accounts_count", poolCustomerCount);
        objectDataList.add(objectData);
        if (CollectionUtils.notEmpty(objectDataList)) {
            List<String> updateField = Arrays.asList("accounts_count");
            serviceFacade.batchUpdateByFields(customerBiz.buildUser(tenantId), objectDataList, updateField);
        }
    }

}

