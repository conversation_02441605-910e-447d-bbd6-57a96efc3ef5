package com.facishare.crm.recycling.task.executor.enums;


@Deprecated
public enum AccountStatusEnum {

    /**
     * 报备中
     */
    REPORTING("1", "报备中"),

    /**
     * 未分配
     */
    UNALLOCATED("2", "未分配"),

    /**
     * 已分配
     */
    ALLOCATED("3", "已分配"),
    /**
     * 未生效
     */
    NOACTIVE("4", "未生效"),

    /**
     * 变更中
     */
    CHANGING("5", "变更中"),
    /**
     * 已经作废
     */
    INVALID("99", "已经作废");


    private final String value;

    private final String label;

    AccountStatusEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }
}
