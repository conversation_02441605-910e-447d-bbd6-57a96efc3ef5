package com.facishare.crm.recycling.task.executor.util.sql;


import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.util.HashMap;
import java.util.Map;

@SuppressWarnings("unchecked")
@Slf4j
public class UpdateSql extends BaseSql {
	private final Map<String, Object> updateFieldMap = new HashMap<>();

	public static UpdateSql table(String tableName) {
		UpdateSql updateSql = new UpdateSql();
		updateSql.setTableName(tableName);
		return updateSql;
	}

	public UpdateSql set(String column, Object value) {
		updateFieldMap.put(column, value);
		return this;
	}

	@Override
	protected void checked() {
		super.checked();
		Assert.notEmpty(updateFieldMap, "更新字段不能为空");
	}

	public int update() {
		checked();
		try {
			return commonSqlService.update(tableName, updateFieldMap, whereList, actionContext);
		} catch (MetadataServiceException e) {
			if (ignoreException) {
				log.error("更新异常", e);
				return 0;
			}
			throw new RuntimeException(e);
		}
	}

	@Override
	public UpdateSql where(WhereSql whereSql) {
		return super.where(whereSql);
	}

	@Override
	public UpdateSql withContext(IActionContext context) {
		return super.withContext(context);
	}

	@Override
	public UpdateSql withAdminContext() {
		return super.withAdminContext();
	}

	@Override
	public UpdateSql ignoreException() {
		return super.ignoreException();
	}
}