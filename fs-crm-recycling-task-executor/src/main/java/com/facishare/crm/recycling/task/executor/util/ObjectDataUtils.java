package com.facishare.crm.recycling.task.executor.util;

import com.facishare.crm.recycling.task.executor.biz.CustomerBiz;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

import static com.facishare.crm.recycling.task.executor.util.ConstantUtils.HIGH_SEAS_ID;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-07-07 23:26
 */
@Component
public class ObjectDataUtils {

    @Autowired
    private CustomerBiz customerBiz;

    public static String getOwner(IObjectData objectData) {
        List<String> ownerList = objectData.getOwner();
        String owner = "";
        if (CollectionUtils.notEmpty(ownerList)) {
            owner = ownerList.get(0);
        }
        return owner;
    }
    public static String getValueOrDefault(IObjectData data, String key, String defaultValue) {
        if (data == null) {
            return defaultValue;
        }
        return Optional.ofNullable(data.get(key)).orElse(defaultValue).toString();
    }

	public static Integer getIntegerOrDefault(IObjectData data, String key, Integer defaultValue) {
		if (data == null) {
			return defaultValue;
		}
		return Integer.parseInt(Optional.ofNullable(data.get(key)).orElse(defaultValue).toString());
	}

    public static Boolean getBooleanOrDefault(IObjectData data, String key, Boolean defaultValue) {
        if (data == null) {
            return defaultValue;
        }
        return Optional.ofNullable(data.get(key, Boolean.class)).orElse(defaultValue);
    }

    public static Integer getOwnerInteger(IObjectData objectData) {
        String owner = getOwner(objectData);
        return owner.equals("") ? null : Integer.parseInt(owner);
    }



    /**
     * 是否是公海客户，
     *
     * @param objectData
     * @return true 公海
     */
    public static Boolean isHighSeas(IObjectData objectData) {
        return objectData.get(HIGH_SEAS_ID) != null && StringUtils.isNotEmpty(objectData.get(HIGH_SEAS_ID).toString());
    }

    public static User buildUser(String tenantId){
        return new User(tenantId, User.SUPPER_ADMIN_USER_ID);
    }

}
