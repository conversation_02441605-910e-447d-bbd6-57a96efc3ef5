package com.facishare.crm.recycling.task.executor.model.forecast;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.metadata.api.IObjectData;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public class ForecastTaskRecord extends BaseForecastRecord implements ForecastTask {
    private static final long serialVersionUID = 1;
    @JsonIgnore
    @JSONField(serialize = false)
    private transient List<ForecastTaskDetail> forecastTaskDetails;
    @JsonIgnore
    @JSONField(serialize = false)
    private transient ForecastRule forecastRule;

    public ForecastTaskRecord() {

    }

    public ForecastTaskRecord(Map map) {
        super(map);
    }

    public ForecastTaskRecord(IObjectData objectData) {
        super(objectData);
    }

    @Override
    public String getDescribeApiName() {
        String describeApiName = super.getDescribeApiName();
        return describeApiName == null ? FORECAST_TASK_OBJECT_DESCRIBE_API_NAME : describeApiName;
    }

    @Override
    public String getForecastRuleObjectId() {
        return get(FORECAST_RULE_OBJECT_ID, String.class);
    }

    @Override
    public void setForecastRuleObjectId(String forecastRuleObjectId) {
        set(FORECAST_RULE_OBJECT_ID, forecastRuleObjectId);
    }

    @Override
    public Long getForecastStartDate() {
        return get(FORECAST_START_DATE, Long.class);
    }

    @Override
    public void setForecastStartDate(Long forecastStartDate) {
        set(FORECAST_START_DATE, forecastStartDate);
    }

    @Override
    public Long getForecastEndDate() {
        return get(FORECAST_END_DATE, Long.class);
    }

    @Override
    public void setForecastEndDate(Long forecastEndDate) {
        set(FORECAST_END_DATE, forecastEndDate);
    }

    @Override
    public Integer getForecastDateSplitType() {
        return get(FORECAST_DATE_SPLIT_TYPE, Integer.class);
    }

    @Override
    public void setForecastDateSplitType(Integer forecastDateSplitType) {
        set(FORECAST_DATE_SPLIT_TYPE, forecastDateSplitType);
    }

    @Override
    public Integer getBizStatus() {
        return get(BIZ_STATUS, Integer.class);
    }

    @Override
    public void setBizStatus(Integer bizStatus) {
        set(BIZ_STATUS, bizStatus);
    }

    @Override
    public String getForecastMonthGroup() {
        return get(FORECAST_MONTH_GROUP, String.class);
    }

    @Override
    public void setForecastMonthGroup(String forecastMonthGroup) {
        set(FORECAST_MONTH_GROUP, forecastMonthGroup);
    }

    @Override
    public String getForecastQuarterGroup() {
        return get(FORECAST_QUARTER_GROUP, String.class);
    }

    @Override
    public void setForecastQuarterGroup(String forecastQuarterGroup) {
        set(FORECAST_QUARTER_GROUP, forecastQuarterGroup);
    }

    @Override
    public BigDecimal getBestPracticesForecastModel1() {
        return get(BEST_PRACTICES_FORECAST_MODEL1, BigDecimal.class);
    }

    @Override
    public void setBestPracticesForecastModel1(BigDecimal bestPracticesForecastModel1) {
        set(BEST_PRACTICES_FORECAST_MODEL1, bestPracticesForecastModel1);
    }

    @Override
    public BigDecimal getBestPracticesForecastModel2() {
        return get(BEST_PRACTICES_FORECAST_MODEL2, BigDecimal.class);
    }

    @Override
    public void setBestPracticesForecastModel2(BigDecimal bestPracticesForecastModel2) {
        set(BEST_PRACTICES_FORECAST_MODEL2, bestPracticesForecastModel2);
    }

    @Override
    public BigDecimal getBestPracticesForecastModel3() {
        return get(BEST_PRACTICES_FORECAST_MODEL3, BigDecimal.class);
    }

    @Override
    public void setBestPracticesForecastModel3(BigDecimal bestPracticesForecastModel3) {
        set(BEST_PRACTICES_FORECAST_MODEL3, bestPracticesForecastModel3);
    }

    @Override
    public BigDecimal getBestPracticesForecastModel4() {
        return get(BEST_PRACTICES_FORECAST_MODEL4, BigDecimal.class);
    }

    @Override
    public void setBestPracticesForecastModel4(BigDecimal bestPracticesForecastModel4) {
        set(BEST_PRACTICES_FORECAST_MODEL4, bestPracticesForecastModel4);
    }

    @Override
    public BigDecimal getBestPracticesForecastModel5() {
        return get(BEST_PRACTICES_FORECAST_MODEL5, BigDecimal.class);
    }

    @Override
    public void setBestPracticesForecastModel5(BigDecimal bestPracticesForecastModel5) {
        set(BEST_PRACTICES_FORECAST_MODEL5, bestPracticesForecastModel5);
    }

    @Override
    public BigDecimal getArtificialCommitmentForecastModel() {
        return get(ARTIFICIAL_COMMITMENT_FORECAST_MODEL, BigDecimal.class);
    }

    @Override
    public void setArtificialCommitmentForecastModel(BigDecimal artificialCommitmentForecastModel) {
        set(ARTIFICIAL_COMMITMENT_FORECAST_MODEL, artificialCommitmentForecastModel);
    }

    @Override
    public BigDecimal getStageWeightForecastModel() {
        return get(STAGE_WEIGHT_FORECAST_MODEL, BigDecimal.class);
    }

    @Override
    public void setStageWeightForecastModel(BigDecimal stageWeightForecastModel) {
        set(STAGE_WEIGHT_FORECAST_MODEL, stageWeightForecastModel);
    }

    @Override
    public BigDecimal getArtificialWeightForecastModel() {
        return get(ARTIFICIAL_WEIGHT_FORECAST_MODEL, BigDecimal.class);
    }

    @Override
    public void setArtificialWeightForecastModel(BigDecimal artificialWeightForecastModel) {
        set(ARTIFICIAL_WEIGHT_FORECAST_MODEL, artificialWeightForecastModel);
    }

    @Override
    public BigDecimal getAIWeightForecastModel() {
        return get(AI_WEIGHT_FORECAST_MODEL, BigDecimal.class);
    }

    @Override
    public void setAIWeightForecastModel(BigDecimal aiWeightForecastModel) {
        set(AI_WEIGHT_FORECAST_MODEL, aiWeightForecastModel);
    }

    @Override
    public List<ForecastTaskDetail> getForecastTaskDetails() {
        return forecastTaskDetails;
    }

    @Override
    public void setForecastTaskDetails(List<ForecastTaskDetail> forecastTaskDetails) {
        this.forecastTaskDetails = forecastTaskDetails;
    }

    @Override
    public ForecastRule getForecastRule() {
        return forecastRule;
    }

    @Override
    public void setForecastRule(ForecastRule forecastRule) {
        this.forecastRule = forecastRule;
    }

    @Override
    public boolean equals(Object obj) {
        return super.equals(obj);
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }
}