package com.facishare.crm.recycling.task.executor.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-03-05 15:45
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RecyclingRule {
    /**
     * RecyclingRuleID : c96f4b0fb6b64540b1247f6231ffe380
     * Priority : 1
     * DataID : 6fc7745f7cbf4e078dc011dc0f9106f8
     * DataType : 1
     * RecyclingRuleType : 2
     * DealDays : 1
     * FollowUpDays : 1
     * HighSeasID : 6fc7745f7cbf4e078dc011dc0f9106f8
     * GroupID : 33ae1cb6b815401cb847b6eeb57e614b
     * RecyclingFilterList : [{"RecyclingFilterID":"541f4ef962ff4170a3e62e461e6f6e00","RecyclingRuleID":"c96f4b0fb6b64540b1247f6231ffe380","DataID":"6fc7745f7cbf4e078dc011dc0f9106f8","FieldName":"Name","FieldType":2,"FieldOrder":0,"Compare":3,"FieldValue":"gchr回收"},{"RecyclingFilterID":"89e934db91f24cafb31c99ee79766c13","RecyclingRuleID":"c96f4b0fb6b64540b1247f6231ffe380","DataID":"6fc7745f7cbf4e078dc011dc0f9106f8","FieldName":"Source","FieldType":8,"FieldOrder":0,"Compare":1,"FieldValue":"1"}]
     * DataName : null
     * HighSeasName : gchr_test
     * IsIncludePastTime : true
     * RecyclingRemindRuleList : [{"RecyclingRemindRuleID":"dd629df760ac44c1ace8f4d37d571bfb","RecyclingRuleID":"c96f4b0fb6b64540b1247f6231ffe380","DataID":"6fc7745f7cbf4e078dc011dc0f9106f8","RuleType":2,"RemindDays":1,"GroupID":"33ae1cb6b815401cb847b6eeb57e614b"}]
     */

    private String ei;
    private String RecyclingRuleID;
    private int Priority;
    private String DataID;
    private int DataType;
    private int RecyclingRuleType;
    private int DealDays;
    private int FollowUpDays;
    private String HighSeasID;
    private String GroupID;
    private Object DataName;
    private String HighSeasName;
    private boolean IsIncludePastTime;
    private List<RecyclingFilter> RecyclingFilterList;
    private List<RecyclingRemindRule> RecyclingRemindRuleList;
    private long UpdateTime;
    private String creatorID;

    public void setIsIncludePastTime(boolean includePastTime) {
        IsIncludePastTime = includePastTime;
    }

}
