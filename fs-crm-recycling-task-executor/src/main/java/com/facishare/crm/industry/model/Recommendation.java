package com.facishare.crm.industry.model;

import com.facishare.crm.sfa.lto.rest.models.IndustryCompanyAdvance;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/3/27 11:03
 * @description:
 */
public interface Recommendation {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {

        String tenantId;

        String message;

    }

    @Data
    @Builder
    class Result {

        String errorCode;

        String message;

        List<IndustryCompanyAdvance.IndustryCompany> data;
    }
}
