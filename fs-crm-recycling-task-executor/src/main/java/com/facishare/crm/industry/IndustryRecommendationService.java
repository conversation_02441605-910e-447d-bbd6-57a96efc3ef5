package com.facishare.crm.industry;

import com.facishare.ai.api.dto.BaseArgument;
import com.facishare.ai.api.dto.OpenAIChatComplete;
import com.facishare.ai.api.model.Message;
import com.facishare.ai.api.service.FsAI;
import com.facishare.ai.api.service.Openai;
import com.facishare.crm.sfa.lto.rest.SFAIndustryInterfaceProxy;
import com.facishare.crm.sfa.lto.rest.models.IndustryCompanyAdvance;
import com.facishare.crm.sfa.lto.utils.HttpHeaderUtil;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR> gongchunru
 * @date : 2024/3/26 20:27
 * @description:
 */
@Service
@Slf4j
public class IndustryRecommendationService {

    @Autowired
    SFAIndustryInterfaceProxy sfaIndustryInterfaceProxy;

    @Autowired
    private Openai openai;

    private static String CUSTOMER_PROMPT = "请根据用户的描述,生成工商查询的参数,返回 JSON 格式,为空的字段则丢弃。参数如下:\n" +
            "keyword: 企业名称等搜索关键字\n" +
            "companyStatus: 登记状态\n" +
            "companyType: 主题类型\n" +
            "currency: 注册资本币种\n" +
            "regCapitalMin: 注册资本最小值(Long类型)\n" +
            "regCapitalMax: 注册资本最大值(Long类型,和regCapitalMin一起出现)\n" +
            "province: 省,编码如下:\n" +
            " 249:北京市\n" +
            " 250:天津市\n" +
            " 251:河北省\n" +
            " 252:山西省\n" +
            " 253:内蒙古自治区\n" +
            " 254:辽宁省\n" +
            " 255:吉林省\n" +
            " 256:黑龙江省\n" +
            " 257:上海市\n" +
            " 258:江苏省\n" +
            " 259:浙江省\n" +
            " 260:安徽省\n" +
            " 261:福建省\n" +
            " 262:江西省\n" +
            " 263:山东省\n" +
            " 264:河南省\n" +
            " 265:湖北省\n" +
            " 266:湖南省\n" +
            " 267:广东省\n" +
            " 268:广西壮族自治区\n" +
            " 269:海南省\n" +
            " 270:重庆市\n" +
            " 271:四川省\n" +
            " 272:贵州省\n" +
            " 273:云南省\n" +
            " 274:西藏自治区\n" +
            " 275:陕西省\n" +
            " 276:甘肃省\n" +
            " 277:青海省\n" +
            " 278:宁夏回族自治区\n" +
            " 279:新疆维吾尔自治区\n" +
            " 282:台湾省\n" +
            " 280:香港特别行政区\n" +
            " 281:澳门特别行政区\n" +
            "city: 市\n" +
            "district: 区\n" +
            "employeeNumMin: 员工数量最小值(Long类型,最小为0)\n" +
            "employeeNumMax: 员工数量最大值(Long类型,需要和employeeNumMin一起出现)\n" +
            "hasPhone: 是否有联系电话(0:否,1:有)\n" +
            "hasEmail: 是否有邮箱(0:否,1:有)\n" +
            "stockType: 上市类型\n" +
            "hasRegHg: 是否有注册海关(0:否,1:有)\n" +
            "hasBid: 是否有投标信息(0:否,1:有)\n" +
            "hasProduct: 是否有产品信息(0:否,1:有)\n" +
            "hasPunish: 是否有行政处罚信息(0:否,1:有)\n" +
            "hasZhixing: 是否有被执行人(0:否,1:有)\n" +
            "hasXianxiao: 是否有限销信息(0:否,1:有)\n" +
            "hasAbnormal: 是否有异常信息(0:否,1:有)\n" +
            "sortWay: 排序方式(0:默认,1:成立日期从早到晚,2:成立日期从晚到早,3:注册资本从高到低,4:注册资本从低到高)\n";

    private static String OPENAI_MODEL = "gpt-3.5-turbo";

    public List<IndustryCompanyAdvance.IndustryCompany> getRecommendation(String tenantId, String message) {
        JsonObject jsonArg = getArgByOpenAI(tenantId, message);
        return getCompanyAdvance(tenantId, jsonArg);
    }

    public List<IndustryCompanyAdvance.IndustryCompany> getCompanyAdvance(String tenantId, JsonObject jsonArg) {
        IndustryCompanyAdvance.Arg arg = generateCompanyArg(jsonArg);
        arg.setPageSize(5);
        arg.setPageNumber(1);
        if (arg.getSortWay() == null){
            arg.setSortWay(0);
        }

        IndustryCompanyAdvance.CompanyAdvanceResult companyAdvance = sfaIndustryInterfaceProxy.getCompanyAdvance(HttpHeaderUtil.getHeaders(tenantId), arg);
        return companyAdvance.getResult().getData();
    }

    public IndustryCompanyAdvance.Arg generateCompanyArg(JsonObject jsonArg) {
        Gson gson = new Gson();
        return gson.fromJson(jsonArg, IndustryCompanyAdvance.Arg.class);
    }

    public JsonObject getArgByOpenAI(String tenantId, String message) {
        BaseArgument baseArgument = new BaseArgument();
        baseArgument.setTenantId(tenantId);

        OpenAIChatComplete.Arg arg = new OpenAIChatComplete.Arg();
        arg.setModel(OPENAI_MODEL);
        arg.setMessages(getMessage(CUSTOMER_PROMPT, message));
        arg.setStream(false);
        arg.setUser_id("-10000");

        OpenAIChatComplete.Result result = FsAI.openai().chatComplete(baseArgument, arg);
        log.info("OpenAI result: {}", result.getMessage());

        JsonObject jsonArg = new Gson().fromJson(result.getMessage(), JsonObject.class);
        return jsonArg;
    }

    private List<Message> getMessage(String prompt, String text) {
        List<Message> messages = new ArrayList<>();

        Message systemMessage = new Message();
        systemMessage.setRole("system");
        systemMessage.setContent(prompt);
        messages.add(systemMessage);

        Message userMessage = new Message();
        userMessage.setRole("user");
        userMessage.setContent(text);
        messages.add(userMessage);

        return messages;
    }
}
