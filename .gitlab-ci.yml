build-job:
  stage: build
  except:
    - tags
  script:
    - FORMATTED_VERSION=`date +%Y%m%d`
    - mvn clean test verify sonar:sonar -Dsonar.branch.name=${CI_COMMIT_BRANCH} -Dmaven.test.failure.ignore=true -Dsonar.qualitygate.wait=true -Dsonar.core.codeCoveragePlugin=jacoco -Dsonar.projectVersion=${FORMATTED_VERSION}#${CI_BUILD_ID}
deploy-job:
  stage: deploy
  only:
    - tags
  script:
    - mvn clean deploy -Dmaven.test.skip