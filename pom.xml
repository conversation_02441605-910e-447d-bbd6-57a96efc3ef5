<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.fxiaoke.common</groupId>
        <artifactId>fxiaoke-parent-pom</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <groupId>com.facishare</groupId>
    <artifactId>fs-crm-recycling-task</artifactId>
    <packaging>pom</packaging>
    <dependencies>
        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>jedis-spring-support</artifactId>
        </dependency>
    </dependencies>
    <version>9.7.0-SNAPSHOT</version>
    <modules>
        <module>fs-crm-recycling-task-web</module>
        <module>fs-crm-recycling-task-executor</module>
        <module>fs-crm-sfa-expression-provider</module>
        <module>fs-crm-sfa-lto</module>
        <module>fs-crm-sfa-equity-support</module>
        <module>fs-crm-sfa-equity-es-support</module>
        <module>fs-crm-sfa-data-ext</module>
    </modules>

    <properties>
        <appframework.version>9.6.0-SNAPSHOT</appframework.version>
        <metadata-provider.version>9.6.0-SNAPSHOT</metadata-provider.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-metadata</artifactId>
                <version>${appframework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-metadata-provider</artifactId>
                <version>${metadata-provider.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-core</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-web</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-metadata-restdriver</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-privilege</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-privilege-temp</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-fcp</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-config</artifactId>
                <version>${appframework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-pod-client</artifactId>
                <version>9.0.1-SNAPSHOT</version>
            </dependency>
        </dependencies>
    </dependencyManagement>


</project>
