<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="equityMongoDataStoreBean" class="com.github.mongo.support.MongoDataStoreFactoryBean">
        <property name="configName" value="fs-crm-follow-crm-action-consumer"/>
        <property name="sectionNames" value="mongo"/>
    </bean>

    <bean id="mongoEquityStore" class="com.facishare.crm.sync.MongoEquityStore">
        <property name="equityMongoDataStore" ref="equityMongoDataStoreBean"/>
    </bean>
</beans>
