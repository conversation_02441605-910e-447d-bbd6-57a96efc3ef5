package com.facishare.crm.sync;

import com.github.mongo.support.DatastoreExt;
import com.mongodb.BasicDBObject;
import com.mongodb.DBCollection;
import com.mongodb.DBCursor;
import com.mongodb.DBObject;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> gongchunru
 * @date : 2023/7/7 14:57
 * @description:
 */
@Component
@Slf4j
public class MongoEquityStore {

    private DatastoreExt equityMongoDataStore;

    public void setEquityMongoDataStore(DatastoreExt equityMongoDataStore) {
        this.equityMongoDataStore = equityMongoDataStore;
    }

    public List<Document> getDocsByIDs(List<ObjectId> ids) {
        DBCollection dbCollection = equityMongoDataStore.getDB().getCollection("biz_equity_relationship_-100");
        List<Document> documents = findDocumentsByIds(dbCollection, ids);
        for (Document document : documents) {
            log.info("---findDocumentsByIds---{}", document.toJson());
        }
        return documents;
    }

    private List<Document> findDocumentsByIds(DBCollection dbCollection, List<ObjectId> ids) {
        BasicDBObject inQuery = new BasicDBObject("_id", new BasicDBObject("$in", ids));
        DBCursor cursor = dbCollection.find(inQuery);
        List<Document> documents = new ArrayList<>();
        while (cursor.hasNext()) {
            DBObject dbObject = cursor.next();
            Document document = Document.parse(dbObject.toString());
            documents.add(document);
        }
        return documents;
    }
}
