package com.facishare.crm.sync;

import com.facishare.crm.sfa.equity.es.EquityESService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> gongchunru
 * @date : 2023/7/8 10:48
 * @description:
 */
@Component
@Slf4j
public class Mongo2ESService {


    @Autowired
    private MongoEquityStore mongoEquityStore;

    @Autowired
    private EquityESService equityEsService;


    public void save2Es(List<ObjectId> ids) {
        List<Document> docs = mongoEquityStore.getDocsByIDs(ids);
        if (CollectionUtils.isNotEmpty(docs)){
            log.info(" ----save2Es----{},docs.size:{}",docs.size(),docs);
            equityEsService.upsertBatch(docs);
        }
    }

    public void deleteFromEs(ObjectId id) {
        if (id != null){
            log.info(" ----deleteFromEs----{}",id.toHexString());
            equityEsService.deleteDocument(id.toHexString());
        }
    }
}
