package com.facishare.crm.sync.model;

import lombok.Builder;
import lombok.Data;

import java.io.*;

/**
 * <AUTHOR> gongchunru
 * @date : 2023/6/29 22:13
 * @description:
 */
public interface OPLogCheckpoint {
    @Data
    @Builder
    class Checkpoint {

        private int timestamp;
        private int inc;

        public static Checkpoint parseFrom(FileInputStream input) throws IOException{
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(input))) {
                String timestampLine = reader.readLine();
                String incLine = reader.readLine();

                if (timestampLine == null || incLine == null) {
                    throw new IOException("Invalid checkpoint data. Expecting two lines with timestamp and inc.");
                }

                int timestamp = Integer.parseInt(timestampLine.split("=")[1]);
                int inc = Integer.parseInt(incLine.split("=")[1]);

                return Checkpoint.builder()
                        .timestamp(timestamp)
                        .inc(inc)
                        .build();
            }
        }

        public void writeTo(FileOutputStream output) {
            try (BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(output))) {
                writer.write("timestamp=" + timestamp);
                writer.newLine();
                writer.write("inc=" + inc);
            } catch (IOException e) {
                throw new RuntimeException("Failed to write checkpoint to file.", e);
            }
        }
    }
}
