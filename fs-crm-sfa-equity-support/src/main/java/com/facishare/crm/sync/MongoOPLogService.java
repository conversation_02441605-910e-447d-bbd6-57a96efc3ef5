package com.facishare.crm.sync;

import com.facishare.crm.sfa.lto.common.SFAJedisLock;
import com.facishare.crm.sfa.lto.equityrelationship.service.EquityRelationshipDataSyncService;
import com.facishare.crm.sync.model.OPLogCheckpoint;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.github.jedis.support.JedisCmd;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.RateLimiter;
import com.mongodb.ConnectionString;
import com.mongodb.CursorType;
import com.mongodb.client.*;
import com.mongodb.client.model.Filters;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.BsonTimestamp;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.*;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR> gongchunru
 * @date : 2023/6/30 18:08
 * @description:
 */
@Slf4j
@Component
public class MongoOPLogService {

    private MongoClient mongoOpLogClient;
    private String checkPointFile;
    private String opLogConnectionString;
    private String mongoConnectionString;
    private MongoClient mongoClient;
    private String dbName;
    private String collection;
    private String opLogDBName;
    private String opLogCollection;
    private String opList;
    private Set<String> opSets = Sets.newHashSet();
    private String nsWhite;
    private String nsBlack;
    private Bson mongoFilters;

    private int noNewDataMaxSleepTime = 1000;
    // oplog 无数据的时候sleep 时间
    private int sleepStep = 100;
    private int maxBackoffSleep = 500;

    // 检查点时间保存阈值，比如50保存一次
    private int checkpointBatch;

    // 检查点时间保存超时时间，比如10秒保存一次
    private int checkPointTime;

    private int opLogLossTime;
    private boolean isEnableSync = false;


    private ExecutorService threadpool = Executors.newFixedThreadPool(1);
    private RateLimiter limiter;
    private String isSyncAllData;

    @Autowired
    private MongoEquityStore mongoEquityStore;

    @Autowired
    private Mongo2ESService mongo2ESService;

    @Autowired
    protected JedisCmd SFAJedisCmd;

    private SFAJedisLock jedisLock;
    @Autowired
    private EquityRelationshipDataSyncService equityRelationshipDataSyncService;

    @PostConstruct
    void init() {
        ConfigFactory.getConfig("fs-crm-follow-crm-action-consumer", this::initFromConfig);
        if (!isEnableSync){
            return;
        }
        jedisLock = new SFAJedisLock(SFAJedisCmd, "sfa-mongo-oplog-read-lock", 60000);
        start();
    }


    public void start() {
        ReadOpLog task = new ReadOpLog();
        threadpool.submit(task);
    }

    private void initFromConfig(IConfig c) {
        this.isEnableSync = c.getBool("isEnableSync", false);
        this.mongoConnectionString = c.get("mongoConnectionString");
        this.dbName = c.get("dbName");
        this.collection = c.get("collection");
        this.opLogConnectionString = c.get("opLogConnectionString");
        this.opLogDBName = c.get("opLogDBName");
        this.opLogCollection = c.get("opLogCollection");
        this.opList = c.get("opList");
        if (opList != null) {
            String[] opSplit = this.opList.split(",");
            Collections.addAll(opSets, opSplit);
        } else {
            Collections.addAll(opSets, "i", "u");
        }

        this.nsBlack = c.get("nsBlack", "");
        this.nsWhite = c.get("nsWhite", ".*");
        this.isSyncAllData = c.get("isSyncAllData", "false");
        this.checkPointFile = c.get("checkPointFile");
        this.sleepStep = c.getInt("sleepStep", 100);
        this.maxBackoffSleep = c.getInt("maxBackoffSleep", 500);
        this.noNewDataMaxSleepTime = c.getInt("noNewDataMaxSleepTime", 1000);
        this.checkpointBatch = c.getInt("checkpointBatch", 50);
        this.opLogLossTime = c.getInt("opLogLossTime", 0);
        this.checkPointTime = c.getInt("checkPointTime", 10);
        Double limiterValue = c.getDouble("limiter", 1.0);
        limiter = RateLimiter.create(limiterValue);
    }

    private void setMongoOpLogConnection(String mongoConnectionString) {
        if (mongoOpLogClient != null) return;
        ConnectionString connection = new ConnectionString(mongoConnectionString);
        mongoOpLogClient = MongoClients.create(connection);
        log.info("=======================new MongoClient OpLog:{}", mongoConnectionString);
    }


    private class ReadOpLog implements Callable {
        @Override
        public Long call() {
            long noNewDataNo = 1L;
            try {
                while (true) {
                    boolean b = jedisLock.tryLock();
                    if (b) {
                        log.info("--- get lock success -----");
                        break;
                    } else {
                        log.info("获取锁失败，等待1分钟");
                        try {
                            Thread.sleep(60000);
                        } catch (InterruptedException e) {
                            throw new RuntimeException(e);
                        }
                    }
                }
                setMongoOpLogConnection(opLogConnectionString);
                log.info("--- start  sync ---");
                while (true) {
                    Long fieldNo = readLog();
                    if (fieldNo == 0) {
                        long sleepTime = Math.min(noNewDataNo * sleepStep, noNewDataMaxSleepTime);
                        try {
                            Thread.sleep(sleepTime);
                        } catch (InterruptedException e) {
                            log.error("ReadOpLog sleep error", e);
                        }
                        noNewDataNo++;
                        log.info("No increased data, sleep {} milliseconds. ", sleepTime);
                    } else if (fieldNo > 0) {
                        noNewDataNo = 1;
                    }
                }
            } catch (Exception e) {
                log.error("ReadOpLog call error", e);
            } finally {
                jedisLock.unlock();
            }
            return noNewDataNo;
        }

        /**
         * 增量读取日志
         * todo 做单线程执行前的锁抢占
         */

        private Long readLog() {
            BsonTimestamp lastCheckPoint = getLastCheckpoint();
            Long fieldNo = 0L;
            FindIterable<Document> cursor = null;
            try {
                setMongoOpLogConnection(opLogConnectionString);
                // 增量数据
                MongoDatabase db = mongoOpLogClient.getDatabase(opLogDBName);
                MongoCollection<Document> collection = db.getCollection(opLogCollection);
                List<Bson> filterLists = Lists.newArrayList();
                //必须要有ts字段OplogReplay query才生效，否则报错
                filterLists.add(Filters.gte("ts", lastCheckPoint));
                //加入checkpoint丢失，则读取先读取整个oplog中的日志，然后再增量
                filterLists.add(Filters.gte("ts", new BsonTimestamp(0, 0)));
                filterLists.add(Filters.in("op", opSets));
                if (!nsBlack.isEmpty()) {
                    filterLists.add(Filters.not(Filters.regex("ns", nsBlack)));
                }
                if (!nsWhite.isEmpty()) {
                    filterLists.add(Filters.regex("ns", nsWhite));
                }
                mongoFilters = Filters.and(filterLists);
                log.debug("start collection find:{}", filterLists);
                cursor = collection.find(mongoFilters).noCursorTimeout(true)
                        .oplogReplay(true)
                        .cursorType(CursorType.Tailable)
                        .cursorType(CursorType.TailableAwait);
                log.debug("end collection find:{}", cursor);
                BsonTimestamp ts;
                List<ObjectId> ids = new ArrayList<>();
                int lastSaveTime = 0;
                for (Document doc : cursor) {
                    //只有在异常的情况下才会跳出cursor增量监听的for循环，进入while(true)循环
                    int timestamp = 0;
                    int inc = 0;
                    ts = doc.get("ts", BsonTimestamp.class);
                    int lastTimestamp = ts.getTime();
                    String op = doc.getString("op");
                    log.debug("foreach doc:{}", doc);
                    log.debug("foreach doc, ts:{},ids:{},fieldNo:{}", ts.getTime(), ids.size(), fieldNo);
                    Document oObj = (Document) doc.get("o");
                    Document oObj2 = (Document) doc.get("o2");
                    ObjectId id;

                    if ("u".equals(op)) {
                        id = oObj2.get("_id") == null ? null : (ObjectId) oObj2.get("_id");
                    } else if ("i".equals(op)) {
                        id = oObj.get("_id") == null ? null : (ObjectId) oObj.get("_id");
                    } else {
                        id = oObj.get("_id") == null ? null : (ObjectId) oObj.get("_id");
                        mongo2ESService.deleteFromEs(id);
                        continue;
                    }
                    if (id != null) {
                        ids.add(id);
                    }
                    timestamp = ts.getTime();
                    inc = ts.getInc();
                    //保证不是频繁的更新时间点，达到一定量50条或者时间超过checkPointTime后才更新时间
                    if (ids.size() >= checkpointBatch || (System.currentTimeMillis() / 1000 - lastSaveTime) >= checkPointTime) {
                        jedisLock.resetExpireLock("sfa-mongo-oplog-read-lock", checkPointTime * 1000 * 2);
                        log.debug("checkpointBatch getDocsByIDs reached checkpointBatch:{},ids.size:{}", checkpointBatch, ids.size());
                        if (CollectionUtils.isNotEmpty(ids)){
                            limiter.acquire();
                            mongo2ESService.save2Es(ids);
                            equityRelationshipDataSyncService.realTimeUpdatePG(ids);
                            lastSaveTime = (int) (System.currentTimeMillis() / 1000);
                            ids.clear();
                        }
                        setCheckpoint(timestamp, inc);
                    }
                    fieldNo++;
                }
                log.debug("all records:{}", fieldNo);
            } catch (Exception e) {
                log.warn("################for loop,read oplog increament exception:", e);
            } finally {
                if (cursor != null) {
                    cursor.iterator().close();
                }
            }
            return fieldNo;
        }

        /**
         * @param readTimestamp : oplog游标位置
         *                      opLogStartTimestamp ：当前oplog头位置
         *                      opLogEndTimestamp ：当前oplog尾位置
         *                      lastCheckpoint ：已经读到的位置
         *                      lossTime : opLogStartTimestamp - lastCheckpoint ：还有多长时间段内的消息要读
         * @return boolean
         */
        //    private boolean checkOpLogIsRotate(MongoCollection<Document> collection, int readTimestamp) {
        //        boolean isRotate = false;
        //        FindIterable<Document> fStart = collection.find(mongoFilters).sort(new BasicDBObject("$natural", 1)).limit(1);
        //        FindIterable<Document> fEnd = collection.find(mongoFilters).sort(new BasicDBObject("$natural", -1)).limit(1);
        //
        //        int opLogStartTimestamp = fStart.first().get("ts", BsonTimestamp.class).getTime();
        //        int opLogEndTimestamp = fEnd.first().get("ts", BsonTimestamp.class).getTime();
        //        int lastCheckpoint = getLastCheckpoint().getTime();
        //        int lossTime = 0;
        //        if (lastCheckpoint > 0) {
        //            lossTime = opLogStartTimestamp - lastCheckpoint;
        //        }
        //        if (lastCheckpoint > 0 && readTimestamp < opLogStartTimestamp) {
        //            isRotate = true;
        //            log.warn("################oplog rotate: readTimestamp" + readTimestamp + " opLogstarttimestamp:" + opLogStartTimestamp + " opLogendtimestamp" + opLogEndTimestamp);
        //            if (lossTime > 0) {
        //                if (lossTime > opLogLossTime) {
        //                    log.warn("################ oplog rotate loss log time:" + lossTime + "  tsTimestamp:" + readTimestamp + " lastcheckpointTimestamp" + lastCheckpoint + " opLogLossTime:" + opLogLossTime);
        //                    isRotate = true;
        //                } else {
        //                    log.warn("################ oplog rotate loss log time:" + lossTime + "  tsTimestamp:" + readTimestamp + " lastcheckpointTimestamp" + lastCheckpoint + " opLogLossTime:" + opLogLossTime);
        //                    isRotate = false;
        //                }
        //            }
        //        }
        //        return isRotate;
        //    }
        //
    }

    public List<Document> findDocumentsByIds(MongoCollection<Document> collection, List<ObjectId> ids) {
        List<Document> result = new ArrayList<>();
        Bson filter = Filters.in("_id", ids);
        FindIterable<Document> findIterable = collection.find(filter);
        for (Document document : findIterable) {
            result.add(document);
        }
        return result;
    }


    private void setCheckpoint(int timestamp, int inc) {
        log.info("++++++++++++++++++setCheckpoint:{} ++++++++++++++++++inc:{}+++++++++++++++++++++++++++++++++++", timestamp, inc);
        OPLogCheckpoint.Checkpoint checkpoint = OPLogCheckpoint.Checkpoint.builder().build();
        checkpoint.setTimestamp(timestamp);
        checkpoint.setInc(inc);
        FileOutputStream output = null;
        try {
            output = new FileOutputStream(new File(checkPointFile));
            checkpoint.writeTo(output);
            output.close();
        } catch (FileNotFoundException e) {
            log.warn("################FileNotFoundException:", e);
        } catch (IOException e) {
            log.warn("################IOException:", e);
        } catch (Exception e) {
            log.warn("################Exception:", e);
        } finally {
            try {
                if (output != null)
                    output.close();
            } catch (IOException e) {
                log.warn("################IOException again:", e);
            }
        }
    }

    private BsonTimestamp getLastCheckpoint() {
        BsonTimestamp bsonTimestamp = new BsonTimestamp();
        File cpFile = new File(checkPointFile);
        if (!cpFile.exists()) {
            if (!cpFile.getParentFile().exists()) {
                boolean m = cpFile.getParentFile().mkdirs();
                if (!m) {
                    log.warn("checkpoint file parent dir create failed: {}", cpFile.getParentFile().getAbsolutePath());
                }
            }
            log.warn("checkpoint file not exists: {}", new File(checkPointFile).getAbsolutePath());
            setCheckpoint(0, 0);
            return bsonTimestamp;
        }

        FileInputStream input = null;
        try {
            input = new FileInputStream(checkPointFile);
            OPLogCheckpoint.Checkpoint in = OPLogCheckpoint.Checkpoint.parseFrom(input);
            int timestamp = in.getTimestamp();
            int inc = in.getInc();
            bsonTimestamp = new BsonTimestamp(timestamp, inc);
            input.close();
        } catch (FileNotFoundException e) {
            log.error("File not found: {}", checkPointFile, e);
        } catch (IOException e) {
            log.error("File IOException: {}", checkPointFile, e);
        } catch (Exception e) {
            log.error("File Exception: {}", checkPointFile, e);
        } finally {
            try {
                if (input != null) {
                    input.close();
                }
            } catch (IOException e) {
                log.error("Cannot close file {}. ", checkPointFile, e);
            }
        }
        return bsonTimestamp;
    }
}
