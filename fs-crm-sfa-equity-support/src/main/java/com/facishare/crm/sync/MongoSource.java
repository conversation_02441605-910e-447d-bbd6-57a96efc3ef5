package com.facishare.crm.sync;

import com.github.autoconf.ConfigFactory;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.RateLimiter;
import com.mongodb.BasicDBObject;
import com.mongodb.ConnectionString;
import com.mongodb.CursorType;
import com.mongodb.client.*;
import com.mongodb.client.model.Filters;
import org.apache.commons.collections.CollectionUtils;
import org.apache.flume.Context;
import org.apache.flume.Event;
import org.apache.flume.EventDrivenSource;
import org.apache.flume.channel.ChannelProcessor;
import org.apache.flume.conf.Configurable;
import org.apache.flume.event.EventBuilder;
import org.apache.flume.instrumentation.SourceCounter;
import org.apache.flume.source.AbstractSource;
import org.bson.BsonTimestamp;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR> gongchunru
 * @date : 2023/6/29 14:57
 * @description:
 */
public class MongoSource extends AbstractSource implements EventDrivenSource, Configurable {
    private static final Logger LOG = LoggerFactory.getLogger(MongoSource.class);
    private String checkPointFile;
    private String opList;
    private String nsWhite;
    private String nsBlack;
    private Set<String> opSets = Sets.newHashSet();
    private int opLogLossTime;
    private String opLogDb;
    private String opLogCollection;
    private Bson mongoFilters;
    private int checkpointBatch;
    private int noNewDataMaxSleepTime = 1000;
    private int sleepStep = 100;
    private int maxBackoffSleep = 500;
    private ChannelProcessor channelProcessor;
    private SourceCounter sourceCounter;
    private MongoClient mongo;
    private long checkPointTime;
    private String isSyncAllData;
    private String mongoIps;
    private int mongoPort;
    private String mongoUserName;
    private String mongoPassword;
    private String mongoDbName;
    private ExecutorService threadpool = Executors.newFixedThreadPool(1);
    private static final String TENANT_ID = "tenant_id";
    private static final String SORT_OBJ = "object_describe";

    private RateLimiter limiter = RateLimiter.create(2);

    private boolean allNetRefreshSwitch;

    /**
     * flume 脚本启动后会执行该方法
     */
    @Override
    public void start() {
        channelProcessor = getChannelProcessor();
        sourceCounter.start();
        if (isSyncAllData.equals("true")) {
            LOG.info("-----------------------only syncAllData");
            setMongoConnection("");
            syncAllDbData();
            mongo.close();
            mongo=null;
            return;
        }
        ReadOpLog task = new ReadOpLog();
        threadpool.submit(task);

        ConfigFactory.getConfig("fs-refresh-es-data", iConfig -> {
            allNetRefreshSwitch = iConfig.getBool("all_net_refresh_switch", true);
        });

    }

    /**
     * 导数完成后，关闭mongo连接
     */
    @Override
    public void stop() {
        LOG.info("MongoSource stop ....");
        if (mongo != null) {
            mongo.close();
            mongo=null;
        }
        if (!threadpool.isShutdown()) {
            threadpool.shutdown();
        }
    }

    /**
     * flume 的配置文件会通过该方法读入，并传入key-value
     *
     * @param context
     */
    public void configure(Context context) {
        this.checkPointFile = context.getString("checkpointFile");
        this.opList = context.getString("opList");
        this.nsWhite = context.getString("nsWhite", ".*");
        this.nsBlack = context.getString("nsBlack", "");

        this.opLogDb = context.getString("opLogdb", "local");
        this.opLogCollection = context.getString("opLogCollection");
        this.opLogLossTime = context.getInteger("opLogLossTime", 0);
        /*oplog失效时长  当前第一条oplog-checkpoint timestamp差值**/
        this.checkpointBatch = context.getInteger("checkpointBatch", 100);
        this.noNewDataMaxSleepTime = context.getInteger("noNewDataMaxSleepTime", 10 * 1000);
        this.checkPointTime = context.getInteger("checkPointTime", 1000);
        this.isSyncAllData = context.getString("isSyncAllData", "false");

        //this.mongoIps = context.getString("mongoIps");
        //this.mongoPort = context.getInteger("mongoPort", 27017);
        //this.mongoUserName = context.getString("mongoUserName", "");
        //this.mongoPassword = context.getString("mongoPassword", "");
        //this.mongoDbName = context.getString("mongoDbName", "");

        LOG.info("----------------------------------------------------------------------------------------");
        LOG.info("checkPointFile:" + checkPointFile);
        LOG.info("opList:" + opList);
        LOG.info("nsWhite:" + nsWhite);
        LOG.info("mongoIps:" + mongoIps);
        LOG.info("mongoPort:" + mongoPort);
        LOG.info("mongoUserName:" + mongoUserName);
        LOG.info("mongoPassword:" + mongoPassword);
        LOG.info("mongoDbName:" + mongoDbName);
        LOG.info("----------------------------------------------------------------------------------------");

        Preconditions.checkArgument(checkPointFile != null, "checkpointFile is null");
        Preconditions.checkArgument(opLogDb != null, "opLogDb is null");
        Preconditions.checkArgument(opLogCollection != null, "opLogCollection is null");
        Preconditions.checkArgument(checkpointBatch > 0, "checkpointBatch should gt 0");

        LOG.info("isSyncAllData:" + isSyncAllData);
        sourceCounter = new SourceCounter(getName());
        String[] opSplit = this.opList.split(",");
        Collections.addAll(opSets, opSplit);
    }

    /**
     * 全量方式同步数据
     */
    private Long syncAllDbData() {
        //同步完全量数据后，以此时间点开始增量同步
        int timestamp = (int)(System.currentTimeMillis()/1000);
        setCheckpoint(timestamp,0);
//        LOG.info("-----------全量开始----------重置的timestamp："+timestamp);
//        setMongoConnection();
        Map<String, Set<String>> whiteMap = getMongoWhiteOrBlackNameList(nsWhite);
        Map<String, Set<String>> blackMap = getMongoWhiteOrBlackNameList(nsBlack);
        Set<String> whiteDb = whiteMap.keySet();
        long totalCount = 0L;
        for (String mongoDbName : whiteDb) {
            LOG.info("####################syncAllData   dbname:" + mongoDbName + " start#################");
            MongoDatabase mongoDatabase = mongo.getDatabase(mongoDbName);
            Set<String> set = Sets.newHashSet();
            Set<String> white = whiteMap.get(mongoDbName);
            Set<String> black = blackMap.get(mongoDbName);
//            if (white != null && white.size() > 0) {
            if (!CollectionUtils.isEmpty(white)) {
                set.addAll(white);
                if (black != null && black.size() > 0) {
                    set.removeAll(black);
                }
                MongoCollection<Document> collection;
                int count=0;
                for (String colName : set) {
                    collection = mongoDatabase.getCollection(colName);
                    FindIterable<Document> cursor;
                    //对于object_describe表需要特殊处理，按照revision升序排序,按照这种方式排序，数据量大时(超过32M)超过会导致内存溢出
                    if (SORT_OBJ.equalsIgnoreCase(colName)) {
                        Map<String,Integer> sortMap = Maps.newHashMap();
                        sortMap.put(TENANT_ID,1);
                        sortMap.put("api_name",1);
                        sortMap.put("revision",1);
                        Bson sortBson = new BasicDBObject(sortMap);
                        cursor = collection.find().sort(sortBson).noCursorTimeout(true);
                        count+=sendMessage(cursor,mongoDbName,colName);
                    } else {
                        cursor = collection.find().noCursorTimeout(true);
                        count+=sendMessage(cursor,mongoDbName,colName);
                    }
                }
                totalCount += count;
                LOG.info("$$$$$$$$$$$$$sync database alldata  db: " + mongoDbName + " total:" + count);
            }
        }
        LOG.info("---------------------sync all end------------------------");
//        if (mongo != null) {
//            mongo.close();
//            mongo=null;
//        }
        return totalCount;
    }

    /**
     * 遍历collection中的数据，并发送给flume
     */
    private int sendMessage(FindIterable<Document> cursor,String mongoDbName,String collectionName){
//        LOG.info("c*********************sendMessage:" +collectionName+"");
        Document message;
        int i = 0;
        for (Document doc : cursor) {
            i++;
            if(i%1000==0)
                LOG.info("-----------current dbname:"+mongoDbName+" collection :"+collectionName+" now ,have sync " +i+"");
            if (doc != null) {
                //LOG.info("mongoDbName:"+mongoDbName+"   collectionName:"+collectionName  );
                message = new Document();
                message.put("database", mongoDbName);
                message.put("collection", collectionName);
                message.put("_allorinc_", "all");
                message.put("op", "i");
                for (Map.Entry<String, Object> field : doc.entrySet()) {
                    String fieldName = field.getKey();
                    Object fieldValue = field.getValue();
                    message.put(fieldName, fieldValue);
                }
//                LOG.info("c*********************message body:" +message.toJson().toString()+"");
                //System.out.println(message.toJson().toString());
                try {
                    //发送消息，根据指定的需求，要按照tenant_id（eid）对message进行分区处理，同一tenant_id的数据发送到一个分区中
                    String kafkaKey ;
                    if (SORT_OBJ.equalsIgnoreCase(collectionName)){
                        kafkaKey = doc.getString(TENANT_ID);
                    }else{
                        kafkaKey = doc.get("_id") == null ? "null" : doc.get("_id").toString();
                    }
                    Event event = eventBuild(message.toJson(), kafkaKey);
                    if (event != null) {
                        processEvent(event);
                    }
                } catch (InterruptedException e) {
                    LOG.warn("################syncDbs InterruptedException:", e);
                }
            }
        }
        LOG.info("************DB:"+mongoDbName+" COLLECTION:"+collectionName +" total dataCount:"+i );
//        System.out.println("mongoDbName:"+mongoDbName+" collectionName:"+collectionName +" dataCount:"+i );
        return i;
    }

//    /**
//     * 根据db创建验证方式的连接,此方法暂时不用，都用admin进行数据库的验证
//     */
//    private void setMongoConnection(String mongoDbName) {
//
//        String mongoAddStr = getmongoAddress();
//        String mongoUrl = "";
//        try {
//            mongoUrl = String.format("mongodb://%s:%s@%s/%s", mongoUserName, URLEncoder.encode(mongoPassword, "UTF-8"), mongoAddStr, mongoDbName);
//        } catch (UnsupportedEncodingException e) {
//            LOG.error("UnsupportedEncoding while encode mongo password: {}", mongoPassword, e);
//        }
//        MongoClientURI uri = new MongoClientURI(mongoUrl);
//        mongo = new MongoClient(uri);
//    }

//    private String getMongoAddress(){
//        String[] ips = mongoIps.split(",");
//        StringBuilder mongoAdd = new StringBuilder();
//        for (String ip : ips) {
//            mongoAdd.append(ip + ":" + mongoPort + ",");
//        }
//        String mongoAddStr = mongoAdd.substring(0, mongoAdd.length() - 1);
//        return mongoAddStr;
//    }
    /**
     * 无DB时创建连接，供admin验证使用，用户读取oplog
     */
//    private void setMongoConnection_1() {
//        String mongoUrl = "";
//        try {
//            //测试环境不授权某个db，依然可以获取oplor.rs 这个日志collection的数据，但是对于其他collection是不行的
//            //mongoUrl = String.format("mongodb://%s:%s@%s:%s", mongoUserName, URLEncoder.encode(mongoPassword,"UTF-8"), mongoIps.split(",")[0], mongoPort);
//            String mongoAddStr = getMongoAddress();
//            mongoUrl = String.format("mongodb://%s:%s@%s/admin", mongoUserName, URLEncoder.encode(mongoPassword, "UTF-8"), mongoAddStr);
////            System.out.println("mongoUrl:" + mongoUrl);
//        } catch (UnsupportedEncodingException e) {
//            LOG.error("UnsupportedEncoding while encode mongo password: {}", mongoPassword, e);
//        }
//        MongoClientURI uri = new MongoClientURI(mongoUrl);
//        mongo = new MongoClient(uri);
//        LOG.debug("replicateSetName:" + mongo.getMongoClientOptions().getRequiredReplicaSetName());
//        LOG.debug("mongoHosts:" + mongoIps);
//    }

    private void setMongoConnection(String mongoConnectionString){
        if(mongo!=null) return ;
        ConnectionString connection = new ConnectionString(mongoConnectionString);
        mongo = MongoClients.create(connection);
        LOG.info("=======================new MongoClient");
    }

    /**
     * 发送消息
     */
    private void processEvent(Event event) throws InterruptedException {
        if (event.getBody().length == 0) {
            LOG.warn("body length: 0");
            return;
        }
        LOG.debug(event.toString());
        boolean done = false;
        sourceCounter.incrementAppendReceivedCount();
        sourceCounter.incrementEventReceivedCount();
        while (!done) {
            try {
                channelProcessor.processEvent(event);
                done = true;
            } catch (Exception e) {
                LOG.error("Unhandled exception, logging and sleeping for " + maxBackoffSleep + "ms", e);
                try {
                    Thread.sleep(maxBackoffSleep);
                } catch (InterruptedException ex) {
                    throw new InterruptedException(ex.getMessage());
                }
            }
        }
        sourceCounter.incrementAppendAcceptedCount();
        sourceCounter.incrementEventAcceptedCount();
    }

    private Event eventBuild(String body, String key) {
        try {
            Event event = eventBuild(body.getBytes("UTF-8"), key);
            if (event == null) {
                LOG.error("Event key is empty, body: {}", body);
            }
            return event;
        } catch (UnsupportedEncodingException e) {
            LOG.warn("################UnsupportedEncodingException:" + body, e);
        }
        return null;
    }

    private Event eventBuild(byte[] body, String key) {
        Map<String, String> headers = Maps.newHashMap();
        if (Strings.isNullOrEmpty(key)) {
            return null;
        }
        headers.put("key", key);
        return EventBuilder.withBody(body, headers);
    }

    /**
     * 读取oplog
     */
    private class ReadOpLog implements Callable {
        @Override
        public Long call() {
            //连接admin库，授权
            long noNewDataNo = 1L;
            setMongoConnection("");
            //LOG.info("@@@@@@@@@@@@"+nsWhite+" start sync @@@@@@@@@@");
            while (true) {
                Long fieldNo = readLog();
                if (fieldNo == -1) {
                    LOG.error("###############opLog is refreshed#############");
                    /*
                     *处理oplog refresh
                     **/
                    setCheckpoint(0, 0);
                    noNewDataNo = -1;
                    mongo.close();
                    mongo=null;
                    break;
                } else if (fieldNo == 0) {
                    long sleepTime = Math.min(noNewDataNo * sleepStep, noNewDataMaxSleepTime);
                    try {
                        Thread.sleep(sleepTime);
                    } catch (InterruptedException e) {
                        LOG.warn("Thread sleep error", e);
                        Thread.currentThread().interrupt();
                    }
                    noNewDataNo++;
                    LOG.info("No increased data, sleep {} milliseconds. ", sleepTime);
                } else if (fieldNo > 0) {
                    noNewDataNo = 1;
                }
            }
            return noNewDataNo;
        }

        /**
         * 增量读取日志
         */

        private  Long readLog() {
            BsonTimestamp lastCheckPoint = getLastCheckpoint();
            int lastCheckPointTimestamp = lastCheckPoint.getTime();
            if (lastCheckPointTimestamp == 0) {
                LOG.info("-----------------------before increase sync, should sync all data first!");
                return syncAllDbData();
            }

            Long fieldNo = 0L;
            FindIterable<Document> cursor = null;
            try {
//                setMongoConnection();
                // 增量数据
                MongoDatabase  db = mongo.getDatabase(opLogDb);
                MongoCollection<Document> collection = db.getCollection(opLogCollection);
                /*
                 * 生成Filters
                 */
                List<Bson> filterLists = Lists.newArrayList();
                //必须要有ts字段OplogReplay query才生效，否则报错
                filterLists.add(Filters.gte("ts", lastCheckPoint));
                //加入checkpoint丢失，则读取先读取整个oplog中的日志，然后再增量
//                filterLists.add(Filters.gte("ts", new BsonTimestamp(0,0)));
                filterLists.add(Filters.in("op", opSets));

                if (!nsBlack.equals("")) {
                    filterLists.add(Filters.not(Filters.regex("ns", nsBlack)));
                }

                if (!nsWhite.equals("")) {
                    filterLists.add(Filters.regex("ns", nsWhite));
                }

                mongoFilters = Filters.and(filterLists);
                LOG.debug("start collection find");
                cursor = collection.find(mongoFilters).noCursorTimeout(true)
                        .oplogReplay(true)
                        .cursorType(CursorType.Tailable)
                        .cursorType(CursorType.TailableAwait);
                LOG.debug("end collection find");

                BsonTimestamp ts;
//                int firstTimestamp = cursor.first().get("ts", BsonTimestamp.class).getTime();
//                LOG.info("firstTimestamp:" + firstTimestamp);
//                boolean isRotate = checkOpLogIsRotate(collection,firstTimestamp);
//                if (isRotate) {
//                    LOG.error("+++++++++++++++opLog is refreshed before loop cursor!!!");
//                    return -1L;
//                } else {
                LOG.info("+++++++++++++++"+nsWhite+"ok,now increment read is begin!");
//                }

                Document message;
                for (Document doc : cursor) {
                    //只有在异常的情况下才会跳出cursor增量监听的for循环，进入while(true)循环
                    int timestamp = 0;
                    int inc = 0;
                    //long lastTimestamp = System.currentTimeMillis();
                    long lastTimestamp = getLastCheckpoint().getTime();
                    long nowTimestamp;

                    ts = doc.get("ts", BsonTimestamp.class);
                    String op = doc.getString("op");
                    //下面是在轮询的情况下防止游标过期，在tail方式下去掉
//                    if (op!=null&&!"".equals(op)) {
//                        fieldNo++;
//                        if (lastCheckPointTimestamp > 0) {
//                            if (ts.getTime() <= lastCheckPointTimestamp) {
//                                //LOG.debug("find lastcheckpoint oplog:" + lastCheckPoint);
//                                continue;
//                            } else {
//                                int lossTime = ts.getTime() - lastCheckPointTimestamp;
//                                if (lossTime > opLogLossTime) {
//                                    LOG.warn("################oplog rotate loss log time:" + lossTime + "  tsTimestamp:" + ts.getTime() + " lastcheckpointTimestamp:" + lastCheckPointTimestamp);
//                                    return -1L;
//                                }
//                            }
//                        }
//                    } else {
//                        continue;
//                    }

                    String ns = doc.getString("ns");
                    String dbName = "";
                    String colName = "";
                    if (ns != null && !"".equals(ns)) {
                        dbName = ns.split("\\.")[0];
                        colName = ns.split("\\.")[1];
                    }

                    message = new Document();
                    message.put("database", dbName);
                    message.put("collection", colName);
                    message.put("_allorinc_", "inc");
                    message.put("op", op);
                    message.put("ts", ts);
                    message.put("o", doc.get("o"));

                    Document oObj = (Document) doc.get("o");
                    Document oObj2 = (Document) doc.get("o2");
                    String kafkaKey ;
                    if (SORT_OBJ.equalsIgnoreCase(colName)){
                        //针对object_describe表只有insert没有update和delete
                        if ("u".equals(op)){
                            kafkaKey = oObj2.get(TENANT_ID)==null?(oObj.get(TENANT_ID)==null?"null":oObj.get(TENANT_ID).toString())
                                    :oObj2.get(TENANT_ID).toString();
                        }else{
                            kafkaKey = oObj.get(TENANT_ID)==null?"null":oObj.get(TENANT_ID).toString();
                        }
                    }else{
                        if ("u".equals(op)){
                            kafkaKey = oObj2.get("_id")==null?"null":oObj2.get("_id").toString();
                        }else{
                            kafkaKey = oObj.get("_id")==null?"null":oObj.get("_id").toString();
                        }
                    }

                    if ("u".equals(op)) {
                        message.put("o2", doc.get("o2"));
                    }

                    Event event = eventBuild(message.toJson(), kafkaKey);
                    if (event != null) {
                        processEvent(event);
                    }
                    timestamp = ts.getTime();
                    inc = ts.getInc();
                    nowTimestamp = System.currentTimeMillis();
                    //保证不是频繁的更新时间点，达到一定量50条或者时间超过500ms后才更新时间
                    if (fieldNo % checkpointBatch == 0 || (nowTimestamp - lastTimestamp) >= checkPointTime) {
                        //LOG.debug("checkpointBatch do checkpoint");
                        LOG.info("+++++"+nsWhite+" read opLog records:" + fieldNo);
                        setCheckpoint(timestamp, inc);
//                        isRotate = checkOpLogIsRotate(collection,ts.getTime());
//                        if (isRotate) {
//                            LOG.warn("+++++++++++++++opLog is refreshed   while loop cursor!!!");
//                            return -1L;
//                        }
                    }
                    fieldNo++;
                }
                //LOG.debug("all records:" + fieldNo);

            } catch (Exception e) {
                LOG.warn("################for loop,read oplog increament exception:", e);
            } finally {
                if(cursor!=null){
                    cursor.iterator().close();
                }
            }
            return fieldNo;
        }

        /**
         * @param readTimestamp : oplog游标位置
         *                      opLogStartTimestamp ：当前oplog头位置
         *                      opLogEndTimestamp ：当前oplog尾位置
         *                      lastCheckpoint ：已经读到的位置
         *                      lossTime : opLogStartTimestamp - lastCheckpoint ：还有多长时间段内的消息要读
         * @return boolean
         */
//        private boolean checkOpLogIsRotate(MongoCollection<Document> collection,int readTimestamp) {
//            boolean isRotate = false;
//            FindIterable<Document> fStart = collection.find(mongoFilters).sort(new BasicDBObject("$natural", 1)).limit(1);
//            FindIterable<Document> fEnd = collection.find(mongoFilters).sort(new BasicDBObject("$natural", -1)).limit(1);
//
//            int opLogStartTimestamp = fStart.first().get("ts", BsonTimestamp.class).getTime();
//            int opLogEndTimestamp = fEnd.first().get("ts", BsonTimestamp.class).getTime();
//            int lastCheckpoint = getLastCheckpoint().getTime();
//            int lossTime = 0;
//            if (lastCheckpoint > 0) {
//                lossTime = opLogStartTimestamp - lastCheckpoint;
//            }
//            if (lastCheckpoint > 0 && readTimestamp < opLogStartTimestamp) {
//                isRotate = true;
//                LOG.warn("################oplog rotate: readTimestamp" + readTimestamp + " opLogstarttimestamp:" + opLogStartTimestamp + " opLogendtimestamp" + opLogEndTimestamp);
//                if (lossTime > 0) {
//                    if (lossTime > opLogLossTime) {
//                        LOG.warn("################ oplog rotate loss log time:" + lossTime + "  tsTimestamp:" + readTimestamp + " lastcheckpointTimestamp" + lastCheckpoint + " opLogLossTime:" + opLogLossTime);
//                        isRotate = true;
//                    } else {
//                        LOG.warn("################ oplog rotate loss log time:" + lossTime + "  tsTimestamp:" + readTimestamp + " lastcheckpointTimestamp" + lastCheckpoint + " opLogLossTime:" + opLogLossTime);
//                        isRotate = false;
//                    }
//                }
//            }
//            return isRotate;
//        }

    }


    /**
     * 最后一次偏移量
     *
     * @return BsonTimestamp
     */
    private BsonTimestamp getLastCheckpoint() {
        BsonTimestamp bsonTimestamp = new BsonTimestamp();
        File cpFile = new File(checkPointFile);
        if (!cpFile.exists()) {
            if(!cpFile.getParentFile().exists()) {
                cpFile.getParentFile().mkdirs();
            }
            LOG.warn("checkpoint file not exists: {}", new File(checkPointFile).getAbsolutePath());
            return bsonTimestamp;
        }

        FileInputStream input = null;
        try {
            input = new FileInputStream(new File(checkPointFile));
            //OPlogCheckpoint.Checkpoint in = OPlogCheckpoint.Checkpoint.parseFrom(input);
            //int timestamp = in.getTimestamp();
            //int inc = in.getInc();
            //bsonTimestamp = new BsonTimestamp(timestamp, inc);
            input.close();
        } catch (FileNotFoundException e) {
            LOG.error("File not found: {}", checkPointFile, e);
        } catch (IOException e) {
            LOG.error("File IOException: {}", checkPointFile, e);
        } catch (Exception e) {
            LOG.error("File Exception: {}", checkPointFile, e);
        } finally {
            try {
                if (input != null) {
                    input.close();
                }
            } catch (IOException e) {
                LOG.error("Cannot close file {}. ", checkPointFile, e);
            }
        }
        return bsonTimestamp;
    }

    /**
     * 设置偏移量
     *
     * @param timestamp :時間戳
     * @param inc ：增量
     */
    private void setCheckpoint(int timestamp, int inc) {
        LOG.info("++++++++++++++++++setCheckpoint:"+timestamp+"++++++++++++++++++inc:"+inc+"+++++++++++++++++++++++++++++++++++");
//        OPlogCheckpoint.Checkpoint.Builder checkpoint = OPlogCheckpoint.Checkpoint.newBuilder();
//        checkpoint.setTimestamp(timestamp);
//        checkpoint.setInc(inc);
//        OPlogCheckpoint.Checkpoint cp = checkpoint.build();
//        FileOutputStream output = null;
//        try {
//            output = new FileOutputStream(new File(checkPointFile));
//            cp.writeTo(output);
//            output.close();
//        } catch (FileNotFoundException e) {
//            LOG.warn("################FileNotFoundException:", e);
//        } catch (IOException e) {
//            LOG.warn("################IOException:", e);
//        } catch (Exception e) {
//            LOG.warn("################Exception:", e);
//        } finally {
//            try {
//                if (output != null)
//                    output.close();
//            } catch (IOException e) {
//                LOG.warn("################IOException again:", e);
//            }
//        }
    }

    /**
     * 根据名单字符串解析出库-列的对关系map
     * @param nsWhiteOrNsBlackName :黑白名單
     * @return库表键值对
     */
    private Map<String, Set<String>> getMongoWhiteOrBlackNameList(String nsWhiteOrNsBlackName) {
        Map<String, Set<String>> dbcolMap = Maps.newHashMap();
        if (nsWhiteOrNsBlackName == null) return dbcolMap;
        String[] nameList = nsWhiteOrNsBlackName.split("\\|");
        for (String dbcol : nameList) {
            if (dbcol == null || "".equals(dbcol) || dbcol.indexOf(".") == -1)
                continue;
            String db = dbcol.split("\\.")[0];
            String col = dbcol.split("\\.")[1];
            if ("*".equals(db) && "*".equals(col)) {
                ListDatabasesIterable<Document> dbs = mongo.listDatabases();
                dbcolMap.clear();
                for (Document doc : dbs) {
                    String dbNameTemp = doc.getString("name");
                    ListCollectionsIterable<Document> cols = mongo.getDatabase(dbNameTemp).listCollections();
                    Set<String> whiteColSet = Sets.newHashSet();
                    for (Document doc2 : cols) {
                        String colNameTemp = doc2.getString("name");
                        whiteColSet.add(colNameTemp);
                    }
                    dbcolMap.put(dbNameTemp, whiteColSet);
                }
                break;
            } else if (!"*".equals(db) && "*".equals(col)) {
                ListCollectionsIterable<Document> cols = mongo.getDatabase(db).listCollections();
                Set<String> whiteColSet = Sets.newHashSet();
                for (Document doc2 : cols) {
                    String colNameTemp = doc2.getString("name");
                    whiteColSet.add(colNameTemp);
                }
                dbcolMap.put(db, whiteColSet);
            }else if (!"*".equals(db) && col.indexOf("*")>0) {
                ListCollectionsIterable<Document> cols = mongo.getDatabase(db).listCollections();
                Set<String> whiteColSet = Sets.newHashSet();
                for (Document doc2 : cols) {
                    String colNameTemp = doc2.getString("name");
                    String key = col.replaceAll("\\*","");
                    if(colNameTemp.indexOf(key)>=0) {
                        whiteColSet.add(colNameTemp);
                    }
                }
                dbcolMap.put(db, whiteColSet);
            }else if (!"*".equals(db) && !"*".equals(col)) {
                Set<String> whiteColSet = dbcolMap.get(db) == null ? Sets.newHashSet() : dbcolMap.get(db);
                whiteColSet.add(col);
                dbcolMap.put(db, whiteColSet);
            }
        }
        return dbcolMap;
    }
//    public static void main(String[] args) {
//        MongoSource ms = new MongoSource();
//        ms.mongoDbName = "metadata-engine-mongodb";
//
//        ms.mongoIps = "***********";
//        ms.mongoPort = 27017;
//        ms.mongoUserName = "metadata";
//        ms.mongoPassword = "1234";
//
//        ms.mongoUserName = "myLocalUser";
//        ms.mongoPassword = "w!a9x*+SJnLvEQ#g~Z8e";
//
////        ms.isSyncAllData="false";
////        ms.mongoIps = "***********";
////        ms.mongoUserName = "engine_user";
////        ms.mongoPassword = "w$4vKWi9toJmGbiRa%NJ";
//
////        ms.nsBlack = "metadata-engine-mongodb.object_data_archive";
////        ms.nsWhite="metadata-engine-mongodb.object_describe|metadata-engine-mongodb.*|metadata-engine-data-4.*";
////        ms.nsWhite = "metadata-engine-mongodb.object_data_0";
////        ms.nsWhite="metadata-engine-mongodb.auto_number";
////        ms.start();
//        ms.nsWhite="metadata-engine-mongodb.object_describe";
////        ms.nsWhite="metadata-engine-mongodb.object_describe*";
////        ms.nsWhite="metadata-engine-mongodb.object_data*";
//        ms.syncAllDbData();
//        System.out.println(System.currentTimeMillis());



//        //下面是增量测试
//
//        String mongoUrl = "";
//        try {
//            //测试环境不授权某个db，依然可以获取oplor.rs 这个日志collection的数据，但是对于其他collection是不行的
//            //mongoUrl = String.format("mongodb://%s:%s@%s:%s", mongoUserName, URLEncoder.encode(mongoPassword,"UTF-8"), mongoIps.split(",")[0], mongoPort);
//            mongoUrl = String.format("mongodb://%s:%s@%s:%s/admin", "myLocalUser", java.net.URLEncoder.encode("w!a9x*+SJnLvEQ#g~Z8e", "UTF-8"), "***********".split(",")[0], 27017);
//        } catch (UnsupportedEncodingException e) {
//            LOG.error("UnsupportedEncoding while encode mongo password: {}", "", e);
//        }
//        MongoClientURI uri = new MongoClientURI(mongoUrl);
//        MongoClient mongo = new MongoClient(uri);
//
//        while (true) {
//            MongoDatabase db;
//            MongoCollection collection;
//
//            db = mongo.getDatabase("local");
//            collection = db.getCollection("oplog.rs");
//            /*
//             * 生成Filters
//             */
//            BsonTimestamp lastCheckPoint = new BsonTimestamp();
//            Set<String> opSets = Sets.newHashSet();
//            opSets.add("i");
//            opSets.add("u");
//            List<Bson> filterLists = Lists.newArrayList();
//            filterLists.add(Filters.gte("ts", lastCheckPoint));
//            filterLists.add(Filters.in("op", opSets));
//            filterLists.add(Filters.regex("ns", "metadata-engine-mongodb.test"));
//            FindIterable<Document> cursor = null;
//            try {
//                Bson  mongoFilters = Filters.and(filterLists);
//                LOG.debug("start collection find");
//                cursor = collection.find(mongoFilters).noCursorTimeout(true)
//                        .oplogReplay(true)
//                        .cursorType(CursorType.Tailable)
//                        .cursorType(CursorType.TailableAwait);
//                LOG.debug("end collection find");
//                System.out.println("1111111111");
//                //第一次访问游标时速度很慢，会把整个oplog读一遍，以后就会增量读，并且每来一条数据，就会执行且只执行for循环中的内容，例如Thread.sleep（1000）不会被执行
//                //而且外层也没有必要加while (true)，因为他并不是通过while (true)来循环的
//                for (Document doc : cursor) {
//                    System.out.println("2");
//                    System.out.println(doc.toJson().toString());
//                    System.out.println("3");
////                    只有在异常的情况下才会跳出cursor增量监听，进入while(true)循环
////                    throw new Exception("");
//                }
////                Thread.sleep(1000);
//            } catch (Exception e) {
//                   System.out.println("yichangle");
//                try {
//                    Thread.sleep(2000);
//                } catch (InterruptedException e1) {
//                    e1.printStackTrace();
//                }
//            }finally {
//                if(cursor != null){
//                }
//            }
//        }
//    }


//    public static void main(String[] args){
//        String fname = "D:/a/b/c/hello.txt";
//        File cpFile = new File(fname);
//        if (!cpFile.exists()) {
//            if(!cpFile.getParentFile().exists()) {
//                cpFile.getParentFile().mkdirs();
//            }
//            LOG.warn("checkpoint file not exists: {}", new File(fname).getAbsolutePath());
//        }
//    }
}
