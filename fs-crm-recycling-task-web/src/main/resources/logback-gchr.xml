<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true">
	<!-- ch.qos.logback.core.ConsoleAppender 控制台输出 -->
	<appender name="console" class="ch.qos.logback.core.ConsoleAppender">
		<filter class="ch.qos.logback.core.filter.EvaluatorFilter">
			<evaluator>
				<expression>return message.contains("RocketmqClient") || message.contains("fs-paas-appframework-config") || message.contains("EVENT_CONNECT_ERROR") || message.contains("reload config");</expression>
			</evaluator>
			<OnMatch>DENY</OnMatch>
			<OnMismatch>ACCEPT</OnMismatch>
		</filter>
		<encoder>
			<pattern>%d{HH:mm:ss.SSS} %green([%thread]) %highlight(%-5level) %logger %X{traceId} %X{userId} %cyan(%msg%n)</pattern>
		</encoder>
	</appender>


	<logger name="com.facishare.paas.metadata.service.impl" level="error" additivity="false">
		<appender-ref ref="console"/>
	</logger>

	<logger name="com.facishare.crm.sfa" level="info" additivity="false">
		<appender-ref ref="console"/>
	</logger>

	<logger name="com.facishare.paas.metadata.cache.DescribeCache" level="error" additivity="false">
		<appender-ref ref="console"/>
	</logger>

	<logger name="org.apache.rocketmq" additivity="false" level="error">
		<appender-ref ref="console" />
	</logger>

	<logger name="com.fxiaoke.notifier.support" additivity="false" level="error">
		<appender-ref ref="console" />
	</logger>
	<logger name="com.facishare.crm.sync" additivity="false" level="debug">
		<appender-ref ref="console" />
	</logger>
	<logger name="org.mongodb.driver.protocol.command" level="error">
		<appender-ref ref="console" />
	</logger>
	<root level="info">
		<appender-ref ref="console" />
	</root>
</configuration>
