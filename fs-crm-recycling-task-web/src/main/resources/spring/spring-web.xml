<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xmlns:p="http://www.springframework.org/schema/p"
       default-lazy-init="true" xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
       http://www.springframework.org/schema/context
       http://www.springframework.org/schema/context/spring-context-3.1.xsd
       http://www.springframework.org/schema/mvc
       http://www.springframework.org/schema/mvc/spring-mvc.xsd">

  <bean id="fastJsonConfig" class="com.alibaba.fastjson.support.config.FastJsonConfig"
        p:charset="UTF-8"
        p:dateFormat="yyyy-MM-dd HH:mm:ss"/>
  <import resource="classpath:lto.xml"/>


  <mvc:annotation-driven>
    <mvc:message-converters>
      <bean class="org.springframework.http.converter.StringHttpMessageConverter" p:supportedMediaTypes="text/plain; charset=UTF-8"/>
    </mvc:message-converters>
  </mvc:annotation-driven>
  <context:component-scan base-package="com.facishare.crm"/>

  <!-- 容器默认的DefaultServletHandler处理 所有静态内容与无RequestMapping处理的URL-->
  <mvc:default-servlet-handler/>
</beans>
