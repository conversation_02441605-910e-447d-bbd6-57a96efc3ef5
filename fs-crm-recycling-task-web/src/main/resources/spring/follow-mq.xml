<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">


    <context:annotation-config/>

    <bean id="crmFollowActionProcessor" class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer"
          init-method="start" destroy-method="shutdown">
        <constructor-arg name="configName" value="fs-crm-task-sfa-mq.ini"/>
        <constructor-arg name="sectionNames" value="crm-task-follow-action-consumer"/>
        <constructor-arg name="messageListener" ref="crmActionListener"/>
    </bean>

    <bean id="crmFollowActionVIPProcessor" class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer"
          init-method="start" destroy-method="shutdown">
        <constructor-arg name="configName" value="fs-crm-task-sfa-mq.ini"/>
        <constructor-arg name="sectionNames" value="crm-task-follow-action-vip-consumer"/>
        <constructor-arg name="messageListener" ref="crmActionVIPListener"/>
    </bean>


    <!--    监听新feed的消息-->
    <bean id="crmNewFeedListener" class="com.facishare.crm.follow.mq.CrmNewFeedListener"/>
    <bean id="crmNewFeedProcessor"
          class="com.facishare.paas.appframework.common.mq.RocketMQMessageProcessor"
          p:configName="fs-crm-follow-new-feed-consumer"
          p:rocketMQMessageListener-ref="crmNewFeedListener"/>

    <!--665 新增 监听自定义对象对象级变更服务配置-->
    <bean id="objectDescribeChangeForPoolFieldPermissionListener"
          class="com.facishare.crm.follow.mq.ObjectDescribeChangeForPoolFieldPermissionListener"/>
    <bean id="objectDescribeChangeForPoolFieldPermissionProcessor"
          class="com.facishare.paas.appframework.common.mq.RocketMQMessageProcessor"
          p:configName="crm_object_describe_permission_setting"
          p:rocketMQMessageListener-ref="objectDescribeChangeForPoolFieldPermissionListener"/>

    <!--监听自定义对象对象级变更服务配置-->
    <bean id="objectDescribeChangeListener" class="com.facishare.crm.follow.mq.ObjectDescribeChangeListener"/>
    <bean id="objectDescribeChangeProcessor"
          class="com.facishare.paas.appframework.common.mq.RocketMQMessageProcessor"
          p:configName="fs-crm-follow-describe-change-consumer"
          p:rocketMQMessageListener-ref="objectDescribeChangeListener"/>

    <!--商机阶段变更跟进行为MQ监听-->
    <bean id="opportunityStageChangeListener"
          class="com.facishare.crm.follow.mq.OpportunityStageChangeListener"/>
    <bean id="opportunityStageChangeProcessor"
          class="com.facishare.paas.appframework.common.mq.RocketMQMessageProcessor"
          p:configName="fs-crm-follow-opportunityStageChange-consumer"
          p:rocketMQMessageListener-ref="opportunityStageChangeListener"/>
</beans>