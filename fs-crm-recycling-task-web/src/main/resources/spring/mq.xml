<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">


    <bean id="objectLimitRuleListener" class="com.facishare.crm.recycling.task.executor.consumer.ObjectLimitRuleCalculateListener"/>
    <bean id="objectLimitRuleProcessor"
          class="com.facishare.paas.appframework.common.mq.RocketMQMessageProcessor"
          p:configName="crm_object_limit_calculate"
          p:rocketMQMessageListener-ref="objectLimitRuleListener"/>
    <bean id="poolAllocateRuleMemberWheresCalculateListener" class="com.facishare.crm.recycling.task.executor.consumer.PoolAllocateRuleMemberWheresCalculateListener"/>
    <bean id="poolAllocateRuleMemberWheresCalculateProcessor"
          class="com.facishare.paas.appframework.common.mq.RocketMQMessageProcessor"
          p:configName="crm_pool_member_wheres_calculate_mq"
          p:rocketMQMessageListener-ref="poolAllocateRuleMemberWheresCalculateListener"/>

    <bean id="objectLimitRuleAdaptUserGroupChangedListener" class="com.facishare.crm.recycling.task.executor.consumer.ObjectLimitRuleAdaptUserGroupChangedListener"/>
    <bean id="objectLimitRuleAdaptUserGroupChangedProcessor"
          class="com.facishare.paas.appframework.common.mq.RocketMQMessageProcessor"
          p:configName="crm_object_limit_user_group"
          p:rocketMQMessageListener-ref="objectLimitRuleAdaptUserGroupChangedListener"/>

    <bean id="objectLimitRuleAdaptUserRoleChangedListener" class="com.facishare.crm.recycling.task.executor.consumer.ObjectLimitRuleAdaptUserRoleChangedListener"/>
    <bean id="objectLimitRuleAdaptUserRoleChangedProcessor"
          class="com.facishare.paas.appframework.common.mq.RocketMQMessageProcessor"
          p:configName="crm_object_limit_user_role"
          p:rocketMQMessageListener-ref="objectLimitRuleAdaptUserRoleChangedListener"/>

    <bean id="objectLimitRuleAdaptPartnerChangedListener" class="com.facishare.crm.recycling.task.executor.consumer.ObjectLimitRuleAdaptPartnerChangedListener"/>
    <bean id="objectLimitRuleAdaptPartnerChangedProcessor"
          class="com.facishare.paas.appframework.common.mq.RocketMQMessageProcessor"
          p:configName="crm_object_limit_partner"
          p:rocketMQMessageListener-ref="objectLimitRuleAdaptPartnerChangedListener"/>

    <bean id="objectLimitRuleAdaptCustomObjectDataChangedListener" class="com.facishare.crm.recycling.task.executor.consumer.ObjectLimitRuleAdaptCustomObjectDataChangedListener"/>
    <bean id="customObjectDataChangeProcessor"
          class="com.facishare.paas.appframework.common.mq.RocketMQMessageProcessor"
          p:configName="crm_object_limit_customobjectdatachanged"
          p:rocketMQMessageListener-ref="objectLimitRuleAdaptCustomObjectDataChangedListener"/>

    <bean id="objectLimitRuleAdaptOrganizationChangedListener" class="com.facishare.crm.recycling.task.executor.consumer.ObjectLimitRuleAdaptOrganizationChangedListener"/>

    <bean id="recalculateOrgChangedListener" class="com.facishare.crm.recycling.task.executor.consumer.RecalculateOrgChangedListener"/>

    <bean id="crmRfmTaskListener" class="com.facishare.crm.recycling.task.executor.consumer.TaskListener"/>
    <bean id="crmRfmTaskProcessor"
          class="com.facishare.paas.appframework.common.mq.RocketMQMessageProcessor"
          p:configName="fs-crm-sfa-rfm-mq-consumer"
          p:rocketMQMessageListener-ref="crmRfmTaskListener"/>
</beans>
