<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd">


    <bean id="redissonClient" class="com.fxiaoke.common.redisson.RedissonFactoryBean">
        <property name="configName" value="fs-paas-metadata-service-redis"/>
    </bean>
    <bean id="dispatchLockService" class="com.fxiaoke.dispatcher.processor.LockService">
        <constructor-arg ref="redissonClient"/>
        <property name="prefix" value="sfa-follow-"/>
    </bean>

    <bean id="followDataEventParser" class="com.facishare.crm.follow.dispatch.FollowDataEventParser"/>

    <bean id="followEventListener" class="com.facishare.crm.follow.dispatch.FollowEventListener"/>

    <bean id="eventPorter" class="com.fxiaoke.dispatcher.processor.EventPorter"
          depends-on="eventStore,dispatchLockService,dynamicOrder">
        <constructor-arg name="store" ref="eventStore"/>
        <constructor-arg name="parser" ref="followDataEventParser"/>
        <constructor-arg name="lock" ref="dispatchLockService"/>
        <constructor-arg name="order" ref="dynamicOrder"/>
        <constructor-arg name="counter" ref="getMetrics"/>
        <property name="configName" value="fs-crm-follow-crm-action-consumer"/>
        <property name="processor" ref="followEventListener"/>
    </bean>


    <bean id="eventStore" class="com.fxiaoke.dispatcher.store.EventStore">
        <property name="configName" value="fs-crm-follow-crm-action-consumer"/>
        <property name="sectionName" value="store"/>
        <constructor-arg ref="mongoStore"/>
    </bean>

    <bean id="mongoStore" class="com.github.mongo.support.MongoDataStoreFactoryBean">
        <property name="configName" value="fs-crm-follow-crm-action-consumer"/>
        <property name="sectionNames" value="mongo"/>
    </bean>

    <bean id="dynamicOrder" class="com.fxiaoke.dispatcher.processor.DynamicOrder"/>
</beans>
