package com.facishare.crm.recycling.task.web;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.recycling.task.model.EquityResult;
import com.facishare.crm.sfa.equity.es.EquityESService;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.dao.pg.entity.metadata.Describe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.service.impl.ObjectDescribeServiceImpl;
import com.facishare.paas.service.model.ObjectDataDocument;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> gongchunru
 * @date : 2023/7/12 20:13
 * @description:
 */
@Slf4j
@RestController
@RequestMapping("/api/rest/equity")
public class EquityESServiceController {

    @Autowired
    private EquityESService equityESService;

    @Autowired
    private ObjectDescribeServiceImpl objectDescribeService;

    @PostMapping("/getRootPathEquity")
    public Object getEquity(@RequestBody JSONObject param) throws MetadataServiceException {
        String enterpriseId = Objects.requireNonNull(param.getString("enterpriseId"));
        List<Map<String, Object>> maps = equityESService.queryPathEnterprise(enterpriseId);
        return maps;
    }

    @PostMapping("/getParentEnterpriseEquity")
    public EquityResult queryParentEnterprise(@RequestBody JSONObject param) throws MetadataServiceException {
        String enterpriseId = Objects.requireNonNull(param.getString("enterpriseId"));
        String tenantId = Objects.requireNonNull(param.getString("tenantId"));
        Describe describe = objectDescribeService.getDescribe(tenantId, "EquityRelationshipObj", new ActionContext());
        List<ObjectDataDocument> dataList = equityESService.queryParentEnterprise(enterpriseId, describe);
        return EquityResult.builder().errorCode(0).message("success").documents(dataList).build();
    }
}
