package com.facishare.crm.recycling.task.web;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.recycling.task.executor.enums.forecast.ForecastRuleEnums;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastExplanation;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastObjectRecord;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastPeriod;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastRule;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastRuleRecord;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastTask;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastTaskRecord;
import com.facishare.crm.recycling.task.executor.service.ForecastService;
import com.facishare.crm.recycling.task.executor.service.impl.forecast.calculate.ForecastModelCalculator;
import com.facishare.crm.recycling.task.executor.service.impl.forecast.mq.ForecastMessage;
import com.facishare.crm.recycling.task.model.ForecastRestApiModel;
import com.facishare.crm.recycling.task.model.GlobalResult;
import com.facishare.crm.sfa.audit.log.context.SFALogContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("/api/rest/forecast")
@Slf4j
public class ForecastController {

    @Autowired
    private ForecastService forecastService;
    @Autowired
    private ServiceFacade serviceFacade;

    @PostMapping("/task/init")
    public Object initTaskByRule(@RequestBody JSONObject param) throws MetadataServiceException {
        String tenantId = Objects.requireNonNull(param.getString("tenantId"));
        String ruleId = Objects.requireNonNull(param.getString("ruleId"));
        String forecastObjectSearchSource = param.getString("forecastObjectSearchSource");
        IObjectData objectData = serviceFacade.findObjectData(User.systemUser(tenantId), ruleId, ForecastRule.FORECAST_RULE_OBJECT_DESCRIBE_API_NAME);
        if (objectData == null) {
            return GlobalResult.builder().success(false).message("找不到规则");
        }
        ForecastRule rule = new ForecastRuleRecord(objectData);
        if (forecastObjectSearchSource != null) {
            rule.setForecastObjectSearchSource(forecastObjectSearchSource);
        }
        forecastService.initTaskByRule(rule);
        return GlobalResult.builder().success(true).build();
    }

    @PostMapping("/task/recalculate")
    public Object recalculateTask(@RequestBody JSONObject param) {
        ForecastTask task = new ForecastTaskRecord(param.getJSONObject("task"));
        String name = param.getString("calculator");
        ForecastModelCalculator calculator = ForecastModelCalculator.Register.nameOf(name);
        Objects.requireNonNull(calculator, "Calculator [" + name + "] is not registered");
        try {
            forecastService.recalculateTask(task, calculator);
        } catch (MetadataServiceException e) {
            log.error("Recalculate Task error [param:" + param + "]", e);
            return GlobalResult.builder().success(false).message(e.getMessage()).errorCode(e.getErrCode()).build();
        }
        return GlobalResult.builder().success(true).build();
    }

    @PostMapping("/task/detail/add")
    public Object addTaskDetail(@RequestHeader(value = "X-fs-Enterprise-Id") String tenantId, @RequestHeader(value = "X-fs-Employee-Id") String owner, @RequestBody JSONObject param) {
        User user = User.builder().tenantId(tenantId).userId(owner).build();
        String forecastTaskObjectId = param.getString("forecastTaskObjectId");
        List<String> forecastObjectIds = param.getJSONArray("forecastObjectIds").toJavaList(String.class);
        IObjectData objectData = serviceFacade.findObjectData(user, forecastTaskObjectId, ForecastTask.FORECAST_TASK_OBJECT_DESCRIBE_API_NAME);
        if (objectData == null) {
            return ForecastRestApiModel.Result.builder().errCode(500).errorMsg("未找到预测任务").build();
        }
        ForecastTaskRecord task = new ForecastTaskRecord(objectData);
        try {
            forecastService.addTaskDetailManually(task, forecastObjectIds);
        } catch (MetadataServiceException e) {
            return ForecastRestApiModel.Result.builder().errCode(e.getErrCode()).errorMsg(e.getMessage()).build();
        }
        return ForecastRestApiModel.Result.builder().build();
    }

    @PostMapping("/task/detail/remove")
    public Object removeTaskDetail(@RequestHeader(value = "X-fs-Enterprise-Id") String tenantId, @RequestHeader(value = "X-fs-Employee-Id") String owner, @RequestBody JSONObject param) {
        User user = User.builder().tenantId(tenantId).userId(owner).build();
        String forecastTaskObjectId = param.getString("forecastTaskObjectId");
        List<String> forecastTaskDetailIds = param.getJSONArray("forecastTaskDetailIds").toJavaList(String.class);
        IObjectData objectData = serviceFacade.findObjectData(user, forecastTaskObjectId, ForecastTask.FORECAST_TASK_OBJECT_DESCRIBE_API_NAME);
        if (objectData == null) {
            return ForecastRestApiModel.Result.builder().errorMsg("未找到预测任务").build();
        }
        ForecastTaskRecord task = new ForecastTaskRecord(objectData);
        try {
            forecastService.removeTaskDetail(task, forecastTaskDetailIds);
        } catch (MetadataServiceException e) {
            return ForecastRestApiModel.Result.builder().errCode(e.getErrCode()).errorMsg(e.getMessage()).build();
        }
        return ForecastRestApiModel.Result.builder().build();
    }

    @PostMapping("/period/get")
    public Object getForecastPeriod(@RequestHeader(value = "X-fs-Enterprise-Id") String tenantId, @RequestHeader(value = "X-fs-Employee-Id") String owner, @RequestBody JSONObject param) {
        String ruleId = param.getString("forecastRuleObjectId");
        User user = User.builder().tenantId(tenantId).userId(owner).build();
        IObjectData objectData = serviceFacade.findObjectData(user, ruleId, ForecastRule.FORECAST_RULE_OBJECT_DESCRIBE_API_NAME);
        if (objectData == null) {
            return ForecastRestApiModel.Result.builder().errCode(500).errorMsg("未找到预测规则").build();
        }
        ForecastRuleRecord rule = new ForecastRuleRecord(objectData);
        List<ForecastPeriod> forecastPeriods = forecastService.getForecastPeriod(rule);
        ForecastRuleEnums.DateSplitType dateSplitType = ForecastRuleEnums.DateSplitType.valueOf(rule.getForecastDateSplitType());
        List<JSONObject> resultData = new ArrayList<>(forecastPeriods.size());
        for (ForecastPeriod period : forecastPeriods) {
            JSONObject object = new JSONObject();
            object.put("group", dateSplitType == ForecastRuleEnums.DateSplitType.BY_MONTH ? period.toMonthGroupString() : period.toQuarterGroupString());
            object.put("period", period);
            object.put("dateSplitType", dateSplitType.getValue());
            resultData.add(object);
        }
        ForecastRestApiModel.Result result = ForecastRestApiModel.Result.builder().build();
        result.setData(resultData);
        return result;
    }

    @PostMapping("/object/sync")
    public Object syncObject(@RequestBody ForecastMessage.ObjectSync message) {
        try {
            forecastService.syncForecastObject(message);
            SFALogContext.clearContext();
        } catch (MetadataServiceException e) {
            return ForecastRestApiModel.Result.builder().errCode(e.getErrCode()).errorMsg(e.getMessage()).build();
        }
        return ForecastRestApiModel.Result.builder().build();
    }

    @PostMapping("/task/delete")
    public Object deleteTask(@RequestHeader(value = "X-fs-Enterprise-Id") String tenantId, @RequestHeader(value = "X-fs-Employee-Id") String owner, @RequestBody JSONObject param) {
        User user = User.builder().tenantId(tenantId).userId(owner).build();
        String ruleId = param.getString("forecastRuleObjectId");
        IObjectData objectData = serviceFacade.findObjectData(user, ruleId, ForecastRule.FORECAST_RULE_OBJECT_DESCRIBE_API_NAME);
        forecastService.deleteTaskByRule(new ForecastRuleRecord(objectData));
        return ForecastRestApiModel.Result.builder().build();
    }

    @PostMapping("/explain")
    public Object explain(@RequestHeader(value = "X-fs-Enterprise-Id") String tenantId, @RequestBody JSONObject param) throws MetadataServiceException {
        IObjectData objectData = serviceFacade.findObjectData(User.systemUser(tenantId), param.getString(DBRecord.ID), param.getString(IObjectData.DESCRIBE_API_NAME));
        List<ForecastExplanation> explanations = forecastService.explain(new ForecastObjectRecord(objectData));
        return ForecastRestApiModel.Result.builder().data(explanations).build();
    }

    @PostMapping("/explain/briefly")
    public Object explainBriefly(@RequestHeader(value = "X-fs-Enterprise-Id") String tenantId, @RequestBody JSONObject param) throws MetadataServiceException {
        IObjectData objectData = serviceFacade.findObjectData(User.systemUser(tenantId), param.getString(DBRecord.ID), param.getString(IObjectData.DESCRIBE_API_NAME));
        List<ForecastExplanation> explanations = forecastService.explain(new ForecastObjectRecord(objectData));
        JSONArray data = new JSONArray();
        for (ForecastExplanation explanation : explanations) {
            JSONObject item = new JSONObject();
            item.put("rule_id", explanation.getRuleId());
            item.put("rule_name", explanation.getRuleName());
            item.put("brief_explanation", explanation.brief());
            data.add(item);
        }
        return ForecastRestApiModel.Result.builder().data(data).build();
    }
}