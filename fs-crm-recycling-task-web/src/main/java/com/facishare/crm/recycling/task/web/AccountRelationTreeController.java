package com.facishare.crm.recycling.task.web;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.recycling.task.model.GlobalResult;
import com.facishare.crm.sfa.lto.accountreerelation.IEquityRelationDataService;
import com.facishare.crm.sfa.lto.accountreerelation.models.AccountTreeRelationModels;
import com.facishare.crm.sfa.lto.utils.AccountTreeRelationUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

@RestController
@RequestMapping("/api/rest/account_relation_tree")
@Slf4j
public class AccountRelationTreeController {
    @Autowired
    private ServiceFacade serviceFacade;

    @Autowired
    private IEquityRelationDataService equityRelationDataService;

    @PostMapping("/test")
    public Object testGenerateAccountRelationTree(@RequestBody JSONObject param) throws MetadataServiceException {
        String tenantId = Objects.requireNonNull(param.getString("tenantId"));
        String name = Objects.requireNonNull(param.getString("name"));
        return GlobalResult.builder().success(true).build();
    }
}