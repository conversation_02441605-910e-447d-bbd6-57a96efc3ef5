package com.facishare.crm.recycling.task.web;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.follow.service.ObjectDataChangeConsume;
import com.facishare.crm.recycling.task.executor.biz.CustomerBiz;
import com.facishare.crm.recycling.task.executor.common.RecalculateFactory;
import com.facishare.crm.recycling.task.executor.common.RecyclingFactory;
import com.facishare.crm.recycling.task.executor.model.JobResult;
import com.facishare.crm.recycling.task.executor.model.RecalculateMessage;
import com.facishare.crm.recycling.task.executor.model.RecyclingMessage;
import com.facishare.crm.recycling.task.executor.service.impl.AccountRecalculate;
import com.facishare.crm.sfa.lto.accountreerelation.AccountTreeRelationServiceImpl;
import com.facishare.crm.sfa.lto.accountreerelation.CommonTreeRelationServiceImpl;
import com.facishare.crm.sfa.lto.accountreerelation.EnterpriseInfoMatchService;
import com.facishare.crm.sfa.lto.accountreerelation.models.AccountTreeRelationModels;
import com.facishare.crm.sfa.lto.activity.mongo.ActivityMongoDao;
import com.facishare.crm.sfa.lto.activity.mongo.ActivityMongoParamDao;
import com.facishare.crm.sfa.lto.business.BusinessCirclesPlugService;
import com.facishare.crm.sfa.lto.business.models.PlugInitCreateModels;
import com.facishare.crm.sfa.lto.equityrelationship.dao.EquityRelationshipDataMongoDbDao;
import com.facishare.crm.sfa.lto.equityrelationship.model.EquityRelationshipDataModel;
import com.facishare.crm.sfa.lto.equityrelationship.service.EquityRelationshipDataSyncService;
import com.facishare.crm.sfa.lto.equityrelationship.service.EquityRelationshipService;
import com.facishare.crm.sfa.lto.maincontrolstrategy.MainControlStrategySerivce;
import com.facishare.crm.sfa.lto.maincontrolstrategy.models.MainControlStrategyModels;
import com.facishare.crm.sfa.lto.objectpool.IObjectPoolService;
import com.facishare.crm.sfa.lto.objectpool.ObjectPoolFactory;
import com.facishare.crm.sfa.lto.objectpool.models.ObjectPoolActionModels;
import com.facishare.crm.sfa.lto.relationship.models.RelationshipModels;
import com.facishare.crm.sfa.lto.relationship.service.RelationshiService;
import com.facishare.crm.sfa.lto.rfm.RFMService;
import com.facishare.crm.sfa.lto.utils.CommonTreeRelationUtil;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@Slf4j
public class RecyclingController {

    @Resource
    private RecyclingFactory recyclingFactory;

    @Autowired
    private RecalculateFactory recalculateFactory;
    @Autowired
    private RFMService rfmService;

    @Autowired
    private RelationshiService relationshiService;
    @Autowired
    private BusinessCirclesPlugService businessCirclesPlugService;
    @Autowired
    private MainControlStrategySerivce mainControlStrategySerivce;
    @Autowired
    private EquityRelationshipService equityRelationshipService;
    @Autowired
    private EquityRelationshipDataSyncService equityRelationshipDataSyncService;
    @Autowired
    private EquityRelationshipDataMongoDbDao equityRelationshipDataMongoDbDao;
    @Autowired
    private AccountTreeRelationServiceImpl accountTreeRelationService;
    @Autowired
    private CommonTreeRelationServiceImpl commonTreeRelationService;
    @Autowired
    private ServiceFacade serviceFacade;

    @Autowired
    private CustomerBiz customerBiz;

    @Autowired
    private AccountRecalculate accountRecalculate;
    @Autowired
    private EnterpriseInfoMatchService enterpriseInfoMatchService;
    @Autowired
    private ObjectDataChangeConsume objectDataChangeConsume;

    @Autowired
    protected ObjectPoolFactory objectPoolFactory;

    @Autowired
    protected ActivityMongoDao activityMongoDao;


    /**
     * 重算到期时间rest接口
     *
     * @param body
     * @return
     */
    @RequestMapping(value = "/recalculate", method = RequestMethod.POST)
    @ResponseBody
    public JobResult recalculate(@RequestBody String body) {
        boolean success = false;
        try {
            log.info("===== start recalculate ====");
            RecalculateMessage message = JSON.parseObject(body, RecalculateMessage.class);
            recalculateFactory.getRecalculateService(message.getObjectApiName()).execute(message);
            success = true;
        } catch (Exception e) {
            log.error("recalculate is error,body={}", body, e);
        }
        return JobResult.builder()
                .success(success)
                .remove(true)
                .build();
    }


    /**
     * 回收rest接口
     *
     * @param body
     * @return
     */
    @RequestMapping(value = "/recycling", method = RequestMethod.POST)
    @ResponseBody
    public JobResult recycling(@RequestBody String body) {
        boolean success = false;
        try {
            log.info("===== start recycling ====");
            RecyclingMessage message = JSON.parseObject(body, RecyclingMessage.class);
            recyclingFactory.getRecyclingService(message.getObjectApiName()).execute(message);
            success = true;
        } catch (Exception e) {
            log.error("recycling is error,body={}", body, e);
        }
        return JobResult.builder()
                .success(success)
                .remove(true)
                .build();
    }


    @RequestMapping(value = "/getRemainingDays", method = RequestMethod.POST)
    @ResponseBody
    public JobResult getRemainingDays(@RequestBody String body){
        boolean success = false;
        try {
            log.info("===== start getRemainingDays ====");
            Integer ruleDays = 7;
            RecalculateMessage message = JSON.parseObject(body, RecalculateMessage.class);
            
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date date = sdf.parse("2025-01-11");
            IObjectData objectData = new ObjectData();
            objectData.setTenantId(message.getTenantId());
            objectData.setId(message.getObjectId());
            objectData.setDescribeApiName("AccountObj");
            objectData.setOwner(Lists.newArrayList("1001"));

            accountRecalculate.getRemainingDays(date, ruleDays, objectData);
            success = true;
        } catch (Exception e) {
            log.error("getRemainingDays is error,body={}", body, e);
        }
        return JobResult.builder()
                .success(success)
                .remove(true)
                .build();
    }
    

/**
     * 回收rest接口
     *
     * @param body
     * @return
     */
    @RequestMapping(value = "/rfm", method = RequestMethod.POST)
    @ResponseBody
    public JobResult rfm(@RequestBody String body) {
        boolean success = false;
        try {
            log.info("===== start recycling ====");
            RecyclingMessage message = JSON.parseObject(body, RecyclingMessage.class);
            if(ObjectUtils.isEmpty(message.getObjectId())){
                rfmService.processRFMRule(new User(message.getTenantId(), User.SUPPER_ADMIN_USER_ID),
                        message.getObjectApiName());
            }else{
                rfmService.processRFMRule(new User(message.getTenantId(), User.SUPPER_ADMIN_USER_ID),
                        message.getObjectApiName(),message.getObjectId());
            }

            success = true;
        } catch (Exception e) {
            log.error("recycling is error,body={}", body, e);
        }
        return JobResult.builder()
                .success(success)
                .remove(true)
                .build();
    }
    /**
     * 回收rest接口
     *
     * @param body
     * @return
     */
    @RequestMapping(value = "/rfmCreateTask", method = RequestMethod.POST)
    @ResponseBody
    public JobResult rfmCreateTask(@RequestBody String body) {
        boolean success = false;
        try {
            RelationshipModels.TaskArg arg = JSON.parseObject(body, RelationshipModels.TaskArg.class);
            //rfmService.createRFMCreateCalculateTask(arg.getTenantId());
            success = true;
        } catch (Exception e) {
            log.error("recycling is error,body={}", body, e);
        }
        return JobResult.builder()
                .success(success)
                .remove(true)
                .build();
    }


    /**
     * 回收rest接口
     *
     * @param body
     * @return
     */
    @RequestMapping(value = "/relationshi", method = RequestMethod.POST)
    @ResponseBody
    public JobResult relationshi(@RequestBody String body) {
        boolean success = false;
        try {
            log.info("===== start relationshi ====");
            RelationshipModels.TaskArg arg = JSON.parseObject(body, RelationshipModels.TaskArg.class);
//            if(!relationshiService.getConfigValueByKey(arg.getTenantId())){
//                return JobResult.builder()
//                        .success(success)
//                        .remove(true)
//                        .build();
//            }
            relationshiService.dealHistoryDataMsg(arg);
            //relationshiService.dealDataRelationshipByDataId(arg);
            success = true;
        } catch (Exception e) {
            log.error("relationshi is error,body={},e:{}", body, e);
            System.out.println(e);
        }
        return JobResult.builder()
                .success(success)
                .remove(true)
                .build();
    }

    /**
     * 人脉关系雷达删除数据通过企业id
     *
     * @param body
     * @return
     */
    @RequestMapping(value = "/relationshiDeleteByTenantId", method = RequestMethod.POST)
    @ResponseBody
    public JobResult relationshiDeleteByTenantId(@RequestBody String body) {
        boolean success = false;
        try {
            log.info("===== start relationshiDeleteByTenantId ====");
            RelationshipModels.TaskArg arg = JSON.parseObject(body, RelationshipModels.TaskArg.class);
            if(!relationshiService.getConfigValueByKey(arg.getTenantId())){
                return JobResult.builder()
                        .success(success)
                        .remove(true)
                        .build();
            }
            relationshiService.deleteDataByTenantId(arg.getTenantId(),arg.getDataIds().get(0),arg.getObjectApiName());
            success = true;
        } catch (Exception e) {
            log.error("relationshiDeleteByTenantId is error,body={},e:{}", body, e);
            System.out.println(e);
        }
        return JobResult.builder()
                .success(success)
                .remove(true)
                .build();
    }

    /**
     * 人脉关系雷达删除数据通过企业id
     *
     * @param body
     * @return
     */
    @RequestMapping(value = "/plugInitServer", method = RequestMethod.POST)
    @ResponseBody
    public JobResult plugInitServer(@RequestBody String body) {
        boolean success = false;
        try {
            log.info("===== start relationshiDeleteByTenantId ====");
            PlugInitCreateModels.TaskArg arg = JSON.parseObject(body, PlugInitCreateModels.TaskArg.class);

            ServiceContext serviceContext = new ServiceContext(RequestContext.builder().tenantId(arg.getTenantId())
                    .user(new User(arg.getTenantId(), "-10000")).build(), null, null);
            businessCirclesPlugService.execute(arg);
            success = true;
        } catch (Exception e) {
            log.error("relationshiDeleteByTenantId is error,body={},e:{}", body, e);
            System.out.println(e);
        }
        return JobResult.builder()
                .success(success)
                .remove(true)
                .build();
    }
    @RequestMapping(value = "/mainControlStrategySerivce", method = RequestMethod.POST)
    @ResponseBody
    public JobResult mainControlStrategySerivce(@RequestBody String body) {
        boolean success = false;
        try {
            log.info("===== start mainControlStrategySerivce ====");
            MainControlStrategyModels.TaskArg arg = JSON.parseObject(body, MainControlStrategyModels.TaskArg.class);
            mainControlStrategySerivce.execute(arg);
            success = true;
        } catch (Exception e) {
            log.error("mainControlStrategySerivce is error,body={},e:{}", body, e);
            System.out.println(e);
        }
        return JobResult.builder()
                .success(success)
                .remove(true)
                .build();
    }

    @RequestMapping(value = "/EquityRelationship", method = RequestMethod.POST)
    @ResponseBody
    public JobResult EquityRelationship(@RequestBody String body) {
        boolean success = false;
        //equityRelationshipService.queryBranchInfoByName(new User("79337", "-10000"),"小米科技有限责任公司");
        EquityRelationshipDataModel.DataSyncArg arg = new EquityRelationshipDataModel.DataSyncArg();
        EquityRelationshipDataModel.Keys keys =  new EquityRelationshipDataModel.Keys();
        keys.setTenant_id("79337");
        keys.setId("dewdewdc3232dcewfe232");
        arg.setKeys(keys);
        //equityRelationshipDataSyncService.execute(arg);
       // equityRelationshipDataMongoDbDao.queryListByParamPaging("-100",null);
        //equityRelationshipDataSyncService.RealTimeUpdatePG(Lists.newArrayList("64ae8cc2181b0214cf5806fe","64ae8cc2181b0214cf58070e","64ae8cc2181b0214cf580714","64ae8cc2181b0214cf5806fd","64ae8cc2181b0214cf58070d"));
       // EquityRelationshipDataModel.CompanyDetail companyDetail =equityRelationshipService.queryCompanyDetailByName(new User("79337", "-10000"),"中国银联股份有限公司");
        long time = 1694671633423L;
        //equityRelationshipService.hget();
//        ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
//        task.submit(() -> equityRelationshipDataSyncService.dataSyncPgExecute("78060"));
//        task.run();
      // log.error("companyDetail:{}", JSONObject.toJSONString(companyDetail));
        Map<String,Object> tempParamMap = new HashMap<>();
        EquityRelationshipDataModel.CompanyDetail keys1 = JSON.parseObject(body, EquityRelationshipDataModel.CompanyDetail.class);
//        tempParamMap.put(EquityRelationshipDataConstants.Param.DATA_NAME,"北京纷扬科技有限责任公司");
//        tempParamMap.put(EquityRelationshipDataConstants.Param.DATA_ID,"64c8c298278dd00001d658e4");
       // equityRelationshipDataSyncService.realTimeUpdatePGByTime("84895",time,tempParamMap);
       // equityRelationshipService.webGetByProxy("https://hipapig.cmft.com/gateway/cmei/v1/group/getFamilyTree?name=连云港纷享互联网科技有限责任公司");
//        equityRelationshipService.getIndustryInterfaceByBI(new User(keys1.getTenant_id(), "-10000"),keys1.getId());
//        equityRelationshipService.getCompanyDetailByBI(new User(keys1.getTenant_id(), "-10000"),keys1.getId());
        //equityRelationshipDataSyncService.getObjectDataOfEnterpriseInfoObj(new User("79337", "-10000"),"2232323232",companyDetail);
        tempParamMap = JSON.parseObject(body, Map.class);
//        equityRelationshipService.saveEnterpriseObj(new User(tempParamMap.get("tenant_id").toString(), "-10000"),tempParamMap);

//        equityRelationshipService.getObjectDataOfEnterpriseInfoObj(new User(tempParamMap.get("tenant_id").toString(), "-10000"),"xxswq",keys1);
        EquityRelationshipDataModel.RelationshipCreateMsg msg = JSON.parseObject(body, EquityRelationshipDataModel.RelationshipCreateMsg.class);
        equityRelationshipService.handleEquityRelationship(msg);
        return JobResult.builder()
                .success(success)
                .remove(true)
                .build();
    }
    @RequestMapping(value = "/getRelationshipsMapping", method = RequestMethod.POST)
    @ResponseBody
    public JobResult getRelationshipsMapping(@RequestBody String body) {
        boolean success = false;
//        AccountTreeRelationModels.RelationshipsMapping relationshipsMapping = CommonTreeRelationUtil.getRelationshipsMapping("AccountObj");
//        AccountTreeRelationModels.RelationshipsMapping relationshipsMapping1 = CommonTreeRelationUtil.getMappingInfoByLinkDescribeApiName("AccountTreeRelationObj");
        AccountTreeRelationModels.CreateAccountTreeRelationArg arg = JSON.parseObject(body, AccountTreeRelationModels.CreateAccountTreeRelationArg.class);
        EquityRelationshipDataModel.Keys keys1 = JSON.parseObject(body, EquityRelationshipDataModel.Keys.class);
//        enterpriseInfoMatchService.getMatchRule(new User(keys1.getTenant_id(), "-10000"));
//        accountTreeRelationService.createAccountTreeRelationAsync(new User(keys1.getTenant_id(), "-10000"),arg);
        commonTreeRelationService.createTreeRelationAsync(new User(keys1.getTenant_id(), "-10000"),arg);
        return JobResult.builder()
                .success(success)
                .remove(true)
                .build();
    }
    @RequestMapping(value = "/afterAccountMainDataDeleted", method = RequestMethod.POST)
    @ResponseBody
    public JobResult afterAccountMainDataDeleted(@RequestBody String body) {
        boolean success = false;
        EquityRelationshipDataModel.Keys keys1 = JSON.parseObject(body, EquityRelationshipDataModel.Keys.class);
        CommonTreeRelationUtil.afterAccountMainDataDeleted(new User(keys1.getTenant_id(), "-10000"),Lists.newArrayList(keys1.getId()),serviceFacade,"AccountObj");
        return JobResult.builder()
                .success(success)
                .remove(true)
                .build();
    }
    @RequestMapping(value = "/getDept", method = RequestMethod.POST)
    @ResponseBody
    public JobResult getDept(@RequestBody String body) {
        boolean success = false;
        EquityRelationshipDataModel.Keys keys1 = JSON.parseObject(body, EquityRelationshipDataModel.Keys.class);
        RecalculateMessage event = JSON.parseObject(body, RecalculateMessage.class);
        //customerBiz.getDept(keys1.getTenant_id(), keys1.getId());
        accountRecalculate.execute(event);

        return JobResult.builder()
                .success(success)
                .remove(true)
                .build();
    }
    @RequestMapping(value = "/calculateFollowAndDealStatus", method = RequestMethod.POST)
    @ResponseBody
    public JobResult calculateFollowAndDealStatus(@RequestBody String body) {
        boolean success = false;
        List<MessageExt> messageExtList = new ArrayList<>();
        MessageExt messageExt = new MessageExt();
        try {
            messageExt.setBody(body.getBytes("UTF-8"));
            messageExt.putUserProperty("x-fs-ei","91564");
            messageExt.putUserProperty("describe_api_name","SaleContractObj");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
        messageExtList.add(messageExt);
        objectDataChangeConsume.consumeMessage(messageExtList);

        return JobResult.builder()
                .success(success)
                .remove(true)
                .build();
    }

    @RequestMapping(value = "/deleteAllPoolData", method = RequestMethod.POST)
    @ResponseBody
    public JobResult BaseObjectPoolService(@RequestBody String body) {
        boolean success = false;
        ObjectPoolActionModels.ReplacePoolIdMsg msg2 = JSON.parseObject(body, ObjectPoolActionModels.ReplacePoolIdMsg.class);
        log.info("EquityRelationshipCreateListener sfa-pool-replace-poolId msg:{}",msg2);
        IObjectPoolService objectPoolService = objectPoolFactory.getObjectPoolService(msg2.getObjectApiName());
        objectPoolService.replacePoolId(msg2);
        return JobResult.builder()
                .success(success)
                .remove(true)
                .build();
    }
    @RequestMapping(value = "/updateContent", method = RequestMethod.POST)
    @ResponseBody
    public JobResult updateContent(@RequestBody String body) {
        boolean success = false;
        ActivityMongoParamDao.RecordingReplaceParam msg2 = JSON.parseObject(body, ActivityMongoParamDao.RecordingReplaceParam.class);
        log.info("EquityRelationshipCreateListener sfa-pool-replace-poolId msg:{}",msg2);
       // activityMongoDao.updateContent(msg2.getUserName(),msg2.getObjectId(),msg2);
        activityMongoDao.queryFullContentWithSpeaker(msg2.getUserName(),msg2.getObjectId());
        return JobResult.builder()
                .success(success)
                .remove(true)
                .build();
    }



    @RequestMapping(value = "/refreshDataIsDeleted", method = RequestMethod.POST)
    @ResponseBody
    public JobResult refreshDataIsDeleted(@RequestBody String body) {
        boolean success = false;
        String[] tenantIds = body.split(";");
        for (String tenantId : tenantIds) {
            activityMongoDao.refreshDataIsDeleted(tenantId);
        }
        return JobResult.builder()
                .success(success)
                .remove(true)
                .build();
    }
}
