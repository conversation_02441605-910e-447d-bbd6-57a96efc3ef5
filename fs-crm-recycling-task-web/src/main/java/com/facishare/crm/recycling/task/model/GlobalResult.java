package com.facishare.crm.recycling.task.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-04-15 10:53
 */

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GlobalResult {
    private boolean success ;
    private String message;
    private Map< String, Object > value;
    private Integer errorCode;
}
