package com.facishare.crm.recycling.task.web;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.industry.IndustryRecommendationService;
import com.facishare.crm.industry.model.Recommendation;
import com.facishare.crm.sfa.lto.industry.CompanyModel;
import com.facishare.crm.sfa.lto.rest.SFAIndustryInterfaceProxy;
import com.facishare.crm.sfa.lto.rest.models.IndustryCompanyAdvance;
import com.facishare.crm.sfa.lto.utils.HttpHeaderUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/3/26 21:22
 * @description:
 */
@Controller
@Slf4j
@RequestMapping(value = "/industry", method = RequestMethod.POST)
public class AIIndustryController {


    @Autowired
    private IndustryRecommendationService IndustryRecommendationService;

    @Autowired
    SFAIndustryInterfaceProxy sfaIndustryInterfaceProxy;

    @RequestMapping(value = "/companyRecommendation", method = RequestMethod.POST)
    @ResponseBody
    public Recommendation.Result companyRecommendation(@RequestBody String body) {
        Recommendation.Arg arg = JSON.parseObject(body, Recommendation.Arg.class);
        List<IndustryCompanyAdvance.IndustryCompany> companyList = IndustryRecommendationService.getRecommendation(arg.getTenantId(), arg.getMessage());
        return Recommendation.Result.builder().errorCode("0").message("success").data(companyList).build();
    }

    /**
     * 测试方法
     */
    @RequestMapping(value = "/getCompanyDetailByName", method = RequestMethod.POST)
    @ResponseBody
    public CompanyModel.CompanyDetailResult getCompanyDetailByName(@RequestBody String body) {
        CompanyModel.CompanyDetailArg arg = JSON.parseObject(body, CompanyModel.CompanyDetailArg.class);
        CompanyModel.CompanyDetailResult detailByName = sfaIndustryInterfaceProxy.getCompanyDetailByName(HttpHeaderUtil.getHeaders("90473"), arg);
        log.info("getCompanyDetailByName result:{}", JSON.toJSONString(detailByName));
        return null;
    }
}
