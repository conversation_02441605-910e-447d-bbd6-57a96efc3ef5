package com.facishare.crm.recycling.task.model;

import com.facishare.paas.service.model.ObjectDataDocument;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-04-15 10:53
 */

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EquityResult {
    private boolean success ;
    private String message;
    private List<ObjectDataDocument> documents;
    private Integer errorCode;
}
