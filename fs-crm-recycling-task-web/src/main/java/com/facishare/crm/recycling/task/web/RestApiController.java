package com.facishare.crm.recycling.task.web;

import com.facishare.crm.recycling.task.executor.biz.CustomerBiz;
import com.facishare.crm.recycling.task.executor.biz.RecyclingBiz;
import com.facishare.crm.recycling.task.executor.biz.RecyclingRemoteBiz;
import com.facishare.crm.recycling.task.executor.enums.ApiNameEnum;
import com.facishare.crm.recycling.task.executor.model.PoolAllocateRuleMemberCalculateModel;
import com.facishare.crm.recycling.task.executor.model.RecyclingRuleInfoModel;
import com.facishare.crm.recycling.task.executor.model.WorkDaysModel;
import com.facishare.crm.recycling.task.executor.service.PoolAllocateRuleMemberCalculateService;
import com.facishare.crm.recycling.task.executor.service.impl.AccountRecalculate;
import com.facishare.crm.recycling.task.model.GlobalResult;
import com.facishare.crm.recycling.task.model.MapRecyclingRuleArg;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

import static com.facishare.crm.recycling.task.executor.util.ConstantUtils.*;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-04-15 10:54
 */

@Slf4j
@RestController
@RequestMapping(value = "/api/rest", method = RequestMethod.POST)
public class RestApiController {


    @Autowired
    private CustomerBiz customerBiz;

    @Autowired
    private RecyclingRemoteBiz recyclingRemoteBiz;

    @Autowired
    private RecyclingBiz recyclingBiz;

    @Autowired
    private AccountRecalculate accountRecalculate;

    @Autowired
    private PoolAllocateRuleMemberCalculateService poolAllocateRuleMemberCalculateService;
    @Autowired
    private ServiceFacade serviceFacade;


    private static final ArrayList<String> noRemindDaysEI = Lists.newArrayList("253937", "625684", "633969", "662658", "662836", "659576", "78005", "653008", "662767", "650848", "666171", "666271", "663784", "614302", "496004", "574187", "607154", "625880", "590008", "617592", "522663", "498685", "629415", "94721", "576141", "264943", "636656", "580856", "470621", "438513", "467146", "654245", "397043", "83703", "627526", "239555", "533452", "381627", "258459", "615147", "642165", "625215", "67494", "238266", "570922", "626252", "635311", "416003", "434439", "557311", "155535", "263921", "57515", "655940", "649888", "576415", "576141", "634873", "581070", "623489", "569762", "581021", "625016", "607699", "100444", "582360", "99516", "606615", "519454", "76830", "57256", "644966", "57654", "582330", "80282", "603575", "486266", "631274", "604841", "593461", "548489", "654245", "7089", "75170", "88159", "99576", "100204", "236845", "261918", "297193", "365025", "425781", "452039", "464244", "479442", "489054", "499438", "501903", "508682", "519610", "540265", "545361", "549114", "549653", "559775", "570967", "571465", "578953", "583834", "585375", "592325", "607801", "622836", "624498", "627104", "637677", "645753", "648526", "649649", "663784", "662977", "662978", "662979", "662980", "662981", "662982", "662983", "662984", "662985", "662986", "662987", "662988", "662989", "662990", "662991", "662992", "662993", "662994", "662995", "662996", "662997", "662998", "662999", "663000", "663001", "663002", "663003", "663004", "663005", "663006", "663007", "663008", "663009", "663010", "663011", "663012", "663013", "663014", "663015", "663016", "663017", "663018", "663019", "663020", "663021", "663022", "663023", "663024", "663025", "663026", "47988", "58683", "83520", "92737", "114433", "142346", "155785", "244048", "258550", "277674", "336706", "386228", "399204", "402884", "414981", "424949", "439561", "452276", "458227", "472713", "478917", "491055", "533049", "538649", "545711", "551055", "556269", "558400", "562839", "565159", "570841", "573985", "574026", "574414", "575210", "582603", "585186", "587294", "589753", "598839", "605577", "607984", "608532", "610154", "623269", "623474", "623594", "625076", "626502", "627891", "629818", "632202", "633347", "633776", "635139", "638129", "642690", "642845", "644135", "647381", "648788", "650282", "654486", "654992", "655151", "657332", "658438", "661750", "662495", "662757", "663800", "664041", "666651", "667658");


    @RequestMapping("/calculateAllocateRuleMemberWheres")
    public GlobalResult calculateAllocateRuleMemberWheres(@RequestBody PoolAllocateRuleMemberCalculateModel.Arg arg) {
        poolAllocateRuleMemberCalculateService.calculateMemberWheres(arg);
        return GlobalResult.builder().success(true).build();
    }


    @RequestMapping("/mapRecyclingRule")
    public GlobalResult mapRecyclingRule(@RequestHeader(value = "X-fs-Enterprise-Id") String tenantId, @RequestBody MapRecyclingRuleArg arg) {

        GlobalResult globalResult = GlobalResult.builder().build();
        globalResult.setMessage("");
        globalResult.setErrorCode(0);
        globalResult.setSuccess(false);
        globalResult.setValue(Maps.newHashMap());
        if (!ApiNameEnum.ACCOUNT_OBJ.getApiName().equals(arg.getApiName()) && !ApiNameEnum.LEADS_OBJ.getApiName().equals(arg.getApiName())) {
            globalResult.setMessage("apiName " + arg.getApiName() + " 不支持");
            return globalResult;
        }
        if (CollectionUtils.isEmpty(arg.getObjectIds())) {
            globalResult.setMessage("objectIds 为空 ");
            return globalResult;
        }
        if (!noRemindDaysEI.contains(tenantId)) {
            globalResult.setMessage("");
            return globalResult;
        }

        List<IObjectData> objectDatas = customerBiz.getObjectBySearchQuery(tenantId, arg.getObjectIds(), arg.getApiName());
        if (CollectionUtils.isEmpty(objectDatas)) {
            globalResult.setMessage("数据不存在");
            return globalResult;
        }

        Map<String, Object> value = new HashMap<>(arg.getObjectIds().size() * 2);
        List<RecyclingRuleInfoModel> recyclingRules;
        Map<String, String> ownerDept = new HashMap<>();
        Map<String, List<RecyclingRuleInfoModel>> recyclingRuleMap = new HashMap<>();
        if (ApiNameEnum.ACCOUNT_OBJ.getApiName().equals(arg.getApiName())) {
            for (IObjectData objectData : objectDatas) {
                if (objectData.get(EXPIRE_TIME) == null || StringUtils.isBlank(objectData.get(EXPIRE_TIME).toString())) {
                    continue;
                }
                if (System.currentTimeMillis() - Long.valueOf(objectData.get(EXPIRE_TIME).toString()) > 0) {
                    log.info("expire_time  < 0  {},{}", objectData.getId(), tenantId);
                    sendRecalculateMQ(tenantId, objectData.getId(), arg.getApiName());
                    continue;
                }
                try {
                    String mappingRuleId;
                    // 非公海客户
                    if (objectData.get(HIGH_SEAS_ID) == null || StringUtils.isBlank(objectData.get(HIGH_SEAS_ID).toString())) {
                        if (CollectionUtils.isEmpty(objectData.getOwner())) {
                            log.warn("非公海客户负责人为空:{},tenantId;{}", objectData.getId(), tenantId);
                            value.put(objectData.getId(), null);
                            continue;
                        }
                        String ownerId = StringUtils.strip(objectData.get(OWNER).toString(), "[]");
                        if (recyclingRuleMap.get(ownerId) == null) {
                            recyclingRules = recyclingBiz.getRecyclingRule(objectData, ApiNameEnum.HIGH_SEAS_OBJ.getApiName());
                            recyclingRuleMap.put(ownerId, recyclingRules);
                        } else {
                            recyclingRules = recyclingRuleMap.get(ownerId);
                        }
                        mappingRuleId = accountRecalculate.getMappingRuleId(recyclingRules, objectData, arg.getApiName());
                    } else {
                        String highSeasId = objectData.get(HIGH_SEAS_ID).toString();
                        if (recyclingRuleMap.get(highSeasId) == null) {
                            recyclingRules = recyclingBiz.getRecyclingRule(tenantId, highSeasId, ApiNameEnum.HIGH_SEAS_OBJ.getApiName());
                            recyclingRuleMap.put(highSeasId, recyclingRules);
                        } else {
                            recyclingRules = recyclingRuleMap.get(highSeasId);
                        }
                        mappingRuleId = accountRecalculate.getMappingRuleId(recyclingRules, objectData, arg.getApiName());
                    }
                    globalResult.setSuccess(true);
                    value.put(objectData.getId(), mappingRuleId);
                } catch (Exception e) {
                    log.warn("rest error:{}", e);
                }
            }
        } else if (ApiNameEnum.LEADS_OBJ.getApiName().equals(arg.getApiName())) {
            for (IObjectData objectData : objectDatas) {
                if (objectData.get(LEADS_POOL_ID) != null) {
                    recyclingRules = recyclingBiz.getRecyclingRule(tenantId, objectData.get(LEADS_POOL_ID).toString(), ApiNameEnum.LEADS_POOL_OBJ.getApiName());
                    String mappingRuleId = accountRecalculate.getMappingRuleId(recyclingRules, objectData, arg.getApiName());
                    value.put(objectData.getId(), mappingRuleId);
                } else {
                    value.put(objectData.getId(), null);
                    continue;
                }

            }
        }
        globalResult.setSuccess(true);
        globalResult.setValue(value);
        return globalResult;
    }


    @RequestMapping("/getWorkDays")
    public WorkDaysModel.Result calculateAllocateRuleMemberWheres(@RequestBody WorkDaysModel.Arg arg) {
        return accountRecalculate.getUserAttendanceDetails(arg);
    }

    private void sendRecalculateMQ(String tenantId, String objectId, String apiName) {
        log.info("sendRecalculateMQ tenantId:{},{},{}", tenantId, objectId, apiName);
        accountRecalculate.sendRecalculate(tenantId, objectId, apiName, new Date());
    }

    @RequestMapping("/testPartnerRecyclingRule")
    public Boolean testPartnerRecyclingRule() {
        // User user = new User("71568", "-10000");
        // String highSeasId = "62319ca849ab6a0001d8bcd2";
        // String accountId = "634625e81109dd0001f51085";
        // IObjectData highSeasData = serviceFacade.findObjectData(user, highSeasId, "HighSeasObj");
        // IObjectData accountData = serviceFacade.findObjectData(user, accountId, "AccountObj");
        // customerBiz.objectOutTeamHandle("71568", highSeasData, Lists.newArrayList(accountData));
        return Boolean.TRUE;
    }


}
