package com.facishare.crm.biz;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.BaseTest;
import com.facishare.crm.recycling.task.executor.biz.CustomerBiz;
import com.facishare.crm.recycling.task.executor.biz.RecyclingBiz;
import com.facishare.crm.recycling.task.executor.enums.ApiNameEnum;
import com.facishare.crm.recycling.task.executor.enums.BizStatusEnum;
import com.facishare.crm.recycling.task.executor.enums.LifeStatusEnum;
import com.facishare.crm.recycling.task.executor.model.RecyclingMessage;
import com.facishare.crm.recycling.task.executor.model.RecyclingRule;
import com.facishare.crm.recycling.task.executor.service.impl.LeadsRecycling;
import com.facishare.crm.recycling.task.executor.util.DateUtils;
import com.facishare.crm.recycling.task.executor.util.SearchUtil;
import com.facishare.paas.appframework.core.exception.APPException;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.fxiaoke.paas.gnomon.api.NomonProducer;
import com.fxiaoke.paas.gnomon.api.entity.NomonMessage;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.facishare.crm.recycling.task.executor.util.ConstantUtils.*;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-03-01 11:28
 */
@Slf4j
public class RecyclingBizTest extends BaseTest {

    @Autowired
    private RecyclingBiz recyclingBiz;

    @Autowired
    private MetaDataFindService metaDataFindService;

    @Autowired
    private CustomerBiz customerBiz;

    @Autowired
    private NomonProducer nomonProducer;

    @Autowired
    private ObjectDataServiceImpl objectDataPgService;

    @Autowired
    private LeadsRecycling leadsRecycling;

    private static final String EXPIRE_TIME = "957680773000";

    @Test
    public void getRecyclingRuleTest() {

        //"6fc7745f7cbf4e078dc011dc0f9106f8"
        List<Map> recyclingRule = recyclingBiz.getRecyclingRule("2", "6fc7745f7cbf4e078dc011dc0f9106f8",
                "2", ApiNameEnum.HIGH_SEAS_OBJ.getApiName());

        log.info(recyclingRule.toString());
    }

    @Test
    public void getListRecyclingRuleTest() {

        //"6fc7745f7cbf4e078dc011dc0f9106f8"
        List<RecyclingRule> listRecyclingRule = recyclingBiz.getListRecyclingRule("74203", "8115fba9a45a4e05a7b90180ca215fb5",
                "1", ApiNameEnum.HIGH_SEAS_OBJ.getApiName());

        log.info(listRecyclingRule.toString());
    }



    @Test
    public void getRemindRule() {
        List<IObjectData> remindRules = recyclingBiz.getRemindRule("2", "1149", "2");
    }

    public List<IObjectData> getCustomerLimitByOwner(String tenantId, String owenrId, Integer limit, Integer offset) {
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(limit);
        searchQuery.setOffset(offset);
        List filters = Lists.newLinkedList();
        SearchUtil.fillFilterEq(filters, BIZ_STATUS, BizStatusEnum.ALLOCATED.getValue());
        SearchUtil.fillFilterIn(filters, LIFE_STATUS, Lists.newArrayList(LifeStatusEnum.NORMAL.getValue(),LifeStatusEnum.IN_CHANGE.getValue()));
        SearchUtil.fillFilterEq(filters, OWNER, owenrId);

        Filter filter = new Filter();
        filter.setOperator(Operator.IS);
        filter.setFieldValues(null);
        filter.setFieldName(EXPIRE_TIME);
        filters.add(filter);

        searchQuery.setFilters(filters);

        List<OrderBy> orders =  new ArrayList<>();
        orders.add(new OrderBy(CREATE_TIME,Boolean.TRUE));
        searchQuery.setOrders(orders);

        QueryResult<IObjectData> queryResult;
        try {
            queryResult = metaDataFindService.findBySearchQuery(buildUser(tenantId), ApiNameEnum.ACCOUNT_OBJ.getApiName(), searchQuery);
        } catch (Exception e) {
            log.error(" getCustomerLimitByOwner throw exception {},tenantId:{},", e.getMessage(), tenantId,e);
            throw new RuntimeException();
        }
        return queryResult == null ? Lists.newArrayList() : queryResult.getData();
    }


    /**
     * 测试数据 被回收后是否插入表 biz_data_claim_log 中
     */
    @Test
    public void addPoolClaimLogTest() {
        List<IObjectData> customerLimitByOwner = getCustomerLimitByOwner(TENANT_ID, OWNER, 5, 1);

        List<String> updateFieldList = new ArrayList<>();
        for (IObjectData iObjectData : customerLimitByOwner) {
            iObjectData.set(EXPIRE_TIME, DateUtils.beforeDays(System.currentTimeMillis(),20));
            iObjectData.set(REMIND_DAYS, 0);
        }
        updateFieldList.add(EXPIRE_TIME);
        updateFieldList.add(REMIND_DAYS);
        customerBiz.updateField(customerLimitByOwner, updateFieldList, ApiNameEnum.ACCOUNT_OBJ.getApiName(), "74203");
        for (IObjectData iObjectData : customerLimitByOwner) {
            send(TENANT_ID, iObjectData.getId(),iObjectData.getDescribeApiName(),new Date());
        }
        try {
            Thread.sleep(1000 * 60 * 3);
        } catch (InterruptedException e) {

        }
        List<String> dataIds = customerLimitByOwner.stream().map(DBRecord::getId).collect(Collectors.toList());

        List<Map> maps;
        String sql = "select id from biz_data_claim_log "
                + String.format(" where tenant_id = '%s' ", TENANT_ID)
                + String.format(" and api_name = 'AccountObj' and is_deleted = 0 ")
                + String.format(" and object_id in ('%s')", String.join("','", dataIds));
        try {
            log.info("select biz_data_claim_log sql :{}",sql);
            maps = objectDataPgService.findBySql(TENANT_ID, sql);
        } catch (MetadataServiceException e) {
            throw new APPException("filterStopedUsers 查询元数据异常", e);
        }
        Assert.assertEquals(5,maps.size());
    }

    public User buildUser(String tenantId) {
        return new User(tenantId, "-10000", "", "");
    }

    public void send(String tenantId, String objectId, String apiName, Date executeTime) {
        RecyclingMessage recyclingMessage = new RecyclingMessage();
        recyclingMessage.setTenantId(tenantId);
        recyclingMessage.setObjectId(objectId);
        recyclingMessage.setObjectApiName(apiName);

        NomonMessage nomonMessage = NomonMessage.builder()
                .biz("crm_object_recycling_task_recycling")
                .tenantId(tenantId)
                .dataId(objectId)
                .executeTime(executeTime)
                .callArg(JSONObject.toJSON(recyclingMessage).toString())
                .build();
        nomonProducer.send(nomonMessage);
        log.info("send nomonMessage:{}", nomonMessage);
    }

    @Test
    public void executeFunctionTest(){
        String tenantId = "78557";
        String id = "5f55ea355f3baa0001aa1101";
        IObjectData leadsObj = null;
        String functionApiName = "func_xfE12__c";

        IActionContext context = ActionContextExt.of(new User(tenantId, "-10000"), RequestContextManager.getContext())
                .allowUpdateInvalid(true)
                .getContext();

        try {
            leadsObj = objectDataPgService.findById(id, tenantId, context,"LeadsObj");
        } catch (MetadataServiceException e) {

        }
        leadsRecycling.executeFunction(tenantId,"LeadsObj",functionApiName,leadsObj);
    }

}
