package com.facishare.crm.biz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.BaseTest;
import com.facishare.crm.recycling.task.executor.biz.CommonBiz;
import com.facishare.crm.sfa.lto.common.LtoOrgCommonService;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-03-06 15:53
 */

public class CommonBizTest extends BaseTest {
    @Autowired
    private CommonBiz commonBiz;

    @Autowired
    private LtoOrgCommonService ltoOrgCommonService;

    @Autowired
    private OrgService orgService;

    @Test
    public void getDeptInfoTest() {
        ltoOrgCommonService.getMembersByDeptIds("74203", Lists.newArrayList("1001"), true);
        ltoOrgCommonService.getMainDepartment("74203", "1001");
        ltoOrgCommonService.getDepartmentByUserId("74203",  "1940");
        ltoOrgCommonService.getAllSuperDepartmentByIds("74203",  Lists.newArrayList("1059","1009","1013","1014","1015"));
        ltoOrgCommonService.getAllSubDepartmentByIds("74203",  Lists.newArrayList("1059","1009","1013","1014","1015"));

        return;
    }

    @Test
    public void getUserGroupTest() {
        ltoOrgCommonService.getMembersByUserGroupIds("74203", Lists.newArrayList("5d342b78319d19b4a4ce30da"), true);
        ltoOrgCommonService.getUserGroupIdsByMemberId("74203", "1000");
        ltoOrgCommonService.getUserGroupByIds("74203",  Lists.newArrayList("5d342b78319d19b4a4ce30da"));
        ltoOrgCommonService.batchGetMembersByUserGroupIds("78696",  Lists.newArrayList("5d342b78319d19b4a4ce30da"), true);

        return;
    }

    @Test
    public void getUserRuleTest() {
        ltoOrgCommonService.batchGetRoleUsersByRoleIds("78696",  Lists.newArrayList("5d8c67cfe4b062c52b712e99"), true);
        ltoOrgCommonService.batchGetRoleUsersByRoleIds("74203",  Lists.newArrayList("5d8c67cfe4b062c52b712e99"), true);
        ltoOrgCommonService.getUserRoleIdsByUserId("74203",  "1000");
        ltoOrgCommonService.getDepartmentByUserId("74203",  "1940");

        return;
    }

    @Test
    public void getDataBySearchQueryTest() {
        String tenantId = "78535";
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setPermissionType(0);
        String wheresString = "[{\"filters\": [{\"isIndex\": false, \"fieldNum\": 0, \"operator\": \"EQ\", \"connector\": \"AND\", \"field_name\": \"name\", \"field_values\": \"熊-78535\", \"isObjectReferencea\": false}], \"connector\": \"OR\"}]";
        List<Wheres> wheresList = Lists.newArrayList();
        convert2WheresList(wheresString, wheresList);
        searchTemplateQuery.setWheres(wheresList);
        commonBiz.queryBySearchTemplate("78535", "AccountObj", searchTemplateQuery);
//        commonBiz.queryBySearchTemplate("74203", "PartnerObj", searchTemplateQuery);

        int offset = 0;
        int pageSize = 100;
        searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setWheres(wheresList);
        searchTemplateQuery.setLimit(pageSize);
        searchTemplateQuery.setOffset(offset);
        searchTemplateQuery.setOrders(Lists.newArrayList(new OrderBy("id", true)));
        String objectApiName = "PersonnelObj";
        List<String> dataIds = Lists.newArrayList();
        int maxCount = 1000;
        while (maxCount >= 0) {
            --maxCount;
            QueryResult<IObjectData> rstResult = commonBiz.queryBySearchTemplate(tenantId, objectApiName, searchTemplateQuery);
            if(rstResult.getData() == null) {
                break;
            }

            if(rstResult == null || CollectionUtils.empty(rstResult.getData())) {
                break;
            }
            dataIds.addAll(rstResult.getData().stream().map(x -> x.get("user_id").toString()).collect(Collectors.toSet()));
            offset += pageSize;
            if(rstResult.getTotalNumber() <= offset) {
                break;
            }
            searchTemplateQuery.setOffset(offset);
        }

        return;
    }

    @Test
    public void getPartnerTest() {
        ltoOrgCommonService.getPartnerOutUserIds("74203", Lists.newArrayList("5d9dc4ec1e36850001dff4af","5d5b5e5da5083d2b60ae44fe","5d08bc33a5083d207cab66dd"));
        ltoOrgCommonService.getPartnerOuterTenant("74203", Lists.newArrayList("5d9dc4ec1e36850001dff4af","5d5b5e5da5083d2b60ae44fe"));
        ltoOrgCommonService.getPartnerOutUserIds("74203", Lists.newArrayList("5d9dc4ec1e36850001dff4af","5d5b5e5da5083d2b60ae44fe","5d08bc33a5083d207cab66dd"));
        ltoOrgCommonService.batchGetOuterTenantsByUserIds ("74203",  Lists.newArrayList(100018591L));

        return;
    }

    private void convert2WheresList(String wheresString, List<Wheres> wheresList) {
        if(StringUtils.isEmpty(wheresString)) {
            return;
        }
        List<JSONObject> wheresJSONObjectList = JSON.parseObject(wheresString, List.class);
        if(CollectionUtils.notEmpty(wheresJSONObjectList)){
            for(JSONObject jsonObject : wheresJSONObjectList){
                Wheres wheres = JSON.parseObject(jsonObject.toJSONString(), Wheres.class);
                wheresList.add(wheres);
            }
        }
    }


    @Test
    public void testOrgService(){
        //1153
        List<String> membersByDeptIds = ltoOrgCommonService.getMembersByDeptIds("71554", Lists.newArrayList("1016"), false);
        System.out.println(membersByDeptIds.toString());
    }

}
