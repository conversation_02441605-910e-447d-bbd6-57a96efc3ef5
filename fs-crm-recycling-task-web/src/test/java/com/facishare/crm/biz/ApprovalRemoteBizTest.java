package com.facishare.crm.biz;

import com.facishare.crm.BaseTest;
import com.facishare.crm.recycling.task.executor.biz.ApprovalRemoteBiz;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-03-09 14:55
 */

public class ApprovalRemoteBizTest extends BaseTest {

    @Autowired
    private ApprovalRemoteBiz approvalRemoteBiz;

    @Test
    public void getCurInstanceStateByObjectIdsTest() {
        approvalRemoteBiz.getCurInstanceStateByObjectIds("71567", "d07108a755064c44b9e8112e20135e07", "-10000");
    }


}
