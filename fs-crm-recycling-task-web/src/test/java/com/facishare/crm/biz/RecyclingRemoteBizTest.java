package com.facishare.crm.biz;

import com.facishare.crm.BaseTest;
import com.facishare.crm.recycling.task.executor.biz.RecyclingRemoteBiz;
import com.facishare.crm.recycling.task.executor.model.RecyclingRule;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-03-05 19:19
 */

@Slf4j
public class RecyclingRemoteBizTest extends BaseTest {

    @Autowired
    private RecyclingRemoteBiz recyclingRemoteBiz;


    @Test
    public void testGetRecyclingRuleByDataid() {
//        List<RecyclingRule> recyclingRules = recyclingRemoteBiz.getRecyclingRuleByDataid("6fc7745f7cbf4e078dc011dc0f9106f8");
//        List<RecyclingRule> recyclingRules2 = recyclingRemoteBiz.getRecyclingRuleByDataid("2", "1000", "HighSeasObj");
        List<RecyclingRule> recyclingRules2 = recyclingRemoteBiz.getRecyclingRuleByDataid("74203", "bf30f2c60c674d12aed8ac1e0da8f06c", "HighSeasObj");
        for (RecyclingRule recyclingRule : recyclingRules2) {
            System.out.println("=========");
            System.out.println(recyclingRule.toString());
        }
    }

}
