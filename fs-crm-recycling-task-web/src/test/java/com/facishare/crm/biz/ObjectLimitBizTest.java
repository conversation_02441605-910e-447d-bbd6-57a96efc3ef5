package com.facishare.crm.biz;

import com.facishare.crm.BaseTest;
import com.facishare.crm.recycling.task.executor.model.ObjectLimitRuleCalculateModel;
import com.facishare.crm.sfa.lto.objectlimit.ObjectLimitRuleService;
import com.facishare.crm.sfa.lto.objectlimit.models.ObjectLimitRuleModel;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Optional;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-03-06 15:53
 */

public class ObjectLimitBizTest extends BaseTest {
    @Autowired
    private ObjectLimitRuleService objectLimitBiz;

    @Test
    public void getObjectLimitRuleTest() {
        String tenantId = "74203";
        String objectApiName = "LeadsObj";
        String groupId = "5db10833e507d1000131a342";
        String employeeId = "1000";

        List<ObjectLimitRuleModel.ObjectLimitRule> objectLimitRuleList = objectLimitBiz.getObjectLimitRuleByGroupId(tenantId, objectApiName, groupId);
        List<ObjectLimitRuleModel.ObjectLimitRule> objectLimitRuleByDataIdList = objectLimitBiz.getObjectLimitRuleByDataIds(tenantId, Lists.newArrayList(employeeId), ObjectLimitRuleModel.DataTypeEnum.EMPLOYEE.getCode());
        List<ObjectLimitRuleModel.ObjectLimitRule> objectLimitRuleByDataTypeList = objectLimitBiz.getObjectLimitRuleByDataType(tenantId, ObjectLimitRuleModel.DataTypeEnum.EMPLOYEE.getCode());

        Assert.assertNotNull(objectLimitRuleList);
        Assert.assertNotNull(objectLimitRuleByDataIdList);
        Assert.assertNotNull(objectLimitRuleByDataTypeList);

        Optional<ObjectLimitRuleModel.ObjectLimitRule> groupLimitOptional = objectLimitRuleList.stream()
                .filter(x -> employeeId.equals(x.getDataId())&& objectApiName.equals(x.getObjectApiName())
                        && groupId.equals(x.getGroupId()) && ObjectLimitRuleModel.DataTypeEnum.EMPLOYEE.getCode().equals(x.getDataType())).findFirst();

        Optional<ObjectLimitRuleModel.ObjectLimitRule> employeeLimitOptional = objectLimitRuleByDataIdList.stream()
                .filter(x -> employeeId.equals(x.getDataId()) && objectApiName.equals(x.getObjectApiName())
                        && groupId.equals(x.getGroupId()) && ObjectLimitRuleModel.DataTypeEnum.EMPLOYEE.getCode().equals(x.getDataType())).findFirst();

        Optional<ObjectLimitRuleModel.ObjectLimitRule> dataTypeLimitOptional = objectLimitRuleByDataTypeList.stream()
                .filter(x -> employeeId.equals(x.getDataId()) && objectApiName.equals(x.getObjectApiName())
                        && groupId.equals(x.getGroupId()) && ObjectLimitRuleModel.DataTypeEnum.EMPLOYEE.getCode().equals(x.getDataType())).findFirst();

        Assert.assertTrue(groupLimitOptional.isPresent());
        Assert.assertTrue(employeeLimitOptional.isPresent());
        Assert.assertTrue(dataTypeLimitOptional.isPresent());

        return;
    }

    @Test
    public void getObjectLimitRuleUserIdsTest() {
        String tenantId = "74203";
        String objectApiName = "LeadsObj";
        String groupId = "5db10833e507d1000131a342";
        String employeeId = "1000";

        List<ObjectLimitRuleModel.ObjectLimitRule> objectLimitRuleList = objectLimitBiz.getObjectLimitRuleByGroupId(tenantId, objectApiName, groupId);

        Assert.assertNotNull(objectLimitRuleList);

        List<String> ruleUserIdList = objectLimitBiz.getObjectLimitRuleUserIds(tenantId, objectLimitRuleList);

        Assert.assertTrue(ruleUserIdList.contains(employeeId));

        return;
    }
}
