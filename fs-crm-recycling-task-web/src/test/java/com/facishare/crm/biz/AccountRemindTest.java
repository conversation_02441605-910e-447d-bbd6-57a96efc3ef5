package com.facishare.crm.biz;

import com.facishare.crm.BaseTest;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.recycling.task.executor.biz.CustomerBiz;
import com.facishare.crm.recycling.task.executor.biz.RemindBiz;
import com.facishare.crm.recycling.task.executor.enums.ApiNameEnum;
import com.facishare.crm.recycling.task.executor.enums.RemindRecordTypeEnum;
import com.facishare.crm.recycling.task.executor.model.RemindMessage;
import com.facishare.crm.recycling.task.executor.service.impl.AccountRemindService;
import com.facishare.paas.appframework.common.service.CRMNotificationServiceImpl;
import com.facishare.paas.appframework.common.service.SendCrmMessageProxy;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static com.facishare.crm.recycling.task.executor.util.I18NKey.SFA_LEADS_RECOVERD_NEW_RECORD;
import static com.facishare.crm.recycling.task.executor.util.I18NKey.SFA_LEADS_RECYClING_REASON;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-03-19 21:00
 */

public class AccountRemindTest extends BaseTest {


    @Autowired
    private SendCrmMessageProxy sendCrmMessageProxy;

    @Autowired
    private AccountRemindService accountRemindService;

    @Autowired
    private CRMNotificationServiceImpl crmNotificationService;

    @Autowired
    private RemindBiz remindBiz;


    @Autowired
    private CustomerBiz customerBiz;


    @Test
    public void execute() {
        RemindMessage remindMessage = new RemindMessage();
        remindMessage.setObjectId("");
        remindMessage.setTenantId("2");
        remindMessage.setObjectApiName(ApiNameEnum.ACCOUNT_OBJ.getApiName());
        accountRemindService.execute(remindMessage);
    }


    @Test
    public void sendRemindTest(){

        String tenantId = "79337";
        String accountId = "5cb45f26266e7300012554d0";
        String content = "是的发生 被收回到公海：工商注册";
        String dataId = "5f1178ca01e4770001d88d14";

        //CRMNotification crmNotification = CRMNotification.builder()
        //        .sender(User.SUPPER_ADMIN_USER_ID)
        //        .remindRecordType(6)
        //        .content(content)
        //        .dataId("5f0a9cd4b7042b000103be51")
        //        .receiverIds(Sets.newHashSet(1000))
        //        //.content2Id("5f0a9cd4b7042b000103be51")
        //        .fixContent2ID(String.format("{\"dataId\":\"%s\",\"type\":\"%s\"}","5f0a9cd4b7042b000103be51", ACCOUNT_OBJ))
        //        .objectApiName(ACCOUNT_OBJ)
        //        .build();
        //
        //    crmNotification.setTitle("");

        //CRMNotification crmNotification = CRMNotification.builder().sender(User.SUPPER_ADMIN_USER_ID)
        //        .remindRecordType(6)
        //        .title("")
        //        .content("测试 给您分配来一条线索 xx,xxx")
        //        .dataId(dataId)
        //        //.fixContent2ID(String.format("{\"dataId\":\"%s\",\"type\":\"%s\"}",dataId, LEADS_OBJ))
        //        .objectApiName(LEADS_OBJ)
        //        .receiverIds(Sets.newHashSet(1000))
        //        .build();

        //CRMNotification crmNotification = CRMNotification.builder().sender(User.SUPPER_ADMIN_USER_ID)
        //        .remindRecordType(6)
        //        .content("客户被收回到公海--------闪退？")
        //        .dataId("5f052fb8c037bf0001434e69")
        //        .receiverIds(Sets.newHashSet(1000))
        //        .fixContent2ID(String.format("{\"dataId\":\"%s\",\"type\":\"%s\"}","5f052fb8c037bf0001434e69", "AccountObj"))
        //        .objectApiName(ACCOUNT_OBJ)
        //        .title(null)
        //        .build();

        //CRMNotification crmNotification = CRMNotification.builder().sender(User.SUPPER_ADMIN_USER_ID)
        //        .remindRecordType(121)
        //        .content("线索被收回到线索池---")
        //        .dataId("5f30fcdb5076af00011691db")
        //        .receiverIds(Sets.newHashSet(1000))
        //        .fixContent2ID(String.format("{\"dataId\":\"%s\",\"type\":\"%s\"}", "5f30fcdb5076af00011691db", "LeadsObj"))
        //        .objectApiName(LEADS_OBJ)
        //        .title("线索已被收回")
        //        .build();


        IObjectData objectData = customerBiz.getObjectById(tenantId, "60e6a4a0e456490001159ed6", Utils.LEADS_API_NAME);

        remindBiz.sendRemind(objectData, tenantId, null,
                RemindRecordTypeEnum.LEADS_RECYCLING_NEW, "线索已被收回", SFA_LEADS_RECOVERD_NEW_RECORD, Lists.newArrayList(),
                SFA_LEADS_RECYClING_REASON, Lists.newArrayList(objectData.getName(), "2天未跟进"));

        //try {
        //    crmNotificationService.sendCRMNotification(ObjectDataUtils.buildUser("78060"), crmNotification);
        //} catch (Exception e) {
        //    System.out.println("----------------");
        //    throw new RuntimeException();
        //}
    }

    @Test
    public void sendRmind(){
        //remindBiz.sendRemind();
    }


}
