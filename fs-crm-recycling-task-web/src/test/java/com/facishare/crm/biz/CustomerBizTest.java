package com.facishare.crm.biz;

import com.facishare.crm.BaseTest;
import com.facishare.crm.recycling.task.executor.biz.BaseBiz;
import com.facishare.crm.recycling.task.executor.biz.CustomerBiz;
import com.facishare.crm.recycling.task.executor.enums.ApiNameEnum;
import com.facishare.crm.recycling.task.executor.enums.BizStatusEnum;
import com.facishare.crm.recycling.task.executor.util.DateUtils;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.TeamMember;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

import static com.facishare.crm.recycling.task.executor.util.ConstantUtils.*;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-03-06 15:53
 */

public class CustomerBizTest extends BaseTest {


    @Autowired
    private CustomerBiz customerBiz;

    @Autowired
    private BaseBiz baseBiz;


    @Test
    public void getDeptInfoTest() {
        customerBiz.getDept("71567", "1001");
    }



    @Test
    public void removeRelevantTeamTest() {
        IObjectData objectData = customerBiz.getObjectById("74203", "5cb850c79ea9b10001226224", ApiNameEnum.ACCOUNT_OBJ.getApiName());
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        List<TeamMember> teamMembers = objectDataExt.getTeamMembers();
        if (CollectionUtils.isNotEmpty(teamMembers)) {
            for (TeamMember teamMember : teamMembers) {
                System.out.println("----------");
                System.out.println(teamMember.getEmployee());
            }
        }
        System.out.println(objectData);
    }


    @Test
    public void getRemindCustomer() {

        Date parse = DateUtils.parse("2019-03-14 00:00:00", "yyyy-MM-dd HH:mm:ss");
        List<IObjectData> dataList = customerBiz.getCustomerLimitByHighSeasId("71567","", 10, 1);
        for (IObjectData objectData : dataList) {
            System.out.printf("======= %s", objectData.toString());
        }
    }

    @Test
    public void getContact() {
        List<IObjectData> contact = customerBiz.getContact("2", "5c88eeb53a175500018479f1");
        for (IObjectData objectData : contact) {
            System.out.printf("====== %s", objectData.toString());
        }
    }

    @Test
    public void removeContactOwner() {
        customerBiz.removeContactOwner("2", Lists.newArrayList("5c8f4a17b3f5330001878e5a"), ApiNameEnum.ACCOUNT_OBJ.getApiName());
    }

    @Test
    public void getCustomerLimitByDeptId() {
        List<String> customerLimitByDeptId = customerBiz.getMembersByDeptId("74203", "1003");
        System.out.println(customerLimitByDeptId);
    }

    @Test
    public void getCustomerLimitByOwner() {
        int limit = 500;
        int offset = 0;
        List<IObjectData> objectDataList;
        do {
            objectDataList = customerBiz.getCustomerLimitByOwner("79743", "1001", limit, offset);
            offset = offset + objectDataList.size();
        }while (CollectionUtils.isNotEmpty(objectDataList));
        System.out.println("====================");
        System.out.println(offset);
        Assert.assertTrue(true);
    }

    @Test
    public void getObjectBySearchQuery() {
        List<IObjectData> objectBySearchQuery = customerBiz.getObjectBySearchQuery("74203", Lists.newArrayList("5d0a1d18a5083d5d682e3c8e", "5cdcd83ea5083d62e688e78b"), ApiNameEnum.ACCOUNT_OBJ.getApiName());
        System.out.println(objectBySearchQuery);
    }


    @Test
    public void updateFields() {
        List<String> updateFieldList = Lists.newArrayList();
        IObjectData data = new ObjectData();

        data.setTenantId("74203");
        data.setDescribeApiName(ApiNameEnum.ACCOUNT_OBJ.getApiName());
        data.setId("5cd12e06b76c0b0001dc688c");

        data.setOwner(null);
        updateFieldList.add(OWNER);

        data.set(BIZ_STATUS, BizStatusEnum.UNALLOCATED.getValue());
        updateFieldList.add(BIZ_STATUS);

        //到期时间清空
        data.set(EXPIRE_TIME, "");
        updateFieldList.add(EXPIRE_TIME);

        data.set(HIGH_SEAS_ID, "b1effed95e894992bf25f86f95bac59c");
        updateFieldList.add(HIGH_SEAS_ID);

        //领取/退回时间清空
        data.set(CLAIMED_TIME, "");
        updateFieldList.add(CLAIMED_TIME);

        // 回收时间 设置为系统时间
        data.set(RETURNED_TIME, System.currentTimeMillis());
        updateFieldList.add(RETURNED_TIME);

        customerBiz.updateField(Lists.newArrayList(data), updateFieldList, ApiNameEnum.ACCOUNT_OBJ.getApiName(), "74203");

    }

    @Test
    public void getById() {
        IObjectData objectData = customerBiz.getObjectById("74203", "5cb850c79ea9b10001226224", ApiNameEnum.ACCOUNT_OBJ.getApiName());
        if (objectData.get(LAST_FOLLOWED_TIME,Long.class) > 1556195034596L){
            System.out.println("大于------------");
        }
        System.out.println(objectData);

    }

    @Test
    public void test() {
        customerBiz.updateExpireTime(ApiNameEnum.ACCOUNT_OBJ.getApiName(),"74203",  "5d15f052a5083dfbb3d8eab6",System.currentTimeMillis() + "",  2);
    }


}
