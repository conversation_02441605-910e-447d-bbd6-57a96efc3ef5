package com.facishare.crm;

import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.support.GenericApplicationContext;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-02-20 15:27
 */

public class ProfileApplicationContextInitializer implements ApplicationContextInitializer<GenericApplicationContext> {
    @Override
    public void initialize(GenericApplicationContext context) {
//        context.getEnvironment().getSystemProperties().put("process.profile","ceshi113");
        context.getEnvironment().getSystemProperties().put("process.profile","fstest");
    }
}
