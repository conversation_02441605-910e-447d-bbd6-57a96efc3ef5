package com.facishare.crm.service;

import com.facishare.appserver.checkinsoffice.api.model.GetUserAttendanceInfos;
import com.facishare.appserver.checkinsoffice.api.service.CheckinsOfficeV2Service;
import com.facishare.crm.BaseTest;
import com.google.common.collect.Sets;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/3/1 15:05
 */

public class CheckinsOfficeV2ServiceTest extends BaseTest {

    @Autowired
    private CheckinsOfficeV2Service checkinsOfficeV2Service;


    @Test
    public void testGetCheckinsOfficeV2() {
        String tenantId = "78612";
        GetUserAttendanceInfos.Args args = new GetUserAttendanceInfos.Args();
        args.setStartDateStr("2022-03-01");
        args.setEndDateStr("2022-03-10");
        args.setUserIds(Sets.newHashSet(1000));

        GetUserAttendanceInfos.Result userAttendanceInfos = checkinsOfficeV2Service.getUserAttendanceInfos(tenantId, args);

        System.out.println(userAttendanceInfos);
    }


}
