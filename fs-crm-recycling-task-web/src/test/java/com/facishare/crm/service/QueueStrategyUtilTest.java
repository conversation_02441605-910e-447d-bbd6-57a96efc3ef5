package com.facishare.crm.service;

import com.facishare.crm.recycling.task.executor.util.QueueStrategyUtil;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020/4/9 16:44
 */

public class QueueStrategyUtilTest {
    public static void main(String[] args) {
        for (int i = 0; i < 1000; i++) {
            QueueStrategyUtil.count("1");
            // 是否触发限速
            boolean slow = QueueStrategyUtil.toSlow("1", 5, 5);
            if (slow){
                System.out.println("触发了限速");
            }else {
                System.out.println("没有触发限速");
            }
            System.out.printf("i:%s \n",i);
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }
}
