package com.facishare.crm.service;

import com.facishare.crm.BaseTest;
import com.facishare.crm.recycling.task.executor.biz.CommonBiz;
import com.facishare.crm.sfa.expression.SFAExpressionServiceImpl;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019/11/25 00:32
 */

public class SFAExpressionServiceTest extends BaseTest {

    @Autowired
    @Qualifier("sfaExpressionService")
    private SFAExpressionServiceImpl expressionService;

    //@Autowired
    //private ExpressionService expressionService;

    @Autowired
    private CommonBiz commonBiz;

    @Test
    public void execute(){

        String tenantId = "78060";
        String objectId = "5eb66ed57b130d000185f132";
        String objectApiName = "AccountObj";
        String groupId = "5db10833e507d1000131a342";
        String employeeId = "1000";
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setPermissionType(0);
        //{\"filters\\":[{\"field_name\":\"created_by\",\"field_values\":\"1000\",\"operator\":\"EQ\",\"operator_name\":\"等于\",\"connector\":\"AND\"}],\"connector\":\"OR\"}
        //String wheresString = "[{\"filters\": [{\"isIndex\": false, \"fieldNum\": 0, \"operator\": \"EQ\", \"connector\": \"AND\", \"field_name\": \"name\", \"field_values\": \"熊-78535\", \"isObjectReferencea\": false}], \"connector\": \"OR\"}]";
        //String wheresString = "{\"filters\": [{\"field_name\":\"created_by\",\"field_values\":\"1000\",\"operator\":\"EQ\",\"operator_name\":\"等于\",\"connector\":\"AND\"}," +
        //        "{\"field_name\":\"data_own_department\",\"field_values\":\"1000\",\"operator\":\"EQ\",\"operator_name\":\"等于\",\"connector\":\"AND\"}], \"connector\": \"OR\"}";
        //String wheresString = "{\"filters\": [{\"field_name\":\"field_H2Z1b__c\",\"field_values\":[\"1\",\"2\"],\"operator\":\"IN\",\"operator_name\":\"属于\",\"connector\":\"AND\"}], \"connector\": \"OR\"}";

        String wheresString = "{\"filters\": [{\"operator\": \"GT\", \"connector\": \"AND\", \"field_name\": \"create_time\", \"field_values\": *************, \"operator_name\": \"晚于\"}, {\"operator\": \"EQ\", \"connector\": \"AND\", \"field_name\": \"deal_status\", \"field_values\": \"1\", \"operator_name\": \"等于\"}], \"connector\": \"OR\"}";
//        List<Wheres> wheresList = Lists.newArrayList();
//        commonConvert2WheresList(wheresString, wheresList);
//        searchTemplateQuery.setWheres(wheresList);
//        commonBiz.queryBySearchTemplate("78535", "AccountObj", searchTemplateQuery);

        List<IObjectData> accountObj = commonBiz.getObjectByIds(tenantId, objectApiName,
                Lists.newArrayList(objectId), false);

        IObjectDescribe objectDescribe = commonBiz.getObjectDescribe(tenantId, objectApiName);
        Boolean evaluate = expressionService.evaluate(wheresString, accountObj.get(0),objectDescribe);
        //Boolean evaluate = expressionService.evaluate(wheresString, accountObj.get(0), objectDescribe);
        System.out.println(evaluate);


    }


    //@Test
    //public void testEvaluate(){
    //    HashMap<String, Object> map = new HashMap<>();
    //    map.put("name","");
    //    String expression = "(!ISNULL(\"name\") && !CONTAINS('1001','name'))";
    //    Boolean evaluate = expressionService.evaluate(expression, map);
    //    System.out.println(evaluate);
    //
    //}




}
