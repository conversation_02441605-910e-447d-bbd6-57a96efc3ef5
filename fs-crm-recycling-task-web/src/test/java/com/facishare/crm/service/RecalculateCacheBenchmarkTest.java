package com.facishare.crm.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.follow.model.RecalculateMessage;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.common.RemotingHelper;

import java.util.concurrent.atomic.AtomicInteger;

import static com.facishare.crm.follow.util.ConstantUtils.FOLLOW;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/12/30 13:10
 */

public class RecalculateCacheBenchmarkTest {
    private static final AtomicInteger counter = new AtomicInteger(1);

    public static void main(String[] args) throws MQClientException, InterruptedException {

        /*
         * Instantiate with a producer group name.
         */
        DefaultMQProducer producer = new DefaultMQProducer("recalculate-cache-benchmark-test");
        producer.setNamesrvAddr("***********:9876;************:9876");
        producer.setVipChannelEnabled(false);
        producer.setSendMsgTimeout(3000);
        producer.setMaxMessageSize(1024 * 1024 * 4);
        producer.setRetryTimesWhenSendFailed(2);
        producer.setRetryTimesWhenSendAsyncFailed(2);


        /*
         * Launch the instance.
         */
        producer.start();

        for (int i = 79337; i < 82337; i++) {
            try {
                RecalculateMessage message = RecalculateMessage.builder()
                        .actionCode(FOLLOW)
                        .objectApiName("LeadsObj")
                        .objectId("")
                        .tenantId(i+"")
                        .build();

                /*
                 * Create a message instance, specifying topic, tag and message body.
                 */
                Message msg = new Message("sfa-recycling-recalculate" /* Topic */,
                        "follow_calculate" /* Tag */,
                        (JSON.toJSONString(message)).getBytes(RemotingHelper.DEFAULT_CHARSET) /* Message body */
                );

                /*
                 * Call send message to deliver message to one of brokers.
                 */
                SendResult sendResult = producer.send(msg);

                System.out.printf("%s%n", sendResult);
            } catch (Exception e) {
                e.printStackTrace();
                Thread.sleep(1000);
            }
        }

        /*
         * Shut down once the producer instance is not longer in use.
         */
        producer.shutdown();
    }
}
