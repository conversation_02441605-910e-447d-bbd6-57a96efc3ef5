package com.facishare.crm.service;

import com.facishare.crm.BaseTest;
import com.facishare.crm.recycling.task.executor.enums.ActionCodeEnum;
import com.facishare.crm.recycling.task.executor.enums.ApiNameEnum;
import com.facishare.crm.recycling.task.executor.model.RecalculateMessage;
import com.facishare.crm.recycling.task.executor.producer.AllocateBatchProducer;
import com.facishare.crm.recycling.task.executor.service.impl.AccountRecalculate;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-02-27 17:54
 */

public class AccountRecalculateTest extends BaseTest {

    @Autowired
    private AccountRecalculate accountRecalculate;

    @Autowired
    private AllocateBatchProducer allocateBatchProducer;


    @Test
    public void testExecuteCustomerAction(){
        RecalculateMessage message2 = RecalculateMessage.builder().
                tenantId("2")
                .objectId("5feee6e170c34050852cf7a4ddeb1150")//客户id
                .actionCode(ActionCodeEnum.ADD.getActionCode())
                .objectApiName(ApiNameEnum.ACCOUNT_OBJ.getApiName())
                .build();
        accountRecalculate.execute(message2);
    }

    @Test
    public void testExecuteChangeRule(){
        RecalculateMessage message = RecalculateMessage.builder().
                tenantId("2")
//                tenantId("71567")
                .objectId("6fc7745f7cbf4e078dc011dc0f9106f8")//公海id 113
//                .objectId("6b4fe662d15747e589a9c7309ab47574")//公海id 112"6b4fe662d15747e589a9c7309ab47574"
                .actionCode(ActionCodeEnum.CHANGE_RULE.getActionCode())
                .objectApiName(ApiNameEnum.ACCOUNT_OBJ.getApiName())
                .build();
        accountRecalculate.execute(message);

    }


    @Test
    public void sendAllocateMQ(){
        allocateBatchProducer.sendAllocateTask("78060","leadsId","ddadfadfpoolId");
    }


}
