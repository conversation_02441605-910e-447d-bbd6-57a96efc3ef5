package com.facishare.crm.service;

import com.facishare.crm.BaseTest;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.recycling.task.executor.biz.CustomerBiz;
import com.facishare.crm.recycling.task.executor.enums.ApiNameEnum;
import com.facishare.crm.recycling.task.executor.model.RecyclingMessage;
import com.facishare.crm.recycling.task.executor.model.ReturnHighSeasActionContent;
import com.facishare.crm.recycling.task.executor.service.SFAOpenApiMqService;
import com.facishare.crm.recycling.task.executor.service.impl.AccountRecycling;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.metadata.MetaDataServiceImpl;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-02-20 12:52
 */

public class AccountRecyclingTest extends BaseTest {


    @Autowired
    private AccountRecycling accountRecycling;


    @Autowired
    private SFAOpenApiMqService sfaOpenApiMqService;

    @Autowired
    private CustomerBiz customerBiz;

    @Autowired
    private MetaDataServiceImpl metaDataService;

    @Test
    public void testExecute(){
        RecyclingMessage message = new RecyclingMessage();
        message.setObjectApiName("AccountObj");
        message.setObjectId("5c88eeb53a175500018479f1");
        message.setTenantId("2");
        accountRecycling.execute(message);

//        RecyclingMessage message = new RecyclingMessage();
//        message.setObjectApiName("AccountObj");
//        message.setObjectId("dd625a8e80004fa3945bcd82a217dd1a");
//        message.setTenantId("71567");
//        message.setTargetId("6b4fe662d15747e589a9c7309ab47574"); //gong_test
//        accountRecycling.execute(message);
    }


    @Test
    public void testSendOpenApiMq() {
//        ReturnHighSeasActionContent content = ReturnHighSeasActionContent.builder().accountIds(Lists.newArrayList("5cbd87f2cfbf8e0001356304"))
//                .customerName("*************")
//                .accountStatus("unallocated").build();
//        sfaOpenApiMqService.sendOpenApiMq(customerBiz.buildUser("79337"), ObjectAction.CHANGE_OWNER.getActionCode(),
//                Utils.LEADS_API_NAME, "60efe23f6c506200018c74e9", new Object());

        String tenantId = "78060";
        String highSeasId = "613ef41f216f330001a29382";
        String leadsPoolId = "614d7a3b8988c0000118389e";
        String objectId = "613ef41f216f330001a29382";

        //IObjectData objectData = customerBiz.getObjectById(tenantId, "5f043c596068500001d6dd83", Utils.ACCOUNT_API_NAME);
        IObjectData objectData = customerBiz.getObjectById(tenantId, "613ef41f216f330001a29382", Utils.LEADS_API_NAME);
        ReturnHighSeasActionContent content = ReturnHighSeasActionContent.builder()
                .objectIds(Lists.newArrayList(objectId))
                //.backReason("自动收回")
                .backReasonDescription("满足3天未跟进")
                //.accountStatus("normal")
                //.poolName("北京线索池")
                .company("google")
                .name("线索名称")
                .customerName(objectData.getName()).build();
        sfaOpenApiMqService.sendOpenApiMq(customerBiz.buildUser(tenantId), ObjectAction.TAKE_BACK.getActionCode(),
                ApiNameEnum.LEADS_POOL_OBJ.getApiName(), leadsPoolId, content);
        metaDataService.sendActionMq(customerBiz.buildUser(tenantId), Lists.newArrayList(objectData), ObjectAction.TAKE_BACK);


        objectData = customerBiz.getObjectById(tenantId, "5f043c596068500001d6dd83", Utils.ACCOUNT_API_NAME);
        ReturnHighSeasActionContent content2 = ReturnHighSeasActionContent.builder()
                .accountIds(Lists.newArrayList(objectData.getId()))
                //.backReason("自动收回")
                .backReasonDescription("满足3天未跟进")
                //.accountStatus("normal")
                //.poolName("北京线索池")
                .name("客户名称")
                .customerName(objectData.getName()).build();
        sfaOpenApiMqService.sendOpenApiMq(customerBiz.buildUser(tenantId), ObjectAction.TAKE_BACK.getActionCode(),
                ApiNameEnum.HIGH_SEAS_OBJ.getApiName(), highSeasId, content2);
        metaDataService.sendActionMq(customerBiz.buildUser(tenantId), Lists.newArrayList(objectData), ObjectAction.TAKE_BACK);


        //sfaOpenApiMqService.sendOpenApiMq(customerBiz.buildUser(tenantId), ObjectAction.TAKE_BACK.getActionCode(),
        //        ApiNameEnum.HIGH_SEAS_OBJ.getApiName(), highSeasId, content);


//        metaDataService.sendActionMq(customerBiz.buildUser("74203"),Lists.newArrayList(objectData), ObjectAction.CHANGE_OWNER);


    }

//    public static List<IObjectData> fillOldData(List<IObjectData> updatedDataList, List<IObjectData> oldDataList) {
//        if (!CollectionUtils.empty(updatedDataList) && !CollectionUtils.empty(oldDataList)) {
//            Map<String, Map> map = (Map)oldDataList.stream().collect(Collectors.toMap(DBRecord::getId, AccountRecycling::of));
//            List<IObjectData> list = Lists.newArrayList();
//            updatedDataList.forEach((a) -> {
//                Map<String, Object> data = Maps.newHashMap(of(a));
//                data.put("old_data", map.get(a.getId()));
//                list.add(ObjectDataExt.of(data).getObjectData());
//            });
//            return list;
//        } else {
//            return updatedDataList;
//        }
//    }
}
