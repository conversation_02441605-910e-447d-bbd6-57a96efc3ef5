package com.facishare.crm.recycling.task.executor.service.impl.forecast.task;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.recycling.task.executor.enums.forecast.ForecastRuleEnums;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastPeriod;
import com.facishare.crm.recycling.task.executor.service.impl.forecast.mq.ForecastMessage;
import org.apache.rocketmq.client.exception.MQBrokerException;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.exception.RemotingException;
import org.junit.Assert;
import org.junit.Test;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

public class ForecastPeriodUtilTestCase {

    @Test
    public void match() {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        long startTimestamp = 1612800000000L; // 2021-02-09 00:00:00
        long endTimestamp = 1654012799000L; // 2022-05-31 23:59:59
        ZoneId zoneId = ZoneId.systemDefault();
        String left0 = dateTimeFormatter.format(ZonedDateTime.ofInstant(Instant.ofEpochMilli(startTimestamp), zoneId));
        String right0 = dateTimeFormatter.format(ZonedDateTime.ofInstant(Instant.ofEpochMilli(endTimestamp), zoneId));
        int monthSpan = ForecastRuleEnums.DateSplitType.BY_QUARTER.getMonthSpan();
        System.out.println("split from " + left0 + " to " + right0 + " by month span " + monthSpan);
        List<ForecastPeriod> periods = ForecastPeriodMatcher.generatePeriod(startTimestamp, endTimestamp, monthSpan);
        for (ForecastPeriod period : periods) {
            String left = dateTimeFormatter.format(ZonedDateTime.ofInstant(Instant.ofEpochMilli(period.getLeft()), zoneId));
            String right = dateTimeFormatter.format(ZonedDateTime.ofInstant(Instant.ofEpochMilli(period.getRight()), zoneId));
            System.out.println("from " + left + " to " + right + " YM " + period.toMonthGroupString() + " YQ " + period.toQuarterGroupString());
        }
        long target = 1646791750000L; // 2022-03-09 10:09:10
        ForecastPeriod pair = ForecastPeriodMatcher.matchPeriod(target, periods);
        Assert.assertNotNull(pair);
        print(dateTimeFormatter, zoneId, target, pair);
        target = 1236564550000L; // 2009-03-9 10:09:10
        pair = ForecastPeriodMatcher.matchPeriod(target, periods);
        Assert.assertNotNull(pair);
        print(dateTimeFormatter, zoneId, target, pair);
    }

    private void print(DateTimeFormatter dateTimeFormatter, ZoneId zoneId, long target, ForecastPeriod period) {
        System.out.print("target " + dateTimeFormatter.format(ZonedDateTime.ofInstant(Instant.ofEpochMilli(target), zoneId)) + " match period ");
        if (period == null) {
            System.out.println("null");
        } else {
            String left = dateTimeFormatter.format(ZonedDateTime.ofInstant(Instant.ofEpochMilli(period.getLeft()), zoneId));
            String right = dateTimeFormatter.format(ZonedDateTime.ofInstant(Instant.ofEpochMilli(period.getRight()), zoneId));
            System.out.println("[" + left + " ~ " + right + "]" + " YM " + period.toMonthGroupString() + " YQ " + period.toQuarterGroupString());
        }
    }

    @Test
    public void sendRuleEnableMessage() throws MQBrokerException, RemotingException, InterruptedException, MQClientException {
        DefaultMQProducer producer = new DefaultMQProducer();
        producer.setNamesrvAddr("***********:9876;************:9876");
        producer.setInstanceName("");
        producer.setVipChannelEnabled(false);
        producer.setSendMsgTimeout(3000);
        producer.setMaxMessageSize(4194304);
        producer.setRetryTimesWhenSendFailed(2);
        producer.setRetryTimesWhenSendAsyncFailed(2);
        producer.setProducerGroup(ForecastMessage.RULE_ENABLE_TOPIC_TAG); // fs-crm-sfa-forecast-rule-enable
        Message message = new Message();
        ForecastMessage.RuleEnable enableMessage = new ForecastMessage.RuleEnable();
        enableMessage.setTenantId("79337");
        String ruleId = "628762c02864342f08d9801e";
        enableMessage.setRuleId(ruleId);
        message.setTopic("sfa-recycling-recalculate");
        message.setTags(ForecastMessage.RULE_ENABLE_TOPIC_TAG);
        message.setBody(JSON.toJSONString(enableMessage).getBytes());
        message.setKeys(ruleId);
        producer.start();
        SendResult result = producer.send(message);
        Assert.assertNotNull(result);
        System.out.println(result.getMsgId());
    }

    @Test
    public void sendObjectSyncMessage() throws MQBrokerException, RemotingException, InterruptedException, MQClientException {
        DefaultMQProducer producer = new DefaultMQProducer();
        producer.setNamesrvAddr("***********:9876;************:9876");
        producer.setInstanceName("");
        producer.setVipChannelEnabled(false);
        producer.setSendMsgTimeout(3000);
        producer.setMaxMessageSize(4194304);
        producer.setRetryTimesWhenSendFailed(2);
        producer.setRetryTimesWhenSendAsyncFailed(2);
        producer.setProducerGroup(ForecastMessage.OBJECT_SYNC_TOPIC_TAG); // fs-crm-sfa-forecast-object-sync
        ForecastMessage.ObjectSync syncMessage = new ForecastMessage.ObjectSync();
        syncMessage.setTenantId("79337");
        syncMessage.setObjectId("6247fb0fb33e24000165c10c");
        syncMessage.setObjectApiName("NewOpportunityObj");
        Message message = new Message();
        message.setTopic("sfa-recycling-recalculate");
        message.setTags("fs-crm-sfa-forecast-object-sync");
        message.setBody(JSON.toJSONString(syncMessage).getBytes()); // {"objectId":"6247fb0fb33e24000165c10c","objectApiName":"NewOpportunityObj","tenantId":"79337"}
        message.setKeys("6247fb0fb33e24000165c10c");
        producer.start();
        SendResult result = producer.send(message);
        Assert.assertNotNull(result);
        System.out.println(result.getMsgId());
    }

}
