package com.facishare.crm.recycling.task.executor.service.impl.forecast;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.BaseTest;
import com.facishare.crm.recycling.task.executor.biz.ForecastBiz;
import com.facishare.crm.recycling.task.executor.enums.forecast.ForecastObjectEnums;
import com.facishare.crm.recycling.task.executor.enums.forecast.ForecastRuleEnums;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastObjectRecord;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastPeriod;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastRule;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastTask;
import com.facishare.crm.recycling.task.executor.model.forecast.ForecastTaskDetail;
import com.facishare.crm.recycling.task.executor.service.ForecastTaskAutomationService;
import com.facishare.crm.recycling.task.executor.service.impl.forecast.mq.ForecastMessage;
import com.facishare.crm.recycling.task.executor.service.impl.forecast.task.ForecastPeriodMatcher;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.fxiaoke.paas.gnomon.api.NomonProducer;
import com.fxiaoke.paas.gnomon.api.entity.NomonMessage;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class ForecastServiceTestCase extends BaseTest {

    @Autowired
    private ForecastServiceImpl forecastService;
    @Autowired
    private ForecastBiz forecastBiz;
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private NomonProducer nomonProducer;
    @Autowired
    private ForecastTaskAutomationService forecastTaskAutomationService;

    private final String tenantId = "79337";
    private final String ruleId = "62f0c8bbd3278d00016d0718";

    @Test
    public void initTaskByRule() throws MetadataServiceException {
        ForecastRule rule = forecastBiz.queryRuleById(tenantId, ruleId);
        Assert.assertNotNull(rule);
        Map<String, Object> data = new HashMap<>();
        data.put(ForecastRule.BIZ_STATUS, ForecastRuleEnums.BizStatus.ENABLING.getValue());
        forecastBiz.updateRuleById(rule.getTenantId(), rule.getId(), data);
        rule.setBizStatus(ForecastRuleEnums.BizStatus.ENABLING.getValue());
        forecastService.initTaskByRule(rule);
    }

    @Test
    public void deleteTaskByOwner() {
        Assert.assertNotNull(tenantId);
        forecastService.deleteTaskByOwner(tenantId, "1002");
    }

    @Test
    public void deleteTaskByRule() throws MetadataServiceException {
        ForecastRule rule = forecastBiz.queryRuleById(tenantId, ruleId);
        Assert.assertNotNull(rule);
        forecastService.deleteTaskByRule(rule);
    }

    @Test
    public void syncForecastObjectDelete() throws MetadataServiceException {
        syncForecastObject("DELETE");
    }

    @Test
    public void syncForecastObjectInsert() throws MetadataServiceException {
        syncForecastObject("INSERT");
    }

    @Test
    public void syncForecastObjectUpdate() throws MetadataServiceException {
        syncForecastObject("UPDATE");
    }

    @Test
    public void syncForecastObjectChangeOwner() throws MetadataServiceException {
        syncForecastObject("CHANGE_OWNER");
    }

    @Test
    public void autoCheckSyncForecastObjectUpdate() throws MetadataServiceException {
        ForecastMessage.ObjectSync message = new ForecastMessage.ObjectSync();
        message.setTenantId(tenantId);
        message.setObjectId("6247fb0fb33e24000165c10c");
        message.setObjectApiName("NewOpportunityObj");
        message.setAction("UPDATE");
        ForecastObjectEnums.SyncAction action = ForecastObjectEnums.SyncAction.valueOf(message.getAction());
        IObjectData objectData = serviceFacade.findObjectData(User.systemUser(message.getTenantId()), message.getObjectId(), message.getObjectApiName());
        ForecastRule rule = forecastBiz.queryRuleById(tenantId, "628762c02864342f08d9801e");
        Set<String> ruleIds = Collections.singleton(rule.getId());
        List<ForecastTask> originTasks = forecastBiz.queryTasksForSync(tenantId, ruleIds, objectData.getId());
        ForecastTask originTask = originTasks.get(0);
        ForecastTaskDetail detail = originTask.getForecastTaskDetails().get(0);
        BigDecimal originAmount = detail.getForecastAmount();
        List<ForecastTask> currentTasks = forecastBiz.queryTasksForSync(tenantId, ruleIds, objectData.getId());
        ForecastTask currentTask = currentTasks.get(0);
        ForecastTaskDetail currentDetail = currentTask.getForecastTaskDetails().get(0);
        Assert.assertNotNull(currentDetail);
        ForecastObjectRecord forecastObject = new ForecastObjectRecord(objectData);
        // case 1 uniqueKey没变 amount改变
        BigDecimal currentAmount = originAmount.subtract(BigDecimal.TEN);
        forecastObject.set(rule.getForecastObjectAmountApiName(), currentAmount);
        Long currentData = 1651690739000L;
        forecastObject.set(rule.getForecastObjectDateApiName(), currentData);
        forecastService.syncForecastObject(forecastObject, action);
        currentTasks = forecastBiz.queryTasksForSync(tenantId, ruleIds, objectData.getId());
        currentTask = currentTasks.get(0);
        currentDetail = currentTask.getForecastTaskDetails().get(0);
        Assert.assertNotEquals(0, currentTask.getBestPracticesForecastModel1().compareTo(originTask.getBestPracticesForecastModel1()));
        Assert.assertEquals(currentDetail.getForecastAmount(), currentAmount);
        // case 2 uniqueKey改变 amount改变
        forecastService.syncForecastObject(forecastObject, action);
        currentTasks = forecastBiz.queryTasksForSync(tenantId, ruleIds, objectData.getId());
        currentTask = currentTasks.get(0);
        currentDetail = currentTask.getForecastTaskDetails().get(0);
        Assert.assertEquals(currentDetail.getForecastDate(), currentData);
        Assert.assertEquals(currentDetail.getForecastTaskObjectId(), currentTask.getId());
        Assert.assertNotEquals(currentTask.getId(), originTask.getId());
        // case 3 时间区间发生改变且不在规则范围之内
        List<ForecastPeriod> periods = ForecastPeriodMatcher.generatePeriod(rule);
        currentData = periods.get(periods.size() - 1).getRight() + 1;
        forecastObject.set(rule.getForecastObjectDateApiName(), currentData);
        forecastService.syncForecastObject(forecastObject, action);
        currentTasks = forecastBiz.queryTasksForSync(tenantId, ruleIds, objectData.getId());
        Assert.assertEquals(0, currentTasks.size());
        // case 4 还原
        syncForecastObject("INSERT");
        currentTasks = forecastBiz.queryTasksForSync(tenantId, ruleIds, objectData.getId());
        Assert.assertEquals(1, currentTasks.size());
    }

    private void syncForecastObject(String action) throws MetadataServiceException {
        ForecastMessage.ObjectSync message = new ForecastMessage.ObjectSync();
        message.setTenantId(tenantId);
        message.setObjectId("62be9c851317960001ee5a64");
        message.setObjectApiName("NewOpportunityObj");
        message.setAction(action);
        forecastService.syncForecastObject(message);
        Assert.assertNotNull(tenantId);
    }

    @Test
    public void testSubmitForecastTaskRemindBeforeEndMessage() {
        Assert.assertNotNull(nomonProducer);
        long now = System.currentTimeMillis();
        ForecastMessage.RuleEnable callArg = new ForecastMessage.RuleEnable();
        callArg.setRuleId(ruleId);
        callArg.setTenantId(tenantId);
        callArg.setExpectTime(1651679999999L);
        callArg.setCreateTime(now);
        NomonMessage message = NomonMessage.builder()
                .biz(ForecastMessage.TASK_REMIND_TOPIC_TAG)
                .tenantId(tenantId)
                .dataId(ruleId)
                .executeTime(new Date())
                .callArg(JSON.toJSONString(callArg))
                .build();
        nomonProducer.send(message);
    }

    @Test
    public void testSubmitForecastTaskLockAfterEndMessage() {
        Assert.assertNotNull(nomonProducer);
        long now = System.currentTimeMillis();
        ForecastMessage.RuleEnable callArg = new ForecastMessage.RuleEnable();
        callArg.setRuleId(ruleId);
        callArg.setTenantId(tenantId);
        callArg.setExpectTime(1657220339000L);
        callArg.setCreateTime(now);
        NomonMessage message = NomonMessage.builder()
                .biz(ForecastMessage.TASK_LOCK_TOPIC_TAG)
                .tenantId(tenantId)
                .dataId(ruleId)
                .executeTime(new Date())
                .callArg(JSON.toJSONString(callArg))
                .build();
        nomonProducer.send(message);
    }

    @Test
    public void handleForecastTaskAutoLockAfterEndMessage() throws MetadataServiceException {
        ForecastRule rule = forecastBiz.queryRuleById(tenantId, ruleId);
        Assert.assertNotNull(rule);
        ForecastMessage.RuleEnable message = new ForecastMessage.RuleEnable();
        message.setCreateTime(System.currentTimeMillis());
        message.setExpectTime(1719763199999L);
        message.setTenantId(tenantId);
        message.setRuleId(ruleId);
        forecastTaskAutomationService.handleForecastTaskAutoLockAfterEndMessage(rule, message);
    }

    @Test
    public void launch() throws InterruptedException {
        Assert.assertNotNull(tenantId);
        synchronized (tenantId) {
            tenantId.wait();
        }
    }
}