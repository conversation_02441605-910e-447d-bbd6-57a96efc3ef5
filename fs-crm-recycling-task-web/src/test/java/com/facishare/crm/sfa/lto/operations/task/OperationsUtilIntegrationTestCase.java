package com.facishare.crm.sfa.lto.operations.task;

import com.facishare.crm.BaseTest;
import com.facishare.crm.sfa.lto.operations.OperationsUtil;
import com.facishare.paas.license.http.LicenseClient;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class OperationsUtilIntegrationTestCase extends BaseTest {
    @Autowired
    private LicenseClient licenseClient;

    @Test
    public void testHasLicense() {
        Assert.assertTrue(OperationsUtil.hasLicense("79337", licenseClient));
    }
}
