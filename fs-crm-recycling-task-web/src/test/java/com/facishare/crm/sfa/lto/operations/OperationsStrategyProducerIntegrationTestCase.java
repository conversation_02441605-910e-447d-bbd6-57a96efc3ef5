package com.facishare.crm.sfa.lto.operations;

import com.facishare.crm.BaseTest;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.TimeZone;

public class OperationsStrategyProducerIntegrationTestCase extends BaseTest {

    @Autowired
    private OperationsStrategyProducer producer;
    @Autowired
    private ServiceFacade serviceFacade;

    @Test
    public void testSendEnableMessage() {
        User user = User.systemUser("79337");
        IObjectData strategy = serviceFacade.findObjectData(user, "65f16b02ef131317d8fd5c7c", OperationsConstant.OPERATION_STRATEGY_OBJECT_API_NAME);
        Assert.assertNotNull(strategy);
        producer.sendEnableMessage(strategy, TimeZone.getTimeZone("GMT+8"));
    }
}
