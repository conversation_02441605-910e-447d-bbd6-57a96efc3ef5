package com.facishare.crm.sfa.lto.operations.task;

import com.facishare.crm.BaseTest;
import com.facishare.crm.sfa.lto.common.LtoOrgCommonService;
import com.facishare.crm.sfa.lto.operations.OperationsConstant;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.expression.ExpressionService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.service.ICommonSqlService;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;

public class OperationsTaskUtilIntegrationTestCase extends BaseTest {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private ExpressionService expressionService;
    @Autowired
    private ICommonSqlService commonSqlService;
    @Autowired
    private LtoOrgCommonService ltoOrgCommonService;

    @Test
    public void testCreateTaskByTemplate() {
        User user = User.systemUser("79337");
        IObjectData template = serviceFacade.findObjectData(user, "65f02cb0ef13133974cded11", OperationsConstant.OPERATION_STRATEGY_TASK_TEMPLATE_OBJECT_API_NAME);
        IObjectData account = serviceFacade.findObjectData(user, "62fb0416d5dc810001bbb286", "AccountObj");
        OperationsTaskTemplateUtil.getMemberField(template, commonSqlService);
        OperationsTaskUtil util = new OperationsTaskUtil(serviceFacade, expressionService, ltoOrgCommonService);
        List<OperationsTask> task = util.createTaskByTemplate(template, Collections.singletonList(account));
        Assert.assertFalse(task.isEmpty());
    }
}
