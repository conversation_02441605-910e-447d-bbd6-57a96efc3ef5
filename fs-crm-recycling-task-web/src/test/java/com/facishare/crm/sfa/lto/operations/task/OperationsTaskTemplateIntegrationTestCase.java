package com.facishare.crm.sfa.lto.operations.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.BaseTest;
import com.facishare.crm.sfa.lto.operations.OperationsConstant;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.service.ICommonSqlService;
import com.facishare.paas.metadata.impl.ObjectData;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class OperationsTaskTemplateIntegrationTestCase extends BaseTest {

    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private ICommonSqlService commonSqlService;

    @Test
    public void testAll() {
        String id = serviceFacade.generateId();
        IObjectData t1 = new ObjectData();
        t1.setId(id);
        t1.setTenantId("79337");
        t1.set(OperationsConstant.TASK_CC, getDefaultValue());
        t1.set(OperationsConstant.TASK_EXECUTORS, getDefaultValue());
        OperationsTaskTemplateUtil.saveMemberField(t1, commonSqlService);
        IObjectData t2 = new ObjectData();
        t2.setId(t1.getId());
        t2.setTenantId("79337");
        OperationsTaskTemplateUtil.getMemberField(t2, commonSqlService);
        Assert.assertEquals(JSON.toJSONString(t1.get(OperationsConstant.TASK_CC)), JSON.toJSONString(t2.get(OperationsConstant.TASK_CC)));
        Assert.assertEquals(JSON.toJSONString(t1.get(OperationsConstant.TASK_EXECUTORS)), JSON.toJSONString(t2.get(OperationsConstant.TASK_EXECUTORS)));
        IObjectData t3 = new ObjectData();
        t3.setId(id);
        t3.setTenantId("79337");
        int deleted = OperationsTaskTemplateUtil.deleteMemberField(t3, commonSqlService);
        Assert.assertTrue(deleted > 0);
    }

    private static JSONObject getDefaultValue() {
        return JSON.parseObject("{\"person\":[\"1076\"],\"ext_process\":[\"owner\",\"data_group\",\"data_owner_leader\"]}");
    }
}
