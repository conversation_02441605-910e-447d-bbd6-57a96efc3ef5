package com.facishare.crm.sfa.lto.account;

import com.facishare.crm.BaseTest;
import com.facishare.crm.sfa.lto.utils.AccountTreeRelationUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.license.http.LicenseClient;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

public class AccountTreeRelationUtilTestCase extends BaseTest {

    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private LicenseClient licenseClient;

//    @Test
//    public void testAfterAccountMainDataDeleted() {
//        User user = User.systemUser("78532");
//        AccountTreeRelationUtil.afterAccountMainDataDeleted(user, Collections.singletonList("64c0f0ae84f688000104cfb0"), serviceFacade);
//    }
//
//    @Test
//    public void testAfterEnterpriseNameChanged() {
//        User user = User.systemUser("78532");
//        Map<String, String> diffMap = new HashMap<>();
//        AccountTreeRelationUtil.afterEnterpriseNameChanged(user, diffMap, serviceFacade);
//    }
//
//    @Test
//    public void testHasLicense() {
//        Assert.assertTrue(AccountTreeRelationUtil.hasLicense("78532", licenseClient));
//        Assert.assertFalse(AccountTreeRelationUtil.hasLicense("78531", licenseClient));
//    }
}
