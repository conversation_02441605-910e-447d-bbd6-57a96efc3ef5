package com.facishare.crm;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.recycling.task.executor.enums.ActionCodeEnum;
import com.facishare.crm.recycling.task.executor.enums.ApiNameEnum;
import com.facishare.crm.recycling.task.executor.model.ObjectLimitRuleCalculateModel;
import com.facishare.crm.recycling.task.executor.model.RecalculateMessage;
import com.facishare.crm.recycling.task.executor.model.RecyclingMessage;
import com.facishare.crm.recycling.task.executor.model.RemindMessage;
import com.facishare.crm.recycling.task.executor.util.DateUtils;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.fxiaoke.paas.gnomon.api.NomonProducer;
import com.fxiaoke.paas.gnomon.api.entity.NomonMessage;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-02-20 11:34
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext-test.xml"}, initializers = ProfileApplicationContextInitializer.class)
public class BaseTest {

    public static final String TENANT_ID = "78696";
    public static final String OWNER = "1000";

    @Autowired
    private NomonProducer nomonProducer;


    @Autowired
    private IObjectDataService objectDataPgService;

    @Test
    public void send() {
        RecyclingMessage recyclingMessage = new RecyclingMessage();
        recyclingMessage.setTenantId("74203");
        recyclingMessage.setObjectId("5d552531a5083d742e7fc8ab");
        recyclingMessage.setObjectApiName("AccountObj");
        recyclingMessage.setTargetId("8115fba9a45a4e05a7b90180ca215fb5");

        NomonMessage nomonMessage = NomonMessage.builder()
                .biz("crm_object_recycling_task_recycling")
                .tenantId("74203")
                .dataId("5d552531a5083d742e7fc8ab")
                .executeTime(DateUtils.asDate(LocalDateTime.now()))
                .callArg(JSONObject.toJSON(recyclingMessage).toString())
                .build();
        nomonProducer.send(nomonMessage);
    }

    @Test
    public void sendRecalculate() {

        RecalculateMessage recalculateMessage = new RecalculateMessage();
        recalculateMessage.setTenantId("74745");
        recalculateMessage.setActionCode(ActionCodeEnum.FOLLOW.getActionCode());
        recalculateMessage.setObjectId("5d11fa57a5083d1e35d44f0d");
        recalculateMessage.setObjectApiName("AccountObj");


        NomonMessage nomonMessage = NomonMessage.builder()
                .biz("crm_object_recycling_task_recalculate")
                .tenantId("74745")
                .dataId("5d11fa57a5083d1e35d44f0d")
                .executeTime(DateUtils.asDate(LocalDateTime.now().plusMinutes(1)))
                .callArg(JSONObject.toJSON(recalculateMessage).toString())
                .build();
        nomonProducer.send(nomonMessage);
    }

    @Test
    public void sendRecalculateByChangeRule() {

        RecalculateMessage recalculateMessage = new RecalculateMessage();
        recalculateMessage.setTenantId("74203");
        recalculateMessage.setActionCode(ActionCodeEnum.CHANGE_RULE.getActionCode());
        recalculateMessage.setObjectId("dcaed8bec4904d3881e8ac55df4f4a49");
        recalculateMessage.setObjectApiName("AccountObj");


        NomonMessage nomonMessage = NomonMessage.builder()
                .biz("crm_object_recycling_task_recalculate")
                .tenantId("74203")
                .dataId("dcaed8bec4904d3881e8ac55df4f4a49")
                .executeTime(DateUtils.asDate(LocalDateTime.now()))
                .callArg(JSONObject.toJSON(recalculateMessage).toString())
                .build();
        nomonProducer.send(nomonMessage);
    }


    @Test
    public void sendRemindTask() {

        RemindMessage remindMessage = RemindMessage.builder()
                .objectApiName(ApiNameEnum.ACCOUNT_OBJ.getApiName())
                .batchRemind("true")
                .objectId("73c00ccd94274587b4645a466037a0fc")
                .tenantId("76311")
                .build();

        NomonMessage nomonMessage = NomonMessage.builder()
                .biz("crm_object_recycling_task_remind")
                .tenantId("76311")
                .dataId("73c00ccd94274587b4645a466037a0fc")
                .executeTime(new Date(System.currentTimeMillis() + 1000 * 30))
                .callArg(JSONObject.toJSON(remindMessage).toString())
                .build();
        nomonProducer.send(nomonMessage);
    }

    @Test
    public void test(){
        IObjectData data2 = new ObjectData();
        List<String> updateFieldList2 = Lists.newArrayList();
        data2.setTenantId("74203");
        data2.setDescribeApiName("SalesOrderProductObj");
        data2.setId("5d0a24610230ad00016492a1");
        data2.set("product_price",new BigDecimal(1200));
        updateFieldList2.add("product_price");
        try {
            objectDataPgService.batchUpdateIgnoreOther(Lists.newArrayList(data2),Lists.newArrayList(updateFieldList2),getDefaultActionContext());
        } catch (MetadataServiceException e) {
            e.printStackTrace();
        }
    }



    protected ActionContext getDefaultActionContext() {
        ActionContext actionContext = new ActionContext();
        actionContext.setEnterpriseId("74203");
        actionContext.setUserId("-10000");
        actionContext.setDbType("pg");
        actionContext.setPrivilegeCheck(false);
        return actionContext;
    }


    protected void commonConvert2WheresList(String wheresString, List<Wheres> wheresList) {
        if(StringUtils.isEmpty(wheresString)) {
            return;
        }
        List<JSONObject> wheresJSONObjectList = JSON.parseObject(wheresString, List.class);
        if(CollectionUtils.notEmpty(wheresJSONObjectList)){
            for(JSONObject jsonObject : wheresJSONObjectList){
                Wheres wheres = JSON.parseObject(jsonObject.toJSONString(), Wheres.class);
                wheresList.add(wheres);
            }
        }
    }

    @Test
    public void sendObjectLimit() {

        String groupId = "5dc5165cdd67b900019191a4";
        String tenantId = "74203";
        ObjectLimitRuleCalculateModel.ObjectLimitRuleCalculateMessage recalculateMessage = new ObjectLimitRuleCalculateModel.ObjectLimitRuleCalculateMessage();
        recalculateMessage.setTenantId(tenantId);
        recalculateMessage.setObjectApiName(Utils.LEADS_API_NAME);
        recalculateMessage.setGroupId("5dc5165cdd67b900019191a4");
        recalculateMessage.setCurrentCalculateId("5de0e8e7b67dd40001e76220");


        NomonMessage nomonMessage = NomonMessage.builder()
                .biz("crm_object_limit_calculate")
                .tenantId(tenantId)
                .dataId(groupId)
                .executeTime(DateUtils.asDate(LocalDateTime.now().plusMinutes(1)))
                .callArg(JSONObject.toJSON(recalculateMessage).toString())
                .build();
        nomonProducer.send(nomonMessage);
    }
}
