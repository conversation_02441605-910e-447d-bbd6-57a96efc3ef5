package com.facishare.crm.sfa.equity.es

import com.facishare.crm.sfa.BaseGroovyTest
import com.facishare.paas.metadata.service.impl.ObjectDescribeServiceImpl
import org.springframework.beans.factory.annotation.Autowired
import spock.lang.Title

@Title("股权ES服务查询测试")
class EquityESServiceImplTest extends BaseGroovyTest {

    EquityESServiceImpl equityESService

    @Autowired
    private ObjectDescribeServiceImpl objectDescribeService = Mock()

    def setup() {
        equityESService = new EquityESServiceImpl()
        objectDescribeService.getDescribe(_,_,_) >> Mock()
        equityESService.init()
    }


    def "test queryPathEnterprise Spy"() {
        when: "queryPathEnterprise is called"
        def result = equityESService.queryPathEnterprise(enterpriseId)
        then: "the response is returned"
        result != null
        print(result)
        where:
        enterpriseId << ["4b89842f0218456bba5f5239d8932a5f"]
    }

    def "test queryParentEnterprise"() {
        EquityESService service = new EquityESServiceImpl();
        def result = service.queryParentEnterprise("someEnterpriseId", null);
    }

    def "test queryEnterprise"() {
        when: "queryEnterprise is called"
        def result = equityESService.queryEnterprise(enterpriseId)
        then: "the response is returned"
        result != null
        print(result)
        where:
        enterpriseId << ["4b89842f0218456bba5f5239d8932a5f"]
    }

    def "test queryChildEnterprise"() {
        when: "queryChildEnterprise is called"
        def result = equityESService.queryChildEnterprise(enterpriseId)
        then: "the response is returned"
        result != null
        print(result)
        where:
        enterpriseId << ["4b89842f0218456bba5f5239d8932a5f"]
    }
}