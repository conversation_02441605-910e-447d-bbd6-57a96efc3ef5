package com.facishare.crm.sfa

import com.facishare.eservice.common.utils.SpringContextUtil
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.RequestContextManager
import com.facishare.paas.appframework.core.model.ServiceContext
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.Lang
import org.springframework.context.ApplicationContext
import org.springframework.test.context.ContextConfiguration
import spock.lang.Shared
import spock.lang.Specification
@ContextConfiguration(value = "classpath:applicationContext-test.xml")
abstract class BaseGroovyTest extends Specification{
    @Shared
    protected ServiceContext serviceContext
    @Shared
    protected RequestContext requestContext
    @Shared
    protected String tenantId
    @Shared
    protected String fsUserId
    @Shared
    protected User user

    def setupSpec() {
        System.setProperty("spring.profiles.active", "fstest");
        initRequestContext()
        initSpringContext()
    }

    protected initI18nClient() {
        def field = I18nClient.class.getDeclaredField("impl")
        field.setAccessible(true)

        def stub = Stub(I18nServiceImpl)
        field.set(I18nClient.getInstance(), stub)
        stub.getOrDefault(_ as String, _ as Long, _ as String, _ as String) >> {
            String key, Long tenantId, String lang, String defaultVal ->
                return defaultVal
        }
    }

    ServiceContext initRequestContext() {
        initUser()
        this.user = new User(tenantId, fsUserId)
        RequestContext.RequestContextBuilder requestContextBuilder = RequestContext.builder()
        requestContextBuilder.tenantId(tenantId)
        requestContextBuilder.user(this.user)
        requestContextBuilder.contentType(RequestContext.ContentType.FULL_JSON)
        requestContextBuilder.requestSource(RequestContext.RequestSource.CEP)
        requestContextBuilder.lang(Lang.zh_CN)
        requestContext = requestContextBuilder.build()
        RequestContextManager.setContext(requestContext)
    }

    protected ServiceContext getServiceContext(String serviceName, String serviceMethod) {
        return new ServiceContext(requestContext, serviceName, serviceMethod)
    }

    void initUser() {
        this.tenantId = "79337";
        this.fsUserId = "1000";
    }

    void initSpringContext() {
        def stubApplicationContext = Stub(ApplicationContext)
        def util = new SpringContextUtil()
        util.setApplicationContext(stubApplicationContext)
        Map<Class, Object> map = new HashMap<>()
        stubApplicationContext.getBean(_ as Class) >> { Class requiredType ->
            map.computeIfAbsent(requiredType, { key ->
                Stub(requiredType)
            })
        }
    }
}
