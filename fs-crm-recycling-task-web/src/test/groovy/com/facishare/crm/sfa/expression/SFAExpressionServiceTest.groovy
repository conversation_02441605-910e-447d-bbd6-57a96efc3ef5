import com.facishare.crm.sfa.BaseGroovyTest
import com.facishare.crm.sfa.expression.SFAExpressionService
import com.facishare.paas.metadata.api.service.IObjectDataService
import com.facishare.paas.metadata.api.service.IObjectDescribeService
import org.springframework.beans.factory.annotation.Autowired
import spock.lang.Title

@Title("表达式解析测试")
//@ContextConfiguration(value = "classpath:applicationContext-test.xml")
class SFAExpressionServiceTest extends BaseGroovyTest {

    @Autowired
    SFAExpressionService sfaExpressionService

    @Autowired
    private IObjectDataService objectDataPgService

    @Autowired
    private IObjectDescribeService objectDescribeService


    void setup() {
    }

    void cleanup() {

    }

    def "Evaluate"() {
        given:
        def objectdata = objectDataPgService.findById(dataId, tenantId, describeApiName)
        def objectDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, apiName)
        def wheres = "[{\"filters\": [{\"isIndex\": false, \"fieldNum\": 0, \"operator\": \"EQ\", \"connector\": \"AND\", \"field_name\": \"field_42zcU__c\", \"field_values\": [\"33333333\"], \"operator_name\": \"等于\", \"isObjectReferencea\": false}, {\"isIndex\": false, \"fieldNum\": 0, \"operator\": \"N\", \"connector\": \"AND\", \"field_name\": \"tel\", \"field_values\": [\"33333\"], \"operator_name\": \"不等于\", \"isObjectReferencea\": false}, {\"isIndex\": false, \"fieldNum\": 0, \"operator\": \"LT\", \"connector\": \"AND\", \"field_name\": \"UDDate1__c\", \"field_values\": [1573574400000], \"operator_name\": \"早于\", \"isObjectReferencea\": false}, {\"isIndex\": false, \"fieldNum\": 0, \"operator\": \"NLIKE\", \"connector\": \"AND\", \"field_name\": \"tel\", \"field_values\": [\"11\"], \"operator_name\": \"不包含\", \"isObjectReferencea\": false}], \"connector\": \"OR\"}]"

        when:
        def a = sfaExpressionService.evaluate(wheres, objectdata, objectDescribe)

        then:
        a == true

        where:
        dataId | tenantId | describeApiName | apiName
        "2323" | "74203"  | "AccountObj"    | "AccountObj"

    }


    def "TestEvaluate"() {
    }

    def "GetExpression"() {
    }
}
