<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:c="http://www.springframework.org/schema/c" xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-4.1.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <import resource="classpath:spring/metadata.xml"/>
    <import resource="classpath:spring/common.xml"/>
    <import resource="classpath:spring/dubbo.xml"/>
    <import resource="classpath:spring/log.xml"/>
    <import resource="classpath:spring/flow.xml"/>
    <import resource="classpath:spring/privilege.xml"/>
    <import resource="classpath:spring/licence.xml"/>
    <import resource="classpath:spring/fsi.xml"/>
    <import resource="classpath:spring/payment.xml"/>
    <import resource="classpath:spring/restdriver.xml"/>
    <import resource="classpath*:spring/ei-ea-converter.xml"/>
    <import resource="classpath:spring/function-service.xml"/>
    <import resource="classpath:spring/config.xml"/>
    <import resource="classpath:privilege-temp.xml"/>
    <import resource="classpath:spring/mq.xml"/>
    <import resource="spring-aop.xml"/>
    <import resource="classpath:spring/action.xml"/>
    <import resource="classpath:spring/idempotent.xml"/>
    <import resource="classpath:META-INF/spring/fs-fsi-proxy-service.xml"/>
    <import resource="classpath:lto.xml"/>
    <import resource="classpath:spring/dispatch.xml"/>
    <import resource="classpath:spring/follow-mq.xml"/>
    <import resource="classpath:spring/cores.xml"/>
    <import resource="classpath:fs-social-api.xml"/>
    <import resource="classpath*:equity.xml"/>
    <import resource="classpath*:spring/global-transaction-tcc.xml"/>
    <import resource="classpath:META-INF/fs-paas-ai-client.xml"/>



    <context:component-scan base-package="com.facishare.paas.appframework,com.facishare.crm"/>
    <context:annotation-config/>


    <bean id="autoConf"
          class="com.github.autoconf.spring.reloadable.ReloadablePropertySourcesPlaceholderConfigurer"
          p:fileEncoding="UTF-8"
          p:ignoreResourceNotFound="true"
          p:ignoreUnresolvablePlaceholders="false"
          p:location="classpath:application.properties"
          p:configName="dubbo-common,fs-paas-metadata-mongo,fs-paas-appframework-rest,fs-crm-java-config
            ,fs-crm-printconfig,fs-metadata,fs-crm-icon-path,fs-crm-java-detailpage-layout-setting
            ,fs-crm-sys-variable,fs-crm-java-openapi-black-list,fs-crm-privilege,fs-crm-java-fsi-proxy
            ,fs-crm-java-special-field,fs-crm-add-function-code,fs-crm-java-reference_relation_type
            ,new-predefined-com.facishare.crm.privilege.objects,new-predefined-object,fs-crm-task-rate-limit"/>

    <bean class="com.github.autoconf.spring.reloadable.ReloadablePropertyPostProcessor"
          c:placeholderConfigurer-ref="autoConf"/>


    <bean id="nomonProducer" class="com.fxiaoke.paas.gnomon.api.NomonProducer"/>

    <!--推送企信消息-->
    <!--    <dubbo:reference id="sessionSandwichApiService"-->
    <!--                     interface="com.facishare.qixin.plugin.service.SessionSandwichApiService"-->
    <!--                     check="false" retries="0" timeout="3000"/>-->

    <bean id="recyclingRuleProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.recycling.task.executor.proxy.RecyclingRuleProxy">
        <property name="factory" ref="restServiceProxyFactory">
        </property>
    </bean>


    <bean id="approvalInitProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.recycling.task.executor.proxy.ApprovalInitProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean id="bizConfApi" class="com.facishare.restful.client.FRestApiProxyFactoryBean">
        <property name="type" value="com.fxiaoke.bizconf.api.BizConfApi"/>
    </bean>


    <bean id="sfaExpressionService" class="com.facishare.crm.sfa.expression.SFAExpressionServiceImpl"/>

    <dubbo:reference id="checkinsOfficeV2Service"
                     interface="com.facishare.appserver.checkinsoffice.api.service.CheckinsOfficeV2Service"
                     check="false" timeout="3000" retries="0"/>

</beans>

