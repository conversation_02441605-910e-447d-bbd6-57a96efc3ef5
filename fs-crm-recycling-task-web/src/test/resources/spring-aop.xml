<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <aop:aspectj-autoproxy />

    <bean id="serviceProfiler" class="com.github.trace.aop.ServiceProfiler"/>
    <aop:config>
        <aop:aspect ref="serviceProfiler" order="1">
            <aop:around method="profile" pointcut="execution(* com.facishare.crm.recycling.task.web.*.*(..))  || (execution(* com.facishare.crm.recycling.task.executor.biz.*.*(..)))"/>
        </aop:aspect>
    </aop:config>

<!--    <bean class="com.fxiaoke.metrics.MetricsConfiguration" />-->

    <context:annotation-config />
</beans>
