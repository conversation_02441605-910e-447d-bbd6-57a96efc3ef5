package com.facishare.crm.sfa.lto.utils;

import com.facishare.paas.appframework.common.service.CRMNotificationService;
import com.facishare.paas.appframework.common.service.dto.InternationalItem;
import com.facishare.paas.appframework.common.service.model.NewCrmNotification;
import com.facishare.paas.appframework.core.model.User;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.common.util.set.Sets;

import java.util.List;
import java.util.Map;
import java.util.UUID;


@Slf4j
public class CRMRecordUtil {
    private CRMRecordUtil() {
        throw new IllegalStateException("Utility class");
    }

    public static void sendNewCRMRecord(CRMNotificationService crmNotifyService,
                                        User user,
                                        int type,
                                        List<Integer> receiverIds,
                                        String senderId,
                                        String title,
                                        String fullContent,
                                        String titleInternationalKey,
                                        List<String> internationalParameters,
                                        String fullContentInternationalKey,
                                        List<String> fullContentInternationalParameters,
                                        Map<String, String> urlParameter, String appId) {
        if (receiverIds.isEmpty()) {
            return;
        }
        InternationalItem titleInternationalItem = null;
        InternationalItem contentInternationalItem = null;
        if (!Strings.isNullOrEmpty(titleInternationalKey)) {
            titleInternationalItem = new InternationalItem();
            titleInternationalItem.setInternationalParameters(internationalParameters);
            titleInternationalItem.setInternationalKey(titleInternationalKey);
        }
        if (!Strings.isNullOrEmpty(fullContentInternationalKey)) {
            contentInternationalItem = new InternationalItem();
            contentInternationalItem.setInternationalParameters(fullContentInternationalParameters);
            contentInternationalItem.setInternationalKey(fullContentInternationalKey);
        }
        sendNewCRMRecord(crmNotifyService, user, type, receiverIds, senderId, title, fullContent, titleInternationalItem, contentInternationalItem, urlParameter, appId, false);
    }

    public static void sendNewCRMRecordRemindSender(CRMNotificationService crmNotifyService,
                                                    User user,
                                                    int type,
                                                    List<Integer> receiverIds,
                                                    String senderId,
                                                    String title,
                                                    String fullContent,
                                                    String titleInternationalKey,
                                                    List<String> internationalParameters,
                                                    String fullContentInternationalKey,
                                                    List<String> fullContentInternationalParameters,
                                                    Map<String, String> urlParameter, String appId) {
        if (receiverIds.isEmpty()) {
            return;
        }
        InternationalItem titleInternationalItem = null;
        InternationalItem contentInternationalItem = null;
        if (!Strings.isNullOrEmpty(titleInternationalKey)) {
            titleInternationalItem = new InternationalItem();
            titleInternationalItem.setInternationalParameters(internationalParameters);
            titleInternationalItem.setInternationalKey(titleInternationalKey);
        }
        if (!Strings.isNullOrEmpty(fullContentInternationalKey)) {
            contentInternationalItem = new InternationalItem();
            contentInternationalItem.setInternationalParameters(fullContentInternationalParameters);
            contentInternationalItem.setInternationalKey(fullContentInternationalKey);
        }
        sendNewCRMRecord(crmNotifyService, user, type, receiverIds, senderId, title, fullContent, titleInternationalItem, contentInternationalItem, urlParameter, appId, true);
    }

    public static void sendNewCRMRecord(CRMNotificationService crmNotifyService,
                                        User user,
                                        int type,
                                        List<Integer> receiverIds,
                                        String senderId,
                                        String title,
                                        String fullContent,
                                        InternationalItem titleInternational,
                                        InternationalItem fullContentInternational,
                                        Map<String, String> urlParameter, String appId, boolean remindSender) {
        try {
            NewCrmNotification newCrmNotification = NewCrmNotification.builder()
                    .receiverIDs(Sets.newHashSet(receiverIds))
                    .sourceId(UUID.randomUUID().toString().replace("-", ""))
                    .type(type)
                    .remindSender(remindSender)
                    .build();
            if (!Strings.isNullOrEmpty(senderId)) {
                newCrmNotification.setSenderId(senderId);
            }
            if (urlParameter != null) {
                newCrmNotification.setUrlType(1);
                newCrmNotification.setUrlParameter(urlParameter);
                if(urlParameter.containsKey("objectApiName")){
                    newCrmNotification.setObjectApiName(urlParameter.get("objectApiName"));
                }
                if(urlParameter.containsKey("objectId")){
                    newCrmNotification.setObjectId(urlParameter.get("objectId"));
                }
            }
            if (!Strings.isNullOrEmpty(title)) {
                newCrmNotification.setTitle(title);
            }
            if (!Strings.isNullOrEmpty(fullContent)) {
                newCrmNotification.setFullContent(fullContent);
            }
            if (titleInternational != null) {
                newCrmNotification.setTitleInfo(titleInternational);
            }
            if (fullContentInternational != null) {
                newCrmNotification.setFullContentInfo(fullContentInternational);
            }
            if (!Strings.isNullOrEmpty(appId)) {
                newCrmNotification.setAppId(appId);
            }
            crmNotifyService.sendNewCrmNotification(user, newCrmNotification);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    public static Map<String,String> getUrlParameter(String objectApiName,String objectId){
        Map<String,String> rest = Maps.newHashMap();
        rest.put("objectApiName",objectApiName);
        rest.put("objectId", objectId);
        return rest;
    }
}