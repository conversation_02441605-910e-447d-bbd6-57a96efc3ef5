package com.facishare.crm.sfa.lto.common;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.fxiaoke.paas.gnomon.api.NomonProducer;
import com.fxiaoke.paas.gnomon.api.entity.NomonMessage;

import java.util.Date;

@Component
public class LtoTaskService {
    @Autowired
    private NomonProducer nomonProducer;

    private void send(String biz, String tenantId, String dataId, Date executeTime, String callArg, Integer callQueueMod) {
        NomonMessage message = NomonMessage
                .builder()
                .biz(biz)
                .tenantId(tenantId)
                .dataId(dataId)
                .executeTime(executeTime)
                .callArg(String.format(callArg, tenantId, dataId))
                .callQueueMod(callQueueMod)
                .build();
        nomonProducer.send(message);
    }

    public void createOrUpdateTask(String biz, String tenantId, String dataId, Date executeTime, String callArg, Integer callQueueMod) {
        send(biz, tenantId, dataId, executeTime, callArg, callQueueMod);
    }
}
