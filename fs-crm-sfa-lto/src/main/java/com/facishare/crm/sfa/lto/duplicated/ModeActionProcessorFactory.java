package com.facishare.crm.sfa.lto.duplicated;

import com.facishare.crm.sfa.lto.duplicated.models.DuplicatedModels;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@Slf4j
public class ModeActionProcessorFactory implements ApplicationContextAware {
    private Map<DuplicatedModels.ModeAction, ModeActionProcessor> modeActionProcessorMap = Maps.newHashMap();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        initDuplicatedProcessorMap(applicationContext);
    }

    private void initDuplicatedProcessorMap(ApplicationContext applicationContext) {
        Map<String, ModeActionProcessor> springBeanMap = applicationContext.getBeansOfType(ModeActionProcessor.class);
        springBeanMap.values().forEach(provider -> modeActionProcessorMap.put(provider.getModelAction(), provider));
    }

    public ModeActionProcessor getModeActionProcessor(DuplicatedModels.ModeAction modeAction) {
        return modeActionProcessorMap.getOrDefault(modeAction, null);
    }
}
