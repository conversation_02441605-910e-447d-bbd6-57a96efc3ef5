package com.facishare.crm.sfa.prm.api.channel;

import com.facishare.crm.sfa.prm.api.dto.AgreementStatusRecordDTO;
import com.facishare.crm.sfa.prm.api.enums.SignStatus;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-03-20
 * ============================================================
 */
public interface AgreementStatusRecordService {
    String AGREEMENT_STATUS_RECORD_OBJ = "AgreementStatusRecordObj";
    String CHANGE_BEFORE_STATUS = "change_before_status";
    String CHANGE_AFTER_STATUS = "change_after_status";
    String CHANGE_STATUS_TIME = "change_status_time";
    String CHANGE_RATIONALE = "change_rationale";
    String RELATED_OBJECT_DATA = "related_object_data";
    String RELATED_API_NAMES = "related_api_names";
    String DESCRIBE_API_NAME = "describe_api_name";
    String LAST_MODIFY_TIME = "last_modified_time";
    String ID = "id";
    void record(User user, AgreementStatusRecordDTO statusRecord);
    IObjectData changeSignStatusWithRecord(User user, String rationale, String objectApiName, IObjectData objectData, SignStatus targetStatus);
    IObjectData changeSignStatusWithRecord(User user, String rationale, String objectApiName, IObjectData objectData, SignStatus targetStatus, SignStatus originalSignStatus);
}
