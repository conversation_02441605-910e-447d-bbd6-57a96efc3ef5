package com.facishare.crm.sfa.lto.integral.core.service;

import com.facishare.crm.sfa.lto.integral.core.service.dto.RuleDetail;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;

import java.util.List;
import java.util.Map;

/**
 * crm数据服务类
 *
 * <AUTHOR>
 */
public interface DataLogicService {

    /**
     * 保存单条数据
     *
     * @param objectData
     * @return
     */
    IObjectData saveData(IObjectData objectData);

    /**
     * 查询数据
     *
     * @param tenantId
     * @param objectApiName
     * @param query
     * @return
     */
    List<IObjectData> findBySearchQuery(String tenantId, String objectApiName, SearchTemplateQuery query);

    IObjectData findById(String tenantId, String id, String objectApiName);

    /**
     * 批量更新数据
     *
     * @param objectDataList
     * @param validFieldApiNames
     * @return
     */
    List<IObjectData>  batchUpdateIgnoreOther(List<IObjectData> objectDataList, List<String> validFieldApiNames);

    /**
     * 更新数据并记录日志
     */
    void updateIgnoreOtherAndLog(String tenantId, String userId, String apiName, String ruleName, List<IObjectData> objectDataList, List<String> validFieldApiNames);

    /**
     * 批量保存数据
     *
     * @param objectDataList
     * @return 创建结果
     */
    List<IObjectData> bulkSaveData(List<IObjectData> objectDataList);

    List<IObjectData> bulkSaveDataWithAutoNumber(String tenantId, String userId, List<IObjectData> objectDataList);

    /**
     * 根据引擎计算分数结果，返回结果字段
     */
    Map<String, Object> computeResults(Double score, List<RuleDetail.RuleResult> ruleResults);
}
