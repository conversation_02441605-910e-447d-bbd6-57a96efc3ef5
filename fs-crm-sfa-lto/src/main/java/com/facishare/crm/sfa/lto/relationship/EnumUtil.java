package com.facishare.crm.sfa.lto.relationship;

/**
 * <AUTHOR> lik
 * @date : 2022/7/13 14:48
 */

public class EnumUtil {
    /**
     * dt_team表中member_type字段枚举类
     */
   public enum TeamMemberType{
        /**
         * 员工
         */
        Employee("0"),
        /**
         * 部门
         */
        Circle("2"),
        /**
         * 用户组
         */
        UserGroup("1"),
        /**
         * 用户角色
         */
        UserRole("4");

        private String value;
        TeamMemberType(String _value){
            value = _value;
        }
        public String getValue() {
            return this.value;
        }

        public static TeamMemberType memberTypeOf(String memberType) {
            for (TeamMemberType value : values()) {
                if (value.getValue().equals(memberType)) {
                    return value;
                }
            }
            return null;
        }
    }


    /**
     *
     */
    public enum RelationshipMemberRole{
        /**
         * 负责人
         */
        owner("0"),
        /**
         * 普通成员
         */
        commonMember("1");

        private String value;
        RelationshipMemberRole(String _value){
            value = _value;
        }
        public String getValue() {
            return this.value;
        }

        public static RelationshipMemberRole memberTypeOf(String memberType) {
            for (RelationshipMemberRole value : values()) {
                if (value.getValue().equals(memberType)) {
                    return value;
                }
            }
            return null;
        }
    }

    /**
     * 关系表中relationship_type的类型
     */
    public enum RelationshipType{
        /*** 负责人*/
        owner("0"),
        /*** 相关团队*/
        relatedTeam("1"),
        /***通讯录*/
        addressBook("2");

        private String value;
        RelationshipType(String _value){
            value = _value;
        }
        public String getValue() {
            return this.value;
        }

        public static RelationshipType memberTypeOf(String memberType) {
            for (RelationshipType value : values()) {
                if (value.getValue().equals(memberType)) {
                    return value;
                }
            }
            return null;
        }
    }

    public enum OperationType{
        add("add","新建"),
        edit("edit","编辑"),
        changeOwner("changeOwner","修改负责人"),
        allocate("allocate","分配"),
        choose("choose","领取"),
        bulkReturn("bulkReturn","批量退回"),
        resultOne("resultOne","退回"),
        addTeamMember("addTeamMember","添加相关成员"),
        editTeamMember("editTeamMember","修改相关成员"),
        takeBack("takeBack","收回"),
        transfer("transfer","转换"),
        BulkDelete("BulkDelete","删除"),
        InsertImport("InsertImport","上传保存"),
        updateImport("updateImport","上传更新"),
        BulkAssociate("BulkAssociate","关联"),
        AutoTakeBack("AutoTakeBack","自动收回");
        private String value;
        private String label;
        OperationType(String _value,String _label){
            value = _value;
            label = _label;
        }
        public String getValue() {
            return this.value;
        }

        public static String operationTypeOf(String value) {
            for (OperationType v : values()) {
                if (v.getValue().equals(value)) {
                    return v.label;
                }
            }
            return null;
        }
    }

}
