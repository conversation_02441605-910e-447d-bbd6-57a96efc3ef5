package com.facishare.crm.sfa.lto.accountreerelation;

import com.facishare.crm.sfa.lto.accountreerelation.models.AccountTreeRelationModels.CheckCreateAccountTreeRelationResult;
import com.facishare.crm.sfa.lto.accountreerelation.models.AccountTreeRelationModels.CreateAccountTreeRelationArg;
import com.facishare.crm.sfa.lto.accountreerelation.models.AccountTreeRelationModels.CreateAccountTreeRelationResult;
import com.facishare.crm.sfa.lto.accountreerelation.models.AccountTreeRelationModels.RelateMainData2TreeRelationArg;
import com.facishare.paas.appframework.core.model.User;

public interface ICommonTreeRelationService {
    CreateAccountTreeRelationResult createTreeRelation(User user, CreateAccountTreeRelationArg arg);
    CreateAccountTreeRelationResult createTreeRelationAsync(User user, CreateAccountTreeRelationArg arg);
    CheckCreateAccountTreeRelationResult checkCreateTreeRelation(User user, CreateAccountTreeRelationArg arg);
    CreateAccountTreeRelationResult relateData2TreeRelation(User user, RelateMainData2TreeRelationArg arg);
    CreateAccountTreeRelationResult relateData2TreeRelationAsync(User user, RelateMainData2TreeRelationArg arg);
    CheckCreateAccountTreeRelationResult deleteTreeRelationUniqueData(User user, CreateAccountTreeRelationArg arg);
    CheckCreateAccountTreeRelationResult deleteTreeRelationData(User user, CreateAccountTreeRelationArg arg);
}
