package com.facishare.crm.sfa.lto.qywx.proxy.models;

import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface ConvertCipherIdModel {

	@Data
	@Builder
	class Arg {
		private String fsEa;
		private List<String> plaintextIds; //明文id
		private List<String> openids; // 密文id
	}


	@Data
	class CipherIdMapDto {
		private String corpId;
		private String plaintextId;
		private String openid;
		private Integer type;
	}

}
