package com.facishare.crm.sfa.lto.duplicated;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.lto.common.models.LtoFieldApiConstants;
import com.facishare.crm.sfa.lto.duplicated.models.DuplicatedModels;
import com.facishare.crm.sfa.lto.exception.ExceptionUtil;
import com.facishare.crm.sfa.lto.utils.CommonSqlUtil;
import com.facishare.crm.sfa.lto.utils.ListsUtil;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.ServiceFacadeImpl;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectMappingService;
import com.facishare.paas.appframework.metadata.ObjectMappingServiceImpl;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.foundation.boot.utility.SqlFormatter;
import com.facishare.paas.metadata.api.IObjectMappingRuleInfo;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.CommonSqlOperator;
import com.facishare.paas.metadata.impl.search.WhereParam;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class DuplicatedUtil {
    private static final ObjectMappingService objectMappingService = SpringUtil.getContext().getBean(ObjectMappingServiceImpl.class);
    private static final ServiceFacade serviceFacade = SpringUtil.getContext().getBean(ServiceFacadeImpl.class);

    private DuplicatedUtil() {
        throw new IllegalStateException("Utility class");
    }

    public static List<IObjectMappingRuleInfo> getMappingRuleInfoList(User user, String sourceObjectApiName, String targetObjectApiName) {
        List<IObjectMappingRuleInfo> mappingRuleInfoList = Lists.newArrayList();
        List<String> list1 = Lists.newArrayList(Utils.LEADS_API_NAME, Utils.ACCOUNT_API_NAME);
        List<String> list2 = Lists.newArrayList(Utils.LEADS_API_NAME, Utils.CONTACT_API_NAME);
        String defaultMappingRuleApiName = "";
        if (sourceObjectApiName.equals(targetObjectApiName)) {
            return mappingRuleInfoList;
        } else if (list1.contains(sourceObjectApiName) && list1.contains(targetObjectApiName)) {
            defaultMappingRuleApiName = "rule_leadsobj2accountobj__c";
        } else if (list2.contains(sourceObjectApiName) && list2.contains(targetObjectApiName)) {
            defaultMappingRuleApiName = "rule_leadsobj2contactobj__c";
        }
        if (StringUtils.isEmpty(defaultMappingRuleApiName)) {
            return mappingRuleInfoList;
        }
        mappingRuleInfoList = objectMappingService.findByApiName(user, defaultMappingRuleApiName);
        String ruleApiName = defaultMappingRuleApiName;
        mappingRuleInfoList = mappingRuleInfoList.stream().filter(x -> ruleApiName.equals(x.getRuleApiName())).collect(Collectors.toList());
        return mappingRuleInfoList;
    }

    public static List<Map> doSelect(User user, String tableName, Map<String, Object> whereMap) {
        List<Map> result = Lists.newArrayList();
        if (MapUtils.isEmpty(whereMap)) {
            return result;
        }
        try {
            String tenantId = user.getTenantId();
            List<WhereParam> whereParamList = getWhereParamList(tenantId, whereMap);
            result = CommonSqlUtil.queryData(tenantId, tableName, whereParamList);
        } catch (MetadataServiceException ex) {
            log.error("select data by metadata service error", ex);
            throw new MetaDataException(ex.getMessage());
        }
        return result;
    }

    public static int doDelete(User user, String tableName, Map<String, Object> whereMap) {
        int result;
        try {
            List<WhereParam> whereParamList = getWhereParamList(user.getTenantId(), whereMap);
            result = CommonSqlUtil.deleteData(user.getTenantId(), tableName, whereParamList);
        } catch (MetadataServiceException ex) {
            log.error("delete data by metadata service error", ex);
            throw new MetaDataException(ex.getMessage());
        }
        return result;
    }

    private static List<WhereParam> getWhereParamList(String tenantId, Map<String, Object> map) {
        List<WhereParam> result = Lists.newArrayList();
        WhereParam param = new WhereParam();
        param.setColumn(LtoFieldApiConstants.TENANT_ID);
        param.setOperator(CommonSqlOperator.EQ);
        param.setValue(Lists.newArrayList(tenantId));
        result.add(param);
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (StringUtils.isEmpty(entry.getKey())) {
                continue;
            }
            if (entry.getValue() instanceof List) {
                List<Object> value = (List<Object>) entry.getValue();
                param = new WhereParam();
                param.setColumn(entry.getKey());
                param.setOperator(CommonSqlOperator.IN);
                param.setValue(value);
                result.add(param);
            } else {
                param = new WhereParam();
                param.setColumn(entry.getKey());
                param.setOperator(CommonSqlOperator.EQ);
                param.setValue(Lists.newArrayList(entry.getValue()));
                result.add(param);
            }
        }
        return result;
    }

    public static List<Map> select(String tenantId, DuplicatedModels.SearchTemplate searchTemplate) {
        if (null == searchTemplate) {
            log.warn("Entering select: ObjectFieldReference SqlSearchTemplate is null.");
            return Lists.newArrayList();
        } else {
            String tableName = searchTemplate.getTableName();
            if (com.google.common.base.Strings.isNullOrEmpty(tableName)) {
                log.warn("Entering select: tableName can't be null or empty.");
                throw new ValidateException("tableName can't be null or empty.");
            } else {
                StringBuilder selectSql = new StringBuilder();
                Boolean needCountNum = searchTemplate.getNeedCountNum();
                if (BooleanUtils.isTrue(needCountNum)) {
                    selectSql.append("select  count(*)  from ").append(tableName).append(" ");
                } else {
                    selectSql.append("select * from ").append(tableName).append(" ");
                }
                List<DuplicatedModels.WhereParam> whereParamList = searchTemplate.getWhereParams();
                if (CollectionUtils.isEmpty(whereParamList)) {
                    whereParamList = Lists.newArrayList();
                }
                if (whereParamList.stream().noneMatch(w -> LtoFieldApiConstants.TENANT_ID.equals(w.getFieldName()))) {
                    whereParamList.add(getWhereParam(LtoFieldApiConstants.TENANT_ID, DuplicatedModels.SqlOperator.EQ, Lists.newArrayList(tenantId)));
                }
                if (whereParamList.stream().noneMatch(w -> LtoFieldApiConstants.IS_DELETED.equals(w.getFieldName()))) {
                    whereParamList.add(getWhereParam(LtoFieldApiConstants.IS_DELETED, DuplicatedModels.SqlOperator.NEQ, Lists.newArrayList(1)));
                }
                if (!CollectionUtils.isEmpty(whereParamList)) {
                    StringBuilder whereSql = new StringBuilder();
                    whereSql.append(" where ");
                    Iterator var3 = whereParamList.iterator();
                    while (var3.hasNext()) {
                        DuplicatedModels.WhereParam whereParam =
                                (DuplicatedModels.WhereParam) var3.next();
                        getWherePartSql(whereSql, whereParam);
                        whereSql.append(" and ");
                    }

                    int len = whereSql.length();
                    whereSql.delete(len - 4, len);
                    selectSql.append(whereSql).append(" ");
                }

                if (BooleanUtils.isNotTrue(needCountNum)) {
                    List<DuplicatedModels.OrderBy> orderByList = searchTemplate.getOrders();
                    if (CollectionUtils.isEmpty(orderByList)) {
                        orderByList = Lists.newArrayList();
                        DuplicatedModels.OrderBy orderBy = new DuplicatedModels.OrderBy();
                        orderBy.setFieldName("create_time");
                        orderBy.setIsAsc(true);
                        orderByList.add(orderBy);
                    }
                    if (!CollectionUtils.isEmpty(orderByList)) {
                        StringBuilder orderBySql = new StringBuilder();
                        orderBySql.append(" order by ");
                        Iterator var19 = orderByList.iterator();

                        while (var19.hasNext()) {
                            DuplicatedModels.OrderBy orderBy = (DuplicatedModels.OrderBy) var19.next();
                            String fieldName = orderBy.getFieldName();
                            orderBySql.append(fieldName);
                            if (Boolean.TRUE.equals(orderBy.getIsAsc())) {
                                orderBySql.append(" asc ,");
                            } else {
                                orderBySql.append(" desc ,");
                            }
                        }

                        orderBySql.deleteCharAt(orderBySql.length() - 1);
                        selectSql.append(orderBySql).append(" ");
                    }

                    int limit = searchTemplate.getLimit();
                    if (limit != 0) {
                        selectSql.append(" limit ").append(limit).append(" offset ").append(searchTemplate.getOffset());
                    }
                }

                String sql = SqlFormatter.format(String.valueOf(selectSql));
                log.debug("Entering select : sql :{}", sql);
                return CommonSqlUtil.findBySql(tenantId, sql);
            }
        }
    }

    public static DuplicatedModels.WhereParam getWhereParam(String fieldName, DuplicatedModels.SqlOperator sqlOperator, List<Object> values) {
        DuplicatedModels.WhereParam whereParam = new DuplicatedModels.WhereParam();
        whereParam.setFieldName(fieldName);
        whereParam.setValues(values);
        whereParam.setOperator(sqlOperator.toString());
        return whereParam;
    }

    private static void getWherePartSql(StringBuilder whereSql, DuplicatedModels.WhereParam whereParam) {
        DuplicatedModels.SqlOperator sqlOperator = DuplicatedModels.SqlOperator.valueOf(whereParam.getOperator());
        if (DuplicatedModels.SqlOperator.EQ.equals(sqlOperator) || DuplicatedModels.SqlOperator.NEQ.equals(sqlOperator)
                || DuplicatedModels.SqlOperator.LIKE.equals(sqlOperator)) {
            String fieldName = whereParam.getFieldName();
            String operator = DuplicatedModels.SqlOperator.findSQLOperator(sqlOperator);
            List<Object> valueList = whereParam.getValues();
            Object obj;
            if (CollectionUtils.isEmpty(valueList)) {
                //等于，模糊匹配 空数组时，相当于没条件
                whereSql.append(" 1=1 ");
                return;
            }
            if (valueList.size() == 1) {
                obj = valueList.get(0);
                if (null == obj) {
                    whereSql.append(fieldName).append(" is null ");
                    return;
                }
                if (DuplicatedModels.SqlOperator.LIKE.equals(sqlOperator)) {
                    String value = String.format("'%%%s%%'", obj);
                    whereSql.append(fieldName).append(operator).append(value);
                    return;
                }
                if (!(obj instanceof Number) && !(obj instanceof Boolean)) {
                    whereSql.append(fieldName).append(operator).append("'").append(StringEscapeUtils.escapeSql(String.valueOf(obj))).append("'");
                    return;
                }

                whereSql.append(fieldName).append(operator).append(StringEscapeUtils.escapeSql(String.valueOf(obj)));
                return;
            }

            if (valueList.size() > 1) {
                obj = valueList.get(0);
                if (DuplicatedModels.SqlOperator.LIKE.equals(sqlOperator)) {
                    whereSql.append(fieldName).append(operator).append(StringEscapeUtils.escapeSql("%" + obj + "%"));
                    return;
                }
                if (obj instanceof Number) {
                    whereSql.append(fieldName).append(operator).append("array").append(StringEscapeUtils.escapeSql(String.valueOf(valueList)));
                    return;
                }

                if (obj instanceof String) {
                    whereSql.append(fieldName).append(operator).append("array[");
                    Iterator var7 = valueList.iterator();

                    while (var7.hasNext()) {
                        Object object = var7.next();
                        whereSql.append("'").append(StringEscapeUtils.escapeSql(String.valueOf(object))).append("' ,");
                    }

                    whereSql.deleteCharAt(whereSql.length() - 1);
                    whereSql.append("] ");
                }
            }
        }
    }

    public static int doInsert(User user, String tableName, List<Map<String, Object>> mapList, int batchSize) {
        int result = 0;
        List<List<Map<String, Object>>> splitList = ListsUtil.splitList(mapList, batchSize);
        for (List<Map<String, Object>> dataList : splitList) {
            try {
                for (Map dataMap : mapList) {
                    setCommonInsertFields(user, dataMap);
                }
                int tempResult = CommonSqlUtil.insertData(user.getTenantId(), tableName, dataList);
                result += tempResult;
            } catch (MetadataServiceException ex) {
                log.error("insert data by metadata service error", ex);
                ExceptionUtil.throwCommonBusinessException();
            }
        }
        return result;
    }

    private static void setCommonInsertFields(User user, Map<String, Object> dataMap) {
        Long currentTime = System.currentTimeMillis();
        String id = serviceFacade.generateId();
        dataMap.put("id", id);
        if (dataMap.get("name") == null) {
            dataMap.put("name", id);
        }
        dataMap.put(LtoFieldApiConstants.TENANT_ID, user.getTenantId());
        dataMap.put("created_by", user.getUpstreamOwnerIdOrUserId());
        dataMap.put("create_time", currentTime);
        dataMap.put("last_modified_by", user.getUpstreamOwnerIdOrUserId());
        dataMap.put("last_modified_time", currentTime);
        dataMap.put(LtoFieldApiConstants.IS_DELETED, 0);
        dataMap.put("record_type", "default__c");
    }
}