package com.facishare.crm.sfa.lto.duplicated;

import com.facishare.crm.sfa.lto.duplicated.models.DuplicatedModels;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;

public interface ModeActionProcessor {
    DuplicatedModels.ModeAction getModelAction();
    void process(User user, DuplicatedModels.DuplicatedProcessingMode processingMode,
                                           IObjectData freshData, List<IObjectData> duplicatedDataList,
                                           DuplicatedModels.TriggerAction triggerAction);
}
