package com.facishare.crm.sfa.lto.integral.core.rest.dto;

import com.facishare.crm.sfa.lto.integral.core.service.dto.FindRuleList;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Objects;

public interface GetRuleList {

    @EqualsAndHashCode(callSuper = true)
	@Data
    class Arg extends BaseEngine.Arg {
        @SerializedName("apiName")
        private String apiName = "";
        @SerializedName("entityId")
        private String entityId;
        @SerializedName("name")
        private String name;
        @SerializedName("pageInfo")
        private PageInfo pageInfo;

        public static Arg build(FindRuleList.Arg arg, RequestContext requestContext){
            Arg request = new Arg();
            if (Objects.isNull(arg) || Objects.isNull(requestContext)) {
                return request;
            }
            FindRuleList.PageInfo info = arg.getPageInfo();
            request.setEntityId(info.getObjectDescribeApiName());
            //处理"/"转义符号问题
            if (Objects.nonNull(info.getLabel())) {
                String label = info.getLabel().replaceAll("\\\\", "\\\\\\\\");
                request.setName(label);
            }

            PageInfo pageInfo = new PageInfo();
            pageInfo.setCurrentPage(info.getPageNumber());
            pageInfo.setPageSize(info.getLimit());
            request.setPageInfo(pageInfo);

            BaseEngine.Context context = new BaseEngine.Context();
            context.setTenantId(requestContext.getTenantId());
            context.setUserId(requestContext.getUser().getUserId());
            request.setContext(context);

            return request;
        }
    }

    @Data
    class PageInfo {
        @SerializedName("currentPage")
        private Integer currentPage;
        @SerializedName("pageSize")
        private Integer pageSize;
        @SerializedName("total")
        private Integer total = 0;
        @SerializedName("totalPage")
        private Integer totalPage = 0;
    }

    @EqualsAndHashCode(callSuper = true)
	@Data
    class Result extends BaseEngine.Result<resultData>{
    }

    @Data
    class resultData {
        @SerializedName("pageInfo")
        private RuleListPageInfo pageInfo;
        @SerializedName("content")
        private List<RuleListInfo> content;
    }


    @Data
    class RuleListPageInfo {
        private Integer total;
        private Integer pageSize;
        private Integer currentPage;
        private Integer totalPage;
    }

    @Data
    class RuleListInfo {
        private String id;
        private String tenantId;
        private String appId;
        @SerializedName("entityId")
        private String objectApiName;
        private String scene;
        @SerializedName("apiName")
        private String ruleApiName;
        private String name;
        /** 规则状态: 1启用，0停用*/
        private Integer status;
        private String remark;
        @SerializedName("createdBy")
        private String createdUserId;
        private Long createTime;
        @SerializedName("lastModifiedBy")
        private String lastModifiedUserId;
        private Long lastModifiedTime;
        private Integer isDeleted;
        @SerializedName("result")
        private String resultRules;
    }

}
