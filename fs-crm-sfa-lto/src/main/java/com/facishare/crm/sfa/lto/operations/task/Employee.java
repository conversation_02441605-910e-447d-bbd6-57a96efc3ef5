package com.facishare.crm.sfa.lto.operations.task;

import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;
import java.util.function.BiConsumer;

class Employee implements TemplateMemberField {

    private final List<String> raw;
    private final BiConsumer<OperationsTask, List<String>> setter;

    Employee(List<String> raw, BiConsumer<OperationsTask, List<String>> setter) {
        this.raw = raw;
        this.setter = setter;
    }

    @Override
    public List<String> getMember(IObjectData bizData) {
        return raw;
    }

    @Override
    public BiConsumer<OperationsTask, List<String>> setter() {
        return setter;
    }
}
