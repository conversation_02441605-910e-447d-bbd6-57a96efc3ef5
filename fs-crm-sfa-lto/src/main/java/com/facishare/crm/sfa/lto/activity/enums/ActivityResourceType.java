package com.facishare.crm.sfa.lto.activity.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

@Getter
public enum ActivityResourceType {
    RECORDING("1", "interaction_recording_duration_limit"), // 录音时长
    RECORDING_TRANSLATION("2", "real_time_translation_in_recording_limit"); // 录音中实时翻译

    private final String value;
    private final String paraKey;

    ActivityResourceType(String value, String paraKey) {
        this.value = value;
        this.paraKey = paraKey;
    }

    public static Optional<ActivityResourceType> valueFrom(String value) {
        return Arrays.stream(ActivityResourceType.values()).filter(t -> t.value.equals(value)).findFirst();
    }
}
