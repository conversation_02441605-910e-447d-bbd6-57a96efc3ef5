package com.facishare.crm.sfa.lto.integral.core.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface CreateRuleOrUpdate {
    @Data
    class Arg {
        @JSONField(name = "api_name")
		@JsonProperty("api_name")
        private String apiName;
        private String label;
        @JSONField(name = "obj_api_name")
		@JsonProperty("obj_api_name")
        private String objectApiName;
        private String remark;
        @JSONField(name = "is_active")
		@JsonProperty("is_active")
        private boolean active;
        @JSONField(name = "rule_items")
		@JsonProperty("rule_items")
        private List<RuleDetail.RuleItem> ruleItems;
        @J<PERSON><PERSON>ield(name = "rule_results")
		@JsonProperty("rule_results")
        private List<RuleDetail.RuleResult> ruleResults;
    }


    @Data
    @Builder
    class Result {
        private boolean success;
    }
}
