package com.facishare.crm.sfa.prm.model;

import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024-06-29 17:28
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EmailMessageContent extends CommonMessageContent {
    private String subject;
    private String templateId;
    private String objectDataId;
    private List<String> attachments;
    private List<String> userIdList;
}
