package com.facishare.crm.sfa.lto.duplicated;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.lto.common.models.LtoFieldApiConstants;
import com.facishare.crm.sfa.lto.duplicated.models.DuplicatedModels;
import com.facishare.crm.sfa.lto.utils.CommonUtil;
import com.facishare.crm.sfa.lto.utils.GrayUtil;
import com.facishare.crm.sfa.lto.utils.ListsUtil;
import com.facishare.crm.sfa.lto.utils.ObjectDataUtil;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.lto.duplicated.models.DuplicatedConstants.*;

@Component
@Slf4j
public class ClearDuplicatedProcessor extends AbstractDuplicatedDataProcessor {
    @Override
    public String getProcessorModel() {
        return "CLEAR_DUPLICATED_PROCESSOR";
    }

    @Override
    public boolean matchProcessor(DuplicatedModels.TriggerAction triggerAction) {
        return DuplicatedModels.TriggerAction.INVALID.equals(triggerAction)
                || DuplicatedModels.TriggerAction.CLEAR_DUPLICATED.equals(triggerAction);
    }

    @Override
    public void process(DuplicatedDataProcessor.DuplicatedDataProcessArg processArg) {
        List<IObjectData> objectDataList = processArg.getDataList();
        if (CollectionUtils.isEmpty(objectDataList)) {
            return;
        }
        List<IObjectData> needChangeList = objectDataList.stream()
                .filter(m -> ObjectDataUtil.getBooleanValue(m, "is_duplicated", false))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(needChangeList)) {
            return;
        }

        User user = processArg.getUser();
        objectDataList.forEach(objectData -> {
            if(skipLeadsDuplicated(user.getTenantId())) {
                return;
            }
            clearDuplicated(user, objectData);
            clearDuplicatedPerformance(user, objectData);
        });
    }

    private void clearDuplicated(User user, IObjectData objectData) {
        String currentDataId = objectData.getId();
        Map<String, Object> wheres = Maps.newHashMap();
        wheres.put(LtoFieldApiConstants.OBJECT_API_NAME, Utils.LEADS_API_NAME);
        wheres.put(LtoFieldApiConstants.OBJECT_ID, currentDataId);
        List<Map> currentDataMaps = DuplicatedUtil.doSelect(user, BIZ_DUPLICATED_PROCESSING_RELATION_TABLE, wheres);
        wheres = Maps.newHashMap();
        wheres.put(LtoFieldApiConstants.OBJECT_API_NAME, Utils.LEADS_API_NAME);
        wheres.put(EXTEND_OBJ_DATA_ID, currentDataId);
        List<Map> extendDataMaps = DuplicatedUtil.doSelect(user, BIZ_DUPLICATED_PROCESSING_RELATION_TABLE, wheres);
        if (CollectionUtils.isEmpty(currentDataMaps) && CollectionUtils.isEmpty(extendDataMaps)) {
            updateDuplicatedField(user, false, Lists.newArrayList(currentDataId));
            return;
        }
        //关系id
        Set<String> relationIdList = Sets.newHashSet();
        //与当前数据重复的数据id
        Set<String> extendObjDataIdList = Sets.newHashSet();
        if (CollectionUtils.isNotEmpty(currentDataMaps)) {
            relationIdList = currentDataMaps.stream().filter(x -> Objects.isNull(x.get(DUPLICATED_GROUP_ID)) || StringUtils.isEmpty(x.get(DUPLICATED_GROUP_ID).toString())).map(m -> m.getOrDefault(LtoFieldApiConstants.ID, "").toString()).collect(Collectors.toSet());
            extendObjDataIdList = currentDataMaps.stream().filter(x -> Objects.isNull(x.get(DUPLICATED_GROUP_ID)) || StringUtils.isEmpty(x.get(DUPLICATED_GROUP_ID).toString())).map(m -> m.getOrDefault(EXTEND_OBJ_DATA_ID, "").toString()).collect(Collectors.toSet());
        }
        if (CollectionUtils.isNotEmpty(extendDataMaps)) {
            relationIdList.addAll(extendDataMaps.stream().filter(x -> Objects.isNull(x.get(DUPLICATED_GROUP_ID)) || StringUtils.isEmpty(x.get(DUPLICATED_GROUP_ID).toString())).map(m -> m.getOrDefault(LtoFieldApiConstants.ID, "").toString()).collect(Collectors.toSet()));
            extendObjDataIdList.addAll(extendDataMaps.stream().filter(x -> Objects.isNull(x.get(DUPLICATED_GROUP_ID)) || StringUtils.isEmpty(x.get(DUPLICATED_GROUP_ID).toString())).map(m -> m.getOrDefault(LtoFieldApiConstants.OBJECT_ID, "").toString()).collect(Collectors.toSet()));
        }
        if (CollectionUtils.isEmpty(relationIdList) && CollectionUtils.isEmpty(extendObjDataIdList)) {
            updateDuplicatedField(user, false, Lists.newArrayList(currentDataId));
            return;
        }

        //删除与当前节点关联的节点
        deleteProcessingRelation(user, relationIdList);

        if (CollectionUtils.isEmpty(extendObjDataIdList)) {
            updateDuplicatedField(user, false, Lists.newArrayList(currentDataId));
            return;
        }
        extendObjDataIdList.remove(currentDataId);
        if (CollectionUtils.isEmpty(extendObjDataIdList)) {
            updateDuplicatedField(user, false, Lists.newArrayList(currentDataId));
            return;
        }

        wheres = Maps.newHashMap();
        wheres.put(LtoFieldApiConstants.OBJECT_API_NAME, Utils.LEADS_API_NAME);
        wheres.put(LtoFieldApiConstants.OBJECT_ID, Lists.newArrayList(extendObjDataIdList));
        List<Map> otherDataMaps = DuplicatedUtil.doSelect(user, BIZ_DUPLICATED_PROCESSING_RELATION_TABLE, wheres);
        wheres = Maps.newHashMap();
        wheres.put(LtoFieldApiConstants.OBJECT_API_NAME, Utils.LEADS_API_NAME);
        wheres.put(EXTEND_OBJ_DATA_ID, Lists.newArrayList(extendObjDataIdList));
        List<Map> otherExtendDataMaps = DuplicatedUtil.doSelect(user, BIZ_DUPLICATED_PROCESSING_RELATION_TABLE, wheres);
        if (CollectionUtils.isEmpty(otherDataMaps) && CollectionUtils.isEmpty(otherExtendDataMaps)) {
            extendObjDataIdList.add(currentDataId);
            updateDuplicatedField(user, false, Lists.newArrayList(extendObjDataIdList));
            return;
        }

        Set<String> needChangeIDSet = Sets.newHashSet(currentDataId);
        Set<String> otherRelationIdSet = Sets.newHashSet();
        extendObjDataIdList.forEach(k -> {
            if(skipLeadsDuplicated(user.getTenantId())) {
                return;
            }
            List<Map> currentOtherDataMap = Lists.newArrayList();
            List<Map> currentOtherExtendDataMap = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(otherDataMaps)) {
                currentOtherDataMap = otherDataMaps.stream().filter(n -> Objects.equals(k, n.getOrDefault(LtoFieldApiConstants.OBJECT_ID, ""))).collect(Collectors.toList());
            }
            if (CollectionUtils.isNotEmpty(otherDataMaps)) {
                currentOtherExtendDataMap = otherExtendDataMaps.stream().filter(n -> Objects.equals(k, n.getOrDefault(EXTEND_OBJ_DATA_ID, ""))).collect(Collectors.toList());
            }
            if (isNeedClear(currentDataId, currentOtherDataMap, currentOtherExtendDataMap)) {
                needChangeIDSet.add(k);
                otherRelationIdSet.addAll(currentOtherDataMap.stream().map(m -> m.getOrDefault(LtoFieldApiConstants.ID, "").toString()).collect(Collectors.toSet()));
                otherRelationIdSet.addAll(currentOtherExtendDataMap.stream().map(m -> m.getOrDefault(LtoFieldApiConstants.ID, "").toString()).collect(Collectors.toSet()));
            }
        });
        deleteProcessingRelation(user, otherRelationIdSet);
        updateDuplicatedField(user, false, Lists.newArrayList(needChangeIDSet));
    }

    private boolean isNeedClear(String currentDataId, List<Map> currentOtherDataMap, List<Map> currentOtherExtendDataMap) {
        if (currentOtherDataMap.stream().anyMatch(m -> !ObjectDataUtil.getStringValue(m, LtoFieldApiConstants.OBJECT_ID, "").equals(ObjectDataUtil.getStringValue(m, EXTEND_OBJ_DATA_ID, ""))
                && !Objects.equals(currentDataId, ObjectDataUtil.getStringValue(m, EXTEND_OBJ_DATA_ID, "")))) {
            return false;
        }
        if (currentOtherExtendDataMap.stream().anyMatch(m -> !ObjectDataUtil.getStringValue(m,LtoFieldApiConstants.OBJECT_ID, "").equals(ObjectDataUtil.getStringValue(m, EXTEND_OBJ_DATA_ID, ""))
                && !currentDataId.equals(ObjectDataUtil.getStringValue(m,LtoFieldApiConstants.OBJECT_ID, "")))) {
            return false;
        }
        return true;
    }

    private void clearDuplicatedPerformance(User user, IObjectData curObjectData) {
        String leadsId = curObjectData.getId();
        List<Map> duplicatedGroupList = getExtendObjectIds(user.getTenantId(), Utils.LEADS_API_NAME, Lists.newArrayList(leadsId));
        if(CollectionUtils.isEmpty(duplicatedGroupList)) {
            updateDuplicatedField(user, false, Lists.newArrayList(leadsId));
            return;
        }
        List<String> needUpdateDataIdList = Lists.newArrayList(leadsId);
        List<String> checkedDataIdList = Lists.newArrayList();
        List<String> duplicatedProcessingIds = duplicatedGroupList.stream().map(x -> String.valueOf(x.get(DUPLICATED_PROCESSING_ID))).distinct().collect(Collectors.toList());
        List<DuplicatedModels.DuplicatedProcessing> duplicatedProcessingList = duplicatedProcessingRuleService.getDuplicatedProcessingList(user, true);
        List<DuplicatedModels.DuplicatedProcessing> needCheckProcessingList = duplicatedProcessingList.stream().filter(x -> duplicatedProcessingIds.contains(x.getId())).collect(Collectors.toList());
        Map<String, Boolean> needProcessMap = isNeedProcess(user, curObjectData, needCheckProcessingList);
        for(String duplicatedProcessingId : duplicatedProcessingIds) {
            if(skipLeadsDuplicated(user.getTenantId())) {
                return;
            }
            List<String> duplicateGroupIds = duplicatedGroupList.stream().filter(x -> duplicatedProcessingId.equals(x.get(DUPLICATED_PROCESSING_ID))).map(x -> x.get(DUPLICATED_GROUP_ID).toString()).collect(Collectors.toList());
            if(StringUtils.isBlank(duplicatedProcessingId) ||
                    (needProcessMap != null && needProcessMap.containsKey(duplicatedProcessingId) && Boolean.FALSE.equals(needProcessMap.get(duplicatedProcessingId)))) {
                continue;
            }
            try {
                String duplicateGroupIdString = CommonUtil.buildSqlInString(duplicateGroupIds);
                String queryString = String.format("SELECT DISTINCT object_id FROM biz_duplicated_processing_relation WHERE tenant_id='%s' " +
                                "AND object_api_name='%s' AND duplicated_group_id=any(array[%s]) AND duplicated_processing_id='%s' AND object_id<>'%s' AND is_deleted=0 ",
                        user.getTenantId(), Utils.LEADS_API_NAME, duplicateGroupIdString, duplicatedProcessingId, leadsId);
                List<Map> queryResult = objectDataService.findBySql(user.getTenantId(), queryString);
                if(CollectionUtils.isEmpty(queryResult)) {
                    deleteProcessingRelation(user, duplicatedProcessingId, duplicateGroupIds);
                    continue;
                }
                if(queryResult.size() > 5000) {
                    log.warn("duplicated data too large,tenantId:{},groupId:{},duplicatedProcessingId:{},leadsID:{}", user.getTenantId(), duplicateGroupIds.get(0), duplicatedProcessingId, leadsId);
                    continue;
                }
                deleteProcessingRelation(user, duplicatedProcessingId, duplicateGroupIds, leadsId);
                List<String> allDataIds = queryResult.stream().map(x -> String.valueOf(x.get(LtoFieldApiConstants.OBJECT_ID))).collect(Collectors.toList());
                List<String> duplicatedDataIdList = getDuplicatedDataListByDataIds(user, duplicatedProcessingId, duplicatedProcessingList, allDataIds);
                duplicatedDataIdList.remove(leadsId);
                if(CollectionUtils.isNotEmpty(duplicatedDataIdList)) {
                    checkedDataIdList.addAll(duplicatedDataIdList);
                    allDataIds.removeIf(duplicatedDataIdList::contains);
                    if(CollectionUtils.isNotEmpty(allDataIds)) {
                        deleteProcessingRelation(user, duplicatedProcessingId, duplicateGroupIds, allDataIds);
                    }
                }
                for(String objectId : allDataIds) {
                    if(skipLeadsDuplicated(user.getTenantId())) {
                        return;
                    }
                    if(checkedDataIdList.contains(objectId)) {
                        continue;
                    }
                    List<Map> objectDuplicatedGroupList = getExtendObjectIds(user.getTenantId(), Utils.LEADS_API_NAME, Lists.newArrayList(objectId));
                    if(objectDuplicatedGroupList.stream().anyMatch(x -> !duplicatedProcessingIds.contains(String.valueOf(x.get(DUPLICATED_PROCESSING_ID))))) {
                        checkedDataIdList.add(objectId);
                        continue;
                    }
                    List<String> processingIds = objectDuplicatedGroupList.stream().map(x -> String.valueOf(x.get(DUPLICATED_PROCESSING_ID))).collect(Collectors.toList());
                    boolean objectDuplicated = false;
                    for(String processId : processingIds) {
                        if(skipLeadsDuplicated(user.getTenantId())) {
                            return;
                        }
                        List<IObjectData> duplicateDataList = getDuplicatedDataListByProcessingId(user, processId, duplicatedProcessingList, objectId, 20);
                        if(CollectionUtils.isEmpty(duplicateDataList) || (duplicateDataList.size() == 1 && leadsId.equals(duplicateDataList.get(0).getId()))) {
                            List<String> tempDuplicateGroupIds = objectDuplicatedGroupList.stream().filter(x -> processId.equals(String.valueOf(x.get(DUPLICATED_PROCESSING_ID)))).map(x -> x.get(DUPLICATED_GROUP_ID).toString()).collect(Collectors.toList());
                            deleteProcessingRelation(user, processId, tempDuplicateGroupIds, objectId);
                        } else {
                            objectDuplicated = true;
                        }
                    }
                    if(!objectDuplicated) {
                        needUpdateDataIdList.add(objectId);
                    }
                    checkedDataIdList.add(objectId);
                }
            } catch (Exception e) {
                log.error("DuplicatedProcessingService getExtendObjectIds error", e);
                throw new MetaDataException(e.getMessage());
            }
        }
        updateDuplicatedField(user, false, needUpdateDataIdList);
    }

    private List<Map> getExtendObjectIds(String tenantId, String objectApiName, List<String> dataIds) {
        if(CollectionUtils.isEmpty(dataIds)) {
            return Lists.newArrayList();
        }
        try {
            List<Map> result = Lists.newArrayList();
            List<List<String>> splitList = ListsUtil.splitList(dataIds, batchSize);
            for(List<String> splitDataIds : splitList) {
                String dataIdString = CommonUtil.buildSqlInString(splitDataIds);
                String queryString = String.format("SELECT DISTINCT duplicated_group_id, duplicated_processing_id FROM biz_duplicated_processing_relation WHERE tenant_id='%s' " +
                                "AND object_id=any(array[%s]) AND object_api_name='%s' AND is_deleted=0 AND duplicated_group_id IS NOT NULL AND duplicated_group_id<>'' ",
                        tenantId, dataIdString, objectApiName);
                List<Map> queryResult = objectDataService.findBySql(tenantId, queryString);
                if(CollectionUtils.isNotEmpty(queryResult)) {
                    result.addAll(queryResult);
                }
            }
            return result;
        } catch (Exception e) {
            log.error("DuplicatedProcessingService getExtendObjectIds error", e);
            throw new MetaDataException(e.getMessage());
        }
    }

    private Map<String, Boolean> isNeedProcess(User user, IObjectData objectData, List<DuplicatedModels.DuplicatedProcessing> processingRuleList) {
        Map<String, Boolean> result = Maps.newHashMap();
        processingRuleList.forEach(x -> result.put(x.getId(), true));
        if(ObjectDataExt.of(objectData).isInvalid() || "invalid".equals(objectData.get(LtoFieldApiConstants.LIFE_STATUS))) {
            return result;
        }
        Map<String, List<IObjectData>> matchMaps = ruleMatcher.matches(user.getTenantId(), Utils.LEADS_API_NAME, processingRuleList, Lists.newArrayList(objectData));
        List<String> needCheckProcessingIds = Lists.newArrayList();
        for(Map.Entry<String, List<IObjectData>> entry : matchMaps.entrySet()) {
            if(CollectionUtils.isNotEmpty(entry.getValue())) {
                needCheckProcessingIds.add(entry.getKey());
            }
        }
        if(CollectionUtils.isEmpty(needCheckProcessingIds)) {
            return result;
        }
        Map<String, Boolean> checkResult = getNeedProcessByData(user, needCheckProcessingIds, objectData);
        for(Map.Entry<String, Boolean> entry : checkResult.entrySet()) {
            result.put(entry.getKey(), entry.getValue());
        }

        return result;
    }

    private void deleteProcessingRelation(User user, String duplicatedProcessingId, List<String> duplicatedGroupIds, List<String> deletedObjectIds) {
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put(LtoFieldApiConstants.OBJECT_API_NAME, Utils.LEADS_API_NAME);
        dataMap.put(DUPLICATED_PROCESSING_ID, duplicatedProcessingId);
        dataMap.put(DUPLICATED_GROUP_ID, duplicatedGroupIds);
        dataMap.put(LtoFieldApiConstants.OBJECT_ID, deletedObjectIds);
        DuplicatedUtil.doDelete(user, BIZ_DUPLICATED_PROCESSING_RELATION_TABLE, dataMap);
    }

    private void deleteProcessingRelation(User user, String duplicatedProcessingId, List<String> duplicatedGroupIds) {
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put(LtoFieldApiConstants.OBJECT_API_NAME, Utils.LEADS_API_NAME);
        dataMap.put(DUPLICATED_PROCESSING_ID, duplicatedProcessingId);
        dataMap.put(DUPLICATED_GROUP_ID, duplicatedGroupIds);
        DuplicatedUtil.doDelete(user, BIZ_DUPLICATED_PROCESSING_RELATION_TABLE, dataMap);
    }

    private void deleteProcessingRelation(User user, String duplicatedProcessingId, List<String> duplicatedGroupIds, String objectId) {
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put(LtoFieldApiConstants.OBJECT_API_NAME, Utils.LEADS_API_NAME);
        dataMap.put(DUPLICATED_PROCESSING_ID, duplicatedProcessingId);
        dataMap.put(DUPLICATED_GROUP_ID, duplicatedGroupIds);
        dataMap.put(LtoFieldApiConstants.OBJECT_ID, objectId);
        DuplicatedUtil.doDelete(user, BIZ_DUPLICATED_PROCESSING_RELATION_TABLE, dataMap);
    }

    private void deleteProcessingRelation(User user, Set<String> relationIdSet) {
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put(LtoFieldApiConstants.OBJECT_API_NAME, Utils.LEADS_API_NAME);
        dataMap.put(LtoFieldApiConstants.ID, Lists.newArrayList(relationIdSet));
        DuplicatedUtil.doDelete(user, BIZ_DUPLICATED_PROCESSING_RELATION_TABLE, dataMap);
    }

    private List<String> getDuplicatedDataListByDataIds (User user, String processingId,
                                                         List<DuplicatedModels.DuplicatedProcessing> duplicatedProcessingList,
                                                         List<String> dataIds) {
        if(CollectionUtils.isEmpty(dataIds) || CollectionUtils.isEmpty(duplicatedProcessingList)) {
            return Lists.newArrayList();
        }
        Optional<DuplicatedModels.DuplicatedProcessing> opProcessingRule = duplicatedProcessingList.stream()
                .filter(x -> processingId.equals(x.getId())).findFirst();
        if(!opProcessingRule.isPresent()) {
            return Lists.newArrayList();
        }
        List<List<String>> splitList = ListsUtil.splitList(dataIds, batchSize);
        DuplicatedModels.DuplicatedProcessing duplicatedProcessing = opProcessingRule.get();
        Set<String> result = Sets.newHashSet();
        for(List<String> splitDataIds : splitList) {
            if(skipLeadsDuplicated(user.getTenantId())) {
                return Lists.newArrayList();
            }
            List<String> tempDataIds = Lists.newArrayList(splitDataIds);
            tempDataIds.removeIf(result::contains);
            List<IObjectData> dataList = serviceFacade.findObjectDataByIds(user.getTenantId(), tempDataIds, Utils.LEADS_API_NAME);
            if(CollectionUtils.isEmpty(dataList)) {
                continue;
            }
            Map<String, List<IObjectData>> matchMaps = getMatchMapByProcessing(user, Lists.newArrayList(duplicatedProcessing), dataList);
            if(matchMaps != null && matchMaps.containsKey(duplicatedProcessing.getId())
                    && CollectionUtils.isNotEmpty(matchMaps.get(duplicatedProcessing.getId()))) {
                for(IObjectData curData : matchMaps.get(duplicatedProcessing.getId())) {
                    if(result.contains(curData.getId())) {
                        continue;
                    }
                    List<IObjectData> duplicatedDataList = getDuplicatedDataListByProcessing(user, duplicatedProcessing, curData, 2000);
                    if(CollectionUtils.isNotEmpty(duplicatedDataList)) {
                        result.addAll(duplicatedDataList.stream().map(DBRecord::getId).collect(Collectors.toList()));
                        result.add(curData.getId());
                    }
                }
            }
        }
        return Lists.newArrayList(result);
    }

    private Map<String, List<IObjectData>> getMatchMapByProcessing(User user,
                                                                   List<DuplicatedModels.DuplicatedProcessing> duplicatedProcessingList,
                                                                   List<IObjectData> dataList) {
        if(CollectionUtils.isEmpty(dataList) || CollectionUtils.isEmpty(duplicatedProcessingList)) {
            return Maps.newHashMap();
        }
        return ruleMatcher.matches(user, Utils.LEADS_API_NAME, duplicatedProcessingList, dataList);
    }

    private List<IObjectData> getDuplicatedDataListByProcessingId(User user, String processingId, List<DuplicatedModels.DuplicatedProcessing> duplicatedProcessingList,
                                                                  String objectId, int dataSize) {
        Optional<DuplicatedModels.DuplicatedProcessing> opProcessingRule = duplicatedProcessingList.stream()
                .filter(x -> processingId.equals(x.getId())).findFirst();
        if(!opProcessingRule.isPresent()) {
            return Lists.newArrayList();
        }
        List<IObjectData> dataList = serviceFacade.findObjectDataByIds(user.getTenantId(), Lists.newArrayList(objectId), Utils.LEADS_API_NAME);
        if(CollectionUtils.isEmpty(dataList)) {
            return Lists.newArrayList();
        }
        return getDuplicatedDataListByProcessing(user, opProcessingRule.get(), dataList.get(0), dataSize);
    }

    private List<IObjectData> getDuplicatedDataListByProcessing(User user, DuplicatedModels.DuplicatedProcessing duplicatedProcessing, IObjectData curObjectData, int dataSize) {
        List<IObjectData> dataList = Lists.newArrayList();
        if(duplicatedProcessing == null) {
            return dataList;
        }
        List<DuplicatedModels.DuplicatedSearchRule> searchRuleList = duplicatedProcessing.getDuplicatedSearchRules();
        for (DuplicatedModels.DuplicatedSearchRule searchRule : searchRuleList) {
            String objectApiName = searchRule.getObjectApiName();
            if (!Utils.LEADS_API_NAME.equals(objectApiName)) {
                continue;
            }

            List<IObjectData> searchResult = duplicatedSearchDelegate.search(user, searchRule, curObjectData, dataSize, null, Lists.newArrayList("_id"));
            dataList.addAll(searchResult);
        }
        return dataList;
    }

    private Map<String, Boolean> getNeedProcessByData(User user, List<String> processingIds, IObjectData curObjectData) {
        Map<String, Boolean> result = Maps.newHashMap();
        for(String processingId : processingIds) {
            boolean needProcess = true;
            List<DuplicatedModels.DuplicatedSearchRule> searchRuleList = duplicatedProcessingRuleService.getDuplicatedSearchRulesByProcessingId(user, processingId);
            for (DuplicatedModels.DuplicatedSearchRule searchRule : searchRuleList) {
                String objectApiName = searchRule.getObjectApiName();
                if (!Utils.LEADS_API_NAME.equals(objectApiName)) {
                    continue;
                }

                List<IObjectData> searchResult = duplicatedSearchDelegate.search(user, searchRule, curObjectData, 20, null, Lists.newArrayList("_id"));
                if(CollectionUtils.isNotEmpty(searchResult)) {
                    needProcess = false;
                    break;
                }
            }
            result.put(processingId, needProcess);
        }

        return result;
    }

}