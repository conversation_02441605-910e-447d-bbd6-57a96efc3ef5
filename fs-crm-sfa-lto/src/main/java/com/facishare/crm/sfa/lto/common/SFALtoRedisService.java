package com.facishare.crm.sfa.lto.common;

import com.github.jedis.support.JedisCmd;
import com.github.jedis.support.MergeJedisCmd;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.UUID;


@Component
@Slf4j
public class SFALtoRedisService {
    @Autowired
    protected JedisCmd SFAJedisCmd;
    private static final String LOCK_SUCCESS = "OK";
    private static final String SET_IF_NOT_EXIST = "NX";
    private static final String SET_WITH_EXPIRE_TIME_MILLISECOND  = "PX";
    private static final String SET_WITH_EXPIRE_TIME_SECOND = "EX";
    private static final Long RELEASE_SUCCESS = 1L;

    public boolean releaseLock(String tenantId, String objectApiName, String objectActionCode, String objectId, String requestId){
        String lockKey = getLockKey(tenantId, objectApiName, objectActionCode, objectId);
        String script = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
        Object result = SFAJedisCmd.eval(script, Collections.singletonList(lockKey), Collections.singletonList(requestId));

        return RELEASE_SUCCESS.equals(result);
    }

    public String getLockKey(String tenantId, String objectApiName, String objectActionCode, String objectId) {
        return String.format("%s-%s-%s-%s", tenantId, objectApiName, objectActionCode, objectId);
    }
}
