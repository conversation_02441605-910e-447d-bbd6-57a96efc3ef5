package com.facishare.crm.sfa.lto.todo;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.lto.todo.enums.LtoSessionBOCItemKeys;
import com.facishare.crm.sfa.lto.todo.model.LtoGetTobeConfirmOpportunityIDsModel;
import com.facishare.crm.sfa.lto.todo.model.LtoSaleActionModel;
import com.facishare.crm.sfa.lto.utils.DateUtil;
import com.facishare.crm.sfa.lto.utils.JsonUtil;
import com.facishare.crm.sfa.lto.utils.TodoUtils;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.rest.core.exception.RestProxyInvokeException;
import com.fxiaoke.common.SqlEscaper;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * Demo class
 *
 * <AUTHOR>
 * @date 2019/10/23
 */
@Service
@Slf4j
public class LtoOpportunityUnProcessedService implements LtoIUnProcessedService {
    @Override
    public String getObjectApiName() {
        return "OpportunityObj";
    }

    @Override
    public List<IObjectData> getTotalUnProcessedData(User user, LtoSessionBOCItemKeys sessionBOCItemKey) {
        Integer unProcessedStatus = 1;
        List<IObjectData> remindData = TodoUtils.getTobeConfirmOpportunityData(unProcessedStatus, user,getObjectApiName());
        return remindData;
    }



}
