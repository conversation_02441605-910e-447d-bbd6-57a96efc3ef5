package com.facishare.crm.sfa.prm.platform.infrastructure.statemachine.rule;

import com.facishare.crm.sfa.prm.platform.infrastructure.statemachine.core.StateAction;
import com.facishare.crm.sfa.prm.platform.infrastructure.statemachine.transition.StateTransition;

import java.util.HashMap;
import java.util.Map;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-04-09
 * ============================================================
 */
public class StateRulesBuilder<S, E, C> {
    private final Map<S, Map<E, StateTransition<S, E, C>>> rules;

    private StateRulesBuilder() {
        this.rules = new HashMap<>();
    }

    public static <S, E, C> StateRulesBuilder<S, E, C> builder() {
        return new StateRulesBuilder<>();
    }

    public StateRulesBuilder<S, E, C> addTransition(
            S sourceState,
            E event,
            S targetState,
            StateAction<S, E, C> action) {
        rules.computeIfAbsent(sourceState, k -> new HashMap<>())
                .put(event, new StateTransition<>(sourceState, event, targetState, action));
        return this;
    }

    public Map<S, Map<E, StateTransition<S, E, C>>> build() {
        return rules;
    }
}
