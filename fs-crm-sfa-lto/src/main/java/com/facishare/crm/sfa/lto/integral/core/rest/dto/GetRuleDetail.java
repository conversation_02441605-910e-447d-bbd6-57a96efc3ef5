package com.facishare.crm.sfa.lto.integral.core.rest.dto;

import com.facishare.crm.sfa.lto.integral.core.service.dto.FindDetailByApiName;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

public interface GetRuleDetail {

    @EqualsAndHashCode(callSuper = true)
	@Data
    class Arg extends BaseEngine.Arg {
        @SerializedName("macroGroupApiName")
        String ruleApiName;

        public static Arg build(FindDetailByApiName.Arg in, RequestContext requestContext) {
            Arg out = new Arg();
            BaseEngine.Context context = new BaseEngine.Context();
            context.setUserId(requestContext.getUser().getUserId());
            context.setTenantId(requestContext.getTenantId());
            out.setRuleApiName(in.getApiName());
            out.setContext(context);

            return out;
        }

        public static Arg build(String apiName, String tenantId, String userId) {
            Arg out = new Arg();
            BaseEngine.Context context = new BaseEngine.Context();
            context.setUserId(userId);
            context.setTenantId(tenantId);
            out.setRuleApiName(apiName);
            out.setContext(context);

            return out;
        }
    }

    @EqualsAndHashCode(callSuper = true)
	@Data
    class Result extends BaseEngine.Result<RuleDetail> { }

    @Data
    class RuleDetail {
        @SerializedName("ruleMacroGroupPojo")
        BasicInfo basicInfo;
        @SerializedName("ruleGroupPojos")
        List<RuleGroup> ruleGroups;

    }

    @Data
    class BasicInfo {
        String id;
        String tenantId;
        @SerializedName("entityId")
        String objectDescribeApiName;
        @SerializedName("apiName")
        String ruleApiName;
        @SerializedName("name")
        String label;
        /** 规则状态: 1启用，0停用*/
        int status;
        String remark;
        String createdBy;
        long createTime;
        String lastModifiedBy;
        long lastModifiedTime;
        @SerializedName("isDeleted")
        int deleted;
        @SerializedName("isRealTime")
        boolean realTimeUpdate;
        @SerializedName("result")
        String ruleResult;
    }

    @Data
    class RuleGroup {
        @SerializedName("result")
        String calculateScore;
        @SerializedName("category")
        String fieldApiName;
        int priority;
        List<Rule> rules;
    }

    @Data
    class Rule {
        String fieldName;
        String fieldType;
        String operate;
        List<String> fieldValue;
    }


}
