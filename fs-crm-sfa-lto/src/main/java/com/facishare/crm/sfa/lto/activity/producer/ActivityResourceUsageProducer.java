package com.facishare.crm.sfa.lto.activity.producer;

import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Component
public class ActivityResourceUsageProducer implements InitializingBean, ApplicationListener<ContextClosedEvent> {
    private static final String CONFIG_NAME = "fs-crm-task-sfa-mq.ini";
    private static final String CONFIG_SECTION_NAME = "license_para_change";
    private static final String TOPIC = "overview-change-topic";
    private static final String TAG = "overview";

    private AutoConfMQProducer producer;

    public void updateLicenseUsageTo(String tenantId, String key, String value) {
        Message message = newMessage(tenantId, key, value);
        SendResult result = producer.send(message);
        log.debug("send ok, msgId={}", result.getMsgId());
    }

    public void updateLicenseUsageTo(String tenantId, Map<String, String> map) {
        List<Message> messages = new ArrayList<>(map.size());
        for (Map.Entry<String, String> entry : map.entrySet()) {
            messages.add(newMessage(tenantId, entry.getKey(), entry.getValue()));
        }
        SendResult result = producer.send(messages);
        log.debug("send ok, msgId={}", result.getMsgId());
    }

    private static Message newMessage(String tenantId, String key, String value) {
        String msg = String.format("{\"paraKey\":\"%s\",\"tenantId\":\"%s\",\"usedValue\":\"%s\"}", key, tenantId, value);
        return new Message(TOPIC, TAG, msg.getBytes(StandardCharsets.UTF_8));
    }

    @Override
    public void afterPropertiesSet() {
        producer = new AutoConfMQProducer(CONFIG_NAME, CONFIG_SECTION_NAME);
    }

    @Override
    public void onApplicationEvent(ContextClosedEvent contextRefreshedEvent) {
        Optional.ofNullable(producer).ifPresent(AutoConfMQProducer::close);
    }
}
