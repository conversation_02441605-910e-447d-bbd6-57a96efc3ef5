package com.facishare.crm.sfa.lto.qywx.proxy.models;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class QualityInspectionRuleDel {

    private String fsEa;
    private DelData data;

    public QualityInspectionRuleDel build(String fsEa, String ruleId) {
        QualityInspectionRuleDel delParam = new QualityInspectionRuleDel();
        delParam.setFsEa(fsEa);
        DelData data = new DelData();
        data.setRuleId(ruleId);
        delParam.setData(data);
        return delParam;
    }

    @Data
    public static class DelData {
        @JSONField(name = "rule_id")
        private String ruleId;
    }

}
