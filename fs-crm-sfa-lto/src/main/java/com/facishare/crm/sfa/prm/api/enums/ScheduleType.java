package com.facishare.crm.sfa.prm.api.enums;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import lombok.Getter;

import static com.facishare.crm.sfa.prm.core.constants.PrmI18NConstants.PRM_ENUM_PARAM_TYPE_ERROR;

/**
 * ============================================================
 *
 * @Description: 签署类型
 * @CreatedBy: Sundy on 2025-02-27
 * ============================================================
 */
@Getter
public enum ScheduleType {
    /**
     * 固定周期签署
     */
    CYCLE("cycle"),
    /**
     * 固定日签署
     */
    FIXED_DATE("fixed_date"),
    /**
     * 一次性签署
     */
    ONE_TIME("one_time");

    private final String type;

    ScheduleType(String type) {
        this.type = type;
    }

    public static ScheduleType from(String type) {
        for (ScheduleType e : values()) {
            if (e.type.equals(type)) {
                return e;
            }
        }
        throw new ValidateException(I18N.text(PRM_ENUM_PARAM_TYPE_ERROR, type));
    }

    public static ScheduleType find(String type) {
        return find(type, null);
    }

    public static ScheduleType find(String type, ScheduleType defaultValue) {
        for (ScheduleType e : values()) {
            if (e.type.equals(type)) {
                return e;
            }
        }
        return defaultValue;
    }
}
