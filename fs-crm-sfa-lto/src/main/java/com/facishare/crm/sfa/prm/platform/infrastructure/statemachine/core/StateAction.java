package com.facishare.crm.sfa.prm.platform.infrastructure.statemachine.core;

/**
 * ============================================================
 *
 * @param <S> 状态类型
 * @param <E> 事件类型
 * @param <C> 上下文类型
 * @Description: 状态动作接口
 * @CreatedBy: Sundy on 2025-04-09
 * ============================================================
 */
@FunctionalInterface
public interface StateAction<S, E, C> {
    /**
     * 执行状态转换动作
     *
     * @param sourceState 源状态
     * @param event       触发事件
     * @param context     上下文
     */
    void execute(S sourceState, E event, C context);
}
