package com.facishare.crm.sfa.lto.integral.core.service;

import com.facishare.crm.sfa.lto.integral.common.constant.IntegralObject;
import com.facishare.crm.sfa.lto.integral.core.service.dto.BehaviorInfo;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class BehaviorCategoryServiceImpl extends BehaviorObjectService implements BehaviorCategoryService {
    @Autowired
    DataLogicService dataLogicService;

    @Override
    public IObjectData getBehaviorCategoryByApiName(String tenantId, String categoryApiName) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, IntegralObject.FIELD_CATEGORY_API_NAME, categoryApiName);
        query.addFilters(filters);
        query.setLimit(1);
        query.setNeedReturnCountNum(false);

        IObjectData objectData;
        Optional<BehaviorInfo.BehaviorCategory> systemCategory = SystemBehaviorArchives.getSystemBehaviorCategoryList().stream()
                .filter(x -> categoryApiName.equals(x.getCategoryApiName())).findAny();
        if (systemCategory.isPresent()) {
            objectData = new ObjectData();
            objectData.set(IntegralObject.FIELD_CATEGORY_API_NAME, systemCategory.get().getCategoryApiName());
            objectData.set(IntegralObject.FIELD_CATEGORY_LABEL, systemCategory.get().getCategoryLabel());
        } else {
            List<IObjectData> objectDataList = dataLogicService.findBySearchQuery(tenantId, IntegralObject.BEHAVIOR_CATEGORY_API_NAME, query);
            objectData = CollectionUtils.notEmpty(objectDataList) ? objectDataList.get(0) : null;
        }

        return objectData;
    }

    @Override
    public List<IObjectData> getBehaviorCategoryList(String tenantId) {
        return getBehaviorObjectList(tenantId, IntegralObject.BEHAVIOR_CATEGORY_API_NAME);
    }
}
