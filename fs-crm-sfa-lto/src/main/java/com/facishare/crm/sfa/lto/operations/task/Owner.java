package com.facishare.crm.sfa.lto.operations.task;

import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;
import java.util.function.BiConsumer;

class Owner implements TemplateMemberField {

    private final BiConsumer<OperationsTask, List<String>> setter;

    Owner(BiConsumer<OperationsTask, List<String>> setter) {
        this.setter = setter;
    }

    @Override
    public List<String> getMember(IObjectData bizData) {
        return bizData.getOwner();
    }

    @Override
    public BiConsumer<OperationsTask, List<String>> setter() {
        return setter;
    }
}
