package com.facishare.crm.sfa.lto.activity.mongo;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.lto.utils.CollectionUtil;
import com.facishare.crm.sfa.lto.utils.Safes;
import com.github.mongo.support.DatastoreExt;
import com.google.common.collect.Lists;
import com.mongodb.MongoClientSettings;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.*;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.codecs.configuration.CodecRegistries;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ParagraphMongoDao {

    @Getter
    private final DatastoreExt store;

    private static final  String COLLECTION_PREFIX = "interactive_paragraph_";

    private final String databaseName;

    private static class SingleCodecHolder {
        //需要让自定义的codec放前面
        private static final CodecRegistry codecRegistry = CodecRegistries.fromRegistries(
                MongoClientSettings.getDefaultCodecRegistry(),
                CodecRegistries.fromProviders(PojoCodecProvider.builder()
                        .register(ParagraphDocument.class)
                        .automatic(true).build()));
    }

    public ParagraphMongoDao(@Qualifier("activityDataMongoDbStore")DatastoreExt store) {
        if (store == null) {
            this.store = null;
            this.databaseName = null;
            log.warn("mongo store is null");
            return;
        }
        this.store = store;
        this.databaseName = store.getDB().getName();
    }

    private String getCollectionName(String tenantId) {
        return COLLECTION_PREFIX + tenantId;
    }

    private MongoCollection<ParagraphDocument> getMongoCollection(String tenantId) {
        String collectionName = getCollectionName(tenantId);
        ensureCollectionAndIndexes(collectionName);
        return store.getMongo().getDatabase(databaseName)
                .withCodecRegistry(SingleCodecHolder.codecRegistry)
                .getCollection(collectionName, ParagraphDocument.class);
    }

    public List<ParagraphDocument> queryListByActiveRecordId(String tenantId, String objectId, Integer offset, Integer limit, boolean sort) {
        MongoCollection<ParagraphDocument> collection = getMongoCollection(tenantId);
        List<Bson> bsonList = Lists.newArrayList();
        bsonList.add(Filters.eq("objectId", objectId));
        bsonList.add(Filters.eq("objectApiName", Utils.ACTIVE_RECORD_API_NAME));
        bsonList.add(Filters.eq("isDeleted", false));
        if (sort) {
            return collection.find(Filters.and(bsonList)).sort(Sorts.ascending("seqNo")).skip(offset).limit(limit).into(new ArrayList<>());
        } else {
            return collection.find(Filters.and(bsonList)).skip(offset).limit(limit).into(new ArrayList<>());
        }
    }

    private synchronized void ensureCollectionAndIndexes(String collectionName) {
        MongoCollection<Document> collection = store.getMongo().getDatabase(databaseName).getCollection(collectionName);
        
        // 检查索引是否存在
        boolean indexExists = false;
        for (Document idx : Safes.of(collection.listIndexes())) {
            if (idx.containsKey("name") && "paragraphDataIndex".equals(idx.getString("name"))) {
                indexExists = true;
                break;
            }
        }

        // 如果索引不存在，创建索引
        if (!indexExists) {
            List<IndexModel> indexList = Lists.newArrayList();
            Bson paragraphDataIndex = Indexes.compoundIndex(
                    Indexes.ascending("objectId"),
                    Indexes.ascending("objectApiName"),
                    Indexes.ascending("isDeleted")
            );
            indexList.add(new IndexModel(paragraphDataIndex, new IndexOptions()
                    .background(true)
                    .name("paragraphDataIndex")
            ));

            List<String> indexes = collection.createIndexes(indexList);
            log.info("Collection {} - Created indexes: {}", collectionName, indexes);
        }
    }

    public void batchInsert(String tenantId, List<ParagraphDocument> documents) {
        documents.forEach(d -> {
            d.setIsDeleted(false);
            d.setCreateTime(System.currentTimeMillis());
            d.setLastUpdateTime(System.currentTimeMillis());
        });
        MongoCollection<ParagraphDocument> collection = getMongoCollection(tenantId);
        collection.insertMany(documents);
    }

    public ParagraphDocument queryById(String tenantId, String id) {
        MongoCollection<ParagraphDocument> collection = getMongoCollection(tenantId);
        List<Bson> bsonList = Lists.newArrayList();
        bsonList.add(Filters.eq("tenantId", tenantId));
        bsonList.add(Filters.eq("_id", new ObjectId(id)));
        List<ParagraphDocument> list = collection.find(Filters.and(bsonList)).into(new ArrayList<>());
        if(CollectionUtil.isNotEmpty(list)){
            return list.get(0);
        }
        return null;
    }

    public List<ParagraphDocument> queryByIds(String tenantId, List<String> ids) {
        MongoCollection<ParagraphDocument> collection = getMongoCollection(tenantId);
        List<ObjectId> objectIds = ids.stream().map(ObjectId::new).collect(Collectors.toList());
        List<Bson> bsonList = Lists.newArrayList();
        bsonList.add(Filters.eq("tenantId", tenantId));
        bsonList.add(Filters.in("_id", objectIds));
        List<ParagraphDocument> list = collection.find(Filters.and(bsonList)).into(new ArrayList<>());
        if(CollectionUtil.isNotEmpty(list)){
            return list;
        }
        return null;
    }

    /**
     * 根据ID列表查询指定字段的文档
     *
     * @param tenantId 租户ID
     * @param activeRecordId 销售记录id
     * @param fields 需要返回的字段列表
     * @return 符合条件的文档列表
     */
    public List<ParagraphDocument> queryByIdsWithFields(String tenantId, String activeRecordId, List<String> fields) {
        if (StringUtils.isEmpty(tenantId) || StringUtils.isEmpty(activeRecordId) || CollectionUtils.isEmpty(fields)) {
            log.warn("查询指定字段失败：参数无效, tenantId={}, ids为空或fields为空", tenantId);
            return Lists.newArrayList();
        }

        MongoCollection<ParagraphDocument> collection = getMongoCollection(tenantId);

        // 构建查询条件
        List<Bson> bsonList = Lists.newArrayList();
        bsonList.add(Filters.eq("tenantId", tenantId));
        bsonList.add(Filters.eq("objectId", activeRecordId));
        bsonList.add(Filters.eq("objectApiName", "ActiveRecordObj"));
        // 添加tags不为空的条件
        bsonList.add(Filters.and(
            Filters.exists("tags", true),
            Filters.not(Filters.size("tags", 0))
        ));

        // 构建投影，指定返回哪些字段
        Document projection = new Document();
        // 默认包含_id字段
        fields.forEach(field -> projection.append(field, 1));
        
        // 执行查询
        List<ParagraphDocument> list = collection.find(Filters.and(bsonList))
                .projection(projection)
                .into(new ArrayList<>());
                
        if(CollectionUtil.isNotEmpty(list)){
            return list;
        }
        return Lists.newArrayList();
    }

    /**
     * 批量更新文档的tags字段
     *
     * @param tenantId 租户ID
     * @param documents 要更新的文档列表
     * @return 更新的文档数量
     */
    public long batchUpdateTags(String tenantId, List<ParagraphDocument> documents) {
        if (StringUtils.isEmpty(tenantId) || CollectionUtils.isEmpty(documents)) {
            log.warn("批量更新tags失败：参数无效, tenantId={}, documents为空", tenantId);
            throw new IllegalArgumentException("TenantId不能为空且documents不能为空");
        }
        
        // 创建更新模型列表
        List<UpdateOneModel<ParagraphDocument>> updateModelList = documents.stream()
            .map(document -> {
                // 构建过滤条件 - 根据文档ID
                Bson filter = Filters.and(
                    Filters.eq("tenantId", tenantId),
                    Filters.eq("_id", document.getId()),
                    Filters.eq("isDeleted", false)
                );
                
                // 构建更新操作
                Document updateDoc = new Document();
                List<String> tags = document.getTags();
                if (CollectionUtils.isEmpty(tags)) {
                    updateDoc.append("tags", Lists.newArrayList());
                } else {
                    updateDoc.append("tags", tags);
                }

                if (StringUtils.isNotBlank(document.getReason())) {
                    updateDoc.append("reason", document.getReason());
                } else {
                    // 可选：如果为空，可以设置为默认值或不更新该字段
                    updateDoc.append("reason", ""); // 或者省略该字段
                }

// 更新 noReason 字段
                if (StringUtils.isNotBlank(document.getNoReason())) {
                    updateDoc.append("noReason", document.getNoReason());
                } else {
                    // 可选：如果为空，可以设置为默认值或不更新该字段
                    updateDoc.append("noReason", ""); // 或者省略该字段
                }
                
                Bson update = new Document("$set", updateDoc);
                
                // 返回更新模型
                return new UpdateOneModel<ParagraphDocument>(filter, update, new UpdateOptions().upsert(false));
            })
            .collect(Collectors.toList());
        
        if (updateModelList.isEmpty()) {
            log.warn("批量更新tags失败：没有有效的更新操作, tenantId={}", tenantId);
            return 0;
        }
        
        try {
            // 执行批量更新
            BulkWriteResult bulkWriteResult = getMongoCollection(tenantId)
                    .bulkWrite(updateModelList, new BulkWriteOptions().ordered(false));
            
            // 记录日志
            log.info("批量更新tags完成, tenantId={}, 文档数量={}, matched={}, modified={}", 
                    tenantId, documents.size(), bulkWriteResult.getMatchedCount(), bulkWriteResult.getModifiedCount());
            
            return bulkWriteResult.getModifiedCount();
        } catch (Exception e) {
            log.error("批量更新tags失败, tenantId={}, 文档数量={}, error:", tenantId, documents.size(), e);
            throw new RuntimeException("批量更新tags失败", e);
        }
    }

}
