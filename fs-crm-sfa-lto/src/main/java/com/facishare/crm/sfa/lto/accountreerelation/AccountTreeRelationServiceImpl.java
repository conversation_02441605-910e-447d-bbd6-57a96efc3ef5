package com.facishare.crm.sfa.lto.accountreerelation;

import com.alibaba.fastjson.JSONObject;
import com.facishare.common.parallel.ParallelUtils;
import com.facishare.crm.sfa.lto.accountreerelation.models.AccountTreeRelationModels.*;
import com.facishare.crm.sfa.lto.common.models.LtoFieldApiConstants;
import com.facishare.crm.sfa.lto.equityrelationship.model.EquityRelationshipDataModel;
import com.facishare.crm.sfa.lto.exception.ExceptionUtil;
import com.facishare.crm.sfa.lto.utils.*;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.*;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.fxiaoke.common.SqlEscaper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.lto.accountreerelation.models.AccountTreeRelationConstants.ACCOUNT_TREE_RELATION_API_NAME;
import static com.facishare.crm.sfa.lto.accountreerelation.models.AccountTreeRelationConstants.ROOT_ID;
import static com.facishare.crm.sfa.lto.utils.SfaLtoI18NKeyUtil.*;

@Component
@Slf4j
public class AccountTreeRelationServiceImpl implements IAccountTreeRelationService{
    @Autowired
    private IEquityRelationDataService equityRelationDataService;
    @Autowired
    private ObjectDataServiceImpl objectDataService;
    @Autowired
    private FunctionPrivilegeService functionPrivilegeService;
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    protected AccountMainDataMatchServiceImpl accountMainDataMatchService;
    @Autowired
    protected CommonTreeRelationServiceImpl commonTreeRelationService;

    private static final String UNIQUE_TABLE_NAME = "biz_account_tree_relation_unique_temp";
    private static final String API_NAME = "AccountTreeRelationObj";

    @Override
    public CreateAccountTreeRelationResult createAccountTreeRelation(User user, CreateAccountTreeRelationArg arg) {
        CreateAccountTreeRelationResult result = CreateAccountTreeRelationResult.builder()
                .errorCode("0").message("").build();
        String tenantId= user.getTenantId();
        String companyName = arg.getCompanyName();
        commonTreeRelationService. baseCheck(tenantId, companyName,arg);

        try {
            EquityRelationshipDataModel.RelationshipModel equityRelationInfo = getRelationshipModel(user, arg);
            equityRelationDataService.processEquityRelationData(user, equityRelationInfo,arg);
        } catch (ValidateException validateException) {
            throw validateException;
        } catch (Exception e) {
            log.warn(String.format("CreateAccountTreeRelation error, tenantId:%s, companyName:%s", tenantId, companyName), e);
            ExceptionUtil.throwCommonBusinessException();
        } finally {
            commonTreeRelationService.deleteUniqueTable(tenantId, companyName,arg);
        }
        return result;
    }

    @Override
    public CreateAccountTreeRelationResult createAccountTreeRelationAsync(User user, CreateAccountTreeRelationArg arg) {
        //查询是否有工商数据
        queryCompanyDetailIsExistByName(user, arg);
        //查询配额是否还有
        CommonTreeRelationUtil.handleTreeOverQuota(user,arg.getDescribeApiName());
        commonTreeRelationService.baseCheck(user.getTenantId(), arg.getCompanyName(),arg);
        ParallelUtils.ParallelTask task = ParallelUtils.createBackgroundTask();
        task.submit(() -> {
            try {
                EquityRelationshipDataModel.RelationshipModel equityRelationInfo = getRelationshipModel(user, arg);
                equityRelationDataService.processEquityRelationData(user, equityRelationInfo,arg);
            }catch (Exception e) {
                CommonTreeRelationUtil.handleTreeException(user,arg.getDescribeApiName());
                log.warn(String.format("createAccountTreeRelationAsync error, tenantId:%s, companyName:%s", user.getTenantId(), arg.getCompanyName()), e);
            }
            commonTreeRelationService.deleteUniqueTable(user.getTenantId(), arg.getCompanyName(),arg);
        });
        task.run();
        return CreateAccountTreeRelationResult.builder().build();
    }

    @Override
    public CheckCreateAccountTreeRelationResult checkCreateAccountTreeRelation(User user, CreateAccountTreeRelationArg arg) {
        CheckCreateAccountTreeRelationResult result = CheckCreateAccountTreeRelationResult.builder()
                .errorCode("200").message(I18N.text(SFA_ACCOUNT_TREE_RELATION_PROCESSING)).status(1).build();
        String companyName = arg.getCompanyName();
        String tenantId = user.getTenantId();
        if(StringUtils.isBlank(companyName)){
            result.setErrorCode("5000");
            result.setStatus(0);
            result.setMessage(I18N.text(I18NKey.PARAM_ERROR));
            return result;
        }

        List<Map> queryResult = commonTreeRelationService.queryUniqueTable(tenantId, companyName,arg);
        if(CollectionUtils.isNotEmpty(queryResult)) {
            result.setErrorCode("5000");
            result.setStatus(2);
            result.setMessage(I18N.text(SFA_ACCOUNT_TREE_RELATION_PROCESSING));
            return result;
        }

        try {
            commonTreeRelationService.checkTreeRelationExists(tenantId, companyName,arg);
        } catch (Exception e) {
            String linkId = commonTreeRelationService.getTreeRelationExists(tenantId, companyName,arg);
            if(ObjectUtils.isNotEmpty(linkId)){
                result.setErrorCode("5000");
                result.setStatus(5);
                result.setMessage(I18N.text(SFA_COMMON_TREE_RELATION_EXISTS));
                result.setLinkId(linkId);
                return result;
            }
            result.setErrorCode("5000");
            result.setStatus(3);
            result.setMessage(I18N.text(SFA_ACCOUNT_TREE_RELATION_EXISTS));
            return result;
        }

        try {
            Map<String, Boolean> funcResult = functionPrivilegeService.funPrivilegeCheck(user, API_NAME, Lists.newArrayList("GenerateTreeRelation"));
            if(funcResult == null || funcResult.isEmpty() || !Boolean.TRUE.equals(funcResult.get("GenerateTreeRelation"))) {
                result.setErrorCode("5000");
                result.setStatus(0);
                result.setMessage(I18N.text(I18NKey.NO_PERMISSION_IN_OPERATE));
                return result;
            }
        } catch (Exception e) {
            result.setErrorCode("5000");
            result.setStatus(0);
            result.setMessage(I18N.text(I18NKey.NO_PERMISSION_IN_OPERATE));
            return result;
        }
        return result;
    }

    @Override
    public CreateAccountTreeRelationResult relateMainData2TreeRelation(User user, RelateMainData2TreeRelationArg arg) {
        CreateAccountTreeRelationResult result = CreateAccountTreeRelationResult.builder().errorCode("0").message("Success").build();
        if(CollectionUtils.isEmpty(arg.getObjectIds())) {
            return result;
        }
        log.warn("relateMainData2TreeRelation getObjectIds:{}", JSONObject.toJSONString(arg.getObjectIds()));
        List<IObjectData> dataList = accountMainDataMatchService.getMatchedAccountMainData(user.getTenantId(), arg.getObjectIds());
        if(CollectionUtils.isEmpty(dataList)) {
            return result;
        }
        List<String> nameList = dataList.stream().map(x -> x.getName()).collect(Collectors.toList());
        log.info("relateMainData2TreeRelation nameList:{}", JSONObject.toJSONString(nameList));
        SearchTemplateQuery searchQuery = getBaseSearchQuery(user.getTenantId());
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, "name", nameList);
        SearchUtil.fillFilterIsNull(filters, "account_main_data_id");
        searchQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = ObjectDataUtil.findDataBySearchQuery(user, API_NAME, searchQuery,
                Lists.newArrayList("tenant_id", "_id", "name", "account_main_data_id", "parent_account_main_data_id", "root_account_main_data_id", "is_root", "root_id", "object_describe_api_name"));
        if(queryResult == null || CollectionUtils.isEmpty(queryResult.getData())) {
            log.info("relateMainData2TreeRelation queryResult si null");
            return result;
        }
        List<IObjectData> updateDataList = Lists.newArrayList();
        queryResult.getData().forEach(x -> {
            Optional<IObjectData> op = dataList.stream().filter(d -> x.getName().equals(d.getName())).findFirst();
            if(op.isPresent()) {
                String rootId = ObjectDataUtil.getStringValue(x, "root_id", "");
                String accountMainDataId = op.get().getId();
                if(Boolean.TRUE.equals(ObjectDataUtil.getBooleanValue(x, "is_root", false))) {
                    updateRootAccountMainData(user, x.getId(), accountMainDataId);
                }
                updateChildrenAccountMainData(user, rootId, x.getId(), accountMainDataId);
                x.set("account_main_data_id", accountMainDataId);
                updateDataList.add(x);
            }
        });
        if(CollectionUtils.isNotEmpty(updateDataList)) {
            try {
                ObjectDataUtil.updateFields(user, updateDataList, Lists.newArrayList("account_main_data_id"), false, true);
            } catch (Exception e) {
                log.error("updateAccountMainData error,tenantId:{}", user.getTenantId(), e);
            }
        }
        return result;
    }

    private SearchTemplateQuery getBaseSearchQuery(String tenantId) {
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(100);
        searchQuery.setOffset(0);
        searchQuery.setPermissionType(0);
        searchQuery.setNeedReturnCountNum(false);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, "tenant_id", tenantId);
        SearchUtil.fillFilterEq(filters, "object_describe_api_name", API_NAME);
        searchQuery.setFilters(filters);
        return searchQuery;
    }

    private void updateRootAccountMainData(User user, String rootId, String accountMainDataId) {
        try {
            int maxExecuteCount = 100000;
            int executedCount = 0;
            SearchTemplateQuery searchQuery = getBaseSearchQuery(user.getTenantId());
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, "root_id", rootId);
            SearchUtil.fillFilterIsNull(filters, "root_account_main_data_id");
            searchQuery.setFilters(filters);
            while (true) {
                ++executedCount;
                SearchTemplateQuery templateQuery = SearchUtil.copySearchTemplateQuery(searchQuery);
                QueryResult<IObjectData> queryResult = ObjectDataUtil.findDataBySearchQuery(user, API_NAME, templateQuery,
                        Lists.newArrayList("_id", "tenant_id", "object_describe_api_name"));
                if(queryResult == null || CollectionUtils.isEmpty(queryResult.getData())) {
                    return;
                }
                List<IObjectData> objectDataList = queryResult.getData();
                objectDataList.forEach(x -> x.set("root_account_main_data_id", accountMainDataId));
                ObjectDataUtil.updateFields(user, objectDataList, Lists.newArrayList("root_account_main_data_id"), false, true);
                if(executedCount > maxExecuteCount) {
                    log.warn("updateRootAccountMainData executed max count 100000");
                    return;
                }
            }
        } catch (Exception e) {
            log.error("updateRootAccountMainData error,tenantId:{},rootId:{},accountMainDataId:{}", user.getTenantId(), rootId, accountMainDataId, e);
        }
    }

    private void updateChildrenAccountMainData(User user, String rootId, String parentId, String accountMainDataId) {
        try {
            int maxExecuteCount = 100000;
            int executedCount = 0;
            SearchTemplateQuery searchQuery = getBaseSearchQuery(user.getTenantId());
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, "parent_id", parentId);
            SearchUtil.fillFilterEq(filters, "root_id", rootId);
            SearchUtil.fillFilterIsNull(filters, "parent_account_main_data_id");
            searchQuery.setFilters(filters);
            while (true) {
                ++executedCount;
                SearchTemplateQuery templateQuery = SearchUtil.copySearchTemplateQuery(searchQuery);
                QueryResult<IObjectData> queryResult = ObjectDataUtil.findDataBySearchQuery(user, API_NAME, templateQuery,
                        Lists.newArrayList("_id", "tenant_id", "object_describe_api_name"));
                if(queryResult == null || CollectionUtils.isEmpty(queryResult.getData())) {
                    return;
                }
                List<IObjectData> objectDataList = queryResult.getData();
                objectDataList.forEach(x -> x.set("parent_account_main_data_id", accountMainDataId));
                ObjectDataUtil.updateFields(user, objectDataList, Lists.newArrayList("parent_account_main_data_id"), false, true);
                if(executedCount > maxExecuteCount) {
                    log.warn("updateChildrenAccountMainData executed max count 100000");
                    return;
                }
            }
        } catch (Exception e) {
            log.error("updateChildrenAccountMainData error,tenantId:{},rootId:{}, parentId:{}, accountMainDataId:{}",
                    user.getTenantId(), rootId, parentId, accountMainDataId, e);
        }
    }

    @Override
    public CreateAccountTreeRelationResult relateMainData2TreeRelationAsync(User user, RelateMainData2TreeRelationArg arg) {
        try {
            ParallelUtils.ParallelTask task = ParallelUtils.createBackgroundTask();
            task.submit(() -> relateMainData2TreeRelation(user, arg));
            task.run();
        } catch (Exception e) {
            log.warn("relateMainData2TreeRelationAsync error, tenantId:{}, objectIds:{}", user.getTenantId(), String.join(",", arg.getObjectIds()), e);
            ExceptionUtil.throwCommonBusinessException();
        }
        return CreateAccountTreeRelationResult.builder().errorCode("0").message("Success").build();
    }

    @Override
    public CheckCreateAccountTreeRelationResult deleteAccountTreeRelationUniqueData(User user, CreateAccountTreeRelationArg arg) {
        CheckCreateAccountTreeRelationResult result = CheckCreateAccountTreeRelationResult.builder()
                .errorCode("200").message(I18N.text(SFA_ACCOUNT_TREE_RELATION_PROCESSING)).status(1).build();
        commonTreeRelationService.deleteUniqueTable(user.getTenantId(), arg.getCompanyName(),arg);
        return  result;
    }

    @Override
    public CheckCreateAccountTreeRelationResult deleteAccountTreeRelationData(User user, CreateAccountTreeRelationArg arg) {
        CheckCreateAccountTreeRelationResult result = CheckCreateAccountTreeRelationResult.builder()
                .errorCode("200").message(I18N.text(SFA_ACCOUNT_TREE_RELATION_PROCESSING)).status(1).build();
        deleteAccountRelationData(user, arg.getCompanyName());
        return  result;
    }

    @NotNull
    private EquityRelationshipDataModel.RelationshipModel getRelationshipModel(User user, CreateAccountTreeRelationArg arg) {
        EquityRelationshipDataModel.RelationshipModel equityRelationInfo = equityRelationDataService.getEquityRelationInfo(user, arg.getCompanyName());
        if (equityRelationInfo == null
                || CollectionUtils.isEmpty(equityRelationInfo.getC_trees())
                || !equityRelationInfo.getName().equals(arg.getCompanyName())) {
            commonTreeRelationService.deleteUniqueTable(user.getTenantId(), arg.getCompanyName(),arg);
            throw new ValidateException(I18N.text(SFA_ACCOUNT_TREE_RELATION_EQUITY_DATA_NOT_EXIST));
        }
        return equityRelationInfo;
    }
    @NotNull
    private void queryCompanyDetailIsExistByName(User user, CreateAccountTreeRelationArg arg) {
        EquityRelationshipDataModel.CompanyDetail companyDetail = equityRelationDataService.queryCompanyDetailIsExistByName(user, arg.getCompanyName());
        if (companyDetail == null
                || ObjectUtils.isEmpty(companyDetail.getId())
                || !companyDetail.getName().equals(arg.getCompanyName())) {
            commonTreeRelationService.deleteUniqueTable(user.getTenantId(), arg.getCompanyName(),arg);
            throw new ValidateException(I18N.text(SFA_ACCOUNT_TREE_RELATION_EQUITY_DATA_NOT_EXIST));
        }
    }


    private void deleteAccountRelationData(User user, String companyName) {
        try {
            if(StringUtils.isBlank(companyName)) {
                return;
            }
            int maxExecutedCount = 10000;
            while (maxExecutedCount > 0) {
                SearchTemplateQuery searchQuery = new SearchTemplateQuery();
                List<IFilter> filters = Lists.newArrayList();
                SearchUtil.fillFilterEq(filters, ROOT_ID, companyName);
                searchQuery.setFilters(filters);
                searchQuery.setLimit(100);
                searchQuery.setNeedReturnCountNum(false);
                searchQuery.setNeedReturnQuote(false);
                QueryResult<IObjectData> queryResult = ObjectDataUtil.findDataBySearchQuery(user, ACCOUNT_TREE_RELATION_API_NAME, searchQuery, Lists.newArrayList());
                if(queryResult == null || CollectionUtils.isEmpty(queryResult.getData())) {
                    return;
                }
                serviceFacade.bulkInvalidAndDeleteWithSuperPrivilege(queryResult.getData(), user);
                --maxExecutedCount;
            }
        } catch (Exception e) {
            log.warn("deleteAccountRelationData error!", e);
        }
    }

}
