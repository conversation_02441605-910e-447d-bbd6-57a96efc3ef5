package com.facishare.crm.sfa.lto.common;

import com.facishare.crm.sfa.lto.common.models.LtoFieldApiConstants;
import com.facishare.crm.sfa.lto.exception.ExceptionUtil;
import com.facishare.crm.sfa.lto.objectpool.PoolEmptyRule;
import com.facishare.crm.sfa.lto.utils.ActionContextUtil;
import com.facishare.crm.sfa.lto.utils.ObjectDataUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.TeamMember;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.fxiaoke.enterpriserelation2.result.RelationDownstreamResult;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import jodd.util.StringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
public class LtoTeamMemberService {
    @Autowired
    private ObjectDataServiceImpl objectDataService;

    @Autowired
    private ConfigService configService;

    @Autowired
    private LtoOrgCommonService ltoOrgCommonService;

    public void changeInnerOwner(User user, String owner, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        objectDataList.forEach(objectData -> removeObjectTeamMember(Lists.newArrayList(owner), objectData));
        removeObjectInnerOwner(objectDataList);
        objectDataList.forEach(objectData -> addObjectTeamMember(Lists.newArrayList(owner), TeamMember.Role.OWNER.getValue(),
                TeamMember.Permission.READANDWRITE.getValue(), objectData));
        batchUpdateRelevantTeam(user, objectDataList);
    }

    public void removeObjectAllInnerTeamMember(User user, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        objectDataList.forEach(objectData -> {
            ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
            List<TeamMember> teamMembers = objectDataExt.getTeamMembers();
            List<TeamMember> deleteMember = teamMembers.stream()
                    .filter(f -> !f.isOutMember()).collect(Collectors.toList());
            teamMembers.removeAll(deleteMember);
            objectDataExt.setTeamMembers(teamMembers);
        });
        batchUpdateRelevantTeam(user, objectDataList);
    }

    public void removeObjectAllInnerTeamMember(User user, List<IObjectData> objectDataList, List<IObjectData> oldObjectDataList, Boolean exceptOwner) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }

        if (CollectionUtils.empty(oldObjectDataList)) {
            if (exceptOwner != null && exceptOwner) {
                removeObjectAllInnerTeamMemberExceptInnerOwner(user, objectDataList);
            } else {
                removeObjectAllInnerTeamMember(user, objectDataList);
            }
            return;
        }
        for (IObjectData oldObjectData : oldObjectDataList) {
            objectDataList.removeIf(x -> Objects.equals(x.getId(), oldObjectData.getId()) && Objects.equals(oldObjectData.getOwner(), x.getOwner()));
        }
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        if (exceptOwner != null && exceptOwner) {
            removeObjectAllInnerTeamMemberExceptInnerOwner(user, objectDataList);
        } else {
            removeObjectAllInnerTeamMember(user, objectDataList);
        }
    }

    public void removeObjectAllInnerTeamMemberExceptInnerOwner(User user, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        objectDataList.forEach(objectData -> {
            ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
            List<TeamMember> teamMembers = objectDataExt.getTeamMembers();
            List<TeamMember> deleteMember = teamMembers.stream()
                    .filter(f -> !f.isOutMember() && !TeamMember.Role.OWNER.equals(f.getRole())).collect(Collectors.toList());
            teamMembers.removeAll(deleteMember);
            objectDataExt.setTeamMembers(teamMembers);
        });
        batchUpdateRelevantTeam(user, objectDataList);
    }

    public void changeOwner(User user, String owner, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        objectDataList.forEach(objectData -> removeObjectTeamMember(Lists.newArrayList(owner), objectData));
        removeObjectOwner(objectDataList);
        objectDataList.forEach(objectData -> addObjectTeamMember(Lists.newArrayList(owner), TeamMember.Role.OWNER.getValue(),
                TeamMember.Permission.READANDWRITE.getValue(), objectData));
        batchUpdateRelevantTeam(user, objectDataList);
    }

    public void changeOwner(User user, String owner, List<IObjectData> objectDataList,
                            String outTenantId, String outOwner) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        List<String> toRemoveMemberIds = Lists.newArrayList(owner);
        if (!Strings.isNullOrEmpty(outOwner)) {
            toRemoveMemberIds.add(outOwner);
        }
        objectDataList.forEach(objectData -> removeObjectTeamMember(toRemoveMemberIds, objectData));
        removeObjectOwner(objectDataList);
        objectDataList.forEach(objectData -> {
            addObjectTeamMember(Lists.newArrayList(owner), TeamMember.Role.OWNER.getValue(),
                    TeamMember.Permission.READANDWRITE.getValue(), objectData);
            if (!Strings.isNullOrEmpty(outOwner)) {
                addOutOwner(objectData, outOwner, outTenantId);
            }
        });
        batchUpdateRelevantTeam(user, objectDataList);
    }

    public void removeObjectInnerOwner(User user, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        removeObjectInnerOwner(objectDataList);
        batchUpdateRelevantTeam(user, objectDataList);
    }

    /**
     * 根据外部负责人与合作伙伴的匹配去决定是否移除合作伙伴字段，不更新db
     */
    public boolean removeObjectPartnerByOutOwnerNotUpdate(User user, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return false;
        }
        if (!isPartnerEnabled(user)) {
            return false;
        }
        List<IObjectData> removedPartnerObjectDataList = objectDataList.stream()
                .filter(d -> StringUtil.isNotBlank(d.getOutTenantId())
                        && StringUtil.isNotBlank(ObjectDataUtil.getStringValue(d, LtoFieldApiConstants.POOL_ID, "")))
                .collect(Collectors.toList());
        MatchOutOwnerResult matchOutOwnerResult = matchOutOwner(user, removedPartnerObjectDataList);
        List<String> notMatchIds = matchOutOwnerResult.getNotMatchData().stream().map(PartnerOutOwner::getObjectDataId).collect(Collectors.toList());
        objectDataList.stream().filter(o -> !notMatchIds.contains(o.getId())).forEach(objectData -> objectData.set(LtoFieldApiConstants.PARTNER_ID, null));
        return true;
    }

    /**
     * 移除外部相关团队中的负责人
     */
    public void removeObjectOutOwner(User user, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        boolean update = removeObjectOutOwnerNotUpdate(objectDataList);
        if (update) {
            ObjectDataUtil.updateFields(user, objectDataList, Lists.newArrayList(DBRecord.OUT_OWNER, ObjectDataExt.OUTER_TENANT));
            batchUpdateRelevantTeam(user, objectDataList);
        }
    }

    public void moveActionOutTeamHandle(User user, PoolEmptyRule.EmptyRule emptyRule, List<IObjectData> removedOutOwnerDataList) {
        if (CollectionUtils.empty(removedOutOwnerDataList)) {
            return;
        }
        outTeamHandle(user, emptyRule, removedOutOwnerDataList);
    }

    public void removedOutOrdinaryMember(User user, List<IObjectData> removedOutOwnerDataList) {
        boolean update = removedOutOrdinaryMemberNotUpdate(removedOutOwnerDataList);
        if (update) {
            batchUpdateRelevantTeam(user, removedOutOwnerDataList);
        }
    }

    /**
     * 移除外部负责人，外部相关团队中的负责人，但是不更新db
     */
    public boolean removeObjectOutOwnerNotUpdate(List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return false;
        }
        objectDataList.forEach(objectData -> {
            objectData.setOutTenantId(null);
            objectData.setOutOwner(Lists.newArrayList());
            ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
            List<TeamMember> teamMembers = objectDataExt.getTeamMembers();
            List<TeamMember> outOwnerMembers = teamMembers.stream()
                    .filter(f -> TeamMember.Role.OWNER.equals(f.getRole())
                            && f.isOutMember()).collect(Collectors.toList());
            for (TeamMember outOwnerMember : outOwnerMembers) {
                teamMembers.remove(outOwnerMember);
            }
            objectDataExt.setTeamMembers(teamMembers);
        });
        return true;
    }

    public void backActionOutTeamHandle(User user, PoolEmptyRule.EmptyRule emptyRule, List<IObjectData> removedOutOwnerDataList) {
        moveActionOutTeamHandle(user, emptyRule, removedOutOwnerDataList);
    }

    private void removeObjectOwner(List<IObjectData> objectDataList) {
        objectDataList.forEach(objectData -> {
            ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
            List<TeamMember> teamMembers = objectDataExt.getTeamMembers();
            List<TeamMember> deleteMember = teamMembers.stream()
                    .filter(f -> TeamMember.Role.OWNER.equals(f.getRole())).collect(Collectors.toList());
            teamMembers.removeAll(deleteMember);
            objectDataExt.setTeamMembers(teamMembers);
        });
    }

    private void removeObjectTeamMember(List<String> employeeIDs, IObjectData objectData) {
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        List<TeamMember> teamMembers = objectDataExt.getTeamMembers();
        for (String teamMemberId : employeeIDs) {
            List<TeamMember> deleteMember = teamMembers.stream()
                    .filter(f -> f.getEmployee().equals(teamMemberId)).collect(Collectors.toList());
            teamMembers.removeAll(deleteMember);
        }
        objectDataExt.setTeamMembers(teamMembers);
    }

    private void removeObjectInnerOwner(List<IObjectData> objectDataList) {
        objectDataList.forEach(objectData -> {
            ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
            List<TeamMember> teamMembers = objectDataExt.getTeamMembers();
            List<TeamMember> deleteMember = teamMembers.stream()
                    .filter(f -> !f.isOutMember() && TeamMember.Role.OWNER.equals(f.getRole())).collect(Collectors.toList());
            teamMembers.removeAll(deleteMember);
            objectDataExt.setTeamMembers(teamMembers);
        });
    }

    private void addObjectTeamMember(List<String> employeeIDs, String teamMemberRole, String permissionType, IObjectData objectData) {
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        for (String teamMemberId : employeeIDs) {
            objectDataExt.addTeamMembers(Lists.newArrayList(
                    new TeamMember(teamMemberId, teamMemberRole, TeamMember.Permission.of(permissionType), "", TeamMember.MemberType.EMPLOYEE, "")));
        }
    }

    private void addOutOwner(IObjectData objectData, String outOwner, String outTenantId) {
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        objectDataExt.addTeamMembers(Lists.newArrayList(
                new TeamMember(outOwner, TeamMember.Role.OWNER, TeamMember.Permission.READANDWRITE, outTenantId, TeamMember.MemberType.EMPLOYEE, "")));
    }

    private void batchUpdateRelevantTeam(User user, List<IObjectData> objectDataList) {
        try {
            ActionContextUtil.ActionContextOp op = ActionContextUtil.ActionContextOp.builder()
                    .updateLastModifyTime(true).notValidate(true)
                    .build();
            IActionContext actionContext = ActionContextUtil.createActionContext(user, op);
            objectDataService.batchUpdateRelevantTeam(objectDataList, actionContext);
        } catch (Exception e) {
            log.error("batchUpdateRelevantTeam error", e);
            ExceptionUtil.throwCommonBusinessException();
        }
    }

    private void outTeamHandle(User user, PoolEmptyRule.EmptyRule emptyRule, List<IObjectData> removedOutOwnerDataList) {
        removeObjectOutOwner(user, removedOutOwnerDataList);
        if (Boolean.TRUE.equals(emptyRule.getRecyclingOutOrdinaryMember())) {
            removedOutOrdinaryMember(user, removedOutOwnerDataList);
        }
    }

    private boolean removedOutOrdinaryMemberNotUpdate(List<IObjectData> removedOutOwnerDataList) {
        boolean update = false;
        for (IObjectData data : removedOutOwnerDataList) {
            ObjectDataExt objectDataExt = ObjectDataExt.of(data);
            List<TeamMember> teamMembers = objectDataExt.getTeamMembers();
            List<TeamMember> outMembers = teamMembers.stream()
                    .filter(f -> !TeamMember.Role.OWNER.equals(f.getRole())
                            && f.isOutMember()).collect(Collectors.toList());
            if (CollectionUtils.notEmpty(outMembers)) {
                update = true;
            }
            for (TeamMember outMember : outMembers) {
                teamMembers.remove(outMember);
            }
            objectDataExt.setTeamMembers(teamMembers);
        }
        return update;
    }

    private boolean isPartnerEnabled(User user) {
        String configValue = configService.findTenantConfig(user, "37");
        if ("1".equals(configValue)) {
            return true;
        }

        configValue = configService.findTenantConfig(user, "config_partner_open");
        return "1".equals(configValue);
    }

    public MatchOutOwnerResult matchOutOwner(User user, List<IObjectData> dataList) {
        MatchOutOwnerResult result = new MatchOutOwnerResult();
        result.setAllMatch(true);
        result.setNotMatchData(Lists.newArrayList());

        if (CollectionUtils.empty(dataList)) {
            return result;
        }
        List<IObjectData> hasOutOwnerDataList = dataList.stream().filter(d -> org.apache.commons.collections.CollectionUtils.isNotEmpty(d.getOutOwner())).collect(Collectors.toList());
        List<IObjectData> needCheckDataList = hasOutOwnerDataList.stream().filter(d -> StringUtils.isNotBlank(ObjectDataUtil.getStringValue(d, LtoFieldApiConstants.PARTNER_ID, ""))).collect(Collectors.toList());
        List<IObjectData> notMatchDataList = hasOutOwnerDataList.stream().filter(d -> StringUtils.isBlank(ObjectDataUtil.getStringValue(d, LtoFieldApiConstants.PARTNER_ID, ""))).collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(notMatchDataList)) {
            result.setAllMatch(false);
            for (IObjectData data : notMatchDataList) {
                PartnerOutOwner partnerOutOwner = new PartnerOutOwner(data.getId(), null, null);
                result.getNotMatchData().add(partnerOutOwner);
            }
        }
        List<String> partnerIds = needCheckDataList.stream().map(d -> ObjectDataUtil.getStringValue(d, LtoFieldApiConstants.PARTNER_ID, "")).collect(Collectors.toList());
        Map<String, RelationDownstreamResult> relationDataMap = ltoOrgCommonService.findRelationDownstreamVoByPartnerIds(user, Sets.newHashSet(partnerIds));
        for (IObjectData data : needCheckDataList) {
            String outOwner = data.getOutOwner().get(0).toString();
            String partnerId = data.get(LtoFieldApiConstants.PARTNER_ID, String.class);
            RelationDownstreamResult downstreamResult = relationDataMap.get(partnerId);
            Long outOwnerId = null;
            Integer outTenantId = null;
            if (downstreamResult != null) {
                outOwnerId = downstreamResult.getRelationOwnerOuterUid();
                outTenantId = downstreamResult.getDownstreamOuterTenantId();
            }
            if (!Long.valueOf(outOwner).equals(outOwnerId)) {
                result.setAllMatch(false);
                PartnerOutOwner partnerOutOwner = new PartnerOutOwner(data.getId(), outTenantId, outOwnerId);
                result.getNotMatchData().add(partnerOutOwner);
            }
        }
        return result;

    }

    @Data
    static class MatchOutOwnerResult {
        private Boolean allMatch;
        private List<PartnerOutOwner> notMatchData;
    }

    @Data
    static class PartnerOutOwner {
        private String objectDataId;
        /**
         * 下游企业外部账号
         */
        private Integer outTenantId;
        /**
         * 下游企业对接负责人外部Id
         */
        private Long outOwnerId;

        public PartnerOutOwner(String objectDataId, Integer outTenantId, Long outOwnerId) {
            this.objectDataId = objectDataId;
            this.outTenantId = outTenantId;
            this.outOwnerId = outOwnerId;
        }
    }
}
