package com.facishare.crm.sfa.lto.todo.model;

import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2020/7/7
 */
public interface LtoFindEmployeeIdsByEmployeeAndRolesModel {
  @Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  class Arg {
    private String employeeId;
    private List<String> roleIds;
  }


  @Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  class Result {
    private Map<String, List<String>> roleIdEmployeeIdsMap;

    public static Result empty(){
      Result result = new Result();
      result.setRoleIdEmployeeIdsMap(Maps.newHashMap());
      return result;
    }
  }
}
