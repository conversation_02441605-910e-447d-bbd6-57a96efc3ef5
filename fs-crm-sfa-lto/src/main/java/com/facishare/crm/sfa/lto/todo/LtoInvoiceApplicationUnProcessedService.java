package com.facishare.crm.sfa.lto.todo;

import com.facishare.crm.sfa.lto.todo.enums.LtoSessionBOCItemKeys;
import com.facishare.crm.sfa.lto.utils.TodoUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * Demo class
 *
 * <AUTHOR>
 * @date 2019/10/23
 */
@Service
@Slf4j
public class LtoInvoiceApplicationUnProcessedService implements LtoIUnProcessedService {
    @Autowired
    private ServiceFacade serviceFacade;

    @Override
    public String getObjectApiName() {
        return "InvoiceApplicationObj";
    }

    @Override
    public List<IObjectData> getTotalUnProcessedData(User user, LtoSessionBOCItemKeys sessionBOCItemKey) {
        List<IObjectData> dataList = Lists.newArrayList();
        SearchTemplateQuery query = new SearchTemplateQuery();
        TodoUtils.handleSearchQuery(query, user);
        query.setLimit(150);
        IObjectDescribe objectDescribe = this.serviceFacade.findObject(user.getTenantId(), getObjectApiName());
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryWithDeleted(user, objectDescribe, query);
        if (!Objects.isNull(queryResult)) {
            dataList = queryResult.getData();
        }
        return dataList;
    }
}
