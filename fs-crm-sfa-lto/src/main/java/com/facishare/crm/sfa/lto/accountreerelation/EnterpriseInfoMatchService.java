package com.facishare.crm.sfa.lto.accountreerelation;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.lto.accountreerelation.models.AccountTreeRelationConstants;
import com.facishare.crm.sfa.lto.accountreerelation.models.AccountTreeRelationModels;
import com.facishare.crm.sfa.lto.equityrelationship.model.EquityRelationshipDataModel;
import com.facishare.crm.sfa.lto.equityrelationship.service.EquityRelationshipService;
import com.facishare.crm.sfa.lto.utils.*;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.common.SqlEscaper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.lto.accountreerelation.models.AccountTreeRelationConstants.ENTERPRISE_ID;
import static com.facishare.crm.sfa.lto.accountreerelation.models.AccountTreeRelationConstants.NAME;


/**
 * <AUTHOR> lik
 * @date : 2024/6/7 17:35
 */
@Component
@Slf4j
public class EnterpriseInfoMatchService {
    @Autowired
    private ConfigService configService;
    @Autowired
    private EquityRelationshipService equityRelationshipService;
    @Autowired
    protected SpecialTableMapper SPECIAL_TABLE_MAPPER;


    //获取匹配规则
    public AccountTreeRelationModels.EnterpriseMatch getMatchRule(User user){
        String configValue = configService.findTenantConfig(user, AccountTreeRelationConstants.MATCH_ENTERPRISE_RULE_CONFIG_KEY);
        if(ObjectUtils.isEmpty(configValue)){
            return null;
        }
        return JSONObject.parseObject(configValue,AccountTreeRelationModels.EnterpriseMatch.class);
    }


    public void handleEnterpriseByMatchRule(User user,AccountTreeRelationModels.EnterpriseMatch enterpriseMatch, List<Map> result,String rootId,AccountTreeRelationModels.CreateAccountTreeRelationArg arg){
        if(ObjectUtils.isEmpty(enterpriseMatch) || CollectionUtil.isEmpty(result)){
            return;
        }
        List<Wheres> wheresList = CommonTreeRelationUtil.convert2WheresList(enterpriseMatch.getWhereString());
        List<AccountTreeRelationModels.EnterpriseMatchMapping> matchMappings = enterpriseMatch.getMappings();
        Map<String,String> matchMappingMap = matchMappings.stream().collect(Collectors.toMap(AccountTreeRelationModels.EnterpriseMatchMapping::getTargetField,
                AccountTreeRelationModels.EnterpriseMatchMapping::getOriginalField,(v1,v2)->v1));

        result.stream().forEach(r->{
            if(ObjectUtils.isEmpty(r.get(NAME))){
                return;
            }
            EquityRelationshipDataModel.CompanyDetail companyDetail = equityRelationshipService.queryCompanyDetailByNameOfMultipleRequests(user, r.get(NAME).toString());
            if(ObjectUtils.isEmpty(companyDetail)){
                return;
            }
            IObjectData objectData = AccountTreeRelationUtil.ofCompanyDetail(companyDetail);
            //检验所有的字段是否存在
            for(Wheres w:wheresList){
                List<IFilter> filters  = w.getFilters();
                for(IFilter f:filters){
                    if(!matchMappingMap.containsKey(f.getFieldName())){
                        log.warn("matchMappingMap is not have {}",f.getFieldName());
                        return;
                    }
                    String originalField =matchMappingMap.get(f.getFieldName());
                    if(!objectData.containsField(originalField) || ObjectUtils.isEmpty(objectData.get(originalField))){
                        log.warn("objectData is not have {}",f.getFieldName());
                        return;
                    }
                    f.setFieldValues(Lists.newArrayList(objectData.get(originalField).toString()));
                }
            }
            List<IObjectData> enterpriseInfoList = queryEnterpriseInfoObj(user.getTenantId(),wheresList);
            if(CollectionUtil.isEmpty(enterpriseInfoList)){
                return;
            }
            IObjectData enterpriseInfo = enterpriseInfoList.get(0);
            updateEnterpriseInfoId(user,enterpriseInfo.getId(), r.get(ENTERPRISE_ID).toString(),rootId,arg);
        });

    }

    private List<IObjectData> queryEnterpriseInfoObj(String tenantId, List<Wheres> wheresList) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setDataRightsParameter(null);
        searchTemplateQuery.setPermissionType(0);
        searchTemplateQuery.setNeedReturnCountNum(false);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, DBRecord.IS_DELETED, 0);
        searchTemplateQuery.setFilters(filters);
        List<Wheres> tempWhereList = Lists.newArrayList(wheresList);
        searchTemplateQuery.setWheres(tempWhereList);
        QueryResult<IObjectData> queryResult = ObjectDataUtil.findDataBySearchQuery(new User(tenantId, User.SUPPER_ADMIN_USER_ID), "EnterpriseInfoObj", searchTemplateQuery, null);
        return queryResult == null ? Lists.newArrayList() : queryResult.getData();
    }

    public void updateEnterpriseInfoId(User user, String enterpriseId, String oldEnterpriseId, String rootId, AccountTreeRelationModels.CreateAccountTreeRelationArg arg){
        if(enterpriseId.equals(oldEnterpriseId)){
            return;
        }
        //修改enterpriseId
        String upsql = String.format("update biz_equity_relation_temp set enterprise_id ='%s' where tenant_id='%s' and enterprise_id='%s'  and  root_id = '%s' and object_api_name='%s'",
                SqlEscaper.pg_escape(enterpriseId) ,SqlEscaper.pg_escape(user.getTenantId()),SqlEscaper.pg_escape(oldEnterpriseId),SqlEscaper.pg_escape(rootId),SqlEscaper.pg_escape(arg.getDescribeApiName()));
        SPECIAL_TABLE_MAPPER.setTenantId(user.getTenantId()).batchUpdateBySql(upsql);

        //修改 parent_enterprise_id  parent_enterprise_id
        updateParentIdAndRootIdOfEnterpriseInfo(user,enterpriseId,oldEnterpriseId,rootId,"parent_enterprise_id",arg);
        //修改 root_enterprise_id
        updateParentIdAndRootIdOfEnterpriseInfo(user,enterpriseId,oldEnterpriseId,rootId,"root_enterprise_id",arg);

    }
    public void updateParentIdAndRootIdOfEnterpriseInfo(User user, String enterpriseId, String oldEnterpriseId, String rootId,String updateField, AccountTreeRelationModels.CreateAccountTreeRelationArg arg){
        //修改 parent_enterprise_id
        String countSql = String.format("select count(1) as num from biz_equity_relation_temp where tenant_id='%s' and %s='%s'  and  root_id = '%s'  and object_api_name='%s'",
                SqlEscaper.pg_escape(user.getTenantId()),SqlEscaper.pg_escape(updateField),SqlEscaper.pg_escape(oldEnterpriseId),SqlEscaper.pg_escape(rootId),SqlEscaper.pg_escape(arg.getDescribeApiName()));
        List<Map> countResult = SPECIAL_TABLE_MAPPER.setTenantId(user.getTenantId()).findBySql(countSql);
        if(CollectionUtils.empty(countResult)){
            return;
        }
        Integer num = Integer.parseInt(countResult.get(0).get("num").toString());
        if(num == 0){
            return;
        }
        String upsql ="";
        if(num>500){
            //批量更新
            int index = 0;
            while(true){
                String findSql = String.format("select id from biz_equity_relation_temp where tenant_id='%s' and %s='%s'  and  root_id = '%s'  and object_api_name='%s' limit 200",
                        SqlEscaper.pg_escape(user.getTenantId()),SqlEscaper.pg_escape(updateField),SqlEscaper.pg_escape(oldEnterpriseId),SqlEscaper.pg_escape(rootId),SqlEscaper.pg_escape(arg.getDescribeApiName()));
                List<Map> findResult = SPECIAL_TABLE_MAPPER.setTenantId(user.getTenantId()).findBySql(findSql);
                if(CollectionUtils.empty(findResult)){
                    return;
                }
                List<String> ids = findResult.stream().map(m->m.get("id").toString()).collect(Collectors.toList());
                upsql = String.format("update biz_equity_relation_temp set %s ='%s' where tenant_id='%s' and id = %s  and  root_id = '%s'  and object_api_name='%s'",
                        SqlEscaper.pg_escape(updateField),SqlEscaper.pg_escape(enterpriseId) ,SqlEscaper.pg_escape(user.getTenantId()),SqlEscaper.any_clause(ids),SqlEscaper.pg_escape(rootId) ,SqlEscaper.pg_escape(arg.getDescribeApiName()));
                SPECIAL_TABLE_MAPPER.setTenantId(user.getTenantId()).batchUpdateBySql(upsql);
                index++;
                if(index>1000){
                    log.error("replaceEnterpriseInfoData 强硬跳出循环");
                    return;
                }
            }
        }else{
            //一次性更新
            upsql = String.format("update biz_equity_relation_temp set %s ='%s' where tenant_id='%s' and %s='%s'  and  root_id = '%s'  and object_api_name='%s'",
                    SqlEscaper.pg_escape(updateField),SqlEscaper.pg_escape(enterpriseId) ,SqlEscaper.pg_escape(user.getTenantId()),SqlEscaper.pg_escape(updateField),SqlEscaper.pg_escape(oldEnterpriseId),SqlEscaper.pg_escape(rootId),SqlEscaper.pg_escape(arg.getDescribeApiName()));
            SPECIAL_TABLE_MAPPER.setTenantId(user.getTenantId()).batchUpdateBySql(upsql);
        }

    }

    public void handleEnterpriseByName(User user, List<Map> result,String rootId,AccountTreeRelationModels.CreateAccountTreeRelationArg arg){
        Set<String> nameList = result.stream().map(x->x.get(NAME).toString()).collect(Collectors.toSet());
        if(CollectionUtils.empty(nameList)){
            return;
        }
        String queryString = String.format("SELECT id,name FROM biz_enterprise WHERE tenant_id=%s AND name = %s AND is_deleted>=0 ",
                SqlEscaper.pg_quote(user.getTenantId()), SqlEscaper.any_clause(nameList));
        List<Map> queryResult = CommonSqlUtil.findBySql(user.getTenantId(), queryString);
        if(CollectionUtils.empty(queryResult)){
            return;
        }
        Map<String,Map> nameMap = queryResult.stream().collect(Collectors.toMap(x->x.get(NAME).toString(), Function.identity(), (v1, v2) -> v1));
        result.forEach(x-> {
            String name = ObjectDataUtil.getStringValue(x, NAME, "");
            if (!nameMap.containsKey(name)) {
                return;
            }
            String enterpriseId = nameMap.get(name).get("id").toString();
            String equityRelationEnterpriseId = x.get(ENTERPRISE_ID).toString();
            updateEnterpriseInfoId(user,enterpriseId,equityRelationEnterpriseId,rootId,arg);
        });
    }


}
