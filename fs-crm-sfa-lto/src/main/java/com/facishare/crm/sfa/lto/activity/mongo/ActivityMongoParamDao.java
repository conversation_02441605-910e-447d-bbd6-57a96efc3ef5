package com.facishare.crm.sfa.lto.activity.mongo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/12/20 17:27
 * @description:
 */
public interface ActivityMongoParamDao {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class BatchReplaceParam {
        private String objectId;
        private String userName;
        private String userId;
        private String userApiName;
        private Integer nameAvaId;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class RecordingReplaceParam {
        private String objectId;
        private String userName;
        private List<Long> seqIds;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class UpdateContentParam {
        private String id;
        private String content;
        private String oldContent;
        // 翻译后内容
        private String translateContent;
        // 翻译后内容
        private String oldTranslateContent;
    }
}
