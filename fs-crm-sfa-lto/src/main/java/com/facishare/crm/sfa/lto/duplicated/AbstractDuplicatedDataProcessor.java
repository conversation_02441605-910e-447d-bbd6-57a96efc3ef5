package com.facishare.crm.sfa.lto.duplicated;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.lto.common.LtoRateLimiterService;
import com.facishare.crm.sfa.lto.common.models.LtoFieldApiConstants;
import com.facishare.crm.sfa.lto.duplicated.models.DuplicatedConstants;
import com.facishare.crm.sfa.lto.duplicated.models.DuplicatedModels;
import com.facishare.crm.sfa.lto.exception.ExceptionUtil;
import com.facishare.crm.sfa.lto.utils.CommonUtil;
import com.facishare.crm.sfa.lto.utils.GrayUtil;
import com.facishare.crm.sfa.lto.utils.ListsUtil;
import com.facishare.crm.sfa.lto.utils.ObjectDataUtil;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.MetaDataService;
import com.facishare.paas.foundation.boot.utility.JSON;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public abstract class AbstractDuplicatedDataProcessor implements DuplicatedDataProcessor {
    @Autowired
    protected RuleMatcher ruleMatcher;
    @Autowired
    protected DuplicatedSearchDelegate duplicatedSearchDelegate;
    @Autowired
    protected ServiceFacade serviceFacade;
    @Autowired
    protected MetaDataService metaDataService;
    @Autowired
    protected DuplicatedProcessingRuleService duplicatedProcessingRuleService;
    @Autowired
    protected ObjectDataServiceImpl objectDataService;
    @Autowired
    protected LtoRateLimiterService rateLimiterService;
    @Autowired
    protected ModeActionProcessorFactory modeActionProcessorFactory;

    protected static int batchSize = 100;

    static {
        ConfigFactory.getConfig("fs-crm-sales-config", config -> batchSize = config.getInt("duplicated_process_batch_size", 100));
    }

    /**
     * 处理重复数据入口方法
     *
     * @param processArg 需要处理的数据(single and batch)
     * @return
     */
    @Override
    public void process(DuplicatedDataProcessArg processArg) {
    }

    protected boolean isProcessingWithMode() {
        return false;
    }

    protected void process(User user, List<IObjectData> objectDataList,
                           List<DuplicatedModels.DuplicatedProcessing> processingRuleList,
                           DuplicatedModels.TriggerAction triggerAction, int refreshVersion) {
        if(skipLeadsDuplicated(user.getTenantId())) {
            return;
        }
        log.info("process-start");
        updateRefreshVersion(user, refreshVersion, objectDataList); //更新刷新版本号
        log.info("process-end-updateRefreshVersion");
        List<String> objectDataIdList = objectDataList.stream().map(IObjectData::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(processingRuleList)) {
            return;
        }
        //返回结构: key 处理规则id,value 符合处理规则的数据列表,如果是单独创建的数据,结果只有一个规则对应数量为 1 的数据列表
        Map<String, List<IObjectData>> matchMaps = ruleMatcher.matches(user.getTenantId(), Utils.LEADS_API_NAME, processingRuleList, objectDataList);
        log.debug("LeadsDuplicatedProcessing->process->matchMaps:" + JSON.serialize(matchMaps));
        if(MapUtils.isEmpty(matchMaps)) {
            log.info("LeadsDuplicatedProcessing->process matchMaps is empty {},{},processingRuleList:{}", user.getTenantId(), objectDataIdList, processingRuleList.size());
            return;
        }
        for (Map.Entry<String, List<IObjectData>> entry : matchMaps.entrySet()) {
            if(skipLeadsDuplicated(user.getTenantId())) {
                return;
            }
            String ruleId = entry.getKey();
            List<IObjectData> matchedDataList = entry.getValue();
            Optional<DuplicatedModels.DuplicatedProcessing> optional = processingRuleList.stream()
                    .filter(p -> ruleId.equals(p.getId())).findFirst();
            if (StringUtils.isBlank(ruleId)
                    || CollectionUtils.isEmpty(matchedDataList)
                    || !optional.isPresent()) {
                log.info("LeadsDuplicatedProcessing->processDataListWithMatchedProcessingRule skip... ruleId:{},ids:{}", ruleId, objectDataIdList);
                continue;
            }

            DuplicatedModels.DuplicatedProcessing processingRule = optional.get();
            log.info("LeadsDuplicatedProcessing->processDataListWithMatchedProcessingRule start... :{}", objectDataIdList);
            processDataListWithMatchedProcessingRule(user, processingRule, matchedDataList, triggerAction, refreshVersion);
            log.info("LeadsDuplicatedProcessing->processDataListWithMatchedProcessingRule finished... :{}", objectDataIdList);
        }
        log.info("LeadsDuplicatedProcessing->process finish...");
    }

    protected void processDataListWithMatchedProcessingRule(
            User user, DuplicatedModels.DuplicatedProcessing processingRule, List<IObjectData> dataList,
            DuplicatedModels.TriggerAction triggerAction, int refreshVersion) {
        List<DuplicatedModels.DuplicatedSearchRule> searchRuleList = processingRule.getDuplicatedSearchRules();
        List<DuplicatedModels.DuplicatedProcessingMode> modeList = processingRule.getDuplicatedProcessingModes();
        for (IObjectData objectData : dataList) {
            if(skipLeadsDuplicated(user.getTenantId())) {
                return;
            }
            //返回map结构: key 查重对象,value:重复数据列表
            Map<String, List<IObjectData>> duplicatedMap = duplicatedSearchDelegate.search(user, searchRuleList, objectData, null);
            log.debug("processDataListWithMatchedProcessingRule duplicatedMap: " + JSON.serialize(duplicatedMap));
            processDataWithProcessingMode(user, objectData, duplicatedMap,
                    processingRule.getId(), modeList, refreshVersion, triggerAction);
        }
    }

    /**
     * 使用查重出来的数据对处理方式进行匹配,匹配完进行相应的处理
     *
     * @param duplicatedMap      每个对象下的重复数据
     * @param processingModeList 处理方式列表
     * @return
     */
    protected void processDataWithProcessingMode(
            User user, IObjectData freshData, Map<String, List<IObjectData>> duplicatedMap, String processingRuleId,
            List<DuplicatedModels.DuplicatedProcessingMode> processingModeList, int refreshVersion,
            DuplicatedModels.TriggerAction triggerAction) {
        mark(user, freshData, duplicatedMap, processingRuleId, refreshVersion);
        if (!isProcessingWithMode()) {
            //后台任务刷新时只置重复标签
            return;
        }
        List<IObjectData> duplicatedDataList = duplicatedMap.get(Utils.LEADS_API_NAME);
        String freshDataId = freshData.getId();
        if (StringUtils.isNotEmpty(freshDataId) && CollectionUtils.isNotEmpty(duplicatedDataList)) {
            duplicatedDataList = duplicatedDataList.stream()
                    .filter(d -> StringUtils.isNotEmpty(d.getId()) && !freshDataId.equals(d.getId()))
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(duplicatedDataList)) {
            return;
        }
        Tuple<DuplicatedModels.DuplicatedProcessingMode, List<IObjectData>> tuple = ruleMatcher.matches(user.getTenantId(),
                Utils.LEADS_API_NAME, processingModeList, freshData, duplicatedDataList);

        if (tuple == null || tuple.getKey() == null) {
            return;
        }
        DuplicatedModels.DuplicatedProcessingMode mode = tuple.getKey();
        DuplicatedModels.ModeAction modeAction = mode.getModeAction();
        ModeActionProcessor modeActionProcessor = modeActionProcessorFactory.getModeActionProcessor(modeAction);
        if(modeActionProcessor != null) {
            modeActionProcessor.process(user, mode, freshData, tuple.getValue(), triggerAction);
        }
    }

    /**
     * 对查重出来的数据置重复标签
     *
     * @param objectData    线索新建的数据
     * @param duplicatedMap 各对象重复数据
     * @return
     */
    protected void mark(User user, IObjectData objectData, Map<String, List<IObjectData>> duplicatedMap,
                        String processingRuleId, int refreshVersion) {
        if (MapUtils.isNotEmpty(duplicatedMap)) {
            boolean isDuplicated = false;
            for (Map.Entry<String, List<IObjectData>> entry : duplicatedMap.entrySet()) {
                if(CollectionUtils.isNotEmpty(entry.getValue())) {
                    isDuplicated = true;
                    break;
                }
            }
            List<IObjectData> objectDataList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(duplicatedMap.get(Utils.LEADS_API_NAME))) {
                objectDataList.addAll(duplicatedMap.get(Utils.LEADS_API_NAME));
                duplicatedMap.get(Utils.LEADS_API_NAME).add(objectData);
            }
            objectDataList.add(objectData);
            updateDuplicatedField(user, objectDataList, isDuplicated);
            if(isDuplicated) {
                insertDuplicatedProcessingRelation(user,
                        Utils.LEADS_API_NAME, objectData.getId(), objectDataList, processingRuleId, refreshVersion);
            }
        } else if(StringUtils.isNotBlank(objectData.getId())) {
            updateDuplicatedField(user, false, Lists.newArrayList(objectData.getId()));
            return;
        }
    }

    private void updateRefreshVersion(User user, int refreshVersion, List<IObjectData> dataList) {
        if(CollectionUtils.isEmpty(dataList) || dataList.stream().noneMatch(x -> StringUtils.isNotBlank(x.getId()))) {
            return;
        }
        dataList.removeIf(x -> StringUtils.isBlank(x.getId()));
        List<List<IObjectData>> splitList = ListsUtil.splitList(dataList, batchSize);
        for (List<IObjectData> splitDataList : splitList) {
            splitDataList.forEach(d -> d.set("refresh_duplicated_version", refreshVersion));
            ObjectDataUtil.updateFields(user, splitDataList,
                    Lists.newArrayList("refresh_duplicated_version"), false, true);
        }
    }

    protected void updateDuplicatedField(User user, boolean isDuplicated, List<String> objectDataIdList) {
        if (CollectionUtils.isEmpty(objectDataIdList)) {
            return;
        }
        List<IObjectData> updateDataList = Lists.newArrayList();
        for (String objectDataId : objectDataIdList) {
            IObjectData objectData = ObjectDataUtil.buildObjectData(user.getTenantId(), Utils.LEADS_API_NAME, objectDataId);
            updateDataList.add(objectData);
        }
        updateDuplicatedField(user, updateDataList, isDuplicated);
    }

    protected void updateDuplicatedField(User user, List<IObjectData> objectDataList, boolean isDuplicated) {
        if (CollectionUtils.isEmpty(objectDataList)) {
            return;
        }
        objectDataList.forEach(x -> x.set(DuplicatedConstants.IS_DUPLICATED, isDuplicated));
        List<List<IObjectData>> splitList = ListsUtil.splitList(objectDataList, batchSize);
        for (List<IObjectData> splitDataList : splitList) {
            ObjectDataUtil.updateFields(user, splitDataList,
                    Lists.newArrayList(DuplicatedConstants.IS_DUPLICATED), false, true);
        }
    }

    public void insertDuplicatedProcessingRelation(
            User user, String objectApiName, String leadsId, List<IObjectData> objectDataList,
            String processingId, int refreshVersion) {
        List<String> dataIdList = objectDataList.stream()
                .map(DBRecord::getId).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dataIdList)) {
            return;
        }
        insertDuplicatedProcessingRelation(user, objectApiName, dataIdList, leadsId, processingId, refreshVersion);
    }

    public void insertDuplicatedProcessingRelation(
            User user, String objectApiName, List<String> dataIdList, String leadsId, String processingId,
            int refreshVersion) {
        if (CollectionUtils.isEmpty(dataIdList)) {
            return;
        }
        Set<String> dataIds = Sets.newHashSet(leadsId);
        dataIds.addAll(dataIdList);
        insertDuplicatedProcessingRelation(user, objectApiName, Lists.newArrayList(dataIds), processingId, refreshVersion);
    }

    public void insertDuplicatedProcessingRelation(
            User user, String objectApiName, List<String> dataIdList, String processingId, int refreshVersion) {
        if (CollectionUtils.isEmpty(dataIdList)) {
            return;
        }
        String duplicatedGroupId = getExtendObjectId(user.getTenantId(), objectApiName, dataIdList, processingId, refreshVersion);
        List<String> existedIdList = Lists.newArrayList();
        if(StringUtils.isBlank(duplicatedGroupId)) {
            duplicatedGroupId = serviceFacade.generateId();
        } else {
            existedIdList.addAll(getExistObjectIds(user.getTenantId(), objectApiName, processingId, duplicatedGroupId, refreshVersion, dataIdList));
        }
        List<Map<String, Object>> dataMaps = Lists.newArrayList();
        for (String dataId : dataIdList) {
            if (existedIdList.contains(dataId)) {
                continue;
            }
            Map<String, Object> dataMap = Maps.newHashMap();
            dataMap.put(LtoFieldApiConstants.OBJECT_API_NAME, objectApiName);
            dataMap.put(DuplicatedConstants.DUPLICATED_PROCESSING_ID, processingId);
            dataMap.put(LtoFieldApiConstants.OBJECT_ID, dataId);
            dataMap.put(DuplicatedConstants.EXTEND_OBJ_DATA_ID, duplicatedGroupId);
            dataMap.put(DuplicatedConstants.DUPLICATED_GROUP_ID, duplicatedGroupId);
            dataMap.put(DuplicatedConstants.REFRESH_VERSION, refreshVersion);
            dataMaps.add(dataMap);
        }
        if(CollectionUtils.isNotEmpty(dataMaps)) {
            DuplicatedUtil.doInsert(user, DuplicatedConstants.BIZ_DUPLICATED_PROCESSING_RELATION_TABLE, dataMaps, batchSize);
        }
    }

    private String getExtendObjectId(
            String tenantId, String objectApiName, List<String> dataIds, String processingId, int refreshVersion) {
        if(CollectionUtils.isEmpty(dataIds)) {
            return null;
        }
        try {
            List<List<String>> splitList = ListsUtil.splitList(dataIds, batchSize);
            for(List<String> splitDataIds : splitList) {
                String dataIdString = CommonUtil.buildSqlInString(splitDataIds);
                String queryString = String.format("SELECT duplicated_group_id FROM biz_duplicated_processing_relation WHERE tenant_id='%s' " +
                                "AND object_id= any(array[%s]) AND duplicated_processing_id='%s' AND object_api_name='%s' AND refresh_version=%s AND is_deleted=0 " +
                                "AND duplicated_group_id IS NOT NULL AND duplicated_group_id<>'' " +
                                "ORDER BY create_time DESC LIMIT 1",
                        tenantId, dataIdString, processingId, objectApiName, refreshVersion);
                List<Map> queryResult = objectDataService.findBySql(tenantId, queryString);
                if(CollectionUtils.isEmpty(queryResult) || queryResult.get(0) == null || queryResult.get(0).get(DuplicatedConstants.DUPLICATED_GROUP_ID) == null) {
                    continue;
                }
                return queryResult.get(0).get(DuplicatedConstants.DUPLICATED_GROUP_ID).toString();
            }
        } catch (Exception e) {
            log.error("LeadsDuplicatedProcessingService getExtendObjectId error", e);
            ExceptionUtil.throwCommonBusinessException();
        }
        return null;
    }

    private List<String> getExistObjectIds(
            String tenantId, String objectApiName, String processingId, String duplicatedGroupId,
            int refreshVersion, List<String> dataIds) {
        if(CollectionUtils.isEmpty(dataIds)) {
            return Lists.newArrayList();
        }
        List<String> existIds = Lists.newArrayList();
        try {
            List<List<String>> splitList = ListsUtil.splitList(dataIds, batchSize);
            for (List<String> splitDataIds : splitList) {
                String dataIdString = CommonUtil.buildSqlInString(splitDataIds);
                String queryString = String.format("SELECT object_id FROM biz_duplicated_processing_relation WHERE tenant_id='%s' " +
                                "AND object_id= any(array[%s]) AND duplicated_processing_id='%s' AND duplicated_group_id='%s' AND object_api_name='%s' AND refresh_version=%s AND is_deleted=0 ",
                        tenantId, dataIdString, processingId, duplicatedGroupId, objectApiName, refreshVersion);
                List<Map> queryResult = objectDataService.findBySql(tenantId, queryString);
                if(CollectionUtils.isNotEmpty(queryResult)) {
                    existIds.addAll(queryResult.stream().map(x -> String.valueOf(x.get("object_id"))).collect(Collectors.toList()));
                }
            }
        } catch (Exception e) {
            log.error("getExistObjectIds getExtendObjectIds error", e);
            ExceptionUtil.throwCommonBusinessException();
        }
        return existIds;
    }

    protected boolean skipLeadsDuplicated(String tenantId) {
        return GrayUtil.isGrayEnable("skip_leads_duplicated_tenant_id", tenantId);
    }
}

