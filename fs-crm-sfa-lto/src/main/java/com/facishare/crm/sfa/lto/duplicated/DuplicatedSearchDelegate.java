package com.facishare.crm.sfa.lto.duplicated;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.lto.duplicated.models.DuplicatedModels;
import com.facishare.crm.sfa.lto.utils.LeadsPoolUtil;
import com.facishare.crm.sfa.lto.utils.ObjectDataUtil;
import com.facishare.crm.sfa.lto.utils.ObjectDescribeUtils;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.*;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IObjectReferenceField;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
public class DuplicatedSearchDelegate {
    @Autowired
    ServiceFacade serviceFacade;

    private static int maxSearchNumber = 1000;
    private static int pageSize = 500;

    static {
        ConfigFactory.getConfig("fs-crm-sales-config", config -> pageSize = config.getInt("duplicated_process_page_size", 500));
        ConfigFactory.getConfig("fs-crm-sales-config", config -> maxSearchNumber = config.getInt("duplicated_process_max_search_size", 500));
    }

    public Map<String, List<IObjectData>> search(User user,
                                                 List<DuplicatedModels.DuplicatedSearchRule> searchRuleList,
                                                 IObjectData objectData, List<String> fieldNameList) {
        Map<String, List<IObjectData>> result = Maps.newHashMap();
        for (DuplicatedModels.DuplicatedSearchRule searchRule : searchRuleList) {
            //所属对象
            String objectApiName = searchRule.getObjectApiName();
            result.computeIfAbsent(objectApiName, key -> Lists.newArrayList());
            //具体生效的查重规则(条件)
            List<DuplicatedModels.UsableRule> usableRuleList = searchRule.getUsableRules();
            SearchTemplateQuery searchTemplateQuery = buildSearchTemplateQuery(user,objectApiName, usableRuleList,
                    objectData, null);
            int limit = pageSize;
            int searchNumber = DuplicatedSearchDelegate.maxSearchNumber;
            if (!Utils.LEADS_API_NAME.equals(objectApiName)) {
                limit = 2; //同时查重其它对象，不需要置重复标签，只需查出有无重复数据即可，提高性能
                searchNumber = 1;
            }
            log.warn("DuplicatedSearchDelegate searchTemplateQuery:{}", JSON.toJSONString(searchTemplateQuery));
            List<IObjectData> objectDataList = getDataListBySearchQuery(user, objectApiName, searchTemplateQuery, limit, searchNumber, fieldNameList);
            if(CollectionUtils.isNotEmpty(objectDataList) && StringUtils.isNotBlank(objectData.getId())) {
                objectDataList.removeIf(x -> objectData.getId().equals(x.getId()));
            }
            result.get(objectApiName).addAll(objectDataList);
        }
        return result;
    }

    public List<IObjectData> search(User user, DuplicatedModels.DuplicatedSearchRule searchRule,
                                           IObjectData objectData, int maxSearchNumber,
                                           List<DuplicatedModels.Filter> processingFilters, List<String> fieldNameList) {
        String apiName = searchRule.getObjectApiName();
        //具体生效的查重规则(条件)
        List<DuplicatedModels.UsableRule> usableRuleList = searchRule.getUsableRules();
        SearchTemplateQuery searchTemplateQuery = null;
        if (LeadsPoolUtil.isGraySfaLeadsDuplicateEmpty(user.getTenantId())) {
            searchTemplateQuery = buildSearchTemplateQueryWithGray(user,apiName, usableRuleList, objectData, processingFilters);
        } else {
            searchTemplateQuery = buildSearchTemplateQuery(user,apiName, usableRuleList, objectData, processingFilters);
        }
        List<IObjectData> objectDataList = getDataListBySearchQuery(user, apiName, searchTemplateQuery, pageSize, maxSearchNumber, fieldNameList);
        if(CollectionUtils.isNotEmpty(objectDataList) && StringUtils.isNotBlank(objectData.getId())) {
            objectDataList.removeIf(x -> objectData.getId().equals(x.getId()));
        }
        return objectDataList;
    }

    private List<IObjectData> getDataListBySearchQuery(User user,String apiName, SearchTemplateQuery searchTemplateQuery,
                                                       int limit, int maxSearchNumber, List<String> fieldNameList) {
        List<IObjectData> result = Lists.newArrayList();
        if(searchTemplateQuery == null) {
            return result;
        }

        int count = maxSearchNumber / limit + 1;
        for (int i = 0; i < count; i++) {
            int offset = i * limit;
            SearchTemplateQuery templateSearchQuery = SearchUtil.copySearchTemplateQuery(searchTemplateQuery);
            templateSearchQuery.setLimit(limit);
            templateSearchQuery.setOffset(offset);
            QueryResult<IObjectData> queryResult = findData(user, apiName, templateSearchQuery, fieldNameList);
            if(queryResult == null || CollectionUtils.isEmpty(queryResult.getData())) {
                break;
            }

            result.addAll(queryResult.getData());
        }
        return result;
    }

    private  String populateFilters(List<IFilter> filters, String pattern,
                                    List<DuplicatedModels.Filter> processingFilters, int index) {
        if (CollectionUtils.isEmpty(processingFilters)) {
            return pattern;
        }
        StringBuilder patternBuilder = new StringBuilder();
        for (DuplicatedModels.Filter f : processingFilters) {
            List<DuplicatedModels.Condition> conditions = f.getConditions();
            StringBuilder andPatternBuilder = new StringBuilder("(");
            for (DuplicatedModels.Condition condition : conditions) {
                String fieldName = condition.getFieldName();
                String operator = condition.getOperator();

                if(StringUtils.isBlank(fieldName)
                        || !EnumUtils.isValidEnum(Operator.class, operator)) {
                    continue;
                }
                List<String> values = condition.getFieldValues();
                index++;
                SearchUtil.fillFilterEq(filters, fieldName, values);
                andPatternBuilder.append(index + " AND ");
            }
            int len = andPatternBuilder.length();
            if (len > 4) {
                andPatternBuilder.delete(len - 4, len);
                andPatternBuilder.append(")");
                patternBuilder.append(andPatternBuilder + " OR ");
            }
        }
        int len = patternBuilder.length();
        if (len > 4) {
            String pattern1 = patternBuilder.delete(len - 4, len).toString();
            pattern1 = "(" + pattern1 + ")";
            pattern = pattern + " AND " + pattern1;
        }
        return pattern;
    }

    protected SearchTemplateQuery buildSearchTemplateQuery(User user,String objectApiName,
                                                           List<DuplicatedModels.UsableRule> usableRuleList,
                                                           IObjectData objectData,
                                                           List<DuplicatedModels.Filter> processingFilters
                                                           ) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        //objectData 所属的对象作为源对象
        String actualObjApiName = objectData.getDescribeApiName();
        String ruleObjApiName = objectApiName;
        MapResult mapResult= getFieldMap(user,actualObjApiName, ruleObjApiName, usableRuleList);
        Map<String,Map<String,String>> fieldOptionMap=mapResult.getFieldOptionMap();
        //返回结果：key:目标对象的字段,value:源对象(真实对象)的字段
        Map<String, String> fieldMap = mapResult.getFieldMap();
        StringBuilder patternBuilder = new StringBuilder();
        IObjectDescribe objectDescribe = ObjectDescribeUtils.getObjectDescribe(user.getTenantId(), objectApiName);
        int index = 0; 
        for (DuplicatedModels.UsableRule usableRule : usableRuleList) {
            List<DuplicatedModels.SearchCondition> conditions = usableRule.getConditions();
            StringBuilder andPatternBuilder = new StringBuilder("(");
            for (DuplicatedModels.SearchCondition condition : conditions) {
                String fieldName = condition.getFieldName();
                if(StringUtils.isBlank(fieldName)
                        || StringUtils.isBlank(fieldMap.get(fieldName))) {
                    continue;
                }
                String actualFieldName = fieldMap.get(fieldName);
                Object v =getSearchValue(actualFieldName, objectData.get(actualFieldName),fieldOptionMap);
                if(null == v) {
                    continue;
                }
                index++;
                IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(fieldName);
                if(fieldDescribe != null && fieldDescribe instanceof IObjectReferenceField) {
                    fieldName = fieldName + ".name";
                }
                SearchUtil.fillFilterEq(filters, fieldName, v);
                andPatternBuilder.append(index + " AND ");
            }
            int len = andPatternBuilder.length();
            if(len> 4) {
                andPatternBuilder.delete(len - 4, len);
                andPatternBuilder.append(")");
                patternBuilder.append(andPatternBuilder.toString() + " OR ");
            }
        }
        if(CollectionUtils.isEmpty(filters)) {
            return null;
        }
        int len = patternBuilder.length();
        if(len>4) {
            String pattern = patternBuilder.delete(len - 4, len).toString();
            pattern ="(" + pattern +")";
//            if (actualObjApiName.equals(ruleObjApiName)
//                    && StringUtils.isNotBlank(objectData.getId())) {
//                if(searchCurrentData) {
//                    SearchUtil.fillFilterEq(filters, "_id", objectData.getId());
//                } else {
//                    SearchUtil.fillFilterNotEq(filters, "_id", objectData.getId());
//                }
//                index++;
//                pattern += " AND " + index;
//            }

            pattern = populateFilters(filters,pattern,processingFilters,index);
            searchTemplateQuery.setDataRightsParameter(null);
            searchTemplateQuery.setPermissionType(0);
            searchTemplateQuery.setFilters(filters);
            searchTemplateQuery.setPattern(pattern);
            searchTemplateQuery.setNeedReturnCountNum(false);
//            searchTemplateQuery.setSearchSource("db");
        }
        return searchTemplateQuery;
    }

    protected SearchTemplateQuery buildSearchTemplateQueryWithGray(User user,String objectApiName,
                                                           List<DuplicatedModels.UsableRule> usableRuleList,
                                                           IObjectData objectData,
                                                           List<DuplicatedModels.Filter> processingFilters
                                                           ) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        //objectData 所属的对象作为源对象
        String actualObjApiName = objectData.getDescribeApiName();
        String ruleObjApiName = objectApiName;
        MapResult mapResult= getFieldMap(user,actualObjApiName, ruleObjApiName, usableRuleList);
        Map<String,Map<String,String>> fieldOptionMap=mapResult.getFieldOptionMap();
        //返回结果：key:目标对象的字段,value:源对象(真实对象)的字段
        Map<String, String> fieldMap = mapResult.getFieldMap();
        StringBuilder patternBuilder = new StringBuilder();
        IObjectDescribe objectDescribe = ObjectDescribeUtils.getObjectDescribe(user.getTenantId(), objectApiName);
        int index = 0;
        for (DuplicatedModels.UsableRule usableRule : usableRuleList) {
            List<DuplicatedModels.SearchCondition> conditions = usableRule.getConditions();
            StringBuilder andPatternBuilder = new StringBuilder("(");
            
            // 先检查是否存在非空值
            boolean hasNonNullValue = false;
            boolean allValuesNull = true;
            
            for (DuplicatedModels.SearchCondition condition : conditions) {
                String fieldName = condition.getFieldName();
                if(StringUtils.isBlank(fieldName) || StringUtils.isBlank(fieldMap.get(fieldName))) {
                    continue;
                }
                String actualFieldName = fieldMap.get(fieldName);
                Object v = getSearchValue(actualFieldName, objectData.get(actualFieldName), fieldOptionMap);
                if(v != null) {
                    hasNonNullValue = true;
                    allValuesNull = false;
                    break;
                }
            }
            
            // 如果全部为空，则跳过此规则
            if(allValuesNull) {
                continue;
            }
            
            // 处理条件
            for (DuplicatedModels.SearchCondition condition : conditions) {
                String fieldName = condition.getFieldName();
                if(StringUtils.isBlank(fieldName) || StringUtils.isBlank(fieldMap.get(fieldName))) {
                    continue;
                }
                String actualFieldName = fieldMap.get(fieldName);
                Object v = getSearchValue(actualFieldName, objectData.get(actualFieldName), fieldOptionMap);
                
                index++;
                IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(fieldName);
                if(fieldDescribe != null && fieldDescribe instanceof IObjectReferenceField) {
                    fieldName = fieldName + ".name";
                }
                
                if(v == null && hasNonNullValue) {
                    // 如果当前值为空，但存在其他非空值，则使用isNull过滤
                    SearchUtil.fillFilterIsNull(filters, fieldName);
                } else if(v != null) {
                    // 如果不为空，保持原逻辑
                    SearchUtil.fillFilterEq(filters, fieldName, v);
                } else {
                    // 如果全部为空，则不添加过滤条件
                    index--;
                    continue;
                }
                
                andPatternBuilder.append(index + " AND ");
            }
            
            int len = andPatternBuilder.length();
            if(len > 4) {
                andPatternBuilder.delete(len - 4, len);
                andPatternBuilder.append(")");
                patternBuilder.append(andPatternBuilder.toString() + " OR ");
            }
        }
        
        if(CollectionUtils.isEmpty(filters)) {
            return null;
        }
        
        int len = patternBuilder.length();
        if(len > 4) {
            String pattern = patternBuilder.delete(len - 4, len).toString();
            pattern = "(" + pattern + ")";
//            if (actualObjApiName.equals(ruleObjApiName)
//                    && StringUtils.isNotBlank(objectData.getId())) {
//                if(searchCurrentData) {
//                    SearchUtil.fillFilterEq(filters, "_id", objectData.getId());
//                } else {
//                    SearchUtil.fillFilterNotEq(filters, "_id", objectData.getId());
//                }
//                index++;
//                pattern += " AND " + index;
//            }

            pattern = populateFilters(filters, pattern, processingFilters, index);
            searchTemplateQuery.setDataRightsParameter(null);
            searchTemplateQuery.setPermissionType(0);
            searchTemplateQuery.setFilters(filters);
            searchTemplateQuery.setPattern(pattern);
            searchTemplateQuery.setNeedReturnCountNum(false);
//            searchTemplateQuery.setSearchSource("db");
        }
        return searchTemplateQuery;
    }

    private Object getSearchValue(String fieldName,Object v,Map<String,Map<String,String>> fieldOptionMap) {
        if (fieldOptionMap != null && fieldOptionMap.containsKey(fieldName)
                && MapUtils.isNotEmpty(fieldOptionMap.get(fieldName))) {
            Map<String, String> optionMap = fieldOptionMap.get(fieldName);
            if (optionMap.containsKey(v)) {
                v = optionMap.get(v);
            }
        }
        return v;
    }

    /**
     * 获取当前对象查重规则字段 和源对象字段的对应关系，查重规则字段属于目标对象字段，源对象和目标对象由参数指定
     * @param actualObjectApiName  实际有数据的对象，要查重的源对象
     * @param ruleObjectApiName   查重规则要查的对象（目标对象）
     * @param usableRuleList
     * @return
     */
    public MapResult getFieldMap(User user,String actualObjectApiName, String ruleObjectApiName,
                                 List<DuplicatedModels.UsableRule> usableRuleList) {
        MapResult mapResult = new MapResult();
        Map<String, String> fieldMap = Maps.newHashMap();
        Set<String> fieldList = Sets.newHashSet();
        Map<String, Map<String, String>> fieldOptionMap = Maps.newHashMap();
        if (StringUtils.isEmpty(actualObjectApiName) || StringUtils.isEmpty(ruleObjectApiName)) {
            return mapResult;
        }
        //映射规则属于目标对象
        usableRuleList.stream()
                .map(r -> r.getConditions().stream()
                        .map(DuplicatedModels.SearchCondition::getFieldName)
                        .collect(Collectors.toList()))
                .forEach(a -> {
                    if (CollectionUtils.isNotEmpty(a))
                        fieldList.addAll(a);
                });
        if (actualObjectApiName.equals(ruleObjectApiName)) {
            fieldList.stream().forEach(f -> fieldMap.put(f, f));
            mapResult.setFieldMap(fieldMap);
            return mapResult;
        }
        List<IObjectMappingRuleInfo> mappingRuleInfos = DuplicatedUtil.getMappingRuleInfoList(user, actualObjectApiName, ruleObjectApiName);
        if (CollectionUtils.isNotEmpty(mappingRuleInfos)) {
            IObjectMappingRuleInfo mappingRuleInfo = mappingRuleInfos.get(0);
            for (String ruleFieldName : fieldList) {
                IObjectMappingRuleDetailInfo ruleDetail= getMappingRuleDetail(ruleObjectApiName,ruleFieldName,  mappingRuleInfo);
                if(ruleDetail!=null) {
                    String actualFieldName;
                    Map<String, String> optionMap = Maps.newHashMap();
                    if (Utils.LEADS_API_NAME.equals(ruleObjectApiName)) { //查重规则所属对象 为线索时，是映射规则的源端
                        actualFieldName = ruleDetail.getTargetFieldName();
                        if (CollectionUtils.isNotEmpty(ruleDetail.getOptionMapping()))
                            ruleDetail.getOptionMapping()
                                    .stream()
                                    .forEach(x -> optionMap.put(x.getTargetEnumCode(), x.getSourceEnumCode())); // optionMap key 是实际对象 选项值，value 是查重规则 的选项值
                        fieldOptionMap.put(actualFieldName, optionMap);
                    } else {
                        actualFieldName = ruleDetail.getSourceFieldName();
                        if (CollectionUtils.isNotEmpty(ruleDetail.getOptionMapping()))
                            ruleDetail.getOptionMapping().stream()
                                    .forEach(x -> optionMap.put(x.getSourceEnumCode(),x.getTargetEnumCode()));
                        fieldOptionMap.put(actualFieldName, optionMap);
                    }
                    fieldMap.put(ruleFieldName, actualFieldName);
                } else {
                    //如果在线索中找不到映射字段,置null值
                    fieldMap.put(ruleFieldName, null);
                }
            }
        }
        mapResult.setFieldMap(fieldMap);
        mapResult.setFieldOptionMap(fieldOptionMap);
        return mapResult;
    }

    private IObjectMappingRuleDetailInfo  getMappingRuleDetail(String ruleObjectApiName, String ruleFieldName,
                                                               IObjectMappingRuleInfo mappingRuleInfo) {
        //映射规则 源对象是线索，目标对象是客户，或者联系人
        List<IObjectMappingRuleDetailInfo> objectMapping = mappingRuleInfo.getFieldMapping();

        //当目标对象不是线索时，取映射规则的目标字段和规则字段作比较。当目标对象是线索时，取映射规则的源字段和规则字段作比较。 找出规则字段对应的真实需要字段
        if(CollectionUtils.isEmpty(objectMapping)) {
            return null;
        }
        Optional<IObjectMappingRuleDetailInfo> optional = objectMapping
                .stream()
                .filter(m -> ruleFieldName.equals(Utils.LEADS_API_NAME.equals(ruleObjectApiName)
                        ? m.getSourceFieldName() : m.getTargetFieldName()))
                .findFirst();
        if (Utils.CONTACT_API_NAME.equals(ruleObjectApiName)
                && ("mobile".equals(ruleFieldName) || "tel".equals(ruleObjectApiName))) {
            List<String> list;
            if ("mobile".equals(ruleFieldName)) {
                list = Lists.newArrayList("mobile1", "mobile2", "mobile3", "mobile4", "mobile5");
                optional = objectMapping
                        .stream()
                        .filter(m -> list.contains(m.getTargetFieldName()))
                        .findFirst();
            } else if ("tel".equals(ruleFieldName)) {
                list = Lists.newArrayList("tel1", "tel2", "tel3", "tel4", "tel5");
                optional = objectMapping
                        .stream()
                        .filter(m -> list.contains(m.getTargetFieldName()))
                        .findFirst();
            }
        }
        IObjectMappingRuleDetailInfo ruleDetail = null;
        if (optional.isPresent()) {
            ruleDetail = optional.get();
        }
        return ruleDetail;
    }

    private QueryResult<IObjectData> findData(User user, String apiName, SearchTemplateQuery query, List<String> fieldNameList) {
        if (true) {//暂时认为只查询非作废数据,有需求时放开
            return ObjectDataUtil.findDataBySearchQuery(user, apiName, query, fieldNameList);
        } else {
            IObjectDescribe describe = serviceFacade.findObject(user.getTenantId(), apiName);
            IFilter filter = new Filter();
            filter.setFieldName("is_deleted");
            filter.setOperator(Operator.IN);
            filter.setFieldValues(Lists.newArrayList(String.valueOf(DELETE_STATUS.NORMAL.getValue()),
                    String.valueOf(DELETE_STATUS.INVALID.getValue())));
            query.addFilters(Lists.newArrayList(filter));
            return this.serviceFacade.findBySearchQueryWithDeleted(user, describe, query);
        }
    }

    @Data
    static  class  MapResult
    {
        public Map<String,String> fieldMap;
        public Map<String,Map<String,String>> fieldOptionMap;
    }
}