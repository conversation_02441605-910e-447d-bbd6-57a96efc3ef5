package com.facishare.crm.sfa.lto.business.models;

import com.facishare.paas.appframework.core.model.ButtonDocument;
import com.facishare.paas.appframework.core.model.MappingRuleDocument;
import com.fxiaoke.crmrestapi.arg.CreateRuleArg;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import org.codehaus.jackson.annotate.JsonProperty;

import java.util.List;
import java.util.Map;

/**
 * Created by zhaopx on 2018/1/25.
 */
public interface CreateRuleModel {
    @Data
    class Arg {
        @JsonProperty("rule_list")
        @SerializedName("rule_list")
        private List<MappingRuleDocument> ruleList;

        @JsonProperty("button")
        @SerializedName("button")
        private ButtonDocument button;

        @JsonProperty("describe_api_name")
        @SerializedName("describe_api_name")
        private String describeApiName;

        @JsonProperty("roles")
        @SerializedName("roles")
        private List<String> roles;
    }

    @Builder
    @Data
    class Result {
        @JsonProperty("rule_list")
        @SerializedName("rule_list")
        private List<MappingRuleDocument> ruleList;
    }
}
