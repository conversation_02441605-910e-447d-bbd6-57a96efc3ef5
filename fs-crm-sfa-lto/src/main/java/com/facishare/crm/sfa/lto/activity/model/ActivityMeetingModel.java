package com.facishare.crm.sfa.lto.activity.model;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

public interface ActivityMeetingModel {

    @Data
    class CreateMeetingArg {
        private String objectId;
        private String objectApiName;
        private MeetingType meetingType;
        private String createUserName;
        private CreateScheduleArg data;
    }

    @Data
    class GetUrlArg {
        private String objectId;
        private String meetingId;
        private MeetingType meetingType;
    }

    enum MeetingType {
        /**
         * 腾讯会议-预约会议
         */
        tencent_scheduled,
        /**
         * 腾讯会议-快速会议
         */
        tencent_quick,
        /**
         * zoom
         */
        zoom;

        public boolean isTencent() {
            return tencent_scheduled.equals(this) || tencent_quick.equals(this);
        }

        public boolean isTencentScheduled() {
            return tencent_scheduled.equals(this);
        }

        public boolean isZoom() {
            return zoom == this;
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class CreateScheduleArg {
        private boolean fillMultiLang;
        private Map<String, Object> maskFieldApiNames;
        @JsonProperty("object_data")
        @SerializedName("object_data")
        private ObjectDataDocument objectData;
        private Map<String, Object> optionInfo;
        private String seriesId;
    }

    @Data
    class CreateScheduleResult {
        private Boolean isDuplicate;
        private ObjectDataDocument newObjectData;
        private ObjectDataDocument objectData;
        private Boolean triggerApproval;
        private Boolean writeDB;
    }

    @Data
    class Result<T> {
        private String code;
        private String errorMsg;
        private T data;

        public static <T> Result<T> error(String errorMsg) {
            Result<T> result = new Result<>();
            result.setCode("500");
            result.setErrorMsg(errorMsg);
            return result;
        }

        public static <T> Result<T> success(T data) {
            Result<T> result = new Result<>();
            result.setCode("200");
            result.setData(data);
            return result;
        }

        public boolean isSuccess() {
            return "200".equals(code);
        }
    }

}
