package com.facishare.crm.sfa.prm.api.dto;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-02-28
 * ============================================================
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AdmissionConfigDTO {
    private String channelAccess;
    private String channelMode;
    private String applyToApp;
    private String relatedObjectApiName;
    @Builder.Default
    private List<String> applyModules = Lists.newArrayList();
}
