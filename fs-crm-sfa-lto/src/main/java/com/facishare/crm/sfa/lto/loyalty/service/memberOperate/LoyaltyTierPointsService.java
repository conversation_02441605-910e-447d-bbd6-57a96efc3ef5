package com.facishare.crm.sfa.lto.loyalty.service.memberOperate;


import com.facishare.crm.sfa.lto.loyalty.constants.LoyaltyConstants;
import com.facishare.crm.sfa.lto.loyalty.i18n.LoyaltyI18nException;
import com.facishare.crm.sfa.lto.loyalty.model.Loyalty;
import com.facishare.crm.sfa.lto.loyalty.service.LoyaltyMemberService;
import com.facishare.crm.sfa.lto.loyalty.service.memberTierStrategy.IMemberTierStrategy;
import com.facishare.crm.sfa.lto.loyalty.utils.LoyaltyI18nKey;
import com.facishare.crm.sfa.lto.loyalty.utils.NumberUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.common.StopWatch;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

@Service
public class LoyaltyTierPointsService extends AbstractLoyaltyPointsOperateService {

    @Resource
    LoyaltyMemberService loyaltyMemberService;

    @Override
    public Loyalty.PointsOperationParam.Type type() {
        return Loyalty.PointsOperationParam.Type.LEVEL_POINTS;
    }

    @Transactional
    @Override
    public void action(Loyalty.PointsOperationParam param) {
        String tenantId = param.getTenantId();
        String memberId = param.getMemberId();
        StopWatch stopWatch = StopWatch.createStarted("定级积分变更");
        if (StringUtils.isEmpty(param.getPointTypeId())) {
            throw LoyaltyI18nException.buildMissingParametersException("pointTypeId");
        }
        if (param.getValue() >= 0) {
            ObjectDataDocument pointsDetail = loyaltyPointsDetailService.saveLoyaltyPointsDetail(param);
            stopWatch.lap("插入积分明细");
            ObjectDataDocument recordPointsChange = new ObjectDataDocument();
            recordPointsChange.put(LoyaltyConstants.LoyaltyMemberChangeRecords.MEMBER_POINTS_DETAIL_ID, pointsDetail.getId());
            recordPointsChange.put(LoyaltyConstants.LoyaltyMemberChangeRecords.LOY_CHANGE_VALUE, param.getValue());
            recordPointsChange.put(LoyaltyConstants.LoyaltyMemberChangeRecords.LOY_CHANGE_TYPE, Loyalty.PointsOperationParam.Type.LEVEL_POINTS.toString());
            saveMemberChangeRecord(param, Lists.newArrayList(recordPointsChange));
            stopWatch.lap("插入操作记录");
        } else {
            List<IObjectData> pointsDetaislList = loyaltyPointsDetailService.findMemberPointsDetails(param, true);
            stopWatch.lap("按优先级顺序查询积分明细列表");
            List<ObjectDataDocument> insertRecordlList = updateLoyaltyPointsDetail(tenantId, Math.abs(param.getValue()), param.getPointPoolId(), pointsDetaislList);
            for (ObjectDataDocument record : insertRecordlList) {
                long value = NumberUtils.getLong(record.toObjectData(), LoyaltyConstants.LoyaltyMemberChangeRecords.LOY_CHANGE_VALUE);
                record.put(LoyaltyConstants.LoyaltyMemberChangeRecords.LOY_CHANGE_VALUE, -value);
            }
            stopWatch.lap("扣减积分明细");
            saveMemberChangeRecord(param, insertRecordlList);
            stopWatch.lap("插入操作记录");
        }
        loyaltyMemberService.unLockUpdateMemberPointsAndTier(tenantId, memberId, IMemberTierStrategy.Operator.upgrades);
        stopWatch.lap("会员重算积分/等级");
        stopWatch.logSlow(STOP_WATCH_TIME);
    }

    @Override
    public void fallback(Loyalty.FallbackUpdateData updateData, Loyalty.FallbackInfo fallbackInfo) {
        IObjectData record = fallbackInfo.getRecord();
        IObjectData pointsDetail = fallbackInfo.getPointsDetail();
        if (pointsDetail == null) {
            throw LoyaltyI18nException.build(LoyaltyI18nKey.NOT_FOUND, LoyaltyConstants.LoyaltyPointsDetail.API_NAME);
        }
        long changePointsValue = NumberUtils.getLong(record, LoyaltyConstants.LoyaltyMemberChangeRecords.LOY_CHANGE_VALUE);
        //积分明细扣分
        if (NumberUtils.notEnoughToReduce(pointsDetail, LoyaltyConstants.LoyaltyPointsDetail.AVAILABLE_POINTS, changePointsValue)) {
            throw LoyaltyI18nException.build(LoyaltyI18nKey.INSUFFICIENT_POINTS, LoyaltyConstants.LoyaltyPointsDetail.API_NAME);
        }
        NumberUtils.sum(pointsDetail, LoyaltyConstants.LoyaltyPointsDetail.AVAILABLE_POINTS, -changePointsValue);
        //更新
        record.set(LoyaltyConstants.LoyaltyMemberChangeRecords.IS_FALLBACK, true);
        updateData.getUpdatePointsDetailList().add(pointsDetail);
    }

}
