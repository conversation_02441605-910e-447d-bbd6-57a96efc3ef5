package com.facishare.crm.sfa.lto.integral.core.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

public interface EnableRule {

    @Data
    class Arg {

        @JSONField(name = "api_name")
		@JsonProperty("api_name")
        private String apiName;
    }

    @Data
    @Builder
    class Result {
        private boolean success;
    }

}
