package com.facishare.crm.sfa.lto.integral.core.rest.dto;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

public interface QueryResultList {
    @EqualsAndHashCode(callSuper = true)
	@Data
    class Arg extends BaseEngine.Arg{
        @SerializedName("entityIds")
        List<String> describeApiNames;
        Integer status;

        public static Arg build(String describeApiName,String scene, RequestContext requestContext){

            Arg request = new Arg();

            BaseEngine.Context context = new BaseEngine.Context();
            context.setTenantId(requestContext.getTenantId());
            context.setUserId(requestContext.getUser().getUserId());
            context.setScene(scene);
            request.setContext(context);
            request.setDescribeApiNames(Lists.newArrayList(describeApiName));

            return request;
        }

    }

    @EqualsAndHashCode(callSuper = true)
	@Data
    class Result extends BaseEngine.Result<List<RuleMacroGroupResult>>{ }

    @Data
    class RuleMacroGroupResult{
        @SerializedName("entityId")
        private String describeApiName;
        private String result;
    }
}
