package com.facishare.crm.sfa.lto.objectlimit;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.lto.common.LtoOrgCommonService;
import com.facishare.crm.sfa.lto.common.models.LtoFieldApiConstants;
import com.facishare.crm.sfa.lto.exception.ExceptionUtil;
import com.facishare.crm.sfa.lto.objectlimit.models.*;
import com.facishare.crm.sfa.lto.utils.*;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.*;
import com.facishare.paas.util.PaasBeanProxy;
import com.facishare.social.personnel.PersonnelObjService;
import com.facishare.social.personnel.model.FindByQuery;
import com.facishare.social.personnel.model.PersonnelDto;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.result.EmployeeCardVO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ObjectLimitRuleService {
    private static final String OBJECT_LIMIT_RULE_TABLE = "object_limit_rule";
    private static final String OBJECT_LIMIT_EMPLOYEE_RULE_TABLE = "object_limit_employee_rule";
    private static final String OBJECT_LIMIT_FILTER_TABLE = "object_limit_filter";
    private static final String OBJECT_LIMIT_GLOBAL_FILTER_TABLE = "object_limit_global_filter";
    private static final String OBJECT_LIMIT_OVER_RULE_TABLE = "object_limit_over_rule";
    private static final String GROUP_ID = "group_id";
    private static final String CALCULATE_ID = "calculate_id";
    private static final String EMPLOYEE_ID = "employee_id";

    @Autowired
    private PersonnelObjService personnelObjService;
    @Autowired
    private LtoOrgCommonService ltoOrgCommonService;
    @Autowired
    private ServiceFacade serviceFacade;

    public List<ObjectLimitRuleModel.ObjectLimitRule> getObjectLimitRuleByEmployeeId(User user, String objectApiName, String employeeId) {
        List<ObjectLimitRuleModel.ObjectLimitRule> result = Lists.newArrayList();
        try {
            List<WhereParam> whereParams = getDefaultWhereParams(user.getTenantId(), objectApiName);
            CommonSqlUtil.addWhereParam(whereParams, EMPLOYEE_ID, CommonSqlOperator.EQ, Lists.newArrayList(employeeId));

            List<Map> queryResult = CommonSqlUtil.queryData(user.getTenantId(), OBJECT_LIMIT_EMPLOYEE_RULE_TABLE, whereParams);
            if (CollectionUtils.empty(queryResult)) {
                return result;
            }
            List<ObjectLimitRuleModel.ObjectLimitEmployeeRule> employeeRuleList = Lists.newArrayList();
            convert2ObjectLimitEmployeeRule(employeeRuleList, queryResult);
            Set<String> groupIds = employeeRuleList.stream().map(ObjectLimitRuleModel.ObjectLimitEmployeeRule::getGroupId).collect(Collectors.toSet());
            Optional<ObjectLimitRuleModel.ObjectLimitEmployeeRule> defaultEmployeeRule = employeeRuleList.stream()
                    .filter(ObjectLimitRuleModel.ObjectLimitEmployeeRule::isDefaultRule).findFirst();

            String defaultGroupId = "";
            if (defaultEmployeeRule.isPresent()) {
                defaultGroupId = defaultEmployeeRule.get().getGroupId();
            }

            List<ObjectLimitRuleModel.ObjectLimitRule> allObjectLimitRuleList = getObjectLimitRuleByGroupIds(user, objectApiName, Lists.newArrayList(groupIds), employeeId);
            for (String groupId : groupIds) {
                Optional<ObjectLimitRuleModel.ObjectLimitRule> objectLimitRuleOptional = allObjectLimitRuleList.stream()
                        .filter(x -> groupId.equals(x.getGroupId()) && ObjectLimitRuleModel.RuleTypeEnum.EMPLOYEE.getCode().equals(x.getRuleType())).findFirst();
                if (objectLimitRuleOptional.isPresent()) {
                    if (defaultGroupId.equals(groupId)) {
                        objectLimitRuleOptional.get().setDefaultRule(true);
                    }
                    result.add(objectLimitRuleOptional.get());
                }
                //部门，用户组的保有量规则
                List<ObjectLimitRuleModel.ObjectLimitRule> objectLimitRules = allObjectLimitRuleList.stream()
                        .filter(x -> groupId.equals(x.getGroupId()) && ObjectLimitRuleModel.RuleTypeEnum.ORGANIZATION.getCode().equals(x.getRuleType())).collect(Collectors.toList());
                if (CollectionUtils.notEmpty(objectLimitRules)) {
                    if (defaultGroupId.equals(groupId)) {
                        for (ObjectLimitRuleModel.ObjectLimitRule objectLimitRule : objectLimitRules) {
                            objectLimitRule.setDefaultRule(true);
                        }
                    }
                    result.addAll(objectLimitRules);
                }
            }
        } catch (Exception e) {
            log.error("getObjectLimitRuleGlobalFilter error", e);
            ExceptionUtil.throwCommonBusinessException();
        }
        return result;
    }

    private void convert2ObjectLimitEmployeeRule(List<ObjectLimitRuleModel.ObjectLimitEmployeeRule> objectLimitRuleList, List<Map> dataMapList) {
        for (Map<String, Object> data : dataMapList) {
            ObjectLimitRuleModel.ObjectLimitEmployeeRule objectLimitEmployeeRule = ObjectLimitRuleModel.ObjectLimitEmployeeRule.builder()
                    .objectApiName(ObjectDataUtil.getStringValue(data, LtoFieldApiConstants.OBJECT_API_NAME, ""))
                    .groupId(ObjectDataUtil.getStringValue(data, GROUP_ID, ""))
                    .id(ObjectDataUtil.getStringValue(data, LtoFieldApiConstants.ID, ""))
                    .calculateId(ObjectDataUtil.getStringValue(data, CALCULATE_ID, ""))
                    .isDeleted(ObjectDataUtil.getIntegerValue(data, LtoFieldApiConstants.IS_DELETED, 0))
                    .employeeId(ObjectDataUtil.getStringValue(data, EMPLOYEE_ID, ""))
                    .defaultRule(ObjectDataUtil.getBooleanValue(data, "is_default", false))
                    .build();
            objectLimitRuleList.add(objectLimitEmployeeRule);
        }
    }

    public List<ObjectLimitRuleModel.ObjectLimitRule> getObjectLimitRuleByGroupId(String tenantId, String objectApiName, String groupId) {
        List<ObjectLimitRuleModel.ObjectLimitRule> result = Lists.newArrayList();
        if(StringUtils.isEmpty(groupId)){
            return result;
        }
        try{
            List<WhereParam> whereParams = getDefaultWhereParams(tenantId, objectApiName);
            CommonSqlUtil.addWhereParam(whereParams, GROUP_ID, CommonSqlOperator.EQ, Lists.newArrayList(groupId));

            List<Map> queryResult = CommonSqlUtil.queryData(tenantId, OBJECT_LIMIT_RULE_TABLE, whereParams);
            if(CollectionUtils.empty(queryResult)){
                return result;
            }
            convert2ObjectLimitRule(result, queryResult);
        } catch (Exception e){
            log.error("getObjectLimitRuleByGroupId error", e);
            ExceptionUtil.throwCommonBusinessException();
        }
        return result;
    }

    public List<ObjectLimitRuleModel.ObjectLimitRule> getObjectLimitRuleByGroupIds(User user, String objectApiName,
                                                                                   List<String> groupIds, String employeeId) {
        List<ObjectLimitRuleModel.ObjectLimitRule> result = Lists.newArrayList();
        if (CollectionUtils.empty(groupIds)) {
            return result;
        }
        try {
            List<WhereParam> whereParams = getDefaultWhereParams(user.getTenantId(), objectApiName);
            CommonSqlUtil.addWhereParam(whereParams, GROUP_ID, CommonSqlOperator.IN, Lists.newArrayList(groupIds));

            List<Map> queryResult = CommonSqlUtil.queryData(user.getTenantId(), OBJECT_LIMIT_RULE_TABLE, whereParams);
            if (CollectionUtils.empty(queryResult)) {
                return result;
            }
            convert2ObjectLimitRule(result, queryResult);

            List<ObjectLimitRuleModel.ObjectLimitFilter> filterList = getObjectLimitFilterByGroupIds(user, objectApiName, groupIds, employeeId);

            for (ObjectLimitRuleModel.ObjectLimitRule objectLimitRule : result) {
                List<ObjectLimitRuleModel.ObjectLimitFilter> ruleFilterList = filterList.stream()
                        .filter(x -> x.getGroupId().equals(objectLimitRule.getGroupId())).collect(Collectors.toList());
                if (CollectionUtils.empty(ruleFilterList)) {
                    ruleFilterList = Lists.newArrayList();
                }
                objectLimitRule.setObjectLimitFilterList(ruleFilterList);
            }
        } catch (Exception e) {
            log.error("getObjectLimitRuleByGroupIds error", e);
            ExceptionUtil.throwCommonBusinessException();
        }
        return result;
    }

    @NotNull
    private List<WhereParam> getDefaultWhereParams(String tenantId, String objectApiName) {
        List<WhereParam> whereParams = Lists.newArrayList();
        CommonSqlUtil.addWhereParam(whereParams, Tenantable.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(tenantId));
        CommonSqlUtil.addWhereParam(whereParams, LtoFieldApiConstants.OBJECT_API_NAME, CommonSqlOperator.EQ, Lists.newArrayList(objectApiName));
        CommonSqlUtil.addWhereParam(whereParams, LtoFieldApiConstants.IS_DELETED, CommonSqlOperator.EQ, Lists.newArrayList(0));
        return whereParams;
    }

    private void convert2ObjectLimitRule(List<ObjectLimitRuleModel.ObjectLimitRule> objectLimitRuleList, List<Map> dataMapList) {
        for (Map<String, Object> data : dataMapList) {
            ObjectLimitRuleModel.ObjectLimitRule objectLimitRule = ObjectLimitRuleModel.ObjectLimitRule.builder()
                    .objectApiName(ObjectDataUtil.getStringValue(data, LtoFieldApiConstants.OBJECT_API_NAME, ""))
                    .dataId(ObjectDataUtil.getStringValue(data, "data_id", ""))
                    .dataType(ObjectDataUtil.getStringValue(data, "data_type", ""))
                    .dataWheres(ObjectDataUtil.getStringValue(data, "data_wheres", ""))
                    .name(ObjectDataUtil.getStringValue(data, "name", ""))
                    .groupId(ObjectDataUtil.getStringValue(data, GROUP_ID, ""))
                    .id(ObjectDataUtil.getStringValue(data, LtoFieldApiConstants.ID, ""))
                    .calculateId(ObjectDataUtil.getStringValue(data, CALCULATE_ID, ""))
                    .isDeleted(ObjectDataUtil.getIntegerValue(data, LtoFieldApiConstants.IS_DELETED, 0))
                    .defaultRule(false)
                    .ruleType(ObjectDataUtil.getStringValue(data, "rule_type", ObjectLimitRuleModel.RuleTypeEnum.EMPLOYEE.getCode()))
                    .createBy(ObjectDataUtil.getStringValue(data, "created_by", ""))
                    .build();
            objectLimitRuleList.add(objectLimitRule);
        }
    }

    public List<ObjectLimitRuleModel.ObjectLimitFilter> getObjectLimitFilterByGroupIds(User user, String objectApiName,
                                                                                       List<String> groupIds, String employeeId) {
        List<ObjectLimitRuleModel.ObjectLimitFilter> result = Lists.newArrayList();
        if (CollectionUtils.empty(groupIds)) {
            return result;
        }
        try {
            List<WhereParam> whereParams = getDefaultWhereParams(user.getTenantId(), objectApiName);
            CommonSqlUtil.addWhereParam(whereParams, GROUP_ID, CommonSqlOperator.IN, Lists.newArrayList(groupIds));

            List<Map> queryResult = CommonSqlUtil.queryData(user.getTenantId(), OBJECT_LIMIT_FILTER_TABLE, whereParams);
            if (CollectionUtils.empty(queryResult)) {
                return result;
            }
            convert2ObjectLimitFilter(user, result, queryResult, employeeId);
        } catch (Exception e) {
            log.error("getObjectLimitFilterByGroupIds error", e);
            ExceptionUtil.throwCommonBusinessException();
        }
        return result;
    }

    private void convert2ObjectLimitFilter(User user, List<ObjectLimitRuleModel.ObjectLimitFilter> objectLimitRuleList, List<Map> dataMapList, String employeeId) {
        for (Map<String, Object> data : dataMapList) {
            ObjectLimitRuleModel.ObjectLimitFilter objectLimitFilter = ObjectLimitRuleModel.ObjectLimitFilter.builder()
                    .objectApiName(ObjectDataUtil.getStringValue(data, LtoFieldApiConstants.OBJECT_API_NAME, ""))
                    .wheres(ObjectDataUtil.getStringValue(data, "wheres", ""))
                    .groupId(ObjectDataUtil.getStringValue(data, GROUP_ID, ""))
                    .id(ObjectDataUtil.getStringValue(data, LtoFieldApiConstants.ID, ""))
                    .calculateId(ObjectDataUtil.getStringValue(data, CALCULATE_ID, ""))
                    .isDeleted(ObjectDataUtil.getIntegerValue(data, LtoFieldApiConstants.IS_DELETED, 0))
                    .limitNumber(ObjectDataUtil.getIntegerValue(data, "limit_number", 0))
                    .createTime(ObjectDataUtil.getLongValue(data, "create_time", 0L))
                    .build();
            //处理根据某个对象某个字段获取保有量
            if (objectLimitFilter.getLimitNumber() == 0 && StringUtils.isNotBlank(ObjectDataUtil.getStringValue(data,"limit_object_api_name", "")) &&
                    StringUtils.isNotBlank(ObjectDataUtil.getStringValue(data, "limit_field_name", ""))) {
                String limitObjectApiName = data.getOrDefault("limit_object_api_name", "").toString();
                String limitFieldName = data.getOrDefault("limit_field_name", "").toString();
                int limitNum = getLimitNumByObject(user, limitObjectApiName, limitFieldName, employeeId);
                objectLimitFilter.setLimitNumber(limitNum);
            }
            objectLimitRuleList.add(objectLimitFilter);
        }
    }

    /**
     * 根据对象获取保有量
     *
     * @param user
     * @param limitObjectApiName
     * @param limitFieldName
     */
    public int getLimitNumByObject(User user, String limitObjectApiName, String limitFieldName, String employeeId) {
        //默认无限制返回
        int limitNum = 999999999;
        if (Utils.PERSONNEL_OBJ_API_NAME.equals(limitObjectApiName)) {
            FindByQuery.Argument argument = new FindByQuery.Argument();
            argument.setTenantId(user.getTenantId());
            PersonnelDto personnelDto = PaasBeanProxy.newProxy(PersonnelDto.class);
            personnelDto.setUserId(employeeId);
            argument.setPersonnelDto(personnelDto);
            List<PersonnelDto> ret = personnelObjService.findByQueryUseCacheChain(argument);
            if (CollectionUtils.notEmpty(ret)) {
                Object fieldValue = ret.get(0).get(limitFieldName);
                if (!Objects.isNull(fieldValue) && Strings.isNotBlank(fieldValue.toString())) {
                    limitNum = Double.valueOf(fieldValue.toString()).intValue();
                }
            }
        } else if (Utils.PARTNER_API_NAME.equals(limitObjectApiName)) {
            RestResult<Map<Long, EmployeeCardVO>> restResult = ltoOrgCommonService.getOuterEmployeeInfoByIds(user.getTenantId(), Lists.newArrayList(employeeId));
            if (restResult == null || CollectionUtils.empty(restResult.getData())) {
                return limitNum;
            }
            Optional<String> outerTenantIdOption = restResult.getData().values().stream()
                    .map(x -> String.valueOf(x.getOuterTenantId())).findFirst();
            if (!outerTenantIdOption.isPresent()) {
                return limitNum;
            }
            String partnerId = ltoOrgCommonService.getCrmMapperByOuterTenantIds(user.getTenantId(), Utils.PARTNER_API_NAME, Long.parseLong(outerTenantIdOption.get()));
            if (Strings.isBlank(partnerId)) {
                return limitNum;
            }
            IObjectData partnerData = serviceFacade.findObjectData(user, partnerId, Utils.PARTNER_API_NAME);
            if (partnerData == null) {
                return limitNum;
            }
            String fieldValue = partnerData.get(limitFieldName, String.class);
            if (Strings.isNotBlank(fieldValue)) {
                limitNum = Double.valueOf(fieldValue).intValue();
            }
        }
        if (limitNum < 0) {
            limitNum = 0;
        }
        return limitNum;
    }

    public List<ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter> getObjectLimitRuleGlobalFilter(String tenantId, String objectApiName) {
        List<ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter> result = Lists.newArrayList();
        try {
            List<WhereParam> whereParams = getDefaultWhereParams(tenantId, objectApiName);

            List<Map> queryResult = CommonSqlUtil.queryData(tenantId, OBJECT_LIMIT_GLOBAL_FILTER_TABLE, whereParams);
            if (CollectionUtils.empty(queryResult)) {
                return result;
            }
            for (Map dataMap : queryResult) {
                ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter globalFilter = ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter.builder()
                        .id(dataMap.get(LtoFieldApiConstants.ID).toString())
                        .isDeleted(0)
                        .objectApiName(objectApiName)
                        .wheres(dataMap.getOrDefault("wheres", "[]").toString())
                        .build();
                result.add(globalFilter);
            }
        } catch (Exception e) {
            log.error("getObjectLimitRuleGlobalFilter error", e);
            ExceptionUtil.throwCommonBusinessException();
        }
        return result;
    }

    public ObjectLimitRuleModel.ObjectLimitOverRule getObjectLimitOverRule(String tenantId, String objectApiName) {
        ObjectLimitRuleModel.ObjectLimitOverRule result = ObjectLimitRuleModel.ObjectLimitOverRule.builder()
                .objectApiName(objectApiName).build();
        try {
            List<WhereParam> whereParams = getDefaultWhereParams(tenantId, objectApiName);

            List<Map> queryResult = CommonSqlUtil.queryData(tenantId, OBJECT_LIMIT_OVER_RULE_TABLE, whereParams);
            if (CollectionUtils.empty(queryResult)) {
                return result;
            }
            Map<String, Object> overRuleMapData = queryResult.get(0);
            result.setId(ObjectDataUtil.getStringValue(overRuleMapData, LtoFieldApiConstants.ID, ""));
            result.setObjectPoolId(ObjectDataUtil.getStringValue(overRuleMapData, "object_pool_id", ""));
            result.setOwner(ObjectDataUtil.getStringValue(overRuleMapData, LtoFieldApiConstants.OWNER, ""));
        } catch (Exception e) {
            log.error("getObjectLimitOverRule error", e);
            ExceptionUtil.throwCommonBusinessException();
        }
        return result;
    }

    public List<ObjectLimitRuleModel.ObjectLimitRule> getObjectLimitRuleByDataIds(String tenantId, List<String> dataIds, String dataType) {
        List<ObjectLimitRuleModel.ObjectLimitRule> result = Lists.newArrayList();
        if(CollectionUtils.empty(dataIds)){
            return result;
        }
        try{
            List<WhereParam> whereParams = Lists.newArrayList();
            CommonSqlUtil.addWhereParam(whereParams, Tenantable.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(tenantId));
            CommonSqlUtil.addWhereParam(whereParams, LtoFieldApiConstants.IS_DELETED, CommonSqlOperator.EQ, Lists.newArrayList(0));
            CommonSqlUtil.addWhereParam(whereParams, "data_id", CommonSqlOperator.IN, Lists.newArrayList(dataIds));
            CommonSqlUtil.addWhereParam(whereParams, "data_type", CommonSqlOperator.EQ, Lists.newArrayList(dataType));

            List<Map> queryResult = CommonSqlUtil.queryData(tenantId, OBJECT_LIMIT_RULE_TABLE, whereParams);

            if(CollectionUtils.empty(queryResult)){
                return result;
            }
            convert2ObjectLimitRule(result, queryResult);
        } catch (Exception e){
            log.error("getObjectLimitRuleByDataIds error", e);
            ExceptionUtil.throwCommonBusinessException();
        }
        return  result;
    }

    public List<ObjectLimitRuleModel.ObjectLimitRule> getObjectLimitRuleByDataType(String tenantId, String dataType) {
        List<ObjectLimitRuleModel.ObjectLimitRule> result = Lists.newArrayList();
        if(StringUtils.isEmpty(dataType)){
            return result;
        }
        try{
            List<WhereParam> whereParams = Lists.newArrayList();
            CommonSqlUtil.addWhereParam(whereParams, Tenantable.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(tenantId));
            CommonSqlUtil.addWhereParam(whereParams, LtoFieldApiConstants.IS_DELETED, CommonSqlOperator.EQ, Lists.newArrayList(0));
            CommonSqlUtil.addWhereParam(whereParams, "data_type", CommonSqlOperator.EQ, Lists.newArrayList(dataType));

            List<Map> queryResult = CommonSqlUtil.queryData(tenantId, OBJECT_LIMIT_RULE_TABLE, whereParams);

            if(CollectionUtils.empty(queryResult)){
                return result;
            }
            convert2ObjectLimitRule(result, queryResult);
        } catch (Exception e){
            log.error("getObjectLimitRuleByDataIds error", e);
            ExceptionUtil.throwCommonBusinessException();
        }
        return  result;
    }

    public List<String> getObjectLimitRuleUserIds(String tenantId, List<ObjectLimitRuleModel.ObjectLimitRule> objectLimitRuleList) {
        List<String> userIds = Lists.newArrayList();
        if(CollectionUtils.empty(objectLimitRuleList)){
            return userIds;
        }
        List<String> dataTypes = objectLimitRuleList.stream().map(x -> x.getDataType()).collect(Collectors.toList());
        Set<String> userIdsSet = Sets.newHashSet();

        for(String dataType : dataTypes){
            List<String> tempUserIds = Lists.newArrayList();
            if(ObjectLimitRuleModel.DataTypeEnum.DEPARTMENT.getCode().equals(dataType)){
                List<String> dataIds = objectLimitRuleList.stream().filter(x -> dataType.equals(x.getDataType()))
                        .map(x -> x.getDataId()).collect(Collectors.toList());
                tempUserIds = ltoOrgCommonService.getMembersByDeptIds(tenantId, dataIds, true);
            } else if(ObjectLimitRuleModel.DataTypeEnum.EMPLOYEE.getCode().equals(dataType)) {
                tempUserIds = objectLimitRuleList.stream().filter(x -> dataType.equals(x.getDataType()))
                        .map(x -> x.getDataId()).collect(Collectors.toList());
            } else if(ObjectLimitRuleModel.DataTypeEnum.USER_GROUP.getCode().equals(dataType)){
                List<String> dataIds = objectLimitRuleList.stream().filter(x -> dataType.equals(x.getDataType()))
                        .map(x -> x.getDataId()).collect(Collectors.toList());
                tempUserIds = ltoOrgCommonService.batchGetMembersByUserGroupIds(tenantId, dataIds, true);
            } else if(ObjectLimitRuleModel.DataTypeEnum.USER_ROLE.getCode().equals(dataType)){
                List<String> dataIds = objectLimitRuleList.stream().filter(x -> dataType.equals(x.getDataType()))
                        .map(x -> x.getDataId()).collect(Collectors.toList());
                tempUserIds = ltoOrgCommonService.batchGetRoleUsersByRoleIds(tenantId, dataIds, true);
            } else if(ObjectLimitRuleModel.DataTypeEnum.CUSTOM.getCode().equals(dataType)) {
                List<ObjectLimitRuleModel.ObjectLimitRule> tempRuleList = objectLimitRuleList.stream()
                        .filter(x -> dataType.equals(x.getDataType())).collect(Collectors.toList());

                for(ObjectLimitRuleModel.ObjectLimitRule objectLimitRule : tempRuleList) {
                    tempUserIds.addAll(getCustomDataIds(tenantId, objectLimitRule));
                }
            } else if(ObjectLimitRuleModel.DataTypeEnum.PARTNER.getCode().equals(dataType)){
                List<String> dataIds = objectLimitRuleList.stream().filter(x -> dataType.equals(x.getDataType()))
                        .map(x -> x.getDataId()).collect(Collectors.toList());
                List<Long> outerTenantIds = dataIds.stream().map(Long::valueOf).collect(Collectors.toList());
                tempUserIds = ltoOrgCommonService.batchGetOuterTenantsUserIds(tenantId, outerTenantIds);
            } else if(ObjectLimitRuleModel.DataTypeEnum.PARTNER_EMPLOYEE.getCode().equals(dataType)){
                tempUserIds = objectLimitRuleList.stream().filter(x -> dataType.equals(x.getDataType()))
                        .map(x -> x.getDataId()).collect(Collectors.toList());
            } else if(ObjectLimitRuleModel.DataTypeEnum.PARTNER_CUSTOM.getCode().equals(dataType)){
                List<ObjectLimitRuleModel.ObjectLimitRule> tempRuleList = objectLimitRuleList.stream()
                        .filter(x -> dataType.equals(x.getDataType())).collect(Collectors.toList());

                List<String> dataIds = Lists.newArrayList();
                for(ObjectLimitRuleModel.ObjectLimitRule objectLimitRule : tempRuleList) {
                    dataIds.addAll(getCustomDataIds(tenantId, objectLimitRule));
                }
                tempUserIds = ltoOrgCommonService.getPartnerOutUserIds(tenantId, dataIds);
            }

            if(CollectionUtils.empty(tempUserIds)){
                continue;
            }

            userIdsSet.addAll(tempUserIds);
        }
        userIds.addAll(userIdsSet);
        return  userIds;
    }

    private List<String> getCustomDataIds(String tenantId, ObjectLimitRuleModel.ObjectLimitRule objectLimitRule) {
        List<String> dataIds = Lists.newArrayList();
        int offset = 0;
        int pageSize = 100;
        List<Wheres> wheresList = Lists.newArrayList();
        convert2WheresList(objectLimitRule.getDataWheres(), wheresList);
        String objectApiName = "PersonnelObj";
        if(ObjectLimitRuleModel.DataTypeEnum.PARTNER_CUSTOM.getCode().equals(objectLimitRule.getDataType())) {
            objectApiName = "PartnerObj";
        }
        IObjectDescribe objectDescribe = ObjectDescribeUtils.getObjectDescribe(tenantId, objectApiName);
        convertFilter(wheresList, objectDescribe);

        int maxCount = 1000;
        while (maxCount >= 0) {

            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            searchTemplateQuery.setWheres(wheresList);
            searchTemplateQuery.setLimit(pageSize);
            searchTemplateQuery.setOffset(offset);
            searchTemplateQuery.setPermissionType(0);
            searchTemplateQuery.setOrders(Lists.newArrayList(new OrderBy("id", true)));

            --maxCount;
            QueryResult<IObjectData> queryResult = ObjectDataUtil.findDataBySearchQuery(CommonUtil.buildUser(tenantId), objectApiName, searchTemplateQuery, null);
            if(queryResult == null || CollectionUtils.empty(queryResult.getData())) {
                log.warn("getCustomDataIds queryResult is null SearchTemplateQuery:{}",JSONObject.toJSONString(searchTemplateQuery));
                break;
            }
            if("PersonnelObj".equals(objectApiName)) {
                dataIds.addAll(queryResult.getData().stream().map(x -> x.get("user_id").toString()).collect(Collectors.toSet()));
            } else {
                dataIds.addAll(queryResult.getData().stream().map(x -> x.getId()).collect(Collectors.toSet()));
            }
            offset += pageSize;
            if(queryResult.getTotalNumber() <= offset) {
                break;
            }
        }
        return  dataIds;
    }

    private void convert2WheresList(String wheresString, List<Wheres> wheresList) {
        if(StringUtils.isEmpty(wheresString)) {
            return;
        }
        List<JSONObject> wheresJSONObjectList = JSON.parseObject(wheresString, List.class);
        if(CollectionUtils.notEmpty(wheresJSONObjectList)){
            for(JSONObject jsonObject : wheresJSONObjectList){
                Wheres wheres = JSON.parseObject(jsonObject.toJSONString(), Wheres.class);
                if(CollectionUtils.notEmpty(wheres.getFilters())) {
                    for(IFilter filter : wheres.getFilters()) {
                        if(filter.getFieldValues() == null) {
                            filter.setFieldValues(Lists.newArrayList());
                        }
                    }
                }
                wheresList.add(wheres);
            }
        }
    }

    private void convertFilter(List<Wheres> wheresList, IObjectDescribe objectDescribe) {
        if(CollectionUtils.empty(wheresList) || objectDescribe == null) {
            return;
        }

        for (Wheres wheres : wheresList) {
            if(CollectionUtils.empty(wheres.getFilters())) {
                continue;
            }
            for (IFilter filter : wheres.getFilters()) {
                IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(filter.getFieldName());
                if(fieldDescribe == null) {
                    continue;
                }
                if(IFieldType.OBJECT_REFERENCE.equals(fieldDescribe.getType())) {
                    filter.setFieldName(String.format("%s.name", filter.getFieldName()));
                } else if(IFieldType.SELECT_MANY.equals(fieldDescribe.getType())) {
                    if(Operator.IN.equals(filter.getOperator())) {
                        filter.setOperator(Operator.HASANYOF);
                    } else if(Operator.NIN.equals(filter.getOperator())) {
                        filter.setOperator(Operator.NHASANYOF);
                    }
                }
            }
        }
    }

    public boolean checkIsFitObjectLimitRule (String tenantId, String dataId, ObjectLimitRuleModel.ObjectLimitRule objectLimitRule) {
        if(StringUtils.isEmpty(dataId) || objectLimitRule == null) {
            return false;
        }
        List<Wheres> wheresList = Lists.newArrayList();
        convert2WheresList(objectLimitRule.getDataWheres(), wheresList);
        String objectApiName = "PersonnelObj";
        if(ObjectLimitRuleModel.DataTypeEnum.PARTNER_CUSTOM.getCode().equals(objectLimitRule.getDataType())) {
            objectApiName = "PartnerObj";
        }
        IObjectDescribe objectDescribe = ObjectDescribeUtils.getObjectDescribe(tenantId, objectApiName);
        convertFilter(wheresList, objectDescribe);

        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setWheres(wheresList);
        searchTemplateQuery.setLimit(100);
        searchTemplateQuery.setOffset(0);
        List<IFilter> filterList = Lists.newArrayList();
        if("PersonnelObj".equals(objectApiName)) {
            SearchUtil.fillFilterEq(filterList, "user_id", dataId);
        } else {
            SearchUtil.fillFilterEq(filterList, "_id", dataId);
        }

        searchTemplateQuery.setFilters(filterList);
        QueryResult<IObjectData> queryResult = ObjectDataUtil.findDataBySearchQuery(CommonUtil.buildUser(tenantId), objectApiName, searchTemplateQuery, null);
        return queryResult != null && CollectionUtils.notEmpty(queryResult.getData());
    }

    public Map<String, Boolean> checkIsFitObjectLimitRule (String tenantId, List<String> dataIds, ObjectLimitRuleModel.ObjectLimitRule objectLimitRule) {
        Map<String, Boolean> result = Maps.newHashMap();
        dataIds.forEach(x -> result.put(x, false));
        if(CollectionUtils.empty(dataIds) || objectLimitRule == null) {
            return result;
        }
        List<Wheres> wheresList = Lists.newArrayList();
        convert2WheresList(objectLimitRule.getDataWheres(), wheresList);
        String objectApiName = "PersonnelObj";
        if(ObjectLimitRuleModel.DataTypeEnum.PARTNER_CUSTOM.getCode().equals(objectLimitRule.getDataType())) {
            objectApiName = "PartnerObj";
        }

        IObjectDescribe objectDescribe = ObjectDescribeUtils.getObjectDescribe(tenantId, objectApiName);
        convertFilter(wheresList, objectDescribe);

        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setWheres(wheresList);
        searchTemplateQuery.setLimit(100);
        searchTemplateQuery.setOffset(0);
        List<IFilter> filterList = Lists.newArrayList();
        if("PersonnelObj".equals(objectApiName)) {
            SearchUtil.fillFilterIn(filterList, "user_id", dataIds);
        } else {
            SearchUtil.fillFilterIn(filterList, "_id", dataIds);
        }
        searchTemplateQuery.setFilters(filterList);
        QueryResult<IObjectData> queryResult = ObjectDataUtil.findDataBySearchQuery(CommonUtil.buildUser(tenantId), objectApiName, searchTemplateQuery, null);
        if(queryResult == null || CollectionUtils.empty(queryResult.getData())) {
            return result;
        }
        for(IObjectData objectData : queryResult.getData()) {
            result.put(objectData.getId(), true);
        }
        return result;
    }

    public List<String> getEmployeeRuleByGroupId(String tenantId, String objectApiName, String groupId) throws MetadataServiceException {
        List<WhereParam> whereParams = getDefaultWhereParams(tenantId, objectApiName);
        CommonSqlUtil.addWhereParam(whereParams, GROUP_ID, CommonSqlOperator.EQ, Lists.newArrayList(groupId));

        List<Map> queryResult = CommonSqlUtil.queryData(tenantId, OBJECT_LIMIT_EMPLOYEE_RULE_TABLE, whereParams);
        List<String> result = Lists.newArrayList();
        if(CollectionUtils.empty(queryResult)){
            return result;
        }
        queryResult.forEach(x -> {
            result.add(ObjectDataUtil.getStringValue(x, EMPLOYEE_ID, ""));
        });
        return result;
    }

    public List<ObjectLimitRuleModel.ObjectLimitEmployeeRule> getEmployeeRuleByEmployeeId(String tenantId, String employeeId) throws MetadataServiceException {
        List<WhereParam> whereParams = Lists.newArrayList();
        CommonSqlUtil.addWhereParam(whereParams, Tenantable.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(tenantId));
        CommonSqlUtil.addWhereParam(whereParams, LtoFieldApiConstants.IS_DELETED, CommonSqlOperator.EQ, Lists.newArrayList(0));
        CommonSqlUtil.addWhereParam(whereParams, EMPLOYEE_ID, CommonSqlOperator.EQ, Lists.newArrayList(employeeId));

        List<Map> queryResult = CommonSqlUtil.queryData(tenantId, OBJECT_LIMIT_EMPLOYEE_RULE_TABLE, whereParams);
        List<ObjectLimitRuleModel.ObjectLimitEmployeeRule> result = Lists.newArrayList();
        if(CollectionUtils.empty(queryResult)){
            return result;
        }
        queryResult.forEach(x -> {
            ObjectLimitRuleModel.ObjectLimitEmployeeRule employeeRule = ObjectLimitRuleModel.ObjectLimitEmployeeRule.builder()
                    .employeeId(employeeId).groupId(ObjectDataUtil.getStringValue(x, GROUP_ID, ""))
                    .objectApiName(ObjectDataUtil.getStringValue(x, "object_api_name", ""))
                    .build();
            result.add(employeeRule);
        });
        return result;
    }

    @NotNull
    private List<WhereParam> getWhereParams(String tenantId, String objectApiName, String groupId, String preCalculateId) {
        List<WhereParam> whereParams = getDefaultWhereParams(tenantId, objectApiName);
        CommonSqlUtil.addWhereParam(whereParams, GROUP_ID, CommonSqlOperator.EQ, Lists.newArrayList(groupId));
        if (StringUtils.isEmpty(preCalculateId)) {
            CommonSqlUtil.addWhereParam(whereParams, CALCULATE_ID, CommonSqlOperator.IS, null);
        } else {
            CommonSqlUtil.addWhereParam(whereParams, CALCULATE_ID, CommonSqlOperator.EQ, Lists.newArrayList(preCalculateId));
        }
        return whereParams;
    }

    public void updateObjectLimitRule(String tenantId, String objectApiName, String groupId, String preCalculateId, String newCalculateId) throws MetadataServiceException {
        List<WhereParam> whereParams = getWhereParams(tenantId, objectApiName, groupId, preCalculateId);
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put(CALCULATE_ID, newCalculateId);
        CommonSqlUtil.updateData(tenantId, OBJECT_LIMIT_RULE_TABLE, dataMap, whereParams);
    }

    public void deleteObjectLimitRuleByDataIds(String tenantId, String objectApiName, String groupId, List<String> dataIds, String dataType) throws MetadataServiceException {
        List<WhereParam> whereParams = getDefaultWhereParams(tenantId, objectApiName);
        CommonSqlUtil.addWhereParam(whereParams, GROUP_ID, CommonSqlOperator.EQ, Lists.newArrayList(groupId));
        CommonSqlUtil.addWhereParam(whereParams, "data_id", CommonSqlOperator.IN, Lists.newArrayList(dataIds));
        CommonSqlUtil.addWhereParam(whereParams, "data_type", CommonSqlOperator.EQ, Lists.newArrayList(dataType));
        Map<String, Object> dataMap = getDefaultUpdateDeletedDataMap();
        CommonSqlUtil.updateData(tenantId, OBJECT_LIMIT_RULE_TABLE, dataMap, whereParams);
    }

    public void deleteObjectLimitRuleByDataIds(String tenantId, List<String> dataIds, String dataType) throws MetadataServiceException {
        List<WhereParam> whereParams = Lists.newArrayList();
        CommonSqlUtil.addWhereParam(whereParams, Tenantable.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(tenantId));
        CommonSqlUtil.addWhereParam(whereParams, LtoFieldApiConstants.IS_DELETED, CommonSqlOperator.EQ, Lists.newArrayList(0));
        CommonSqlUtil.addWhereParam(whereParams, "data_id", CommonSqlOperator.IN, Lists.newArrayList(dataIds));
        CommonSqlUtil.addWhereParam(whereParams, "data_type", CommonSqlOperator.EQ, Lists.newArrayList(dataType));
        Map<String, Object> dataMap = getDefaultUpdateDeletedDataMap();
        CommonSqlUtil.updateData(tenantId, OBJECT_LIMIT_RULE_TABLE, dataMap, whereParams);
    }

    public void deleteEmployeeRule(String tenantId, String objectApiName, String groupId) throws MetadataServiceException {
        List<WhereParam> whereParams = getDefaultWhereParams(tenantId, objectApiName);
        CommonSqlUtil.addWhereParam(whereParams, GROUP_ID, CommonSqlOperator.EQ, Lists.newArrayList(groupId));
        Map<String, Object> dataMap = getDefaultUpdateDeletedDataMap();
        CommonSqlUtil.updateData(tenantId, OBJECT_LIMIT_EMPLOYEE_RULE_TABLE, dataMap, whereParams);
    }

    public void deleteEmployeeRuleByEmployeeIds(String tenantId, String objectApiName, String groupId, List<String> employeeIds) throws MetadataServiceException {
        List<WhereParam> whereParams = getDefaultWhereParams(tenantId, objectApiName);
        CommonSqlUtil.addWhereParam(whereParams, GROUP_ID, CommonSqlOperator.EQ, Lists.newArrayList(groupId));
        CommonSqlUtil.addWhereParam(whereParams, EMPLOYEE_ID, CommonSqlOperator.IN, Lists.newArrayList(employeeIds));
        Map<String, Object> dataMap = getDefaultUpdateDeletedDataMap();
        CommonSqlUtil.updateData(tenantId, OBJECT_LIMIT_EMPLOYEE_RULE_TABLE, dataMap, whereParams);
    }

    public void deleteEmployeeRuleByEmployeeIds(String tenantId, List<String> employeeIds) throws MetadataServiceException {
        List<WhereParam> whereParams = Lists.newArrayList();
        CommonSqlUtil.addWhereParam(whereParams, Tenantable.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(tenantId));
        CommonSqlUtil.addWhereParam(whereParams, LtoFieldApiConstants.IS_DELETED, CommonSqlOperator.EQ, Lists.newArrayList(0));
        CommonSqlUtil.addWhereParam(whereParams, EMPLOYEE_ID, CommonSqlOperator.IN, Lists.newArrayList(employeeIds));
        Map<String, Object> dataMap = getDefaultUpdateDeletedDataMap();
        CommonSqlUtil.updateData(tenantId, OBJECT_LIMIT_EMPLOYEE_RULE_TABLE, dataMap, whereParams);
    }

    public void updateObjectLimitFilter(String tenantId, String objectApiName, String groupId, String preCalculateId, String newCalculateId) throws MetadataServiceException{
        List<WhereParam> whereParams = getWhereParams(tenantId, objectApiName, groupId, preCalculateId);
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put(CALCULATE_ID, newCalculateId);
        CommonSqlUtil.updateData(tenantId, OBJECT_LIMIT_FILTER_TABLE, dataMap, whereParams);
    }

    public void insertEmployeeRule(String tenantId, String objectApiName, String groupId, String calculateId, List<String> userIds) throws MetadataServiceException {
        if(CollectionUtils.empty(userIds)){
            log.warn("insertEmployeeRule : userIds is empty");
            return;
        }
        List<Map<String, Object>> dataMapList = Lists.newArrayList();
        for(String userId : userIds){
            Map<String, Object> dataMap = getDefaultInsertDataMap(tenantId, objectApiName, groupId);
            dataMap.put(EMPLOYEE_ID, userId);
            dataMap.put("is_default", false);
            dataMap.put(CALCULATE_ID, calculateId);
            dataMapList.add(dataMap);
        }
        CommonSqlUtil.insertData(tenantId, OBJECT_LIMIT_EMPLOYEE_RULE_TABLE, dataMapList);
    }

    public void insertEmployeeRule(String tenantId, List<ObjectLimitRuleModel.ObjectLimitEmployeeRule> employeeRuleList) throws MetadataServiceException {
        if(CollectionUtils.empty(employeeRuleList)){
            return;
        }
        List<Map<String, Object>> dataMapList = Lists.newArrayList();
        for(ObjectLimitRuleModel.ObjectLimitEmployeeRule employeeRule : employeeRuleList){
            String objectApiName = employeeRule.getObjectApiName();
            if(StringUtils.isEmpty(objectApiName)){
                continue;
            }
            String groupId = employeeRule.getGroupId();
            if(StringUtils.isEmpty(groupId)){
                continue;
            }
            String userId = employeeRule.getEmployeeId();
            if(StringUtils.isEmpty(userId)){
                continue;
            }
            Map<String, Object> dataMap = getDefaultInsertDataMap(tenantId, objectApiName, groupId);
            dataMap.put(EMPLOYEE_ID, userId);
            dataMap.put(CALCULATE_ID, employeeRule.getCalculateId());
            dataMap.put("is_default", false);
            dataMapList.add(dataMap);
        }
        CommonSqlUtil.insertData(tenantId, OBJECT_LIMIT_EMPLOYEE_RULE_TABLE, dataMapList);
    }

    public void deleteEmployeeRule(String tenantId, List<ObjectLimitRuleModel.ObjectLimitEmployeeRule> employeeRuleList) throws MetadataServiceException {
        if(CollectionUtils.empty(employeeRuleList)){
            return;
        }
        for(ObjectLimitRuleModel.ObjectLimitEmployeeRule employeeRule : employeeRuleList){
            String objectApiName = employeeRule.getObjectApiName();
            if(StringUtils.isEmpty(objectApiName)){
                continue;
            }
            String groupId = employeeRule.getGroupId();
            if(StringUtils.isEmpty(groupId)){
                continue;
            }
            String userId = employeeRule.getEmployeeId();
            if(StringUtils.isEmpty(userId)){
                continue;
            }
            Map<String, Object> dataMap = getDefaultUpdateDeletedDataMap();
            List<WhereParam> whereParams = getDefaultWhereParams(tenantId, objectApiName);
            CommonSqlUtil.addWhereParam(whereParams, GROUP_ID, CommonSqlOperator.EQ, Lists.newArrayList(groupId));
            CommonSqlUtil.addWhereParam(whereParams, EMPLOYEE_ID, CommonSqlOperator.EQ, Lists.newArrayList(userId));
            CommonSqlUtil.updateData(tenantId, OBJECT_LIMIT_EMPLOYEE_RULE_TABLE, dataMap, whereParams);
        }
    }

    @NotNull
    private Map<String, Object> getDefaultInsertDataMap(String tenantId, String objectApiName, String groupId) {
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put("id", serviceFacade.generateId());
        dataMap.put("tenant_id", tenantId);
        dataMap.put("object_api_name", objectApiName);
        dataMap.put(GROUP_ID, groupId);
        dataMap.put("is_deleted", 0);
        dataMap.put("last_modified_by", User.SUPPER_ADMIN_USER_ID);
        dataMap.put("last_modified_time", System.currentTimeMillis());
        return dataMap;
    }

    @NotNull
    private Map<String, Object> getDefaultUpdateDeletedDataMap() {
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put("is_deleted", 1);
        dataMap.put("last_modified_by", User.SUPPER_ADMIN_USER_ID);
        dataMap.put("last_modified_time", System.currentTimeMillis());
        return dataMap;
    }

    public void dealDefaultEmployeeRule(String tenantId, String objectApiName, String groupId, List<String> employeeIds, String newCalculateId) throws MetadataServiceException {
        if(CollectionUtils.empty(employeeIds)){
            return;
        }
        List<WhereParam> whereParams = getDefaultWhereParams(tenantId, objectApiName);
        CommonSqlUtil.addWhereParam(whereParams, GROUP_ID, CommonSqlOperator.EQ, Lists.newArrayList(groupId));
        if(StringUtils.isEmpty(newCalculateId)){
            CommonSqlUtil.addWhereParam(whereParams, CALCULATE_ID, CommonSqlOperator.IS, null);
        }else{
            CommonSqlUtil.addWhereParam(whereParams, CALCULATE_ID, CommonSqlOperator.EQ, Lists.newArrayList(newCalculateId));
        }
        CommonSqlUtil.addWhereParam(whereParams, EMPLOYEE_ID, CommonSqlOperator.IN, Lists.newArrayList(employeeIds));
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put("is_default", "t");
        CommonSqlUtil.updateData(tenantId, OBJECT_LIMIT_EMPLOYEE_RULE_TABLE, dataMap, whereParams);
    }

    public List<String> getDefaultEmployeeRule(String tenantId, String objectApiName, String groupId) throws MetadataServiceException {
        List<WhereParam> whereParams = getDefaultWhereParams(tenantId, objectApiName);
        CommonSqlUtil.addWhereParam(whereParams, GROUP_ID, CommonSqlOperator.EQ, Lists.newArrayList(groupId));
        CommonSqlUtil.addWhereParam(whereParams, "is_default", CommonSqlOperator.EQ, Lists.newArrayList("t"));
        List<Map> result = CommonSqlUtil.queryData(tenantId, OBJECT_LIMIT_EMPLOYEE_RULE_TABLE, whereParams);
        if(CollectionUtils.empty(result)){
            return Lists.newArrayList();
        }

        return result.stream().map(x -> String.valueOf(ObjectDataUtil.getStringValue(x, EMPLOYEE_ID, "")))
                .collect(Collectors.toList());
    }

    public String generateId() {
        return serviceFacade.generateId();
    }
}
