package com.facishare.crm.sfa.lto.integral.core.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

public interface RegisterMaterial {
    @Data
    class Arg {
        @JSONField(name="category_api_name")
		@JsonProperty("category_api_name")
        private String categoryApiName;
        @JSONField(name="material_api_name")
		@JsonProperty("material_api_name")
        String materialApiName;
        @JSONField(name="material_label")
		@JsonProperty("material_label")
        String materialLabel;
    }

    @Data
    @Builder
    class Result {
        private boolean success;
    }
}
