package com.facishare.crm.sfa.lto.integral.core.service;

import com.facishare.crm.sfa.lto.integral.common.constant.IntegralObject;
import com.facishare.crm.sfa.lto.integral.core.util.FieldDescribeLoader;
import com.facishare.crm.sfa.lto.integral.core.util.FieldInstaller;
import com.facishare.paas.appframework.metadata.LayoutLogicService;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.NumberFieldDescribe;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 行为积分初始化服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class BehaviorIntegralInitServiceImpl implements BehaviorIntegralInitService {
    @Autowired
    private IObjectDescribeService objectDescribeService;
    @Autowired
    private LayoutLogicService layoutLogicService;

    @Override
    public void open(String tenantId) {
        String apiName = "LeadsObj";
        installField(tenantId, apiName);
    }

    private IObjectDescribe findDescribe(String tenantId, String apiName) {
        try {
            return objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, apiName);
        } catch (MetadataServiceException e) {
            log.warn("findDescribe error", e);
            throw new MetaDataBusinessException(e);
        }
    }

    private void installField(String tenantId, String apiName) {
        IObjectDescribe describe = findDescribe(tenantId, apiName);
        if (describe.containsField(IntegralObject.FIELD_BEHAVIOR_INTEGRAL)) {
            return;
        }
        NumberFieldDescribe fieldDescribe = new NumberFieldDescribe();
        fieldDescribe.fromJsonString(FieldDescribeLoader.getFieldDescribeJson(IntegralObject.FIELD_BEHAVIOR_INTEGRAL));
        FieldInstaller.builder()
                .objectDescribeService(objectDescribeService)
                .layoutLogicService(layoutLogicService)
                .tenantId(tenantId)
                .objectDescribe(describe)
                .fieldDescribes(Lists.newArrayList(fieldDescribe))
                .build()
                .install();
    }
}
