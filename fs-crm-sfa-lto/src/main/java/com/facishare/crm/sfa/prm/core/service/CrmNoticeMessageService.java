package com.facishare.crm.sfa.prm.core.service;

import com.facishare.crm.sfa.prm.api.enums.NotificationType;
import com.facishare.crm.sfa.prm.api.notification.MessageService;
import com.facishare.crm.sfa.prm.model.CrmNoticeMessageContent;
import com.facishare.crm.sfa.prm.model.MessageContent;
import com.facishare.paas.appframework.common.service.CRMNotificationService;
import com.facishare.paas.appframework.common.service.model.NewCrmNotification;
import com.facishare.paas.appframework.core.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @time 2024-06-29 16:18
 * @Description
 */
@Service
@Slf4j
public class CrmNoticeMessageService implements MessageService {
    @Resource
    private CRMNotificationService crmNotificationService;

    @Override
    public NotificationType getMessageType() {
        return NotificationType.CRM_NOTICE;
    }

    @Override
    public boolean sendMessage(User user, MessageContent content) {
        if (!(content instanceof CrmNoticeMessageContent)) {
            throw new IllegalArgumentException("Invalid content type for CrmNoticeMessageService");
        }
        CrmNoticeMessageContent crmNoticeMessageContent = (CrmNoticeMessageContent) content;
        Set<Integer> receiverIds = crmNoticeMessageContent.getRecipients().stream().map(Integer::parseInt).collect(Collectors.toSet());
        NewCrmNotification crmNotification = NewCrmNotification.builder()
                .title(crmNoticeMessageContent.getTitle())
                .senderId(user.getUserId())
                .receiverIDs(receiverIds)
                .sourceId(crmNoticeMessageContent.getSourceId())
                .type(crmNoticeMessageContent.getType())
                .remindSender(crmNoticeMessageContent.isRemindSender())
                .fullContent(crmNoticeMessageContent.getContent())
                .titleInfo(crmNoticeMessageContent.getI18nTitle())
                .fullContentInfo(crmNoticeMessageContent.getI18nContent())
                .appId(crmNoticeMessageContent.getAppId())
                .build();
        try {
            crmNotificationService.sendNewCrmNotification(user, crmNotification);
            return true;
        } catch (Exception e) {
            log.error("CrmNoticeMessageService#sendMessage error, tenant:{}, crmNotification:{}", user.getTenantId(), crmNotification, e);
        }
        return false;
    }
}
