package com.facishare.crm.sfa.prm.core.service;

import com.facishare.crm.sfa.prm.api.notification.MessageService;
import com.facishare.crm.sfa.prm.api.notification.MessageDispatcher;
import com.facishare.crm.sfa.prm.api.enums.NotificationType;
import com.facishare.crm.sfa.prm.model.CrmNoticeMessageContent;
import com.facishare.crm.sfa.prm.model.MessageContent;
import com.facishare.paas.appframework.common.util.AppIdMapping;
import com.facishare.paas.appframework.core.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Created by Sundy on 2024/11/14 10:46
 */
@Service
@Slf4j
public class PrmCrmNoticeMessageService implements MessageService {
    @Resource
    private MessageDispatcher messageDispatcher;

    @Override
    public NotificationType getMessageType() {
        return NotificationType.PRM_CRM;
    }

    @Override
    public boolean sendMessage(User user, MessageContent content) {
        if (!(content instanceof CrmNoticeMessageContent)) {
            throw new IllegalArgumentException("Invalid content type for PrmCrmNoticeMessageService");
        }
        CrmNoticeMessageContent crmNoticeMessageContent = (CrmNoticeMessageContent) content;
        crmNoticeMessageContent.setAppId(AppIdMapping.getAppIdByName("prm"));
        return messageDispatcher.dispatch(NotificationType.CRM_NOTICE).sendMessage(user, crmNoticeMessageContent);
    }
}
