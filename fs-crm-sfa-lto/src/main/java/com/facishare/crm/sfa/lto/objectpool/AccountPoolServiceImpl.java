package com.facishare.crm.sfa.lto.objectpool;

import com.facishare.crm.openapi.Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AccountPoolServiceImpl extends BaseObjectPoolService {
    @Override
    public String getObjectPoolApiName() {
        return Utils.HIGHSEAS_API_NAME;
    }

    @Override
    public String getObjectApiName() {
        return Utils.ACCOUNT_API_NAME;
    }
}
