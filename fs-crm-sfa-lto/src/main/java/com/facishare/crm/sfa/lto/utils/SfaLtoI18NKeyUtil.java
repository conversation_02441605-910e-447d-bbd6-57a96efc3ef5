package com.facishare.crm.sfa.lto.utils;

public interface SfaLtoI18NKeyUtil {
    /**
     * {0}重复，已生成{1}，不再生成{0}！
     */
    String SFA_LEADS_REPEAT = "sfa.leads.repeat";

    /**
     * %s %s 到 %s
     */
    String SFA_LEADS_COLLECTED_TO = "sfa.leads.collected_to.log";
    /**
     * 自动归集 ，%s %s 到 %s
     */
    String SFA_LEADS_AUTO_COLLECTED_TO = "sfa.leads.auto.collected_to.log";

	/**
	 * 自动归集
	 */
	String SFA_LEADS_AUTO_COLLECT_TO = "sfa.leads.auto.collect.to";

	/**
	 * 到
	 */
	String SFA_LEADS_COLLECT_TO = "sfa.leads.collect.to";

    /**
     * 系统错误，请稍候重试!
     */
    String SFA_SYS_ERROR_MSG = "sfa.sys.error.msg";

    /**
     * 自动更新已有线索
     */
    String SFA_DUPLICATED_AUTOCHANGELEADS = "sfa.duplicated.autochangeleads";

    /**
     * 企业已建立客户树关系不允许重复创建
     */
    String SFA_ACCOUNT_TREE_RELATION_EXISTS = "sfa.account.tree.relation.exists";

    /**
     * 企业正在创建客户树关系,完成后,将以通知方式告知
     */
    String SFA_ACCOUNT_TREE_RELATION_PROCESSING = "sfa.account.tree.relation.processing";

    /**
     * 股权信息不存在或企业未进行对外投资控股
     */
    String SFA_ACCOUNT_TREE_RELATION_EQUITY_DATA_NOT_EXIST = "sfa.account.tree.relation.equity.data.not.exist";

    /**
     * 客户树创建成功
     */
    String SFA_ACCOUNT_TREE_RELATION_PROCESS_SUCCESS = "sfa.account.tree.relation.process.success";

    /**
     * 以%s为根节点的客户树已创建完成
     */
    String SFA_ACCOUNT_TREE_RELATION_PROCESS_SUCCESS_CONTENT = "sfa.account.tree.relation.process.success.content";

    /**
     * 获取描述失败！请重新尝试！
     */
    String SFA_ACCOUNT_GETDESCRIPTIONFAILED = "sfa.account.getdescriptionfailed";

    /**
     * 所选数据已是该数据的上级或子级
     */
    String SFA_MARKETING_EVENT_CHILDISFATHER = "sfa.marketing.event.childisfather";

    /**
     * 层级不能超过{}级
     */
    String SFA_OBJ_PATH_PARENT_ID_NOT_EXCEED = "sfa.obj.path.parent.id.not.exceed";

    /**
     * 股权信息不存在或企业未进行对外投资控股
     */
    String SFA_EQUITY_INFORMATION_DOES_NOT_EXIST = "sfa.equity.information.does.not.exist";

    /**
     * 获取工商信息调用企智接口超时，请重试。
     */
    String SFA_EQUITY_INFORMATION_CALL_INTERFACE_TIMEOUT = "sfa.equity.information.call.interface.timeout";

    /**
     * 客户资源超时配额，请购买
     */
    String SFA_COMMON_TREE_RELATION_LIMIT_QUOTA = "sfa.common.tree.relation.limit.quota";

    /**
     * 贵公司系统中存在名称相同的客户，且已创建了客户树，
     */
    String SFA_COMMON_TREE_RELATION_EXISTS = "sfa.common.tree.relation.exists";
    /**
     * 股权信息不存在或企业未进行对外投资控股
     */
    String SFA_EQUITY_INFORMATION_NOT_EXIST_BY_BI = "sfa.equity.information.not.exist.by.bi";

    /**
     * 企微网关接口错误
     */
    String SFA_WECOM_GATEWAY_SERVICE_ERROR = "sfa.wecom.gateway.service.error";

    /**
     * 股权关系获取成功
     */
    String SFA_EQUITY_INFORMATION_QUERY_SUCCESS_TITLE = "sfa.equity.information.query.success.title";
    /**
     * {0}股权关系获取成功
     */
    String SFA_EQUITY_INFORMATION_QUERY_SUCCESS_CONTENT = "sfa.equity.information.query.success.content";
}
