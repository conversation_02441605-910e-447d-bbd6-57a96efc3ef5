package com.facishare.crm.sfa.prm.api.notification;

import com.facishare.crm.sfa.prm.api.enums.NotificationType;
import com.facishare.paas.appframework.core.exception.ValidateException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @time 2024-06-29 16:12
 * @Description
 */
@Service
@Slf4j
public class MessageDispatcher implements ApplicationContextAware {
    private final Map<NotificationType, MessageService> messageServicesMapping = new HashMap<>();

    public MessageService dispatch(NotificationType messageType) {
        MessageService messageService = messageServicesMapping.get(messageType);
        if (messageService == null) {
            String errorMsg = String.format("无法获取到 %s 对应的服务", messageType);//ignoreI18n
            throw new ValidateException(errorMsg);
        }
        return messageService;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, MessageService> springBeanMapping = applicationContext.getBeansOfType(MessageService.class);
        springBeanMapping.values().stream()
                .filter(bean -> bean.getMessageType() != null)
                .forEach(bean -> messageServicesMapping.put(bean.getMessageType(), bean));
    }
}
