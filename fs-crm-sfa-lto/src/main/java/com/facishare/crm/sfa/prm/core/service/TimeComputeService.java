package com.facishare.crm.sfa.prm.core.service;

import com.facishare.crm.sfa.prm.platform.enums.TimeUnit;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.google.common.collect.ImmutableSet;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

import static com.facishare.crm.sfa.prm.core.constants.PrmI18NConstants.PRM_CHANNEL_TIME_MUST_GREATER_THAN_ZERO;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-04-22
 * ============================================================
 */
@Service
@Slf4j
public class TimeComputeService {
    private static final Set<Integer> LONG_MONTHS = ImmutableSet.of(1, 3, 5, 7, 8, 10, 12);

    public @NotNull Date timestamp2Date(@NotNull Long timestamp) {
        return new Date(timestamp);
    }

    public @NotNull Long date2timestamp(@NotNull Date date) {
        return date.toInstant().toEpochMilli();
    }

    public boolean isExpired(Long expiredTimestamp) {
        if (expiredTimestamp == null) {
            return false;
        }
        LocalDate currentDate = LocalDate.now();
        LocalDate expiredDate = Instant.ofEpochMilli(expiredTimestamp)
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
        return currentDate.isEqual(expiredDate) || currentDate.isAfter(expiredDate);
    }

    public Date getNDaysBefore(Date anchorDate, int timeHours, int nDays) {
        // 将 anchorDate 转换为 LocalDateTime
        LocalDateTime localDateTime = anchorDate.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
        // 减去 N 天，并将时间设置为 timeHours
        localDateTime = localDateTime.minusDays(nDays)
                .withHour(timeHours)
                .withMinute(0)
                .withSecond(0)
                .withNano(0);
        // 将 LocalDateTime 转换回 Date
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public Date getNextNDay(int plusDays, int hours, int minutes) {
        LocalDateTime now = LocalDateTime.now();
        // 获取第二天的日期时间
        LocalDateTime tomorrow = now.plusDays(plusDays).with(LocalTime.of(hours, minutes));
        return Date.from(tomorrow.atZone(ZoneId.systemDefault()).toInstant());
    }

    public int calculateDifference(@NotNull Long endDate, @NotNull TimeUnit timeUnit) {
        LocalDate endLocalDate = Instant.ofEpochMilli(endDate)
                .atZone(ZoneId.systemDefault()) // 根据系统默认时区
                .toLocalDate();
        return (int) calculateDifference(endLocalDate, timeUnit);
    }

    public long calculateDifference(@NotNull LocalDate endDate, @NotNull TimeUnit timeUnit) {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        return calculateDifference(currentDate, endDate, timeUnit);
    }

    public long calculateDifference(LocalDate referenceDate, @NotNull LocalDate endDate, @NotNull TimeUnit timeUnit) {
        // 根据不同的单位计算差异
        switch (timeUnit) {
            case YEAR:  // 计算差异的年数
                return ChronoUnit.YEARS.between(referenceDate, endDate);
            case MONTH:  // 计算差异的年数
                return ChronoUnit.MONTHS.between(referenceDate, endDate);
            case QUARTER:  // 计算差异的周数
                return calculateQuartersBetween(referenceDate, endDate);
            case WEEK:  // 计算差异的周
                return ChronoUnit.WEEKS.between(referenceDate, endDate);
            case DAY:  // 计算差异的周
                return ChronoUnit.DAYS.between(referenceDate, endDate);
            default:
                throw new IllegalArgumentException("Invalid unit. Use 'day', 'week', 'month', 'year', or 'quarter'.");
        }
    }

    /**
     * 计算月份的差异
     *
     * @param currentDate 当前时间
     * @param endDate     结束时间
     * @return 相差季度
     */
    private long calculateQuartersBetween(LocalDate currentDate, @NotNull LocalDate endDate) {
        long monthsBetween = ChronoUnit.MONTHS.between(currentDate, endDate);
        // 一个季度是3个月，因此将月份的差异除以3
        return monthsBetween / 3;
    }

    /**
     * 计算换算为多少天
     *
     * @param time     时间
     * @param timeUnit 时间单位
     * @return
     */
    public int calculateDays(Integer time, TimeUnit timeUnit) {
        if (time == null || time < 0) {
            throw new ValidateException(I18N.text(PRM_CHANNEL_TIME_MUST_GREATER_THAN_ZERO));
        }
        AtomicInteger daysAtomic = new AtomicInteger(0);
        switch (timeUnit) {
            case YEAR:
                daysAtomic.set(time * 365);
                break;
            case MONTH:
                daysAtomic.set(time * 30);
                break;
            case QUARTER:
                daysAtomic.set(time * 90);
                break;
            case WEEK:
                daysAtomic.set(time * 7);
                break;
            case DAY:
                daysAtomic.set(time);
                break;
            default:
                throw new IllegalArgumentException("Invalid unit. Use 'week', 'month', 'year', or 'quarter'.");
        }
        return daysAtomic.get();
    }

    public boolean isMonthWith31(int fixedMonth) {
        return LONG_MONTHS.contains(fixedMonth);
    }

    /**
     * 获取当前 periodKey
     *
     * @return periodKey 字符串
     */
    public String getCurrentPeriodKey(TimeUnit timeUnit) {
        String periodKey;
        switch (timeUnit) {
            case MONTH:
                periodKey = computeCurrentMonthPeriodKey();
                break;
            default:
                throw new IllegalArgumentException("Unsupported time unit: {}" + timeUnit);
        }
        return periodKey;
    }

    /**
     * 获取上个时间 periodKey
     *
     * @return periodKey 字符串
     */
    public String getLastPeriodKey(TimeUnit timeUnit) {
        String lastPeriodKey;
        switch (timeUnit) {
            case MONTH:
                lastPeriodKey = computeLastMonthPeriodKey();
                break;
            default:
                throw new IllegalArgumentException("Unsupported time unit: {}" + timeUnit);
        }
        return lastPeriodKey;
    }

    private String computeCurrentMonthPeriodKey() {
        // 获取当前日期
        LocalDate now = LocalDate.now();
        // 格式化为 "yyyyMM" 格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
        return now.format(formatter);
    }

    private String computeLastMonthPeriodKey() {
        // 获取当前日期并减去一个月
        LocalDate lastMonth = LocalDate.now().minusMonths(1);
        // 格式化为 "yyMM" 格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
        return lastMonth.format(formatter);
    }

    /**
     * 判断时间是否在区间内
     *
     * @param time      原始时间戳，单位毫秒
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 0 表示在区间内，-1 表示早于开始时间，1 表示晚于结束时间
     */
    public int compareTimeRange(Long time, Long startTime, Long endTime) {
        if (time < startTime) {
            return -1;
        } else if (time > endTime) {
            return 1;
        } else {
            return 0;
        }
    }

    /**
     * 根据给定的年和月日字符串（格式 MM-dd），返回该日期0点或23:59:59的时间戳（秒）
     *
     * @param year         年份，如2025
     * @param monthDay     月日字符串，格式为 MM-dd，如 "05-09"
     * @param isStartOfDay true 返回当天0点时间戳，false 返回当天23:59:59时间戳
     * @return 对应日期指定时间点的时间戳，单位毫秒
     */
    public long calculateTimestampAtDayBoundary(int year, String monthDay, boolean isStartOfDay) {
        String[] parts = monthDay.split("-");
        if (parts.length != 2) {
            throw new IllegalArgumentException("monthDay format error, expected MM-dd");
        }
        int month = Integer.parseInt(parts[0]);
        int day = Integer.parseInt(parts[1]);

        LocalDate date = LocalDate.of(year, month, day);
        LocalDateTime dateTime;
        if (isStartOfDay) {
            dateTime = date.atStartOfDay();
        } else {
            // 当天23:59:59
            dateTime = date.atTime(23, 59, 59);
        }
        return dateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * 根据给定时间戳（秒）获取对应的年、月、日
     *
     * @param timestampMillis 时间戳，单位毫秒
     * @return int数组，分别是年、月、日
     */
    public int[] getYearMonthDay(long timestampMillis) {
        Instant instant = Instant.ofEpochMilli(timestampMillis);
        LocalDate date = instant.atZone(ZoneId.systemDefault()).toLocalDate();

        int year = date.getYear();
        int month = date.getMonthValue();  // 1-12
        int day = date.getDayOfMonth();

        return new int[]{year, month, day};
    }

    /**
     * 获取指定年和月日字符串（如 "MM-dd" 或 "MM-dd HH"）的0点时间戳。
     *
     * @param year           年份 (int)
     * @param monthDayString 月日字符串，格式可以为 "MM-dd" 或 "MM-dd HH:mm:ss" 等。
     * @param timeZone       时区，例如 TimeZone.getDefault() 或 TimeZone.getTimeZone("Asia/Shanghai")
     * @return 0点时间戳 (耗秒)
     * @throws IllegalArgumentException 如果 monthDayString 格式不正确或日期无效
     */
    public long toTimestamp(int year, String monthDayString, int hour, int minute, int second, int millisecond, TimeZone timeZone) {
        if (monthDayString == null || monthDayString.trim().isEmpty()) {
            throw new IllegalArgumentException("Month-day string cannot be null or empty.");
        }

        String[] parts = monthDayString.split("-");
        if (parts.length < 2) {
            throw new IllegalArgumentException("Invalid month-day string format. Expected at least 'MM-dd', but got: " + monthDayString);
        }

        int month;
        int day;

        try {
            month = Integer.parseInt(parts[0].trim());
            String dayPart = parts[1].trim().split(" ")[0];
            day = Integer.parseInt(dayPart);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Could not parse a valid month or day number from '" + monthDayString + "'.", e);
        }

        // Calendar 的月份是从 0 开始的 (0=JAN, 1=FEB, ...)
        if (month < 1 || month > 12) {
            throw new IllegalArgumentException("Month must be between 1 and 12, but got: " + month);
        }

        Calendar calendar = Calendar.getInstance(timeZone);
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month - 1); // 月份 0-indexed
        calendar.set(Calendar.DAY_OF_MONTH, day);

        // 验证日期是否有效 (Calendar 会自动调整，例如 2月30日会变成3月2日，所以需要额外检查)
        // 如果设置的日期和获取的日期不一致，说明输入日期无效（被调整了）
        if (calendar.get(Calendar.MONTH) != (month - 1) || calendar.get(Calendar.DAY_OF_MONTH) != day) {
            throw new IllegalArgumentException("Invalid date specified: " + year + "-" + month + "-" + day +
                    " (date might be out of range, e.g., February 30th, or year was adjusted).");
        }

        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.MINUTE, minute);
        calendar.set(Calendar.SECOND, second);
        calendar.set(Calendar.MILLISECOND, millisecond);

        return calendar.getTimeInMillis();
    }

    /**
     * 获取指定年和月日字符串（如 "MM-dd" 或 "MM-dd HH"）的0点时间戳。
     *
     * @param year           年份 (int)
     * @param monthDayString 月日字符串，格式可以为 "MM-dd" 或 "MM-dd HH:mm:ss" 等。
     * @return 0点时间戳 (豪秒)
     * @throws IllegalArgumentException 如果 monthDayString 格式不正确或日期无效
     */
    public long getMidnightTimestamp(int year, String monthDayString) {
        return toTimestamp(year, monthDayString, 0, 0, 0, 0, TimeZone.getDefault());
    }

    /**
     * 获取指定年和月日字符串（如 "MM-dd" 或 "MM-dd HH"）的23:59:59点时间戳。
     *
     * @param year           年份 (int)
     * @param monthDayString 月日字符串，格式可以为 "MM-dd" 或 "MM-dd HH:mm:ss" 等。
     * @return 23:59:59点时间戳 (豪秒)
     * @throws IllegalArgumentException 如果 monthDayString 格式不正确或日期无效
     */
    public long getEndOfDayTimestamp(int year, String monthDayString) {
        return toTimestamp(year, monthDayString, 23, 59, 59, 0, TimeZone.getDefault());
    }

    public long timestampOf(long timestampMillis, int hour, int minute, int second, int millisecond) {
        Instant instant = Instant.ofEpochMilli(timestampMillis);
        LocalDateTime dateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        dateTime = dateTime.withHour(hour).withMinute(minute).withSecond(second).withNano(millisecond);
        return dateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * 计算时间戳偏移
     *
     * @param timestamp 原始时间戳（毫秒）
     * @param value     偏移值
     * @param unit      偏移单位（YEAR、MONTH、QUARTER、WEEK、DAY）
     * @return 计算后的时间戳（毫秒）
     */
    public long calculateTimestampByOffset(long timestamp, int value, TimeUnit unit) {
        LocalDateTime dateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());
        switch (unit) {
            case YEAR:
                dateTime = dateTime.plusYears(value);
                break;
            case MONTH:
                dateTime = dateTime.plusMonths(value);
                break;
            case QUARTER:
                dateTime = dateTime.plusMonths(value * 3L);
                break;
            case WEEK:
                dateTime = dateTime.plusWeeks(value);
                break;
            case DAY:
                dateTime = dateTime.plusDays(value);
                break;
            default:
                throw new IllegalArgumentException("Unsupported time unit: " + unit);
        }

        return dateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }
}
