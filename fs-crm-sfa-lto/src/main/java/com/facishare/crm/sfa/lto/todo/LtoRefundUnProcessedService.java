package com.facishare.crm.sfa.lto.todo;

import com.facishare.crm.sfa.lto.todo.enums.LtoSessionBOCItemKeys;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.crm.sfa.lto.utils.TodoUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Demo class
 *
 * <AUTHOR>
 * @date 2019/10/23
 */
@Service
@Slf4j
public class LtoRefundUnProcessedService implements LtoIUnProcessedService {
    @Autowired
    private ServiceFacade serviceFacade;

    @Override
    public String getObjectApiName() {
        return "RefundObj";
    }

    @Override
    public List<IObjectData> getTotalUnProcessedData(User user, LtoSessionBOCItemKeys sessionBOCItemKey) {
        List<IObjectData> dataList = Lists.newArrayList();
        SearchTemplateQuery query = new SearchTemplateQuery();
        handleSearchQuery(query, user);
        query.setLimit(150);
        IObjectDescribe objectDescribe = this.serviceFacade.findObject(user.getTenantId(), getObjectApiName());
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryWithDeleted(user, objectDescribe, query);
        if (!Objects.isNull(queryResult)) {
            dataList = queryResult.getData();
        }
        return dataList;
    }

    public static void handleSearchQuery(SearchTemplateQuery query, User user) {
        List<String> owners = TodoUtils.getObjectOwnerIdList(user);
        List<IFilter> filters = Lists.newArrayList();
        IFilter filter = new Filter();
        filter.setFieldName("life_status");
        List<String> fieldValues = Lists.newArrayList();
        fieldValues.add(ObjectLifeStatus.UNDER_REVIEW.getCode());
        filter.setFieldValues(fieldValues);
        filter.setOperator(Operator.IN);
        if (owners.isEmpty()) {
            owners.add("");
        }
        filters.add(filter);
        IFilter filterIsFixedFlow = new Filter();
        filterIsFixedFlow.setFieldName("is_fixed_flow");
        List<String> filedValueIsFixedFlow = Lists.newArrayList();
        filedValueIsFixedFlow.add("false");
        filterIsFixedFlow.setFieldValues(filedValueIsFixedFlow);
        filterIsFixedFlow.setOperator(Operator.EQ);
        filters.add(filterIsFixedFlow);
        SearchUtil.fillFilterIn(filters, "owner", new ArrayList<>(owners));
        query.setFilters(filters);
    }
}
