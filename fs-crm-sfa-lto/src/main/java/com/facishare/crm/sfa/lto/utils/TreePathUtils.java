package com.facishare.crm.sfa.lto.utils;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.ServiceFacadeImpl;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseImportAction;
import com.facishare.paas.appframework.core.predef.action.BaseImportDataAction;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.common.exception.CRMErrorCode.FS_CRM_DEFOBJ_CHECKED_CAN_NOT_LOOKUP_MYSELF;

/**
 * @Description
 * <AUTHOR>
 * @Date 2021/2/2 18:24
 */

@Slf4j
public class TreePathUtils {

    private static final ServiceFacade SERVICE_FACADE = SpringUtil.getContext().getBean(ServiceFacadeImpl.class);

    public static final String TEN = "10";

    private final static Map<String, Map<String, String>> fieldMap = new HashMap<>();

    private static final String PATH_API_NAME = "path_api_name";
    private static final String ASSOCIATE_API_NAME = "associate_api_name";


    static {

        Map<String, String> accountObj = new HashMap<>();
        accountObj.put(PATH_API_NAME, "account_path");
        accountObj.put(ASSOCIATE_API_NAME, "parent_account_id");
        fieldMap.put(Utils.ACCOUNT_API_NAME, accountObj);

        Map<String, String> marketingEventObj = new HashMap<>();
        marketingEventObj.put(PATH_API_NAME, "marketing_event_path");
        marketingEventObj.put(ASSOCIATE_API_NAME, "parent_id");
        fieldMap.put(Utils.MARKETING_EVENT_API_NAME, marketingEventObj);

        Map<String, String> LandingPageObj = new HashMap<>();
        LandingPageObj.put(PATH_API_NAME, "landing_page_path");
        LandingPageObj.put(ASSOCIATE_API_NAME, "parent_landing_page_id");
        fieldMap.put("LandingPageObj", LandingPageObj);

        Map<String, String> PartnerObj = new HashMap<>(2);
        PartnerObj.put(PATH_API_NAME, "partner_path");
        PartnerObj.put(ASSOCIATE_API_NAME, "parent_id");
        fieldMap.put("PartnerObj", PartnerObj);

        Map<String, String> NewOpportunityObj = new HashMap<>();
        NewOpportunityObj.put(PATH_API_NAME, "new_opportunity_path");
        NewOpportunityObj.put(ASSOCIATE_API_NAME, "parent_id");
        fieldMap.put("NewOpportunityObj", NewOpportunityObj);

        Map<String, String> contactObj = new HashMap<>();
        contactObj.put(PATH_API_NAME, "treepath");
        contactObj.put(ASSOCIATE_API_NAME, "parent_id");
        fieldMap.put("ContactObj", contactObj);

        Map<String, String> accountTreeRelation = new HashMap<>();
        accountTreeRelation.put(PATH_API_NAME, "tree_path");
        accountTreeRelation.put(ASSOCIATE_API_NAME, "parent_id");
        fieldMap.put("AccountTreeRelationObj", accountTreeRelation);

        Map<String, String> subAccountTreeRelation = new HashMap<>();
        subAccountTreeRelation.put(PATH_API_NAME, "tree_path");
        subAccountTreeRelation.put(ASSOCIATE_API_NAME, "parent_id");
        fieldMap.put("SubAccountTreeRelationObj", subAccountTreeRelation);


        Map<String, String> accountDepartment = new HashMap<>();
        accountDepartment.put(PATH_API_NAME, "tree_path");
        accountDepartment.put(ASSOCIATE_API_NAME, "parent_id");
        fieldMap.put("AccountDepartmentObj", accountDepartment);

        Map<String, String> ContactRelationship = new HashMap<>();
        ContactRelationship.put(PATH_API_NAME, "tree_path");
        ContactRelationship.put(ASSOCIATE_API_NAME, "parent_id");
        fieldMap.put("ContactRelationshipObj", ContactRelationship);

        Map<String, String> partnerDepartment = new HashMap<>();
        partnerDepartment.put(PATH_API_NAME, "tree_path");
        partnerDepartment.put(ASSOCIATE_API_NAME, "parent_id");
        fieldMap.put("PartnerDepartmentObj", partnerDepartment);

        Map<String, String> partnerContactRelationship = new HashMap<>();
        partnerContactRelationship.put(PATH_API_NAME, "tree_path");
        partnerContactRelationship.put(ASSOCIATE_API_NAME, "parent_id");
        fieldMap.put("PartnerContactRelationshipObj", partnerContactRelationship);

        Map<String, String> newOpportunityContactRelationship = new HashMap<>();
        newOpportunityContactRelationship.put(PATH_API_NAME, "tree_path");
        newOpportunityContactRelationship.put(ASSOCIATE_API_NAME, "parent_id");
        fieldMap.put("NewOpportunityContactRelationshipObj", newOpportunityContactRelationship);

        Map<String, String> loyaltyMember = new HashMap<>();
        loyaltyMember.put(PATH_API_NAME, "tree_path");
        loyaltyMember.put(ASSOCIATE_API_NAME, "parent_id");
        fieldMap.put("LoyaltyMemberObj", loyaltyMember);
    }

    /**
     * 根据id获取所有叶子节点
     *
     * @param user
     * @param dataIds
     * @return
     */
    public static List<IObjectData> getChildrenList(User user, String apiName, Set<String> dataIds) {
        if (CollectionUtils.empty(dataIds)) {
            return Lists.newArrayList();
        }
        // 检查当前租户上有没有对应的字段描述
        String fieldApiName = fieldMap.get(apiName).get(ASSOCIATE_API_NAME);
        IObjectDescribe objectDescribe = SERVICE_FACADE.findObject(user.getTenantId(), apiName);
        if (ObjectUtils.isEmpty(objectDescribe) || ObjectUtils.isEmpty(objectDescribe.getFieldDescribe(fieldApiName))) {
            return Lists.newArrayList();
        }
        // 查询叶子节点
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, fieldApiName, Lists.newArrayList(dataIds));
        SearchTemplateQuery searchTemplateQuery = getSearchTemplateQuery(filters);
        searchTemplateQuery.setNeedReturnQuote(false);
        IActionContext actionContext = buildActionContext(user);
        QueryResult<IObjectData> objectDataLists = SERVICE_FACADE.findBySearchQuery(actionContext, apiName,
                searchTemplateQuery);
        if (CollectionUtils.empty(objectDataLists.getData())) {
            return Lists.newArrayList();
        }
        return objectDataLists.getData();
    }

    /**
     * 检查是否成环
     *
     * @param user
     * @param objectDataList
     */
    public static void checkIsHoop(User user, List<IObjectData> objectDataList, IObjectDescribe associateDescribe) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        changeObjectDataPath(user, objectDataList, Maps.newHashMap(), false, "", Lists.newArrayList(), associateDescribe);
    }

    /**
     * 检查是否成环,编辑导入专用
     *
     * @param user
     * @param objectDataList
     */
    public static List<BaseImportAction.ImportError> checkIsHoopForImport(User user, String importType, List<IObjectData> objectDataList, IObjectDescribe describe) {
        if (CollectionUtils.empty(objectDataList)) {
            return Lists.newArrayList();
        }
        List<BaseImportAction.ImportError> importErrors = Lists.newArrayList();
        changeObjectDataPath(user, objectDataList, Maps.newHashMap(), false, importType, importErrors, describe);
        return importErrors;
    }

    /**
     * 处理新增导入需要校验的数据
     *
     * @param user
     * @param dataList
     */
    public static List<BaseImportAction.ImportError> checkPathForImport(User user, String apiName, List<BaseImportDataAction.ImportData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return Lists.newArrayList();
        }
        List<IObjectData> needCheckDataList = Lists.newArrayList();
        dataList.forEach(m -> {
            String parentId = m.getData().get(fieldMap.get(apiName).get(ASSOCIATE_API_NAME), String.class);
            if (!Strings.isNullOrEmpty(parentId)) {
                m.getData().set("row_no", m.getRowNo());
                if (Strings.isNullOrEmpty(m.getData().getId())) {
                    m.getData().setId(SERVICE_FACADE.generateId());
                }
                needCheckDataList.add(m.getData());
            }
        });
        if (CollectionUtils.empty(needCheckDataList)) {
            return Lists.newArrayList();
        }
        IObjectDescribe describe = ObjectDescribeUtils.getObjectDescribe(user.getTenantId(), apiName);
        return checkIsHoopForImport(user,
                "addImport", needCheckDataList, describe);
    }

    /**
     * 根据路径获取客户列表，包含作废删除的
     *
     * @param user
     * @param currentAccountIds
     * @return
     */
    public static List<IObjectData> getDataListByPathWithDeleted(User user, IObjectDescribe objectDescribe,
                                                                 Set<String> currentAccountIds, String pathFieldApiName) {
        if (CollectionUtils.empty(currentAccountIds)) {
            return Lists.newArrayList();
        }
        Set<String> realCurrentAccountIds = filtersName(user, objectDescribe, currentAccountIds);
        if (CollectionUtils.empty(realCurrentAccountIds)) {
            return Lists.newArrayList();
        }
        List<IFilter> filters = Lists.newArrayList();
        String matchValue = String.format("*.%s.*", Joiner.on("|").join(realCurrentAccountIds));
        SearchUtil.fillFilterMatch(filters, pathFieldApiName, matchValue);
        return getDataListWithDeleted(user, objectDescribe, filters);
    }

    /**
     * @param currentAccountIds 里面包含了name，因为不存在数据库的数据，name不会转换成id
     * @return 通过数据库id进行过滤，返回的都是数据库存在的id
     */
    public static Set<String> filtersName(User user, IObjectDescribe objectDescribe, Set<String> currentAccountIds) {
        //currentAccountIds中包含了name,过滤掉数据库不存在的name,只用id去查询
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, IObjectData.ID, Lists.newArrayList(currentAccountIds));
        SearchTemplateQuery searchTemplateQuery = getSearchTemplateQuery(filters);
        IActionContext actionContext = buildActionContext(user);
        QueryResult<IObjectData> objectDataResult = SERVICE_FACADE.findBySearchQueryWithDeleted(actionContext, objectDescribe,
                searchTemplateQuery);
        if (objectDataResult == null || CollectionUtils.empty(objectDataResult.getData())) {
            return Sets.newHashSet();
        }
        return objectDataResult.getData().stream().map(IObjectData::getId).collect(Collectors.toSet());
    }

    /**
     * 根据id获取客户列表，包含作废删除的
     *
     * @param user
     * @param currentAccountIds
     * @return
     */
    public static List<IObjectData> getDataListByIdWithDeleted(User user, IObjectDescribe objectDescribe,
                                                               List<String> currentAccountIds) {
        if (CollectionUtils.empty(currentAccountIds)) {
            return Lists.newArrayList();
        }
        List<IFilter> filters = Lists.newArrayList();
        if (currentAccountIds.size() > 1) {
            SearchUtil.fillFilterIn(filters, IObjectData.ID, currentAccountIds);
        } else {
            SearchUtil.fillFilterEq(filters, IObjectData.ID, currentAccountIds);
        }
        return getDataListWithDeleted(user, objectDescribe, filters);
    }

    /**
     * 根据条件获取客户列表
     *
     * @param user
     * @param filters
     * @return
     */
    public static List<IObjectData> getDataListWithDeleted(User user, IObjectDescribe objectDescribe, List<IFilter> filters) {
        if (CollectionUtils.empty(filters)) {
            return Lists.newArrayList();
        }
        SearchTemplateQuery searchTemplateQuery = getSearchTemplateQuery(filters);
        searchTemplateQuery.setNeedReturnQuote(false);
        IActionContext actionContext = buildActionContext(user);
        QueryResult<IObjectData> objectDataResult = SERVICE_FACADE.findBySearchQueryWithDeleted(actionContext, objectDescribe,
                searchTemplateQuery);
        if (CollectionUtils.empty(objectDataResult.getData())) {
            return Lists.newArrayList();
        }
        return objectDataResult.getData();
    }

    /**
     * 组装数据并校验
     *
     * @param user
     * @param objectDataList
     * @param needChangeData
     * @param needSave
     */
    public static void changeObjectDataPath(User user, List<IObjectData> objectDataList, Map<String, IObjectData> needChangeData,
                                            Boolean needSave, String importType, List<BaseImportAction.ImportError> importErrors, IObjectDescribe describe) {
        StopWatch stopWatch = StopWatch.create("changeMarketingEventPath");
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }

        String apiName = describe.getApiName();
        Set<String> parentIds = Sets.newHashSet();
        parentIds.addAll(objectDataList.stream()
                .filter(n -> !Strings.isNullOrEmpty(n.get(fieldMap.get(apiName).get(ASSOCIATE_API_NAME), String.class)))
                .map(m -> m.get(fieldMap.get(apiName).get(ASSOCIATE_API_NAME), String.class))
                .collect(Collectors.toSet()));

        Set<String> accountIds = objectDataList.stream().map(DBRecord::getId).collect(Collectors.toSet());
        parentIds.addAll(accountIds);
        stopWatch.lap("findParentAccountIds");

        //获取所有可能受影响的数据
        List<IObjectData> allDataList = Lists.newArrayList();
        allDataList.addAll(getDataListByPathWithDeleted(user, describe, parentIds, fieldMap.get(apiName).get(PATH_API_NAME)));
        stopWatch.lap("findAllDataList");

        //先移除后添加保证数据是最新的，并且全面的
        allDataList.removeIf(m -> accountIds.contains(m.getId()));
        allDataList.addAll(objectDataList);

        //补充可能没有查到的根节点
        Set<String> allAccountIds = allDataList.stream().map(m -> m.getId()).collect(Collectors.toSet());
        parentIds.removeAll(allAccountIds);
        if (CollectionUtils.notEmpty(parentIds)) {
            List<IObjectData> accountRootList = getDataListByIdWithDeleted(user, describe,
                    Lists.newArrayList(parentIds));
            stopWatch.lap("findAccountRootList");
            if (CollectionUtils.notEmpty(accountRootList)) {
                allDataList.addAll(accountRootList);
            }
        }
        if (CollectionUtils.empty(allDataList)) {
            return;
        }

        //获取在当前数据列表中算作根节点的数据
        List<IObjectData> rootObjectDataList = getRootObjectData(apiName, objectDataList, importType, importErrors, allDataList);
        stopWatch.lap("findRootAccountList");

        if (CollectionUtils.empty(rootObjectDataList)) {
            if (!Strings.isNullOrEmpty(importType)) {
                objectDataList.forEach(m -> {
                    setImportErrors(importErrors, m, I18N.text(SfaLtoI18NKeyUtil.SFA_MARKETING_EVENT_CHILDISFATHER),
                            true, objectDataList);
                });
                return;
            } else {
                throw new ValidateException(I18N.text(SfaLtoI18NKeyUtil.SFA_MARKETING_EVENT_CHILDISFATHER));
            }
        }

        //处理根节点
        changeRootData(user, describe, objectDataList, needChangeData, needSave, importType, importErrors,
                allDataList, rootObjectDataList);
        stopWatch.lap("changeRootAccountList");
        stopWatch.logSlow(1000);
    }

    /**
     * 获取在当前数据列表中算作根节点的数据
     *
     * @param objectDataList
     * @param importType
     * @param importErrors
     * @param allDataList
     * @return
     */
    @Nullable
    private static List<IObjectData> getRootObjectData(String apiName, List<IObjectData> objectDataList, String importType,
                                                       List<BaseImportAction.ImportError> importErrors,
                                                       List<IObjectData> allDataList) {
        List<IObjectData> rootAccountList = Lists.newArrayList();
        //标记根节点
        Iterator<IObjectData> it = objectDataList.iterator();
        while (it.hasNext()) {
            IObjectData m = it.next();
            if (m.getId().equals(m.get(fieldMap.get(apiName).get(ASSOCIATE_API_NAME), String.class))) {
                if (!Strings.isNullOrEmpty(importType)) {
                    setImportErrors(importErrors, m, FS_CRM_DEFOBJ_CHECKED_CAN_NOT_LOOKUP_MYSELF.getMessage(),
                            true, objectDataList);
                    allDataList.removeIf(n -> n.getId().equals(m.getId()));
                } else {
                    throw new ValidateException(FS_CRM_DEFOBJ_CHECKED_CAN_NOT_LOOKUP_MYSELF.getMessage());
                }
            } else if (Strings.isNullOrEmpty(m.get(fieldMap.get(apiName).get(ASSOCIATE_API_NAME), String.class)) ||
                    objectDataList.stream().noneMatch(x -> x.getId().equals(m.get(fieldMap.get(apiName).get(ASSOCIATE_API_NAME), String.class)))) {
                rootAccountList.add(m);
            }
        }
        return rootAccountList;
    }

    /**
     * 给导入错误赋值
     *
     * @param importErrors
     * @param child
     * @param text
     * @param isSelf
     * @param objectDataList
     */
    private static void setImportErrors(List<BaseImportAction.ImportError> importErrors, IObjectData child, String text,
                                        boolean isSelf, List<IObjectData> objectDataList) {
        //depth避免无限递归
        if (child == null) {
            return;
        }
        String apiName = child.getDescribeApiName();
        if (isSelf) {
            Integer rowNo = child.get("row_no", Integer.class);
            if (rowNo == null || rowNo == 0) {
                return;
            }
            if (importErrors.stream().anyMatch(x -> rowNo.equals(x.getRowNo()))) {
                return;
            }
            importErrors.add(new BaseImportAction.ImportError(child.get("row_no", Integer.class), text));
        } else {
            String accountPath = child.get(fieldMap.get(apiName).get(PATH_API_NAME), String.class);
            Set<String> parentAccountIds = Sets.newHashSet(accountPath.split("\\."));
            objectDataList.forEach(m -> {
                if (parentAccountIds.contains(m.getId())) {
                    Integer rowNo = m.get("row_no", Integer.class);
                    if (rowNo == null || rowNo == 0) {
                        return;
                    }
                    if (importErrors.stream().anyMatch(x -> rowNo.equals(x.getRowNo()))) {
                        return;
                    }
                    importErrors.add(new BaseImportAction.ImportError(m.get("row_no", Integer.class), text));
                }
            });
        }
    }

    /**
     * 处理提供数据的根节点
     *
     * @param objectDataList
     * @param needChangeData
     * @param needSave
     * @param importType
     * @param importErrors
     * @param allDataList
     * @param rootAccountList
     */
    private static void changeRootData(User user, IObjectDescribe accountDescribe, List<IObjectData> objectDataList,
                                       Map<String, IObjectData> needChangeData,
                                       Boolean needSave, String importType, List<BaseImportAction.ImportError> importErrors,
                                       List<IObjectData> allDataList, List<IObjectData> rootAccountList) {
        Iterator<IObjectData> rootIt = rootAccountList.iterator();
        String apiName = accountDescribe.getApiName();
        while (rootIt.hasNext()) {
            IObjectData child = rootIt.next();
            if (Strings.isNullOrEmpty(child.get(fieldMap.get(apiName).get(ASSOCIATE_API_NAME), String.class)) ||
                    "invalid".equals(child.get("life_status", String.class)) || Boolean.TRUE.equals(child.isDeleted())) {
                child.set(fieldMap.get(apiName).get(PATH_API_NAME), child.getId());
                if (needSave) {
                    needChangeData.put(child.getId(), child);
                }
                recursionChangeData(allDataList, child, needChangeData, needSave, importType, importErrors,
                        0, objectDataList);
                continue;
            }
            Optional<IObjectData> parentAccountCurrentOptional = allDataList.stream()
                    .filter(n -> n.getId().equals(child.get(fieldMap.get(apiName).get(ASSOCIATE_API_NAME), String.class)))
                    .findAny();
            parentAccountCurrentOptional.ifPresent(parent -> {
                String accountPath = getOldAccountPath(apiName, parent);
                changeParentAccountPath(apiName, parent, needChangeData, needSave);
                if (accountPath.contains(child.getId())) {
                    if (!Strings.isNullOrEmpty(importType)) {
                        setImportErrors(importErrors, child, I18N.text(SfaLtoI18NKeyUtil.SFA_MARKETING_EVENT_CHILDISFATHER),
                                true, objectDataList);
                        allDataList.removeIf(n -> n.getId().equals(child.getId()));
                    } else {
                        throw new ValidateException(I18N.text(SfaLtoI18NKeyUtil.SFA_MARKETING_EVENT_CHILDISFATHER));
                    }
                    recursionChangeData(allDataList, child, needChangeData, needSave, importType, importErrors,
                            0, objectDataList);
                    return;
                }
                String newAccountPath = accountPath + child.getId();
                if (newAccountPath.split("\\.").length > 10) {
                    if (!Strings.isNullOrEmpty(importType)) {
                        setImportErrors(importErrors, child, I18N.text(SfaLtoI18NKeyUtil.SFA_OBJ_PATH_PARENT_ID_NOT_EXCEED, TEN),
                                true, objectDataList);
                        allDataList.removeIf(n -> n.getId().equals(child.getId()));
                    } else {
                        throw new ValidateException(I18N.text(SfaLtoI18NKeyUtil.SFA_OBJ_PATH_PARENT_ID_NOT_EXCEED, TEN));
                    }
                    recursionChangeData(allDataList, child, needChangeData, needSave, importType, importErrors,
                            0, objectDataList);
                    return;
                }
                child.set(fieldMap.get(apiName).get(PATH_API_NAME), newAccountPath);
                if (needSave) {
                    needChangeData.put(child.getId(), child);
                }
                recursionChangeData(allDataList, child, needChangeData, needSave, importType, importErrors,
                        0, objectDataList);
                return;
            });
            if (!parentAccountCurrentOptional.isPresent()) {
                List<IObjectData> parentObect = getDataListByIdWithDeleted(user, accountDescribe,
                        Lists.newArrayList(child.get(fieldMap.get(apiName).get(ASSOCIATE_API_NAME), String.class)));
                if (CollectionUtils.notEmpty(parentObect)) {
                    changeParentAccountPath(apiName, parentObect.get(0), needChangeData, needSave);
                    allDataList.addAll(parentObect);
                    recursionChangeData(allDataList, parentObect.get(0), needChangeData, needSave, importType, importErrors,
                            0, objectDataList);
                    continue;
                }
                child.set(fieldMap.get(apiName).get(PATH_API_NAME), child.getId());
                if (needSave) {
                    needChangeData.put(child.getId(), child);
                }
                recursionChangeData(allDataList, child, needChangeData, needSave, importType, importErrors,
                        0, objectDataList);
                continue;
            }
        }
    }

    /**
     * 以当前节点为根节点处理下级节点数据
     *
     * @param parent
     */
    public static void recursionChangeData(List<IObjectData> allDataList, IObjectData parent,
                                           Map<String, IObjectData> needChangeData, Boolean needSave, String importType,
                                           List<BaseImportAction.ImportError> importErrors,
                                           int depth, List<IObjectData> objectDataList) {
        //depth避免无限递归
        if (parent == null) {
            return;
        } else if (depth > 1000) {
            log.warn("递归循环超过1000次，请注意验证！tenantId:{},parentId:{}", parent.getTenantId(), parent.getId());
            return;
        }
        depth++;

        String apiName = parent.getDescribeApiName();
        List<IObjectData> leafNodeList = allDataList.stream()
                .filter(n -> parent.getId().equals(n.get(fieldMap.get(apiName).get(ASSOCIATE_API_NAME), String.class)))
                .collect(Collectors.toList());
        if (CollectionUtils.notEmpty(leafNodeList)) {
            Iterator<IObjectData> leafIt = leafNodeList.iterator();
            while (leafIt.hasNext()) {
                IObjectData child = leafIt.next();
                if ("invalid".equals(child.get("life_status", String.class)) || Boolean.TRUE.equals(child.isDeleted())) {
                    child.set(fieldMap.get(apiName).get(PATH_API_NAME), child.getId());
                    if (needSave) {
                        needChangeData.put(child.getId(), child);
                    }
                    recursionChangeData(allDataList, child, needChangeData, needSave, importType,
                            importErrors, depth, objectDataList);
                    continue;
                }

                if (child.getId().equals(child.get(fieldMap.get(apiName).get(ASSOCIATE_API_NAME), String.class))) {
                    if (!Strings.isNullOrEmpty(importType)) {
                        //子节点中存在自关联，说明是脏数据，导入不处理
                        allDataList.removeIf(n -> n.getId().equals(child.getId()));
                    } else {
                        throw new ValidateException(FS_CRM_DEFOBJ_CHECKED_CAN_NOT_LOOKUP_MYSELF.getMessage());
                    }
                    recursionChangeData(allDataList, child, needChangeData, needSave, importType,
                            importErrors, depth, objectDataList);
                    continue;
                }

                String parentAccountPath = getOldAccountPath(apiName, parent);

                if (parentAccountPath.contains(child.getId())) {
                    if (!Strings.isNullOrEmpty(importType)) {
                        setImportErrors(importErrors, parent, I18N.text(SfaLtoI18NKeyUtil.SFA_MARKETING_EVENT_CHILDISFATHER),
                                false, objectDataList);
                        allDataList.removeIf(n -> n.getId().equals(child.getId()));
                    } else {
                        throw new ValidateException(I18N.text(SfaLtoI18NKeyUtil.SFA_MARKETING_EVENT_CHILDISFATHER));
                    }
                    recursionChangeData(allDataList, child, needChangeData, needSave, importType,
                            importErrors, depth, objectDataList);
                    continue;
                }

                String newAccountPath = parentAccountPath + child.getId();
                if (newAccountPath.split("\\.").length > 10) {
                    if (!Strings.isNullOrEmpty(importType)) {
                        child.set(fieldMap.get(apiName).get(PATH_API_NAME), newAccountPath);
                        setImportErrors(importErrors, child, I18N.text(SfaLtoI18NKeyUtil.SFA_OBJ_PATH_PARENT_ID_NOT_EXCEED, TEN),
                                false, objectDataList);
                        allDataList.removeIf(n -> n.getId().equals(child.getId()));
                    } else {
                        throw new ValidateException(I18N.text(SfaLtoI18NKeyUtil.SFA_OBJ_PATH_PARENT_ID_NOT_EXCEED, TEN));
                    }
                    recursionChangeData(allDataList, child, needChangeData, needSave, importType,
                            importErrors, depth, objectDataList);
                    continue;
                }

                child.set(fieldMap.get(apiName).get(PATH_API_NAME), newAccountPath);
                if (needSave) {
                    needChangeData.put(child.getId(), child);
                }

                recursionChangeData(allDataList, child, needChangeData, needSave, importType,
                        importErrors, depth, objectDataList);
                continue;
            }
        }
    }


    @NotNull
    private static String getOldAccountPath(String apiName, IObjectData parent) {
        String accountPath = parent.get(fieldMap.get(apiName).get(PATH_API_NAME), String.class) + ".";
        if ("invalid".equals(parent.get("life_status", String.class)) || Boolean.TRUE.equals(parent.isDeleted())) {
            accountPath = "";
        } else if (Strings.isNullOrEmpty(parent.get(fieldMap.get(apiName).get(PATH_API_NAME), String.class))) {
            accountPath = parent.getId() + ".";
        }
        return accountPath;
    }


    @NotNull
    private static void changeParentAccountPath(String apiName, IObjectData parent, Map<String, IObjectData> needChangeData, Boolean needSave) {
        String accountPath = parent.get(fieldMap.get(apiName).get(PATH_API_NAME), String.class);
        if ("invalid".equals(parent.get("life_status", String.class)) || Boolean.TRUE.equals(parent.isDeleted())) {
            return;
        }
        if (Strings.isNullOrEmpty(accountPath)) {
            accountPath = parent.getId();
            parent.set(fieldMap.get(apiName).get(PATH_API_NAME), accountPath);
            if (needSave) {
                needChangeData.put(parent.getId(), parent);
            }
        }
    }


    public static IActionContext buildActionContext(User user) {
        return ActionContextExt.of(user, RequestContextManager.getContext())
                .set("skip_relevantTeam", true)
                .getContext();
    }


    /**
     * 根据路径获取数据列表，不包含作废删除的
     *
     * @param user
     * @param ids
     * @return
     */
    public static List<IObjectData> getTreePathActiveList(User user, Set<String> ids, String pathFieldName, String objectApiName) {
        if (CollectionUtils.empty(ids)) {
            return Lists.newArrayList();
        }
        List<IFilter> filters = Lists.newArrayList();
        String matchValue = String.format("*.%s.*", Joiner.on("|").join(ids));
        SearchUtil.fillFilterMatch(filters, pathFieldName, matchValue);
        SearchTemplateQuery searchTemplateQuery = getSearchTemplateQuery(filters);
        searchTemplateQuery.setOrders(Lists.newArrayList(new OrderBy("last_modified_time", false)));
        IActionContext actionContext = buildActionContext(user);
        QueryResult<IObjectData> accountList = SERVICE_FACADE.findBySearchQuery(actionContext, objectApiName,
                searchTemplateQuery);
        if (CollectionUtils.empty(accountList.getData())) {
            return Lists.newArrayList();
        }
        return accountList.getData();
    }

    private static SearchTemplateQuery getSearchTemplateQuery(List<IFilter> filters) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(2000);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setPermissionType(0);
        searchTemplateQuery.setFilters(filters);
        return searchTemplateQuery;
    }
}
