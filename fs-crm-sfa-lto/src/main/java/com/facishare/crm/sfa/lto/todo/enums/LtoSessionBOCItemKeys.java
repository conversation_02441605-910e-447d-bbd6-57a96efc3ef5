package com.facishare.crm.sfa.lto.todo.enums;


public enum LtoSessionBOCItemKeys {
    TOBE_ASSIGNED_SALES_CLUE("401", "待分配的销售线索", "销售线索"),
    TobeProcessedSalesClue("402", "待处理的销售线索", "销售线索"),
    TobeCheckedCustomer("403", "待审批的客户", "客户"),
    TobeConfirmedCustomerTrade("404", "待确认的销售订单", "销售订单"),
    CRMNotice("405", "CRM通知", ""),
    TobeConfirmedSaleActionStage("406", "待确认的销售阶段", "商机"),
    TobeConfirmedTradePayment("407", "待确认的回款", "回款"),
    TobeConfirmedTradeRefund("408", "待确认的退款", "退款"),
    TobeFinanceConfirmedCustomerTrade("409", "待财务确认的订单", "销售订单"),
    TobeFinanceConfirmedTradeBill("410", "待确认的开票申请", "开票申请"),
    TobeConfirmedReturnOrder("411", "待确认的退货单", "退货单"),
    ToBeProcessedBusinessByMe("451", "待我处理的业务流程", "业务流程"),
    ToBeProcessedApprovalByMe("452", "待处理的审批流程", "固定审批流"),
    ApprovalSentByMe("453", "我发出的审批流程", "固定审批流"),
    ApprovalPush("454", "审批通知", "固定审批流"),
    ImportAssistant("455", "导入工具通知", "导入"),
    TobeDeliveryCustomerOrder("456", "待发货的订单", "销售订单"),
    TobeProcessedBusinessProcess("457", "待处理的业务流程", "业务流程"),
    WorkFlowPush("458", "工作流通知", "工作流"),
    ToBeProcessedAccountWarn("459", "待预警的客户账户", "客户账户"),
    ToBeProcessedNewOpportuntiy("460", "待处理的商机2.0", "商机2.0");


    private  String code;
    private  String description;
    private  String belongTo;

    LtoSessionBOCItemKeys(String code, String description, String belongTo) {
        this.code = code;
        this.description = description;
        this.belongTo = belongTo;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public String getbelongTo() {
        return belongTo;
    }

    public static LtoSessionBOCItemKeys getEnumByCode(String code) {
        for (LtoSessionBOCItemKeys m : LtoSessionBOCItemKeys.values()) {
            if (m.getCode().equals(code)) {
                return m;
            }
        }
        return null;
    }
}
