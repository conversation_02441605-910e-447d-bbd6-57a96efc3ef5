package com.facishare.crm.sfa.lto.qywx.proxy.models;

import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface QywxTransferGroupModel {
  @Data
	@Builder
	class Arg {
    /**
     * 必填
     */
    private String ea;
    /**
     * 必填，需要转群主的客户群ID列表。取值范围： 1 ~ 100
     */
    private List<String> chatIdList;
    /**
     * 必填，新群主ID，纷享员工id
     */
    private String newOwner;
  }

  @Data
  class TransferGroupChatResult {
    private String chatId;//没能成功继承的群ID
    private Integer errCode;//没能成功继承的群，错误码
    private String errMsg;//没能成功继承的群，错误描述
  }
}