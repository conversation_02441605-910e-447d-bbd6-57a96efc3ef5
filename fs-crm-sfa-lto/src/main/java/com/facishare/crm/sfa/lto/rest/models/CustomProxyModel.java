package com.facishare.crm.sfa.lto.rest.models;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> lik
 * @date : 2022/9/9 11:27
 */

public interface CustomProxyModel {
    @Data
    @Builder
    class Arg {
        String ea;
        List<String> ruleIdList;
        String startDate;
        String endDate;
        String ruleId;
    }

    @Data
    class Result {
        private Integer errorCode;
        private String message;
        private List<Map> ruleIdAndNameMap;
        private Map<String,List<String>> ruleIdAndWorkMap;
    }
}
