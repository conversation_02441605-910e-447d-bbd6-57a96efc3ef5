package com.facishare.crm.sfa.lto.qywx.mongo;

import com.mongodb.DBCollection;
import com.mongodb.DBObject;
import org.mongodb.morphia.Datastore;
import org.mongodb.morphia.query.AbstractQueryFactory;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.QueryImpl;

public class InTraceQueryFactory extends AbstractQueryFactory {
    @Override
    public <T> Query<T> createQuery(Datastore datastore, DBCollection collection, Class<T> type, DBObject query) {
        final QueryImpl<T> item = new InTraceQueryImpl<>(type, collection, datastore);

        if (query != null) {
            item.setQueryObject(query);
        }

        return item;
    }
}
