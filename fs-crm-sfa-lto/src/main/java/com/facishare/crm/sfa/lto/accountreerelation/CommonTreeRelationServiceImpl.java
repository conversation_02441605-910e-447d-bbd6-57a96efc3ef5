package com.facishare.crm.sfa.lto.accountreerelation;

import com.alibaba.fastjson.JSONObject;
import com.facishare.common.parallel.ParallelUtils;
import com.facishare.crm.sfa.lto.accountreerelation.models.AccountTreeRelationModels.*;
import com.facishare.crm.sfa.lto.common.models.LtoFieldApiConstants;
import com.facishare.crm.sfa.lto.equityrelationship.model.EquityRelationshipDataModel;
import com.facishare.crm.sfa.lto.equityrelationship.service.EquityRelationshipService;
import com.facishare.crm.sfa.lto.exception.ExceptionUtil;
import com.facishare.crm.sfa.lto.utils.CommonTreeRelationUtil;
import com.facishare.crm.sfa.lto.utils.CommonSqlUtil;
import com.facishare.crm.sfa.lto.utils.ObjectDataUtil;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.CommonSqlOperator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.impl.search.WhereParam;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.fxiaoke.common.SqlEscaper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.lto.accountreerelation.models.AccountTreeRelationConstants.ROOT_ID;
import static com.facishare.crm.sfa.lto.utils.SfaLtoI18NKeyUtil.*;

@Component
@Slf4j
public class CommonTreeRelationServiceImpl implements ICommonTreeRelationService{
    @Autowired
    private IEquityRelationDataService equityRelationDataService;
    @Autowired
    private ObjectDataServiceImpl objectDataService;
    @Autowired
    private FunctionPrivilegeService functionPrivilegeService;
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    protected MatchServiceImpl matchService;
    @Autowired
    protected EquityRelationshipService equityRelationshipService;

    private static final String UNIQUE_TABLE_NAME = "biz_account_tree_relation_unique_temp";

    @Override
    public CreateAccountTreeRelationResult createTreeRelation(User user, CreateAccountTreeRelationArg arg) {
        CreateAccountTreeRelationResult result = CreateAccountTreeRelationResult.builder()
                .errorCode("0").message("").build();
        String tenantId= user.getTenantId();
        String companyName = arg.getCompanyName();
        baseCheck(tenantId, companyName,arg);

        try {
            EquityRelationshipDataModel.RelationshipModel equityRelationInfo = getRelationshipModel(user, arg);
            equityRelationDataService.processEquityRelationData(user, equityRelationInfo,arg);
        } catch (ValidateException validateException) {
            throw validateException;
        } catch (Exception e) {
            log.warn(String.format("CreateAccountTreeRelation error, tenantId:%s, companyName:%s", tenantId, companyName), e);
            ExceptionUtil.throwCommonBusinessException();
        } finally {
            deleteUniqueTable(tenantId, companyName,arg);
        }
        return result;
    }

    public void baseCheck(String tenantId, String companyName,CreateAccountTreeRelationArg arg) {
        if(StringUtils.isBlank(companyName)){
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }

        checkTreeRelationExists(tenantId, companyName,arg);

    }

    @Override
    public CreateAccountTreeRelationResult createTreeRelationAsync(User user, CreateAccountTreeRelationArg arg) {

        baseCheck(user.getTenantId(), arg.getCompanyName(),arg);
        if(EquityRelationshipService.QUERY_DATA_BY_BI){
            //纷享云处理
            equityRelationshipService.checkEquityRelationIsExist(user,arg.getCompanyName());
            //查询配额是否还有
            CommonTreeRelationUtil.handleTreeOverQuota(user,arg.getDescribeApiName());
            ParallelUtils.ParallelTask task = ParallelUtils.createBackgroundTask();
            task.submit(() -> {
                startAsyncHandle(null,user,arg);
            });
            task.run();
            return CreateAccountTreeRelationResult.builder().build();
        }
        // 招商局处理
        EquityRelationshipDataModel.RelationshipModel equityRelationInfo = getRelationshipModel(user, arg);
        ParallelUtils.ParallelTask task = ParallelUtils.createBackgroundTask();
        task.submit(() -> {
                startAsyncHandle(equityRelationInfo,user,arg);
        });
        task.run();
        return CreateAccountTreeRelationResult.builder().build();
    }

    public void startAsyncHandle(EquityRelationshipDataModel.RelationshipModel equityRelationInfo,User user, CreateAccountTreeRelationArg arg){
        log.warn("startAsyncHandle start companyName:{}.",arg.getCompanyName());
        insertUniqueTable(user.getTenantId(), arg.getCompanyName(),arg);
        try {
            if(ObjectUtils.isEmpty(equityRelationInfo)){
                equityRelationInfo = getRelationshipModel(user, arg);
            }
            equityRelationDataService.processEquityRelationData(user, equityRelationInfo,arg);
        }catch (Exception e) {
            CommonTreeRelationUtil.handleTreeException(user,arg.getDescribeApiName());
            log.warn(String.format("createTreeRelationAsync error, tenantId:%s, companyName:%s", user.getTenantId(), arg.getCompanyName()), e);
        }
        deleteUniqueTable(user.getTenantId(), arg.getCompanyName(),arg);

    }

    @Override
    public CheckCreateAccountTreeRelationResult checkCreateTreeRelation(User user, CreateAccountTreeRelationArg arg) {
        CheckCreateAccountTreeRelationResult result = CheckCreateAccountTreeRelationResult.builder()
                .errorCode("200").message("").status(1).build();
        String companyName = arg.getCompanyName();
        String tenantId = user.getTenantId();
        if(StringUtils.isBlank(companyName)){
            result.setErrorCode("5000");
            result.setStatus(0);
            result.setMessage(I18N.text(I18NKey.PARAM_ERROR));
            return result;
        }

        List<Map> queryResult = queryUniqueTable(tenantId, companyName,arg);
        if(CollectionUtils.isNotEmpty(queryResult)) {
            result.setErrorCode("5000");
            result.setStatus(2);
            result.setMessage(I18N.text(SFA_ACCOUNT_TREE_RELATION_PROCESSING));
            return result;
        }

        try {
            checkTreeRelationExists(tenantId, companyName,arg);
        } catch (Exception e) {
            String linkId = getTreeRelationExists(tenantId, companyName,arg);
            if(ObjectUtils.isNotEmpty(linkId)){
                result.setErrorCode("5000");
                result.setStatus(5);
                result.setMessage(I18N.text(SFA_COMMON_TREE_RELATION_EXISTS));
                result.setLinkId(linkId);
                return result;
            }
            result.setErrorCode("5000");
            result.setStatus(3);
            result.setMessage(I18N.text(SFA_ACCOUNT_TREE_RELATION_EXISTS));
            return result;
        }

        try {
            RelationshipsMapping relationshipsMapping = CommonTreeRelationUtil.getRelationshipsMapping(arg.getDescribeApiName());
            Map<String, Boolean> funcResult = functionPrivilegeService.funPrivilegeCheck(user, relationshipsMapping.getLinkDescribeApiName(), Lists.newArrayList("GenerateTreeRelation"));
            if(funcResult == null || funcResult.isEmpty() || !Boolean.TRUE.equals(funcResult.get("GenerateTreeRelation"))) {
                result.setErrorCode("5000");
                result.setStatus(0);
                result.setMessage(I18N.text(I18NKey.NO_PERMISSION_IN_OPERATE));
                return result;
            }
        } catch (Exception e) {
            result.setErrorCode("5000");
            result.setStatus(0);
            result.setMessage(I18N.text(I18NKey.NO_PERMISSION_IN_OPERATE));
            return result;
        }
        return result;
    }

    @Override
    public CreateAccountTreeRelationResult relateData2TreeRelation(User user, RelateMainData2TreeRelationArg arg) {
        CreateAccountTreeRelationResult result = CreateAccountTreeRelationResult.builder().errorCode("0").message("Success").build();
        if(CollectionUtils.isEmpty(arg.getObjectIds())) {
            return result;
        }
        log.warn("relateData2TreeRelation getObjectIds:{}", JSONObject.toJSONString(arg.getObjectIds()));
        RelationshipsMapping relationshipsMapping = CommonTreeRelationUtil.getRelationshipsMapping(arg.getDescribeApiName());
        List<IObjectData> dataList = matchService.getMatchedData(user.getTenantId(), arg.getObjectIds(),arg.getDescribeApiName());
        if(CollectionUtils.isEmpty(dataList)) {
            return result;
        }
        List<String> nameList = dataList.stream().map(x -> x.getName()).collect(Collectors.toList());
        log.info("relateMainData2TreeRelation nameList:{}", JSONObject.toJSONString(nameList));
        SearchTemplateQuery searchQuery = getBaseSearchQuery(user.getTenantId());
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, "name", nameList);
        SearchUtil.fillFilterIsNull(filters, relationshipsMapping.getLinkFieldApiName());
        searchQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = ObjectDataUtil.findDataBySearchQuery(user, relationshipsMapping.getLinkDescribeApiName(), searchQuery,
                Lists.newArrayList("tenant_id", "_id", "name", relationshipsMapping.getLinkFieldApiName(), CommonTreeRelationUtil.PARENT_REF_PREFIX+relationshipsMapping.getLinkFieldApiName(),
                        CommonTreeRelationUtil.ROOT_REF_PREFIX+relationshipsMapping.getLinkFieldApiName(), "is_root", "root_id", "object_describe_api_name"));
        if(queryResult == null || CollectionUtils.isEmpty(queryResult.getData())) {
            log.info("relateMainData2TreeRelation queryResult si null");
            return result;
        }
        List<IObjectData> updateDataList = Lists.newArrayList();
        queryResult.getData().forEach(x -> {
            Optional<IObjectData> op = dataList.stream().filter(d -> x.getName().equals(d.getName())).findFirst();
            if(op.isPresent()) {
                String rootId = ObjectDataUtil.getStringValue(x, "root_id", "");
                String accountMainDataId = op.get().getId();
                if(Boolean.TRUE.equals(ObjectDataUtil.getBooleanValue(x, "is_root", false))) {
                    updateRootData(user, x.getId(), accountMainDataId,relationshipsMapping);
                }
                updateChildrenData(user, rootId, x.getId(), accountMainDataId,relationshipsMapping);
                x.set(relationshipsMapping.getLinkFieldApiName(), accountMainDataId);
                updateDataList.add(x);
            }
        });
        if(CollectionUtils.isNotEmpty(updateDataList)) {
            try {
                ObjectDataUtil.updateFields(user, updateDataList, Lists.newArrayList(relationshipsMapping.getLinkFieldApiName()), false, true);
            } catch (Exception e) {
                log.error("updateAccountMainData error,tenantId:{}", user.getTenantId(), e);
            }
        }
        return result;
    }

    private SearchTemplateQuery getBaseSearchQuery(String tenantId) {
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(100);
        searchQuery.setOffset(0);
        searchQuery.setPermissionType(0);
        searchQuery.setNeedReturnCountNum(false);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, "tenant_id", tenantId);
        searchQuery.setFilters(filters);
        return searchQuery;
    }

    private void updateRootData(User user, String rootId, String accountMainDataId, RelationshipsMapping relationshipsMapping) {
        try {
            String rootField = CommonTreeRelationUtil.ROOT_REF_PREFIX+relationshipsMapping.getLinkFieldApiName();
            int maxExecuteCount = 100000;
            int executedCount = 0;
            SearchTemplateQuery searchQuery = getBaseSearchQuery(user.getTenantId());
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, "root_id", rootId);
            SearchUtil.fillFilterIsNull(filters, rootField);
            searchQuery.setFilters(filters);
            while (true) {
                ++executedCount;
                SearchTemplateQuery templateQuery = SearchUtil.copySearchTemplateQuery(searchQuery);
                QueryResult<IObjectData> queryResult = ObjectDataUtil.findDataBySearchQuery(user, relationshipsMapping.getLinkDescribeApiName(), templateQuery,
                        Lists.newArrayList("_id", "tenant_id", "object_describe_api_name"));
                if(queryResult == null || CollectionUtils.isEmpty(queryResult.getData())) {
                    return;
                }
                List<IObjectData> objectDataList = queryResult.getData();
                objectDataList.forEach(x -> x.set(rootField, accountMainDataId));
                ObjectDataUtil.updateFields(user, objectDataList, Lists.newArrayList(rootField), false, true);
                if(executedCount > maxExecuteCount) {
                    log.warn("updateRootAccountMainData executed max count 100000");
                    return;
                }
            }
        } catch (Exception e) {
            log.error("updateRootAccountMainData error,tenantId:{},rootId:{},accountMainDataId:{}", user.getTenantId(), rootId, accountMainDataId, e);
        }
    }

    private void updateChildrenData(User user, String rootId, String parentId, String accountMainDataId, RelationshipsMapping relationshipsMapping) {
        try {
            String parentField = CommonTreeRelationUtil.PARENT_REF_PREFIX+relationshipsMapping.getLinkFieldApiName();
            int maxExecuteCount = 100000;
            int executedCount = 0;
            SearchTemplateQuery searchQuery = getBaseSearchQuery(user.getTenantId());
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, "parent_id", parentId);
            SearchUtil.fillFilterEq(filters, "root_id", rootId);
            SearchUtil.fillFilterIsNull(filters, parentField);
            searchQuery.setFilters(filters);
            while (true) {
                ++executedCount;
                SearchTemplateQuery templateQuery = SearchUtil.copySearchTemplateQuery(searchQuery);
                QueryResult<IObjectData> queryResult = ObjectDataUtil.findDataBySearchQuery(user, relationshipsMapping.getLinkDescribeApiName(), templateQuery,
                        Lists.newArrayList("_id", "tenant_id", "object_describe_api_name"));
                if(queryResult == null || CollectionUtils.isEmpty(queryResult.getData())) {
                    return;
                }
                List<IObjectData> objectDataList = queryResult.getData();
                objectDataList.forEach(x -> x.set(parentField, accountMainDataId));
                ObjectDataUtil.updateFields(user, objectDataList, Lists.newArrayList(parentField), false, true);
                if(executedCount > maxExecuteCount) {
                    log.warn("updateChildrenAccountMainData executed max count 100000");
                    return;
                }
            }
        } catch (Exception e) {
            log.error("updateChildrenAccountMainData error,tenantId:{},rootId:{}, parentId:{}, accountMainDataId:{}",
                    user.getTenantId(), rootId, parentId, accountMainDataId, e);
        }
    }

    @Override
    public CreateAccountTreeRelationResult relateData2TreeRelationAsync(User user, RelateMainData2TreeRelationArg arg) {
        try {
            ParallelUtils.ParallelTask task = ParallelUtils.createBackgroundTask();
            task.submit(() -> relateData2TreeRelation(user, arg));
            task.run();
        } catch (Exception e) {
            log.warn("relateMainData2TreeRelationAsync error, tenantId:{}, objectIds:{}", user.getTenantId(), String.join(",", arg.getObjectIds()), e);
            ExceptionUtil.throwCommonBusinessException();
        }
        return CreateAccountTreeRelationResult.builder().errorCode("0").message("Success").build();
    }

    @Override
    public CheckCreateAccountTreeRelationResult deleteTreeRelationUniqueData(User user, CreateAccountTreeRelationArg arg) {
        CheckCreateAccountTreeRelationResult result = CheckCreateAccountTreeRelationResult.builder()
                .errorCode("200").message(I18N.text(SFA_ACCOUNT_TREE_RELATION_PROCESSING)).status(1).build();
        deleteUniqueTable(user.getTenantId(), arg.getCompanyName(),arg);
        return  result;
    }

    @Override
    public CheckCreateAccountTreeRelationResult deleteTreeRelationData(User user, CreateAccountTreeRelationArg arg) {
        CheckCreateAccountTreeRelationResult result = CheckCreateAccountTreeRelationResult.builder()
                .errorCode("200").message(I18N.text(SFA_ACCOUNT_TREE_RELATION_PROCESSING)).status(1).build();
        deleteAccountRelationData(user, arg);
        return  result;
    }

    @NotNull
    private EquityRelationshipDataModel.RelationshipModel getRelationshipModel(User user, CreateAccountTreeRelationArg arg) {
        EquityRelationshipDataModel.RelationshipModel equityRelationInfo = equityRelationDataService.getEquityRelationInfo(user, arg.getCompanyName());
        if (equityRelationInfo == null
                || !equityRelationInfo.getName().equals(arg.getCompanyName())) {
            deleteUniqueTable(user.getTenantId(), arg.getCompanyName(),arg);
            throw new ValidateException(I18N.text(SFA_ACCOUNT_TREE_RELATION_EQUITY_DATA_NOT_EXIST));
        }
        return equityRelationInfo;
    }
    @NotNull
    private void queryCompanyDetailIsExistByName(User user, CreateAccountTreeRelationArg arg) {
        EquityRelationshipDataModel.CompanyDetail companyDetail = equityRelationDataService.queryCompanyDetailIsExistByName(user, arg.getCompanyName());
        if (companyDetail == null
                || ObjectUtils.isEmpty(companyDetail.getId())
                || !companyDetail.getName().equals(arg.getCompanyName())) {
            deleteUniqueTable(user.getTenantId(), arg.getCompanyName(),arg);
            throw new ValidateException(I18N.text(SFA_ACCOUNT_TREE_RELATION_EQUITY_DATA_NOT_EXIST));
        }
    }

    public void checkTreeRelationExists(String tenantId, String companyName, CreateAccountTreeRelationArg arg) {
        String table = CommonTreeRelationUtil.getRelationshipsMapping(arg.getDescribeApiName()).getStoreTableName();
        String queryString = String.format("SELECT id FROM %s WHERE tenant_id=%s " +
                        "AND name=%s AND is_root='t' AND is_deleted>=0 LIMIT 2",
                SqlEscaper.pg_escape(table),SqlEscaper.pg_quote(tenantId),SqlEscaper.pg_quote(companyName));
        try {
            List<Map> queryResult = objectDataService.findBySql(tenantId, queryString);
            if(CollectionUtils.isNotEmpty(queryResult)) {
                throw new ValidateException(I18N.text(SFA_ACCOUNT_TREE_RELATION_EXISTS));
            }
        } catch (ValidateException e) {
            throw e;
        } catch (Exception err) {
            log.warn("checkAccountTreeRelationExists error", err);
            ExceptionUtil.throwCommonBusinessException();
        }
    }

    public String getTreeRelationExists(String tenantId, String companyName, CreateAccountTreeRelationArg arg) {
        RelationshipsMapping relationshipsMapping = CommonTreeRelationUtil.getRelationshipsMapping(arg.getDescribeApiName());
        String table = relationshipsMapping.getStoreTableName();
        String field = relationshipsMapping.getLinkFieldApiName();
        String queryString = String.format("SELECT %s FROM %s WHERE tenant_id=%s " +
                        "AND name=%s AND is_root='t' AND is_deleted>=0 ", SqlEscaper.pg_escape(field),
                SqlEscaper.pg_escape(table),SqlEscaper.pg_quote(tenantId),SqlEscaper.pg_quote(companyName));
        try {
            List<Map> queryResult = objectDataService.findBySql(tenantId, queryString);
            if(CollectionUtils.isEmpty(queryResult)) {
                log.warn("getTreeRelationExists queryResult is null");
               return "";
            }
            for(Map map : queryResult){
                if(ObjectUtils.isNotEmpty(map) && map.containsKey(field) && ObjectUtils.isNotEmpty(map.get(field))){
                    return map.get(field).toString();
                }
            }
            log.warn("getTreeRelationExists queryResult is not null,but {} is not Exists",field);
        } catch (ValidateException e) {
            throw e;
        } catch (Exception err) {
            log.warn("checkAccountTreeRelationExists error", err);
            ExceptionUtil.throwCommonBusinessException();
        }
        return "";
    }

    public void insertUniqueTable(String tenantId, String companyName,CreateAccountTreeRelationArg arg) {
        Map<String, Object> dataItem = Maps.newHashMap();
        dataItem.put("tenant_id", tenantId);
        dataItem.put("name", companyName);
        dataItem.put("object_api_name", arg.getDescribeApiName());

        try {
            CommonSqlUtil.insertData(tenantId, UNIQUE_TABLE_NAME, Lists.newArrayList(Lists.newArrayList(dataItem)));
        } catch (Exception e) {
            log.warn("insertUniqueTable error!", e);
            throw new ValidateException(I18N.text(SFA_ACCOUNT_TREE_RELATION_PROCESSING));
        }
    }

    public List<Map> queryUniqueTable(String tenantId, String companyName,CreateAccountTreeRelationArg arg) {
        try {
            List<WhereParam> whereParamsList = Lists.newArrayList();
            CommonSqlUtil.addWhereParam(whereParamsList, LtoFieldApiConstants.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(tenantId));
            CommonSqlUtil.addWhereParam(whereParamsList, "name", CommonSqlOperator.EQ, Lists.newArrayList(companyName));
            CommonSqlUtil.addWhereParam(whereParamsList, "object_api_name", CommonSqlOperator.EQ, Lists.newArrayList(arg.getDescribeApiName()));
            return CommonSqlUtil.queryData(tenantId, UNIQUE_TABLE_NAME, whereParamsList);
        } catch (Exception e) {
            log.warn("queryUniqueTable error!", e);
            throw new ValidateException(I18N.text(SFA_ACCOUNT_TREE_RELATION_PROCESSING));
        }
    }

    public void deleteUniqueTable(String tenantId, String companyName,CreateAccountTreeRelationArg arg) {
        try {
            List<WhereParam> whereParamsList = Lists.newArrayList();
            CommonSqlUtil.addWhereParam(whereParamsList, "tenant_id", CommonSqlOperator.EQ, Lists.newArrayList(tenantId));
            CommonSqlUtil.addWhereParam(whereParamsList, "name", CommonSqlOperator.EQ, Lists.newArrayList(companyName));
            CommonSqlUtil.addWhereParam(whereParamsList, "object_api_name", CommonSqlOperator.EQ, Lists.newArrayList(arg.getDescribeApiName()));
            CommonSqlUtil.deleteData(tenantId, UNIQUE_TABLE_NAME, whereParamsList);
        } catch (Exception e) {
            log.warn("deleteUniqueTable error!", e);
        }
    }

    private void deleteAccountRelationData(User user, CreateAccountTreeRelationArg arg) {
        try {
            if(StringUtils.isBlank(arg.getCompanyName())) {
                return;
            }
            RelationshipsMapping relationshipsMapping = CommonTreeRelationUtil.getRelationshipsMapping(arg.getDescribeApiName());
            int maxExecutedCount = 10000;
            while (maxExecutedCount > 0) {
                SearchTemplateQuery searchQuery = new SearchTemplateQuery();
                List<IFilter> filters = Lists.newArrayList();
                SearchUtil.fillFilterEq(filters, ROOT_ID, arg.getCompanyName());
                searchQuery.setFilters(filters);
                searchQuery.setLimit(100);
                searchQuery.setNeedReturnCountNum(false);
                searchQuery.setNeedReturnQuote(false);
                QueryResult<IObjectData> queryResult = ObjectDataUtil.findDataBySearchQuery(user, relationshipsMapping.getLinkDescribeApiName(), searchQuery, Lists.newArrayList());
                if(queryResult == null || CollectionUtils.isEmpty(queryResult.getData())) {
                    return;
                }
                serviceFacade.bulkInvalidAndDeleteWithSuperPrivilege(queryResult.getData(), user);
                --maxExecutedCount;
            }
        } catch (Exception e) {
            log.warn("deleteAccountRelationData error!", e);
        }
    }


}
