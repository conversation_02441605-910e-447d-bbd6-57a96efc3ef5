package com.facishare.crm.sfa.lto.duplicated;

import com.facishare.crm.sfa.lto.duplicated.models.DuplicatedModels;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface DuplicatedDataProcessor {
    String getProcessorModel();
    boolean matchProcessor(DuplicatedModels.TriggerAction triggerAction);

    void process(DuplicatedDataProcessArg processArg);

    @Data
    @Builder
    class DuplicatedDataProcessArg {
        private User user;
        private DuplicatedModels.TriggerAction action;
        private int refreshVersion;
        private List<IObjectData> dataList;
    }
}