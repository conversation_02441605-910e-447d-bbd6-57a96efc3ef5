package com.facishare.crm.sfa.prm.core.service;

import com.facishare.crm.sfa.prm.api.enums.NotificationType;
import com.facishare.crm.sfa.prm.api.notification.MessageService;
import com.facishare.crm.sfa.prm.model.MessageContent;
import com.facishare.crm.sfa.prm.model.QiXinGroupMessageContent;
import com.facishare.paas.appframework.core.model.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @time 2024-07-25 20:30
 * @Description
 */
@Slf4j
@Service
public class QiXinMessageService implements MessageService {
    @Override
    public NotificationType getMessageType() {
        return NotificationType.QI_XIN;
    }

    @Override
    public boolean sendMessage(User user, MessageContent content) {
        if (!(content instanceof QiXinGroupMessageContent)) {
            throw new IllegalArgumentException("Invalid content type for SmsMessageService");
        }
        QiXinGroupMessageContent qiXinGroupMessageContent = (QiXinGroupMessageContent) content;
        try {
            sendToSessionCurl(qiXinGroupMessageContent.getSessionId(), qiXinGroupMessageContent.getSender(), qiXinGroupMessageContent.getContent());
            return true;
        } catch (Exception e) {
            log.warn("发送企业组消息失败，dataTenantId：{}", user.getTenantId(), e);
        }
        return false;
    }

    public void sendToSessionCurl(String sessionId, String senderId, String content) {
        if (StringUtils.isAnyBlank(sessionId, senderId, content)) {
            log.warn("发送企业组消息失败， 参数为空。, sessionId:{}, senderId:{}, content:{}", sessionId, senderId, content);
            return;
        }
        String url = "http://172.17.4.230:45656/fs-flow-session/session/v2/sendToSession";
        String requestJson = "{\"content\":\"%s\",\"sessionId\":\"%s\",\"senderId\":\"%s\"}";
        String h = "-H";
        String x = "-X";
        String[] cmd = {"curl", x, "POST", url, h, "accept-language: zh", h, "content-type: application/json", h, "x-tenant-id: 1", h, "x-user-id: -10000", "-d",
                String.format(requestJson, content, sessionId, senderId)};
        execCurl(cmd);
    }

    private String execCurl(String[] cmd) {
        ProcessBuilder process = new ProcessBuilder(cmd);
        Process p;
        try {
            p = process.start();
            BufferedReader reader = new BufferedReader(new InputStreamReader(p.getInputStream(), StandardCharsets.UTF_8));
            StringBuilder builder = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                builder.append(line);
                builder.append(System.lineSeparator());
            }
            return builder.toString();
        } catch (Exception e) {
            log.error("execCurl error", e);
        }
        return null;
    }
}
