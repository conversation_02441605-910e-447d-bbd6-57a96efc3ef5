package com.facishare.crm.sfa.lto.loyalty.utils;

import lombok.Getter;

public class LoyaltyI18nKey {
    /**
     * 会员管理发放员工消费积分
     */
    public static String MEMBER_ADD_POINTS_TITLE = "sfa.loyalty.member_add_points_title";
    /**
     * 会员管理减少员工消费积分
     */
    public static String MEMBER_REDUCE_POINTS_TITLE = "sfa.loyalty.member_reduce_points_title";
    /**
     * 会员管理增加/减少员工定级积分
     */
    public static String MEMBER_TIER_POINTS_TITLE = "sfa.loyalty.member_tier_points_title";
    /**
     * 会员管理增加员工定级积分
     */
    public static String MEMBER_ADD_TIER_POINTS_TITLE = "sfa.loyalty.member_add_tier_points_title";
    /**
     * 会员管理减少员工定级积分
     */
    public static String MEMBER_REDUCE_TIER_POINTS_TITLE = "sfa.loyalty.member_reduce_tier_points_title";
    /**
     * 会员管理设置员工等级
     */
    public static String MEMBER_SET_TIER_TITLE = "sfa.loyalty.member_set_tier_title";
    /**
     * 执行成功,点击查看会员详情
     */
    public static String MEMBER_ACTION_SUCCESS_MESSAGE = "sfa.loyalty.member_action_message";
    /**
     * 执行失败 : {0}
     */
    public static String MEMBER_ACTION_VALIDATE_MESSAGE = "sfa.loyalty.member_validate_message";
    /**
     * 执行失败 : 系统错误
     */
    public static String MEMBER_ACTION_FAIL_MESSAGE = "sfa.loyalty.member_action_fail_message";

    /**
     * 已有变更在处理中,请稍后重试
     */
    public static String PROCESSING = "sfa.loyalty.processing";

    /**
     * {0}积分不足，扣减失败
     */
    public static String INSUFFICIENT_POINTS = "sfa.loyalty.insufficient_points";

    /**
     * 积分不足或者积分明细使用条数达到{0}条上限
     */
    public static String INSUFFICIENT_POINTS_OR_EXCEEDS_LIMIT = "sfa.loyalty.insufficient_points_or_exceeds_limit";

    /**
     * 暂不支持等级回退
     */
    public static String NO_SUPPORT_FOR_ROLLBACK = "sfa.loyalty.no_support_for_rollback";

    /**
     * 该积分已失效,无法回退
     */
    public static String POINTS_EXPIRED = "sfa.loyalty.points_expired";

    /**
     * 未找到有效的{0}
     */
    public static String NOT_FOUND = "sfa.loyalty.not_found";

    /**
     * {0}不能为空
     */
    public static String MISSING_PARAMETERS = "sfa.loyalty.missing_parameters";

    /**
     * {0}未启用
     */
    public static String NOT_ENABLED = "sfa.loyalty.not_enabled";

    /**
     * {0}积分预警
     */
    public static String POINTS_WAINING_TITLE = "sfa.loyalty.points_waining_title";

    /**
     * {0}剩余积分[{1}]低于预警规则[{2}]
     */
    public static String POINTS_WAINING_INFO = "sfa.loyalty.points_waining_info";

    /**
     * 升级发放优惠卷失败
     */
    public static String UPGRADE_COUPONS_ERROR_TITLE = "sfa.loyalty.upgrade_coupons_error_title";

    /**
     * 会员升级为[{0}]发放优惠卷[{1}]失败
     */
    public static String UPGRADE_COUPONS_ERROR_INFO = "sfa.loyalty.upgrade_coupons_error_info";

    /**
     * 当前状态[{0}]不允许变更
     */
    public static String PROHIBIT_OPERATION_FOR_MEMBER_STATUS = "sfa.loyalty.prohibit_operation_for_member_status";

    @Getter
    public enum ErrorStatus {
        TryLockFailed(321010406),
        ProgramNotEnable(321010401),
        PointPoolNotFound(321010404),
        MemberPointsInsufficient(321010405);

        private final int errorCode;

        ErrorStatus(int errorCode) {
            this.errorCode = errorCode;
        }

    }

}
