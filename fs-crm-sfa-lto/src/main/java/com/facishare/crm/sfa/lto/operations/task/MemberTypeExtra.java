package com.facishare.crm.sfa.lto.operations.task;

import java.util.Arrays;
import java.util.Optional;

enum MemberTypeExtra {
    OWNER, // 数据-负责人
    DATA_GROUP, // 数据-相关团队成员
    DATA_OWNER_LEADER, // 数据-负责人汇报对象
    ;

    public static Optional<MemberTypeExtra> from(Object value) {
        return Arrays.stream(values()).filter(v -> v.toString().equals(value)).findFirst();
    }

    @Override
    public String toString() {
        return name().toLowerCase();
    }
}