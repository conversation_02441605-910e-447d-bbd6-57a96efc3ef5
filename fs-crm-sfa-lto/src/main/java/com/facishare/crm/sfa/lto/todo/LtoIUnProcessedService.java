package com.facishare.crm.sfa.lto.todo;

import com.facishare.crm.sfa.lto.todo.enums.LtoSessionBOCItemKeys;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;

import java.util.List;

/**
 * 获取待办数据
 *
 * <AUTHOR>
 * @date 2019/10/23
 */
public interface LtoIUnProcessedService {
    /**
     * 获取支持的对象
     *
     * @return
     */
    String getObjectApiName();

    /**
     * 获取所有待办的数据
     *
     * @param user
     * @return
     */
    List<IObjectData> getTotalUnProcessedData(User user, LtoSessionBOCItemKeys sessionBOCItemKey);

    default QueryResult<IObjectData> getOrderTotalUnProcessedData(User user, LtoSessionBOCItemKeys sessionBOCItemKey){
        return new QueryResult<>();
    }
}
