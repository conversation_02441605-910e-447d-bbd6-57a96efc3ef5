package com.facishare.crm.sfa.lto.qywx.proxy.models;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

public interface QueryContactModel {

	@Data
	@Builder
	class QueryArg {
		private String fsEa;
		private String externalUserId;
	}


	@Data
	class QueryResult extends BaseQywxResult {
		private ContactInfoResult data;

	}

	@Data
	class ContactInfoResult {
		private ExternalContact external_contact;
		private List<FollowUser> follow_user;
	}

	@Data
	class ExternalContact {
		private String external_userid;
		private String name;
		private String position;
		private String avatar;//":"http://p.qlogo.cn/bizmail/IcsdgagqefergqerhewSdage/0",
		private String corp_name;//":"腾讯",
		private String corp_full_name;//":"腾讯科技有限公司",
		private int type;
		private int gender;
		private String unionid;
		private List<ExternalProfile> external_profile;
	}

	@Data
	class ExternalProfile {
		private String type;
		private String name;
		private Map<String, Object> content;
	}

	@Data
	class FollowUser {
		private String fxUserId;
		private String userid;//":"rocky",F
		private String remark;//":"李部长",
		private String add_way;
		private String description;//":"对接采购事物",
		private Long createtime;//":1525779812
		private String state;
		private String remark_corp_name; // 腾讯科技
		private List<String> remark_mobiles;
	}
}
