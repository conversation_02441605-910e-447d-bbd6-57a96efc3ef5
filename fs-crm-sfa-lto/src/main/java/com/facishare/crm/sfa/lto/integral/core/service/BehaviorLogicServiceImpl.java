package com.facishare.crm.sfa.lto.integral.core.service;

import com.facishare.crm.sfa.lto.integral.common.constant.IntegralErrorCode;
import com.facishare.crm.sfa.lto.integral.common.constant.IntegralObject;
import com.facishare.crm.sfa.lto.integral.common.constant.RuleDataConstant;
import com.facishare.crm.sfa.lto.integral.core.service.dto.BehaviorInfo;
import com.facishare.crm.sfa.lto.integral.core.service.dto.FindBehaviorList;
import com.facishare.crm.sfa.lto.integral.core.service.dto.RegisterAction;
import com.facishare.crm.sfa.lto.integral.core.service.dto.RegisterCategory;
import com.facishare.crm.sfa.lto.integral.core.service.dto.RegisterMaterial;
import com.facishare.crm.sfa.lto.integral.core.service.dto.RemoveAction;
import com.facishare.crm.sfa.lto.integral.core.service.dto.RemoveCategory;
import com.facishare.crm.sfa.lto.integral.core.service.dto.RemoveMaterial;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOption;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.common.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BehaviorLogicServiceImpl extends AbstractLogicService implements BehaviorLogicService {

    @Autowired
    DataLogicService dataLogicService;

    @Autowired
    DescribeLogicService describeLogicService;

    @Autowired
    BehaviorActionService behaviorActionService;

    @Autowired
    BehaviorMaterialService behaviorMaterialService;

    @Autowired
    BehaviorCategoryService behaviorCategoryService;

    @Override
    public FindBehaviorList.Result findBehaviorList(String tenantId) {
        List<BehaviorInfo.BehaviorCategory> behaviorCategories =
                Lists.newArrayList(SystemBehaviorArchives.getSystemBehaviorCategoryList());
        //填充租户自定义行为分类
        fillTenantBehaviorCategory(tenantId, behaviorCategories);
        return FindBehaviorList.Result.builder().behaviors(behaviorCategories).build();
    }

    private void fillTenantBehaviorCategory(String tenantId, List<BehaviorInfo.BehaviorCategory> behaviorCategories) {
        List<IObjectData> tenantBehaviorCategories = behaviorCategoryService.getBehaviorCategoryList(tenantId);
        List<IObjectData> tenantBehaviorActions = behaviorActionService.getBehaviorActionList(tenantId);
        tenantBehaviorCategories.forEach(c -> {
            BehaviorInfo.BehaviorCategory category = new BehaviorInfo.BehaviorCategory();
            category.setCategoryApiName(c.get(IntegralObject.FIELD_CATEGORY_API_NAME, String.class));
            category.setCategoryLabel(c.get(IntegralObject.FIELD_CATEGORY_LABEL, String.class));
            List<BehaviorInfo.BehaviorAction> actions = tenantBehaviorActions.stream()
                    .map(x -> {
                        BehaviorInfo.BehaviorAction action = new BehaviorInfo.BehaviorAction();
                        action.setActionApiName(x.get(IntegralObject.FIELD_ACTION_API_NAME, String.class));
                        action.setActionLabel(x.get(IntegralObject.FIELD_ACTION_LABEL, String.class));
                        return action;
                    })
                    .collect(Collectors.toList());
            category.setActions(actions);
            behaviorCategories.add(category);
        });
    }

    @Override
    public RegisterCategory.Result registerCategory(RegisterCategory.Arg arg, RequestContext context) {
        if (Strings.isNullOrEmpty(arg.getCategoryApiName()) || Strings.isNullOrEmpty(arg.getCategoryLabel())) {
            throw new ValidateException(IntegralErrorCode.PARAMETER_IS_NULL.getMessage());
        }

        IObjectData categoryData = behaviorCategoryService.getBehaviorCategoryByApiName(context.getTenantId(), arg.getCategoryApiName());
        if (categoryData != null) {
            if (categoryData.get(IntegralObject.FIELD_IS_ACTIVE, Boolean.class)) {
                throw new ValidateException(IntegralErrorCode.BEHAVIOR_CATEGORY_API_NAME_EXIST.getMessage());
            }
            //重新启用分类
            updateActiveStatus(categoryData, context.getUser().getUserId(), true);
        } else {
            if (SystemBehaviorArchives.existSystemCategoryApiName(arg.getCategoryApiName())) {
                throw new ValidateException(IntegralErrorCode.BEHAVIOR_CATEGORY_API_NAME_EXIST.getMessage());
            }
            if (SystemBehaviorArchives.existSystemCategoryLabel(arg.getCategoryLabel())) {
                throw new ValidateException(IntegralErrorCode.BEHAVIOR_CATEGORY_LABEL_EXIST.getMessage());
            }
            IObjectData objectData = new ObjectData();
            objectData.setDescribeApiName(IntegralObject.BEHAVIOR_CATEGORY_API_NAME);
            objectData.set(IntegralObject.FIELD_CATEGORY_API_NAME, arg.getCategoryApiName());
            objectData.set(IntegralObject.FIELD_CATEGORY_LABEL, arg.getCategoryLabel());
            objectData.set(IntegralObject.FIELD_IS_ACTIVE, true);
            setDefaultSystemInfo(objectData, context);
            dataLogicService.saveData(objectData);
        }
        return RegisterCategory.Result.builder().success(true).build();
    }

    @Override
    public RemoveCategory.Result removeCategory(RemoveCategory.Arg arg, RequestContext context) {
        if (Strings.isNullOrEmpty(arg.getCategoryApiName())) {
            throw new ValidateException(IntegralErrorCode.PARAMETER_IS_NULL.getMessage());
        }
        IObjectData objectData = behaviorCategoryService.getBehaviorCategoryByApiName(context.getTenantId(), arg.getCategoryApiName());
        if (objectData == null) {
            throw new ValidateException(IntegralErrorCode.BEHAVIOR_CATEGORY_NOT_FOUND.getMessage());
        }
        updateActiveStatus(objectData, context.getUser().getUserId(), false);
        return RemoveCategory.Result.builder().success(true).build();
    }

    @Override
    public RegisterAction.Result registerAction(RegisterAction.Arg arg, RequestContext context) {
        if (Strings.isNullOrEmpty(arg.getCategoryApiName()) || Strings.isNullOrEmpty(arg.getActionApiName())
                || Strings.isNullOrEmpty(arg.getActionLabel())) {
            throw new ValidateException(IntegralErrorCode.PARAMETER_IS_NULL.getMessage());
        }

        IObjectData disabledActionData = checkRepeatAction(arg, context);
        if (disabledActionData != null) {
            //重新启用行为动作
            updateActiveStatus(disabledActionData, context.getUser().getUserId(), true);
            return RegisterAction.Result.builder().success(true).build();
        }

        IObjectData objectData = new ObjectData();
        objectData.setDescribeApiName(IntegralObject.BEHAVIOR_ACTION_API_NAME);
        objectData.set(IntegralObject.FIELD_CATEGORY_API_NAME, arg.getCategoryApiName());
        objectData.set(IntegralObject.FIELD_ACTION_API_NAME, arg.getActionApiName());
        objectData.set(IntegralObject.FIELD_ACTION_LABEL, arg.getActionLabel());
        objectData.set(IntegralObject.FIELD_IS_ACTIVE, true);
        setDefaultSystemInfo(objectData, context);
        dataLogicService.saveData(objectData);

        updateDescribeOption(context.getTenantId(), arg.getCategoryApiName(), arg.getActionApiName(), arg.getActionLabel());

        return RegisterAction.Result.builder().success(true).build();
    }

    @Override
    public RemoveAction.Result removeAction(RemoveAction.Arg arg, RequestContext context) {
        if (Strings.isNullOrEmpty(arg.getCategoryApiName()) || Strings.isNullOrEmpty(arg.getActionApiName())) {
            throw new ValidateException(IntegralErrorCode.PARAMETER_IS_NULL.getMessage());
        }
        IObjectData objectData = behaviorActionService.getBehaviorActionByApiName(context.getTenantId(), arg.getCategoryApiName(), arg.getActionApiName());
        if (objectData == null) {
            throw new ValidateException(IntegralErrorCode.BEHAVIOR_ACTION_NOT_FOUND.getMessage());
        }
        updateActiveStatus(objectData, context.getUser().getUserId(), false);
        return RemoveAction.Result.builder().success(true).build();
    }

    @Override
    public RegisterMaterial.Result registerMaterial(RegisterMaterial.Arg arg, RequestContext context) {
        if (Strings.isNullOrEmpty(arg.getCategoryApiName()) || Strings.isNullOrEmpty(arg.getMaterialApiName())
                || Strings.isNullOrEmpty(arg.getMaterialLabel())) {
            throw new ValidateException(IntegralErrorCode.PARAMETER_IS_NULL.getMessage());
        }
        IObjectData materialData = checkRepeatMaterial(arg, context);
        if (materialData != null) {
            log.info("update Material CategoryApiName is {} MaterialApiName is {}", arg.getCategoryApiName(), arg.getMaterialApiName());
            behaviorMaterialService.updateBehaviorMaterial(materialData,context.getUser().getUserId(),arg.getMaterialLabel());
            return RegisterMaterial.Result.builder().success(true).build();
        }
        IObjectData objectData = new ObjectData();
        objectData.setDescribeApiName(IntegralObject.BEHAVIOR_MATERIAL_API_NAME);
        objectData.set(IntegralObject.FIELD_CATEGORY_API_NAME, arg.getCategoryApiName());
        objectData.set(IntegralObject.FIELD_MATERIAL_API_NAME, arg.getMaterialApiName());
        objectData.set(IntegralObject.FIELD_MATERIAL_LABEL, arg.getMaterialLabel());
        objectData.set(IntegralObject.FIELD_IS_ACTIVE, true);
        setDefaultSystemInfo(objectData, context);
        log.info("save Material CategoryApiName is {} MaterialApiName is {}", arg.getCategoryApiName(), arg.getMaterialApiName());
        dataLogicService.saveData(objectData);
        return RegisterMaterial.Result.builder().success(true).build();
    }

    @Override
    public RemoveMaterial.Result removeMaterial(RemoveMaterial.Arg arg, RequestContext context) {
        if (Strings.isNullOrEmpty(arg.getCategoryApiName()) || Strings.isNullOrEmpty(arg.getMaterialApiName())) {
            throw new ValidateException(IntegralErrorCode.PARAMETER_IS_NULL.getMessage());
        }
        IObjectData objectData = behaviorMaterialService.getBehaviorMaterialByApiName(context.getTenantId(), arg.getCategoryApiName(), arg.getMaterialApiName());
        if (objectData == null) {
            throw new ValidateException(IntegralErrorCode.BEHAVIOR_MATERIAL_NOT_FOUND.getMessage());
        }
        updateActiveStatus(objectData, context.getUser().getUserId(), false);
        return RemoveMaterial.Result.builder().success(true).build();
    }

    private void setDefaultSystemInfo(IObjectData objectData, RequestContext context) {
        objectData.setTenantId(context.getTenantId());
        objectData.setCreatedBy(context.getUser().getUserId());
        objectData.setLastModifiedBy(context.getUser().getUserId());
        objectData.setLastModifiedTime(System.currentTimeMillis());
        objectData.setCreateTime(System.currentTimeMillis());
    }

    private void updateActiveStatus(IObjectData objectData, String userId, Boolean isActive) {
        objectData.set(IntegralObject.FIELD_IS_ACTIVE, isActive);
        objectData.setLastModifiedBy(userId);
        objectData.setLastModifiedTime(System.currentTimeMillis());
        List<String> validFieldApiNames = Lists.newArrayList(IntegralObject.FIELD_IS_ACTIVE,
                IObjectData.LAST_MODIFIED_BY, IObjectData.LAST_MODIFIED_TIME);
        dataLogicService.batchUpdateIgnoreOther(Lists.newArrayList(objectData), validFieldApiNames);
    }

    private BehaviorInfo.BehaviorMaterial getAnyOneMaterial() {
        BehaviorInfo.BehaviorMaterial material = new BehaviorInfo.BehaviorMaterial();
        material.setMaterialApiName(RuleDataConstant.MATERIAL_API_NAME_ANY_ONE);
        material.setMaterialLabel(RuleDataConstant.getAnyOneLabel());
        return material;
    }

    private BehaviorInfo.BehaviorMaterial buildBehaviorMaterial(IObjectData objectData) {
        BehaviorInfo.BehaviorMaterial material = new BehaviorInfo.BehaviorMaterial();
        material.setMaterialApiName(objectData.get(IntegralObject.FIELD_MATERIAL_API_NAME, String.class));
        material.setMaterialLabel(objectData.get(IntegralObject.FIELD_MATERIAL_LABEL, String.class));
        return material;
    }

    /**
     * 校验同一行为分类下action api name和 label是否重复
     *
     * @param arg
     * @param context
     * @return 返回api name相同且状态为停用的行为动作
     */
    private IObjectData checkRepeatAction(RegisterAction.Arg arg, RequestContext context) {
        IObjectData rst = null;
        //校验是否和系统预置行为动作重复
        if (SystemBehaviorArchives.existSystemActionApiName(arg.getCategoryApiName(), arg.getActionApiName())) {
            throw new ValidateException(IntegralErrorCode.BEHAVIOR_ACTION_API_NAME_EXIST.getMessage());
        }

        if (SystemBehaviorArchives.existSystemActionLabel(arg.getCategoryApiName(), arg.getActionLabel())) {
            throw new ValidateException(IntegralErrorCode.BEHAVIOR_ACTION_API_NAME_EXIST.getMessage());
        }

        //校验是否和租户自定义行为动作重复
        IObjectData objectData = behaviorActionService.getBehaviorActionByApiName(context.getTenantId()
                , arg.getCategoryApiName(), arg.getActionApiName());
        if (objectData != null) {
            if (objectData.get(IntegralObject.FIELD_IS_ACTIVE, Boolean.class)) {
                throw new ValidateException(IntegralErrorCode.BEHAVIOR_ACTION_API_NAME_EXIST.getMessage());
            } else {
                rst = objectData;
            }
        }
        objectData = behaviorActionService.getBehaviorActionByLabel(context.getTenantId()
                , arg.getCategoryApiName(), arg.getActionLabel());
        if (objectData != null) {
            if (objectData.get(IntegralObject.FIELD_IS_ACTIVE, Boolean.class)) {
                throw new ValidateException(IntegralErrorCode.BEHAVIOR_ACTION_LABEL_EXIST.getMessage());
            }
        }
        return rst;
    }

    /**
     * 校验同一行为分类下material label是否重复
     *
     * @param arg
     * @param context
     * @return 返回api name相同的行为动作
     */
    private IObjectData checkRepeatMaterial(RegisterMaterial.Arg arg, RequestContext context) {
        IObjectData rst = null;
        IObjectData objectData = behaviorMaterialService.getBehaviorMaterialByApiName(context.getTenantId()
                , arg.getCategoryApiName(), arg.getMaterialApiName());
        if (objectData != null) {
            rst = objectData;
        }

        objectData = behaviorMaterialService.getBehaviorMaterialByLabel(context.getTenantId()
                , arg.getCategoryApiName(), arg.getMaterialLabel());
        if (objectData != null) {
            if (objectData.get(IntegralObject.FIELD_IS_ACTIVE, Boolean.class)) {
                log.warn("have same lable CategoryApiName is {} MaterialApiName is {}", arg.getCategoryApiName(), arg.getMaterialApiName());
                throw new ValidateException(IntegralErrorCode.BEHAVIOR_MATERIAL_LABEL_EXIST.getMessage());
            }
        }
        return rst;
    }

    /**
     * 更新行为积分明细对象行为分类和行为动作option
     *
     * @param tenantId
     * @param categoryApiName
     * @param actionApiName
     * @param actionLabel
     */
    private void updateDescribeOption(String tenantId, String categoryApiName, String actionApiName, String actionLabel) {
        IObjectDescribe objectDescribe = describeLogicService.findObjectIncludeMultiField(tenantId
                , IntegralObject.BEHAVIOR_INTEGRAL_DETAIL_API_NAME);

        IObjectData categoryData = behaviorCategoryService.getBehaviorCategoryByApiName(tenantId, categoryApiName);
        if (categoryData == null) {
            return;
        }

        List<IFieldDescribe> updatedFieldDescribe = Lists.newArrayList();
        //更新行为动作option
        SelectOneFieldDescribe actionFieldDescribe = (SelectOneFieldDescribe) objectDescribe
                .getFieldDescribe(IntegralObject.FIELD_ACTION_API_NAME);
        List<ISelectOption> selectOptions = actionFieldDescribe.getSelectOptions();
        if (!selectOptions.stream().anyMatch(x -> actionApiName.equals(x.getValue()))) {
            ISelectOption option = new SelectOption();
            option.setValue(actionApiName);
            option.setLabel(actionLabel);
            selectOptions.add(option);
            actionFieldDescribe.setSelectOptions(selectOptions);
            updatedFieldDescribe.add(actionFieldDescribe);
        }

        //更新行为分类字段childOption
        SelectOneFieldDescribe categoryFieldDescribe = (SelectOneFieldDescribe) objectDescribe
                .getFieldDescribe(IntegralObject.FIELD_CATEGORY_API_NAME);
        selectOptions = categoryFieldDescribe.getSelectOptions();
        Optional<ISelectOption> categoryOption = selectOptions.stream()
                .filter(option -> categoryApiName.equals(option.getValue())).findAny();
        if (categoryOption.isPresent()) {
            List<Map<String, List<String>>> childOptions = categoryOption.get().getChildOptions();
            childOptions.stream().filter(x -> x.containsKey(IntegralObject.FIELD_ACTION_API_NAME))
                    .findAny().ifPresent(x -> {
                List<String> childList = x.get(IntegralObject.FIELD_ACTION_API_NAME);
                if (!childList.contains(actionApiName)) {
                    childList.add(actionApiName);
                    categoryOption.get().setChildOptions(childOptions);
                }
            });
        } else {
            ISelectOption option = new SelectOption();
            option.setValue(categoryApiName);
            option.setLabel(categoryData.get(IntegralObject.FIELD_CATEGORY_LABEL, String.class));
            categoryFieldDescribe.addSelectOption(option);
            List<Map<String, List<String>>> childOptions = Lists.newArrayList();
            childOptions.add(ImmutableMap
                    .of(IntegralObject.FIELD_ACTION_API_NAME, Lists.newArrayList(actionApiName)));
            option.setChildOptions(childOptions);
        }
        updatedFieldDescribe.add(categoryFieldDescribe);

        describeLogicService.updateFieldDescribe(objectDescribe, updatedFieldDescribe);
    }
}
