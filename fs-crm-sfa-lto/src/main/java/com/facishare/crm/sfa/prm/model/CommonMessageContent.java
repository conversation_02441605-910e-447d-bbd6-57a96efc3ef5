package com.facishare.crm.sfa.prm.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @time 2024-07-01 16:37
 * @Description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CommonMessageContent implements MessageContent {
    protected String sender = null;
    protected Set<String> recipients = new HashSet<>();
    protected String content = null;
}
