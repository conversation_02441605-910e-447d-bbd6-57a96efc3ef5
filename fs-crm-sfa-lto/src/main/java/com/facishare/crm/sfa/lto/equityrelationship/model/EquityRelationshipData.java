package com.facishare.crm.sfa.lto.equityrelationship.model;


import lombok.Data;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Property;

import java.util.List;

/**
 * <AUTHOR> lik
 * @date : 2023/6/29 15:53
 */

public interface EquityRelationshipData {

    @Data
    @Entity(value = "biz_equity_relationship_", noClassnameStored = true)
    class EquityRelationship {
        @Id
        private String id;
        @Property("name")
        private String name;
        @Property("enterprise_name")
        private String enterprise_name;
        @Property("enterprise_id")
        private String enterprise_id;
//        @Property("root_node_id")
//        private String root_node_id;
//        @Property("root_node_name")
//        private String root_node_name;
//        @Property("root_node_stock_proportion")
//        private double root_node_stock_proportion;

        @Property("root_node_path")
        private List<String> root_node_path;
        @Property("relationship_type")
        private String relationship_type;
        @Property("enterprise_type")
        private String enterprise_type;
        @Property("parent_enterprise_id")
        private String parent_enterprise_id;
        @Property("parent_enterprise_name")
        private String parent_enterprise_name;
        @Property("parent_stock_proportion")
        private Double parent_stock_proportion;
        @Property("parent_investment")
        private Double parent_investment;
        @Property("relationship_path")
        private List<String> relationship_path;
        @Property("is_normal")
        private boolean is_normal;
        @Property("relationship_level")
        private Integer relationship_level;
        @Property("uniqueness_data_relationship")
        private String uniqueness_data_relationship;
        @Property("account_main_data_id")
        private String account_main_data_id;

        @Property("create_time")
        private long create_time;
        @Property("update_time")
        private long update_time;
        @Property("update_time")
        private long last_update_time;

        /**下面两个参数，在递归查找父节点时，用于记录父节点对子节点的股权占比和投资金额*/
        @Property("percent")
        private Double percent;
        @Property("percent")
        private Double investment;
        @Property("find_by_data_id")
        private String find_by_data_id;
    }

    @Data
    class Result<T>{
        private boolean success = true;
        private String msg;
        private T data;
    }
    @Data
    class Arg {
        private String name;
        private Integer pageNo;
        private Integer pageSize;
    }
}
