package com.facishare.crm.sfa.lto.integral.core.service;

import com.facishare.crm.sfa.lto.integral.core.service.dto.*;
import com.facishare.paas.appframework.core.model.RequestContext;

/**
 * 积分规则服务类
 * <AUTHOR>
 */
public interface IntegralRuleService {
    /**
     * 创建积分规则
     * @param arg 积分规则json
     * @param context 请求上下文信息
     * @return 创建结果
     */
    CreateRuleOrUpdate.Result create(CreateRuleOrUpdate.Arg arg, RequestContext context);

    /**
     * 更新积分规则
     * @param arg 积分规则json
     * @param context 请求上下文信息
     * @return 更新结果
     */
    CreateRuleOrUpdate.Result update(CreateRuleOrUpdate.Arg arg, RequestContext context);

    /**
     * 启用积分规则
     * @param arg 积分规则ApiName
     * @param context 请求上下文
     * @return 启用结果
     */
    EnableRule.Result enable(EnableRule.Arg arg, RequestContext context);

    /**
     * 禁用积分规则
     * @param arg 积分规则ApiName
     * @param context 请求上下文
     * @return 停用结果
     */
    DisableRule.Result disable(DisableRule.Arg arg, RequestContext context);

    /**
     * 删除积分规则
     * @param arg 积分规则的api name
     * @param context 请求上下文
     * @return 删除结果
     */
    DeleteRule.Result delete(DeleteRule.Arg arg, RequestContext context);

    /**
     * 分页查询积分规则列表
     * @param arg 分页信息
     * @param context 请求上下文
     * @return 积分规则列表
     */
    FindRuleList.Result findRuleList(FindRuleList.Arg arg, RequestContext context);

    /**
     * 根据apiName 查询积分规则
     * @param arg 积分规则的API name
     * @param context 请求上下文
     * @return 评分规则详情
     */
    FindDetailByApiName.Result findRule(FindDetailByApiName.Arg arg, RequestContext context);


    /**
     * 获取行为列表
     * @param arg
     * @param context
     * @return
     */
    FindBehaviorList.Result findBehaviorList(FindBehaviorList.Arg arg, RequestContext context);

    /**
     * 获取行为积分支持的对象
     * @param context
     * @return
     */
    FindSupportObjects.Result findSupportObjectList(RequestContext context);

    /**
     * 获取规则结果设置字段列表
     * @param arg
     * @param context
     * @return
     */
    FindRuleResultFieldList.Result findRuleResultFieldList(FindRuleResultFieldList.Arg arg, RequestContext context);

    /**
     * 校验规则的名称和ApiName是否重复
     * @param arg 规则信息
     * @param context 请求上下文
     * @return 校验结果
     */
    CheckRule.Result checkRuleApiNameOrLabel(CheckRule.Arg arg, RequestContext context);

    /**
     * 判断当前企业是否开启行为积分模块
     * @param context
     * @return
     */
    ExistModule.Result existModule(RequestContext context);

    /**
     * 查询物料按分类的apiName
     *
     * @param arg     参数
     * @param context 上下文
     * @return {@code FindMaterial.Result}
     */
    FindMaterial.Result findMaterialListByCategory(FindMaterial.Arg arg, RequestContext context);

}
