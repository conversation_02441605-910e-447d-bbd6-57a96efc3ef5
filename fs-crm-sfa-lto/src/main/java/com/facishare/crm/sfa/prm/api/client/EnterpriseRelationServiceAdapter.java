package com.facishare.crm.sfa.prm.api.client;

import com.facishare.crm.sfa.prm.api.dto.ResponseDTO;
import com.facishare.paas.appframework.core.model.User;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-02-27
 * ============================================================
 */
public interface EnterpriseRelationServiceAdapter {
    /**
     * 获取外部企业 id
     *
     * @param user          用户
     * @param objectApiName 对象 ApiName
     * @param objectDataId  对象数据 id
     * @return 外部企业 id
     */
    String fetchOutTenantId(User user, String objectApiName, String objectDataId);

    /**
     * 获取 下游应用映射的 CRM 对象数据id
     *
     * @param user          用户
     * @param objectApiName 对象 apiName
     * @param outTenantId   外部企业 id
     * @return 下游应用映射的 CRM 对象数据id
     */
    String fetchCrmMapperObjectDataId(User user, String objectApiName, String outTenantId);

    /**
     * 获取 下游应用映射的 CRM 对象数据id
     *
     * @param user          下游访问用户
     * @param objectApiName 对下 apiName
     * @return 下游应用映射的 CRM 对象数据id
     */
    String fetchCrmMapperObjectDataId(User user, String objectApiName);

    ResponseDTO fetchOutMainUser(User user, String objectApiName, String mapperDataId);

}
