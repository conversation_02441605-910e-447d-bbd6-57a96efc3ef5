package com.facishare.crm.sfa.lto.accountreerelation;

import com.facishare.crm.sfa.lto.accountreerelation.factory.MatchFactory;
import com.facishare.crm.sfa.lto.accountreerelation.models.AccountTreeRelationModels;
import com.facishare.crm.sfa.lto.accountreerelation.models.AccountTreeRelationModels.*;
import com.facishare.crm.sfa.lto.accountreerelation.models.EquityRelationModels.*;
import com.facishare.crm.sfa.lto.common.models.LtoFieldApiConstants;
import com.facishare.crm.sfa.lto.equityrelationship.model.EquityRelationshipDataModel;
import com.facishare.crm.sfa.lto.equityrelationship.model.EquityRelationshipDataModel.*;
import com.facishare.crm.sfa.lto.equityrelationship.service.EquityRelationshipService;
import com.facishare.crm.sfa.lto.exception.ExceptionUtil;
import com.facishare.crm.sfa.lto.utils.*;
import com.facishare.crm.sfa.lto.utils.constants.EquityRelationshipDataConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.CRMNotificationServiceImpl;
import com.facishare.paas.appframework.common.service.model.CRMNotification;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.*;
import com.fxiaoke.common.SqlEscaper;
import com.fxiaoke.crmrestapi.common.util.JsonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.lto.accountreerelation.models.AccountTreeRelationConstants.*;
import static com.facishare.crm.sfa.lto.utils.SfaLtoI18NKeyUtil.SFA_ACCOUNT_TREE_RELATION_PROCESS_SUCCESS;
import static com.facishare.crm.sfa.lto.utils.SfaLtoI18NKeyUtil.SFA_ACCOUNT_TREE_RELATION_PROCESS_SUCCESS_CONTENT;

@Component
@Slf4j
public class EquityRelationDataServiceImpl implements IEquityRelationDataService {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private ConfigService configService;
    @Autowired
    private EquityRelationshipService equityRelationshipService;
    @Autowired
    protected CRMNotificationServiceImpl crmNotificationService;
    @Autowired
    protected AccountMainDataMatchServiceImpl accountMainDataMatchService;
    @Autowired
    protected MatchFactory matchFactory;
    @Autowired
    protected AccountTreeRelationLogService accountTreeRelationLogService;
    @Autowired
    protected CommonTreeRelationLogService commonTreeRelationLogService;
    @Autowired
    protected SpecialTableMapper SPECIAL_TABLE_MAPPER;
    @Autowired
    private EnterpriseInfoMatchService enterpriseInfoMatchService;

    private static final List<String> INVALID_STATUS = Lists.newArrayList("注销","吊销","停业","清算");

    private static final int BACH_SIZE = 100;
    private static final int SLEEP_MILLIS = 0;
    private static final String ORI_NODE_TYPE = "ORI";

    @Override
    public RelationshipModel getEquityRelationInfo(User user, String companyName) {
        return equityRelationshipService.queryEquityInfoByName(user, companyName);
    }

    @Override
    public CompanyDetail queryCompanyDetailIsExistByName(User user, String companyName) {
        return equityRelationshipService.queryCompanyDetailByName(user, companyName);
    }

    /*
    处理股权关系数据生成客户树，过程：
    1、解析股权关系数据，并将解析好的数据插入biz_equity_relation_temp临时表，为后续处理做准备
    2、根据客户树生成规则取最小股权值
    3、针对叶子节点股权数据做标记（股权占比为1.0的直接标记为Y，股权占比小于规则中设置的股权值标记为N不参与建树，需要计算是否参与最终建树的节点标记为T）
    4、处理有问题的企业数据（企业名称相同，但工商中没有的节点）
    5、处理相同公司由集团内多个子公司持股场景（持股比例最大标记为Y， 多个子公司对同一个公司持股相同时标记层级最长的Y）
    6、处理节点标记为T的数据，计算最终的节点类型（N--不参与建树、 Y或EY--参与建树)
    7、处理所有叶子节点分支机构
    8、处理所有叶子节点分支机构
    9、根据标记为Y的股权数据生成客户树关系
    10、处理股权占比相等的情况并发送CRM通知
    11、发送客户树创建成功CRM通知
    12、清理临时数据
     */
    @Override
    public void processEquityRelationData(User user, RelationshipModel equityRelationInfo, CreateAccountTreeRelationArg arg) {
        String tenantId = user.getTenantId();
        String rootId = serviceFacade.generateId();
        try {
            //1、解析股权关系数据，并将解析好的数据插入biz_equity_relation_temp临时表，为后续处理做准备
            List<Trees> childNodes = equityRelationInfo.getC_trees();
            ProcessedEquityInfo rootEquityInfo = getProcessedEquityInfo(equityRelationInfo, rootId);
            ProcessedEquityRelation rootEquityEntity = ProcessedEquityRelation.builder().parentNodes(Lists.newArrayList())
                    .currentNode(rootEquityInfo).entityId(rootEquityInfo.getId()).build();
            Set<String> insertedDataIds = Sets.newHashSet();
            if(CollectionUtils.notEmpty(childNodes)){
                for (Trees node : childNodes) {
                    processChildNode(tenantId, rootEquityInfo, node, Lists.newArrayList(rootEquityEntity), insertedDataIds,arg);
                }
            }else{
                Map<String, Object> dataItem = getDataItem(tenantId, rootEquityInfo, Lists.newArrayList(rootEquityInfo.getId()), Lists.newArrayList(),  1, null, rootEquityInfo, true,arg);
                try {
                    CommonSqlUtil.insertData(tenantId, EQUITY_TABLE_NAME, Lists.newArrayList(dataItem));
                } catch (Exception e) {
                    log.warn("insertEquityData error!", e);
                    throw e;
                }

            }
            log.warn("processEquityRelationData.markProblemEnterprise start time:{}",System.currentTimeMillis());
            markProblemEnterprise(user, rootId,arg);
            //2、根据客户树生成规则取最小股权值
            Double minPercent = getGenerateTreeRelationRule(tenantId,arg);
            log.warn("processEquityRelationData.markEquityData start time:{}",System.currentTimeMillis());
            //3、针对叶子节点股权数据做标记（股权占比为1.0的直接标记为Y，股权占比小于规则中设置的股权值标记为N不参与建树，需要计算是否参与最终建树的节点标记为T）
            markEquityData(tenantId, rootId, minPercent,arg);
            log.warn("processEquityRelationData.processDuplicatedEnterprise start time:{}",System.currentTimeMillis());
            //4、处理有问题的企业数据（企业名称相同，但工商中没有的节点），
            processDuplicatedEnterprise(user, rootId,arg);
            log.warn("processEquityRelationData.processDuplicatedEquityData start time:{}",System.currentTimeMillis());
            //5、处理相同公司由集团内多个子公司持股场景（持股比例最大标记为Y， 多个子公司对同一个公司持股相同时标记层级最长的Y）
            Map<String, List<ConflictDataInfo>> conflictDataMap = Maps.newHashMap();
            processDuplicatedEquityData(tenantId, rootId, conflictDataMap,arg);
            log.warn("processEquityRelationData.processMarkedEquityData start time:{}",System.currentTimeMillis());
            //6、处理节点标记为T的数据，计算最终的节点类型（N--不参与建树、 Y或EY--参与建树)
            processMarkedEquityData(user, rootId, conflictDataMap,arg);
            log.warn("processEquityRelationData.processEnterpriseBranch start time:{}",System.currentTimeMillis());
            //7、处理所有叶子节点分支机构
            processEnterpriseBranch(user, rootId,arg);
            log.warn("processEquityRelationData.processStatistics start time:{}",System.currentTimeMillis());
            //8、处理是否统计
            processStatistics(tenantId, rootId,arg);
            log.warn("processEquityRelationData.replaceEnterpriseInfoData start time:{}",System.currentTimeMillis());
            //8.5客户树替换
            replaceEnterpriseInfoData(user, rootEquityInfo,arg);
            log.warn("processEquityRelationData.replaceRootNode start time:{}",System.currentTimeMillis());
            //8.6 rootEquityInfo替换
            replaceRootNode(user, rootEquityInfo,arg);
            log.warn("processEquityRelationData.createTreeRelationData start time:{}",System.currentTimeMillis());
            //9、根据标记为Y的股权数据生成客户树关系
            createTreeRelationData(user, rootEquityInfo,arg);
            log.warn("processEquityRelationData.processConflictEquityData start time:{}",System.currentTimeMillis());
            //10、处理股权占比相等的情况并发送CRM通知
            processConflictEquityData(user, rootId, conflictDataMap,arg);
            //11、发送客户树创建成功CRM通知
            sendCRMNotification(user, rootId,arg);
        } catch (Exception e) {
            log.warn(String.format("processEquityRelationData error, tenantId: %s, companyName:%s", user.getTenantId(), equityRelationInfo.getName()), e);
            ExceptionUtil.throwCommonBusinessException();
        } finally {
            //12、清理临时数据
//            clearTempData(tenantId, rootId);
        }
    }

    private ProcessedEquityRelation processChildNode(String tenantId, ProcessedEquityInfo rootNode, Trees currentNode, List<ProcessedEquityRelation> parentNodes, Set<String> insertedDataIds,CreateAccountTreeRelationArg arg) throws Exception {
        try {
            if (CollectionUtils.empty(currentNode.getItems())) {
                insertEquityData(tenantId, rootNode, currentNode, parentNodes, insertedDataIds,arg);
                return null;
            }
            String id = serviceFacade.generateId();
            ProcessedEquityRelation processedEquityEntity = ProcessedEquityRelation.builder()
                    .entityId(id).currentNode(convertTrees2ProcessedEquityInfo(currentNode, id))
                    .parentNodes(Lists.newArrayList(parentNodes)).build();
            parentNodes.add(processedEquityEntity);
            for (Trees node : currentNode.getItems()) {
                processChildNode(tenantId, rootNode, node, parentNodes, insertedDataIds,arg);
            }
            parentNodes.removeIf(x -> currentNode.getEid().equals(x.getCurrentNode().getEid()));
            return null;
        } catch (Exception e) {
            log.warn("processChildNode error", e);
            throw e;
        }
    }

    private void insertEquityData(String tenantId, ProcessedEquityInfo rootNode, Trees currentNode, List<ProcessedEquityRelation> parentNodes, Set<String> insertedDataIds,CreateAccountTreeRelationArg arg) throws Exception {
        List<Map<String, Object>> dataList = Lists.newArrayList();
        List<String> treePath = Lists.newArrayList();
        List<Double> treePercent = Lists.newArrayList();
        if(CollectionUtils.notEmpty(parentNodes)) {
            for(int index = 0; index < parentNodes.size(); ++index) {
                ProcessedEquityInfo parentNode = index > 0 ? parentNodes.get(index - 1).getCurrentNode() : null;
                ProcessedEquityInfo node = parentNodes.get(index).getCurrentNode();
                String id = node.getId();
                treePath.add(id);
                treePercent.add(getPercent(node.getPercent()));
                if(insertedDataIds.contains(id)) {
                    continue;
                }
                Map<String, Object> dataItem = getDataItem(tenantId, rootNode, treePath, treePercent, index + 1, parentNode, node, false,arg);
                dataList.add(dataItem);
                insertedDataIds.add(id);
            }
        }

        ProcessedEquityInfo parentNode = CollectionUtils.notEmpty(parentNodes) ? parentNodes.get(parentNodes.size() - 1).getCurrentNode() : null;
        if(currentNode != null) {
            String id = serviceFacade.generateId();
            treePath.add(id);
            treePercent.add(getPercent(currentNode.getPercent()));
            Map<String, Object> dataItem = getDataItem(tenantId, rootNode, treePath, treePercent, parentNodes.size() + 1, parentNode, convertTrees2ProcessedEquityInfo(currentNode, id), true,arg);
            dataList.add(dataItem);
            insertedDataIds.add(id);
        }
        try {
            CommonSqlUtil.insertData(tenantId, EQUITY_TABLE_NAME, Lists.newArrayList(dataList));
        } catch (Exception e) {
            log.warn("insertEquityData error!", e);
            throw e;
        }

    }

    @NotNull
    private Double getPercent(String percent) {
        Double result = 0D;
        try {
            result = Double.valueOf(percent);
        } catch (Exception e) {
            result = 0D;
        }
        return result;
    }

    @NotNull
    private Map<String, Object> getDataItem(String tenantId, ProcessedEquityInfo rootNode, List<String> treePath, List<Double> treePercent, int level, ProcessedEquityInfo parentNode, ProcessedEquityInfo node, Boolean isLeaf,CreateAccountTreeRelationArg arg) {

        Map<String, Object> dataItem = Maps.newHashMap();
        dataItem.put(ID, node.getId());
        dataItem.put("tenant_id", tenantId);
        dataItem.put(NAME, node.getName());
        dataItem.put(ACCOUNT_MAIN_DATA_ID, null);
        dataItem.put(ENTERPRISE_ID, node.getEid().replace("-", ""));
        dataItem.put(PARENT_ID, parentNode == null ? null : parentNode.getId());
        dataItem.put(PARENT_ACCOUNT_MAIN_DATA_ID, null);
        dataItem.put(PARENT_ENTERPRISE_ID, parentNode == null ? null : parentNode.getEid().replace("-", ""));
        dataItem.put("parent_name", parentNode == null ? null : parentNode.getName());
        dataItem.put("level", level);
        dataItem.put(PERCENT, getPercent(node.getPercent()));
        dataItem.put("amount", node.getAmount());
        dataItem.put("type", node.getType());
        dataItem.put("has_problem", "0".equals(node.getHas_problem()));
        dataItem.put(ROOT_ID, rootNode.getId());
        dataItem.put(ROOT_ACCOUNT_MAIN_DATA_ID, null);
        dataItem.put(ROOT_ENTERPRISE_ID, rootNode.getEid().replace("-", ""));
        dataItem.put(TREE_PATH, String.join(".", treePath));
        dataItem.put(IS_STATISTICS_INCLUDE,true);
        dataItem.put(IS_ROOT, parentNode == null && rootNode.getId().equals(node.getId()));
        dataItem.put(IS_LEAF, isLeaf);
        dataItem.put(IS_BRANCH, false);
        dataItem.put(OBJECT_API_NAME, arg.getDescribeApiName());
        Double rootPercent = 1.0D;
        for(Double percent : treePercent) {
            rootPercent = rootPercent * percent;
        }
        dataItem.put(ROOT_PERCENT, rootPercent);
        dataItem.put(TOTAL_ROOT_PERCENT, rootPercent);
        dataItem.put(NODE_TYPE, ORI_NODE_TYPE);
        return dataItem;
    }

    private ProcessedEquityInfo convertTrees2ProcessedEquityInfo(Trees data, String id) {
        return ProcessedEquityInfo.builder()
                .id(id)
                .eid(data.getEid())
                .name(data.getName())
                .has_problem(data.getHas_problem())
                .type(data.getType())
                .percent(data.getPercent())
                .amount(data.getAmount())
                .build();
    }

    //step1 处理有问题的企业数据,根据工商状态，排除状态为 注销、吊销、停业、清算
    private void markProblemEnterprise(User user, String rootId, CreateAccountTreeRelationArg arg) throws Exception {
        try {
            String tenantId = user.getTenantId();
            CommonSqlSearchTemplate searchTemplate = getSearchTemplateOrderById(tenantId, rootId,arg);
            List<WhereParam> whereParamsList = searchTemplate.getWhereParamList();
            List<String> invalidIds = Lists.newArrayList();
            Map<String, Object> dataMap = Maps.newHashMap();
            dataMap.put(NODE_TYPE, "D");
            int index = 0;
            while (true) {
                List<Map> result = CommonSqlUtil.queryData(searchTemplate, tenantId);
                if(CollectionUtils.empty(result)) {
                    return;
                }
                String lastDataId = ObjectDataUtil.getStringValue(result.get(result.size() - 1), ID, "");
                whereParamsList.removeIf(x -> ID.equals(x.getColumn()));
                CommonSqlUtil.addWhereParam(whereParamsList, ID, CommonSqlOperator.GT, Lists.newArrayList(lastDataId));
                for(Map<String, Object> data : result) {
                    String id = ObjectDataUtil.getStringValue(data, ID, "");
                    if(invalidIds.contains(id)) {
                        continue;
                    }
                    String name = ObjectDataUtil.getStringValue(data, NAME, "");
                    if(StringUtils.isBlank(name)) {
                        continue;
                    }
                    CompanyDetail companyDetail = queryCompanyDetailByName(user, name);
                    if (companyDetail == null || StringUtils.isBlank(companyDetail.getId())) {
                        continue;
                    }
                    String validEnterpriseId = companyDetail.getId().replace("-", "");
                    String enterpriseId = ObjectDataUtil.getStringValue(data, ENTERPRISE_ID, "");
                    //有问题企业
                    if(INVALID_STATUS.contains(companyDetail.getNew_status())
                            || !enterpriseId.equals(validEnterpriseId)) {
                        String getValidIdSql = String.format("SELECT id FROM biz_equity_relation_temp " +
                                        "WHERE tenant_id=%s AND root_id=%s AND object_api_name=%s AND tree_path ~ '*.%s.*' ",
                                SqlEscaper.pg_quote(tenantId),SqlEscaper.pg_quote(rootId),SqlEscaper.pg_quote(arg.getDescribeApiName()), SqlEscaper.pg_escape(id));
                        List<Map> needUpdateResult = CommonSqlUtil.findBySql(tenantId, getValidIdSql);
                        try {
                            List<String> ids = getIds(needUpdateResult, ID);
                            invalidIds.addAll(ids);
                            updateEquityData(tenantId, rootId, dataMap, needUpdateResult);
                        } catch (Exception e) {
                            log.warn("processProblemEnterprise", e);
                        }
                    }
                }
                safeThreadSleep(CommonTreeRelationUtil.CREATE_ACCOUNT_TREE_SLEEP_TIME);
                index++;
                if(index>100000){
                    log.error("markProblemEnterprise 死循环，强制退出");
                 return;
                }
            }
        }catch (Exception e) {
            log.warn("processProblemEnterprise error", e);
            throw e;
        }
    }

    //step2 客户树生成规则获取参与计算的叶子节点最小股权值
    private Double getGenerateTreeRelationRule(String tenantId,CreateAccountTreeRelationArg arg) {
        Double result = 0.1D;

        try {
            User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
            String key = "";
            if(ObjectUtils.isEmpty(arg.getDescribeApiName()) || EquityRelationshipDataConstants.AccountMainDataObj.equals(arg.getDescribeApiName())){
                key = RULE_CONFIG_KEY;
            }else{
                key = COMMON_RULE_CONFIG_KEY+arg.getDescribeApiName();
            }
            String configValue = configService.findTenantConfig(user, key);
            if(StringUtils.isBlank(configValue)) {
                return result;
            }
            GenerateTreeRelationRule generateTreeRelationRule = JsonUtil.fromJson(configValue, GenerateTreeRelationRule.class);
            Optional<ConfigEntity> opConfig = generateTreeRelationRule.getConfig().stream()
                    .filter(x -> "min_percent".equals(x.getConfig_key())).findFirst();
            if(opConfig.isPresent()) {
                Double value = Double.valueOf(opConfig.get().getConfig_value());
                result = value / 100D;
            }
        } catch (Exception e) {
            log.warn("getGenerateTreeRelationRule error, tenantId: " + tenantId, e);
        }
        return result;
    }

    //step3 针对叶子节点股权数据做标记
    private void markEquityData(String tenantId, String rootId, Double minPercent, CreateAccountTreeRelationArg arg) throws Exception{
        try {
            Map<String, Object> dataMap = Maps.newHashMap();
            dataMap.put(NODE_TYPE, 'N');
            List<WhereParam> whereParamsList = getWhereParams(tenantId, rootId);
            CommonSqlUtil.addWhereParam(whereParamsList, IS_LEAF, CommonSqlOperator.EQ, Lists.newArrayList(Boolean.TRUE));
            CommonSqlUtil.addWhereParam(whereParamsList, ROOT_PERCENT, CommonSqlOperator.LT, Lists.newArrayList(minPercent));
            CommonSqlUtil.addWhereParam(whereParamsList, NODE_TYPE, CommonSqlOperator.NEQ, Lists.newArrayList("D"));
            CommonSqlUtil.addWhereParam(whereParamsList, OBJECT_API_NAME, CommonSqlOperator.EQ, Lists.newArrayList(arg.getDescribeApiName()));
            CommonSqlUtil.updateData(tenantId, EQUITY_TABLE_NAME, dataMap, whereParamsList);

            //处理所有叶子节点为N的中间节点，需要将这类中间节点更新为叶子节点
            int maxExecuteCount = 100;
            int executedCount = 0;
            while (true) {
                if(executedCount > maxExecuteCount) {
                    break;
                }
                String queryString = String.format("SELECT DISTINCT parent_id FROM biz_equity_relation_temp " +
                                "WHERE tenant_id=%s AND root_id=%s AND object_api_name=%s AND is_leaf='t' ",
                        SqlEscaper.pg_quote(tenantId), SqlEscaper.pg_quote(rootId), SqlEscaper.pg_quote(arg.getDescribeApiName()));
                List<Map> queryResult = CommonSqlUtil.findBySql(tenantId, queryString);
                if(CollectionUtils.empty(queryResult)) {
                    break;
                }
                queryString = String.format("SELECT DISTINCT parent_id FROM biz_equity_relation_temp " +
                                "WHERE tenant_id=%s AND root_id=%s AND is_leaf='t' AND node_type='%s' AND object_api_name=%s  ",
                        SqlEscaper.pg_quote(tenantId), SqlEscaper.pg_quote(rootId), ORI_NODE_TYPE, SqlEscaper.pg_quote(arg.getDescribeApiName()));
                List<Map> hasChildrenResult = CommonSqlUtil.findBySql(tenantId, queryString);
                List<String> ids = getIds(queryResult, PARENT_ID);
                List<String> hasChildrenIds = getIds(hasChildrenResult, PARENT_ID);
                ids.removeIf(hasChildrenIds::contains);
                if(CollectionUtils.empty(ids)) {
                    break;
                }
                List<List<String>> allIdsList = Lists.partition(ids,500);
                for(List<String> allIds : allIdsList){
                    List<WhereParam> whereParams = getWhereParams(tenantId, rootId);
                    CommonSqlUtil.addWhereParam(whereParams, OBJECT_API_NAME, CommonSqlOperator.EQ, Lists.newArrayList(arg.getDescribeApiName()));
                    CommonSqlUtil.addWhereParam(whereParams, ID, CommonSqlOperator.IN, Lists.newArrayList(allIds));
                    Map<String, Object> updateDataMap = Maps.newHashMap();
                    updateDataMap.put(IS_LEAF, true);
                    CommonSqlUtil.updateData(tenantId, EQUITY_TABLE_NAME, updateDataMap, whereParams);
                    whereParams.removeIf(x -> ID.equals(x.getColumn()));
                    CommonSqlUtil.addWhereParam(whereParams, PARENT_ID, CommonSqlOperator.IN, Lists.newArrayList(allIds));
                    updateDataMap.put(IS_LEAF, false);
                    CommonSqlUtil.updateData(tenantId, EQUITY_TABLE_NAME, updateDataMap, whereParams);
                    CommonSqlUtil.updateData(tenantId, EQUITY_TABLE_NAME, dataMap, whereParamsList);
                }
                ++executedCount;
            }
            dataMap.put(NODE_TYPE, 'T');
            whereParamsList.removeIf(x -> ROOT_PERCENT.equals(x.getColumn()));
            CommonSqlUtil.addWhereParam(whereParamsList, ROOT_PERCENT, CommonSqlOperator.GTE, Lists.newArrayList(minPercent));
            CommonSqlUtil.addWhereParam(whereParamsList, ROOT_PERCENT, CommonSqlOperator.LT, Lists.newArrayList(1.0D));
            CommonSqlUtil.updateData(tenantId, EQUITY_TABLE_NAME, dataMap, whereParamsList);

            dataMap.put(NODE_TYPE, 'Y');
            whereParamsList.removeIf(x -> ROOT_PERCENT.equals(x.getColumn()));
            CommonSqlUtil.addWhereParam(whereParamsList, ROOT_PERCENT, CommonSqlOperator.GTE, Lists.newArrayList(1.0D));
            CommonSqlUtil.updateData(tenantId, EQUITY_TABLE_NAME, dataMap, whereParamsList);
        } catch (Exception e) {
            log.warn(String.format("markEquityData error, %s-%s-%s", tenantId, rootId, minPercent), e);
            throw e;
        }
    }

    //step4 处理有问题的企业数据,企业名称相同，但工商详情中没有的企业节点数据（标记股权数据节点类型为N）
    private void processDuplicatedEnterprise(User user, String rootId , CreateAccountTreeRelationArg arg) throws Exception {
        try {
            String tenantId = user.getTenantId();
            String queryString = String.format("SELECT tenant_id, root_id, name FROM biz_equity_relation_temp " +
                            "WHERE tenant_id=%s AND root_id=%s AND object_api_name=%s AND node_type<>'D' " +
                            "GROUP BY tenant_id, root_id, name HAVING COUNT(*)>1",
                    SqlEscaper.pg_quote(tenantId),SqlEscaper.pg_quote(rootId) ,SqlEscaper.pg_quote(arg.getDescribeApiName()));
            List<Map> queryResult = CommonSqlUtil.findBySql(tenantId, queryString);
            if (CollectionUtils.empty(queryResult)) {
                return;
            }
            Map<String, Object> dataMap = Maps.newHashMap();
            dataMap.put(NODE_TYPE, "D");
            List<List<Map>> splitList = ListsUtil.splitList(queryResult, BACH_SIZE);
            for (List<Map> dataList : splitList) {
                List<String> duplicatedNameList = getIds(dataList, NAME);
                String sqlString = String.format("SELECT DISTINCT name, enterprise_id FROM biz_equity_relation_temp WHERE tenant_id=%s " +
                                "AND root_id=%s AND object_api_name=%s AND name=%s  AND node_type<>'D'",
                        SqlEscaper.pg_quote(tenantId),SqlEscaper.pg_quote(rootId),SqlEscaper.pg_quote(arg.getDescribeApiName()),SqlEscaper.any_clause(duplicatedNameList));
                List<Map> nameResult = CommonSqlUtil.findBySql(tenantId, sqlString);
                if (nameResult.size() == duplicatedNameList.size()) {
                    continue;
                }
                Map<String, List<Map<String, Object>>> nameCountMap = Maps.newHashMap();
                nameResult.forEach(x -> {
                    String name = ObjectDataUtil.getStringValue(x, NAME, "");
                    if (nameCountMap.containsKey(name)) {
                        nameCountMap.get(name).add(x);
                    } else {
                        nameCountMap.put(name, Lists.newArrayList(x));
                    }
                });

                for (Map.Entry<String, List<Map<String, Object>>> entry : nameCountMap.entrySet()) {
                    if (entry.getValue().size() < 2) {
                        continue;
                    }
                    CompanyDetail companyDetail = queryCompanyDetailByName(user, entry.getKey());
                    if (companyDetail == null || StringUtils.isBlank(companyDetail.getId())) {
                        continue;
                    }
                    String validEnterpriseId = companyDetail.getId().replace("-", "");
                    entry.getValue().forEach(x -> {
                        String eid = ObjectDataUtil.getStringValue(x, ENTERPRISE_ID, "");
                        if (!validEnterpriseId.equals(eid)) {
                            String getAllValidIdSql = String.format("SELECT id FROM biz_equity_relation_temp " +
                                            "WHERE tenant_id=%s AND root_id=%s AND enterprise_id=%s AND object_api_name=%s ",
                                    SqlEscaper.pg_quote(tenantId),SqlEscaper.pg_quote(rootId), SqlEscaper.pg_quote(eid),SqlEscaper.pg_quote(arg.getDescribeApiName()));
                            List<Map> tempResult = CommonSqlUtil.findBySql(tenantId, getAllValidIdSql);
                            tempResult.forEach(d -> {
                                String id = ObjectDataUtil.getStringValue(d, ID, "");
                                String getValidIdSql = String.format("SELECT id FROM biz_equity_relation_temp " +
                                                "WHERE tenant_id=%s AND root_id=%s AND object_api_name=%s AND tree_path ~ '*.%s.*' ",
                                        SqlEscaper.pg_quote(tenantId),SqlEscaper.pg_quote(rootId),SqlEscaper.pg_quote(arg.getDescribeApiName()), SqlEscaper.pg_escape(id));
                                List<Map> needUpdateResult = CommonSqlUtil.findBySql(tenantId, getValidIdSql);
                                try {
                                    updateEquityData(tenantId, rootId, dataMap, needUpdateResult);
                                } catch (Exception e) {
                                    log.warn("processDuplicatedEnterprise", e);
                                }
                            });
                        }
                    });
                }
                safeThreadSleep(CommonTreeRelationUtil.CREATE_ACCOUNT_TREE_SLEEP_TIME);
            }
        }catch (Exception e) {
            log.warn("processDuplicatedEnterprise error", e);
            throw e;
        }
    }

    //step5 处理相同公司由集团内多个子公司持股场景（持股比例最大标记为Y， 多个子公司对同一个公司持股相同时标记EY(发送通知并记录日志，由用户决定挂哪个分支））
    private void processDuplicatedEquityData(String tenantId, String rootId, Map<String, List<ConflictDataInfo>> conflictDataMap,CreateAccountTreeRelationArg arg) throws Exception {
        try {
            String queryString = String.format("SELECT tenant_id, root_id, enterprise_id FROM biz_equity_relation_temp WHERE tenant_id=%s " +
                            "AND root_id=%s AND object_api_name=%s AND node_type<>'D' " +
                            "GROUP BY tenant_id, root_id, enterprise_id HAVING COUNT(*)>1",
                    SqlEscaper.pg_quote(tenantId), SqlEscaper.pg_quote(rootId),SqlEscaper.pg_quote(arg.getDescribeApiName()));
            List<Map> queryResult = CommonSqlUtil.findBySql(tenantId, queryString);
            if(CollectionUtils.empty(queryResult)) {
                return;
            }
            for(Map<String, Object> item : queryResult) {
                String eid = ObjectDataUtil.getStringValue(item, ENTERPRISE_ID, "");
                List<WhereParam> whereParamsList = getWhereParams(tenantId, rootId);
                CommonSqlUtil.addWhereParam(whereParamsList, ENTERPRISE_ID, CommonSqlOperator.EQ, Lists.newArrayList(eid));
                CommonSqlUtil.addWhereParam(whereParamsList, OBJECT_API_NAME, CommonSqlOperator.EQ, Lists.newArrayList(arg.getDescribeApiName()));
                List<Map> tempResult = CommonSqlUtil.queryData(tenantId, EQUITY_TABLE_NAME, whereParamsList);
                int maxValueIndex = 0;
                Double maxValue = 0D;
                Double hasEqualsValue = -1D;
                Double totalRootPercent = 0D;
                for(int index = 0; index < tempResult.size(); ++index) {
                    Double rootPercent = ObjectDataUtil.getDoubleValue(tempResult.get(index), ROOT_PERCENT, 0D);
                    if(rootPercent > maxValue) {
                        maxValue = rootPercent;
                        maxValueIndex = index;
                    } else if(Double.doubleToLongBits(maxValue) == Double.doubleToLongBits(rootPercent)) {
                        hasEqualsValue = rootPercent;
                    }
                    totalRootPercent += rootPercent;
                }
                Map<String, Object> dataMap = Maps.newHashMap();
                dataMap.put(TOTAL_ROOT_PERCENT, totalRootPercent);
                updateEquityData(tenantId, rootId, dataMap, tempResult);
                if(maxValue > hasEqualsValue) {
                    tempResult.remove(maxValueIndex);
                    updateNodeType(tenantId, rootId, "C", tempResult);
                } else {
                    Double doubleValue = maxValue;
                    tempResult.removeIf(x -> ObjectDataUtil.getDoubleValue(x, ROOT_PERCENT, 0D) < doubleValue);
                    updateNodeType(tenantId, rootId, "EY", tempResult);
                    if(tempResult.stream().anyMatch(x -> Boolean.TRUE.equals(ObjectDataUtil.getBooleanValue(x, IS_LEAF, false)))) {
                        if(conflictDataMap.containsKey(eid) &&
                                conflictDataMap.get(eid).stream().anyMatch(x -> rootId.equals(x.getConflict_root_id()))) {
                            continue;
                        }
                        ConflictDataInfo conflictDataInfo = getConflictDataInfo(eid, rootId, rootId, "E");
                        if (conflictDataMap.containsKey(eid)) {
                            conflictDataMap.get(eid).add(conflictDataInfo);
                        } else {
                            conflictDataMap.put(eid, Lists.newArrayList(conflictDataInfo));
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("processMarkedEquityData error", e);
            throw e;
        }
    }

    //step6 处理节点标记为T的数据，计算最终的节点类型（N--不参与建树、 Y或EY--参与建树)
    //子步骤：1、判断股权数据上的企业是否已经是否已经生成是已有客户树节点，为否的直接标记为Y
    //       2、已经生成是已有客户树节点处理
    private void processMarkedEquityData(User user, String rootId, Map<String, List<ConflictDataInfo>> conflictDataMap, CreateAccountTreeRelationArg arg) throws Exception {
        try {
            String tenantId = user.getTenantId();
            CommonSqlSearchTemplate searchTemplate = getSearchTemplateOrderById(tenantId, rootId,arg);
            List<WhereParam> whereParamsList = searchTemplate.getWhereParamList();
            CommonSqlUtil.addWhereParam(whereParamsList, IS_LEAF, CommonSqlOperator.EQ, Lists.newArrayList(Boolean.TRUE));
            CommonSqlUtil.addWhereParam(whereParamsList, NODE_TYPE, CommonSqlOperator.EQ, Lists.newArrayList("T"));

            String table = CommonTreeRelationUtil.getRelationshipsMapping(arg.getDescribeApiName()).getStoreTableName();

            while (true) {
                List<Map> result = CommonSqlUtil.queryData(searchTemplate, tenantId);
                if(CollectionUtils.empty(result)) {
                    return;
                }
                String lastDataId = ObjectDataUtil.getStringValue(result.get(result.size() - 1), ID, "");
                whereParamsList.removeIf(x -> ID.equals(x.getColumn()));
                CommonSqlUtil.addWhereParam(whereParamsList, ID, CommonSqlOperator.GT, Lists.newArrayList(lastDataId));
                List<String> eIds = getIds(result, ENTERPRISE_ID);
                String queryString = String.format("SELECT name, root_id, root_enterprise_id, enterprise_id, tree_path, percent, root_percent, total_root_percent FROM %s " +
                                "WHERE tenant_id=%s AND enterprise_id=%s AND is_deleted=0 AND is_leaf='t' AND is_branch='f' ",
                        SqlEscaper.pg_escape(table),SqlEscaper.pg_quote(tenantId), SqlEscaper.any_clause(eIds));
                List<Map> queryResult = CommonSqlUtil.findBySql(tenantId, queryString);
                if(CollectionUtils.empty(queryResult)) {
                    updateNodeType(tenantId, rootId, "Y", result);
                    continue;
                }
                List<String> existTreeIds = getIds(queryResult, ENTERPRISE_ID);
                List<Map> tempResult = Lists.newArrayList(result);
                tempResult.removeIf(x -> existTreeIds.contains(ObjectDataUtil.getStringValue(x, ENTERPRISE_ID, "")));
                updateNodeType(tenantId, rootId, "Y", tempResult);
                result.removeIf(x -> !existTreeIds.contains(ObjectDataUtil.getStringValue(x, ENTERPRISE_ID, "")));
                List<Map> updateToNList = Lists.newArrayList();
                List<Map> updateToYList = Lists.newArrayList();
                List<Map> updateToEYList = Lists.newArrayList();
                for(Map<String, Object> item : result) {
                    String eid = ObjectDataUtil.getStringValue(item, ENTERPRISE_ID, "");
                    List<Map> tempList = queryResult.stream()
                            .filter(x -> eid.equals(ObjectDataUtil.getStringValue(x, ENTERPRISE_ID, "")))
                            .collect(Collectors.toList());
                    Map<String, Object> mapData = tempList.get(0);
                    String conflictRootId = ObjectDataUtil.getStringValue(mapData, ROOT_ID, "");
                    if(conflictDataMap.containsKey(eid) &&
                            conflictDataMap.get(eid).stream().anyMatch(x -> conflictRootId.equals(x.getConflict_root_id()))) {
                        continue;
                    }
                    String companyName = ObjectDataUtil.getStringValue(mapData, NAME, "");
                    String conflictRootEid = ObjectDataUtil.getStringValue(mapData, ROOT_ENTERPRISE_ID, "");
                    Double rootTotalPercent = ObjectDataUtil.getDoubleValue(item, TOTAL_ROOT_PERCENT, 0D);
                    Double conflictRootTotalPercent;
                    if(mapData.get(TOTAL_ROOT_PERCENT) == null) {
                        processConflictEquityData(user, companyName, conflictRootId, conflictRootEid,arg);
                        conflictRootTotalPercent = getTotalRootPercent(tenantId, conflictRootId, conflictRootEid,arg);
                    } else {
                        conflictRootTotalPercent = ObjectDataUtil.getDoubleValue(mapData, TOTAL_ROOT_PERCENT, 0D);
                    }
                    String nodeType = "N";
                    if(rootTotalPercent > conflictRootTotalPercent) {
                        nodeType = "Y";
                        updateToYList.add(item);
                    } else if(Double.doubleToLongBits(rootTotalPercent) == Double.doubleToLongBits(conflictRootTotalPercent)) {
                        nodeType = "E";
                        updateToEYList.add(item);
                    } else {
                        updateToNList.add(item);
                    }
                    if(!("N".equals(nodeType))) {
                        ConflictDataInfo conflictDataInfo = getConflictDataInfo(eid, rootId, conflictRootId, nodeType);
                        if (conflictDataMap.containsKey(eid)) {
                            conflictDataMap.get(eid).add(conflictDataInfo);
                        } else {
                            conflictDataMap.put(eid, Lists.newArrayList(conflictDataInfo));
                        }
                    }
                }
                updateNodeType(tenantId, rootId, "N", updateToNList);
                updateNodeType(tenantId, rootId, "Y", updateToYList);
                updateNodeType(tenantId, rootId, "EY", updateToEYList);
            }
        } catch (Exception e) {
            log.warn("processMarkedEquityData error", e);
            throw e;
        }
    }

    private ConflictDataInfo getConflictDataInfo(String eid, String rootId, String conflictRootId, String nodeType) {
        return ConflictDataInfo.builder()
                .conflict_root_id(conflictRootId)
                .root_id(rootId)
                .enterprise_id(eid)
                .node_type(nodeType)
                .build();
    }

    private void processConflictEquityData(User user, String companyName, String conflictRootId, String conflictRootEid,CreateAccountTreeRelationArg arg) throws Exception {
        try {
            String tenantId = user.getTenantId();
            RelationshipModel equityRelationInfo = getEquityRelationInfo(user, companyName);
            if (equityRelationInfo == null || CollectionUtils.empty(equityRelationInfo.getP_trees())) {
                return;
            }
            List<WhereParam> whereParamList = getWhereParams(user.getTenantId(), conflictRootId);
            CommonSqlUtil.addWhereParam(whereParamList, ID, CommonSqlOperator.EQ, Lists.newArrayList(conflictRootId));
            CommonSqlUtil.addWhereParam(whereParamList, OBJECT_API_NAME, CommonSqlOperator.EQ, Lists.newArrayList(arg.getDescribeApiName()));
            CommonSqlUtil.deleteData(tenantId, EQUITY_TABLE_NAME, whereParamList);
            ProcessedEquityInfo lastChildEquityInfo = getProcessedEquityInfo(equityRelationInfo, conflictRootId);
            ProcessedEquityRelation rootEquityEntity = ProcessedEquityRelation.builder().parentNodes(Lists.newArrayList())
                    .currentNode(lastChildEquityInfo).entityId(lastChildEquityInfo.getId()).build();
            Set<String> insertedDataIds = Sets.newHashSet();
            for (Trees node : equityRelationInfo.getP_trees()) {
                processParentNode(tenantId, conflictRootEid, lastChildEquityInfo, node, Lists.newArrayList(rootEquityEntity), insertedDataIds,arg);
            }
        } catch (Exception e) {
            log.warn("processConflictEquityData error", e);
            throw e;
        }
    }

    private Double getTotalRootPercent(String tenantId, String rootId, String enterpriseId,CreateAccountTreeRelationArg arg) throws Exception {
        try {
            Double result = 0D;
            List<WhereParam> whereParamsList = getWhereParams(tenantId, rootId);
            CommonSqlUtil.addWhereParam(whereParamsList, IS_LEAF, CommonSqlOperator.EQ, Lists.newArrayList(Boolean.TRUE));
            CommonSqlUtil.addWhereParam(whereParamsList, ENTERPRISE_ID, CommonSqlOperator.EQ, Lists.newArrayList(enterpriseId));
            CommonSqlUtil.addWhereParam(whereParamsList, OBJECT_API_NAME, CommonSqlOperator.EQ, Lists.newArrayList(arg.getDescribeApiName()));
            List<Map> queryResult = CommonSqlUtil.queryData(tenantId, EQUITY_TABLE_NAME, whereParamsList);
            for (Map<String, Object> item : queryResult) {
                result += ObjectDataUtil.getDoubleValue(item, ROOT_PERCENT, 0D);
            }
            return result;
        } catch (Exception e) {
            log.warn("getTotalRootPercent error", e);
            throw e;
        }
    }

    private ProcessedEquityInfo getProcessedEquityInfo(RelationshipModel equityRelationInfo, String rootId) {
        return ProcessedEquityInfo.builder()
                .id(rootId)
                .eid(equityRelationInfo.getEid())
                .name(equityRelationInfo.getName())
                .has_problem(equityRelationInfo.getHas_problem())
                .type(equityRelationInfo.getType())
                .percent("1.0")
                .build();
    }

    private void processParentNode(String tenantId, String conflictRootEid, ProcessedEquityInfo rootNode, Trees currentNode, List<ProcessedEquityRelation> parentNodes, Set<String> insertedDataIds,CreateAccountTreeRelationArg arg) throws Exception {
        try {
            String eid = currentNode.getEid().replace("-", "");
            if(conflictRootEid.equals(eid)) {
                insertEquityData(tenantId, rootNode, currentNode, parentNodes, insertedDataIds,arg);
                return;
            }

            String id = serviceFacade.generateId();
            ProcessedEquityRelation processedEquityEntity = ProcessedEquityRelation.builder()
                    .entityId(id).currentNode(convertTrees2ProcessedEquityInfo(currentNode, id))
                    .parentNodes(Lists.newArrayList(parentNodes)).build();
            parentNodes.add(processedEquityEntity);
            for (Trees node : currentNode.getItems()) {
                processParentNode(tenantId, conflictRootEid, rootNode, node, parentNodes, insertedDataIds,arg);
            }
            parentNodes.removeIf(x -> currentNode.getEid().equals(x.getCurrentNode().getEid()));
        } catch (Exception e) {
            log.warn("processParentNode error", e);
            throw e;
        }
    }

    private void updateEquityData(String tenantId, String rootId, Map<String, Object> dataMap, List<Map> dataList) throws MetadataServiceException {
        if(CollectionUtils.empty(dataList)) {
            return;
        }

        List<String> ids = getIds(dataList, ID);
        List<WhereParam> dataWhereParamsList = getWhereParams(tenantId, rootId);
        CommonSqlUtil.addWhereParam(dataWhereParamsList, ID, CommonSqlOperator.IN, Lists.newArrayList(ids));
        CommonSqlUtil.updateData(tenantId, EQUITY_TABLE_NAME, dataMap, dataWhereParamsList);
    }

    private void updateNodeType(String tenantId, String rootId, String nodeTypeValue, List<Map> dataList) throws MetadataServiceException {
        if(CollectionUtils.empty(dataList)) {
            return;
        }

        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put(NODE_TYPE, nodeTypeValue);
        List<String> ids = getIds(dataList, ID);
        List<WhereParam> dataWhereParamsList = getWhereParams(tenantId, rootId);
        CommonSqlUtil.addWhereParam(dataWhereParamsList, ID, CommonSqlOperator.IN, Lists.newArrayList(ids));
        CommonSqlUtil.addWhereParam(dataWhereParamsList, IS_LEAF, CommonSqlOperator.EQ, Lists.newArrayList(Boolean.TRUE));
        CommonSqlUtil.updateData(tenantId, EQUITY_TABLE_NAME, dataMap, dataWhereParamsList);
    }

    @NotNull
    private List<String> getIds(List<Map> dataList, String idKey) {
        List<String> ids = Lists.newArrayList();
        dataList.forEach(x -> {
            if(ObjectUtils.isNotEmpty(x)){
                ids.add(ObjectDataUtil.getStringValue(x, idKey, ""));
            }
        });
        return ids;
    }

    @NotNull
    private List<WhereParam> getWhereParams(String tenantId, String rootId) {
        List<WhereParam> whereParamsList = Lists.newArrayList();
        CommonSqlUtil.addWhereParam(whereParamsList, LtoFieldApiConstants.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(tenantId));
        CommonSqlUtil.addWhereParam(whereParamsList, ROOT_ID, CommonSqlOperator.EQ, Lists.newArrayList(rootId));
        return whereParamsList;
    }

    //step7 处理所有叶子节点分支机构
    private void processEnterpriseBranch(User user, String rootId, CreateAccountTreeRelationArg arg) throws Exception {
        try {
            String tenantId = user.getTenantId();
            boolean grayQueryCommerceInfo = GrayUtil.isGrayEnable("account_tree_query_commerce_info_enable", tenantId);
            if(!grayQueryCommerceInfo) {
                return;
            }
            CommonSqlSearchTemplate searchTemplate = getSearchTemplateOrderById(tenantId, rootId,arg);
            List<WhereParam> whereParamsList = searchTemplate.getWhereParamList();

            if(GrayUtil.isGraySfaCommonTreeAddBranchTenant(user.getTenantId())){
                //纷享云所有的节点都生成分支机构，招商局只有叶子节点生成
                CommonSqlUtil.addWhereParam(whereParamsList, IS_LEAF, CommonSqlOperator.EQ, Lists.newArrayList(Boolean.TRUE));
                CommonSqlUtil.addWhereParam(whereParamsList, NODE_TYPE, CommonSqlOperator.IN, Lists.newArrayList("Y", "EY"));
            }
            CommonSqlUtil.addWhereParam(whereParamsList, IS_BRANCH, CommonSqlOperator.EQ, Lists.newArrayList(false));

            while (true) {
                List<Map> result = CommonSqlUtil.queryData(searchTemplate, tenantId);
                if(CollectionUtils.empty(result)) {
                    return;
                }
                String lastDataId = ObjectDataUtil.getStringValue(result.get(result.size() - 1), ID, "");
                whereParamsList.removeIf(x -> ID.equals(x.getColumn()));
                CommonSqlUtil.addWhereParam(whereParamsList, ID, CommonSqlOperator.GT, Lists.newArrayList(lastDataId));
                for (Map<String, Object> item : result) {
                    String companyName = ObjectDataUtil.getStringValue(item, NAME, "");
                    if(StringUtils.isBlank(companyName)) {
                        continue;
                    }
                    CompanyDetail companyDetail = queryCompanyDetailByName(user, companyName);
                    if(companyDetail == null || ObjectUtils.isEmpty(companyDetail) || CollectionUtils.empty(companyDetail.getBranches())) {
                        continue;
                    }
                    List<Map<String, Object>> dataList = Lists.newArrayList();
                    companyDetail.getBranches().forEach(x -> {
                        CompanyDetail branchCompanyDetail = queryCompanyDetailByName(user, x.getName());
                        if (branchCompanyDetail != null && !INVALID_STATUS.contains(branchCompanyDetail.getNew_status())) {
                            String id = serviceFacade.generateId();
                            Map<String, Object> dataItem = getDataItem(tenantId, id, item, x,arg);
                            dataList.add(dataItem);
                        }
                    });
                    CommonSqlUtil.insertData(tenantId, EQUITY_TABLE_NAME, Lists.newArrayList(dataList));
                }
                safeThreadSleep(CommonTreeRelationUtil.CREATE_ACCOUNT_TREE_SLEEP_TIME);
            }
        } catch (Exception e) {
            log.warn("processEnterpriseBranch error", e);
            throw e;
        }
    }

    private void safeThreadSleep(int sleepMillis) {
        try {
            if(sleepMillis<=0){
                return;
            }
            Thread.sleep(sleepMillis);
        } catch (InterruptedException e) {
            log.warn("safeThreadSleep thread interrupted", e);
            Thread.currentThread().interrupt();
        } catch (Exception ex) {
            log.warn("safeThreadSleep error", ex);
        }
    }

    @NotNull
    private Map<String, Object> getDataItem(String tenantId, String id, Map<String, Object> parentNode, Lawsuits branchInfo, CreateAccountTreeRelationArg arg) {
        Map<String, Object> dataItem = Maps.newHashMap();
        dataItem.put(ID, id);
        dataItem.put("tenant_id", tenantId);
        dataItem.put(NAME, branchInfo.getName());
        dataItem.put(ACCOUNT_MAIN_DATA_ID, null);
        dataItem.put(ENTERPRISE_ID, branchInfo.getId().replace("-", ""));
        dataItem.put(PARENT_ID, parentNode.get(ID));
        dataItem.put(PARENT_ACCOUNT_MAIN_DATA_ID, parentNode.get(ACCOUNT_MAIN_DATA_ID));
        dataItem.put(PARENT_ENTERPRISE_ID, parentNode.get(ENTERPRISE_ID));
        dataItem.put("parent_name", parentNode.get(NAME));
        dataItem.put("level", ObjectDataUtil.getIntegerValue(parentNode, "level", 0) + 1);
        dataItem.put(PERCENT, 1.0D);
        dataItem.put("amount", null);
        dataItem.put("type", "E");
        dataItem.put("has_problem", false);
        dataItem.put(ROOT_ID, parentNode.get(ROOT_ID));
        dataItem.put(ROOT_ACCOUNT_MAIN_DATA_ID, parentNode.get(ROOT_ACCOUNT_MAIN_DATA_ID));
        dataItem.put(ROOT_ENTERPRISE_ID, parentNode.get(ROOT_ENTERPRISE_ID));
        dataItem.put(TREE_PATH, String.format("%s.%s", parentNode.get(TREE_PATH), id));
        dataItem.put(IS_STATISTICS_INCLUDE,true);
        dataItem.put(IS_ROOT, false);
        dataItem.put(IS_LEAF, true);
        dataItem.put(IS_BRANCH, true);
        dataItem.put(ROOT_PERCENT, parentNode.get(PERCENT));
        dataItem.put(TOTAL_ROOT_PERCENT, parentNode.get(PERCENT));
        dataItem.put(NODE_TYPE, "Y");
        dataItem.put(OBJECT_API_NAME, arg.getDescribeApiName());
        return dataItem;
    }

    //step8 处理是否统计
    private void processStatistics(String tenantId, String rootId, CreateAccountTreeRelationArg arg) throws Exception {
        try {
            CommonSqlSearchTemplate searchTemplate = getSearchTemplateOrderById(tenantId, rootId,arg);
            List<WhereParam> whereParamsList = searchTemplate.getWhereParamList();
            Map<String, Object> dataMap = Maps.newHashMap();
            dataMap.put(IS_STATISTICS_INCLUDE, false);
            List<WhereParam> whereParams = getWhereParams(tenantId, rootId);
            CommonSqlUtil.addWhereParam(whereParams, OBJECT_API_NAME, CommonSqlOperator.EQ, Lists.newArrayList(arg.getDescribeApiName()));

            String table = CommonTreeRelationUtil.getRelationshipsMapping(arg.getDescribeApiName()).getStoreTableName();
            while (true) {
                List<Map> result = CommonSqlUtil.queryData(searchTemplate, tenantId);
                if(CollectionUtils.empty(result)) {
                    return ;
                }
                String lastDataId = ObjectDataUtil.getStringValue(result.get(result.size() - 1), ID, "");
                whereParamsList.removeIf(x -> ID.equals(x.getColumn()));
                CommonSqlUtil.addWhereParam(whereParamsList, ID, CommonSqlOperator.GT, Lists.newArrayList(lastDataId));
                List<String> eIds = getIds(result, ENTERPRISE_ID);
                String queryString = String.format("SELECT DISTINCT enterprise_id FROM %s " +
                                "WHERE tenant_id=%s AND enterprise_id=%s AND is_deleted=0 AND is_statistics_include='t' ",
                        SqlEscaper.pg_escape(table),SqlEscaper.pg_quote(tenantId), SqlEscaper.any_clause(eIds));
                List<Map> queryResult = CommonSqlUtil.findBySql(tenantId, queryString);
                if(CollectionUtils.empty(queryResult)) {
                    continue;
                }
                List<String> updateStatisticsList = getIds(queryResult, ENTERPRISE_ID);
                whereParams.removeIf(x -> ENTERPRISE_ID.equals(x.getColumn()));
                CommonSqlUtil.addWhereParam(whereParams, ENTERPRISE_ID, CommonSqlOperator.IN, Lists.newArrayList(updateStatisticsList));
                CommonSqlUtil.updateData(tenantId, EQUITY_TABLE_NAME, dataMap, whereParams);
            }
        } catch (Exception e) {
            log.warn("processStatistics error", e);
            throw e;
        }
    }

    //step9 根据标记为Y的股权数据生成客户树关系
    private void createTreeRelationData(User user, ProcessedEquityInfo rootNode, CreateAccountTreeRelationArg arg) throws Exception {
        try {
            String tenantId = user.getTenantId();
            String rootId = rootNode.getId();
            CommonSqlSearchTemplate searchTemplate = getSearchTemplateOrderById(tenantId, rootId,arg);
            List<WhereParam> whereParamsList = searchTemplate.getWhereParamList();
            CommonSqlUtil.addWhereParam(whereParamsList, IS_LEAF, CommonSqlOperator.EQ, Lists.newArrayList(Boolean.TRUE));
            CommonSqlUtil.addWhereParam(whereParamsList, NODE_TYPE, CommonSqlOperator.IN, Lists.newArrayList("Y", "EY"));

            Set<String> insertedIds = Sets.newHashSet();
            Set<String> insertedEnterpriseIds = Sets.newHashSet();
            Map<String, String> accountMainDataMap = Maps.newHashMap();
            Set<String> processedAccountMainData = Sets.newHashSet();
            while (true) {
                List<Map> result = CommonSqlUtil.queryData(searchTemplate, tenantId);
                if(CollectionUtils.empty(result)) {
                    return;
                }
                String lastDataId = ObjectDataUtil.getStringValue(result.get(result.size() - 1), ID, "");
                whereParamsList.removeIf(x -> ID.equals(x.getColumn()));
                CommonSqlUtil.addWhereParam(whereParamsList, ID, CommonSqlOperator.GT, Lists.newArrayList(lastDataId));
                List<IObjectData> objectDataList = Lists.newArrayList();
                List<String> needQueryNameList = Lists.newArrayList();
                if(!accountMainDataMap.containsKey(rootNode.getName())) {
                    needQueryNameList.add(rootNode.getName());
                }
                Map<String, String> enterpriseMap = Maps.newHashMap();
                if(!insertedEnterpriseIds.contains(rootNode.getEid().replace("-", ""))) {
                    enterpriseMap.put(rootNode.getEid().replace("-", ""), rootNode.getName());
                }
                processNeedStep(insertedIds, result, needQueryNameList, enterpriseMap, processedAccountMainData);
                processAccountMainData(tenantId, accountMainDataMap, needQueryNameList,arg);
                Set parentIds = Sets.newHashSet();
                for(Map<String, Object> item : result) {
                    String parentIdString = ObjectDataUtil.getStringValue(item, TREE_PATH, "");
                    List<String> parentIdList = Lists.newArrayList(parentIdString.split("\\."));
                    parentIdList.removeIf(insertedIds::contains);
                    parentIds.addAll(parentIdList);
                    IObjectData objectData = convert2ObjectData(user, rootNode, accountMainDataMap, item,arg);
                    objectDataList.add(objectData);
                }
                createEnterpriseInfoData(tenantId, enterpriseMap, insertedEnterpriseIds);
                bulkSaveData(tenantId, objectDataList);
                logAsync(user, CommonTreeRelationUtil.getRelationshipsMapping(arg.getDescribeApiName()).getLinkDescribeApiName(), objectDataList, EventType.ADD, ActionType.Add);
                if(CollectionUtils.empty(parentIds)) {
                    continue;
                }
                insertedIds.addAll(parentIds);
                List<List<String>> splitList = ListsUtil.splitList(Lists.newArrayList(parentIds), BACH_SIZE);
                for(List<String> item : splitList) {
                    objectDataList = Lists.newArrayList();
                    needQueryNameList = Lists.newArrayList();
                    enterpriseMap = Maps.newHashMap();
                    List<WhereParam> paramsList = getWhereParams(tenantId, rootId);
                    CommonSqlUtil.addWhereParam(paramsList, ID, CommonSqlOperator.IN, Lists.newArrayList(item));
                    CommonSqlUtil.addWhereParam(paramsList, OBJECT_API_NAME, CommonSqlOperator.EQ, Lists.newArrayList(arg.getDescribeApiName()));
                    List<Map> queryResult = CommonSqlUtil.queryData(tenantId, EQUITY_TABLE_NAME, paramsList);
                    processNeedStep(insertedIds, queryResult, needQueryNameList, enterpriseMap, processedAccountMainData);
                    processAccountMainData(tenantId, accountMainDataMap, needQueryNameList,arg);
                    for(Map<String, Object> data : queryResult) {
                        IObjectData objectData = convert2ObjectData(user, rootNode, accountMainDataMap, data,arg);
                        objectDataList.add(objectData);
                    }
                    createEnterpriseInfoData(tenantId, enterpriseMap, insertedEnterpriseIds);
                    bulkSaveData(tenantId, objectDataList);
                   // logAsync(user, ACCOUNT_TREE_RELATION_API_NAME, objectDataList, EventType.ADD, ActionType.Add);
                    logAsync(user, CommonTreeRelationUtil.getRelationshipsMapping(arg.getDescribeApiName()).getLinkDescribeApiName(), objectDataList, EventType.ADD, ActionType.Add);
                }
                safeThreadSleep(CommonTreeRelationUtil.CREATE_ACCOUNT_TREE_SLEEP_TIME);
            }
        } catch (Exception e) {
            log.warn("createTreeRelationData error", e);
            throw e;
        }
    }

    private void processNeedStep(Set<String> insertedIds, List<Map> result, List<String> needQueryNameList, Map<String, String> enterpriseMap, Set<String> processedAccountMainData) {
        result.forEach(x -> {
            insertedIds.add(ObjectDataUtil.getStringValue(x, ID, ""));
            String name = ObjectDataUtil.getStringValue(x, NAME, "");
            if(!processedAccountMainData.contains(name)) {
                needQueryNameList.add(ObjectDataUtil.getStringValue(x, NAME, ""));
                processedAccountMainData.add(name);
            }
            enterpriseMap.put(ObjectDataUtil.getStringValue(x, ENTERPRISE_ID, ""), ObjectDataUtil.getStringValue(x, NAME, ""));
        });
    }

    private CommonSqlSearchTemplate getSearchTemplateOrderById(String tenantId, String rootId,CreateAccountTreeRelationArg arg) {
        CommonSqlSearchTemplate searchTemplate = new CommonSqlSearchTemplate();
        searchTemplate.setTableName(EQUITY_TABLE_NAME);
        searchTemplate.setLimit(100);
        searchTemplate.setOffset(0);
        List<WhereParam> whereParamsList = getWhereParams(tenantId, rootId);
        CommonSqlUtil.addWhereParam(whereParamsList, ID, CommonSqlOperator.GT, Lists.newArrayList(""));
        CommonSqlUtil.addWhereParam(whereParamsList, OBJECT_API_NAME, CommonSqlOperator.EQ, Lists.newArrayList(arg.getDescribeApiName()));
        searchTemplate.setWhereParamList(whereParamsList);
        OrderBy orderBy = new OrderBy();
        orderBy.setFieldName(ID);
        orderBy.setIsAsc(true);
        searchTemplate.setOrderByList(Lists.newArrayList(orderBy));
        return searchTemplate;
    }

    private void processAccountMainData(String tenantId, Map<String, String> accountMainDataMap, List<String> nameList,CreateAccountTreeRelationArg arg) {
        nameList.removeIf(x -> accountMainDataMap.keySet().contains(x));
        if(EquityRelationshipDataConstants.AccountMainDataObj.equals(arg.getDescribeApiName())){
            accountMainDataMatchService.matchAccountMainData(tenantId, accountMainDataMap, nameList);
        }else{
            matchFactory.getMatchService(arg.getDescribeApiName()).matchData(tenantId, accountMainDataMap, nameList,arg);
        }

    }

    @NotNull
    private IObjectData convert2ObjectData(User user, ProcessedEquityInfo rootNode, Map<String, String> accountMainDataMap, Map<String, Object> item, CreateAccountTreeRelationArg arg) {
        RelationshipsMapping relationshipsMapping = CommonTreeRelationUtil.getRelationshipsMapping(arg.getDescribeApiName());

        String parenField =CommonTreeRelationUtil.PARENT_REF_PREFIX + relationshipsMapping.getLinkFieldApiName();
        String rootField =CommonTreeRelationUtil.ROOT_REF_PREFIX + relationshipsMapping.getLinkFieldApiName();
        IObjectData objectData = ObjectDataUtil.createBaseObjectData(user);
        objectData.setId(ObjectDataUtil.getStringValue(item, ID, ""));
        objectData.setDescribeApiName(CommonTreeRelationUtil.getRelationshipsMapping(arg.getDescribeApiName()).getLinkDescribeApiName());
        String name = ObjectDataUtil.getStringValue(item, NAME, "");
        objectData.setName(name);
        objectData.set(ROOT_ID, rootNode.getId());
        objectData.set("level", item.get("level"));
        objectData.set(PARENT_ID, item.get(PARENT_ID));
        objectData.set(ENTERPRISE_ID, item.get(ENTERPRISE_ID));
        objectData.set(relationshipsMapping.getLinkFieldApiName(), accountMainDataMap.containsKey(name) ? accountMainDataMap.get(name) : null);
        name = ObjectDataUtil.getStringValue(item, "parent_name", "");
        objectData.set(parenField, accountMainDataMap.containsKey(name) ? accountMainDataMap.get(name) : null);
        objectData.set(PARENT_ENTERPRISE_ID, item.get(PARENT_ENTERPRISE_ID));
        objectData.set(rootField, accountMainDataMap.containsKey(rootNode.getName()) ? accountMainDataMap.get(rootNode.getName()) : null);
        objectData.set(PARENT_ID, item.get(PARENT_ID));
        objectData.set(ROOT_ENTERPRISE_ID, item.get(ROOT_ENTERPRISE_ID));
        objectData.set(TREE_PATH, item.get(TREE_PATH));
        objectData.set(IS_STATISTICS_INCLUDE, item.get(IS_STATISTICS_INCLUDE));
        objectData.set(IS_ROOT, item.get(IS_ROOT));
        objectData.set("life_status", "normal");
        objectData.set("is_manual_add", false);
        objectData.set("is_auto_update", true);
        objectData.set(IS_LEAF, item.get(IS_LEAF));
        objectData.set(IS_BRANCH, item.get(IS_BRANCH));
        objectData.set(PERCENT, item.get(PERCENT));
        objectData.set(ROOT_PERCENT, item.get(ROOT_PERCENT));
        objectData.set(TOTAL_ROOT_PERCENT, item.get(TOTAL_ROOT_PERCENT));
        return objectData;
    }

    private void bulkSaveData(String tenantId, List<IObjectData> objectDataList) {
        if(CollectionUtils.empty(objectDataList)) {
            return;
        }
        User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
        if(objectDataList.size() > BACH_SIZE) {
            List<List<IObjectData>> splitList = ListsUtil.splitList(Lists.newArrayList(objectDataList), BACH_SIZE);
            splitList.forEach(listData -> serviceFacade.bulkSaveObjectData(listData, user, true, true,
                    x -> ActionContextExt.of(user, RequestContextManager.getContext()).setNotValidate(true).getContext()));
        } else {
            serviceFacade.bulkSaveObjectData(objectDataList, user, true, true,
                    x -> ActionContextExt.of(user, RequestContextManager.getContext()).setNotValidate(true).getContext());
        }
    }

    private void createEnterpriseInfoData(String tenantId, Map<String, String> enterpriseMap, Set<String> insertedEnterpriseIds) {
        List<IObjectData> objectDataList = Lists.newArrayList();
        List<String> enterpriseIds = Lists.newArrayList(enterpriseMap.keySet());
        enterpriseIds.removeIf(insertedEnterpriseIds::contains);
        if(CollectionUtils.empty(enterpriseIds)) {
            return;
        }
        String queryString = String.format("SELECT id FROM biz_enterprise WHERE tenant_id=%s AND id=%s AND is_deleted>=0 ",
                SqlEscaper.pg_quote(tenantId), SqlEscaper.any_clause(enterpriseIds));
        List<Map> queryResult = CommonSqlUtil.findBySql(tenantId, queryString);
        if(CollectionUtils.notEmpty(queryResult)) {
            queryResult.forEach(x -> insertedEnterpriseIds.add(ObjectDataUtil.getStringValue(x, ID, "")));
        }
        User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
        boolean grayQueryCommerceInfo = GrayUtil.isGrayEnable("account_tree_query_commerce_info_enable", tenantId);
        for (Map.Entry<String, String> entry : enterpriseMap.entrySet()) {
            if(insertedEnterpriseIds.contains(entry.getKey())) {
                continue;
            }
            insertedEnterpriseIds.add(entry.getKey());
            IObjectData objectData;
            try {
                if(grayQueryCommerceInfo) {
                    CompanyDetail companyDetail = queryCompanyDetailByName(user, entry.getValue());
                    if (ObjectUtils.isEmpty(companyDetail)) {
                        log.warn("createEnterpriseInfoData companyDetail is null tenantId:{}, companyName:{}", user.getTenantId(), entry.getValue());
                        objectData = createBaseEnterpriseObjectData(user, entry.getKey(), entry.getValue());
                        objectDataList.add(objectData);
                        continue;
                    }
                    objectData = equityRelationshipService.getObjectDataOfEnterpriseInfoObj(user, entry.getKey(), companyDetail);

                } else {
                    objectData = createBaseEnterpriseObjectData(user, entry.getKey(), entry.getValue());
                }
            } catch (Exception e) {
                log.warn("createEnterpriseInfoData tet companyDetail error tenantId:{}, companyName:{}",user.getTenantId(), entry.getValue());
                objectData = createBaseEnterpriseObjectData(user, entry.getKey(), entry.getValue());
            }
            objectDataList.add(objectData);
        }
        try {
            bulkSaveData(tenantId, objectDataList);
        }catch (Exception e){
            log.warn("createEnterpriseInfoData error,start Single save e:",e);
            String msg = e.getMessage();
            List<IObjectData> objectDataNews= new ArrayList<>();
            objectDataList.stream().forEach(x->{
                if(!msg.contains(x.getName())){
                    objectDataNews.add(x);
                }
            });
            bulkSaveData(tenantId, objectDataNews);
            logAsync(user, "EnterpriseInfoObj", objectDataNews, EventType.ADD, ActionType.Add);
            return;
        }
        logAsync(user, "EnterpriseInfoObj", objectDataList, EventType.ADD, ActionType.Add);
    }

    private IObjectData createBaseEnterpriseObjectData(User user, String eid, String name) {
        IObjectData objectData = ObjectDataUtil.createBaseObjectData(user);
        objectData.setId(eid);
        objectData.setName(name);
        objectData.setDescribeApiName("EnterpriseInfoObj");
        objectData.set("biz_reg_name", true);
        return objectData;
    }

    private CompanyDetail queryCompanyDetailByName(User user, String name) {
        int retryCount = 2;
        while (retryCount > 0) {
            retryCount--;
            try {
                CompanyDetail companyDetail = equityRelationshipService.queryCompanyDetailByName(user, name);
                if(ObjectUtils.isNotEmpty(companyDetail)) {
                    return companyDetail;
                }
            } catch (Exception e) {
                log.warn("getCompanyDetail error tenantId:{}, companyName:{}",user.getTenantId(), name);
            }
            safeThreadSleep(200 * (2 - retryCount));
        }
        return null;
    }

    private void logAsync(User user, String objectApiName, List<IObjectData> dataList, EventType eventType, ActionType actionType) {
        List<IObjectData> copyList = ObjectDataExt.copyList(dataList);
        IObjectDescribe describe = serviceFacade.findObject(user.getTenantId(), objectApiName);
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        describeMap.put(objectApiName, describe);
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createBackgroundTask();
        parallelTask.submit(() -> this.serviceFacade.masterDetailLog(user, eventType, actionType, describeMap, copyList)).run();
    }

    //step10 处理股权占比相等的情况并发送CRM通知
    private void processConflictEquityData(User user, String rootId, Map<String, List<ConflictDataInfo>> conflictDataMap, CreateAccountTreeRelationArg arg) {
        if(CollectionUtils.empty(conflictDataMap)) {
            return;
        }
        for(Map.Entry<String, List<ConflictDataInfo>> entry : conflictDataMap.entrySet()) {
            List<ConflictDataInfo> conflictDataInfoList = entry.getValue();
            if(CollectionUtils.empty(conflictDataInfoList)) {
                continue;
            }
            if(EquityRelationshipDataConstants.AccountMainDataObj.equals(arg.getDescribeApiName())){
                conflictDataInfoList.forEach(data -> accountTreeRelationLogService.logAccountTreeRelation(user, rootId, data));
            }else{
                conflictDataInfoList.forEach(data -> commonTreeRelationLogService.logTreeRelation(user, rootId, data,arg));
            }

        }
    }

    //step11 发送客户树创建成功CRM通知
    private void sendCRMNotification(User user, String rootId, CreateAccountTreeRelationArg arg) throws Exception {
        try {
            RelationshipsMapping relationshipsMapping = CommonTreeRelationUtil.getRelationshipsMapping(arg.getDescribeApiName());
            String tenantId = user.getTenantId();
            String queryString = String.format("SELECT name, %s FROM %s " +
                            "WHERE tenant_id=%s AND root_id=%s AND is_deleted=0 AND is_root='t' ",
                    SqlEscaper.pg_escape(relationshipsMapping.getLinkFieldApiName()),SqlEscaper.pg_escape(relationshipsMapping.getStoreTableName()),SqlEscaper.pg_quote(tenantId), SqlEscaper.pg_quote(rootId));
            List<Map> queryResult = CommonSqlUtil.findBySql(tenantId, queryString);
            if(CollectionUtils.empty(queryResult)) {
                return;
            }
            Map<String, Object> dataItem = queryResult.get(0);
            String apiName = arg.getDescribeApiName();
            String accountMainDataId = ObjectDataUtil.getStringValue(dataItem, relationshipsMapping.getLinkFieldApiName(), "");
            String companyName = ObjectDataUtil.getStringValue(dataItem, NAME, "");
            String title = I18N.text(SFA_ACCOUNT_TREE_RELATION_PROCESS_SUCCESS);
            String content = String.format(I18N.text(SFA_ACCOUNT_TREE_RELATION_PROCESS_SUCCESS_CONTENT), companyName);
            CRMNotification crmNotification = CRMNotification.builder()
                    .sender(user.getUserId())
                    .remindRecordType(92)
                    .title(title)
                    .content(content)
                    .dataId(accountMainDataId)
                    .content2Id(user.getUserId())
                    .receiverIds(Sets.newHashSet(user.getUserIdInt()))
                    .objectApiName(apiName)
                    .build();
            crmNotificationService.sendCRMNotification(user, crmNotification);
            //发送新crm通知
            CRMRecordUtil.sendNewCRMRecordRemindSender(crmNotificationService, user, 92, Lists.newArrayList(user.getUserIdInt()),
                    user.getUserId(), title, content, SFA_ACCOUNT_TREE_RELATION_PROCESS_SUCCESS, Lists.newArrayList(),
                    SFA_ACCOUNT_TREE_RELATION_PROCESS_SUCCESS_CONTENT, Lists.newArrayList(companyName), getUrlParameter(apiName, accountMainDataId), "CRM");
        } catch (Exception e) {
            log.warn("sendCRMNotification error", e);
            throw e;
        }
    }

    private Map<String, String> getUrlParameter(String objectApiName, String objectId) {
        Map<String, String> result = Maps.newHashMap();
        if(StringUtils.isBlank(objectApiName) || StringUtils.isBlank(objectId)) {
            return result;
        }
        result.put("objectApiName", objectApiName);
        result.put("objectId", objectId);
        return result;
    }

    //step12 清理临时表数据，第一期先保留便于跟踪问题
    private void clearTempData(String tenantId, String rootId, CreateAccountTreeRelationArg arg) {
        try {
            CommonSqlSearchTemplate searchTemplate = getSearchTemplateOrderById(tenantId, rootId,arg);
            searchTemplate.setLimit(1000);
            List<WhereParam> whereParamsList = searchTemplate.getWhereParamList();

            while (true) {
                List<Map> result = CommonSqlUtil.queryData(searchTemplate, tenantId);
                if(CollectionUtils.empty(result)) {
                    return;
                }
                String lastDataId = ObjectDataUtil.getStringValue(result.get(result.size() - 1), ID, "");
                whereParamsList.removeIf(x -> ID.equals(x.getColumn()));
                CommonSqlUtil.addWhereParam(whereParamsList, ID, CommonSqlOperator.GT, Lists.newArrayList(lastDataId));
                List<String> dataIds = getIds(result, ID);
                List<WhereParam> whereParamList = getWhereParams(tenantId, rootId);
                CommonSqlUtil.addWhereParam(whereParamList, ID, CommonSqlOperator.IN, Lists.newArrayList(dataIds));
                CommonSqlUtil.deleteData(tenantId, EQUITY_TABLE_NAME, whereParamList);
            }
        } catch (Exception e) {
            log.warn("clearTempData error", e);
        }
    }
    public void replaceRootNode(User user, ProcessedEquityInfo rootNode, CreateAccountTreeRelationArg arg)throws Exception{
        String queryString = String.format("SELECT id,name FROM biz_enterprise WHERE tenant_id=%s AND name = %s AND is_deleted>=0 ",
                SqlEscaper.pg_quote(user.getTenantId()), SqlEscaper.pg_quote(rootNode.getName()));
        List<Map> queryResult = CommonSqlUtil.findBySql(user.getTenantId(), queryString);
        if(CollectionUtils.notEmpty(queryResult)){
            rootNode.setEid(queryResult.get(0).get("id").toString());
        }
    }

    public void replaceEnterpriseInfoData(User user, ProcessedEquityInfo rootNode, CreateAccountTreeRelationArg arg)throws Exception{
        try {
            String tenantId = user.getTenantId();
            String rootId = rootNode.getId();
            CommonSqlSearchTemplate searchTemplate = getSearchTemplateOrderById(tenantId, rootId,arg);
            List<WhereParam> whereParamsList = searchTemplate.getWhereParamList();

            AccountTreeRelationModels.EnterpriseMatch enterpriseMatch = enterpriseInfoMatchService.getMatchRule(user);

            int index1=0;
            while (true) {
                List<Map> result = CommonSqlUtil.queryData(searchTemplate, tenantId);
                if(CollectionUtils.empty(result)) {
                    return;
                }
                String lastDataId = ObjectDataUtil.getStringValue(result.get(result.size() - 1), ID, "");
                whereParamsList.removeIf(x -> ID.equals(x.getColumn()));
                CommonSqlUtil.addWhereParam(whereParamsList, ID, CommonSqlOperator.GT, Lists.newArrayList(lastDataId));

                //查询是否有匹配企业库的规则，如果有走匹配规则，没有则走name匹配
                if(ObjectUtils.isNotEmpty(enterpriseMatch)){
                    enterpriseInfoMatchService.handleEnterpriseByMatchRule(user,enterpriseMatch,result,rootId,arg);
                }else{
                    enterpriseInfoMatchService.handleEnterpriseByName(user,result,rootId,arg);
                }
                index1 ++;
                if(index1>100000){
                    log.error("replaceEnterpriseInfoData index1 强硬跳出循环");
                    return;
                }
                safeThreadSleep(CommonTreeRelationUtil.CREATE_ACCOUNT_TREE_SLEEP_TIME);
            }
        } catch (Exception e) {
            log.error("replaceEnterpriseInfoData error", e);
        }
    }
}
