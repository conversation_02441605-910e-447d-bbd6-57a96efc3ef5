package com.facishare.crm.sfa.lto.todo.enums;

import java.util.Objects;
import java.util.Optional;

public enum LtoLogisticsStatusEnum {
    //待发货
    TOBESHIPPED("1", "待发货"),
    //部分发货
    PARTIALSHIPMENT("2", "部分发货"),
    //已发货
    SHIPPED("3", "已发货"),
    //部分收货
    PARTIALCOLLECTION("4", "部分收货"),
    //已收货
    RECEIVEDGOODS("5", "已收货");

    private final String status;
    private final String label;

    LtoLogisticsStatusEnum(String status, String label) {
        this.status = status;
        this.label = label;
    }

    public static Optional<LtoLogisticsStatusEnum> get(String status) {
        for (LtoLogisticsStatusEnum statusEnum : LtoLogisticsStatusEnum.values()) {
            if (Objects.equals(statusEnum.status, status)) {
                return Optional.of(statusEnum);
            }
        }
        return Optional.empty();
    }

    public String getStatus() {
        return status;
    }

    public String getLabel() {
        return label;
    }
}