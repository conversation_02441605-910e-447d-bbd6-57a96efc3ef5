package com.facishare.crm.sfa.lto.loyalty.service.memberOperate;

import com.facishare.crm.sfa.lto.loyalty.constants.LoyaltyConstants;
import com.facishare.crm.sfa.lto.loyalty.model.Loyalty;
import com.facishare.crm.sfa.lto.loyalty.service.LoyaltyPointsDetailService;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TransferConsumerPointsService extends AbstractLoyaltyTransferPointsService {
    @Override
    public Loyalty.PointsOperationParam.Type type() {
        return Loyalty.PointsOperationParam.Type.TRANSFER_CONSUMER_POINTS;
    }

    @Override
    List<IFilter> findMemberPointsDetailsFilter(Loyalty.PointsOperationParam param) {
        List<IFilter> filterList = Lists.newArrayList();
        IFilter iFilter = SearchUtil.filter(LoyaltyConstants.LoyaltyPointsDetail.IS_QUALIFYING, Operator.EQ, false);
        iFilter.setIsMasterField(true);
        filterList.add(iFilter);
        filterList.addAll(LoyaltyPointsDetailService.buildFilterByPointsStatus(LoyaltyConstants.LoyaltyPointsDetail.PointsStatus.Available, param.getOperatingTime()));
        return filterList;
    }

    @Override
    String counterpointType() {
        return "ADD_CONSUMER_POINTS_BY_MEMBER";
    }
}
