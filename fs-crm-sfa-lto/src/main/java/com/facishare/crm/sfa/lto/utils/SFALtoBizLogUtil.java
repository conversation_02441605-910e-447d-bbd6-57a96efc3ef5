package com.facishare.crm.sfa.lto.utils;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.User;
import com.fxiaoke.log.AuditLog;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.dto.AuditLogDTO;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.github.autoconf.helper.ConfigHelper;
import com.github.trace.TraceContext;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class SFALtoBizLogUtil {
    private static String appName = ConfigHelper.getProcessInfo().getName();
    private static String serverIp = ConfigHelper.getProcessInfo().getIp();
    private static String profile = ConfigHelper.getProcessInfo().getProfile();
    private static String auditTopic = "biz-audit-log";


    public static void sendAuditLog(Arg arg, RequestContext context) {
        sendLog(arg, context, auditTopic);
    }

    private static void sendLog(Arg arg, RequestContext context, String topic) {
        AuditLogDTO dto = AuditLogDTO.builder()
                .createTime(arg.createTime == null ? System.currentTimeMillis() : arg.createTime)
                .cost(arg.cost == null ? 0L : arg.cost)
                .num(arg.num == null ? 0 : arg.num)
                .appName(appName)
                .traceId(TraceContext.get().getTraceId())
                .tenantId(context.getTenantId())
                .userId(context.getUser().getUpstreamOwnerIdOrUserId())
                .action(arg.action)
                .status(arg.status)
                .objectApiNames(StringUtils.isBlank(arg.objectApiNames) ? "sfa" : arg.objectApiNames)
                .objectIds(arg.objectIds)
                .message(arg.message)
                .error(arg.error)
                .extra(arg.extra)
                .profile(profile)
                .serverIp(serverIp)
                .eventId(context.getEventId())
                .parameters(arg.parameters)
                .build();
        BizLogClient.send(topic, Pojo2Protobuf.toMessage(dto, AuditLog.class).toByteArray());
    }


    public static void sendAuditLog(Arg arg, User user) {
        sendLog(arg, user, auditTopic);
    }

    private static void sendLog(Arg arg, User user, String topic) {
        AuditLogDTO dto = AuditLogDTO.builder()
                .createTime(arg.createTime == null ? System.currentTimeMillis() : arg.createTime)
                .cost(arg.cost == null ? 0L : arg.cost)
                .num(arg.num == null ? 0 : arg.num)
                .appName(appName)
                .traceId(TraceContext.get().getTraceId())
                .tenantId(user.getTenantId())
                .userId(user.getUpstreamOwnerIdOrUserId())
                .action(arg.action)
                .status(arg.status)
                .objectApiNames(StringUtils.isBlank(arg.objectApiNames) ? "sfa" : arg.objectApiNames)
                .objectIds(arg.objectIds)
                .message(arg.message)
                .error(arg.error)
                .extra(arg.extra)
                .profile(profile)
                .serverIp(serverIp)
                .parameters(arg.parameters)
                .build();
        BizLogClient.send(topic, Pojo2Protobuf.toMessage(dto, AuditLog.class).toByteArray());
    }


    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Arg {
        private Long createTime;
        private Long cost;
        private Integer num;
        private String action;
        private String status;
        private String objectApiNames;
        private String objectIds;
        private String message;
        private String error;
        private String extra;
        private String parameters;
    }


    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Args {
        private List<Arg> data;
    }

}