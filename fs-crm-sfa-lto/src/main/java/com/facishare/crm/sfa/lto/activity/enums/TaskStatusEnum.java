package com.facishare.crm.sfa.lto.activity.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> gongchunru
 * @date : 2025/1/2 11:54
 * @description:
 */
@Getter
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum TaskStatusEnum {

    /** 任务进行中 */
    ONGOING("ONGOING", 1),
    /** 任务完成 */
    COMPLETED("COMPLETED", 2),
    /** 任务失败 */
    FAILED("FAILED", -1),
    /** 无效任务 */
    INVALID("INVALID", 0),
    /** 扩展状态 */
    EXPAND("EXPAND", 3);

    private final String value;
    private final Integer num;

    /**
     * 根据给定的字符串值查找对应的枚举实例。
     * 如果没有找到匹配项，则返回null而不是抛出异常。
     *
     * @param value 要匹配的字符串值，不区分大小写
     * @return 对应的枚举实例或null
     */
    public static TaskStatusEnum fromValue(String value) {
        try {
            return value != null ? TaskStatusEnum.valueOf(value.toUpperCase()) : null;
        } catch (IllegalArgumentException e) {
            return null;
        }
    }

}
