package com.facishare.crm.sfa.prm.platform.model;

import lombok.Data;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-13
 * ============================================================
 */
@Data
public class ApiResponse<T> {
    private int code;          // 状态码
    private String message;    // 描述信息
    private T data;            // 返回的数据内容

    public ApiResponse() {
    }

    public ApiResponse(int code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    // 成功的静态方法
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(200, "Success", data);
    }

    public static <T> ApiResponse<T> success(T data, String message) {
        return new ApiResponse<>(200, message, data);
    }

    // 失败的静态方法
    public static <T> ApiResponse<T> error(int code, String message) {
        return new ApiResponse<>(code, message, null);
    }
}
