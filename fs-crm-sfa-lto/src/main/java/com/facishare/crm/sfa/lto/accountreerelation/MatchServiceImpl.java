package com.facishare.crm.sfa.lto.accountreerelation;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.facishare.crm.sfa.lto.accountreerelation.models.AccountTreeRelationModels.*;
import com.facishare.crm.sfa.lto.utils.CommonTreeRelationUtil;
import com.facishare.crm.sfa.lto.utils.ObjectDataUtil;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.facishare.crm.sfa.lto.accountreerelation.models.AccountTreeRelationConstants.MATCH_RULE_CONFIG_KEY;

/**
 * <AUTHOR> lik
 * @date : 2024/2/26 16:48
 */
@Component
@Slf4j
public class MatchServiceImpl implements IMatchService{
    @Autowired
    private ConfigService configService;
    @Override
    public void matchData(String tenantId, Map<String, String> accountMainDataMap, List<String> nameList, CreateAccountTreeRelationArg arg) {
        if(CollectionUtils.empty(nameList)) {
            return;
        }
        User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
        String configValue = configService.findTenantConfig(user, getMatchRuleName(arg.getDescribeApiName()));
        List<Wheres> wheresList = CommonTreeRelationUtil.convert2WheresList(configValue);
        List<IObjectData> dataList = queryData(tenantId,nameList, wheresList,arg);
        if(CollectionUtils.notEmpty(dataList)) {
            nameList.forEach(name -> {
                Optional<IObjectData> opData = dataList.stream().filter(x -> name.equals(x.getName())).findFirst();
                if(opData.isPresent()) {
                    accountMainDataMap.put(name, opData.get().getId());
                }
            });
        }
    }
    @Override
    public List<IObjectData> getMatchedData(String tenantId, List<String> objectIds,String describeApiName) {
        User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
        String configValue = configService.findTenantConfig(user, getMatchRuleName(describeApiName));
        List<Wheres> wheresList = CommonTreeRelationUtil.convert2WheresList(configValue);
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setDataRightsParameter(null);
        searchTemplateQuery.setPermissionType(0);
        searchTemplateQuery.setNeedReturnCountNum(false);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, DBRecord.ID, objectIds);
        searchTemplateQuery.setFilters(filters);
        List<Wheres> tempWhereList = Lists.newArrayList(wheresList);
        searchTemplateQuery.setWheres(tempWhereList);
        QueryResult<IObjectData> queryResult = ObjectDataUtil.findDataBySearchQuery(new User(tenantId, User.SUPPER_ADMIN_USER_ID), describeApiName, searchTemplateQuery, null);
        return queryResult == null ? Lists.newArrayList() : queryResult.getData();
    }

    private List<IObjectData> queryData(String tenantId, List<String> nameList, List<Wheres> wheresList,CreateAccountTreeRelationArg arg) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setDataRightsParameter(null);
        searchTemplateQuery.setPermissionType(0);
        searchTemplateQuery.setNeedReturnCountNum(false);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, "name", nameList);
        searchTemplateQuery.setFilters(filters);
        List<Wheres> tempWhereList = Lists.newArrayList(wheresList);
        searchTemplateQuery.setWheres(tempWhereList);
        QueryResult<IObjectData> queryResult = ObjectDataUtil.findDataBySearchQuery(new User(tenantId, User.SUPPER_ADMIN_USER_ID), arg.getDescribeApiName(), searchTemplateQuery, null);
        return queryResult == null ? Lists.newArrayList() : queryResult.getData();
    }

    //获取规则名称
    @Override
    public String getMatchRuleName(String objectApiName){
        return CommonTreeRelationUtil.GENERATE_RELATION_TREE_MATCH_RULE+"_"+objectApiName;
    }

}
