package com.facishare.crm.sfa.lto.qywx.proxy.models;

import lombok.Data;

import java.util.List;

@Data
public class QywxEmployeeInfo {

	//成员UserID。对应管理端的帐号
	private String userId;
	//成员名称
	private String name;
	//成员所属部门id列表
	private List<String> department;
	//部门内的排序值，默认为0。数量必须和department一致，数值越大排序越前面。值范围是[0, 2^32)
	private List<String> order;
	//性别。0表示未定义，1表示男性，2表示女性
	private String gender;

	//成员启用状态。1表示启用的成员，0表示被禁用。服务商调用接口不会返回此字段
	private Integer enable;
	//激活状态: 1=已激活，2=已禁用，4=未激活 已激活代表已激活企业微信或已关注微工作台（原企业号）。未激活代表既未激活企业微信又未关注微工作台（原企业号）。
	private Integer status;

	private Integer userType;//	登录用户的类型：1.创建者 2.内部系统管理员 3.外部系统管理员 4.分级管理员 5.成员

	//登录用户会返回下面的参数
	private String corpId;  //企业id
	private String appId; //当前应用id

	private String fsAccount; //纷享账号



	//第三方应用是通讯录类型 才可获取的字段

	//手机号码，第三方仅通讯录套件可获取
	private String mobile;

	//职位信息；第三方仅通讯录应用可获取
	private String position;

	//邮箱，第三方仅通讯录应用可获取
	private String email;

	//座机。第三方仅通讯录应用可获取
	private String telephone;
	/**
	 * 表示在所在的部门内是否为上级；第三方仅通讯录应用可获取.
	 * 和department一一对应。0：不是leader，1：是leader。
	 */
	private List<Integer> isLeaderInDept;

	//英文名；第三方仅通讯录应用可获取
	private String englishName;

	//头像url。注：如果要获取小图将url最后的”/0”改成”/100”即可。第三方仅通讯录应用可获取
	private String avatar;

	//TODO extattr	扩展属性，第三方仅通讯录套件可获取（忽略）
	//员工个人二维码，扫描可添加为外部联系人；第三方仅通讯录应用可获取
	private String qrCode;
	//成员对外属性，字段详情见对外属性；第三方仅通讯录应用可获取
	private String externalProfile;

	private String mainDepartment;
}
