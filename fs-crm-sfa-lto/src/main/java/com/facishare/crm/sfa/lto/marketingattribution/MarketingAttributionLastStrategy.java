package com.facishare.crm.sfa.lto.marketingattribution;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;


@Slf4j
@Component
public class MarketingAttributionLastStrategy extends BaseMarketingAttributionStrategy{
    @Override
    public String getStrategyModel() {
        return "last_strategy";
    }

    @Override
    public void process(RequestContext context, IObjectData triggerData, IObjectData strategyRule, List<IObjectData> attributionDataList) {
        deleteMarketingEventInfluenceData(context.getTenantId(), triggerData.getDescribeApiName(), triggerData.getId());
        List<IObjectData> stageDataList = getLastStageDataList(attributionDataList, false);
        List<Double> stagePercentList = calculateRatio(100.00, stageDataList.size());
        createMarketingEventInfluence(context, triggerData, strategyRule, stageDataList, stagePercentList);
    }
}
