package com.facishare.crm.sfa.lto.operations.task;

import com.facishare.crm.sfa.lto.common.LtoOrgCommonService;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.expression.ExpressionService;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.dao.pg.entity.metadata.RelevantTeam;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.task.api.model.arg.CreateTaskArg;
import com.fxiaoke.functions.enums.TeamMemberEnum;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.lto.operations.OperationsConstant.*;

public class OperationsTaskUtil {
    private final ServiceFacade serviceFacade;
    private final ExpressionService expressionService;
    private final LtoOrgCommonService ltoOrgCommonService;

    public OperationsTaskUtil(ServiceFacade serviceFacade, ExpressionService expressionService, LtoOrgCommonService ltoOrgCommonService) {
        this.serviceFacade = serviceFacade;
        this.expressionService = expressionService;
        this.ltoOrgCommonService = ltoOrgCommonService;
    }

    @SuppressWarnings("unchecked")
    public List<OperationsTask> createTaskByTemplate(IObjectData template, List<IObjectData> bizDataList) {
        if (bizDataList.isEmpty()) {
            return new ArrayList<>();
        }
        String tenantId = template.getTenantId();
        IObjectDescribe bizDescribe = serviceFacade.findObject(tenantId, bizDataList.get(0).getDescribeApiName());
        boolean settedContext = false;
        User user = User.systemUser(tenantId);
        if (RequestContextManager.getContext() == null) {
            RequestContextManager.setContext(RequestContext.builder().tenantId(tenantId).user(user).build());
            settedContext = true;
        }
        serviceFacade.fillExtendFieldInfo(bizDescribe, bizDataList, user);
        List<TemplateVariableField> variableFields = new ArrayList<>();
        variableFields.add(new Simple(template.get(TASK_NAME, String.class), OperationsTask::setName));
        variableFields.add(new Simple(template.get(TASK_DESCRIPTION, String.class), OperationsTask::setDescription));
        variableFields.add(new DateTime(template.get(TASK_DEAD_LINE, String.class), OperationsTask::setDeadline, expressionService));
        List<OperationsTask> taskList = new ArrayList<>();
        List<String> reminds = (List<String>) template.get(TASK_REMINDS);
        for (IObjectData bizData : bizDataList) {
            OperationsTask task = new OperationsTask();
            task.setData(bizData);
            task.setCreatedBy(template.getCreatedBy());
            task.setReminds(reminds);
            taskList.add(task);
        }
        replaceVariable(bizDescribe, variableFields, taskList); // 替换变量
        replaceMember((Map<String, List<String>>) template.get(TASK_CC), taskList, OperationsTask::addCC); // 替换人员
        replaceMember((Map<String, List<String>>) template.get(TASK_EXECUTORS), taskList, OperationsTask::addExecutors); // 替换人员
        if (settedContext) {
            RequestContextManager.removeContext();
        }
        return taskList;
    }

    public List<CreateTaskArg> createQXTaskByTemplate(IObjectData template, List<IObjectData> bizDataList) {
        return convertToQXTask(createTaskByTemplate(template, bizDataList));
    }

    private List<CreateTaskArg> convertToQXTask(List<OperationsTask> taskList) {
        return taskList.stream().map(OperationsTaskUtil::convert).collect(Collectors.toCollection(() -> new ArrayList<>(taskList.size())));
    }

    // https://wiki.firstshare.cn/pages/viewpage.action?pageId=85624061
    public static CreateTaskArg convert(OperationsTask task) {
        IObjectData data = task.getData();
        CreateTaskArg createTaskArg = new CreateTaskArg();
        createTaskArg.setTenantId(Integer.parseInt(data.getTenantId()));
        createTaskArg.setObjectId(data.getId());
        createTaskArg.setObjectName(data.getName());
        createTaskArg.setObjectApiName(data.getDescribeApiName());
        createTaskArg.setCreatorId(Integer.parseInt(task.getCreatedBy()));
        createTaskArg.setTitle(task.getName());
        createTaskArg.setDescription(task.getDescription());
        List<Integer> executors = task.getExecutors().stream().map(Integer::valueOf).collect(Collectors.toList());
        createTaskArg.setAssigneeIds(executors);
        createTaskArg.setMultiExecute(executors.size() == 1); // 一个执行人用true 多个用false
        createTaskArg.setDeadLine(task.getDeadline());
        createTaskArg.setCcEmployeeIds(task.getCc().stream().map(Integer::valueOf).collect(Collectors.toList()));
        createTaskArg.setReminds(task.getReminds().stream().map(Integer::valueOf).collect(Collectors.toList()));
        return createTaskArg;
    }

    private void replaceVariable(IObjectDescribe bizDescribe, List<TemplateVariableField> variableFields, List<OperationsTask> taskList) {
        Map<String, Lookup> lookupMap = new HashMap<>();
        Map<String, Value> lookupValueMap = new HashMap<>();
        for (OperationsTask task : taskList) {
            for (TemplateVariableField field : variableFields) {
                if (field.hasVariable()) {
                    findLookupVariable(bizDescribe, task.getData(), field.variable(), lookupMap);
                }
            }
        }
        if (!lookupMap.isEmpty()) {
            queryLookupValue(bizDescribe, lookupMap, lookupValueMap);
            lookupMap.clear();
        }
        for (OperationsTask task : taskList) {
            for (TemplateVariableField field : variableFields) {
                if (field.hasVariable()) {
                    Map<String, Value> valueMap = convertValueMap(field.variable(), bizDescribe, task.getData(), lookupValueMap);
                    field.setValue(task, field.replace(valueMap));
                } else {
                    field.setValue(task, field.raw());
                }
            }
        }
    }

    //=== 解析变量 ====
    private void queryLookupValue(IObjectDescribe bizDataDescribe, Map<String, Lookup> lookupQueryMap, Map<String, Value> lookupValueMap) {
        String tenantId = bizDataDescribe.getTenantId();
        Map<String, IObjectDescribe> describeMap = new HashMap<>();
        User user = User.systemUser(tenantId);
        for (Map.Entry<String, Lookup> entry : lookupQueryMap.entrySet()) {
            Lookup lookup = entry.getValue();
            IObjectDescribe lookupDescribe = describeMap.computeIfAbsent(lookup.objectApiName, k -> serviceFacade.findObject(tenantId, lookup.objectApiName));
            List<IObjectData> lookupDataList = findExtendInfoByIdWithFields(user, lookupDescribe, new ArrayList<>(lookup.ids), new ArrayList<>(lookup.fields));
            for (IObjectData lookupData : lookupDataList) {
                for (String field : lookup.fields) {
                    lookupValueMap.put(lookupKey(lookup.objectApiName, field, lookupData.getId()), new Value(lookupDescribe.getFieldDescribe(field), lookupData));
                }
            }
        }
    }

    /**
     * "#getLookupValueKey()":{Value} -> "LeadsObj.lead_id":{Value}
     */
    private static Map<String, Value> convertValueMap(Set<String> variables, IObjectDescribe bizDataDescribe, IObjectData bizData, Map<String, Value> lookupValueMap) {
        Map<String, Value> values = new HashMap<>();
        for (String variable : variables) {
            List<String> variableParts = TemplateVariableField.spiltWithDot(variable);
            if (variableParts.size() == 2) {
                String fieldApiName = variableParts.get(1);
                values.put(variable, new Value(bizDataDescribe.getFieldDescribe(fieldApiName), bizData));
            }
            if (variableParts.size() == 3) {
                String refField = variableParts.get(1);
                String fieldApiName = variableParts.get(2);
                String refId = bizData.get(refField, String.class);
                String refObject = FieldDescribeExt.of(bizDataDescribe.getFieldDescribe(refField)).getRefObjTargetApiName();
                values.put(variable, lookupValueMap.get(lookupKey(refObject, fieldApiName, refId)));
            }
        }
        return values;
    }

    private static String lookupKey(String objectApiName, String fieldApiName, String id) {
        return objectApiName + fieldApiName + id;
    }

    private static void findLookupVariable(IObjectDescribe bizDataDescribe, IObjectData bizData, Set<String> variables, Map<String, Lookup> lookupQueryMap) {
        String tenantId = bizData.getTenantId();
        for (String variable : variables) {
            List<String> variableParts = TemplateVariableField.spiltWithDot(variable);
            if (variableParts.size() == 3) {
                String refField = variableParts.get(1);
                String refId = bizData.get(refField, String.class);
                if (refId != null) {
                    String fieldName = variableParts.get(2);
                    String refObject = FieldDescribeExt.of(bizDataDescribe.getFieldDescribe(refField)).getRefObjTargetApiName();
                    Lookup lookup = lookupQueryMap.computeIfAbsent(refObject, key -> new Lookup(tenantId, key));
                    lookup.addField(fieldName);
                    lookup.addId(refId);
                }
            }
        }
    }

    private List<IObjectData> findExtendInfoByIdWithFields(User user, IObjectDescribe describe, List<String> ids, List<String> fields) {
        if (ids.isEmpty()) {
            return Collections.emptyList();
        }
        String describeApiName = describe.getApiName();
        IActionContext context = getiActionContext(user);
        List<IFilter> filters = new ArrayList<>();
        SearchUtil.fillFilterIn(filters, DBRecord.ID, ids);
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setPermissionType(0);
        query.setNeedReturnCountNum(Boolean.FALSE);
        query.setFilters(filters);
        query.setLimit(AppFrameworkConfig.getMaxQueryLimit());
        List<IObjectData> res = serviceFacade.findBySearchTemplateQueryWithFields(context, describeApiName, query, fields).getData();
        serviceFacade.fillExtendFieldInfo(describe, res, user);
        return res;
    }

    private static IActionContext getiActionContext(User user) {
        return ActionContextExt.of(user).skipRelevantTeam().disableDeepQuote().getContext();
    }

    static class Lookup {
        String tenantId;
        String objectApiName;
        Set<String> fields = new HashSet<>();
        Set<String> ids = new HashSet<>();

        Lookup(String tenantId, String objectApiName) {
            this.tenantId = tenantId;
            this.objectApiName = objectApiName;
        }

        void addId(String id) {
            ids.add(id);
        }

        void addField(String field) {
            fields.add(field);
        }
    }

    //=== 解析人 ====
    private void replaceMember(Map<String, List<String>> map, List<OperationsTask> tasks, BiConsumer<OperationsTask, List<String>> setter) {
        if (map == null) {
            return;
        }
        Map<String, TemplateMemberField> memberFields = new HashMap<>();
        for (Map.Entry<String, List<String>> entry : map.entrySet()) {
            MemberType.fromKey(entry.getKey()).ifPresent(type -> initMemberFields(tasks, setter, entry.getValue(), type, memberFields));
        }
        for (Map.Entry<String, TemplateMemberField> entry : memberFields.entrySet()) {
            for (OperationsTask task : tasks) {
                entry.getValue().setValue(task);
            }
        }
    }

    private void initMemberFields(List<OperationsTask> tasks, BiConsumer<OperationsTask, List<String>> setter, List<String> value, MemberType memberType, Map<String, TemplateMemberField> memberFields) {
        if (memberType == MemberType.USER) {
            memberFields.putIfAbsent(memberType.toString(), new Employee(value, setter));
        }
        if (memberType == MemberType.EXTRA) {
            for (String extra : value) {
                MemberTypeExtra.from(extra).ifPresent(typeExtra -> initMemberFields(tasks, setter, typeExtra, memberFields));
            }
        }
    }

    private void initMemberFields(List<OperationsTask> tasks, BiConsumer<OperationsTask, List<String>> setter, MemberTypeExtra memberTypeExtra, Map<String, TemplateMemberField> memberFields) {
        switch (memberTypeExtra) {
            case OWNER:
                memberFields.putIfAbsent(memberTypeExtra.toString(), new Owner(setter));
                break;
            case DATA_GROUP:
                memberFields.putIfAbsent(memberTypeExtra.toString(), new Team(setter, queryTeam(tasks)));
                break;
            case DATA_OWNER_LEADER:
                memberFields.putIfAbsent(memberTypeExtra.toString(), new Leader(setter, queryLeader(tasks, serviceFacade)));
                break;
        }
    }

    private Map<String, List<String>> queryTeam(List<OperationsTask> tasks) {
        IObjectData data = tasks.get(0).getData();
        String tenantId = data.getTenantId();
        String describeApiName = data.getDescribeApiName();
        List<String> ids = tasks.stream().map(OperationsTask::getData).map(IObjectData::getId).collect(Collectors.toList());
        Map<String, List<String>> map = new HashMap<>();
        map.put(describeApiName, ids);
        Map<String, Map<String, List<RelevantTeam>>> result = serviceFacade.batchFindTeamMember(tenantId, map);
        Map<String, List<RelevantTeam>> result0 = result.get(describeApiName);
        Map<String, List<String>> res = new HashMap<>();
        for (Map.Entry<String, List<RelevantTeam>> entry : result0.entrySet()) {
            List<String> list = new ArrayList<>();
            for (RelevantTeam team : entry.getValue()) {
                list.addAll(convert(team));
            }
            res.put(entry.getKey(), list);
        }
        return res;
    }

    private List<String> convert(RelevantTeam team) {
        TeamMemberEnum.MemberType type = TeamMemberEnum.MemberType.of(team.getMemberType());
        String tenantId = team.getTenantId();
        String memberId = team.getMemberId();
        switch (type) {
            case EMPLOYEE:
                return Collections.singletonList(memberId);
            case GROUP:
                return ltoOrgCommonService.getMembersByUserGroupIds(tenantId, Collections.singletonList(memberId), true).get(memberId);
            case DEPARTMENT:
                return ltoOrgCommonService.getMembersByDeptIds(tenantId, Collections.singletonList(memberId), true);
            case ROLE:
                return ltoOrgCommonService.getRoleUsersByRoleIds(tenantId, Collections.singletonList(memberId), true).get(memberId);
            default:
                return Collections.emptyList();
        }
    }

    private static Map<String, String> queryLeader(List<OperationsTask> tasks, ServiceFacade serviceFacade) {
        Map<String, String> res = new HashMap<>();
        Set<String> ownerIds = tasks.stream().map(OperationsTask::getData).flatMap(data -> data.getOwner().stream()).collect(Collectors.toSet());
        if (!ownerIds.isEmpty()) {
            IObjectData data0 = tasks.get(0).getData();
            List<IObjectData> employeeList = serviceFacade.findEmployeeInfoByUserIds(data0.getTenantId(), new ArrayList<>(ownerIds));
            for (IObjectData employee : employeeList) {
                List<String> leader = ObjectDataExt.of(employee).getEmployeeValues("leader");
                if (!leader.isEmpty()) {
                    res.putIfAbsent(employee.get("user_id", String.class), leader.get(0));
                }
            }
        }
        return res;
    }
}
