package com.facishare.crm.sfa.lto.loyalty.service.memberOperate;

import com.facishare.crm.sfa.lto.loyalty.constants.LoyaltyConstants;
import com.facishare.crm.sfa.lto.loyalty.i18n.LoyaltyI18nException;
import com.facishare.crm.sfa.lto.loyalty.model.Loyalty;
import com.facishare.crm.sfa.lto.loyalty.service.LoyaltyMemberService;
import com.facishare.crm.sfa.lto.loyalty.service.LoyaltyPointsDetailService;
import com.facishare.crm.sfa.lto.loyalty.utils.LoyaltyI18nKey;
import com.facishare.crm.sfa.lto.loyalty.utils.NumberUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.common.StopWatch;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

@Service
public class LoyaltyConsumerPointsToPoolService extends AbstractLoyaltyPointsOperateService {

    @Resource
    LoyaltyMemberService loyaltyMemberService;

    @Override
    public Loyalty.PointsOperationParam.Type type() {
        return Loyalty.PointsOperationParam.Type.CONSUMER_POINTS_TO_POOL;
    }

    @Transactional
    @Override
    public void action(Loyalty.PointsOperationParam param) {
        String tenantId = param.getTenantId();
        String memberId = param.getMemberId();
        StopWatch stopWatch = StopWatch.createStarted("扣减消费积分");
        if (StringUtils.isEmpty(param.getPointPoolId())) {
            throw LoyaltyI18nException.buildMissingParametersException("pointPoolId");
        }
        List<IObjectData> pointsDetaislList = loyaltyPointsDetailService.findMemberPointsDetails(param, false);
        stopWatch.lap("按优先级顺序查询积分明细列表");
        List<ObjectDataDocument> insertRecordlList = updateLoyaltyPointsDetail(tenantId, param.getValue(), param.getPointPoolId(), pointsDetaislList);
        stopWatch.lap("扣减积分明细");
        saveMemberChangeRecord(param, insertRecordlList);
        stopWatch.lap("插入操作记录");
        loyaltyMemberService.unLockUpdateMemberPoints(tenantId, memberId);
        stopWatch.lap("会员重算积分");
        increasePool(tenantId, param.getPointPoolId(), param.getValue(), LoyaltyConstants.LoyaltyPointPool.RECOVERY_QUANTITY);
        stopWatch.lap("积分池增加积分");
        stopWatch.logSlow(STOP_WATCH_TIME);
    }

    @Override
    public void fallback(Loyalty.FallbackUpdateData updateData, Loyalty.FallbackInfo fallbackInfo) {
        IObjectData record = fallbackInfo.getRecord();
        String tenantId = record.get(IObjectData.TENANT_ID, String.class);
        String poolId = record.get(LoyaltyConstants.LoyaltyMemberChangeRecords.POINT_POOL_ID, String.class);
        IObjectData pointsDetail = fallbackInfo.getPointsDetail();
        if (pointsDetail == null) {
            throw LoyaltyI18nException.build(LoyaltyI18nKey.NOT_FOUND, LoyaltyConstants.LoyaltyPointsDetail.API_NAME);
        }
        long changePointsValue = NumberUtils.getLong(record, LoyaltyConstants.LoyaltyMemberChangeRecords.LOY_CHANGE_VALUE);
        //积分池扣分
        decreasePool(tenantId, poolId, changePointsValue, LoyaltyConstants.LoyaltyPointPool.RECOVERY_QUANTITY);
        //积分明细加分
        NumberUtils.sum(pointsDetail, LoyaltyConstants.LoyaltyPointsDetail.AVAILABLE_POINTS, changePointsValue);
        LoyaltyPointsDetailService.setPointsStatus(pointsDetail, LoyaltyConstants.LoyaltyPointsDetail.PointsTrend.increase);
        //更新
        record.set(LoyaltyConstants.LoyaltyMemberChangeRecords.IS_FALLBACK, true);
        updateData.getUpdatePointsDetailList().add(pointsDetail);
    }

}