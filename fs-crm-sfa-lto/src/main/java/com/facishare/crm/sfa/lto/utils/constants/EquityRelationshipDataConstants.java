package com.facishare.crm.sfa.lto.utils.constants;

/**
 * <AUTHOR> lik
 * @date : 2023/6/29 17:14
 */

public interface EquityRelationshipDataConstants {
    String AccountMainDataObj ="AccountMainDataObj";

    class Param {
        public static final String NAME = "name";
        public static final String KEYWORD = "keyword";
        public static final String X_GATEWAY_APIKEY = "x-Gateway-APIKey";
        public static final String UNIQUENESS_DATA_RELATIONSHIP = "uniqueness_data_relationship";
        public static final String ROOT_NODE_STOCK_PROPORTION = "root_node_stock_proportion";
        public static final String ENTERPRISE_ID = "enterprise_id";
        public static final String ENTERPRISE_NAME = "enterprise_name";
        public static final String ENTERPRISE_TYPE = "enterprise_type";
        public static final String ROOT_NODE_PATH = "root_node_path";
        public static final String RELATIONSHIP_TYPE = "relationship_type";
        public static final String PARENT_ENTERPRISE_ID = "parent_enterprise_id";
        public static final String PARENT_ENTERPRISE_NAME = "parent_enterprise_name";
        public static final String PARENT_STOCK_PROPORTION = "parent_stock_proportion";
        public static final String PARENT_INVESTMENT = "parent_investment";
        public static final String RELATIONSHIP_PATH = "relationship_path";
        public static final String IS_NORMAL = "is_normal";
        public static final String RELATIONSHIP_LEVEL = "relationship_level";

        public static final String _ID = "_id";

        public static final String LAST_UPDATE_TIME = "last_update_time";
        public static final String ACCOUNT_MAIN_DATA_ID = "account_main_data_id";
        public static final String FIND_BY_DATA_ID = "find_by_data_id";

        //指定绑定客户主数据的name的key
        public static final String DATA_NAME = "data_name";
        //指定绑定客户主数据的id的key
        public static final String DATA_ID = "data_id";
        //记录股权关系根据那个企业生成的key
        public static final String MAIN_EID = "main_eid";


    }

    enum EnterpriseTypeEnum {

        E("E","企业"),
        P("P","个人"),
        UE("UE","未在工商注册的企业"),
        U("U","类似社会公众股、自然人股东、法人股等字段");
        private final String key;
        private final String value;

        EnterpriseTypeEnum(String key,String value) {
            this.key = key;
            this.value = value;
        }

        public String getKey() {
            return this.key;
        }
    }
    enum RelationshipTypeEnum {

        SHAREHOLDER("shareholder","股东"),
        OVERSEAS_INVESTMENT("investmentAbroad","对外投资"),
        BRANCH("branch","分支机构"),
        EMPLOYEE("employee","高管");
        private final String key;
        private final String value;

        RelationshipTypeEnum(String key,String value) {
            this.key = key;
            this.value = value;
        }

        public String getKey() {
            return this.key;
        }
    }
}
