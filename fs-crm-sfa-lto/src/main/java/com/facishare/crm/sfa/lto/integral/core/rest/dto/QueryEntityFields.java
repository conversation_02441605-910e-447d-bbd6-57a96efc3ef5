package com.facishare.crm.sfa.lto.integral.core.rest.dto;

import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Set;

public interface QueryEntityFields {
    @EqualsAndHashCode(callSuper = true)
	@Data
    class Arg extends BaseEngine.Arg {
        @SerializedName("entityIds")
        Set<String> objectApiNames;

        public static Arg build(String tenantId, Set<String> objectApiNames) {
            Arg out = new Arg();
            BaseEngine.Context context = new BaseEngine.Context();
            context.setUserId("-10000");
            context.setTenantId(tenantId);

            out.setContext(context);
            out.setObjectApiNames(objectApiNames);
            return out;
        }
    }

    @EqualsAndHashCode(callSuper = true)
	@Data
    class Result extends BaseEngine.Result<List<PartialInfo>> { }

    @Data
    class PartialInfo {
        @SerializedName("entityId")
        String objectApiName;
        @SerializedName("fieldName")
        String fieldName;
        @SerializedName("macroGroupApiName")
        String ruleApiName;
        @SerializedName("realTime")
        boolean realTimeUpdate;
    }
}
