package com.facishare.crm.sfa.lto.duplicated;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.lto.duplicated.models.DuplicatedModels;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.foundation.boot.utility.JSON;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@Slf4j
public class DirectDuplicatedDataProcessor extends AbstractDuplicatedDataProcessor {
    @Override
    public String getProcessorModel() {
        return "DIRECT_PROCESSOR";
    }

    @Override
    public boolean matchProcessor(DuplicatedModels.TriggerAction triggerAction) {
        return DuplicatedModels.TriggerAction.DIRECT_PROCESSOR.equals(triggerAction);
    }

    @Override
    public void process(DuplicatedDataProcessor.DuplicatedDataProcessArg processArg) {
        List<IObjectData> objectDataList = processArg.getDataList();
        User user = processArg.getUser();
        if (skipLeadsDuplicated(user.getTenantId()) || CollectionUtils.isEmpty(objectDataList)) {
            return;
        }
        DuplicatedModels.TriggerAction triggerAction = processArg.getAction();
        if(DuplicatedModels.TriggerAction.OPEN_API_ADD.equals(processArg.getAction())
                || DuplicatedModels.TriggerAction.OPEN_API_EDIT.equals(processArg.getAction())) {
            triggerAction = DuplicatedModels.TriggerAction.OPEN_API;
        }
        List<DuplicatedModels.DuplicatedProcessing> processingRuleList = duplicatedProcessingRuleService.getDuplicatedProcessingListByTriggerAction(user, triggerAction);
        List<DuplicatedModels.DuplicatedProcessing> needDirectProcessingRuleList =  getNeedDirectProcessRules(processingRuleList);
        if(CollectionUtils.isEmpty(needDirectProcessingRuleList)) {
            return;
        }

        //返回结构: key 处理规则id,value 符合处理规则的数据列表,如果是单独创建的数据,结果只有一个规则对应数量为 1 的数据列表
        Map<String, List<IObjectData>> matchMaps = ruleMatcher.matches(user.getTenantId(), Utils.LEADS_API_NAME, processingRuleList, objectDataList);
        log.debug("LeadsDuplicatedProcessing->process->matchMaps:" + JSON.serialize(matchMaps));
        if(MapUtils.isEmpty(matchMaps)) {
            return;
        }

        for (Map.Entry<String, List<IObjectData>> entry : matchMaps.entrySet()) {
            if(skipLeadsDuplicated(user.getTenantId())) {
                return;
            }
            String ruleId = entry.getKey();
            List<IObjectData> matchedDataList = entry.getValue();
            Optional<DuplicatedModels.DuplicatedProcessing> optional = processingRuleList.stream()
                    .filter(p -> ruleId.equals(p.getId())).findFirst();
            if (StringUtils.isBlank(ruleId)
                    || CollectionUtils.isEmpty(matchedDataList)
                    || !optional.isPresent()
                    || needDirectProcessingRuleList.stream().noneMatch(x -> ruleId.equals(x.getId()))) {
                continue;
            }

            DuplicatedModels.DuplicatedProcessing processingRule = optional.get();
            log.info("LeadsDuplicatedProcessing->processDataListWithMatchedProcessingRule start...");
            processDataListWithMatchedProcessingRule(user, processingRule, matchedDataList, processArg.getAction());
            log.info("LeadsDuplicatedProcessing->processDataListWithMatchedProcessingRule finished...");
        }
    }

    private List<DuplicatedModels.DuplicatedProcessing> getNeedDirectProcessRules(
            List<DuplicatedModels.DuplicatedProcessing> processingRuleList) {
        if (CollectionUtils.isEmpty(processingRuleList)) {
            return Lists.newArrayList();
        }
        List<DuplicatedModels.DuplicatedProcessing> result = Lists.newArrayList();
        for(DuplicatedModels.DuplicatedProcessing rule : processingRuleList) {
            if(rule.getDuplicatedProcessingModes().stream()
                    .anyMatch(x -> DuplicatedModels.ModeAction.ADD_INTERACTIVE_RECORDS.equals(x.getModeAction()))) {
                result.add(rule);
            }
        }
        return result;
    }

    private void processDataListWithMatchedProcessingRule(
            User user, DuplicatedModels.DuplicatedProcessing processingRule, List<IObjectData> dataList,
            DuplicatedModels.TriggerAction triggerAction) {
        List<DuplicatedModels.DuplicatedSearchRule> searchRuleList = processingRule.getDuplicatedSearchRules();
        List<DuplicatedModels.DuplicatedProcessingMode> modeList = processingRule.getDuplicatedProcessingModes();
        for (IObjectData objectData : dataList) {
            //返回map结构: key 查重对象,value:重复数据列表
            Map<String, List<IObjectData>> duplicatedMap = duplicatedSearchDelegate.search(user, searchRuleList, objectData, null);
            processDataWithProcessingMode(user, objectData, duplicatedMap, modeList, triggerAction);
        }
    }

    /**
     * 使用查重出来的数据对处理方式进行匹配,匹配完进行相应的处理
     *
     * @param duplicatedMap      每个对象下的重复数据
     * @param processingModeList 处理方式列表
     * @return
     */
    private void processDataWithProcessingMode(
            User user, IObjectData freshData, Map<String, List<IObjectData>> duplicatedMap,
            List<DuplicatedModels.DuplicatedProcessingMode> processingModeList,
            DuplicatedModels.TriggerAction triggerAction) {
        List<IObjectData> duplicatedDataList = duplicatedMap.get(Utils.LEADS_API_NAME);
        String freshDataId = freshData.getId();
        if (StringUtils.isNotEmpty(freshDataId) && CollectionUtils.isNotEmpty(duplicatedDataList)) {
            duplicatedDataList = duplicatedDataList.stream()
                    .filter(d -> StringUtils.isNotEmpty(d.getId()) && !freshDataId.equals(d.getId()))
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(duplicatedDataList)) {
            return;
        }
        Tuple<DuplicatedModels.DuplicatedProcessingMode, List<IObjectData>> tuple = ruleMatcher.matches(user.getTenantId(),
                Utils.LEADS_API_NAME, processingModeList, freshData, duplicatedDataList);

        if (tuple == null || tuple.getKey() == null
                || !DuplicatedModels.ModeAction.ADD_INTERACTIVE_RECORDS.equals(tuple.getKey().getModeAction())) {
            return;
        }
        DuplicatedModels.DuplicatedProcessingMode mode = tuple.getKey();
        DuplicatedModels.ModeAction modeAction = mode.getModeAction();
        ModeActionProcessor modeActionProcessor = modeActionProcessorFactory.getModeActionProcessor(modeAction);
        if(modeActionProcessor != null) {
            modeActionProcessor.process(user, mode, freshData, tuple.getValue(), triggerAction);
        }
    }
}