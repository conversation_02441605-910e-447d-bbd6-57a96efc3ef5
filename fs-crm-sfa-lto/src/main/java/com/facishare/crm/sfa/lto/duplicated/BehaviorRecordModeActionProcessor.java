package com.facishare.crm.sfa.lto.duplicated;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.lto.common.models.LtoFieldApiConstants;
import com.facishare.crm.sfa.lto.duplicated.models.DuplicatedModels;
import com.facishare.crm.sfa.lto.utils.*;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.AcceptableValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.metadatahandle.FillFieldInfoHandle;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.LongText;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.FileAttachmentFieldDescribe;
import com.facishare.paas.metadata.impl.search.CommonSqlOperator;
import com.facishare.paas.metadata.impl.search.WhereParam;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class BehaviorRecordModeActionProcessor extends AbstractModeActionProcessor {
    @Autowired
    protected FillFieldInfoHandle fillFieldInfoHandle;

    private static List<String> behaviorRecordIgnoreFieldList = Lists.newArrayList("_id", "id", "object_describe_api_name", LtoFieldApiConstants.TENANT_ID,
            "extend_obj_data_id", "package", "version", "order_by", "owner_change_time", "life_status_before_invalid", "leads_status", "is_deleted");


    @Override
    public DuplicatedModels.ModeAction getModelAction() {
        return DuplicatedModels.ModeAction.ADD_INTERACTIVE_RECORDS;
    }

    @Override
    public void process(User user, DuplicatedModels.DuplicatedProcessingMode processingMode,
                                                  IObjectData freshData, List<IObjectData> duplicatedDataList,
                                                  DuplicatedModels.TriggerAction triggerAction) {
        addInteractiveRecords(user, freshData, duplicatedDataList, processingMode, triggerAction);
    }

    private void addInteractiveRecords(User user, IObjectData freshData, List<IObjectData> duplicatedDataList,
                                      DuplicatedModels.DuplicatedProcessingMode processingMode,
                                      DuplicatedModels.TriggerAction triggerAction) {

        boolean isEnableImport = LeadsPoolUtil.isGraySfaImportBehaviorActiveRecords(user.getTenantId()) && DuplicatedModels.TriggerAction.IMPORT.equals(triggerAction);
        // 如果开启了灰度，放开导入生成行为记录

        if (!DuplicatedModels.TriggerAction.OPEN_API_ADD.equals(triggerAction) && !DuplicatedModels.TriggerAction.SMART_FORM.equals(triggerAction) &&
                !DuplicatedModels.TriggerAction.ADD.equals(triggerAction) && !DuplicatedModels.TriggerAction.MARKETING_ADD.equals(triggerAction)
                && !isEnableImport) {
            return;
        }
        IObjectData needChangeLeads = getCollectedToLeads(processingMode, duplicatedDataList, freshData);
        if (needChangeLeads == null) {
            return;
        }
        IObjectData freshDataCopy = ObjectDataExt.of(freshData).copy();
        IObjectData needChangeLeadsCopy = ObjectDataExt.of(needChangeLeads).copy();
        try {
            ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
            parallelTask.submit(() -> {
                IObjectDescribe leadsDescribe = serviceFacade.findObject(user.getTenantId(), Utils.LEADS_API_NAME);
                if (leadsDescribe == null) {
                    return;
                }
                //自动更新线索
                changeLeads(user, freshDataCopy, needChangeLeadsCopy, processingMode, leadsDescribe);
                //生成行为记录
                addBehaviorRecord(user, triggerAction, freshDataCopy, needChangeLeadsCopy, leadsDescribe);
            });
            parallelTask.run();
        } catch (Exception ex) {
            log.error("parallel execute error:" + ex.getMessage(), ex);
        }

        //此处直接返回错误，后续代码不在执行
        throwLeadsBehaviorValidateException(needChangeLeads);
    }

    /**
     * 修改相关线索
     *
     * @param freshData
     * @param needChangeLeads
     * @param processingMode
     */
    private void changeLeads(User user, IObjectData freshData, IObjectData needChangeLeads,
                             DuplicatedModels.DuplicatedProcessingMode processingMode, IObjectDescribe leadsDescribe) {
        if (processingMode.getModeRule() == null
                || processingMode.getModeRule().getExtendRules() == null
                || !processingMode.getModeRule().getExtendRules().isAutoUpdate()) {
            return;
        }
        DuplicatedModels.ExtendRules extendRules = processingMode.getModeRule().getExtendRules();
        DuplicatedModels.UpdateMode multiLineUpdateMode = extendRules.getMultiLineUpdateMode();
        DuplicatedModels.UpdateMode manySelectUpdateMode = extendRules.getManySelectUpdateMode();
        DuplicatedModels.UpdateMode otherUpdateMode = extendRules.getOtherUpdateMode();
        Map<String, Object> fieldMap = Maps.newHashMap();
        Map<String, Object> diffMap = ObjectDataExt.of(needChangeLeads).diff(freshData, leadsDescribe);
        for (Map.Entry<String, Object> entry : diffMap.entrySet()) {
            String fieldName = entry.getKey();
            IFieldDescribe fieldDescribe = leadsDescribe.getFieldDescribe(fieldName);
            if (fieldDescribe == null) {
                continue;
            }
            String fieldType = fieldDescribe.getType();
            Object newFieldValue = freshData.get(fieldName, Object.class);
            if (ObjectDataExt.isValueEmpty(newFieldValue, fieldType)) {
                continue;
            }
            Object oldFieldValue = needChangeLeads.get(fieldName, Object.class);
            if (ObjectDataExt.isValueEmpty(oldFieldValue, fieldType)) {
                fieldMap.put(fieldName, newFieldValue);
                continue;
            }
            if (IFieldType.LONG_TEXT.equals(fieldType)) {
                switch (multiLineUpdateMode) {
                    case SPLICING:
                        fieldMap.put(fieldName, oldFieldValue.toString() + ";" + newFieldValue.toString());
                        break;
                    case SKIP:
                        continue;
                    case COVER:
                        fieldMap.put(fieldName, newFieldValue);
                        break;
                    case NULL_VALUE_COVER:
                        nullValueCover(fieldMap, fieldName, oldFieldValue, newFieldValue);
                        break;
                    default:
                        break;
                }
            } else if (IFieldType.IMAGE.equals(fieldType) || IFieldType.FILE_ATTACHMENT.equals(fieldType)) {
                Integer fileAmountLimit = ((FileAttachmentFieldDescribe) fieldDescribe).getFileAmountLimit();
                if (fileAmountLimit == null) {
                    fileAmountLimit = 20;
                }
                List newData = (List) newFieldValue;
                int newFileSize = fileAmountLimit - ((List) oldFieldValue).size();
                if (newFileSize > 0) {
                    if (newData.size() > newFileSize) {
                        newData = newData.subList(0, newFileSize - 1);
                    }
                    List oldData = (List) oldFieldValue;
                    List copyOldData = new ArrayList<>();
                    CollectionUtils.addAll(copyOldData, new Object[oldData.size()]);
                    Collections.copy(copyOldData, oldData);
                    copyOldData.addAll(newData);
                    fieldMap.put(fieldName, copyOldData);
                }
            } else if (IFieldType.SELECT_MANY.equals(fieldType) || IFieldType.EMPLOYEE_MANY.equals(fieldType) ||
                    IFieldType.DEPARTMENT_MANY.equals(fieldType) || IFieldType.OBJECT_REFERENCE_MANY.equals(fieldType)) {
                //因为没有刷线索重复处理规则，所以在这兼容处理
                if (manySelectUpdateMode == null) {
                    manySelectUpdateMode = otherUpdateMode;
                }
                switch (manySelectUpdateMode) {
                    case SPLICING:
                        List<String> list = Lists.newArrayList();
                        list.addAll((Collection<? extends String>) oldFieldValue);
                        list.addAll((Collection<? extends String>) newFieldValue);
                        list = list.stream().distinct().collect(Collectors.toList());
                        fieldMap.put(fieldName, list);
                        break;
                    case SKIP:
                        continue;
                    case COVER:
                        fieldMap.put(fieldName, newFieldValue);
                        break;
                    case NULL_VALUE_COVER:
                        nullValueCover(fieldMap, fieldName, oldFieldValue, newFieldValue);
                        break;
                    default:
                        break;
                }
            } else {
                switch (otherUpdateMode) {
                    case SKIP:
                        continue;
                    case COVER:
                        fieldMap.put(fieldName, newFieldValue);
                        break;
                    case NULL_VALUE_COVER:
                        nullValueCover(fieldMap, fieldName, oldFieldValue, newFieldValue);
                        break;
                    default:
                        break;
                }
            }
        }
        if (fieldMap.isEmpty()) {
            return;
        }
        IObjectData oldLeadsData = ObjectDataExt.of(needChangeLeads).copy();
        for (Map.Entry<String, Object> entry : fieldMap.entrySet()) {
            needChangeLeads.set(entry.getKey(), entry.getValue());
        }
        //更新字段
        ObjectDataUtil.updateFields(user, Lists.newArrayList(needChangeLeads), Lists.newArrayList(fieldMap.keySet()), true, false);
        serviceFacade.log(user, EventType.MODIFY, ActionType.Modify, leadsDescribe, needChangeLeads, fieldMap, oldLeadsData,
                I18N.text(SfaLtoI18NKeyUtil.SFA_DUPLICATED_AUTOCHANGELEADS), I18N.text(SfaLtoI18NKeyUtil.SFA_DUPLICATED_AUTOCHANGELEADS), null);
    }

    /**
     * 生成行为记录
     *
     * @param freshData
     */
    private void addBehaviorRecord(User user, DuplicatedModels.TriggerAction triggerAction, IObjectData freshData, IObjectData needChangeLeads, IObjectDescribe leadsDescribe) {
        IObjectDescribe behaviorRecordDescribe = serviceFacade.findObject(user.getTenantId(), Utils.BEHAVIOR_RECORD_OBJ_API_NAME);

        fillFieldInfoHandle.asyncFillFieldInfo(leadsDescribe, Lists.newArrayList(freshData), user);
        RequestContext requestContext = RequestContextManager.getContext();
		if (requestContext == null) {
			log.warn("requestContext为null");
			return;
		}
        requestContext.setAttribute("not_validate", true);
        requestContext.setAttribute("triggerFlow", false);
        com.facishare.paas.appframework.core.model.ActionContext actionContext = new com.facishare.paas.appframework.core.model.ActionContext(requestContext, Utils.BEHAVIOR_RECORD_OBJ_API_NAME, ObjectAction.CREATE.getActionCode());
        IObjectData behaviorRecordData = new ObjectData();
        String buildAction = "3";
        if(DuplicatedModels.TriggerAction.OPEN_API_ADD.equals(triggerAction) || DuplicatedModels.TriggerAction.OPEN_API_EDIT.equals(triggerAction)) {
            buildAction = "2";
        } else if(DuplicatedModels.TriggerAction.SMART_FORM.equals(triggerAction)) {
            buildAction = "1";
        }
        behaviorRecordData.set("build_action", buildAction);
        List<IFieldDescribe> fieldDescribeList = leadsDescribe.getFieldDescribes().stream()
                .filter(m -> IFieldType.IMAGE.equals(m.getType()) || IFieldType.FILE_ATTACHMENT.equals(m.getType()) || IFieldType.SIGNATURE.equals(m.getType()))
                .collect(Collectors.toList());
        List<IFieldDescribe> otherFieldDescribeList = leadsDescribe.getFieldDescribes().stream()
                .filter(m -> !IFieldType.IMAGE.equals(m.getType()) && !IFieldType.FILE_ATTACHMENT.equals(m.getType()) && !IFieldType.SIGNATURE.equals(m.getType()))
                .collect(Collectors.toList());
        StringBuilder behaviorRecordsSb = new StringBuilder();

        if (CollectionUtils.isNotEmpty(otherFieldDescribeList)) {
            for (IFieldDescribe fieldDescribe : otherFieldDescribeList) {
                if (fieldDescribe == null || behaviorRecordIgnoreFieldList.contains(fieldDescribe.getApiName())) {
                    continue;
                }
                Object value = infraServiceFacade.convertData(freshData, fieldDescribe, user);
                if (Objects.isNull(value) || Strings.isBlank(value.toString())) {
                    continue;
                }
                behaviorRecordsSb.append(fieldDescribe.getLabel()).append(":").append(value).append(";   ");
            }
        }
        LongText behaviorRecordsField = (LongText) behaviorRecordDescribe.getFieldDescribe("behavior_records");
        Integer maxLength = behaviorRecordsField.getMaxLength();
        if (maxLength == null) {
            maxLength = 2000;
        }
        behaviorRecordData.set("behavior_records", behaviorRecordsSb.length() > maxLength ? behaviorRecordsSb.substring(0, maxLength) : behaviorRecordsSb.toString());

        List<IObjectData> attachmentObjectData = Lists.newArrayList();
        List<IObjectData> pictureObjectData = Lists.newArrayList();
        FileAttachmentFieldDescribe attachmentField = (FileAttachmentFieldDescribe) behaviorRecordDescribe.getFieldDescribe("attachment");
        FileAttachmentFieldDescribe pictureField = (FileAttachmentFieldDescribe) behaviorRecordDescribe.getFieldDescribe("picture");
        Integer attachmentFileAmountLimit = attachmentField.getFileAmountLimit();
        if (attachmentFileAmountLimit == null) {
            attachmentFileAmountLimit = 20;
        }
        Integer pictureFileAmountLimit = pictureField.getFileAmountLimit();
        if (pictureFileAmountLimit == null) {
            pictureFileAmountLimit = 20;
        }
        if (CollectionUtils.isNotEmpty(fieldDescribeList)) {
            for (IFieldDescribe fieldDescribe : fieldDescribeList) {
                if (fieldDescribe == null || behaviorRecordIgnoreFieldList.contains(fieldDescribe.getApiName())) {
                    continue;
                }
                List newData = freshData.get(fieldDescribe.getApiName(), List.class);
                if (CollectionUtils.isNotEmpty(newData)) {
                    if (IFieldType.FILE_ATTACHMENT.equals(fieldDescribe.getType()) && attachmentObjectData.size() < attachmentFileAmountLimit) {
                        int newFileSize = attachmentFileAmountLimit - attachmentObjectData.size();
                        if (newData.size() > newFileSize) {
                            newData = newData.subList(0, newFileSize - 1);
                        }
                        attachmentObjectData.addAll(newData);
                    }
                    if ((IFieldType.IMAGE.equals(fieldDescribe.getType()) || IFieldType.SIGNATURE.equals(fieldDescribe.getType())) && pictureObjectData.size() < pictureFileAmountLimit) {
                        int newFileSize = pictureFileAmountLimit - pictureObjectData.size();
                        if (newData.size() > newFileSize) {
                            newData = newData.subList(0, newFileSize - 1);
                        }
                        pictureObjectData.addAll(newData);
                    }
                }
            }
        }
        behaviorRecordData.set("attachment", attachmentObjectData);
        behaviorRecordData.set("picture", pictureObjectData);
        Map<String, List<String>> relatedObjectData = Maps.newHashMap();
        relatedObjectData.put(Utils.LEADS_API_NAME, Lists.newArrayList(needChangeLeads.getId()));
        behaviorRecordData.set("related_object", relatedObjectData);
        behaviorRecordData.set("related_api_names", Lists.newArrayList(Utils.LEADS_API_NAME));
        behaviorRecordData.set(LtoFieldApiConstants.OBJECT_DESCRIBE_API_NAME, behaviorRecordDescribe.getApiName());
        behaviorRecordData.set("object_describe_id", behaviorRecordDescribe.getId());
        behaviorRecordData.set(LtoFieldApiConstants.TENANT_ID, user.getTenantId());
        behaviorRecordData.set(LtoFieldApiConstants.OWNER, freshData.getOwner());
        behaviorRecordData.set(LtoFieldApiConstants.OUT_OWNER, freshData.getOutOwner());
        behaviorRecordData.set(LtoFieldApiConstants.OUT_TENANT_ID, freshData.getOutTenantId());
        behaviorRecordData.set("record_type", "default__c");
        behaviorRecordData.set("is_from_leads_add", true);
        BaseObjectSaveAction.Arg arg = new BaseObjectSaveAction.Arg();
        arg.setObjectData(ObjectDataDocument.of(behaviorRecordData));
        BaseObjectSaveAction.OptionInfo optionInfo = new BaseObjectSaveAction.OptionInfo();
        optionInfo.setIsDuplicateSearch(false);
        optionInfo.setUseValidationRule(false);
        optionInfo.setSkipFuncValidate(true);
        arg.setOptionInfo(optionInfo);
        String body = JSON.toJSONString(arg);
        log.debug("AsyncDuplicatedDataProcessor>addBehaviorRecord()  body{}: ", body);
        BaseObjectSaveAction.Result saveResult = serviceFacade.triggerAction(actionContext, arg, BaseObjectSaveAction.Result.class);
        log.debug("AsyncDuplicatedDataProcessor>addBehaviorRecord() result{}: ", JsonUtil.toJsonWithNullValues(saveResult));
        try {
            String objectDataId = saveResult.getObjectData().getId();
            if (Strings.isBlank(objectDataId)) {
                return;
            }
            String behaviorRecordsParam = JsonUtil.toJsonWithNullValues(ObjectDataDocument.of(freshData));
            Map<String, Object> dataMap = Maps.newHashMap();
            dataMap.put("behavior_records_param", behaviorRecordsParam);
            List<WhereParam> whereParamsList = Lists.newArrayList();
            CommonSqlUtil.addWhereParam(whereParamsList, LtoFieldApiConstants.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(user.getTenantId()));
            CommonSqlUtil.addWhereParam(whereParamsList, LtoFieldApiConstants.ID, CommonSqlOperator.EQ, Lists.newArrayList(objectDataId));
            CommonSqlUtil.updateData(user.getTenantId(), behaviorRecordDescribe.getStoreTableName(), dataMap, whereParamsList);
        } catch (Exception ex) {
            log.error("修改行为记录入参报错，", ex);
        }
    }

    private void throwLeadsBehaviorValidateException(IObjectData objectData) {
        throw new AcceptableValidateException(LeadsBehaviorValidateResult.builder()
                .apiName(Utils.LEADS_API_NAME).objectId(objectData.getId()).message(I18N.text(SfaLtoI18NKeyUtil.SFA_LEADS_REPEAT,
                        I18N.text("LeadsObj.attribute.self.display_name"),
                        I18N.text("BehaviorRecordObj.attribute.self.display_name"))).errorCode(999999)
                .isDuplicate(false).objectData(ObjectDataDocument.of(objectData)).build());
    }

    // 空值覆盖
    private void nullValueCover(Map<String, Object> fieldMap, String fieldName, Object oldFieldValue, Object newFieldValue) {
        if (ObjectUtils.isEmpty(oldFieldValue)) {
            fieldMap.put(fieldName, newFieldValue);
        }
    }

    @Data
    @Builder
    public static class LeadsBehaviorValidateResult {
        String message;
        int errorCode;
        String apiName;
        String objectId;
        Boolean isDuplicate;
        ObjectDataDocument objectData;
    }
}