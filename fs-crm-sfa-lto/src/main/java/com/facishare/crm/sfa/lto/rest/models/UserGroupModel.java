package com.facishare.crm.sfa.lto.rest.models;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

public interface UserGroupModel {
    @Data
    @Builder
    class UserGroupListWithContextArg {
        private PAASContext context;
        private List<String> groupIdList;
        private boolean filterByUser;
        private boolean publicFlag;
        private int status;
    }

    @Data
    class UserGroupListResult {
        private int errCode;
        private String errKey;
        private String errMessage;
        private String errDescription;
        private List<String> result;
        private Map<String, List<String>> groupMems;
        private boolean success;
    }

    @Data
    @Builder
    class UserGroupUserListArg {
        private String tenantId;
        private String appId;
        private String userId;
        private List<String> userIdList;
        private boolean filterByUser;
        private boolean isPublic;
        private int status;
        private UserGroupPageInfo page;
    }

    @Data
    class UserGroupListPageResult {
        private int errCode;
        private String errKey;
        private String errMessage;
        private String errDescription;
        private List<String> result;
        private UserGroupPageInfo pageInfo;
        private boolean success;
    }

    @Data
    @Builder
    class UserGroupListArg {
        private String tenantId;
        private String appId;
        private String userId;
        private List<String> groupIdList;
        private boolean filterByUser;
        private boolean isPublic;
        private UserGroupPageInfo page;
    }

    @Data
    class UserGroupPageResult {
        private int errCode;
        private String errKey;
        private String errMessage;
        private String errDescription;
        private List<UserGroupPoto> result;
        private UserGroupPageInfo pageInfo;
        private boolean success;
    }

    @Data
    @Builder
    class UserGroupPageInfo {
        private int total;
        private int pageSize;
        private int currentPage;
        private int totalPage;

    }

    @Data
    class UserGroupPoto {
        private String id;
        private String tenantId;
        private String appId;
        private String name;
        private String description;
        private Boolean delFlag;
        private int status;
        private int type;
        private int totalPage;

    }

    @Data
    @Builder
    class GetUserGroupByIdsArg {
        private String tenantId;
        private String appId;
        private String userId;
        private List<String> groupIdList;
    }

    @Data
    class GetUserGroupByIdsResult {
        private int errCode;
        private String errKey;
        private String errMessage;
        private String errDescription;
        private List<String> result;
        private boolean success;
    }

    @Data
    @Builder
    class GetUserGroupByMemberIdsArg {
        private String tenantId;
        private String appId;
        private String userId;
        private List<String> memberIdList;
    }

    @Data
    class GetUserGroupByMemberIdsResult {
        private int errCode;
        private String errKey;
        private String errMessage;
        private String errDescription;
        private List<String> result;
        private boolean success;
    }

    @Data
    class GetUserGroupMembersResult{
        private int errCode;
        private String errKey;
        private String errMessage;
        private String errDescription;
        private List<String> result;
        private boolean success;
    }

    @Data
    @Builder
    class GetUserGroupMembersArg{
        private String tenantId;
        private String appId;
        private String userId;
        private List<String> groupIdList;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class UserGroupChangedMessage {
        private String appId;
        private String tenantId;
        private List<String> newMembers;
        private List<String> oldMembers;
        private List<String> relationIds;
        private int status;
        private int type;
    }

    enum UserGroupStatusEnum {
        Active(0, "启用"),
        Inactive(1, "停用");

        private final int code;
        private final String description;

        UserGroupStatusEnum(int code, String description) {
            this.code = code;
            this.description = description;
        }

        public int getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

    }
}
