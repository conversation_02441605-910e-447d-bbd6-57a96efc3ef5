package com.facishare.crm.sfa.lto.common.models;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import java.util.List;
import lombok.Builder;
import lombok.Data;

public interface FindByQuery {
    @Data
    @Builder
    class QueryBySearchTemplateArg {
        SearchTemplateQuery queryJson;
    }

    @Data
    class Result {
        int code;
        String message;
        QueryResult data;
    }

    @Data
    class QueryResult {
        @JSONField(name = "describe")
        @JsonProperty(value = "describe")
        @SerializedName("describe")
        ObjectDescribeDocument describeDocument;

        @JSONField(name = "queryResult")
        @JsonProperty(value = "queryResult")
        @SerializedName("queryResult")
        QueryResultInfo queryResult;
    }

    @Data
    class QueryResultInfo {
        List<ObjectDataDocument> data;
        Integer totalNumber;
    }
}
