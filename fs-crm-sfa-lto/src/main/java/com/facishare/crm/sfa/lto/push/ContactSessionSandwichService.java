package com.facishare.crm.sfa.lto.push;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 联系人变更后给终端推送通知
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ContactSessionSandwichService extends AbstractSessionSandwichService {

    @Override
    protected SessionSandwichUpdateTypeEnum getSessionSandwichUpdateType() {
        return SessionSandwichUpdateTypeEnum.CONTACT;
    }

    @Override
    protected String getContent() {
        Map<String, Object> content = Maps.newHashMap();
        content.put("timestamp", System.currentTimeMillis());
        return JSONObject.toJSONString(content);
    }
}
