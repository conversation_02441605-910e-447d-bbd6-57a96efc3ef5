package com.facishare.crm.sfa.lto.rfm;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.lto.common.LtoRateLimiterService;
import com.facishare.crm.sfa.lto.common.LtoTaskService;
import com.facishare.crm.sfa.lto.rest.LtoCustomProxy;
import com.facishare.crm.sfa.lto.rest.models.CustomProxyModel;
import com.facishare.crm.sfa.lto.rfm.models.RFMModels;
import com.facishare.crm.sfa.lto.utils.*;
import com.facishare.paas.appframework.core.model.ObjectFieldDescribeDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.MetaDataComputeService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.fxiaoke.common.SqlEscaper;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.matheclipse.core.eval.util.ArraySet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.lto.common.models.LtoFieldApiConstants.CREATE_TIME;

@Component
@Slf4j
public class RFMService {
    @Autowired
    private ServiceFacade serviceFacade;

    @Autowired
    private MetaDataComputeService metaDataComputeService;

    @Autowired
    private ObjectDataServiceImpl objectDataService;

    @Autowired
    private LtoRateLimiterService rateLimiterService;

    @Autowired
    private LtoTaskService taskService;

    @Autowired
    private IObjectDescribeService objectDescribeService;

    @Autowired
    private LtoCustomProxy customProxy;

    private static int pageSize = 200, batchSize = 100, maxExecuteCount = 1000000,deleteTempTablePageSize=1000;
    private static final String RFM_RULE_OBJ_API_NAME = "RFMRuleObj";
    private static final String RFM_TASK_BIZ = "sfa_rfm_rule_calculate_task";
    private static final String RFM_CREATE_TASK_BIZ = "sfa_create_rfm_rule_calculate_task";
    private static String executeTime = "21:00:00", rfmTenantIds = "";
    private static final String PROCESSING = "1";
    private static final String CALCULATION_FINISH = "2";
    private static final String CALCULATION_STATUS = "calculation_status";
    private static final String LAST_FINISH_TIME = "last_finish_time";

    private static final String ATTENDANCE_RULES_ID = "attendance_rules_id";

    private static String enterprise_environment = "enterprise_environment";
    //前N天、前N周、前N月 //后N天、后N周、后N月，不包含当天
    private static final List<String> OPERATION_LIST = Lists.newArrayList("LTE","GTE","LT","GT");


    static {
        ConfigFactory.getConfig("variables_endpoint", config -> {
            enterprise_environment = config.get("enterprise_environment", "");
        });
        ConfigFactory.getConfig("fs-crm-sales-config", config -> {
            pageSize = config.getInt("rfm_process_page_size", 200);
            batchSize = config.getInt("rfm_process_batch_size", 100);
            maxExecuteCount = config.getInt("rfm_process_max_execute_count", 1000000);
            executeTime = config.get("rfm_process_task_execute_time", "21:00:00");
        });

        ConfigFactory.getConfig("fs-crm-sfa_rfm-config", config -> {
            rfmTenantIds = config.get("rfmTenantIds", "");
            deleteTempTablePageSize = config.getInt("deleteTempTablePageSize", 1000);
            String rfmTenantIdsMapStr = config.get("rfmTenantIdsMap", "");
            if(ObjectUtils.isNotEmpty(rfmTenantIdsMapStr)){
                Map<String,String> rfmTenantIdsMap = JSONObject.parseObject(rfmTenantIdsMapStr, Map.class);
                if(rfmTenantIdsMap.containsKey(enterprise_environment)){
                    rfmTenantIds =  rfmTenantIdsMap.get(enterprise_environment);
                }else{
                    rfmTenantIds = "";
                    log.warn("RFMService rfmTenantIdsMap error  enterprise_environment:{}",enterprise_environment);
                }
            }
        });
    }

    public void createRFMCreateCalculateTask() {
        taskService.createOrUpdateTask(RFM_CREATE_TASK_BIZ, "-100", RFM_CREATE_TASK_BIZ, DateUtil.getDateWithHourTime("20:00:00"), "{}", null);
    }

    public void createRFMProcessTask() {
        if(StringUtils.isBlank(rfmTenantIds)) {
            return;
        }
        String[] tenantIds = rfmTenantIds.split(",");
        if(tenantIds.length <= 0) {
            return;
        }
        Date taskExecuteTime = DateUtil.getDateWithHourTime(executeTime);
        for(int index = 0; index < tenantIds.length; ++index) {
            try {
                String tenantId = tenantIds[index];
                String queryString = String.format("SELECT DISTINCT tenant_id, action_object_api_name FROM biz_rfm_rule  " +
                        "WHERE tenant_id='%s' AND is_deleted=0 AND biz_status='normal' ", tenantId);

                List<Map> queryResult = objectDataService.findBySql(tenantId, queryString);
                if(CollectionUtils.isEmpty(queryResult)) {
                    continue;
                }
                for(Map data : queryResult) {
                    String apiName =String.valueOf(data.get("action_object_api_name"));
                    String dataId = String.format("%s%s", tenantId, apiName);
                    List<IObjectData> rfmRules = getRFMRules(new User(tenantId, User.SUPPER_ADMIN_USER_ID), apiName);
                    //判断规则是否都执行完了，没有执行完，则下一个周期执行
                    if(handleRuleIsFinish(rfmRules)){
                        RFMModels.CalculateRFMTaskArg callArg = RFMModels.CalculateRFMTaskArg.builder().tenantId(tenantId)
                                .objectApiName(apiName).build();
                        taskService.createOrUpdateTask(RFM_TASK_BIZ, tenantId, dataId, taskExecuteTime, JSON.toJSONString(callArg), null);
                    }
                    deleteTempCalculateResult(tenantId,apiName);
                }
            } catch (Exception e) {
                log.warn("createRFMProcessTask", e);
            }
        }
    }

    /**
     * 删除rfm临时表的数据
     * @param tenantId
     * @param apiName
     */
    private void deleteTempCalculateResult(String tenantId,String apiName){
        try {
            String queryString = String.format("SELECT last_recalculate_time as time FROM biz_rfm_temp_calculate_result  " +
                    "WHERE tenant_id='%s' AND action_object_api_name = '%s' ORDER BY last_recalculate_time DESC limit 1;", tenantId,apiName);
            List<Map> queryResult = objectDataService.findBySql(tenantId, queryString);
            if(CollectionUtils.isNotEmpty(queryResult)) {
                Map data = queryResult.get(0);
                List<Map> queryIdResult;
                String findIdtring = "";
                String deleteSql = "";
                while(true){
                    try {
                        findIdtring = String.format("SELECT id FROM biz_rfm_temp_calculate_result  " +
                                "WHERE tenant_id='%s' AND action_object_api_name = '%s' and last_recalculate_time < '%s'  limit %s ;", tenantId,apiName,data.get("time"),deleteTempTablePageSize);
                        queryIdResult = objectDataService.findBySql(tenantId, findIdtring);
                        if(CollectionUtils.isEmpty(queryIdResult)){
                            break;
                        }
                        List<String> ids = queryIdResult.stream().map(x->x.get("id").toString()).collect(Collectors.toList());
                        deleteSql = String.format("delete from biz_rfm_temp_calculate_result WHERE id in %s ;", SqlEscaper.in_clause(ids));
                        objectDataService.deleteBySql(tenantId,deleteSql);
                        if(ids.size()<deleteTempTablePageSize){
                            break;
                        }
                    }catch (Exception e){
                        log.error("deleteTempCalculateResultDelete tenantId:{},apiName:{}",tenantId,apiName,e);
                    }
                }
            }
        } catch (Exception e) {
            log.error("deleteTempCalculateResult e:{}", e.getMessage());
        }
    }

    public void processRFMRule(User user, String apiName) {
        log.info("processRFMRule tenantId:{}, apiName:{}", user.getTenantId(), apiName);
        try {
            String startTime = DateUtil.getCurrentTime();
            List<IObjectData> rfmRules = getRFMRules(user, apiName);
            if(CollectionUtils.isEmpty(rfmRules)) {
                log.error("processRFMRule is not rule tenantId:{}, apiName:{}", user.getTenantId(), apiName);
                return;
            }
            Long processDate = DateUtil.getTimeMillisWithHourTime(executeTime);
            if(processDate > System.currentTimeMillis()) {
                processDate = processDate - DateUtil.getOneDayTimeStamp();
            }
            //有多条规则，使用优先级最高的规则生产数据
            rfmRules = getRuleByHighestPriority(rfmRules);
            for(IObjectData ruleData : rfmRules) {
                try{
                    log.info("processRFMRuleStart111 tenantId:{},ruleName:{},startTime：{}",user.getTenantId(),ruleData.getName(),DateUtil.getCurrentTime());
                    //防止中途服务重启导致重复计算
                    Long lastCalculateTime = ObjectDataUtil.getLongValue(ruleData, "last_recalculate_time", 0L);
                    if(lastCalculateTime >= processDate) {
                        log.error("processRFMRuleStart111continue tenantId:{},ruleName:{},startTime：{}",user.getTenantId(),ruleData.getName(),DateUtil.getCurrentTime());
                        continue;
                    }
                    //更新状态，正在计算中
                    IObjectData updateRuleData = ObjectDataUtil.buildObjectData(user.getTenantId(), RFM_RULE_OBJ_API_NAME, ruleData.getId());
                    updateRuleData.set(CALCULATION_STATUS,  PROCESSING);
                    ObjectDataUtil.updateFields(user, Lists.newArrayList(updateRuleData), Lists.newArrayList(CALCULATION_STATUS), false, true);
                    processRFMRule(user, ruleData, processDate);
                } catch (Exception e) {
                    log.error("processRFMRule rule calculation error:", e);
                }finally {
                    IObjectData updateRuleData = ObjectDataUtil.buildObjectData(user.getTenantId(), RFM_RULE_OBJ_API_NAME, ruleData.getId());
                    updateRuleData.set(CALCULATION_STATUS,  CALCULATION_FINISH);
                    updateRuleData.set(LAST_FINISH_TIME,  new Date().getTime());
                    updateRuleData.set("last_recalculate_time",  processDate);
                    ObjectDataUtil.updateFields(user, Lists.newArrayList(updateRuleData), Lists.newArrayList("last_recalculate_time",CALCULATION_STATUS,LAST_FINISH_TIME), false, true);
                }
                log.info("processRFMRuleStart111 tenantId:{},ruleName:{},endTime:{}",user.getTenantId(),ruleData.getName(),DateUtil.getCurrentTime());
            }
            log.info("processRFMRuleEnd tenantId:{}, apiName:{},startTime:{},endTime:{}",user.getTenantId(), apiName,startTime,DateUtil.getCurrentTime());
        } catch (Exception e) {
            log.error("processRFMRuleEnd error", e);
            throw e;
        }
    }

    /**
     * 如果有规则还在计算中，则跳过，下周期计算
     * @param rfmRules
     * @return
     */
    private boolean handleRuleIsFinish(List<IObjectData> rfmRules){
        boolean finishFlag = true;
        for(IObjectData objectData : rfmRules){
            if(ObjectUtils.isNotEmpty(objectData.get(CALCULATION_STATUS)) && PROCESSING.equals(objectData.get(CALCULATION_STATUS))){
                finishFlag = false;
                break;
            }
        }
        return finishFlag;
    }

    /**
     * 有多条规则，使用优先级最高的规则
     * @return
     */
    private List<IObjectData> getRuleByHighestPriority(List<IObjectData> rfmRules){
        Collections.sort(rfmRules, (o1, o2) -> {
            Integer i1 = ObjectDataUtil.getIntegerValue(o1, "priority", 0);
            Integer i2 = ObjectDataUtil.getIntegerValue(o2, "priority", 0);
            if(Integer.compare(i1, i2) == 0) {
                Long createTime1 = ObjectDataUtil.getLongValue(o1, CREATE_TIME, 0L);
                Long createTime2 = ObjectDataUtil.getLongValue(o2, CREATE_TIME, 0L);
                return Long.compare(createTime1, createTime2);
            }
            return  Integer.compare(i1, i2);
        });
        return rfmRules;
    }

    public void processRFMRule(User user, String apiName, String dataId) {
        log.info("processRFMRule tenantId:{}, apiName:{}, dataId: {}", user.getTenantId(), apiName, dataId);
        try {
            List<IObjectData> rfmRules = getRFMRules(user, apiName);
            if(CollectionUtils.isEmpty(rfmRules)) {
                log.info("processRFMRule is not rule  tenantId:{}, apiName:{}, dataId: {}", user.getTenantId(), apiName, dataId);
                return;
            }
            //有多条规则，使用优先级最高的规则生产数据
             rfmRules = getRuleByHighestPriority(rfmRules);
            //记录优先级高的规则已经计算出数据的标志,其他规则则不需要计算
            Set<String> ruleOfDataIdHaveRFMDataSet = new ArraySet<>();
            for(IObjectData ruleData : rfmRules) {
                if(ruleOfDataIdHaveRFMDataSet.contains(dataId)){
                    break;
                }
                processRFMRule(user, ruleData, dataId, ruleOfDataIdHaveRFMDataSet);
            }

        } catch (Exception e) {
            log.error("processRFMRule error", e);
            throw e;
        }
    }

    private void processRFMRule(User user, IObjectData ruleData, String dataId, Set<String> ruleOfDataIdHaveRFMDataSet) {
        try {
            if(!checkRuleData(user, ruleData)) {
                return;
            }
            String sourceObjectApiName = ObjectDataUtil.getStringValue(ruleData, "source_object_api_name", "");
            String relationFieldApiName = ObjectDataUtil.getStringValue(ruleData, "relation_field_api_name", "");
            String recencyFieldApiName = ObjectDataUtil.getStringValue(ruleData, "recency_field_api_name", "");
            String frequencyType = ObjectDataUtil.getStringValue(ruleData, "frequency_type", "");
            String frequencyFieldApiName = ObjectDataUtil.getStringValue(ruleData, "frequency_field_api_name", "");
            String monetaryFieldApiName = ObjectDataUtil.getStringValue(ruleData, "monetary_field_api_name", "");
            String recencyScoreSettings = ObjectDataUtil.getStringValue(ruleData, "recency_score_settings", "");
            String frequencyScoreSettings = ObjectDataUtil.getStringValue(ruleData, "frequency_score_settings", "");
            String monetaryScoreSettings = ObjectDataUtil.getStringValue(ruleData, "monetary_score_settings", "");
            String rfmResultFieldSettings = ObjectDataUtil.getStringValue(ruleData, "rfm_result_field_settings", "");

            List<RFMModels.RFMFieldResultSetting> rfmFieldResultSettingList = JSON.parseArray(rfmResultFieldSettings, RFMModels.RFMFieldResultSetting.class);
            Map<String, Map<String, List<String>>> fieldResultMap = getRFMFieldResultMap(rfmFieldResultSettingList);

            String actionObjectApiName = ObjectDataUtil.getStringValue(ruleData, "action_object_api_name", "");
            SearchTemplateQuery actionDataSearchQuery = getSearchTemplateQueryByCondition(ruleData, "action_object_condition", CREATE_TIME, true);
            SearchTemplateQuery sourceDataSearchQuery = getSearchTemplateQueryByCondition(ruleData, "source_object_condition", CREATE_TIME, true);
            List<IObjectData> insertResultDataList = Lists.newArrayList();
            List<IObjectData> updateActionDataList = Lists.newArrayList();
            String scoreFieldName = Strings.EMPTY;


            //获取时间字段，如果开启使用考勤规则
            RFMModels.TimeField timeField = findDateTimeField(ruleData,recencyFieldApiName,actionObjectApiName,sourceObjectApiName);
            //设置查询所需要的时间字段
            List<String> actionQueryFieldList = Lists.newArrayList("_id", "name", CREATE_TIME);
            if(ObjectUtils.isNotEmpty(timeField) && CollectionUtils.isNotEmpty(timeField.getActionObjectApiNameTimeField())){
                actionQueryFieldList.addAll(timeField.getActionObjectApiNameTimeField());
                skipHoliday("filters",actionDataSearchQuery,ruleData.get(ATTENDANCE_RULES_ID,String.class),user);
            }
            //设置查询所需要的时间字段
            List<String> sourceQueryFieldList = Lists.newArrayList("_id", "name", CREATE_TIME);
            if(ObjectUtils.isNotEmpty(timeField) && CollectionUtils.isNotEmpty(timeField.getSourceObjectApiNameTimeField())){
                sourceQueryFieldList.addAll(timeField.getSourceObjectApiNameTimeField());
                skipHoliday("filters",sourceDataSearchQuery,ruleData.get(ATTENDANCE_RULES_ID,String.class),user);
            }

            SearchTemplateQuery actionDataQuery = SearchUtil.copySearchTemplateQuery(actionDataSearchQuery);
            List<IFilter> actionDataFilters = Lists.newArrayList();
            SearchUtil.fillFilterEq(actionDataFilters, "_id", dataId);
            actionDataQuery.setFilters(actionDataFilters);

            List<IObjectData> actionDataList = getDataListBySearchQuery(user, actionObjectApiName, actionDataQuery, actionQueryFieldList);
            if(CollectionUtils.isEmpty(actionDataList)) {
                return;
            }
            IObjectData actionData = actionDataList.get(0);
            //命中优先级高规则,其他的规则就不匹配了
            ruleOfDataIdHaveRFMDataSet.add(actionData.getId());
            SearchTemplateQuery dataSearchQuery = SearchUtil.copySearchTemplateQuery(sourceDataSearchQuery);
            dataSearchQuery.setNeedReturnCountNum(true);
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, relationFieldApiName, actionData.getId());
            dataSearchQuery.setFilters(filters);
            rateLimiterService.getRfmSourceDataProcessLimiter().acquire();
            //获取取值数据
            QueryResult<IObjectData>  sourceDataQueryResult = getQueryResultBySearchQuery(user, sourceObjectApiName, dataSearchQuery, sourceQueryFieldList);

             if (sourceDataQueryResult == null || CollectionUtils.isEmpty(sourceDataQueryResult.getData())) {
                return;
            }
            changeValueTypeOfSearchTemplateQuery(1,sourceDataSearchQuery);

            filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, relationFieldApiName, actionData.getId());
            sourceDataSearchQuery.setFilters(filters);
            RFMModels.GetScoreByConditionResult recencyScore, frequencyScore, monetaryScore;
            if ("TotalQuantity".equals(frequencyType)) {
                frequencyScore = getScoreByCondition(frequencyScoreSettings, Double.valueOf(String.valueOf(sourceDataQueryResult.getTotalNumber())));
            } else {
                sourceDataSearchQuery.getOrders().clear();
                frequencyScore = getScoreByCondition(user, frequencyScoreSettings, frequencyFieldApiName, sourceDataSearchQuery);
            }
            log.warn("processRFMRule1 frequencyType:{},frequencyScore:{}", frequencyType, JSON.toJSONString(frequencyScore));

            if (frequencyScore.getScore() <= 0) {
                return;
            }

            monetaryScore = getScoreByCondition(user, monetaryScoreSettings, monetaryFieldApiName, sourceDataSearchQuery);
            log.warn("processRFMRule 2 monetaryScore:{}", JSON.toJSONString(monetaryScore));
            if (monetaryScore.getScore() <= 0) {
                return;
            }
            if(sourceDataQueryResult.getTotalNumber() > sourceDataSearchQuery.getLimit()) {
                recencyScore = getScoreByCondition(user, recencyScoreSettings, sourceDataSearchQuery,
                        sourceDataQueryResult.getData(), relationFieldApiName, actionData.getId(),
                        sourceObjectApiName,timeField.getSourceObjectApiNameTimeField(),ruleData);
                if (recencyScore.getScore() > 0) {
                    recencyScore.setRecencyDateTime(0L);
                    getRecencyDateTime(user, recencyScore, sourceDataSearchQuery, relationFieldApiName, actionData.getId(), recencyFieldApiName);
                }
            } else {
                recencyScore = getScoreByCondition(user, recencyScoreSettings, sourceDataQueryResult.getData(), recencyFieldApiName,ruleData);
            }
            log.warn("processRFMRule 3 recencyScore:{}", JSON.toJSONString(recencyScore));
            if (recencyScore.getScore() <= 0) {
                return;
            }
            String scoreString = String.format("%s%s%s", recencyScore.getScore(), frequencyScore.getScore(), monetaryScore.getScore());
            String scoreFieldValue = Strings.EMPTY;
            boolean findScoreResult = false;
            for (Map.Entry<String, Map<String, List<String>>> fieldResultEntry : fieldResultMap.entrySet()) {
                Map<String, List<String>> singleFieldResultMap = fieldResultEntry.getValue();
                for (Map.Entry<String, List<String>> entry : singleFieldResultMap.entrySet()) {
                    if (entry.getValue().contains(scoreString)) {
                        scoreFieldName = fieldResultEntry.getKey();
                        scoreFieldValue = entry.getKey();
                        findScoreResult = true;
                        break;
                    }
                }
                if (findScoreResult) {
                    break;
                }
            }

            log.warn("processRFMRule 4 findScoreResult:{}, scoreString:{},fieldResultMap:{}",findScoreResult,scoreString, JSON.toJSONString(fieldResultMap));
            if (findScoreResult) {
                RFMModels.RFMScoreResult rfmScoreResult = RFMModels.RFMScoreResult.builder()
                        .actionData(actionData).ruleData(ruleData)
                        .recencyScore(recencyScore).frequencyScore(frequencyScore).monetaryScore(monetaryScore)
                        .resultFieldApiName(scoreFieldName).resultFieldValue(scoreFieldValue).build();
                insertResultDataList.add(getRfmResultData(user, rfmScoreResult));
                updateActionDataList.add(getUpdateActionData(user, rfmScoreResult));
            }

            bulkSaveData(user, insertResultDataList);
            ObjectDataUtil.updateFields(user, updateActionDataList, Lists.newArrayList(scoreFieldName), true, true);
        } catch (Exception e) {
            log.error("processRFMRule-handMovement dataId:{} error:",dataId, e);
            throw e;
        }
    }

    private void processRFMRule(User user, IObjectData ruleData, Long processDate) {
        try {
            if(!checkRuleData(user, ruleData)) {
                return;
            }
            String sourceObjectApiName = ObjectDataUtil.getStringValue(ruleData, "source_object_api_name", "");
            String relationFieldApiName = ObjectDataUtil.getStringValue(ruleData, "relation_field_api_name", "");
            String recencyFieldApiName = ObjectDataUtil.getStringValue(ruleData, "recency_field_api_name", "");
            String frequencyType = ObjectDataUtil.getStringValue(ruleData, "frequency_type", "");
            String frequencyFieldApiName = ObjectDataUtil.getStringValue(ruleData, "frequency_field_api_name", "");
            String monetaryFieldApiName = ObjectDataUtil.getStringValue(ruleData, "monetary_field_api_name", "");
            String recencyScoreSettings = ObjectDataUtil.getStringValue(ruleData, "recency_score_settings", "");
            String frequencyScoreSettings = ObjectDataUtil.getStringValue(ruleData, "frequency_score_settings", "");
            String monetaryScoreSettings = ObjectDataUtil.getStringValue(ruleData, "monetary_score_settings", "");
            String rfmResultFieldSettings = ObjectDataUtil.getStringValue(ruleData, "rfm_result_field_settings", "");

            List<RFMModels.RFMFieldResultSetting> rfmFieldResultSettingList = JSON.parseArray(rfmResultFieldSettings, RFMModels.RFMFieldResultSetting.class);
            Map<String, Map<String, List<String>>> fieldResultMap = getRFMFieldResultMap(rfmFieldResultSettingList);

            String actionObjectApiName = ObjectDataUtil.getStringValue(ruleData, "action_object_api_name", "");

            SearchTemplateQuery actionDataSearchQuery = getSearchTemplateQueryByCondition(ruleData, "action_object_condition", CREATE_TIME, false);
            SearchTemplateQuery sourceDataSearchQuery = getSearchTemplateQueryByCondition(ruleData, "source_object_condition", CREATE_TIME, true);

            //获取时间字段，如果开启使用考勤规则
            RFMModels.TimeField timeField = findDateTimeField(ruleData,recencyFieldApiName,actionObjectApiName,sourceObjectApiName);
            String scoreFieldName = Strings.EMPTY;
            int index = 0;
            //获取源头数据时，出现异常的次数
            int catchTimes = 0;

            //设置查询所需要的时间字段
            List<String> actionQueryFieldList = Lists.newArrayList("_id", "name", CREATE_TIME);
            if(ObjectUtils.isNotEmpty(timeField) && CollectionUtils.isNotEmpty(timeField.getActionObjectApiNameTimeField())){
                actionQueryFieldList.addAll(timeField.getActionObjectApiNameTimeField());
                skipHoliday("filters",actionDataSearchQuery,ruleData.get(ATTENDANCE_RULES_ID,String.class),user);
            }
            //设置查询所需要的时间字段
            List<String> sourceQueryFieldList = Lists.newArrayList("_id", "name", CREATE_TIME);
            if(ObjectUtils.isNotEmpty(timeField) && CollectionUtils.isNotEmpty(timeField.getSourceObjectApiNameTimeField())){
                sourceQueryFieldList.addAll(timeField.getSourceObjectApiNameTimeField());
                skipHoliday("filters",sourceDataSearchQuery,ruleData.get(ATTENDANCE_RULES_ID,String.class),user);
            }
            //处理跳过节假日
            while (true) {
                try {
                    log.info("processRFMRuleGetSourceData,TenantId:{},ruleName:{}, Index;{}",user.getTenantId(),ruleData.getName(), index);
                    if (skipTenant(user.getTenantId())) {
                        log.error("processRFMRuleSkipTenant end TenantId:{}",user.getTenantId());
                        break;
                    }
                    SearchTemplateQuery actionDataQuery = copySearchTemplateQuery(actionDataSearchQuery);

                    actionDataQuery.setOffset(pageSize * index);
                    changeValueTypeOfSearchTemplateQuery(index,actionDataQuery);
                    List<IObjectData> actionDataList = getDataListBySearchQuery(user, actionObjectApiName, actionDataQuery,actionQueryFieldList);
                    if (CollectionUtils.isEmpty(actionDataList)) {
                        return;
                    }
                    //过滤已经计算过的数据
                    exceptProcessedObjectData(user, actionObjectApiName, actionDataList, processDate);
                    if (CollectionUtils.isEmpty(actionDataList)) {
                        index++;
                        continue;
                    }
                    rateLimiterService.getRfmActionDataProcessLimiter().acquire();
                    List<IObjectData> updateActionDataList = new ArrayList<>(pageSize);
                    List<IObjectData> insertResultDataList = new ArrayList<>(pageSize);
                    for (IObjectData actionData : actionDataList) {
                        log.debug("processRFMRuleSourceDataOfOne ruleName:{},dataId:{}",ruleData.getName(),actionData.getId());
                        try {
                            SearchTemplateQuery dataSearchQuery = SearchUtil.copySearchTemplateQuery(sourceDataSearchQuery);

                            dataSearchQuery.setNeedReturnCountNum(true);
                            List<IFilter> filters = Lists.newArrayList();
                            SearchUtil.fillFilterEq(filters, relationFieldApiName, actionData.getId());
                            dataSearchQuery.setFilters(filters);
                            rateLimiterService.getRfmSourceDataProcessLimiter().acquire();

                            changeValueTypeOfSearchTemplateQuery(1,dataSearchQuery);

                            QueryResult<IObjectData>  sourceDataQueryResult = getQueryResultBySearchQuery(user, sourceObjectApiName, dataSearchQuery, sourceQueryFieldList);

                            if (sourceDataQueryResult == null || CollectionUtils.isEmpty(sourceDataQueryResult.getData())) {
                                continue;
                            }
                            RFMModels.GetScoreByConditionResult recencyScore, frequencyScore, monetaryScore;

                            if ("TotalQuantity".equals(frequencyType)) {
                                frequencyScore = getScoreByCondition(frequencyScoreSettings, Double.valueOf(String.valueOf(sourceDataQueryResult.getTotalNumber())));
                            } else {
                                dataSearchQuery.getOrders().clear();
                                frequencyScore = getScoreByCondition(user, frequencyScoreSettings, frequencyFieldApiName, dataSearchQuery);
                            }
                            log.debug("processRFMRule 11 frequencyType:{},frequencyScore:{}",frequencyType,JSON.toJSONString(frequencyScore));
                            if (frequencyScore.getScore() <= 0) {
                                continue;
                            }

                            monetaryScore = getScoreByCondition(user, monetaryScoreSettings, monetaryFieldApiName, dataSearchQuery);
                            log.debug("processRFMRule 12  monetaryScore:{}", JSON.toJSONString(monetaryScore));
                            if (monetaryScore.getScore() <= 0) {
                                continue;
                            }

                            if (sourceDataQueryResult.getTotalNumber() > dataSearchQuery.getLimit()) {
                                recencyScore = getScoreByCondition(user, recencyScoreSettings, dataSearchQuery,
                                        sourceDataQueryResult.getData(), relationFieldApiName, actionData.getId(),
                                        sourceObjectApiName,timeField.getSourceObjectApiNameTimeField(),ruleData);
                                if(recencyScore.getScore() > 0) {
                                    recencyScore.setRecencyDateTime(0L);
                                    getRecencyDateTime(user, recencyScore, dataSearchQuery, relationFieldApiName, actionData.getId(), recencyFieldApiName);
                                }
                            } else {
                                recencyScore = getScoreByCondition(user, recencyScoreSettings, sourceDataQueryResult.getData(), recencyFieldApiName,ruleData);
                            }
                            log.debug("processRFMRule 13  recencyScore:{}", JSON.toJSONString(recencyScore));
                            if (recencyScore.getScore() <= 0) {
                                continue;
                            }
                            String scoreString = String.format("%s%s%s", recencyScore.getScore(), frequencyScore.getScore(), monetaryScore.getScore());
                            String scoreFieldValue = Strings.EMPTY;
                            boolean findScoreResult = false;
                            for (Map.Entry<String, Map<String, List<String>>> fieldResultEntry : fieldResultMap.entrySet()) {
                                Map<String, List<String>> singleFieldResultMap = fieldResultEntry.getValue();
                                for (Map.Entry<String, List<String>> entry : singleFieldResultMap.entrySet()) {
                                    if (entry.getValue().contains(scoreString)) {
                                        scoreFieldName = fieldResultEntry.getKey();
                                        scoreFieldValue = entry.getKey();
                                        findScoreResult = true;
                                        break;
                                    }
                                }
                                if (findScoreResult) {
                                    break;
                                }
                            }

                            log.debug("processRFMRule 4 findScoreResult:{}, scoreString:{},fieldResultMap:{}",findScoreResult,scoreString,JSON.toJSONString(fieldResultMap));
                            if (findScoreResult) {
                                RFMModels.RFMScoreResult rfmScoreResult = RFMModels.RFMScoreResult.builder()
                                        .actionData(actionData).ruleData(ruleData)
                                        .recencyScore(recencyScore).frequencyScore(frequencyScore).monetaryScore(monetaryScore)
                                        .resultFieldApiName(scoreFieldName).resultFieldValue(scoreFieldValue).build();
                                insertResultDataList.add(getRfmResultData(user, rfmScoreResult));
                                updateActionDataList.add(getUpdateActionData(user, rfmScoreResult));
                            }
                            if (insertResultDataList.size() >= batchSize) {
                                bulkSaveData(user, insertResultDataList);
                                ObjectDataUtil.updateFields(user, updateActionDataList, Lists.newArrayList(scoreFieldName), false, true);
                                insertResultDataList = Lists.newArrayList();
                                updateActionDataList = Lists.newArrayList();
                            }

                        } catch (Exception e) {
                            log.error("processRFMRuleCatch one insertResultDataList:{}",JSON.toJSONString(insertResultDataList),e);
                            insertResultDataList = Lists.newArrayList();
                            updateActionDataList = Lists.newArrayList();
                            //单条数据有问题，不能影响其他的数据计算，再次抛出异常
                            log.error("processRFMRuleCatch one data is catch,tenantId：{},assess_data_id:{} e:{}", user.getTenantId(), actionData.getId(), e.getMessage());
                        }
                    }

                    try {
                        bulkSaveData(user, insertResultDataList);
                        ObjectDataUtil.updateFields(user, updateActionDataList, Lists.newArrayList(scoreFieldName), false, true);
                        insertRFMTempCalculateResult(user, actionObjectApiName, actionDataList, processDate);
                    } catch (Exception e) {
                        //该批次处理异常，打印。继续下一批次
                        log.error("processRFMRuleUpdate is catch,tenantId：{} e:{}", user.getTenantId(), e.getMessage());
                    }

                    ++index;
                    if (index >= maxExecuteCount) {
                        return;
                    }
                    //异常次数处理
                    if(catchTimes > 0){
                        catchTimes = 0;
                    }
                }catch (Exception e){
                    log.error("processRFMRuleFindSourceData catchTimes:{} error:",catchTimes, e);
                    if(catchTimes >=3){
                        ++index;
                        if (index >= maxExecuteCount) {
                            return;
                        }
                        if(catchTimes > 10){
                            log.error("processRFMRuleFindSourceData catchTimes:{} error:",catchTimes, e);
                            throw e;
                        }
                    }
                    ++catchTimes;
                }
            }
        } catch (Exception e) {
            log.error("processRFMRuleTask  error:", e);
            throw e;
        }
    }

    private boolean checkRuleData(User user, IObjectData ruleData) {
        String sourceObjectApiName = ObjectDataUtil.getStringValue(ruleData, "source_object_api_name", "");
        IObjectDescribe objectDescribe = serviceFacade.findObject(user.getTenantId(), sourceObjectApiName);
        if(objectDescribe == null || !objectDescribe.isActive()) {
            return false;
        }

        String relationFieldApiName = ObjectDataUtil.getStringValue(ruleData, "relation_field_api_name", "");
        if(!checkFiledIsAvailable(objectDescribe, relationFieldApiName)) {
            return false;
        }

        String recencyFieldApiName = ObjectDataUtil.getStringValue(ruleData, "recency_field_api_name", "");
        if(!checkFiledIsAvailable(objectDescribe, recencyFieldApiName)) {
            return false;
        }

        String frequencyType = ObjectDataUtil.getStringValue(ruleData, "frequency_type", "");
        String frequencyFieldApiName = ObjectDataUtil.getStringValue(ruleData, "frequency_field_api_name", "");
        if("SummaryField".equals(frequencyType) && !checkFiledIsAvailable(objectDescribe, frequencyFieldApiName)) {
            return false;
        }

        String monetaryFieldApiName = ObjectDataUtil.getStringValue(ruleData, "monetary_field_api_name", "");
        if(!checkFiledIsAvailable(objectDescribe, monetaryFieldApiName)) {
            return false;
        }

        String rfmResultFieldSettings = ObjectDataUtil.getStringValue(ruleData, "rfm_result_field_settings", "");

        List<RFMModels.RFMFieldResultSetting> rfmFieldResultSettingList = JSON.parseArray(rfmResultFieldSettings, RFMModels.RFMFieldResultSetting.class);
        if(CollectionUtils.isEmpty(rfmFieldResultSettingList)) {
            return false;
        }

        String actionObjectApiName = ObjectDataUtil.getStringValue(ruleData, "action_object_api_name", "");
        objectDescribe = serviceFacade.findObject(user.getTenantId(), actionObjectApiName);
        if(objectDescribe == null || !objectDescribe.isActive()) {
            return false;
        }

        for(RFMModels.RFMFieldResultSetting resultSetting : rfmFieldResultSettingList) {
            if(!checkFiledIsAvailable(objectDescribe, resultSetting.getField_name())) {
                return false;
            }
        }

        return true;
    }

    protected List<IObjectData> getRFMRules (User user, String apiName) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(1000);
        searchTemplateQuery.setFindExplicitTotalNum(false);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setPermissionType(0);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, "tenant_id", user.getTenantId());
        SearchUtil.fillFilterEq(filters, "object_describe_api_name", RFM_RULE_OBJ_API_NAME);
        SearchUtil.fillFilterEq(filters, "action_object_api_name", apiName);
        SearchUtil.fillFilterEq(filters, "biz_status", "normal");
        SearchUtil.fillFilterEq(filters, "is_deleted", 0);
        searchTemplateQuery.setFilters(filters);
        return getDataListBySearchQuery(user, RFM_RULE_OBJ_API_NAME, searchTemplateQuery, null);
    }

    private void exceptProcessedObjectData(User user, String actionObjectApiName, List<IObjectData> objectDataList, Long processDate) {
        if(CollectionUtils.isEmpty(objectDataList)) {
            return;
        }
        List<String> dataIds = objectDataList.stream().map(x -> x.getId()).collect(Collectors.toList());
        String dataIdString = CommonUtil.buildSqlInString(dataIds);
        try {
            String queryString = String.format("SELECT assess_data_id FROM biz_rfm_temp_calculate_result  " +
                    "WHERE tenant_id='%s' AND action_object_api_name='%s' " +
                    "AND assess_data_id=any(array[%s]) AND last_recalculate_time>=%s  ", user.getTenantId(),
                    actionObjectApiName, dataIdString, processDate);

            List<Map> queryResult = objectDataService.findBySql(user.getTenantId(), queryString);
            if(CollectionUtils.isEmpty(queryResult)) {
                return;
            }

            List<String> calculatedIds = queryResult.stream().map(x -> String.valueOf(x.get("assess_data_id"))).collect(Collectors.toList());
            objectDataList.removeIf(x -> calculatedIds.contains(x.getId()));
        } catch (Exception e) {
            log.error("exceptProcessedObjectData", e);
        }
    }

    private void insertRFMTempCalculateResult(User user, String actionObjectApiName, List<IObjectData> objectDataList, Long processDate) {
        if(CollectionUtils.isEmpty(objectDataList)) {
            return;
        }
        try {
            List<Map<String, Object>> dataList = Lists.newArrayList();
            for (IObjectData data : objectDataList) {
                Map<String, Object> dataMap = Maps.newHashMap();
                dataMap.put("id", serviceFacade.generateId());
                dataMap.put("tenant_id", user.getTenantId());
                dataMap.put("action_object_api_name", actionObjectApiName);
                dataMap.put("assess_data_id", data.getId());
                dataMap.put("last_recalculate_time", processDate);
                dataList.add(dataMap);
            }
            CommonSqlUtil.insertData(user.getTenantId(), "biz_rfm_temp_calculate_result", dataList);
        } catch (Exception e) {
            log.error("insertRFMTempCalculateResult", e);
        }
    }

    private SearchTemplateQuery getSearchTemplateQueryByCondition(IObjectData ruleData, String key, String orderByField, boolean isAsc) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(pageSize);
        searchTemplateQuery.setFindExplicitTotalNum(false);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setPermissionType(0);
        String dataCondition = ObjectDataUtil.getStringValue(ruleData, key, "");
        JSONObject jsonObject = JSON.parseObject(dataCondition);
        String condition = jsonObject.getString("value").trim();
        List<Wheres> wheresList = Lists.newArrayList();
        if(StringUtils.isNotBlank(condition) && condition.startsWith("[") && condition.endsWith("]")) {
            List<Wheres> tmpWheresList = JSON.parseArray(condition, Wheres.class);
            wheresList.addAll(tmpWheresList);
           // searchTemplateQuery.setWheres(wheresList);
        }
        wheresTranferPatternAndIFilter(wheresList,searchTemplateQuery);
        if(StringUtils.isNotBlank(orderByField)) {
            OrderBy orderBy = new OrderBy(orderByField, isAsc);
            searchTemplateQuery.setOrders(Lists.newArrayList(orderBy));
        }
        return searchTemplateQuery;
    }

    private void wheresTranferPatternAndIFilter(List<Wheres> wheresList, SearchTemplateQuery searchTemplateQuery){
        if(CollectionUtils.isEmpty(wheresList)){
            return;
        }
        int startIndex = 1;
        List<IFilter> filters =new ArrayList<>();
        StringBuilder pattern = new StringBuilder();
        for (Wheres wheres : wheresList) {
            if (CollectionUtils.isEmpty(wheres.getFilters())) {
                continue;
            }
           if(startIndex !=1){
               pattern.append(" OR ");
           }
            for (IFilter filter : wheres.getFilters()) {
                pattern.append(startIndex).append(" AND ");
                startIndex = startIndex + 1;
                filters.add(filter);
            }
            pattern = new StringBuilder(pattern.substring(0, pattern.length() - 4));
            pattern = new StringBuilder(String.format(" (%s) ", pattern));
        }
        searchTemplateQuery.setFilters(filters);
        searchTemplateQuery.setPattern(pattern.toString());
    }

    private Map<String, Map<String, List<String>>> getRFMFieldResultMap(List<RFMModels.RFMFieldResultSetting> rfmFieldResultSettingList) {
        Map<String, Map<String, List<String>>> result = Maps.newHashMap();
        for(RFMModels.RFMFieldResultSetting setting : rfmFieldResultSettingList) {
            String fieldName = setting.getField_name();
            List<RFMModels.RFMSingleFieldResultSetting> singleFieldResultSettings = setting.getList();
            Map<String, List<String>> fieldResultMap = Maps.newHashMap();
            for(RFMModels.RFMSingleFieldResultSetting singleFieldResultSetting : singleFieldResultSettings) {
                String fieldValue = singleFieldResultSetting.getField_value();
                List<String> fieldResultList = Lists.newArrayList();
                for(Integer rec : singleFieldResultSetting.getRecency()) {
                    for(Integer fre : singleFieldResultSetting.getFrequency()) {
                        for(Integer mon : singleFieldResultSetting.getMonetary()) {
                            fieldResultList.add(String.format("%s%s%s", rec, fre, mon));
                        }
                    }
                }
                fieldResultMap.put(fieldValue, fieldResultList);
            }
            result.put(fieldName, fieldResultMap);
        }
        return result;
    }

    private void getRecencyDateTime (User user, RFMModels.GetScoreByConditionResult scoreResult,
                                     SearchTemplateQuery sourceDataSearchQuery, String relationFieldApiName,
                                     String relationDataId, String recencyFieldApiName) {
        if(scoreResult == null || scoreResult.getScore() <= 0 || scoreResult.getScoreSetting() == null) {
            return;
        }
        int index = 0;
        while (true) {
            if(skipTenant(user.getTenantId())) {
                return;
            }
            log.debug("getRecencyDateTime  sourceDataSearchQuery:{}",JSON.toJSONString(sourceDataSearchQuery));
            rateLimiterService.getRfmSourceDataProcessLimiter().acquire();
            SearchTemplateQuery dataSearchQuery = SearchUtil.copySearchTemplateQuery(sourceDataSearchQuery);
            dataSearchQuery.setNeedReturnCountNum(false);
            dataSearchQuery.setOffset(index * sourceDataSearchQuery.getLimit());
            dataSearchQuery.setWheres(scoreResult.getScoreSetting().getWheresList());
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, relationFieldApiName, relationDataId);
            dataSearchQuery.setFilters(filters);
            QueryResult<IObjectData> sourceDataQueryResult = getQueryResultBySearchQuery(user, scoreResult.getScoreSetting().getObject_apiname(), dataSearchQuery, Lists.newArrayList("_id", "name", CREATE_TIME));
            if (sourceDataQueryResult == null || CollectionUtils.isEmpty(sourceDataQueryResult.getData())) {
                log.debug("getRecencyDateTime  dataSearchQuery:{}",JSON.toJSONString(dataSearchQuery));
                return;
            }
            List<IObjectData> sourceDataList = sourceDataQueryResult.getData();

            List<String> dataIds = sourceDataList.stream().map(x -> x.getId()).collect(Collectors.toList());

            SearchTemplateQuery searchTemplateQuery = SearchUtil.copySearchTemplateQuery(sourceDataSearchQuery);
            searchTemplateQuery.setLimit(3);
            searchTemplateQuery.setFindExplicitTotalNum(false);
            searchTemplateQuery.setNeedReturnCountNum(false);
            searchTemplateQuery.setPermissionType(0);
            searchTemplateQuery.setWheres(scoreResult.getScoreSetting().getWheresList());
            List<IFilter> sortFilters = Lists.newArrayList();
            SearchUtil.fillFilterIn(sortFilters, "_id", dataIds);
            searchTemplateQuery.setFilters(sortFilters);
            OrderBy orderBy = new OrderBy(recencyFieldApiName, false);
            searchTemplateQuery.setOrders(Lists.newArrayList(orderBy));
            List<IObjectData> dataList = getDataListBySearchQuery(user, scoreResult.getScoreSetting().getObject_apiname(),
                    searchTemplateQuery, Lists.newArrayList("_id", recencyFieldApiName));
            if(CollectionUtils.isEmpty(dataList)) {
                log.debug("getRecencyDateTime  searchTemplateQuery:{}",JSON.toJSONString(searchTemplateQuery));
                return;
            }

            try {
                log.debug("getRecencyDateTime  dataList.get(0):{}",JSON.toJSONString(dataList.get(0)));
                Long recencyDateTime = Long.parseLong(String.valueOf(dataList.get(0).get(recencyFieldApiName)));
                if (Long.compare(recencyDateTime, scoreResult.getRecencyDateTime()) > 0) {
                    scoreResult.setRecencyDateTime(recencyDateTime);
                }
            } catch (Exception e) {
                log.error("getRecencyDateTime error", e);
            }
            ++index;
            if(index >= maxExecuteCount)  {
                return;
            }
        }
    }

    private RFMModels.GetScoreByConditionResult getScoreByCondition (User user, String scoreSettingString, List<IObjectData> objectDataList, String recencyFieldApiName,IObjectData ruleData) {
        List<RFMModels.RFMScoreSetting> scoreSettingList = getSortedRfmScoreSettingList(scoreSettingString);
        return getScoreByCondition(user, scoreSettingList, objectDataList, recencyFieldApiName,ruleData);
    }

    private RFMModels.GetScoreByConditionResult getScoreByCondition (User user, List<RFMModels.RFMScoreSetting> scoreSettingList, List<IObjectData> objectDataList, String recencyFieldApiName,IObjectData ruleData) {
        RFMModels.GetScoreByConditionResult result = RFMModels.GetScoreByConditionResult.builder().build();
        if (CollectionUtils.isEmpty(scoreSettingList)) {
            return result;
        }
        List<String> dataIds = objectDataList.stream().map(x -> x.getId()).collect(Collectors.toList());
        for(RFMModels.RFMScoreSetting scoreSetting : scoreSettingList) {
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            searchTemplateQuery.setLimit(3);
            searchTemplateQuery.setFindExplicitTotalNum(false);
            searchTemplateQuery.setNeedReturnCountNum(false);
            searchTemplateQuery.setPermissionType(0);
            searchTemplateQuery.setWheres(scoreSetting.getWheresList());
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterIn(filters, "_id", dataIds);
            searchTemplateQuery.setFilters(filters);
            List<String> getFieldList = Lists.newArrayList("_id", "name", CREATE_TIME);
            if(StringUtils.isNotBlank(recencyFieldApiName)) {
                getFieldList.add(recencyFieldApiName);
                OrderBy orderBy = new OrderBy(recencyFieldApiName, false);
                searchTemplateQuery.setOrders(Lists.newArrayList(orderBy));
            }
            //是否开启节假期
            if(handleIsHaveAttendance(ruleData,null)){
                skipHoliday("where",searchTemplateQuery,ruleData.get(ATTENDANCE_RULES_ID,String.class),user);
            }
            List<IObjectData> dataList = getDataListBySearchQuery(user, scoreSetting.getObject_apiname(), searchTemplateQuery, getFieldList);
            if(CollectionUtils.isNotEmpty(dataList)) {
                result.setScoreSetting(scoreSetting);
                if(StringUtils.isNotBlank(recencyFieldApiName)) {
                    try {
                        Long recencyDateTime = Long.parseLong(String.valueOf(dataList.get(0).get(recencyFieldApiName)));
                        result.setRecencyDateTime(recencyDateTime);
                    } catch (Exception e) {
                        log.error("getRecencyDateTime error", e);
                    }
                }
                return result;
            }
        }
        return result;
    }

    private RFMModels.GetScoreByConditionResult getScoreByCondition (User user, String scoreSettingString, SearchTemplateQuery sourceDataSearchQuery,
                                     List<IObjectData> objectDataList, String relationFieldApiName,
                                     String relationDataId, String sourceObjectApiName,
                                     List<String> findShowTimeFieldList,IObjectData ruleData) {
        RFMModels.GetScoreByConditionResult result = RFMModels.GetScoreByConditionResult.builder().build();
        List<RFMModels.RFMScoreSetting> scoreSettingList = getSortedRfmScoreSettingList(scoreSettingString);
        if(CollectionUtils.isEmpty(scoreSettingList)) {
            return result;
        }
        result = getScoreByCondition(user, scoreSettingList, objectDataList, "",ruleData);
        if(scoreSettingList.get(0).getScore().compareTo(result.getScore()) == 0) {
            return result;
        }
        List<String> findFieldList = setQueryFieldList(findShowTimeFieldList);
        int index = 1;
        while (true) {
            if(skipTenant(user.getTenantId())) {
                return result;
            }
            rateLimiterService.getRfmSourceDataProcessLimiter().acquire();
            SearchTemplateQuery dataSearchQuery = SearchUtil.copySearchTemplateQuery(sourceDataSearchQuery);
            dataSearchQuery.setNeedReturnCountNum(false);
            dataSearchQuery.setOffset(index * sourceDataSearchQuery.getLimit());
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, relationFieldApiName, relationDataId);
            dataSearchQuery.setFilters(filters);
            QueryResult<IObjectData> sourceDataQueryResult = getQueryResultBySearchQuery(user, sourceObjectApiName, dataSearchQuery, findFieldList);
            if (sourceDataQueryResult == null || CollectionUtils.isEmpty(sourceDataQueryResult.getData())) {
                return result;
            }
            List<IObjectData> sourceData = sourceDataQueryResult.getData();
            RFMModels.GetScoreByConditionResult tmpScore = getScoreByCondition(user, scoreSettingList, sourceData, "",ruleData);
            if(tmpScore.getScore() > result.getScore()) {
                result = tmpScore;
            }
            if(scoreSettingList.get(0).getScore().compareTo(result.getScore()) == 0) {
                return result;
            }
            ++index;
            if(index >= maxExecuteCount)  {
                return result;
            }
        }
    }

    private RFMModels.GetScoreByConditionResult getScoreByCondition (User user, String scoreSettingString, String fieldApiName, SearchTemplateQuery dataSearchQuery) {
        RFMModels.GetScoreByConditionResult result = RFMModels.GetScoreByConditionResult.builder().build();
        List<RFMModels.RFMScoreSetting> scoreSettingList = getSortedRfmScoreSettingList(scoreSettingString);
        if (CollectionUtils.isEmpty(scoreSettingList)) {
            return result;
        }
        SearchTemplateQuery searchTemplateQuery = SearchUtil.copySearchTemplateQuery(dataSearchQuery);
        searchTemplateQuery.getOrders().clear();
        BigDecimal sumResult = metaDataComputeService.getAggregateResult(user, scoreSettingList.get(0).getObject_apiname(), fieldApiName, "sum",
                    6, searchTemplateQuery);

        result = getScoreByCondition(scoreSettingList, sumResult.doubleValue());
        return result;
    }

    private RFMModels.GetScoreByConditionResult getScoreByCondition (String scoreSettingString, Double value) {
        List<RFMModels.RFMScoreSetting> scoreSettingList = getSortedRfmScoreSettingList(scoreSettingString);
        return getScoreByCondition(scoreSettingList, value);
    }

    private RFMModels.GetScoreByConditionResult getScoreByCondition (List<RFMModels.RFMScoreSetting> scoreSettingList, Double value) {
        RFMModels.GetScoreByConditionResult result = RFMModels.GetScoreByConditionResult.builder()
                .evaluateValue(value).build();
        if (CollectionUtils.isEmpty(scoreSettingList)) {
            return result;
        }
        for(RFMModels.RFMScoreSetting scoreSetting : scoreSettingList) {
            List<Wheres> wheresList = scoreSetting.getWheresList();
            for (Wheres wheres : wheresList) {
                List<IFilter> filters = wheres.getFilters();
                if (CollectionUtils.isEmpty(filters)) {
                    result.setScoreSetting(scoreSetting);
                    return result;
                }
                for (IFilter filter : filters) {
                    if (Operator.IS.equals(filter.getOperator())) {
                        //为空
                        if(value == null) {
                            result.setScoreSetting(scoreSetting);
                            return result;
                        }
                       continue;
                    } else if (Operator.ISN.equals(filter.getOperator())) {
                        //不为空
                        if(value != null) {
                            result.setScoreSetting(scoreSetting);
                            return result;
                        }
                        continue;
                    }
                    if(CollectionUtils.isEmpty(filter.getFieldValues())) {
                        return result;
                    }
                    String fieldValue = filter.getFieldValues().get(0);
                    if (StringUtils.isBlank(fieldValue)) {
                        continue;
                    }
                    Double doubleValue = Double.valueOf(fieldValue);
                    int compareResult = value.compareTo(doubleValue);
                    if (Operator.EQ.equals(filter.getOperator()) && compareResult == 0) {
                        //等于
                        result.setScoreSetting(scoreSetting);
                        return result;
                    } else if (Operator.N.equals(filter.getOperator()) && compareResult != 0) {
                        //不等于
                        result.setScoreSetting(scoreSetting);
                        return result;
                    } else if (Operator.GT.equals(filter.getOperator()) && compareResult > 0) {
                        //大于
                        result.setScoreSetting(scoreSetting);
                        return result;
                    } else if (Operator.GTE.equals(filter.getOperator()) && compareResult >= 0) {
                        //大于等于
                        result.setScoreSetting(scoreSetting);
                        return result;
                    } else if (Operator.LT.equals(filter.getOperator()) && compareResult < 0) {
                        //小于
                        result.setScoreSetting(scoreSetting);
                        return result;
                    } else if (Operator.LTE.equals(filter.getOperator()) && compareResult <= 0) {
                        //小于等于
                        result.setScoreSetting(scoreSetting);
                        return result;
                    }
                }
            }
        }
        return result;
    }

    private List<RFMModels.RFMScoreSetting> getSortedRfmScoreSettingList(String scoreSettingString) {
        if(StringUtils.isBlank(scoreSettingString)) {
            return Lists.newArrayList();
        }
        if(!scoreSettingString.startsWith("[") || !scoreSettingString.endsWith("]")) {
            return Lists.newArrayList();
        }

        List<RFMModels.RFMScoreSetting> scoreSettingList = JSON.parseArray(scoreSettingString, RFMModels.RFMScoreSetting.class);
        if(CollectionUtils.isEmpty(scoreSettingList)) {
            return Lists.newArrayList();
        }
        Collections.sort(scoreSettingList, (o1, o2) -> {
            return Integer.compare(o2.getScore(), o1.getScore());
        });
        return scoreSettingList;
    }

    private boolean checkFiledIsAvailable(IObjectDescribe objectDescribe, String fieldApiName) {
        IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(fieldApiName);
        if(fieldDescribe == null || !fieldDescribe.isActive() || "deleted".equals(fieldDescribe.getStatus())) {
            return false;
        }
        return true;
    }

    private List<IObjectData> getDataListBySearchQuery(User user, String apiName, SearchTemplateQuery searchTemplateQuery, List<String> fieldList) {
        QueryResult<IObjectData> queryResult = getQueryResultBySearchQuery(user, apiName, searchTemplateQuery, fieldList);
        if(queryResult != null && CollectionUtils.isNotEmpty(queryResult.getData())) {
            return queryResult.getData();
        }
        return Lists.newArrayList();
    }

    private QueryResult<IObjectData> getQueryResultBySearchQuery(User user, String apiName, SearchTemplateQuery searchTemplateQuery, List<String> fieldList) {
        IActionContext actionContext = ActionContextUtil.createSearchActionContext(user, true, false);
        QueryResult<IObjectData> queryResult;
        if(CollectionUtils.isEmpty(fieldList)) {
            queryResult = serviceFacade.findBySearchQuery(actionContext, apiName, searchTemplateQuery);
        } else {
            queryResult = serviceFacade.findBySearchTemplateQueryWithFields(actionContext, apiName, searchTemplateQuery, fieldList);
        }
        return queryResult;
    }

    private IObjectData getRfmResultData(User user, RFMModels.RFMScoreResult rfmScoreResult) {
        IObjectData objectData = new ObjectData();
        objectData.set("tenant_id", user.getTenantId());
        objectData.set("name", ObjectDataUtil.getNameCode());
        objectData.set("record_type", "default__c");
        objectData.set("_id", serviceFacade.generateId());
        objectData.set("rule_id", rfmScoreResult.getRuleData().getId());
        objectData.set("action_object_api_name", rfmScoreResult.getActionObjectApiName());
        objectData.set("assess_data_id", rfmScoreResult.getActionData().getId());
        objectData.set("recency_score", rfmScoreResult.getRecencyScore().getScore());
        objectData.set("recency_deal_time", rfmScoreResult.getRecencyScore().getRecencyDateTime());
        objectData.set("frequency_score", rfmScoreResult.getFrequencyScore().getScore());
        objectData.set("frequency_sum", rfmScoreResult.getFrequencyScore().getEvaluateValue());
        objectData.set("monetary_score", rfmScoreResult.getMonetaryScore().getScore());
        objectData.set("monetary_sum", rfmScoreResult.getMonetaryScore().getEvaluateValue());
        objectData.set("result_field_api_name", rfmScoreResult.getResultFieldApiName());
        objectData.set("result_field_value", rfmScoreResult.getResultFieldValue());
        objectData.set("owner", Lists.newArrayList(user.getUserId()));
        objectData.set("life_status", "normal");
        objectData.set("lock_status", "0");
        objectData.set("package", "CRM");
        objectData.set("object_describe_api_name", "RFMAssessResultObj");
        objectData.set("is_deleted", false);
        return objectData;
    }

    private IObjectData getUpdateActionData(User user, RFMModels.RFMScoreResult rfmScoreResult) {
        IObjectData objectData = ObjectDataUtil.buildObjectData(user.getTenantId(), rfmScoreResult.getActionObjectApiName(), rfmScoreResult.getActionData().getId());
        objectData.set(rfmScoreResult.getResultFieldApiName(), rfmScoreResult.getResultFieldValue());
        return objectData;
    }

    private void bulkSaveData(User user, List<IObjectData> dataList) {
        if(CollectionUtils.isEmpty(dataList)) {
            return;
        }
        serviceFacade.bulkSaveObjectData(dataList, user, true, true, x -> {
            return ActionContextExt.of(user)
                    .setNotValidate(true)
                    .getContext();
        });
    }

    private boolean skipTenant(String tenantId) {
        return GrayUtil.isGrayEnable("skip_rfm_process", tenantId);
    }

    private SearchTemplateQuery copySearchTemplateQuery(SearchTemplateQuery searchTemplateQuery) {
        SearchTemplateQuery dataSearchQuery = new SearchTemplateQuery();
        dataSearchQuery.setLimit(searchTemplateQuery.getLimit());
        dataSearchQuery.setFindExplicitTotalNum(searchTemplateQuery.getFindExplicitTotalNum());
        dataSearchQuery.setNeedReturnCountNum(searchTemplateQuery.getNeedReturnCountNum());
        dataSearchQuery.setPermissionType(searchTemplateQuery.getPermissionType());
        dataSearchQuery.setWheres(Lists.newArrayList(searchTemplateQuery.getWheres()));
        dataSearchQuery.setFilters(searchTemplateQuery.getFilters());
        dataSearchQuery.setOrders(searchTemplateQuery.getOrders());
        dataSearchQuery.setSearchSource(searchTemplateQuery.getSearchSource());
        dataSearchQuery.setPattern(searchTemplateQuery.getPattern());
        return dataSearchQuery;
    }

    /**
     * 获取时间字段
     * @param ruleData
     */
    private RFMModels.TimeField findDateTimeField(IObjectData ruleData,String recencyFieldApiName, String actionObjectApiName,String sourceObjectApiName){
        RFMModels.TimeField timeField = RFMModels.TimeField.builder().build();
        if(handleIsHaveAttendance(ruleData,null)){
            //设置最近交易时间
            timeField.setRecencyFieldApiName(recencyFieldApiName);
            //适用对象-过滤筛选条件中时间字段
            List<Wheres> actionObjectConditionWheres = dataConditionTransferWheres(ruleData,"action_object_condition");
            timeField.setActionObjectApiNameTimeField(findDateTimeField(actionObjectConditionWheres,ruleData.getTenantId(),actionObjectApiName));
            //取值对象-过滤筛选条件中时间字段
            List<Wheres> sourceObjectConditionWheres = dataConditionTransferWheres(ruleData,"source_object_condition");
            timeField.setSourceObjectApiNameTimeField(findDateTimeField(sourceObjectConditionWheres,ruleData.getTenantId(),sourceObjectApiName));
        }
        return timeField;
    }

    /**
     * Condition转换为Wheres
     */
    private List<Wheres> dataConditionTransferWheres(IObjectData ruleData, String key){
        String dataCondition = ObjectDataUtil.getStringValue(ruleData, key, "");
        JSONObject jsonObject = JSON.parseObject(dataCondition);
        String condition = jsonObject.getString("value").trim();
        List<Wheres> wheresList = Lists.newArrayList();
        if(StringUtils.isNotBlank(condition) && condition.startsWith("[") && condition.endsWith("]")) {
            List<Wheres> tmpWheresList = JSON.parseArray(condition, Wheres.class);
            wheresList.addAll(tmpWheresList);
        }
        return wheresList;
    }

    private IObjectDescribe getIObjectDescribe(String tenantId ,String objectApiName){
        try {
            return objectDescribeService.findByTenantIdAndDescribeApiName(tenantId,objectApiName);
        } catch (MetadataServiceException e) {
            throw new RuntimeException(e);
        }
    }

    private List<String> findDateTimeField(List<Wheres> actionObjectConditionWheres,String tenantId,String objectApiName){
        List<String> list = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(actionObjectConditionWheres)){
            IObjectDescribe iObjectDescribe =getIObjectDescribe(tenantId,objectApiName);
            Map<String, ObjectFieldDescribeDocument> fields = (Map<String, ObjectFieldDescribeDocument>) iObjectDescribe.get("fields");
            actionObjectConditionWheres.stream().forEach(wheres -> {
                if(CollectionUtils.isNotEmpty(wheres.getFilters())){
                    wheres.getFilters().stream().forEach(filters->{
                        if("date_time".equals(((Map)fields.get(filters.getFieldName())).get("type").toString())){
                            list.add(filters.getFieldName());
                        }
                    });
                }
            });
        }
        return list;
    }

    /**
     * 根据考勤规则id查询时间区间有哪些工作日
     */
    private List<String> getSectionIsWorkByRuleId(User user,long startTime,long endTime,String attendanceRulesId){
        CustomProxyModel.Arg arg =CustomProxyModel.Arg.builder().build();
        arg.setEa(user.getTenantId());
        arg.setRuleIdList(Lists.newArrayList(attendanceRulesId));
        arg.setStartDate(DateUtil.getDateString(startTime));
        arg.setEndDate(DateUtil.getDateString(endTime));
        CustomProxyModel.Result result = customProxy.getSectionIsWorkByRuleId(HttpHeaderUtil.getHeaders(user),arg);
        if(ObjectUtils.isEmpty(result) || !result.getErrorCode().equals(0)){
            log.error("getSectionIsWorkByRuleId error e:{}",JSON.toJSONString(result));
            throw new RuntimeException();
        }
        if(ObjectUtils.isNotEmpty(result.getRuleIdAndWorkMap()) && result.getRuleIdAndWorkMap().containsKey(attendanceRulesId)){
            return result.getRuleIdAndWorkMap().get(attendanceRulesId);
        }
        return Lists.newArrayList();
    }

    /**
     * 获取考勤规则详细
     * @param tenantId
     * @param attendanceRulesId
     * @return
     */
    private void getDetailByRuleId(String tenantId,String attendanceRulesId){
        try {
            CustomProxyModel.Arg arg =CustomProxyModel.Arg.builder().build();
            arg.setEa(tenantId);
            arg.setRuleId(attendanceRulesId);
            Object o= customProxy.GetDetailByRuleId(HttpHeaderUtil.getHeaders(new User(tenantId, User.SUPPER_ADMIN_USER_ID)),arg);
            log.warn("rfm->handleIsHaveAttendance tenantId:{},attendance_rules_id:{},ruleDetails:{}",tenantId,attendanceRulesId,JSON.toJSONString(o));
        }catch (Exception e){
            log.warn("rfm->getDetailByRuleId error tenantId:{}",tenantId,e);
        }

    }

    /**
     * 是否绑定考勤规则
     * @param ruleData
     * @param sourceQueryFieldList
     * @return
     */
    private boolean handleIsHaveAttendance(IObjectData ruleData,List<String> sourceQueryFieldList){
        if(CollectionUtils.isEmpty(sourceQueryFieldList)){
            if(ruleData.containsField(ATTENDANCE_RULES_ID) && ObjectUtils.isNotEmpty(ruleData.get(ATTENDANCE_RULES_ID))){
               getDetailByRuleId(ruleData.getTenantId(),ruleData.get(ATTENDANCE_RULES_ID,String.class));
                return true;
            }
        }else {
            if(ruleData.containsField(ATTENDANCE_RULES_ID) && ObjectUtils.isNotEmpty(ruleData.get(ATTENDANCE_RULES_ID)) && CollectionUtils.isNotEmpty(sourceQueryFieldList)){
                return true;
            }
        }
        return false;
    }

    /**
     *
     * @param searchTemplateQuery
     * @param attendanceRulesId
     * @param user
     * @return
     */
    private void skipHoliday(String type,SearchTemplateQuery searchTemplateQuery,String attendanceRulesId,User user){
        if("where".equals(type)){
            searchTemplateQuery.getWheres().stream().forEach(where->{
                where.getFilters().stream().forEach(filter->{
                    setFilterValue(filter,attendanceRulesId,user);
                });
            });
        }else{
            searchTemplateQuery.getFilters().stream().forEach(filter-> setFilterValue(filter,attendanceRulesId,user));
        }
    }

    private void setFilterValue(IFilter filter,String attendanceRulesId,User user){
        if(ObjectUtils.isNotEmpty(filter.getValueType()) && filter.getValueType()==3 && filter.getFieldValues().contains("day")){
            if("LTE".equals(filter.getOperator().toString()) ){
                //前N天、前N周、前N月
                Integer days = recursionSkipHoliday(1,DateUtil.getDateByStr(),attendanceRulesId,user,filter.getFieldValues().get(0),filter.getFieldValues().get(1));
                filter.getFieldValues().clear();
                filter.setFieldValues(Lists.newArrayList("day",String.valueOf(days)));
            }else if("GTE".equals(filter.getOperator().toString())){
                //后N天、后N周、后N月
                Integer days = recursionSkipHoliday(2,DateUtil.getDateByStr(),attendanceRulesId,user,filter.getFieldValues().get(0),filter.getFieldValues().get(1));
                filter.getFieldValues().clear();
                filter.setFieldValues(Lists.newArrayList("day",String.valueOf(days)));
            }else if("LT".equals(filter.getOperator().toString()) ){
                //N天前
                Integer days = recursionSkipHoliday2(1,DateUtil.getDateByStr(),attendanceRulesId,user,filter.getFieldValues().get(0),filter.getFieldValues().get(1));
                filter.getFieldValues().clear();
                filter.setFieldValues(Lists.newArrayList("day",String.valueOf(days)));
            }else if("GT".equals(filter.getOperator().toString()) ){
                //N天后
                Integer days = recursionSkipHoliday2(2,DateUtil.getDateByStr(),attendanceRulesId,user,filter.getFieldValues().get(0),filter.getFieldValues().get(1));
                filter.getFieldValues().clear();
                filter.setFieldValues(Lists.newArrayList("day",String.valueOf(days)));
            }
        }
    }

    /**
     * 递归跳过节假日，适用于 //前N天、前N周、前N月   //后N天、后N周、后N月，不包含当天
     * @param currentDate
     * @param attendanceRulesId
     * @param user
     * @param type
     * @param day
     * @return
     */
    private Integer recursionSkipHoliday(Integer dayType,Date currentDate,String attendanceRulesId,User user,String type,String day){
        Date lastData = null;
        Date startTime = null;
        Date endTime = null;
        if(dayType==1){
            lastData = DateUtil.getDateByTheLastNOfDay(currentDate,type,day);
            startTime = lastData;
            endTime = DateUtil.getDateByTheLastNOfDay(currentDate,"day","1");
        }else {
            lastData = DateUtil.getDateByNdaysAfterObtaining(currentDate,type,day);
            startTime = DateUtil.getDateByNdaysAfterObtaining(currentDate,"day","1");
            endTime = lastData;
        }
        List<String> dayList = getSectionIsWorkByRuleId(user,startTime.getTime(),endTime.getTime(),attendanceRulesId);
        Integer days = Math.abs(DateUtil.calculateTheNumberOfDaysBetweenTwoDates(currentDate,lastData));
        if(days==0){
            return days;
        }
        if(CollectionUtils.isNotEmpty(dayList)){
            if(dayList.size()>=days){
                return days;
            }else{
                return days + recursionSkipHoliday(dayType,lastData,attendanceRulesId,user,"day",String.valueOf(days-dayList.size()));
            }
        }else{
            return days + recursionSkipHoliday(dayType,lastData,attendanceRulesId,user,type,day);
        }
    }

    /**
     * 递归跳过节假日，适用于 //N天前    //N天后 包含当天
     * @param currentDate
     * @param attendanceRulesId
     * @param user
     * @param type
     * @param day
     * @return
     */
    private Integer recursionSkipHoliday2(Integer dayType,Date currentDate,String attendanceRulesId,User user,String type,String day){
        Date lastData = null;
        Date startTime = null;
        Date endTime = null;
        if(dayType==1){
            lastData = DateUtil.getDateByTheLastNOfDay(currentDate,type,String.valueOf(Integer.parseInt(day)-1));
            startTime = lastData;
            endTime = currentDate;
            lastData = DateUtil.getDateByTheLastNOfDay(lastData,"day","1");
        }else {
            lastData = DateUtil.getDateByNdaysAfterObtaining(currentDate,type,String.valueOf(Integer.parseInt(day)-1));
            startTime = currentDate;
            endTime = lastData;
            lastData = DateUtil.getDateByNdaysAfterObtaining(lastData,"day","1");
        }
        List<String> dayList = getSectionIsWorkByRuleId(user,startTime.getTime(),endTime.getTime(),attendanceRulesId);
        Integer days = Math.abs(DateUtil.calculateTheNumberOfDaysBetweenTwoDates(currentDate,lastData));
        if(days==0){
            return days;
        }
        if(CollectionUtils.isNotEmpty(dayList)){
            if(dayList.size()>=days){
                return days;
            }else{
                return days + recursionSkipHoliday2(dayType,lastData,attendanceRulesId,user,"day",String.valueOf(days-dayList.size()));
            }
        }else{
            return days + recursionSkipHoliday2(dayType,lastData,attendanceRulesId,user,type,day);
        }
    }

    /**
     *  //N天前 //N天后 包含当天，前N天、前N周、前N月 //后N天、后N周、后N月，不包含当天
     *  对于上边这几种操作，分页查询时，FieldValues被底层改了，所以在第二页时候 ValueType相对应的改成0
     *  底层对接人 钱凌锋
     * @param index
     * @param searchTemplateQuery
     */
    private void changeValueTypeOfSearchTemplateQuery(Integer index,SearchTemplateQuery searchTemplateQuery){
        if(index == 1){
            searchTemplateQuery.getFilters().stream().forEach(filters->{
                if(OPERATION_LIST.contains(filters.getOperator().toString()) && filters.getValueType()==3 && filters.getFieldValues().size() == 1){
                    filters.setValueType(0);
                }
            });
        }

    }

    public List<String> setQueryFieldList(List<String> findShowTimeFieldList){
        List<String> findFieldList = Lists.newArrayList("_id", "name", CREATE_TIME);
        if(CollectionUtils.isNotEmpty(findShowTimeFieldList)){
            findFieldList.addAll(findShowTimeFieldList);
        }
        return findFieldList;
    }
}
