package com.facishare.crm.sfa.lto.enums;

public enum LeadsBizStatusEnum {

    /**
     * 未分配
     */
    UN_ASSIGNED("un_assigned", "未分配"),
    UN_PROCESSED("un_processed", "待处理"),

    TRANSFORMED("transformed", "已转换"),
    PROCESSED("processed", "跟进中"),
    /**
     * 已分配
     */
    CLOSED("closed", "无效");


    private final String value;
    private final String label;

    LeadsBizStatusEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }
}
