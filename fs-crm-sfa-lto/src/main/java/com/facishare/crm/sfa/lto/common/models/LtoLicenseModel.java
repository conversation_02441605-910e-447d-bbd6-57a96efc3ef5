package com.facishare.crm.sfa.lto.common.models;


import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

public interface LtoLicenseModel {
    @Data
    class CheckModuleLicenseExistArg{
        private String packageName;
    }

    @Data
    @Builder
    class CheckModuleLicenseExistResult{
        private Boolean hasLicense;
    }

    @Data
    class CheckModulesLicenseExistArg{
        private List<String> packageNameList;
    }

    @Data
    @Builder
    class CheckModulesLicenseExistResult{
        private Map<String, Boolean> licenseResult;
    }
}
