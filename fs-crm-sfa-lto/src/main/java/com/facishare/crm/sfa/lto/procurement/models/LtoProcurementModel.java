package com.facishare.crm.sfa.lto.procurement.models;

import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName com.facishare.crm.sfa.lto.procurement.models.ProcurementModel
 * @Description
 * <AUTHOR>
 * @Date 2022/10/18 17:44
 * @Version 1.0
 **/
public interface LtoProcurementModel {
    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class ProcurementMessage{
        private String tenantId;

        private List<String> objectIds;
        /**
         * 1:新建 2:转换
         */
        private Integer opType;
        private String apiName;

        private String userId;
        private Boolean outUser;
        private String outUserId;
        private String outTenantId;

        private String id;
        private String srcId;
        /**
         * 招标单位
         */
        private List<String> callerCreditCode;
        private List<String> callerName;
        /**
         * 中标单位
         */
        private List<String> winnerCreditCode;
        private List<String> winnerName;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class ComparisonItem {
        /**
         * json解析的对象
         */
        private PCBean pcBean;
        private String tenantId;
        /**
         * 统一w信用代码
         */
        private List<String> creditCodes;
        /**
         * 名称
         */
        private List<String> names;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class ComparisonResult {
        List<String> c_status;
        List<String> w_status;
        Map<String, List<String>> idsMap;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class PCBean {
        String name;
        List<InfoBean> infos;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class InfoBean{
        String api_name;
        String label;
        boolean select;
        boolean cancel;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class JoinBean {
        String id;
        String name;
    }

    /**
     * 公告转换对象，详情见文档
     * https://wiki.firstshare.cn/pages/viewpage.action?pageId=185439540#id-%E6%8B%9B%E6%8A%95%E6%A0%87%E6%95%B0%E6%8D%AE%E8%8E%B7%E5%AE%A2%EF%BC%88%E4%BA%8C%E6%9C%9F%EF%BC%89-2.4.6%E6%96%B0%E5%BB%BA%E3%80%90%E6%8B%9B%E6%8A%95%E6%A0%87%E5%85%AC%E5%91%8A%E8%BD%AC%E6%8D%A2%E8%AE%B0%E5%BD%95%E3%80%91%E5%AF%B9%E8%B1%A1
     */
    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class PTLBean {
        String id;
        String name;

        //企业库名称,enterprise_info_id,查找关联【企业库】
        String enterpriseInfoId;
        //客户名称,account_id,查找关联【客户】
        String accountId;
        //线索姓名,leads_id,查找关联【销售线索】
        String leadsId;
        //商机2.0名称,new_opportunity_id,查找关联【商机2.0】
        String newOpportunityId;
        //竞争对手客户名称,competitor_id,查找关联【竞争对手】
        String competitorId;
        //合作伙伴名称,partner_id,查找关联【合作伙伴】
        String partnerId;

        //公告名称,procurement_info_id,查找关联【招投标公告信息】
        String procurementInfoId;
        //公告类型,info_type,引用【招投标公告信息】-公告类型
        String info_type;

        //主体名称,procurement_enterprise_id,【招投标主体信息】
        String procurementEnterpriseId;
        //主体类型,enterprise_type,【招投标主体信息】主体类型
        String enterpriseType;

        //关联类型,related_type
        String relatedType;
    }

    @AllArgsConstructor
    enum OPJApiEnum{
        Enterprise("EnterpriseInfoObj", "企业库"),
        Account("AccountObj", "客户"),
        Leads("LeadsObj", "销售线索"),
        Competitor("CompetitorObj", "竞争对手"),
        Partner("PartnerObj", "合作伙伴"),
        WinnerJoin("WinnerJoinObj", "中标单位与拟定参标方"), Caller("CallerObj", "招标单位"), Winner("WinnerObj", "中标单位"),
        ProcurementTransferLog("ProcurementTransferLog", "公告转换对象");
        final public String apiName, value;
    }
}
