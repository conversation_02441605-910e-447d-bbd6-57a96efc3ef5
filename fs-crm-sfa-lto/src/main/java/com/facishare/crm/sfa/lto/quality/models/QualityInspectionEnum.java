package com.facishare.crm.sfa.lto.quality.models;

import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2023/02/14 17:44
 * @Version 1.0
 **/
public interface QualityInspectionEnum {
    /**
     * 质检类型
     */
    @AllArgsConstructor
    enum RuleTypeEnum{
        DirtyWords("关键词质检", "1"),
        Action("行为质检", "2"),
        Feature("会话特征质检", "3");
        public static RuleTypeEnum getRuleType(String value){
            if(DirtyWords.value.equals(value)){
                return DirtyWords;
            }else if(Action.value.equals(value)){
                return Action;
            }else if(Feature.value.equals(value)){
                return Feature;
            }else {
                return null;
            }
        }
        public static String getRuleLabel(String value){
            RuleTypeEnum typeEnum = getRuleType(value);
            if(typeEnum != null){
                return typeEnum.label;
            }else {
                return "";
            }
        }
        final public String label, value;
    }

    /**
     * 会话特征
     */
    @AllArgsConstructor
    enum SessionFeatureEnum{
        TimeOut("超时回复", "1");
        public static SessionFeatureEnum getSessionFeatureEnum(String value){
            if(TimeOut.value.equals(value)){
                return TimeOut;
            }else {
                return null;
            }
        }
        public static String getSessionFeatureLabel(String value){
            SessionFeatureEnum typeEnum = getSessionFeatureEnum(value);
            if(typeEnum != null){
                return typeEnum.label;
            }else {
                return "";
            }
        }
        final public String label, value;
    }

    /**
     * 行为类型
     */
    @AllArgsConstructor
    enum SessionActionEnum{
        DeleteCustomer("删除客户", "1"),DeletedByCustomer("被客户删除", "2");
        public static SessionActionEnum getSessionActionEnum(String value){
            if(DeleteCustomer.value.equals(value)){
                return DeleteCustomer;
            }else if(DeletedByCustomer.value.equals(value)){
                return DeletedByCustomer;
            }else {
                return null;
            }
        }
        public static String getSessionActionLabel(String value){
            SessionActionEnum typeEnum = getSessionActionEnum(value);
            if(typeEnum != null){
                return typeEnum.label;
            }else {
                return "";
            }
        }
        final public String label, value;
    }

    /**
     * 质检角色
     */
    @AllArgsConstructor
    enum TargetTypeEnum{
        EnterpriseWeChat("企微客户", "1"),Staff("员工", "2");
        public static TargetTypeEnum getTargetTypeEnum(String value){
            if(EnterpriseWeChat.value.equals(value)){
                return EnterpriseWeChat;
            }else if(Staff.value.equals(value)){
                return Staff;
            }else {
                return null;
            }
        }
        public static String getTargetTypeLabel(String value){
            TargetTypeEnum typeEnum = getTargetTypeEnum(value);
            if(typeEnum != null){
                return typeEnum.label;
            }else {
                return "";
            }
        }
        final public String label, value;
    }

    /**
     * 会话场景
     */
    @AllArgsConstructor
    enum SessionTypeEnum{
        WeChat("单聊", "1"),RoomChat("群聊", "2");
        public static SessionTypeEnum getSessionTypeEnum(String value){
            if(WeChat.value.equals(value)){
                return WeChat;
            }else if(RoomChat.value.equals(value)){
                return RoomChat;
            }else {
                return null;
            }
        }
        public static String getSessionTypeLabel(String value){
            SessionTypeEnum typeEnum = getSessionTypeEnum(value);
            if(typeEnum != null){
                return typeEnum.label;
            }else {
                return "";
            }
        }
        final public String label, value;
    }

    /**
     * 发送实时通知
     */
    @AllArgsConstructor
    enum MsgPushEnum{
        Push("发送实时消息通知", "1");
        public static MsgPushEnum getMsgPushEnum(String value){
            if(Push.value.equals(value)){
                return Push;
            }else {
                return null;
            }
        }
        public static String getMsgPushLabel(String value){
            MsgPushEnum typeEnum = getMsgPushEnum(value);
            if(typeEnum != null){
                return typeEnum.label;
            }else {
                return "";
            }
        }
        final public String label, value;
    }
}
