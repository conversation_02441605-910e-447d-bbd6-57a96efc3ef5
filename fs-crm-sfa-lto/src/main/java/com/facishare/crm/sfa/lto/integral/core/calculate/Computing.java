package com.facishare.crm.sfa.lto.integral.core.calculate;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.List;

@Slf4j
public class Computing {

    /** 比较符*/
    public static final String GT = "GT";
    public static final String N = "N";
    public static final String EQ = "EQ";
    public static final String GTE = "GTE";
    public static final String LT = "LT";
    public static final String LTE = "LTE";
    public static final String BETWEEN = "between";

    public static List<Object> compute(String op, double score, List<Object> values,
                                       List<Object> answers) {
        try {
            Computable compute = getClass(op).newInstance();
            BigDecimal bg = BigDecimal.valueOf(score);
            //计算分数保留小数点后两位
            double roundedScore = bg.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
            return compute.compute(roundedScore, values, answers);
        } catch (Exception e) {
            log.error("计算结果字段，操作符错误，请检查操作符: {}", op, e);
            throw new IllegalArgumentException("计算结果操作符错误");
        }
    }

    private static Class<? extends Computable> getClass(String op) {
        switch (op) {
            case GT: return GreatThan.class;
            case N: return NotEqual.class;
            case EQ: return Equal.class;
            case GTE: return GreatThanOrEqual.class;
            case LT: return LessThan.class;
            case LTE: return LessThanOrEqual.class;
            case BETWEEN: return Between.class;
            default: break;
        }

        throw new UnsupportedOperationException("不支持操作符: " + op);
    }

    static class GreatThan implements Computable {
        @Override
        public List<Object> compute(double score, List<Object> values, List<Object> answers) {
            double var = Double.parseDouble(values.get(0).toString());
            if (isGreatThan(score, var)) {
                return answers;
            }
            return Lists.newArrayList();
        }
    }

    static class NotEqual implements Computable {
        @Override
        public List<Object> compute(double score, List<Object> values, List<Object> answers) {
            double var = Double.parseDouble(values.get(0).toString());
            if (!isEqual(score, var)) {
                return answers;
            }
            return Lists.newArrayList();
        }
    }

    static class Equal implements Computable {
        @Override
        public List<Object> compute(double score, List<Object> values, List<Object> answers) {
            double var = Double.parseDouble(values.get(0).toString());
            if (isEqual(score, var)) {
                return answers;
            }
            return Lists.newArrayList();
        }
    }

    static class GreatThanOrEqual implements Computable {
        @Override
        public List<Object> compute(double score, List<Object> values, List<Object> answers) {
            double var = Double.parseDouble(values.get(0).toString());
            if (isGreatThanOrEqual(score, var)) {
                return answers;
            }
            return Lists.newArrayList();
        }
    }

    static class LessThan implements Computable {
        @Override
        public List<Object> compute(double score, List<Object> values, List<Object> answers) {
            double var = Double.parseDouble(values.get(0).toString());
            if (isLessThan(score, var)) {
                return answers;
            }
            return Lists.newArrayList();
        }
    }

    static class LessThanOrEqual implements Computable {
        @Override
        public List<Object> compute(double score, List<Object> values, List<Object> answers) {
            double var = Double.parseDouble(values.get(0).toString());
            if (isLessThanOrEqual(score, var)) {
                return answers;
            }
            return Lists.newArrayList();
        }
    }

    static class Between implements Computable {
        @Override
        public List<Object> compute(double score, List<Object> values, List<Object> answers) {
            double min = Double.parseDouble(values.get(0).toString());
            double max = Double.parseDouble(values.get(1).toString());
            if (isBetween(score, min, max)) {
                return answers;
            }
            return Lists.newArrayList();
        }
    }


    private static boolean isEqual(double a, double b) {
        return a - b < 0.001 && a - b > -0.001;
    }

    private static boolean isGreatThan(double a, double b){
        return a - b > 0.001;
    }

    private static boolean isGreatThanOrEqual(double a, double b) {

        return isGreatThan(a, b) || isEqual(a ,b);
    }

    private static boolean isLessThan(double a, double b) {
        return b - a > 0.001;
    }

    private static boolean isLessThanOrEqual(double a, double b) {
        return isLessThan(a, b) || isEqual(a, b);
    }

    private static boolean isBetween(double a, double min, double max) {
        return isGreatThanOrEqual(a, min) && isLessThanOrEqual(a, max);
    }

}
