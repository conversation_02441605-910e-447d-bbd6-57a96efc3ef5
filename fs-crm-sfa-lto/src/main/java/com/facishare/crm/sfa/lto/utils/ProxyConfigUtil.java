package com.facishare.crm.sfa.lto.utils;

import com.github.autoconf.ConfigFactory;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ProxyConfigUtil {
    @Getter
    private static String proxyHost;

    @Getter
    private static Integer proxyPort;

    static {
        ConfigFactory.getConfig("variables_endpoint", config -> {
            proxyHost = config.get("http_proxy_host");
            proxyPort = config.getInt("http_proxy_port");
            log.info("http代理地址{}:{}", proxyHost, proxyPort);
        });
    }

    public static boolean isProxyEnabled() {
        return proxyHost != null && !proxyHost.isEmpty() && proxyPort != null;
    }
} 