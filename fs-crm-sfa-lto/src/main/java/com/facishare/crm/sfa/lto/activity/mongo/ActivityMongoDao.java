package com.facishare.crm.sfa.lto.activity.mongo;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.lto.utils.CollectionUtil;
import com.facishare.crm.sfa.lto.utils.Safes;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.i18n.client.I18nClient;
import com.github.mongo.support.DatastoreExt;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.mongodb.MongoClientSettings;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.*;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.codecs.configuration.CodecRegistries;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.mongodb.client.model.Filters.*;
import static com.mongodb.client.model.Updates.*;
import static com.mongodb.client.model.Updates.combine;

@Slf4j
@Component
public class ActivityMongoDao {

    @Autowired
    protected ServiceFacade serviceFacade;

    @Getter
    private final DatastoreExt store;

    private final Set<String> collectionCache = Sets.newConcurrentHashSet();

    private final static String COLLECTION_PREFIX = "interactive_document_";

    private final String databaseName;

    private static class SingleCodecHolder {
        //需要让自定义的codec放前面
        private static final CodecRegistry codecRegistry = CodecRegistries.fromRegistries(
                MongoClientSettings.getDefaultCodecRegistry(),
                CodecRegistries.fromProviders(PojoCodecProvider.builder()
                        .register(InteractiveDocument.class)
                        .automatic(true).build()));
    }


    public ActivityMongoDao(@Qualifier("activityDataMongoDbStore")DatastoreExt store) {
        if (store == null) {
            this.store = null;
            this.databaseName = null;
            log.warn("mongo store is null");
            return;
        }
        this.store = store;
        Set<String> collectionNames = store.getDB().getCollectionNames();
        databaseName = store.getDB().getName();
        for (String collectionName : collectionNames) {
            if (collectionName.startsWith(COLLECTION_PREFIX)) {
                collectionCache.add(collectionName);
            }
        }
        log.warn("collectionCache:{}", collectionCache);
    }


    private String getOrCreateCollection(String tenantId) {
        String collectionName = getCollectionName(tenantId);
        if (!collectionCache.contains(collectionName)) {
            this.createIndex(collectionName);
            collectionCache.add(collectionName);
        }
        return collectionName;
    }



    private MongoCollection<InteractiveDocument> getMongoCollection(String tenantId) {
        String collectionName = getOrCreateCollection(tenantId);
        return store.getMongo().getDatabase(databaseName)
                .withCodecRegistry(SingleCodecHolder.codecRegistry)
                .getCollection(collectionName, InteractiveDocument.class);
    }


    private synchronized void createIndex(String collectionName) {
        // double check
        if (collectionCache.contains(collectionName)) {
            return;
        }
        MongoCollection<Document> collection = store.getMongo().getDatabase(databaseName).getCollection(collectionName);
        for (Document idx : Safes.of(collection.listIndexes())) {
            if (idx.containsKey("name")) {
                String name = idx.getString("name");
                if ("interactiveDataIndex".equals(name)) {
                    return;
                }
            }
        }
        List<IndexModel> indexList = Lists.newArrayList();

        Bson interactiveDataIndex = Indexes.compoundIndex(
                Indexes.ascending("objectId"),
                Indexes.ascending("objectApiName"),
                Indexes.ascending("deleted")
        );
        indexList.add(new IndexModel(interactiveDataIndex, new IndexOptions()
                .background(true)
                .name("interactiveDataIndex")
        ));

        List<String> indexes = collection.createIndexes(indexList);
        log.info("createIndexes:{} actual:{}", indexList, indexes);
    }


    public String getCollectionName(String tenantId) {
        // TODO 路由策略
        return COLLECTION_PREFIX + tenantId;
    }


    public void batchInsert(String tenantId, List<InteractiveDocument> documents) {
        documents.stream().forEach(d -> d.setDeleted(false));
        MongoCollection<InteractiveDocument> collection = getMongoCollection(tenantId);
        collection.insertMany(documents);
    }

    public void batchReplaceById(String tenantId, List<InteractiveDocument> documents) {
        List<ReplaceOneModel<InteractiveDocument>> updateModelList = documents.stream().map(doc -> {
            if (doc.getId() == null && StringUtils.isEmpty(doc.getId().toString())) {
                throw new IllegalArgumentException("'_id' must not be empty!");
            }
            Bson filter = and(eq("_id", doc.getId()));
            return new ReplaceOneModel<>(filter, doc, new ReplaceOptions().upsert(true));
        }).collect(Collectors.toList());

        BulkWriteResult bulkWriteResult = getMongoCollection(tenantId)
                .bulkWrite(updateModelList, new BulkWriteOptions().ordered(false));
    }


    public void replaceSingleById(String tenantId, String docId, List<ActivityMongoParamDao.BatchReplaceParam> params) {
        List<UpdateOneModel<InteractiveDocument>> updateModelList = params.stream().map(param -> {
            if (StringUtils.isEmpty(docId)) {
                throw new IllegalArgumentException("'docId' must not be empty!");
            }
            Bson filter = and(eq("_id", new ObjectId(docId)));

            List<Bson> updates = new ArrayList<>();
            updates.add(set("userId", param.getUserId()));
            updates.add(set("userName", param.getUserName()));
            updates.add(set("userApiName", param.getUserApiName()));
            if (param.getNameAvaId() != null) {
                updates.add(set("nameAvaId", param.getNameAvaId()));
            }

            Bson update = combine(updates);

            return new UpdateOneModel<InteractiveDocument>(filter, update, new UpdateOptions().upsert(true));
        }).collect(Collectors.toList());
        BulkWriteResult bulkWriteResult = getMongoCollection(tenantId)
                .bulkWrite(updateModelList, new BulkWriteOptions().ordered(false));
    }

    public void replaceSingleById(String tenantId, String docId, String activityUserId) {
        if (StringUtils.isEmpty(docId)) {
            throw new IllegalArgumentException("'docId' must not be empty!");
        }
        Bson filter = and(eq("_id", new ObjectId(docId)));
        Bson update = set("activityUserId", activityUserId);
        
        getMongoCollection(tenantId)
                .updateOne(filter, update, new UpdateOptions().upsert(true));
    }



    /**
     * 批量替换文档中的用户信息
     * @param tenantId 租户ID
     * @param params 替换参数列表
     */
    public void batchReplaceSpeaker(String tenantId, String objectId, String sourceUserName, List<ActivityMongoParamDao.BatchReplaceParam> params) {
        List<UpdateManyModel<InteractiveDocument>> updateModelList = params.stream()
                .map(param -> {
                    if (StringUtils.isEmpty(param.getObjectId()) || StringUtils.isEmpty(param.getUserName())) {
                        log.warn("batchReplaceByObjectIdAndUserName, objectId:{}, userName:{}", param.getObjectId(), param.getUserName());
                    }
                    Bson filter = and(
                            eq("tenantId", tenantId),
                            eq("objectId", objectId),
                            eq("userName", sourceUserName)
                    );
                    // 使用 $set 只更新指定字段
                    Bson update = combine(
                            set("userId", param.getUserId()),
                            set("userName", param.getUserName()),
                            set("userApiName", param.getUserApiName()),
                            set("nameAvaId", param.getNameAvaId())
                    );

                    return new UpdateManyModel<InteractiveDocument>(filter, update, new UpdateOptions().upsert(true));
                })
                .collect(Collectors.toList());

        BulkWriteResult bulkWriteResult = getMongoCollection(tenantId)
                .bulkWrite(updateModelList, new BulkWriteOptions().ordered(false));

        log.info("batchReplaceByObjectIdAndUserName, tenantId:{}, objectId:{}, sourceUserName:{}, params:{}, result:{}",
                tenantId, objectId, sourceUserName, params, bulkWriteResult);
    }

    public void batchReplaceSpeaker(String tenantId, String objectId, String oldActivityUserId, String newActivityUserId) {
        if (StringUtils.isEmpty(tenantId) || StringUtils.isEmpty(objectId) || StringUtils.isEmpty(oldActivityUserId)) {
            log.warn("batchReplaceSpeaker - Invalid parameters: tenantId={}, objectId={}, oldActivityUserId={}",
                    tenantId, objectId, oldActivityUserId);
            // 可以选择抛出异常或者直接返回
             throw new IllegalArgumentException("TenantId, ObjectId, and oldActivityUserId must not be empty.");
        }
        
        Bson filter = and(
                eq("tenantId", tenantId),
                eq("objectId", objectId),
                eq("activityUserId", oldActivityUserId),
                eq("deleted", false)
        );

        Bson update = set("activityUserId", newActivityUserId);

        List<UpdateManyModel<InteractiveDocument>> updateModelList = Lists.newArrayList();
        updateModelList.add(new UpdateManyModel<>(filter, update, new UpdateOptions().upsert(false)));
        
        BulkWriteResult bulkWriteResult = getMongoCollection(tenantId)
                .bulkWrite(updateModelList, new BulkWriteOptions().ordered(false));

        log.info("batchReplaceSpeaker completed. tenantId={}, objectId={}, oldActivityUserId={}, newActivityUserId={}, matched={}, modified={}",
                tenantId, objectId, oldActivityUserId, newActivityUserId, 
                bulkWriteResult.getMatchedCount(), bulkWriteResult.getModifiedCount());
    }

    /**
     * 实时录音中 批量替换文档中的用户名称
     * @param tenantId 租户ID
     * @param param 替换参数
     */
    public void recordingReplaceSpeaker(String tenantId, String objectId, ActivityMongoParamDao.RecordingReplaceParam param) {
        List<UpdateManyModel<InteractiveDocument>> updateModelList = Lists.newArrayList();
        if (StringUtils.isEmpty(param.getObjectId()) || StringUtils.isEmpty(param.getUserName())) {
            log.warn("batchReplaceByObjectIdAndUserName, objectId:{}, userName:{}", param.getObjectId(), param.getUserName());
        }
        Bson filter = and(
                eq("tenantId", tenantId),
                eq("objectId", objectId),
                in("seq", param.getSeqIds())
        );
        Bson update = combine(
                set("userName", param.getUserName())
        );
        updateModelList.add(new UpdateManyModel<InteractiveDocument>(filter, update, new UpdateOptions().upsert(true)));
        BulkWriteResult bulkWriteResult = getMongoCollection(tenantId)
                .bulkWrite(updateModelList, new BulkWriteOptions().ordered(false));
        log.info("batchReplaceByObjectIdAndUserName, tenantId:{}, objectId:{}, param:{}, result:{}",
                tenantId, objectId, param, bulkWriteResult);
    }



    public List<InteractiveDocument> queryListByActiveRecordId(String tenantId, String objectId, Integer offset, Integer limit) {
        return queryListByActiveRecordId(tenantId, objectId, offset, limit, false);
    }

    public List<InteractiveDocument> queryListByActiveRecordId(String tenantId, String objectId, Integer offset, Integer limit, boolean sort) {
        MongoCollection<InteractiveDocument> collection = getMongoCollection(tenantId);
        List<Bson> bsonList = Lists.newArrayList();
        bsonList.add(Filters.eq("objectId", objectId));
        bsonList.add(Filters.eq("objectApiName", Utils.ACTIVE_RECORD_API_NAME));
        bsonList.add(Filters.eq("deleted", false));
        if (sort) {
            return collection.find(Filters.and(bsonList)).sort(Sorts.ascending("seq")).skip(offset).limit(limit).into(new ArrayList<>());
        } else {
            return collection.find(Filters.and(bsonList)).skip(offset).limit(limit).into(new ArrayList<>());
        }
    }
    public List<InteractiveDocument> queryListByActivityUserId(String tenantId, String objectId, String activityUserId, Integer offset, Integer limit) {
        MongoCollection<InteractiveDocument> collection = getMongoCollection(tenantId);
        List<Bson> bsonList = Lists.newArrayList();
        bsonList.add(Filters.eq("objectId", objectId));
        bsonList.add(Filters.eq("objectApiName", Utils.ACTIVE_RECORD_API_NAME));
        bsonList.add(Filters.eq("activityUserId", activityUserId));
        bsonList.add(Filters.eq("deleted", false));
        return collection.find(Filters.and(bsonList)).skip(offset).limit(limit).into(new ArrayList<>());
    }
    public InteractiveDocument queryById(String tenantId, String id) {
        MongoCollection<InteractiveDocument> collection = getMongoCollection(tenantId);
        List<Bson> bsonList = Lists.newArrayList();
        bsonList.add(Filters.eq("tenantId", tenantId));
        bsonList.add(Filters.eq("_id", new ObjectId(id)));
        List<InteractiveDocument> list = collection.find(Filters.and(bsonList)).into(new ArrayList<>());
        if(CollectionUtil.isNotEmpty(list)){
            return list.get(0);
        }
        return null;
    }

    public List<InteractiveDocument> queryByIds(String tenantId, List<String> ids) {
        MongoCollection<InteractiveDocument> collection = getMongoCollection(tenantId);
        List<ObjectId>  objectIds = ids.stream().map(id->new ObjectId(id)).collect(Collectors.toList());
        List<Bson> bsonList = Lists.newArrayList();
        bsonList.add(Filters.eq("tenantId", tenantId));
        bsonList.add(Filters.in("_id", objectIds));
        List<InteractiveDocument> list = collection.find(Filters.and(bsonList)).into(new ArrayList<>());
        if(CollectionUtil.isNotEmpty(list)){
            return list;
        }
        return null;
    }

    public void updateContent(String tenantId, String id, ActivityMongoParamDao.UpdateContentParam param) {
        Bson filter = and(
                eq("tenantId", tenantId),
                eq("_id", new ObjectId(id))
        );
        Bson update = null;
        if(param.getContent() !=null){
            if(ObjectUtils.isNotEmpty(param.getOldContent())){
                update = combine(
                        set("content", param.getContent()),
                        set("oldContent", param.getOldContent())
                );
            }else{
                update = combine(
                        set("content", param.getContent())
                );
            }
        }else if(param.getTranslateContent()!=null){
            if(ObjectUtils.isNotEmpty(param.getOldTranslateContent())){
                update = combine(
                        set("translateContent", param.getTranslateContent()),
                        set("oldTranslateContent", param.getOldTranslateContent())
                );
            }else{
                update = combine(
                        set("translateContent", param.getTranslateContent())
                );
            }
        }else{
            throw new ValidateException(I18N.text(I18NKey.REQUEST_PARAM_IS_NULL));
        }
        List<UpdateManyModel<InteractiveDocument>> updateModelList = Lists.newArrayList();
        updateModelList.add(new UpdateManyModel<InteractiveDocument>(filter, update, new UpdateOptions().upsert(true)));
        BulkWriteResult bulkWriteResult = getMongoCollection(tenantId)
                .bulkWrite(updateModelList, new BulkWriteOptions().ordered(false));
        log.info("updateContent, tenantId:{}, id:{}",
                tenantId, id);
    }

    public void batchUpdateContent(String tenantId, List<ActivityMongoParamDao.UpdateContentParam> paramList) {
        List<UpdateManyModel<InteractiveDocument>> updateModelList = paramList.stream()
                .map(param -> {
                    Bson filter = and(
                            eq("tenantId", tenantId),
                            eq("_id", new ObjectId(param.getId()))
                    );
                    // 使用 $set 只更新指定字段
                    Bson update = null;
                    if(param.getContent() !=null){
                        if(ObjectUtils.isNotEmpty(param.getOldContent())){
                            update = combine(
                                    set("content", param.getContent()),
                                    set("oldContent", param.getOldContent())
                            );
                        }else{
                            update = combine(
                                    set("content", param.getContent())
                            );
                        }
                    }else if(param.getTranslateContent()!=null){
                        if(ObjectUtils.isNotEmpty(param.getOldTranslateContent())){
                            update = combine(
                                    set("translateContent", param.getTranslateContent()),
                                    set("oldTranslateContent", param.getOldTranslateContent())
                            );
                        }else{
                            update = combine(
                                    set("translateContent", param.getTranslateContent())
                            );
                        }
                    }else{
                        throw new ValidateException(I18N.text(I18NKey.REQUEST_PARAM_IS_NULL));
                    }

                    return new UpdateManyModel<InteractiveDocument>(filter, update, new UpdateOptions().upsert(true));
                })
                .collect(Collectors.toList());

        BulkWriteResult bulkWriteResult = getMongoCollection(tenantId)
                .bulkWrite(updateModelList, new BulkWriteOptions().ordered(false));
        log.info("batchUpdateContent, tenantId:{}",
                tenantId);
    }

    public long deleteByObjectId(String tenantId, String objectId) {
        MongoCollection<InteractiveDocument> collection = getMongoCollection(tenantId);
        Bson filter = Filters.eq("objectId", objectId);
        return collection.deleteMany(filter).getDeletedCount();
    }

    public void deleteById(String tenantId, String id) {
        Bson filter = and(
                eq("tenantId", tenantId),
                eq("_id", new ObjectId(id))
        );
        Bson update = combine(
                set("deleted",true)
                );
        List<UpdateManyModel<InteractiveDocument>> updateModelList = Lists.newArrayList();
        updateModelList.add(new UpdateManyModel<InteractiveDocument>(filter, update, new UpdateOptions().upsert(true)));
        BulkWriteResult bulkWriteResult = getMongoCollection(tenantId)
                .bulkWrite(updateModelList, new BulkWriteOptions().ordered(false));
        log.info("deleteById, tenantId:{}, id:{}",
                tenantId, id);
    }

    public void deleteByIds(String tenantId, List<String> ids) {
        List<ObjectId>  objectIds = ids.stream().map(id->new ObjectId(id)).collect(Collectors.toList());
        Bson filter = and(
                eq("tenantId", tenantId),
                in("_id", objectIds)
        );
        Bson update = combine(
                set("deleted",true)
        );
        List<UpdateManyModel<InteractiveDocument>> updateModelList = Lists.newArrayList();
        updateModelList.add(new UpdateManyModel<InteractiveDocument>(filter, update, new UpdateOptions().upsert(true)));
        BulkWriteResult bulkWriteResult = getMongoCollection(tenantId)
                .bulkWrite(updateModelList, new BulkWriteOptions().ordered(false));
        log.info("deleteByIds, tenantId:{}, id:{}",
                tenantId, JSONObject.toJSONString(ids));
    }

    /**
     * 查询指定对象的完整内容，并包含发言者名称，最多 500 条
     * @param tenantId 租户ID
     * @param objectId 对象ID
     * @return 完整内容，并包含发言者名称
     */
    public String queryFullContentWithSpeaker(String tenantId,String objectId){
        return queryFullContentWithSpeaker(tenantId, objectId, 0, 500);
    }

    /**
     * 查询指定对象的完整内容，并包含发言者名称
     * @param tenantId 租户ID
     * @param objectId 对象ID
     * @param offset 偏移量
     * @param limit 限制条数
     * @return 完整内容，并包含发言者名称
     */
    public String queryFullContentWithSpeaker(String tenantId, String objectId, Integer offset, Integer limit) {
        List<InteractiveDocument> documents = queryListByActiveRecordId(tenantId, objectId, offset, limit,true);
        if (documents.isEmpty()) {
            return null;
        }
        return montageMongoContent(documents,false,"zh-CN");
    }


    /**
     * 更新文档中的 activityUserId 字段，一般是录音文件初始化时候，将 biz_activity_user表没有数据，刷数据兼容。
     */
    public void updateActivityUserId(String tenantId, String objectId, List<InteractiveDocument> documents) {
        if (CollectionUtil.isEmpty(documents)) {
            log.warn("updateActivityUserId - documents is empty, tenantId={}, objectId={}", tenantId, objectId);
            return;
        }

        List<UpdateManyModel<InteractiveDocument>> updateModelList = documents.stream()
                .map(document -> {
                    Bson filter = and(
                        eq("tenantId", tenantId),
                        eq("objectId", objectId),
                        eq("_id", document.getId()),
                        eq("deleted", false)
                    );
                    
                    Bson update = combine(
                                set("activityUserId", document.getActivityUserId())
                        );
                    return new UpdateManyModel<InteractiveDocument>(filter, update, new UpdateOptions().upsert(false));
                })
                .collect(Collectors.toList());

        BulkWriteResult bulkWriteResult = getMongoCollection(tenantId)
                .bulkWrite(updateModelList, new BulkWriteOptions().ordered(false));
        
        log.info("updateActivityUserId completed. tenantId={}, objectId={}, matched={}, modified={}", 
                tenantId, objectId, bulkWriteResult.getMatchedCount(), bulkWriteResult.getModifiedCount());
    }

    /**
     * 更新文档中的 activityUserId 和 userId 字段，一般是上传音频文件初始化时候，将 biz_activity_user表没有数据，刷数据兼容。
     * @param tenantId
     * @param objectId
     * @param documents
     */
    public void updateActivityUserIdAndUserId(String tenantId, String objectId, List<InteractiveDocument> documents) {
        if (CollectionUtil.isEmpty(documents)) {
            log.warn("updateActivityUserId - documents is empty, tenantId={}, objectId={}", tenantId, objectId);
            return;
        }

        List<UpdateManyModel<InteractiveDocument>> updateModelList = documents.stream()
                .map(document -> {
                    Bson filter = and(
                        eq("tenantId", tenantId),
                        eq("objectId", objectId),
                        eq("_id", document.getId()),
                        eq("deleted", false)
                    );
                    
                    Bson update = combine(
                                set("activityUserId", document.getActivityUserId()),
                                set("userId", document.getUserId())
                        );
                    return new UpdateManyModel<InteractiveDocument>(filter, update, new UpdateOptions().upsert(false));
                })
                .collect(Collectors.toList());

        BulkWriteResult bulkWriteResult = getMongoCollection(tenantId)
                .bulkWrite(updateModelList, new BulkWriteOptions().ordered(false));
        
        log.info("updateActivityUserIdAndUserId completed. tenantId={}, objectId={}, matched={}, modified={}", 
                tenantId, objectId, bulkWriteResult.getMatchedCount(), bulkWriteResult.getModifiedCount());
    }


    public void refreshDataIsDeleted(String tenantId){
        MongoCollection<InteractiveDocument> collection = getMongoCollection(tenantId);
        List<Bson> bsonList = Lists.newArrayList();
        bsonList.add(Filters.eq("objectApiName", Utils.ACTIVE_RECORD_API_NAME));
        int offset = 0;
        int limit = 500;
        for(int i = 0 ; i < 1000000; i++){
            List<InteractiveDocument> resultList = collection.find(Filters.and(bsonList)).sort(Sorts.descending("createTime"))
                    .skip(offset).limit(limit).into(new ArrayList<>());
            if(CollectionUtil.isEmpty(resultList)){
                log.warn("refreshDataIsDeleted resultList is null or empty");
                return;
            }
            List<UpdateManyModel<InteractiveDocument>> updateModelList = resultList.stream()
                    .map(param -> {
                        Bson filter = and(
                                eq("tenantId", tenantId),
                                eq("_id", param.getId())
                        );
                        // 使用 $set 只更新指定字段
                        Bson update = combine(
                                set("deleted", false)
                        );

                        return new UpdateManyModel<InteractiveDocument>(filter, update, new UpdateOptions().upsert(true));
                    })
                    .collect(Collectors.toList());

            BulkWriteResult bulkWriteResult = getMongoCollection(tenantId)
                    .bulkWrite(updateModelList, new BulkWriteOptions().ordered(false));

            if(resultList.size() < limit){
                return;
            }
            offset = offset + limit;
            log.warn("refreshDataIsDeleted  tenantId:{}, offset:{}",tenantId, offset);
        }

    }

    public List<InteractiveDocument> queryListByActiveRecordIdAndSeq(String tenantId, String objectId,List<Long> seqList) {
        MongoCollection<InteractiveDocument> collection = getMongoCollection(tenantId);
        List<Bson> bsonList = Lists.newArrayList();
        bsonList.add(Filters.eq("objectId", objectId));
        bsonList.add(Filters.eq("objectApiName", Utils.ACTIVE_RECORD_API_NAME));
        bsonList.add(Filters.in("seq", seqList));
        bsonList.add(Filters.eq("deleted", false));
        return collection.find(Filters.and(bsonList)).into(new ArrayList<>());
    }

    /**
     *  将mongo中的录音信息，拼接为字符串
     * @param interactiveList
     * @param isNeedSN 是否需要序号
     * @return
     */
    public String montageMongoContent(List<InteractiveDocument> interactiveList,boolean isNeedSN,String language){
        if(CollectionUtil.isEmpty(interactiveList)){
            return "";
        }
        StringBuilder sb = new StringBuilder();
        String speaker = I18nClient.getInstance().getOrDefault("sfa.activity.corpus.list_item_user_label",0, language,"发言人");// ignoreI18n
        //根据销售记录查询所有的用户
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(1000);
        searchTemplateQuery.setFindExplicitTotalNum(false);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setPermissionType(0);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, "active_record_id", interactiveList.get(0).getObjectId());
        searchTemplateQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryIgnoreAll(new User(interactiveList.get(0).getTenantId(), "-10000"), "ActivityUserObj", searchTemplateQuery);
        Map<String,String>  userMap = new HashMap<>();
        if(ObjectUtils.isNotEmpty(queryResult) && CollectionUtil.isNotEmpty(queryResult.getData())){
            userMap = queryResult.getData().stream().collect(Collectors.toMap(IObjectData::getId, IObjectData::getName));
        }
        try {
            for(int i = 0; i < interactiveList.size();i++){
                InteractiveDocument document = interactiveList.get(i);
                if(isNeedSN){
                    sb.append("序号:").append(document.getSeq()).append(",");// ignoreI18n
                }
                String userName = document.getUserName();
                if(userMap.containsKey(document.getActivityUserId())){
                    userName = userMap.get(document.getActivityUserId());
                }
                sb.append(handleUserNameConvert(userName,speaker));
                sb.append(":");
                sb.append(document.getContent());
                sb.append("\n");
            }
        }catch (Exception e){
            log.error("handleFragmentationSummary  loop error: ",e);
        }
        return sb.toString();
    }

    public String handleUserNameConvert(String userName,String speaker){
        if(userName.contains("user_")){
            String[]  userNameArr = userName.split("_");
            return speaker+userNameArr[1];
        }
        return userName;
    }

    public InteractiveDocument queryLargestSeqDocument(String tenantId, String objectApiName, String objectId) {
        MongoCollection<InteractiveDocument> collection = getMongoCollection(tenantId);
        List<Bson> bsonList = new ArrayList<>();
        bsonList.add(eq("objectId", objectId));
        bsonList.add(eq("objectApiName", objectApiName));
        bsonList.add(eq("deleted", false));
        return collection.find(and(bsonList)).sort(Sorts.descending("seq")).first();
    }
}
