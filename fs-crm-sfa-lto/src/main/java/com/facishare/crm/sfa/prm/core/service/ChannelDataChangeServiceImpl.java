package com.facishare.crm.sfa.prm.core.service;

import com.facishare.crm.sfa.prm.api.channel.ChannelDataChangeService;
import com.facishare.crm.sfa.prm.api.enums.SignStatus;
import com.facishare.crm.sfa.prm.core.constants.ChannelConstants;
import com.facishare.crm.sfa.prm.platform.utils.DataUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Maps;
import io.socket.client.IO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-02-27
 * ============================================================
 */
@Service
@Slf4j
public class ChannelDataChangeServiceImpl implements ChannelDataChangeService {
    @Resource
    private ServiceFacade serviceFacade;

    @Override
    public IObjectData changeSignStatus(User user, IObjectData objectData, SignStatus signStatus) {
        String SignStatusValue = DataUtils.getValue(objectData, ChannelConstants.SIGNING_STATUS, String.class);
        SignStatus originalSignStatus = SignStatus.find(SignStatusValue);
        if (originalSignStatus != null && originalSignStatus == signStatus) {
            return objectData;
        }
        Map<String, Object> updateMap = Maps.newHashMap();
        updateMap.put(ChannelConstants.SIGNING_STATUS, signStatus.getStatus());
        return serviceFacade.updateWithMap(user, objectData, updateMap);
    }

    @Override
    public IObjectData renewExpiredTime(User user, String objectApiName, IObjectData objectData, Long expiredTimestamp) {
        String expireTimeFieldApiName;
        if (ChannelConstants.ACCOUNT_OBJ.equals(objectApiName)) {
            expireTimeFieldApiName = ChannelConstants.ACC_EXPIRED_TIME;
        } else if (ChannelConstants.PARTNER_OBJ.equals(objectApiName)) {
            expireTimeFieldApiName = ChannelConstants.EXPIRED_TIME;
        } else {
            log.warn("objectApiName not support!, tenantId:{}, objectApiName:{}", user.getTenantId(), objectApiName);
            return objectData;
        }
        Map<String, Object> filedMapping = Maps.newHashMap();
        filedMapping.put(expireTimeFieldApiName, expiredTimestamp);
        return serviceFacade.updateWithMap(user, objectData, filedMapping);
    }
}
