package com.facishare.crm.sfa.lto.qywx.proxy.models;

import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface BatchGetEmployeeInfo {
  @Data
	@Builder
	class Arg {
    /**
     * 必填
     */
    private String fsEa;
    /**
     * 必填，待查询的企微userId
     */
    private List<String> userIds;
  }

  @Data
  class QywxEmployeeInfoResult {
    //private QyweixinEmployeeInfo qyweixinEmployeeInfo;  //员工详情
    private String fsUserId;  //纷享员工id
    private String outUserId;  //企微员工id
    private String errorCode;  //错误码
    private String errorMsg;  //错误信息
  }
}