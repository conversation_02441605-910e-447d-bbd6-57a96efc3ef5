package com.facishare.crm.sfa.lto.objectpool;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.lto.common.models.LtoAccountFieldApiConstants;
import com.facishare.crm.sfa.lto.common.models.LtoFieldApiConstants;
import com.facishare.crm.sfa.lto.exception.ExceptionUtil;
import com.facishare.crm.sfa.lto.objectpool.models.ObjectPoolActionModels;
import com.facishare.crm.sfa.lto.push.ContactSessionSandwichService;
import com.facishare.crm.sfa.lto.relationship.RelationshipProducer;
import com.facishare.crm.sfa.lto.utils.*;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
public class AccountPoolActionServiceImpl extends BasePoolActionService {
    @Autowired
    private ContactSessionSandwichService contactSessionSandwichService;

    @Autowired
    private RelationshipProducer relationshipProducer;

    @Override
    public String getObjectApiName() {
        return Utils.ACCOUNT_API_NAME;
    }

    @Override
    public String getObjectPoolKeyName() {
        return LtoAccountFieldApiConstants.HIGH_SEAS_ID;
    }

    @Override
    public ObjectPoolActionModels.Result choose(User user, String objectPoolId, List<String> objectIds, String eventId, String partnerId) {
        ObjectPoolActionModels.Result result = ObjectPoolActionModels.Result.builder().build();
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIds(user.getTenantId(), objectIds, Utils.ACCOUNT_API_NAME);
        List<IObjectData> oldDataList = ObjectDataExt.copyList(objectDataList);
        String ownerDeptId = ltoOrgCommonService.getMainDepartment(user.getTenantId(), user.getUpstreamOwnerIdOrUserId());
        IObjectData objectPoolData = getObjectPoolById(user.getTenantId(), objectPoolId);
        boolean cleanTeamMember = ObjectDataUtil.getBooleanValue(objectPoolData, "is_recycling_team_member", false);
        if (CollectionUtils.notEmpty(objectDataList)) {
            for (IObjectData objectData : objectDataList) {
                objectData.set(LtoAccountFieldApiConstants.OWNER_MODIFIED_TIME, System.currentTimeMillis());
                objectData.set(LtoAccountFieldApiConstants.LAST_FOLLOWED_TIME, System.currentTimeMillis());
                objectData.set(LtoAccountFieldApiConstants.CLAIMED_TIME, System.currentTimeMillis());
                Integer transferCount = ObjectDataUtil.getIntegerValue(objectData, LtoAccountFieldApiConstants.TRANSFER_COUNT, 0);
                objectData.set(LtoAccountFieldApiConstants.TRANSFER_COUNT, transferCount + 1);
                objectData.set(LtoAccountFieldApiConstants.ACCOUNT_STATUS, 3);
                objectData.set(LtoFieldApiConstants.OWNER, Lists.newArrayList(user.getUpstreamOwnerIdOrUserId()));
                objectData.set(LtoFieldApiConstants.DATA_OWN_DEPARTMENT, StringUtils.isBlank(ownerDeptId) ? Lists.newArrayList() : Lists.newArrayList(ownerDeptId));
                objectData.setLastModifiedBy(user.getUpstreamOwnerIdOrUserId());
                objectData.setLastModifiedTime(System.currentTimeMillis());
                objectData.set(LtoFieldApiConstants.BIZ_STATUS,  LtoAccountFieldApiConstants.BizStatus.ALLOCATED.getValue());
                if (StringUtils.isNotBlank(partnerId)) {
                    objectData.set(LtoFieldApiConstants.PARTNER_ID, partnerId);
                }
                if (StringUtils.isNotBlank(user.getOutUserId())) {
                    objectData.setOutOwner(Lists.newArrayList(user.getOutUserId()));
                    objectData.setOutTenantId(user.getOutTenantId());
                }
            }
        }
        List<String> updateFieldList = Lists.newArrayList(LtoAccountFieldApiConstants.LAST_FOLLOWED_TIME, LtoAccountFieldApiConstants.CLAIMED_TIME, LtoAccountFieldApiConstants.TRANSFER_COUNT,
                LtoAccountFieldApiConstants.ACCOUNT_STATUS, LtoFieldApiConstants.OWNER, LtoAccountFieldApiConstants.OWNER_MODIFIED_TIME, LtoFieldApiConstants.DATA_OWN_DEPARTMENT, LtoFieldApiConstants.LAST_MODIFIED_BY,
                LtoFieldApiConstants.LAST_MODIFIED_TIME, LtoFieldApiConstants.BIZ_STATUS);
        if (!StringUtils.isNotBlank(partnerId)) {
            updateFieldList.add(LtoFieldApiConstants.PARTNER_ID);
        }
        String outTenant = null;
        String outOwner = null;
        if (StringUtils.isNotBlank(user.getOutUserId())) {
            updateFieldList.add(LtoFieldApiConstants.OUT_OWNER);
            updateFieldList.add(LtoFieldApiConstants.OUT_TENANT_ID);
            outTenant = user.getOutTenantId();
            outOwner = user.getOutUserId();
        }

        try {
            serviceFacade.batchUpdateByFields(user, objectDataList, updateFieldList);
            if (cleanTeamMember) {
                teamMemberService.removeObjectAllInnerTeamMember(user, objectDataList, oldDataList, false);
            }
            if (!StringUtils.isNotBlank(outOwner)) {
                teamMemberService.changeOwner(user, user.getUpstreamOwnerIdOrUserId(), objectDataList, outTenant, outOwner);
            } else {
                teamMemberService.changeInnerOwner(user, user.getUpstreamOwnerIdOrUserId(), objectDataList);
            }
            updateContactOwner(user, objectIds, user.getUpstreamOwnerIdOrUserId(), ownerDeptId, cleanTeamMember, true);
            result.setSuccessList(objectIds);
        } catch (Exception e) {
            log.error("choose error", e);
            ExceptionUtil.throwCommonBusinessException();
        }
        return result;
    }

    @Override
    public ObjectPoolActionModels.Result move(User user, String objectPoolId, List<String> objectIds, String eventId) {
        ObjectPoolActionModels.Result result = ObjectPoolActionModels.Result.builder().build();
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIds(user.getTenantId(), objectIds, Utils.ACCOUNT_API_NAME);
        if(CollectionUtils.empty(objectDataList)) {
            return result;
        }
        IObjectData objectPoolData = getObjectPoolById(user.getTenantId(), objectPoolId);
        boolean cleanOwner = ObjectDataUtil.getBooleanValue(objectPoolData, "is_clean_owner", false);
        boolean cleanTeamMember = ObjectDataUtil.getBooleanValue(objectPoolData, "is_recycling_team_member", false);
        PoolEmptyRule.EmptyRule emptyRule = new PoolEmptyRule.Builder().poolData(Lists.newArrayList(objectPoolData)).build().getEmptyRuleById(objectPoolData.getId());
        List<IObjectData> oldDataList = ObjectDataExt.copyList(objectDataList);
        List<String> updatePoolIds = getPoolIds(objectDataList);
        // 被移除外部负责人的数据
        List<IObjectData> removedOutOwnerDataList = getRemoveOutOwnerDataWhenCarry2Pool(emptyRule, objectDataList, ObjectAction.MOVE);
        for (IObjectData objectData : objectDataList) {
            if (cleanOwner) {
                objectData.set(LtoFieldApiConstants.OWNER, Lists.newArrayList());
                objectData.set(LtoAccountFieldApiConstants.ACCOUNT_STATUS, 2);
                objectData.set(LtoFieldApiConstants.DATA_OWN_DEPARTMENT, Lists.newArrayList());
                objectData.set(LtoAccountFieldApiConstants.OWNER_MODIFIED_TIME, System.currentTimeMillis());
                objectData.set(LtoFieldApiConstants.BIZ_STATUS, LtoAccountFieldApiConstants.BizStatus.UN_ALLOCATED.getValue());
            } else {
                objectData.set(LtoAccountFieldApiConstants.CLAIMED_TIME, System.currentTimeMillis());
            }
            objectData.set(LtoAccountFieldApiConstants.HIGH_SEAS_ID, objectPoolId);
            objectData.setLastModifiedBy(user.getUpstreamOwnerIdOrUserId());
            objectData.setLastModifiedTime(System.currentTimeMillis());
        }
        List<String> updateFieldList = Lists.newArrayList(LtoAccountFieldApiConstants.HIGH_SEAS_ID, LtoFieldApiConstants.LAST_MODIFIED_BY, LtoFieldApiConstants.LAST_MODIFIED_TIME);
        if (cleanOwner) {
            updateFieldList.add(LtoFieldApiConstants.OWNER);
            updateFieldList.add(LtoAccountFieldApiConstants.ACCOUNT_STATUS);
            updateFieldList.add(LtoFieldApiConstants.DATA_OWN_DEPARTMENT);
            updateFieldList.add(LtoAccountFieldApiConstants.OWNER_MODIFIED_TIME);
            updateFieldList.add(LtoFieldApiConstants.BIZ_STATUS);
        } else {
            updateFieldList.add(LtoAccountFieldApiConstants.CLAIMED_TIME);
        }

        if (!updatePoolIds.contains(objectPoolId)) {
            updatePoolIds.add(objectPoolId);
        }
        if (CollectionUtils.notEmpty(removedOutOwnerDataList)) {
            boolean update = teamMemberService.removeObjectPartnerByOutOwnerNotUpdate(user, removedOutOwnerDataList);
            if (update){
                updateFieldList.add(LtoFieldApiConstants.PARTNER_ID);
            }
        }
        try {
            serviceFacade.batchUpdateByFields(user, objectDataList, updateFieldList);

            if (cleanTeamMember) {
                teamMemberService.removeObjectAllInnerTeamMember(user, objectDataList, oldDataList, true);
            }
            if (cleanOwner) {
                teamMemberService.removeObjectInnerOwner(user, objectDataList);
            }
            if (CollectionUtils.notEmpty(removedOutOwnerDataList)) {
                teamMemberService.moveActionOutTeamHandle(user, emptyRule, removedOutOwnerDataList);
            }
            result.setSuccessList(objectIds);
        } catch (Exception e) {
            log.error("move error", e);
            ExceptionUtil.throwCommonBusinessException();
        }
        return result;
    }

    @Override
    public ObjectPoolActionModels.Result back(User user, String objectPoolId, List<String> objectIds, Integer operationType,
                                           String backReason, String backReasonOther, String eventId, boolean isPrmOpen) {
        ObjectPoolActionModels.Result result = ObjectPoolActionModels.Result.builder().build();
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIds(user.getTenantId(), objectIds, Utils.ACCOUNT_API_NAME);
        if(CollectionUtils.empty(objectDataList)) {
            return result;
        }
        IObjectData objectPoolData = getObjectPoolById(user.getTenantId(), objectPoolId);
        boolean cleaTeamMember = ObjectDataUtil.getBooleanValue(objectPoolData, "is_recycling_team_member", false);
        PoolEmptyRule.EmptyRule emptyRule = new PoolEmptyRule.Builder().poolData(Lists.newArrayList(objectPoolData)).build().getEmptyRuleById(objectPoolId);
        List<IObjectData> oldDataList = ObjectDataExt.copyList(objectDataList);
        List<IObjectData> removedOutOwnerDataList = getRemoveOutOwnerDataWhenCarry2Pool(emptyRule, objectDataList, ObjectAction.RETURN);
        for (IObjectData objectData : objectDataList) {
            ObjectAction action = ObjectAction.TAKE_BACK;
            String owner = ObjectDataUtil.getOwner(objectData);
            objectData.setOwner(Lists.newArrayList());
            objectData.set(LtoAccountFieldApiConstants.ACCOUNT_STATUS, 2);
            if (operationType == 1) {
                String backReasonAll = ("other".equals(backReason) && StringUtils.isNotBlank(backReasonOther)) ?
                        backReason + backReasonOther : backReason;
                objectData.set(LtoAccountFieldApiConstants.BACK_REASON, backReasonAll);
                action = ObjectAction.RETURN;
            }
            String recyclingReason = getRecyclingReason(user, action, owner);
            objectData.set(LtoAccountFieldApiConstants.CLAIMED_TIME, null);
            objectData.set(LtoAccountFieldApiConstants.RETURNED_TIME, System.currentTimeMillis());
            objectData.set(LtoFieldApiConstants.DATA_OWN_DEPARTMENT, Lists.newArrayList());
            objectData.set(LtoAccountFieldApiConstants.OWNER_MODIFIED_TIME, System.currentTimeMillis());
            objectData.set(LtoAccountFieldApiConstants.RECYCLED_REASON, recyclingReason);
            objectData.set(LtoAccountFieldApiConstants.HIGH_SEAS_ID, objectPoolId);
            objectData.setLastModifiedBy(user.getUpstreamOwnerIdOrUserId());
            objectData.setLastModifiedTime(System.currentTimeMillis());
            objectData.set(LtoFieldApiConstants.BIZ_STATUS, LtoAccountFieldApiConstants.BizStatus.UN_ALLOCATED.getValue());
        }

        List<String> updateFieldList = Lists.newArrayList(LtoAccountFieldApiConstants.RETURNED_TIME, LtoAccountFieldApiConstants.CLAIMED_TIME, LtoAccountFieldApiConstants.ACCOUNT_STATUS, LtoFieldApiConstants.OWNER,
                LtoFieldApiConstants.LAST_MODIFIED_BY, LtoFieldApiConstants.LAST_MODIFIED_TIME, LtoFieldApiConstants.DATA_OWN_DEPARTMENT, LtoAccountFieldApiConstants.OWNER_MODIFIED_TIME, LtoFieldApiConstants.BIZ_STATUS,
                LtoAccountFieldApiConstants.RECYCLED_REASON, LtoAccountFieldApiConstants.HIGH_SEAS_ID);
        if (operationType == 1) {
            updateFieldList.add(LtoAccountFieldApiConstants.BACK_REASON);
        }
        if (CollectionUtils.notEmpty(removedOutOwnerDataList)) {
            boolean update = teamMemberService.removeObjectPartnerByOutOwnerNotUpdate(user, removedOutOwnerDataList);
            if (update){
                updateFieldList.add(LtoFieldApiConstants.PARTNER_ID);
            }
        }

        try {
            serviceFacade.batchUpdateByFields(user, objectDataList, updateFieldList);

            if (cleaTeamMember) {
                teamMemberService.removeObjectAllInnerTeamMember(user, objectDataList, oldDataList, false);
            }
            teamMemberService.removeObjectInnerOwner(user, objectDataList);
            if (CollectionUtils.notEmpty(removedOutOwnerDataList)) {
                teamMemberService.backActionOutTeamHandle(user, emptyRule, removedOutOwnerDataList);
            }
            asyncUpdateContactOwner(user, objectIds, "", null,  cleaTeamMember, true);
            savePoolClaimLog(user, objectPoolId, oldDataList, user.isOutUser(), false, ObjectAction.RETURN.getActionCode());
            result.setSuccessList(objectIds);
        } catch (Exception e) {
            log.error("back error", e);
            ExceptionUtil.throwCommonBusinessException();
        }
        return result;
    }

    @Override
    public ObjectPoolActionModels.Result takeBack(User user, String objectPoolId, List<String> objectIds, String eventId, boolean isPrmOpen) {
        ObjectPoolActionModels.Result result = ObjectPoolActionModels.Result.builder().build();
        IObjectData objectPoolData = getObjectPoolById(user.getTenantId(), objectPoolId);
        boolean cleaTeamMember = ObjectDataUtil.getBooleanValue(objectPoolData, "is_recycling_team_member", false);
        PoolEmptyRule.EmptyRule emptyRule = new PoolEmptyRule.Builder().poolData(Lists.newArrayList(objectPoolData)).build().getEmptyRuleById(objectPoolId);
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIds(user.getTenantId(), objectIds, Utils.ACCOUNT_API_NAME);
        List<IObjectData> oldDataList = ObjectDataExt.copyList(objectDataList);
        List<IObjectData> tempDataList = Lists.newArrayList();
        List<IObjectData> removedOutOwnerDataList = getRemoveOutOwnerDataWhenCarry2Pool(emptyRule, objectDataList, ObjectAction.TAKE_BACK);
        if (CollectionUtils.notEmpty(objectDataList)) {
            String owner;
            for (IObjectData objectData : objectDataList) {
                tempDataList.add(ObjectDataExt.of(objectData).copy());
                objectData.setOwner(Lists.newArrayList());
                objectData.set(LtoAccountFieldApiConstants.ACCOUNT_STATUS, 2);
                owner = ObjectDataUtil.getOwner(objectData);
                objectData.set(LtoAccountFieldApiConstants.CLAIMED_TIME, null);
                objectData.set(LtoAccountFieldApiConstants.RETURNED_TIME, System.currentTimeMillis());
                objectData.set(LtoFieldApiConstants.DATA_OWN_DEPARTMENT, Lists.newArrayList());
                objectData.set(LtoAccountFieldApiConstants.OWNER_MODIFIED_TIME, System.currentTimeMillis());
                objectData.set(LtoAccountFieldApiConstants.RECYCLED_REASON, getRecyclingReason(user, ObjectAction.TAKE_BACK, owner));
                objectData.set(LtoAccountFieldApiConstants.HIGH_SEAS_ID, objectPoolId);
                objectData.setLastModifiedBy(user.getUpstreamOwnerIdOrUserId());
                objectData.setLastModifiedTime(System.currentTimeMillis());
                objectData.set(LtoFieldApiConstants.BIZ_STATUS, LtoAccountFieldApiConstants.BizStatus.UN_ALLOCATED.getValue());
            }
        }
        List<String> updateFieldList = Lists.newArrayList(LtoAccountFieldApiConstants.RETURNED_TIME, LtoAccountFieldApiConstants.CLAIMED_TIME, LtoAccountFieldApiConstants.ACCOUNT_STATUS, LtoFieldApiConstants.OWNER,
                LtoFieldApiConstants.LAST_MODIFIED_BY, LtoFieldApiConstants.LAST_MODIFIED_TIME, LtoFieldApiConstants.DATA_OWN_DEPARTMENT, LtoAccountFieldApiConstants.OWNER_MODIFIED_TIME, LtoFieldApiConstants.BIZ_STATUS,
                LtoAccountFieldApiConstants.RECYCLED_REASON, LtoAccountFieldApiConstants.HIGH_SEAS_ID);
        if (CollectionUtils.notEmpty(removedOutOwnerDataList)) {
            boolean update = teamMemberService.removeObjectPartnerByOutOwnerNotUpdate(user, removedOutOwnerDataList);
            if (update){
                updateFieldList.add(LtoFieldApiConstants.PARTNER_ID);
            }
        }
        try {
            serviceFacade.batchUpdateByFields(user, objectDataList, updateFieldList);

            if (cleaTeamMember) {
                teamMemberService.removeObjectAllInnerTeamMember(user, objectDataList, oldDataList, false);
            }
            teamMemberService.removeObjectInnerOwner(user, objectDataList);
            if (CollectionUtils.notEmpty(removedOutOwnerDataList)) {
                teamMemberService.backActionOutTeamHandle(user, emptyRule, removedOutOwnerDataList);
            }
            asyncUpdateContactOwner(user, objectIds, "", null, cleaTeamMember, true);
            savePoolClaimLog(user, objectPoolId, tempDataList, false, true, ObjectAction.TAKE_BACK.getActionCode());
            result.setSuccessList(objectIds);
        } catch (Exception e) {
            log.error("takeBack error", e);
            ExceptionUtil.throwCommonBusinessException();
        }
        return result;
    }

    @Override
    public ObjectPoolActionModels.Result allocate(User user, String objectPoolId, List<String> objectIds, String owner, String eventId,
                                               Long outTenantId, Long outOwnerId, String partnerId) {
        ObjectPoolActionModels.Result result = ObjectPoolActionModels.Result.builder().build();
        IObjectData objectPoolData = getObjectPoolById(user.getTenantId(), objectPoolId);
        String ownerDeptId = ltoOrgCommonService.getMainDepartment(user.getTenantId(), owner);
        boolean cleaTeamMember = ObjectDataUtil.getBooleanValue(objectPoolData, "is_recycling_team_member", false);
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIds(user.getTenantId(), objectIds, Utils.ACCOUNT_API_NAME);
        boolean hasOutTenantId = false;
        boolean hasOutOwnerId = false;
        boolean hasPartnerId = false;
        String outTenant = null;
        String outOwner = null;
        if (outTenantId != null && outTenantId > 0) {
            hasOutTenantId = true;
            outTenant = String.valueOf(outTenantId);
        }
        if (outOwnerId != null && outOwnerId > 0) {
            hasOutOwnerId = true;
            outOwner = String.valueOf(outOwnerId);
        }
        if (!StringUtils.isNotBlank(partnerId)) {
            hasPartnerId = true;
        }
        if (CollectionUtils.notEmpty(objectDataList)) {
            for (IObjectData objectData : objectDataList) {
                objectData.setOwner(Lists.newArrayList(owner));
                objectData.set(LtoAccountFieldApiConstants.ACCOUNT_STATUS, 3);
                objectData.set(LtoAccountFieldApiConstants.CLAIMED_TIME, System.currentTimeMillis());
                objectData.set(LtoAccountFieldApiConstants.OWNER_MODIFIED_TIME, System.currentTimeMillis());
                objectData.set(LtoFieldApiConstants.DATA_OWN_DEPARTMENT, StringUtils.isBlank(ownerDeptId) ? Lists.newArrayList() : Lists.newArrayList(ownerDeptId));
                objectData.setLastModifiedBy(user.getUpstreamOwnerIdOrUserId());
                objectData.setLastModifiedTime(System.currentTimeMillis());
                objectData.set(LtoFieldApiConstants.BIZ_STATUS, LtoAccountFieldApiConstants.BizStatus.ALLOCATED.getValue());
                Integer transferCount = ObjectDataUtil.getIntegerValue(objectData, LtoAccountFieldApiConstants.TRANSFER_COUNT, 0);
                objectData.set(LtoAccountFieldApiConstants.TRANSFER_COUNT, transferCount + 1);
                String recyclingReason = getRecyclingReason(user, ObjectAction.ALLOCATE, ObjectDataUtil.getOwner(objectData));
                objectData.set(LtoAccountFieldApiConstants.RECYCLED_REASON, recyclingReason);
                if (hasOutTenantId) {
                    objectData.setOutTenantId(outTenant);
                }
                if (hasOutOwnerId) {
                    objectData.setOutOwner(Lists.newArrayList(outOwner));
                }
                if (hasPartnerId) {
                    objectData.set(LtoFieldApiConstants.PARTNER_ID, partnerId);
                }
            }
        }
        List<String> updateFieldList = Lists.newArrayList(LtoAccountFieldApiConstants.CLAIMED_TIME, LtoAccountFieldApiConstants.ACCOUNT_STATUS, LtoFieldApiConstants.OWNER, LtoAccountFieldApiConstants.OWNER_MODIFIED_TIME,
                LtoFieldApiConstants.LAST_MODIFIED_BY, LtoFieldApiConstants.LAST_MODIFIED_TIME, LtoFieldApiConstants.BIZ_STATUS, LtoFieldApiConstants.DATA_OWN_DEPARTMENT, LtoAccountFieldApiConstants.TRANSFER_COUNT, LtoAccountFieldApiConstants.RECYCLED_REASON);
        if (hasOutTenantId) {
            updateFieldList.add(LtoFieldApiConstants.OUT_TENANT_ID);
        }
        if (hasOutOwnerId) {
            updateFieldList.add(LtoFieldApiConstants.OUT_OWNER);
        }
        if (hasPartnerId) {
            updateFieldList.add(LtoFieldApiConstants.PARTNER_ID);
        }

        try {
            serviceFacade.batchUpdateByFields(user, objectDataList, updateFieldList);

            if (cleaTeamMember) {
                teamMemberService.removeObjectAllInnerTeamMember(user, objectDataList);
            }
            if (hasOutOwnerId) {
                teamMemberService.changeOwner(user, owner, objectDataList, outTenant, outOwner);
            } else {
                teamMemberService.changeInnerOwner(user, owner, objectDataList);
            }
            updateContactOwner(user, objectIds, owner, ownerDeptId, cleaTeamMember, true);
            savePoolClaimLog(user, objectPoolId, objectDataList, hasOutOwnerId, false, ObjectAction.ALLOCATE.getActionCode());
            result.setSuccessList(objectIds);
        } catch (Exception e) {
            log.error("allocate error", e);
            ExceptionUtil.throwCommonBusinessException();
        }
        return result;
    }

    @Override
    public ObjectPoolActionModels.Result remove(User user, List<IObjectData> objectDataList, String owner, boolean isKeepOwner, String eventId) {
        ObjectPoolActionModels.Result result = ObjectPoolActionModels.Result.builder().build();
        if (CollectionUtils.empty(objectDataList)) {
            return result;
        }
        List<String> objectIds = objectDataList.stream().map(x -> x.get(DBRecord.ID).toString()).collect(Collectors.toList());
        String ownerDept = ltoOrgCommonService.getMainDepartment(user.getTenantId(), owner);
        if (CollectionUtils.notEmpty(objectDataList)) {
            for (IObjectData objectData : objectDataList) {
                if (!isKeepOwner) {
                    String oldOwner = ObjectDataUtil.getOwner(objectData);
                    Integer transferCount = ObjectDataUtil.getIntegerValue(objectData, LtoAccountFieldApiConstants.TRANSFER_COUNT, 0);
                    if (!owner.equals(oldOwner)) {
                        objectData.set(LtoAccountFieldApiConstants.OWNER_MODIFIED_TIME, System.currentTimeMillis());
                        objectData.set(LtoAccountFieldApiConstants.TRANSFER_COUNT, transferCount + 1);
                    } else {
                        objectData.set(LtoAccountFieldApiConstants.OWNER_MODIFIED_TIME, objectData.get(LtoAccountFieldApiConstants.OWNER_MODIFIED_TIME));
                        objectData.set(LtoAccountFieldApiConstants.TRANSFER_COUNT, transferCount);
                    }
                    objectData.set(LtoFieldApiConstants.OWNER, Lists.newArrayList(owner));
                    objectData.set(LtoFieldApiConstants.DATA_OWN_DEPARTMENT, StringUtils.isBlank(ownerDept) ? Lists.newArrayList() : Lists.newArrayList(ownerDept));
                }
                objectData.set(LtoAccountFieldApiConstants.ACCOUNT_STATUS, 3);
                objectData.set(LtoFieldApiConstants.BIZ_STATUS, LtoAccountFieldApiConstants.BizStatus.ALLOCATED.getValue());
                objectData.set(LtoAccountFieldApiConstants.CLAIMED_TIME, System.currentTimeMillis());
                objectData.set(LtoAccountFieldApiConstants.HIGH_SEAS_ID, null);
                objectData.setLastModifiedBy(user.getUpstreamOwnerIdOrUserId());
                objectData.setLastModifiedTime(System.currentTimeMillis());
            }
        }
        List<String> updateFieldList = Lists.newArrayList(LtoAccountFieldApiConstants.CLAIMED_TIME, LtoAccountFieldApiConstants.ACCOUNT_STATUS, LtoAccountFieldApiConstants.HIGH_SEAS_ID,
                LtoFieldApiConstants.LAST_MODIFIED_BY, LtoFieldApiConstants.LAST_MODIFIED_TIME);
        if (!isKeepOwner) {
            updateFieldList.add(LtoFieldApiConstants.OWNER);
            updateFieldList.add(LtoFieldApiConstants.BIZ_STATUS);
            updateFieldList.add(LtoFieldApiConstants.DATA_OWN_DEPARTMENT);
            updateFieldList.add(LtoAccountFieldApiConstants.TRANSFER_COUNT);
            updateFieldList.add(LtoAccountFieldApiConstants.OWNER_MODIFIED_TIME);
        }

        try {
            serviceFacade.batchUpdateByFields(user, objectDataList, updateFieldList);
            if (!isKeepOwner) {
                teamMemberService.changeInnerOwner(user, owner, objectDataList);
                updateContactOwner(user, objectIds, owner, ownerDept, false, true);
            }
            result.setSuccessList(objectIds);
        } catch (Exception e) {
            log.error("remove error", e);
            ExceptionUtil.throwCommonBusinessException();
        }
        return result;
    }

    private String getRecyclingReason(User user, ObjectAction action, String owner) {
        String recyclingReason = "";
        switch (action) {
            case ALLOCATE:
                if (StringUtils.isNotEmpty(owner)) {
                    recyclingReason = "管理员（上级）收回";
                }
                break;
            case TAKE_BACK:
                recyclingReason = "管理员（上级）收回";
                break;
            case RETURN:
            case CHANGE_OWNER:
                if (user.getUpstreamOwnerIdOrUserId().equals(owner)) {
                    recyclingReason = "销售人员退回";
                } else {
                    recyclingReason = "管理员（上级）收回";
                }
                break;
            default:
                recyclingReason = "未成交/未跟进收回";
                break;
        }
        return recyclingReason;
    }

    private void asyncUpdateContactOwner(User user, List<String> objectIds, String owner, String dataOwnerDpt, boolean cleanTeamMember, boolean followConfig) {
        try {
            ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
            task.submit(() ->
                    updateContactOwner(user, objectIds, owner, dataOwnerDpt, cleanTeamMember, followConfig)
            );
            task.run();
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    public void updateContactOwner(User user, List<String> accountIds, String owner, String dataOwnerDpt, boolean cleanTeamMember, boolean followConfig) {
        if (CollectionUtils.empty(accountIds)) {
            return;
        }
        if (followConfig) {
            //是否开启客户收回分配时，同步变更本客户下联系人的负责人
            String configValue = configService.findTenantConfig(user, "contact_owner_rule_setting");
            if ("0".equals(configValue)) {
                return;
            }
        }
        int offset = 0;
        int limit = 200;
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, "account_id", accountIds);
        searchTemplateQuery.setFilters(filters);
        List<OrderBy> orderByList = Lists.newArrayList();
        OrderBy orderBy = new OrderBy();
        orderBy.setFieldName("create_time");
        orderBy.setIsAsc(true);
        orderByList.add(orderBy);
        searchTemplateQuery.setOrders(orderByList);
        searchTemplateQuery.setLimit(limit);
        searchTemplateQuery.setOffset(offset);
        int executeCount = 0;
        while (true) {
            ++executeCount;
            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user,
                    Utils.CONTACT_API_NAME, searchTemplateQuery);
            if (CollectionUtils.empty(queryResult.getData())) {
                break;
            }
            List<IObjectData> objectDataList = queryResult.getData();
            List<IObjectData> oldDataList = Lists.newArrayList();
            for (IObjectData objectData : objectDataList) {
                oldDataList.add(ObjectDataExt.of(objectData).copy());
                if (StringUtils.isEmpty(owner)) {
                    objectData.set(LtoFieldApiConstants.OWNER, Lists.newArrayList());
                } else {
                    objectData.set(LtoFieldApiConstants.OWNER, Lists.newArrayList(owner));
                }
                objectData.set(LtoAccountFieldApiConstants.OWNER_CHANGED_TIME, System.currentTimeMillis());
                if(StringUtils.isNotBlank(dataOwnerDpt)){
                    objectData.set(LtoFieldApiConstants.DATA_OWN_DEPARTMENT, Lists.newArrayList(dataOwnerDpt));
                }else{
                    objectData.set(LtoFieldApiConstants.DATA_OWN_DEPARTMENT, Lists.newArrayList());
                }
                objectData.setLastModifiedBy(user.getUpstreamOwnerIdOrUserId());
                objectData.setLastModifiedTime(System.currentTimeMillis());
            }
            List<String> updateFieldList = Lists.newArrayList(LtoFieldApiConstants.OWNER, LtoAccountFieldApiConstants.OWNER_CHANGED_TIME, LtoFieldApiConstants.DATA_OWN_DEPARTMENT,
                    LtoFieldApiConstants.LAST_MODIFIED_BY, LtoFieldApiConstants.LAST_MODIFIED_TIME);
            try {
                ActionContextUtil.ActionContextOp actionContextOp = ActionContextUtil.ActionContextOp.builder()
                        .updateLastModifyTime(true).skipVersionChange(false)
                        .allowUpdateInvalid(true).notValidate(true).batch(false).privilegeCheck(false)
                        .build();
                ObjectDataUtil.updateFields(user, objectDataList, updateFieldList, actionContextOp);
                IObjectDescribe contactDescribe = serviceFacade.findObject(user.getTenantId(), Utils.CONTACT_API_NAME);
                if (cleanTeamMember) {
                    teamMemberService.removeObjectAllInnerTeamMember(user, objectDataList, oldDataList, true);
                }

                if (StringUtils.isEmpty(owner)) {
                    teamMemberService.removeObjectInnerOwner(user, objectDataList);
                } else {
                    teamMemberService.changeInnerOwner(user, owner, objectDataList);
                }

                recordOwnerChangeHistory(user, oldDataList);

                serviceFacade.logByActionType(user, EventType.MODIFY, ActionType.ChangeOwner, oldDataList, objectDataList, contactDescribe);

                offset += limit;
                searchTemplateQuery.setOffset(offset);
            } catch (Exception e) {
                log.error("updateContactOwner ", e);
               ExceptionUtil.throwCommonBusinessException();
            } finally {
                if (StringUtils.isNotBlank(owner)) {
                    contactSessionSandwichService.push(user.getTenantId(), owner);
                }
            }
            List<String> ids = objectDataList.stream().map(x -> x.getId()).collect(Collectors.toList());
            sendHandleRelationshipMsgByIds(user.getTenantId(), ids, RelationshipProducer.OperationType.RESULT_ONE.getValue());

            if (executeCount > 10000) {
                log.error("update updateContactOwner, user:{}, accountIds:{}, owner:{}, cleanTeamMember:{}", user,
                        accountIds, owner, cleanTeamMember);
                break;
            }
        }
    }

    private void recordOwnerChangeHistory(User user, List<IObjectData> objectDataList) {
        try {
            ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
            task.submit(() ->
                saveOwnerChangeHistory(user, objectDataList)
            );
            task.run();
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    private void saveOwnerChangeHistory(User user, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        String tableName = "biz_contact_owner_history";
        List<Map<String, Object>> insertMap = Lists.newArrayList();
        objectDataList.forEach(m -> {
            if (CollectionUtils.notEmpty(m.getOwner())) {
                Map<String, Object> insertData = Maps.newHashMap();
                insertData.put("id", serviceFacade.generateId());
                insertData.put(Tenantable.TENANT_ID, user.getTenantId());
                insertData.put("contact_id", m.getId());
                insertData.put("owner", m.getOwner().get(0));
                insertData.put("object_describe_api_name", "ContactObj");
                insertData.put("created_by", user.getUpstreamOwnerIdOrUserId());
                insertData.put("create_time", System.currentTimeMillis());
                insertData.put("last_modified_by", user.getUpstreamOwnerIdOrUserId());
                insertData.put("last_modified_time", System.currentTimeMillis());
                insertData.put("is_deleted", 0);
                insertMap.add(insertData);
            }
        });
        try {
            CommonSqlUtil.insertData(user.getTenantId(), tableName, insertMap);
        } catch (Exception e) {
            log.error("saveOwnerChangeHistory error");
            ExceptionUtil.throwCommonBusinessException();
        }
        Set<String> owners = objectDataList.stream()
                .map(IObjectData::getOwner)
                .filter(CollectionUtils::notEmpty).flatMap(Collection::stream).collect(Collectors.toSet());
        //通过企信服务发送联系人变动通知
        contactSessionSandwichService.push(user.getTenantId(), Lists.newArrayList(owners));
    }

    private void sendHandleRelationshipMsgByIds(String tenantId, List<String> dataIds, String operationType){
        try {
            if(CollectionUtils.notEmpty(dataIds)){
                List<List<String>> dataIdsList = ListsUtil.splitList(dataIds,200);
                dataIdsList.stream().forEach(ids->
                    relationshipProducer.setMsgToSend(tenantId, ids, operationType, Utils.CONTACT_API_NAME)
                );
            }
        }catch (Exception  e){
            log.error("ContactUtils.sendHandleRelationshipMsgByIds e:{}",e);
        }
    }
}
