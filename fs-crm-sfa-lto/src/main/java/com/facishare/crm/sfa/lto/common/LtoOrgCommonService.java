package com.facishare.crm.sfa.lto.common;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.lto.exception.ExceptionUtil;
import com.facishare.crm.sfa.lto.rest.PaasUserGroupProxy;
import com.facishare.crm.sfa.lto.rest.PaasUserRoleProxy;
import com.facishare.crm.sfa.lto.rest.models.PAASContext;
import com.facishare.crm.sfa.lto.rest.models.UserGroupModel;
import com.facishare.crm.sfa.lto.rest.models.UserRoleModel;
import com.facishare.crm.sfa.lto.utils.CommonSqlUtil;
import com.facishare.crm.sfa.lto.utils.CommonUtil;
import com.facishare.crm.sfa.lto.utils.HttpHeaderUtil;
import com.facishare.enterprise.common.result.ResultCode;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.service.dto.QueryAllSuperDeptsByDeptIds;
import com.facishare.paas.appframework.common.service.dto.QueryDeptByName;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByUserId;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByUserIds;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.support.GDSHandler;
import com.fxiaoke.enterpriserelation2.arg.*;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.result.*;
import com.fxiaoke.enterpriserelation2.service.EmployeeCardService;
import com.fxiaoke.enterpriserelation2.service.EnterpriseRelationService;
import com.fxiaoke.enterpriserelation2.service.PublicEmployeeService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


@Component
@Slf4j
public class LtoOrgCommonService {
    @Autowired
    private OrgService orgService;
    @Autowired
    private PaasUserGroupProxy paasUserGroupProxy;
    @Autowired
    private PaasUserRoleProxy paasUserRoleProxy;
    @Autowired
    private EnterpriseRelationService enterpriseRelationService;
    @Autowired
    private PublicEmployeeService publicEmployeeService;
    @Autowired
    private EmployeeCardService employeeCardService;
    @Autowired
    private GDSHandler gdsHandler;
    @Autowired
    private EIEAConverter eieaConverter;


    private static final String PARTNER_API_NAME = "PartnerObj";
    private static final String CRM_APP = "CRM";

    /**
     *取部门所有人员
     * @param tenantId
     * @param deptIds
     * @param excludeStopUser true: 过滤停用员工，false：不过滤
     * @return
     */
    public List<String> getMembersByDeptIds(String tenantId, List<String> deptIds, boolean excludeStopUser) {
        if(CollectionUtils.empty(deptIds)){
            return Lists.newArrayList();
        }
        List<String> membersByDeptIds = Lists.newArrayList();
        try {
            if(excludeStopUser) {
                membersByDeptIds = orgService.getMembersByDeptIds(CommonUtil.buildUser(tenantId), deptIds,0);
            }else {
                membersByDeptIds = orgService.getMembersByDeptIds(CommonUtil.buildUser(tenantId), deptIds);
            }
        } catch (Exception e) {
            log.error("find ObjectFollowDealSettingObj throw tenantId:{}，deptIds:{}", tenantId, deptIds, e);
            ExceptionUtil.throwCommonBusinessException();
        }
        return membersByDeptIds;
    }

    /**
     * 取人员主属部门
     * @param tenantId
     * @param userId
     * @return
     */
    public String getMainDepartment(String tenantId, String userId) {
        Map<String, QueryDeptInfoByUserIds.MainDeptInfo> deptInfo = orgService.getMainDeptInfo(tenantId, User.SUPPER_ADMIN_USER_ID, Lists.newArrayList(userId));
        if (deptInfo.get(userId) == null) {
            log.warn("deptInfo is empty :{},userId:{}", tenantId, userId);
        }
        if (deptInfo.get(userId) != null) {
            return deptInfo.get(userId).getDeptId();
        }
        return null;
    }

    /**
     * 获取人员所在部门
     * @param tenantId
     * @param userId
     * @return
     */
    public List<String> getDepartmentByUserId(String tenantId, String userId) {
        List<QueryDeptInfoByUserId.DeptInfo> deptInfos = orgService.getDeptInfoByUserId(tenantId, User.SUPPER_ADMIN_USER_ID, userId);
        return deptInfos.stream().map(QueryDeptInfoByUserId.DeptInfo::getDeptId).collect(Collectors.toList());
    }

    /**
     * 获取部门树
     *
     * @param tenantId
     * @param userId
     * @return
     */
    public List<String> getNDeptPathByUserId(String tenantId, String userId) {
        List<String> deptIds = getDepartmentByUserId(tenantId, userId);
        return getAllSuperDepartmentByIds(tenantId, deptIds);
    }

    /**
     * 获取部门所有上级部门
     * @param tenantId
     * @param departmentIds
     * @return
     */
    public List<String> getAllSuperDepartmentByIds(String tenantId, List<String> departmentIds) {
        Map<String, List<QueryAllSuperDeptsByDeptIds.DeptInfo>> deptInfosMap = orgService.getAllSuperDeptsByDeptIds(tenantId, User.SUPPER_ADMIN_USER_ID, departmentIds);
        Set<String> result = Sets.newHashSet();
        for(Map.Entry<String, List<QueryAllSuperDeptsByDeptIds.DeptInfo>> keyValue : deptInfosMap.entrySet()){
            result.addAll(keyValue.getValue().stream().filter(x-> !"999999".equals(x.getId()))
                    .map(QueryAllSuperDeptsByDeptIds.DeptInfo::getId).collect(Collectors.toList()));
        }

        return Lists.newArrayList(result);
    }

    /**
     * 获取部门所有下级部门
     * @param tenantId
     * @param departmentIds
     * @return
     */
    public List<String> getAllSubDepartmentByIds(String tenantId, List<String> departmentIds) {
        Map<String, List<QueryDeptByName.DeptInfo>> deptInfosMap = orgService.getSubDeptsByDeptIds(tenantId, User.SUPPER_ADMIN_USER_ID, departmentIds, 0);
        Set<String> result = Sets.newHashSet();
        for(Map.Entry<String, List<QueryDeptByName.DeptInfo>> keyValue : deptInfosMap.entrySet()){
            result.addAll(keyValue.getValue().stream().map(QueryDeptByName.DeptInfo::getId).collect(Collectors.toList()));
        }

        return Lists.newArrayList(result);
    }

    /**
     * 获取用户组所有用户
     * @param tenantId
     * @param userGroupIds
     * @param excludeStopUser
     * @return
     */
    public Map<String, List<String>> getMembersByUserGroupIds(String tenantId, List<String> userGroupIds, boolean excludeStopUser) {
        Map<String, List<String>> members = Maps.newHashMap();
        if(CollectionUtils.empty(userGroupIds)){
            return members;
        }
        try {
            PAASContext contextArg = buildPAASContext(tenantId);
            UserGroupModel.UserGroupListWithContextArg arg = UserGroupModel.UserGroupListWithContextArg.builder().context(contextArg)
                    .groupIdList(userGroupIds).filterByUser(false).publicFlag(true).status(0).build();
            UserGroupModel.UserGroupListResult rstResult = paasUserGroupProxy.getUserGroupMembers(HttpHeaderUtil.getHeaders(tenantId), arg);
            members = rstResult.getGroupMems();
            if(excludeStopUser) {
                Set<String> allMembers = Sets.newHashSet();
                for (Map.Entry<String, List<String>> keyValue : members.entrySet()) {
                    allMembers.addAll(keyValue.getValue());
                }
                List<String> allActiveMembers = filterStopUsers(tenantId, Lists.newArrayList(allMembers));

                for (Map.Entry<String, List<String>> keyValue : members.entrySet()) {
                    keyValue.getValue().removeIf(x -> !allActiveMembers.contains(x));
                }
            }
        } catch (Exception e) {
            log.error("getMembersByUserGroupIds throw exception {},tenantId:{}, userGroupIds:{}", e.getMessage(), tenantId, userGroupIds);
            ExceptionUtil.throwCommonBusinessException();
        }
        return members;
    }

    /**
     *
     * @param tenantId
     * @param memberId
     * @return
     */
    public List<String> getUserGroupIdsByMemberId(String tenantId, String memberId) {
        List<String> userGroups = Lists.newArrayList();
        if(StringUtils.isEmpty(memberId)){
            return userGroups;
        }
        try {
            UserGroupModel.UserGroupUserListArg arg = UserGroupModel.UserGroupUserListArg.builder().appId(CRM_APP)
                    .filterByUser(false).isPublic(true).status(0).userId(User.SUPPER_ADMIN_USER_ID)
                    .userIdList(Lists.newArrayList(memberId)).tenantId(tenantId).page(null).build();
            UserGroupModel.UserGroupListPageResult rstResult = paasUserGroupProxy.getUserGroupByMemberIds(HttpHeaderUtil.getHeaders(tenantId), arg);
            userGroups = rstResult.getResult();
        } catch (Exception e) {
            log.error("getUserGroupIdsByMemberId throw exception {},tenantId:{}, memberId:{}", e.getMessage(), tenantId, memberId);
            ExceptionUtil.throwCommonBusinessException();
        }
        return userGroups;
    }

    /**
     * 获取用户组人员
     * @param tenantId
     * @param userGroupIds
     * @return
     */
    public List<UserGroupModel.UserGroupPoto> getUserGroupByIds(String tenantId, List<String> userGroupIds) {
        List<UserGroupModel.UserGroupPoto> userGroups = Lists.newArrayList();
        if(CollectionUtils.empty(userGroupIds)){
            return userGroups;
        }
        try {
            UserGroupModel.UserGroupListArg arg = UserGroupModel.UserGroupListArg.builder().appId(CRM_APP)
                    .filterByUser(false).isPublic(true).userId(User.SUPPER_ADMIN_USER_ID)
                    .groupIdList(userGroupIds).tenantId(tenantId).page(null).build();
            UserGroupModel.UserGroupPageResult rstResult = paasUserGroupProxy.getUserGroupByIds(HttpHeaderUtil.getHeaders(tenantId), arg);
            userGroups = rstResult.getResult();
        } catch (Exception e) {
            log.error("getUserGroupIdsByMemberId throw exception {},tenantId:{}, userGroupIds:{}", e.getMessage(), tenantId, userGroupIds);
            ExceptionUtil.throwCommonBusinessException();
        }
        return userGroups;
    }

    public PAASContext buildPAASContext(String tenantId) {
        return PAASContext.builder().appId(CRM_APP).tenantId(tenantId).userId(User.SUPPER_ADMIN_USER_ID).build();
    }

    /**
     *批量获取用户组人员
     * @param tenantId
     * @param userGroupIds
     * @param excludeStopUser
     * @return
     */
    public List<String> batchGetMembersByUserGroupIds(String tenantId, List<String> userGroupIds, boolean excludeStopUser) {
        List<String> members = Lists.newArrayList();
        if(CollectionUtils.empty(userGroupIds)){
            return members;
        }
        try {
            UserGroupModel.GetUserGroupMembersArg arg = UserGroupModel.GetUserGroupMembersArg.builder().appId("CRM")
                    .userId(User.SUPPER_ADMIN_USER_ID).groupIdList(userGroupIds).tenantId(tenantId).build();
            UserGroupModel.GetUserGroupMembersResult rstResult = paasUserGroupProxy.batchGetUserGroupMembers(HttpHeaderUtil.getHeaders(tenantId), arg);
            members = rstResult.getResult();
            if (excludeStopUser) {
                members = filterStopUsers(tenantId, members);
            }
        } catch (Exception e) {
            log.error("getUserGroupIdsByMemberId throw exception tenantId:{}, userGroupIds:{}", tenantId, userGroupIds,e);
            ExceptionUtil.throwCommonBusinessException();
        }
        return members;
    }

    /**
     * 获取用户角色组人员
     * @param tenantId
     * @param userRoleIds
     * @param excludeStopUser
     * @return
     */
    public Map<String,List<String>> getRoleUsersByRoleIds(String tenantId, List<String> userRoleIds, boolean excludeStopUser) {
        Map<String, List<String>> members = Maps.newHashMap();
        if(CollectionUtils.empty(userRoleIds)){
            return members;
        }
        try {
            PAASContext contextArg = buildPAASContext(tenantId);
            UserRoleModel.UserRoleListWithContextArg arg = UserRoleModel.UserRoleListWithContextArg.builder()
                    .authContext(contextArg).roles(userRoleIds).build();
            UserRoleModel.UserRoleMapResult rstResult = paasUserRoleProxy.queryRoleUsersByRoles(HttpHeaderUtil.getHeaders(tenantId), arg);
            members = rstResult.getResult();
            if(excludeStopUser) {
                Set<String> allMembers = Sets.newHashSet();
                for (Map.Entry<String, List<String>> keyValue : members.entrySet()) {
                    allMembers.addAll(keyValue.getValue());
                }
                List<String> allActiveMembers = filterStopUsers(tenantId, Lists.newArrayList(allMembers));

                for (Map.Entry<String, List<String>> keyValue : members.entrySet()) {
                    keyValue.getValue().removeIf(x -> !allActiveMembers.contains(x));
                }
            }
        } catch (Exception e) {
            log.error("getRoleUsersByRoleIds throw exception,tenantId:{}, userRoleIds:{}", tenantId, userRoleIds,e);
            ExceptionUtil.throwCommonBusinessException();
        }
        return members;
    }

    /**
     * 批量用户角色组所有人员
     * @param tenantId
     * @param userRoleIds
     * @param excludeStopUser
     * @return
     */
    public List<String> batchGetRoleUsersByRoleIds(String tenantId, List<String> userRoleIds, boolean excludeStopUser) {
        if(CollectionUtils.empty(userRoleIds)){
            return Lists.newArrayList();
        }

        Map<String,List<String>> result = getRoleUsersByRoleIds(tenantId, userRoleIds, excludeStopUser);

        Set<String> members = Sets.newHashSet();
        for(Map.Entry<String, List<String>> keyValue : result.entrySet()){
            members.addAll(keyValue.getValue());
        }

        return Lists.newArrayList(members);
    }

    /**
     * 根据人员列表取所在用户角色组
     * @param tenantId
     * @param userIds
     * @return
     */
    public Map<String,List<String>> getUserRoleIdsByUserIds(String tenantId, List<String> userIds) {
        Map<String, List<String>> members = Maps.newHashMap();
        if(CollectionUtils.empty(userIds)){
            return members;
        }
        try {
            PAASContext contextArg = buildPAASContext(tenantId);
            UserRoleModel.UserRoleUsersWithContextArg arg = UserRoleModel.UserRoleUsersWithContextArg.builder()
                    .authContext(contextArg).users(userIds).build();
            UserRoleModel.UserRoleMapResult rstResult = paasUserRoleProxy.queryUserRoleCodesByUsers(HttpHeaderUtil.getHeaders(tenantId), arg);
            members = rstResult.getResult();
        } catch (Exception e) {
            log.error("getUserRoleIdsByUserIds throw exception ,tenantId:{}, userIds:{}",  tenantId, userIds,e);
            ExceptionUtil.throwCommonBusinessException();
        }
        return members;
    }

    /**
     * 根据人员取所在角色组
     * @param tenantId
     * @param userId
     * @return
     */
    public List<String> getUserRoleIdsByUserId(String tenantId, String userId) {
        if(StringUtils.isEmpty(userId)){
            return Lists.newArrayList();
        }
        List<String> members = Lists.newArrayList();
        try {
            PAASContext contextArg = buildPAASContext(tenantId);
            UserRoleModel.UserRoleUsersWithContextArg arg = UserRoleModel.UserRoleUsersWithContextArg.builder()
                    .authContext(contextArg).users(Lists.newArrayList(userId)).build();
            UserRoleModel.UserRoleMapResult rstResult = paasUserRoleProxy.queryUserRoleCodesByUsers(HttpHeaderUtil.getHeaders(tenantId), arg);

            if(rstResult.getResult().containsKey(userId)){
                members.addAll(rstResult.getResult().getOrDefault(userId, Lists.newArrayList()));
            }
        } catch (Exception e) {
            log.error("getUserRoleIdsByUserIds throw exception ,tenantId:{}, userId:{}", tenantId, userId, e);
            ExceptionUtil.throwCommonBusinessException();
        }
        return members;
    }

    public List<String> filterStopUsers(String tenantId, List<String> userIds) {
        List<String> list = Lists.newArrayList();
        if (CollectionUtils.empty(userIds)) {
            return list;
        }
        try {
            String userString = CommonUtil.buildSqlInString(userIds);
            String sql = String.format("SELECT user_id FROM org_employee_user WHERE tenant_id='%s' AND status='0' " +
                    "AND user_id=any(array[%s]) ", tenantId, userString);

            List<Map> maps = CommonSqlUtil.findBySql(tenantId, sql);
            if (CollectionUtils.notEmpty(maps)){
                for (Map map : maps) {
                    list.add(map.get("user_id").toString());
                }
            }
        } catch (Exception e) {
            ExceptionUtil.throwCommonBusinessException();
        }
        return list;
    }

    /**
     * 获取合作伙伴外部负责人
     * @param tenantId
     * @param partnerIds
     * @return
     */
    public List<String> getPartnerOutUserIds(String tenantId, List<String> partnerIds) {
        if(CollectionUtils.empty(partnerIds)){
            return Lists.newArrayList();
        }
        Set<String> result = Sets.newHashSet();
        Map<String, Integer> partnerOuterTenantMap = getPartnerOuterTenant(tenantId, partnerIds);
        if(partnerOuterTenantMap != null) {
            List<Long> outerTenantIds = partnerOuterTenantMap.values().stream().map(Long::valueOf).collect(Collectors.toList());
            Map<String, List<String>> outerTenantUserMap = getOuterTenantsUserIds(tenantId, outerTenantIds);
            for(Map.Entry<String, List<String>> entry : outerTenantUserMap.entrySet()) {
                result.addAll(entry.getValue());
            }
        }

        return  Lists.newArrayList(result);
    }

    /**
     * 获取合作伙伴外部企业
     * @param tenantId
     * @param partnerIds
     * @return
     */
    public Map<String, Integer> getPartnerOuterTenant(String tenantId, List<String> partnerIds) {
        if(CollectionUtils.empty(partnerIds)) {
            return Maps.newHashMap();
        }
        Integer ei = Integer.parseInt(tenantId);
        BatchGetRelationDownstreamAndOwnerArg arg = new BatchGetRelationDownstreamAndOwnerArg();
        arg.setUpstreamTenantId(ei);
        arg.setObjectApiName(PARTNER_API_NAME);
        arg.setCrmDataIds(partnerIds);
        RestResult<Map<String, RelationDownstreamResult>> restResult = enterpriseRelationService.batchGetRelationDownstreamAndOwner(HeaderObj.newInstance(ei), arg);
        if(restResult == null) {
            return Maps.newHashMap();
        }
        Map<String, RelationDownstreamResult> dataMap = restResult.getData();
        if(dataMap == null) {
            return Maps.newHashMap();
        }
        Map<String, Integer> result = Maps.newHashMap();
        for (Map.Entry<String, RelationDownstreamResult> entry : dataMap.entrySet()) {
            RelationDownstreamResult data = entry.getValue();
            if(data != null) {
                result.put(entry.getKey(), data.getDownstreamOuterTenantId());
            }
        }
        return result;
    }

    /**
     * 获取外部企业人员
     * @param tenantId
     * @param outerTenantId
     * @return
     */
    public List<String> getOuterTenantUserIds(String tenantId, Long outerTenantId) {
        Integer ei = Integer.parseInt(tenantId);
        ListEmployeesByOutTenantIdOutArg arg = new ListEmployeesByOutTenantIdOutArg();
        arg.setUpstreamTenantId(ei);
        arg.setOuterTenantId(outerTenantId);
        RestResult<List<EmployeeCardSimpleResult>> restResult = publicEmployeeService.listEmployeesByOutTenantId(HeaderObj.newInstance(ei), arg);
        if(restResult == null || CollectionUtils.empty(restResult.getData())) {
            return Lists.newArrayList();
        }
        return restResult.getData().stream().map(x -> String.valueOf(x.getOutUserId())).collect(Collectors.toList());
    }

    /**
     * 获取外部企业人员
     * @param tenantId
     * @param outerTenantIds
     * @return
     */
    public Map<String, List<String>> getOuterTenantsUserIds(String tenantId, List<Long> outerTenantIds) {
        Integer ei = Integer.parseInt(tenantId);
        int pageSize = 1000;
        int offset = 0;
        int pageNumber = 0;
        Map<String, List<String>> result = Maps.newHashMap();
        while (pageNumber <= 1000) {
            ListDownstreamEmployeesByDownstreamOuterTenantIdsArg arg = new ListDownstreamEmployeesByDownstreamOuterTenantIdsArg();
            arg.setUpstreamEa(gdsHandler.getEAByEI(tenantId));
            arg.setDownstreamOuterTenantIds(outerTenantIds);
            arg.setLimit(pageSize);
            arg.setOffset(offset);
            RestResult<List<ListDownstreamEmployeesByDownstreamOuterTenantIdsResult>> restResult = publicEmployeeService.listDownstreamEmployeesByDownstreamOuterTenantIds(HeaderObj.newInstance(ei), arg);
            if(restResult == null || CollectionUtils.empty(restResult.getData())) {
                break;
            }
            for(Long outerTenantId : outerTenantIds) {
                List<ListDownstreamEmployeesByDownstreamOuterTenantIdsResult> tempList = restResult.getData().stream()
                        .filter(x -> outerTenantId.equals(x.getOuterTenantId())).collect(Collectors.toList());

                if(CollectionUtils.notEmpty(tempList)) {
                    result.put(String.valueOf(outerTenantId), tempList.stream().map(x -> String.valueOf(x.getOuterUid())).collect(Collectors.toList()));
                } else {
                    result.put(String.valueOf(outerTenantId), Lists.newArrayList());
                }
            }
            pageNumber++;
            offset = pageNumber * pageSize;
        }
        return result;
    }

    /**
     * 批量获取外部企业人员
     * @param tenantId
     * @param outerTenantIds
     * @return
     */
    public List<String> batchGetOuterTenantsUserIds(String tenantId, List<Long> outerTenantIds) {
        List<String> result = Lists.newArrayList();
        Map<String, List<String>> outerTenantUserMap = getOuterTenantsUserIds(tenantId, outerTenantIds);
        for(Map.Entry<String, List<String>> entry : outerTenantUserMap.entrySet()) {
            result.addAll(entry.getValue());
        }
        return  result;
    }

    /**
     * 批量根据外部人员取外部企业
     * @param tenantId
     * @param outerUserIds
     * @return
     */
    public List<String> batchGetOuterTenantsByUserIds(String tenantId, List<Long> outerUserIds) {
        List<String> result = Lists.newArrayList();
        Integer ei = Integer.parseInt(tenantId);
        BatchGetEmployeeCardsArg batchGetEmployeeCardsArg = new BatchGetEmployeeCardsArg();
        batchGetEmployeeCardsArg.setOuterUids(outerUserIds);
        RestResult<Map<Long, EmployeeCardVO>> restResult = employeeCardService.batchGetEmployeeCards(HeaderObj.newInstance(ei), batchGetEmployeeCardsArg);
        if(restResult == null || CollectionUtils.empty(restResult.getData())) {
            return  result;
        }
        Set<String> outerTenantIds = restResult.getData().values().stream()
                .map(x -> String.valueOf(x.getOuterTenantId())).collect(Collectors.toSet());

        result.addAll(outerTenantIds);

        return result;
    }

    /**
     * 根据外部人员id查找员工信息
     *
     * @param tenantId
     * @param outerEmployeeIds
     * @return
     */
    public RestResult<Map<Long, EmployeeCardVO>> getOuterEmployeeInfoByIds(String tenantId, List<String> outerEmployeeIds) {
        Integer ei = Integer.parseInt(tenantId);
        BatchGetEmployeeCardsArg batchGetEmployeeCardsArg = new BatchGetEmployeeCardsArg();
        List<Long> outerUserIds = outerEmployeeIds.stream().map(Long::valueOf).collect(Collectors.toList());
        batchGetEmployeeCardsArg.setOuterUids(outerUserIds);
        return employeeCardService.batchGetEmployeeCards(HeaderObj.newInstance(ei), batchGetEmployeeCardsArg);
    }

    /**
     * 获取外部企业关联企业
     * @param tenantId
     * @param outerTenantIds
     * @return
     */
    public List<String> getOuterTenantsByOuterTenantIds(String tenantId, List<Long> outerTenantIds) {
        List<String> result = Lists.newArrayList();
        Integer ei = Integer.parseInt(tenantId);
        BatchGetShortNamesArg batchGetShortNamesArg = new BatchGetShortNamesArg();
        batchGetShortNamesArg.setOuterTenantIds(outerTenantIds);
        batchGetShortNamesArg.setUpstreamTenantId(ei);
        RestResult<Map<Long, String>> restResult = enterpriseRelationService.batchGetShortNames(HeaderObj.newInstance(ei), batchGetShortNamesArg);
        if(restResult == null || CollectionUtils.empty(restResult.getData())) {
            return  result;
        }
        result = restResult.getData().keySet().stream().map(String::valueOf).collect(Collectors.toList());
        return result;
    }

    /**
     * 获取外部企业伙伴
     * @param tenantId
     * @param outTenantIds
     * @return
     */
    public Map<String, String> getPartnersByOutTenantIds (String tenantId, List<String> outTenantIds) {
        Map<String, String> result = Maps.newHashMap();
        Integer ei = Integer.parseInt(tenantId);
        List<Long> outTenantLongIds = outTenantIds.stream().map(Long::valueOf).collect(Collectors.toList());
        BatchGetCrmMapperByOuterTenantIdsArg batchGetCrmMapperByOuterTenantIdsArg = new BatchGetCrmMapperByOuterTenantIdsArg();
        batchGetCrmMapperByOuterTenantIdsArg.setDownstreamOuterTenantIds(outTenantLongIds);
        batchGetCrmMapperByOuterTenantIdsArg.setUpstreamEa(gdsHandler.getEAByEI(tenantId));
        batchGetCrmMapperByOuterTenantIdsArg.setObjectApiName(PARTNER_API_NAME);
        RestResult<Map<Long, CrmMapperResult>> partnerResult = enterpriseRelationService.batchGetCrmMapperByOuterTenantIds(HeaderObj.newInstance(ei), batchGetCrmMapperByOuterTenantIdsArg);
        if(partnerResult == null || CollectionUtils.empty(partnerResult.getData())) {
            return result;
        }
        for (Long outTenantId : partnerResult.getData().keySet()) {
            result.put(String.valueOf(outTenantId), partnerResult.getData().get(outTenantId).getCrmObjectId());
        }
        return result;
    }

    /**
     * 获取合作伙伴外部企业
     * @param user
     * @param partnerIds
     * @return
     */
    public Map<String, RelationDownstreamResult> findRelationDownstreamVoByPartnerIds(User user, Set<String> partnerIds) {
        BatchGetRelationDownstreamAndOwnerArg arg = new BatchGetRelationDownstreamAndOwnerArg();
        arg.setCrmDataIds(Lists.newArrayList(partnerIds));
        arg.setUpstreamTenantId(Integer.parseInt(user.getTenantId()));
        arg.setObjectApiName(Utils.PARTNER_API_NAME);
        RestResult<Map<String, RelationDownstreamResult>> result = enterpriseRelationService.batchGetRelationDownstreamAndOwner(HeaderObj.newInstance(Integer.parseInt(user.getTenantId())), arg);
        log.info("PartnerService findRelationDownstreamVoByPartnerIds,do enterpriseRelationService.batchGetRelationDownstream," +
                "tenantId {},partnerIds {},result:{}", user.getTenantId(), JSON.toJSONString(partnerIds), JSON.toJSONString(result));
        if (result.getErrCode() != ResultCode.SUCCESS.getErrorCode()) {
            //如果请求失败时
            log.error("enterpriseRelationService batchGetRelationDownstream error,tenantId:{},errorCode:{},errorMsg:{},errorDescription:{}",
                    user.getTenantId(), result.getErrCode(), result.getErrMsg(), result.getErrMsg());
            return Maps.newHashMap();
        }
        return result.getData();
    }

    public String getCrmMapperByOuterTenantIds(String tenantId, String apiName, Long outTenantId) {
        if (!(PARTNER_API_NAME.equals(apiName) || "AccountObj".equals(apiName))) {
            return "";
        }
        BatchGetCrmMapperByOuterTenantIdsArg proxyArg = new BatchGetCrmMapperByOuterTenantIdsArg();
        proxyArg.setDownstreamOuterTenantIds(Lists.newArrayList(outTenantId));
        proxyArg.setObjectApiName(apiName);
        proxyArg.setUpstreamEa(eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId)));
        RestResult<Map<Long, CrmMapperResult>> result = enterpriseRelationService.batchGetCrmMapperByOuterTenantIds(HeaderObj.newInstance(Integer.parseInt(tenantId)), proxyArg);
        if (result.getData().get(outTenantId) == null) {
            return "";
        }
        return result.getData().get(outTenantId).getCrmObjectId();
    }
}