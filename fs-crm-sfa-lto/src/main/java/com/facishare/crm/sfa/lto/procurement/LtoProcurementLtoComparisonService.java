package com.facishare.crm.sfa.lto.procurement;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.lto.common.LtoLicenseService;
import com.facishare.crm.sfa.lto.enums.ProcurementLogApiNameEnum;
import com.facishare.crm.sfa.lto.enums.ProcurementLogLogTypeEnum;
import com.facishare.crm.sfa.lto.procurement.models.LtoProcurementConstants.CommonConstants;
import com.facishare.crm.sfa.lto.procurement.models.LtoProcurementConstants.ComparisonConf;
import com.facishare.crm.sfa.lto.procurement.models.LtoProcurementConstants.ProcurementEnterprise;
import com.facishare.crm.sfa.lto.procurement.models.LtoProcurementConstants.ProcurementInfo;
import com.facishare.crm.sfa.lto.procurement.models.LtoProcurementModel.*;
import com.facishare.crm.sfa.lto.utils.ActionContextUtil;
import com.facishare.crm.sfa.lto.utils.CommonUtil;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.enterprise.common.util.CollectionUtil;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.TeamMember;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName com.facishare.crm.sfa.lto.procurement.ProcurementComparisonService
 * @Description :
 * step 0、收消息
 * step 1、取规则：比对规则（caller_info、winner_info、join_info） 表：biz_procurement_comparison （条件：tenantId）
 * step 2、取公告：表：biz_procurement_info （条件：bid_id + tenantId）
 * step 3、取统一信用代码\公司名称：表：biz_procurement-enterprise （条件：id + tenantId）
 * step 4、比对（企业库、客户、线索、合作伙伴、竞争对手） 根据招标，中标企业的统一信用代码，其中客户以名称匹配
 * step 5、打标：更新4对应API_NAME到caller_status、winner_status字段，表：biz_procurement_info
 * step 6、关联：更新4对应id到对应列，表：biz_procurement_transfer_log  （注：info_id为info表主键id）
 * <AUTHOR>
 * @Date 2022/10/18 18:06
 * @Version 1.0
 **/
@Slf4j
@Component
public class LtoProcurementLtoComparisonService implements LtoComparisonService {
    @Autowired
    protected LtoProcurementDataService ltoProcurementDataService;

    @Autowired
    protected LtoProcurementInfoService ltoProcurementInfoService;

    @Autowired
    protected LtoProcurementTransferLogService ltoProcurementTransferLogService;

    @Autowired
    protected LtoProcurementEnterpriseService ltoProcurementEnterpriseService;

    @Autowired
    protected LtoProcurementLogService ltoProcurementLogService;

    @Autowired
    protected ServiceFacade serviceFacade;

    @Autowired
    private ConfigService configService;

    @Autowired
    protected LtoLicenseService ltoLicenseService;

    private static final String ENTERPRISE_LICENSE_PACKAGE_NAME = "enterprise_library_app";

    private boolean openEnterprise(String tenantId) {
        return ltoLicenseService.checkModuleLicenseExist(tenantId, ENTERPRISE_LICENSE_PACKAGE_NAME);
    }

    private boolean openPartner(String tenantId) {
        User user = CommonUtil.buildUser(tenantId);
        String config1 = configService.findTenantConfig(user, "37");
        if ("1".equals(config1)) {
            return true;
        }
        String config2 = configService.findTenantConfig(user, "config_partner_open");
        return "open".equals(config2);
    }

    @Override
    public void execute(ProcurementMessage message) {
        if (message == null || CollectionUtil.isEmpty(message.getObjectIds())) return;

        String tenantId = message.getTenantId();
        //取后台配置的比对规则
        Map rule = ltoProcurementDataService.getComparisonRule(tenantId);
        log.info("MsgId:{}, ComparisonRules:{}, tenantId:{}", message.getId(), rule, tenantId);
        if (CollectionUtil.isEmpty(rule)) return;

        if (message.getOpType().equals(ComparisonConf.CREATE_ACTION)) {
            createAction(tenantId, message, rule);

        } else if (message.getOpType().equals(ComparisonConf.TRANSFER_ACTION)) {
            if (message.getApiName().equals(ProcurementInfo.NAME_OBJ)) {
                transferProcurementInfo(tenantId, message, rule);

            } else if (message.getApiName().equals(ProcurementEnterprise.NAME_OBJ)) {
                transferProcurementEnterprise(tenantId, message, rule);

            }
        }
    }

    private void transferProcurementEnterprise(String tenantId, ProcurementMessage message, Map rule) {
        transferAction(tenantId, message, rule);
    }

    private void transferProcurementInfo(String tenantId, ProcurementMessage message, Map rule) {
        createAction(tenantId, message, rule);
    }

    private void createAction(String tenantId, ProcurementMessage message, Map rule) {
        String msgId = message.getId();
        //查询公告表，提取公告数据
        List<IObjectData> iObjectDataList = ltoProcurementInfoService.search(message.getObjectIds(), tenantId);
        log.info("MsgId:{}, objIds:{}, tenantId:{}", msgId, message.getObjectIds(), tenantId);
        if (CollectionUtil.isEmpty(iObjectDataList)) return;

        for (IObjectData iod : iObjectDataList) {
            //提取统一信用代码及名称
            ltoProcurementEnterpriseService.loadCodeAndNameMsg(iod, message, tenantId);
            log.info("MsgId:{}, LoadCodeAndNameMsg message:{}", msgId, message);

            if (StringUtil.isEmpty(message.getSrcId())) continue;

            ComparisonResult cr = comparison(rule, message);
            log.info("MsgId:{}, ComparisonResult:{}", msgId, cr);

            //打标，更新biz_procurement_info状态caller_status、winner_status
            ltoProcurementInfoService.markStatus(message.getSrcId(), tenantId, cr.getC_status(), cr.getW_status());

            //if (message.getOpType().equals(ComparisonConf.CREATE_ACTION)) {  //只对新增数据写transferlog
            //    //关联，更新biz_procurement_transfer_log对应关联字段, insert
            //    ltoProcurementTransferLogService.attach(message.getSrcId(), tenantId, cr.getIdsMap());
            //}
        }
    }

    private void transferAction(String tenantId, ProcurementMessage message, Map rule) {
        String msgId = message.getId();
        List<String> objIds = message.getObjectIds();

        //先提取统一信用代码+全称，id对应
        List<IObjectData> iObjectDataList = ltoProcurementDataService.findByIdList(objIds, tenantId, ProcurementEnterprise.NAME_OBJ);
        log.info("MsgId:{}, transferAction {}", msgId, iObjectDataList);

        if (CollectionUtil.isEmpty(iObjectDataList)) return;

        for (IObjectData iObjectData : iObjectDataList) {
            String enterpriseId = (String) iObjectData.get(CommonConstants._ID);
            String creditCode = StringUtil.toSafeString(iObjectData.get(ComparisonConf.TABLE_FIELD_CREDIT_ID));
            String name = StringUtil.toSafeString(iObjectData.get(ComparisonConf.TABLE_FIELD_NAME));
            log.info("MsgId:{}, enterpriseId:{}, creditCode:{}, name:{}", msgId, enterpriseId, creditCode, name);
            if (StringUtil.isEmpty(creditCode) && StringUtil.isEmpty(name)) continue;

            //再取公告id，注意判断是caller还winner
            List<Map> infoDatas = ltoProcurementInfoService.searchByEnterpriseId(enterpriseId, tenantId);
            log.info("MsgId:{}, searchByEnterpriseId {}, {}", msgId, enterpriseId, infoDatas);

            if (CollectionUtil.isEmpty(infoDatas)) return;

            for (Map info : infoDatas) {
                loadMessageCreditCodeAndName(info, message, enterpriseId, creditCode, name);
                if (StringUtil.isEmpty(message.getSrcId())) continue;

                ComparisonResult cr = comparison(rule, message);
                log.info("MsgId:{}, ComparisonResult:{}", msgId, cr);

                //打标，更新biz_procurement_info状态caller_status、winner_status
                ltoProcurementInfoService.markStatus(message.getSrcId(), tenantId, cr.getC_status(), cr.getW_status());
            }
        }
    }

    private void loadMessageCreditCodeAndName(Map info, ProcurementMessage message, String enterpriseId, String creditCode, String name) {
        String msgId = message.getId();
        //拼message相关属性
        message.setSrcId(StringUtil.toSafeString(info.get(CommonConstants.ID)));

        if (info.containsKey(ProcurementInfo.CALLER_ENTERPRISE) && info.get(ProcurementInfo.CALLER_ENTERPRISE) != null) {
            try {
                List<String> callerIds = Lists.newArrayList((String[]) info.get(ProcurementInfo.CALLER_ENTERPRISE));
                log.info("MsgId:{}, transferAction callerIds:{}", msgId, callerIds);
                if (!CollectionUtil.isEmpty(callerIds) && callerIds.contains(enterpriseId)) {
                    message.setCallerCreditCode(Lists.newArrayList(creditCode));
                    //招标单位与线索比对，通过name
                    message.setCallerName(Lists.newArrayList(name));
                }
            } catch (Exception ex) {
                log.error("MsgId:{}, transfer '{}', {} , err.", ProcurementInfo.CALLER_ENTERPRISE, info, msgId, ex);
            }
        }

        if (info.containsKey(ProcurementInfo.WINNER_ENTERPRISE) && info.get(ProcurementInfo.WINNER_ENTERPRISE) != null) {
            try {
                List<String> winnerIds = Lists.newArrayList((String[]) info.get(ProcurementInfo.WINNER_ENTERPRISE));
                log.info("MsgId:{},transferAction winnerIds:{}", msgId, winnerIds);
                if (!CollectionUtil.isEmpty(winnerIds) && winnerIds.contains(enterpriseId)) {
                    message.setWinnerCreditCode(Lists.newArrayList(creditCode));
                    message.setWinnerName(Lists.newArrayList(name));
                }
            } catch (Exception ex) {
                log.error("MsgId:{}, transfer '{}', {} , err.", ProcurementInfo.WINNER_ENTERPRISE, info, msgId, ex);
            }
        }
        log.info("MsgId:{},ProcurementMessage:{}", msgId, message);
    }

    /**
     * 比对逻辑 : 比对规则，三个json（caller_info、winner_info、join_info）
     *
     * @param rule
     * @param msg
     * @return
     */

    private ComparisonResult comparison(Map rule, ProcurementMessage msg) {
        //招标中标的比对成功标识（API_NAME），用于回写对应对象表，即打标
        List<String> cstatus = Lists.newArrayList();
        List<String> wstatus = Lists.newArrayList();
        //招标中标的比对成功标识（K：API_NAME， V：ID），用于回写转换对象表，即关联。比对逻辑
        Map<String, List<String>> idsMap = Maps.newConcurrentMap();

        String tenantId = msg.getTenantId();
        if (rule.containsKey(ComparisonConf.RULE_CALLER_INFO) && (!CollectionUtil.isEmpty(msg.getCallerCreditCode()) || !CollectionUtil.isEmpty(msg.getCallerName()))) {
            PCBean pcBean = JSON.parseObject((String) rule.get(ComparisonConf.RULE_CALLER_INFO), PCBean.class);
            comparisonItem(ComparisonItem.builder().pcBean(pcBean).tenantId(tenantId).creditCodes(msg.getCallerCreditCode()).names(msg.getCallerName()).build(), cstatus, idsMap);
            if (rule.get(ComparisonConf.DEFAULT_RULE) != null && 1 == Integer.parseInt(rule.get(ComparisonConf.DEFAULT_RULE).toString())) {
                preCreateTriggerAction(msg, ProcurementLogLogTypeEnum.CALLER, idsMap);
            }
        }
        // 重置idsMap
        idsMap = Maps.newConcurrentMap();
        if (rule.containsKey(ComparisonConf.RULE_WINNER_INFO) && (!CollectionUtil.isEmpty(msg.getWinnerCreditCode()) || !CollectionUtil.isEmpty(msg.getWinnerName()))) {
            PCBean pcBean = JSON.parseObject((String) rule.get(ComparisonConf.RULE_WINNER_INFO), PCBean.class);
            comparisonItem(ComparisonItem.builder().pcBean(pcBean).tenantId(tenantId).creditCodes(msg.getWinnerCreditCode()).names(msg.getWinnerName()).build(), wstatus, idsMap);
            if (rule.get(ComparisonConf.DEFAULT_RULE) != null && 1 == Integer.parseInt(rule.get(ComparisonConf.DEFAULT_RULE).toString())) {
                preCreateTriggerAction(msg, ProcurementLogLogTypeEnum.WINNER, idsMap);
            }
        }

        if (rule.containsKey(ComparisonConf.RULE_JOIN_INFO) && (!CollectionUtil.isEmpty(msg.getWinnerCreditCode()) || !CollectionUtil.isEmpty(msg.getWinnerName()))) {
            List<JoinBean> joinBeans = JSON.parseArray((String) rule.get(ComparisonConf.RULE_JOIN_INFO), JoinBean.class);
            //复用winner_status字段
            comparisonJoin(joinBeans, msg.getWinnerCreditCode(), msg.getWinnerName(), wstatus);
        }
        return ComparisonResult.builder().c_status(cstatus).w_status(wstatus).idsMap(idsMap).build();
    }

    /**
     * @param item   比对原对象
     * @param enums  比对成功的状态合集
     * @param idsMap 比对成功的id合集
     */
    private void comparisonItem(ComparisonItem item, List<String> enums, Map<String, List<String>> idsMap) {
        if (item == null) return;

        PCBean pcBean = item.getPcBean();
        String tenantId = item.getTenantId();
        List<String> creditCodes = item.getCreditCodes();
        List<String> names = item.getNames();
        log.info("comparisonCallWin PCBean:{}, tenantId:{}, creditCode:{}, name:{}", pcBean, tenantId, creditCodes, names);

        if (pcBean != null && !CollectionUtil.isEmpty(pcBean.getInfos())) {
            for (InfoBean ib : pcBean.getInfos()) {
                List<String> dataList = comparisonCallWin(ib, tenantId, creditCodes, names);
                log.info("comparisonCallWin dataResult:{}", dataList);
                if (!CollectionUtil.isEmpty(dataList)) {
                    enums.add(ib.getApi_name());
                    idsMap.put(ib.getApi_name(), dataList);
                }
            }
        }
        log.info("comparisonCallWin enums:{}, ids:{}", enums, idsMap);
    }

    private List<String> comparisonCallWin(InfoBean ib, String tenantId, List<String> creditCodes, List<String> names) {
        List<String> result = null;
        if (ib.isSelect() && (!CollectionUtil.isEmpty(creditCodes) || !CollectionUtil.isEmpty(names))) {
            List<IObjectData> iObjectData = null;
            if (OPJApiEnum.Enterprise.apiName.equals(ib.getApi_name())) {
                if (openEnterprise(tenantId)) {
                    iObjectData = ltoProcurementDataService.getEnterpriseInfo(tenantId, creditCodes, names);
                }
            } else if (OPJApiEnum.Account.apiName.equals(ib.getApi_name())) {
                iObjectData = ltoProcurementDataService.getAccountInfo(tenantId, creditCodes, names);

            } else if (OPJApiEnum.Leads.apiName.equals(ib.getApi_name())) {
                iObjectData = ltoProcurementDataService.getLeadsInfo(tenantId, names);

            } else if (OPJApiEnum.Competitor.apiName.equals(ib.getApi_name())) {
                iObjectData = ltoProcurementDataService.getCompetitorInfo(tenantId, creditCodes, names);

            } else if (OPJApiEnum.Partner.apiName.equals(ib.getApi_name())) {
                if (openPartner(tenantId)) {
                    iObjectData = ltoProcurementDataService.getPartnerInfo(tenantId, creditCodes, names);
                }
            }

            if (!CollectionUtil.isEmpty(iObjectData)) {
                result = Lists.transform(iObjectData, iData -> {
                    return (String) iData.get(CommonConstants._ID);
                });
            }
        }
        return result;
    }

    /**
     * 比对似定参标方：中标单位和我方拟定参标方比对
     * 逻辑：
     * “我方拟定参标方”数据只要比对成功1个，即显示“中标单位已是我方拟定参标方”结果。
     *
     * @param joinBeans    : 拟定参标方
     * @param creditCode   ： 中标单位
     * @param winnerStatus ： 中标标识
     */
    private void comparisonJoin(List<JoinBean> joinBeans, List<String> creditCode, List<String> names, List<String> winnerStatus) {
        if (CollectionUtil.isEmpty(creditCode) && CollectionUtil.isEmpty(names)) return;

        if (!CollectionUtil.isEmpty(joinBeans)) {
            List<String> idList = Lists.transform(joinBeans, JoinBean::getId);
            if (searchList(idList, creditCode)) {
                winnerStatus.add(OPJApiEnum.WinnerJoin.apiName);
            }

            List<String> nameList = Lists.transform(joinBeans, JoinBean::getName);
            if (searchList(nameList, names)) {
                winnerStatus.add(OPJApiEnum.WinnerJoin.apiName);
            }
        }
    }

    /**
     * 查找比较List是否匹配
     *
     * @param srcList
     * @param destList
     * @return
     */
    private boolean searchList(List<String> srcList, List<String> destList) {
        if (!CollectionUtil.isEmpty(srcList) && !CollectionUtil.isEmpty(destList)) {
            for (String src : srcList) {
                if (destList.contains(src)) {
                    return true;
                }
            }
        }
        return false;
    }

    private void preCreateTriggerAction(ProcurementMessage msg, ProcurementLogLogTypeEnum logTypeEnum, Map<String, List<String>> idsMap) {
        log.info("preCreateTriggerAction msg:{},logTypeEnum:{},idsMap:{}", msg, logTypeEnum, idsMap);
        for (Map.Entry<String, List<String>> entry : idsMap.entrySet()) {
            createTriggerAction(msg, logTypeEnum, entry.getKey(), entry.getValue());
        }
    }

    private void createTriggerAction(ProcurementMessage msg, ProcurementLogLogTypeEnum logTypeEnum, String apiName, List<String> ids) {
        List<IObjectData> objectData = ltoProcurementDataService.getDBLog(msg.getTenantId(), msg.getSrcId(), ids, apiName);
        if (CollectionUtils.isNotEmpty(objectData)) {
            List<String> idList = objectData.stream().map(DBRecord::getId).collect(Collectors.toList());
            ids.removeIf(idList::contains);
        }
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        for (String id : ids) {
            ObjectDataDocument dataDocument = new ObjectDataDocument();
            dataDocument.put("log_type", logTypeEnum.getValue());
            dataDocument.put(ProcurementLogApiNameEnum.getFieldApiName(apiName), id);
            dataDocument.put("procurement_info_id", msg.getSrcId());
            dataDocument.put("owner", Lists.newArrayList(msg.getUserId()));
            if (Boolean.TRUE.equals(msg.getOutUser())) {
                dataDocument.put("out_owner", Lists.newArrayList(msg.getOutUserId()));
                dataDocument.put("out_tenant_id", msg.getOutTenantId());
            }
            dataDocument.put("relevant_team", transitionRelevantTeam(msg));
            if (!findExistData(msg.getTenantId(), id, apiName, logTypeEnum.getValue(), msg.getSrcId())) {
                ltoProcurementLogService.triggerAction(User.systemUser(msg.getTenantId()), "ProcurementLogObj", dataDocument);
                continue;
            }
            log.info(" duplicate procurement log data:{} ",msg.getSrcId());
        }
    }

    private boolean findExistData(String tenantId, String dataId, String apiName, String logType, String infoId) {
        User user = CommonUtil.buildUser(tenantId);
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        ActionContextUtil.SearchActionContextOp op = ActionContextUtil.SearchActionContextOp.builder()
                .skipRelevantTeam(true).calculateFormula(false).needDeepQuote(false)
                .esRedisRecentUpdateCheck(true).privilegeCheck(false).forceESSearch(false)
                .build();
        IActionContext actionContext = ActionContextUtil.createSearchActionContext(user, op);
        actionContext.setDbType("pg");
        searchTemplateQuery.setSearchSource("db");
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, Tenantable.TENANT_ID, tenantId);
        SearchUtil.fillFilterEq(filters, ProcurementLogApiNameEnum.getFieldApiName(apiName), dataId);
        SearchUtil.fillFilterEq(filters, "procurement_info_id", infoId);
        SearchUtil.fillFilterEq(filters, "log_type", logType);
        searchTemplateQuery.setFilters(filters);
        searchTemplateQuery.setLimit(1);
        QueryResult<IObjectData> searchQuery = serviceFacade.findBySearchQuery(actionContext, "ProcurementLogObj", searchTemplateQuery);
        return CollectionUtils.isNotEmpty(searchQuery.getData());
    }

    public static List<Map<String, Object>> transitionRelevantTeam(ProcurementMessage msg) {
        List<Map<String, Object>> relevantTeam = Lists.newArrayList();
        String userId = msg.getUserId();
        if (ObjectUtils.isEmpty(userId)) {
            userId = "-10000";
        }
        TeamMember teamMember = new TeamMember(userId, TeamMember.Role.OWNER, TeamMember.Permission.READANDWRITE);
        relevantTeam.add(teamMember.toMap());
        if (Boolean.TRUE.equals(msg.getOutUser())) {
            TeamMember outTeamMember = new TeamMember(msg.getOutUserId(), TeamMember.Role.OWNER, TeamMember.Permission.READANDWRITE, msg.getOutTenantId());
            relevantTeam.add(outTeamMember.toMap());
        }
        return relevantTeam;
    }

}