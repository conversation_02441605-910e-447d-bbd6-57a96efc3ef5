package com.facishare.crm.sfa.lto.operations.task;

import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;

import java.util.Objects;

class Value {
    IFieldDescribe metadata;
    IObjectData data;

    Value(IFieldDescribe metadata, IObjectData data) {
        this.metadata = Objects.requireNonNull(metadata, "metadata is null");
        this.data = Objects.requireNonNull(data, "data is null");
    }

    Object raw() {
        return data.get(metadata.getApiName());
    }
}
