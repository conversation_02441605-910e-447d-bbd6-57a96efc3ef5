package com.facishare.crm.sfa.lto.integral.core.util;

import com.facishare.crm.sfa.lto.integral.core.rest.dto.BaseEngine;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.util.SFAHeaderUtil;
import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;

import java.util.Map;

public class IntegralHeaderUtils {
    public static Map<String, String> getHeaders(BaseEngine.Context context) {
        String tenantId = context.getTenantId();
        if (StringUtils.isBlank(tenantId)) {
            return Maps.newHashMap();
        }
        String userId = context.getUserId();
        if (StringUtils.isBlank(userId)) {
            userId = User.SUPPER_ADMIN_USER_ID;
        }
        return SFAHeaderUtil.getHeaders(new User(tenantId, userId));
    }
}
