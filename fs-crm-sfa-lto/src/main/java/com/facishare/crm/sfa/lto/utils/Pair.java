package com.facishare.crm.sfa.lto.utils;

import java.io.Serializable;
import java.util.Objects;

/**
 * A simple object that holds onto a pair of object references, first and
 * second.
 *
 * @param <FIRST>
 * @param <SECOND>
 */
final public class Pair<FIRST, SECOND> implements Serializable {
	public final FIRST first;
	public final SECOND second;

	public Pair(FIRST first, SECOND second) {
		this.first = first;
		this.second = second;
	}

	public FIRST getFirst() {
		return first;
	}

	public SECOND getSecond() {
		return second;
	}

	@Override
	public int hashCode() {
		return 17 * ((first != null) ? first.hashCode() : 0) + 17 * ((second != null) ? second.hashCode() : 0);
	}

	@Override
	public boolean equals(Object o) {
		if (o == this) {
			return true;
		}
		if (!(o instanceof Pair<?, ?>)) {
			return false;
		}
		Pair<?, ?> that = (Pair<?, ?>) o;
		return same(this.first, that.first) && same(this.second, that.second);
	}

	private static boolean same(Object a, Object b) {
		return Objects.equals(a, b);
	}

	@Override
	public String toString() {
		return String.format("(%s,%s)", first, second);
	}

	/**
	 * 通过值创建值对
	 *
	 * @param f 第一个值
	 * @param s 第二个值
	 * @return 值对
	 */
	public static <FIRST, SECOND> Pair<FIRST, SECOND> build(FIRST f, SECOND s) {
		return new Pair<FIRST, SECOND>(f, s);
	}

	public static <FIRST, SECOND> Pair<FIRST, SECOND> makePair(FIRST f, SECOND s) {
		return build(f, s);
	}
}
