package com.facishare.crm.sfa.lto.operations;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.license.arg.JudgeModuleArg;
import com.facishare.paas.license.common.LicenseContext;
import com.facishare.paas.license.common.Result;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.paas.license.pojo.JudgeModulePojo;
import com.facishare.paas.license.pojo.ModuleFlag;

import java.util.Collections;
import java.util.Optional;

public class OperationsUtil {
    private OperationsUtil() {}

    public static boolean hasLicense(String tenantId, LicenseClient licenseClient) {
        JudgeModuleArg arg = new JudgeModuleArg();
        LicenseContext licenseContext = new LicenseContext();
        licenseContext.setAppId("CRM");
        licenseContext.setTenantId(tenantId);
        licenseContext.setUserId(User.SUPPER_ADMIN_USER_ID);
        arg.setContext(licenseContext);
        arg.setModuleCodes(Collections.singletonList(OperationsConstant.MODULE_CODE));
        @SuppressWarnings("unchecked")
        Result<JudgeModulePojo> result = licenseClient.judgeModule(arg);
        return Optional.of(result).map(Result::getResult).map(JudgeModulePojo::getModuleFlags).map(flags -> flags.get(0)).map(ModuleFlag::isFlag).orElse(false);
    }
}
