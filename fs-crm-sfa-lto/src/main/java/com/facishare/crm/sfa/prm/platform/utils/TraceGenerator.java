package com.facishare.crm.sfa.prm.platform.utils;

import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;

import java.util.UUID;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-03-05
 * ============================================================
 */
@Slf4j
public class TraceGenerator {
    public static void generator() {
        try {
            String appName = SystemPropertiesUtils.getAppName();
            String traceId = appName + "-" + UUID.randomUUID().toString().replace("-", "") + "-" + System.currentTimeMillis();
            TraceContext.get().setTraceId(traceId);
        } catch (Exception e) {
            log.error("TraceGenerator#generator error", e);
        }
    }
    public static String getTrace() {
        return TraceContext.get().getTraceId();
    }
}
