package com.facishare.crm.sfa.prm.platform.infrastructure.statemachine.rule;

import com.facishare.crm.sfa.prm.platform.infrastructure.statemachine.transition.StateTransition;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-04-09
 * ============================================================
 */
public abstract class AbstractTransitionRuleRule<S, E, C> implements TransitionRule<S, E, C> {
    Map<S, Map<E, StateTransition<S, E, C>>> TRANSITION_RULES = new ConcurrentHashMap<>();

    @Override
    public Map<S, Map<E, StateTransition<S, E, C>>> getTransitionRules() {
        return TRANSITION_RULES;
    }

    protected abstract StateRulesBuilder<S, E, C> initTransitionRules();

    @PostConstruct
    public void postConstructInit() {
        StateRulesBuilder<S, E, C> stateRulesBuilder = initTransitionRules();
        if (stateRulesBuilder != null) {
            TRANSITION_RULES = stateRulesBuilder.build();
        }
    }
}
