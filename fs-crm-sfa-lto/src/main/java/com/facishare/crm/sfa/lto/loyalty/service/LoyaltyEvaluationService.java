package com.facishare.crm.sfa.lto.loyalty.service;

import com.facishare.crm.sfa.lto.loyalty.utils.LoyaltyThreshold;
import com.github.jedis.support.JedisCmd;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
public class LoyaltyEvaluationService {

    @Autowired
    private JedisCmd SFAJedisCmd;

    private String keyLastEvaluationDate(String tenantId, String memberId) {
        return "sfa_last_evaluation_date_" + tenantId + "_" + memberId;
    }

    public long getLastEvaluationDate(String tenantId, String memberId) {
        String key = keyLastEvaluationDate(tenantId, memberId);
        String value = SFAJedisCmd.get(key);
        if (StringUtils.isEmpty(value)) {
            return 0;
        }
        return Long.parseLong(value);
    }

    public void setLastEvaluationDate(String tenantId, String memberId, long lastEvaluationDate) {
        String key = keyLastEvaluationDate(tenantId, memberId);
        SFAJedisCmd.set(key, String.valueOf(lastEvaluationDate));
        SFAJedisCmd.expire(key, (long) LoyaltyThreshold.getLastEvaluationDateCacheTime());
    }

    public void deleteLastEvaluationDate(String tenantId, String memberId) {
        String key = keyLastEvaluationDate(tenantId, memberId);
        SFAJedisCmd.del(key);
    }
}
