package com.facishare.crm.sfa.lto.equityrelationship.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.common.HttpUtils;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.lto.common.LtoRateLimiterService;
import com.facishare.crm.sfa.lto.common.SFALtoRedisService;
import com.facishare.crm.sfa.lto.equityrelationship.dao.EquityRelationshipDataMongoDbDao;
import com.facishare.crm.sfa.lto.equityrelationship.model.EquityRelationshipData;
import com.facishare.crm.sfa.lto.equityrelationship.model.EquityRelationshipDataModel;
import com.facishare.crm.sfa.lto.rest.SFAIndustryInterfaceProxy;
import com.facishare.crm.sfa.lto.utils.*;
import com.facishare.crm.sfa.lto.utils.constants.EquityRelationshipDataConstants;
import com.facishare.crm.data.ext.dao.businessrisk.entity.SFABICompanyBaseEntity;
import com.facishare.crm.data.ext.dao.businessrisk.mapper.SFABICompanyBaseMapper;
import com.facishare.crm.data.ext.dao.businessrisk.mapper.SFABICompanyBranchMapper;
import com.facishare.crm.data.ext.dao.businessrisk.mapper.SFABIOptEntPartnerCnRevertMapper;
import com.facishare.crm.data.ext.dto.SFABICompanyBaseDTO;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.CRMNotificationService;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.CommonSqlOperator;
import com.facishare.paas.metadata.impl.search.WhereParam;
import com.facishare.paas.metadata.support.CountryAreaService;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.SqlEscaper;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.dispatcher.common.MessageHelper;
import com.github.autoconf.ConfigFactory;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import groovy.lang.Lazy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.lto.equityrelationship.dao.EquityRelationshipDataMongoDbDao.MOULD_TENANT_ID;

/**
 * <AUTHOR> lik
 * @date : 2023/7/6 17:36
 */
@Component
@Slf4j
public class EquityRelationshipService {

    @Autowired
    private LtoRateLimiterService rateLimiterService;
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private EquityRelationshipDataMongoDbDao equityRelationshipDataMongoDbDao;
    @Autowired
    SFAIndustryInterfaceProxy sfaIndustryInterfaceProxy;
    @Resource(name = "httpClientSupport")
    private OkHttpSupport client;

    @Autowired
    private CountryAreaService countryAreaService;
    @Autowired
    private  SFABICompanyBaseMapper sfabiCompanyBaseMapper;
    @Autowired
    private  SFABICompanyBranchMapper sfabiCompanyBranchMapper;
    @Autowired
    private  SFABIOptEntPartnerCnRevertMapper sfabiOptEntPartnerCnRevertMapper;
    @Autowired
    private SFALtoRedisService sfaLtoRedisService;

    private static final CRMNotificationService crmNotificationService = SpringUtil.getContext().getBean(CRMNotificationService.class);
    @Autowired
    @Lazy
    private  EquityRelationshipDataSyncService equityRelationshipDataSyncService;
    /**查询股权信息的方法*/
    private static  String QUERY_EQUITY_INFORMATION_METHOD_NAME ="";
    /**分支机构查询*/
    private static  String QUERY_BRANCH_METHOD_NAME ="";
    /**工商信息查询*/
    private static  String QUERY_COMPANY_DETAIL_METHOD_NAME ="";
    /**测试域名*/
    private static  String TEST_DOMAIN_NAME ="";
    /**生产域名*/
    private static  String PROD_DOMAIN_NAME ="";
    /**使用的环境*/
    private static  String USE_ENVIRONMENT ="";
    private static  String X_GATEWAY_APIKEY ="";
    private static  boolean STOP_QUERY_BRANCH_FLAG = false;

    private static boolean IS_OPEN_LIMIT_SPEED =false;
    private static boolean DOWNLOAD_TO_SAVA_PG =false;

    private static boolean IS_NEED_PROXY =true;
    public static boolean QUERY_DATA_BY_BI =false;

    private static Integer LIMIT_SPEED = 200;

    private static  List<Map> fieldMapping = new ArrayList<>();
    private static final List<String> arrTypeFieldApiName= Lists.newArrayList("websites");
    public static  boolean isOpenAllSyncDataFlag = false;
    static {
        ConfigFactory.getConfig("crm-sfa-equity-relationship-config", config -> {
            QUERY_EQUITY_INFORMATION_METHOD_NAME = config.get("QUERY_EQUITY_INFORMATION_METHOD_NAME", "");
            QUERY_BRANCH_METHOD_NAME = config.get("QUERY_BRANCH_METHOD_NAME", "");
            QUERY_COMPANY_DETAIL_METHOD_NAME = config.get("QUERY_COMPANY_DETAIL_METHOD_NAME", "");
            TEST_DOMAIN_NAME = config.get("TEST_DOMAIN_NAME", "");
            PROD_DOMAIN_NAME = config.get("PROD_DOMAIN_NAME", "");
            USE_ENVIRONMENT = config.get("USE_ENVIRONMENT", "");
            X_GATEWAY_APIKEY = config.get("X_GATEWAY_APIKEY", "");
            STOP_QUERY_BRANCH_FLAG = config.getBool("STOP_QUERY_BRANCH_FLAG", false);
            IS_OPEN_LIMIT_SPEED = config.getBool("IS_OPEN_LIMIT_SPEED", false);
            LIMIT_SPEED = config.getInt("LIMIT_SPEED", 200);
            DOWNLOAD_TO_SAVA_PG = config.getBool("DOWNLOAD_TO_SAVA_PG", false);
            IS_NEED_PROXY = config.getBool("IS_NEED_PROXY", true);
            QUERY_DATA_BY_BI = config.getBool("QUERY_DATA_BY_BI", false);
            fieldMapping = JSONObject.parseArray(config.get("fieldMapping", ""),Map.class);
            isOpenAllSyncDataFlag = config.getBool("isOpenAllSyncDataFlag");
        });
    }

    /**
     * 获取股权获取
     * @param user
     * @param name
     * @return
     */
    public EquityRelationshipDataModel.RelationshipModel queryEquityInfoByName(User user, String name){
        if(QUERY_DATA_BY_BI){
            log.debug("queryEquityInfoByName QUERY_DATA_BY_BI:{}",QUERY_DATA_BY_BI);
            return getIndustryInterfaceByBI(user,name);
        }
        Map<String,String> paramMap = new HashMap<>();
        paramMap.put(EquityRelationshipDataConstants.Param.NAME,name);
        String url = HttpUtilse.splitJointURLOfGet(getDoMainByUseEnvironment(),QUERY_EQUITY_INFORMATION_METHOD_NAME,paramMap);
        EquityRelationshipDataModel.QueryResult queryResult = handleIsNeedByProxyToRequest(url);
        if(ObjectUtils.isEmpty(queryResult)){
            throw new ValidateException(I18N.text(SfaLtoI18NKeyUtil.SFA_EQUITY_INFORMATION_CALL_INTERFACE_TIMEOUT));
        }
        if(!queryResult.getCode().equals(200)){
            if(queryResult.getCode().equals(201)){
                throw new ValidateException(I18N.text(SfaLtoI18NKeyUtil.SFA_EQUITY_INFORMATION_DOES_NOT_EXIST));
            }
            log.warn("EquityRelationshipDataService queryEquityInfoByName error queryResult:{}",JSONObject.toJSONString(queryResult));
            throw new ValidateException("招商企智--"+queryResult.getStatus());
        }
        return JSONObject.parseObject(JSONObject.toJSONString(queryResult.getData()),EquityRelationshipDataModel.RelationshipModel.class);
    }


    /**
     * 获取企业的工商信息
     * @param user
     * @param name
     * @return
     */
    public EquityRelationshipDataModel.CompanyDetail queryCompanyDetailByName(User user,String name){
        if(QUERY_DATA_BY_BI){
            log.debug("queryCompanyDetailByName QUERY_DATA_BY_BI:{}",QUERY_DATA_BY_BI);
            return getCompanyDetailByBI(user,name);
        }
        Map<String,String> paramMap = new HashMap<>();
        paramMap.put(EquityRelationshipDataConstants.Param.KEYWORD,name);
        String url = HttpUtilse.splitJointURLOfGet(getDoMainByUseEnvironment(),QUERY_COMPANY_DETAIL_METHOD_NAME,paramMap);
        EquityRelationshipDataModel.QueryResult queryResult = handleIsNeedByProxyToRequest(url);
        if(ObjectUtils.isEmpty(queryResult)){
            return null;
        }
        return JSONObject.parseObject(JSONObject.toJSONString(queryResult.getData()),EquityRelationshipDataModel.CompanyDetail.class);
    }

    /**
     * 获取企业的工商信息，调用企智接口超时的时候会三次请求接口
     * @param user
     * @param name
     * @return
     */
    public EquityRelationshipDataModel.CompanyDetail queryCompanyDetailByNameOfMultipleRequests(User user,String name){

        int retryCount = 3;
        while (retryCount >= 0) {
            try {
                EquityRelationshipDataModel.CompanyDetail companyDetail = queryCompanyDetailByName(user, name);
                if(ObjectUtils.isEmpty(companyDetail)) {
                    log.warn("queryCompanyDetailByNameOfMultipleRequests is null tenantId:{}, companyName:{}",user.getTenantId(), name);
                    retryCount--;
                    safeThreadSleep(300 * (3 - retryCount));
                } else {
                    return companyDetail;
                }
            } catch (Exception e) {
                log.warn("queryCompanyDetailByNameOfMultipleRequests error tenantId:{}, companyName:{}",user.getTenantId(), name);
                retryCount--;
                safeThreadSleep(300 * (3 - retryCount));
            }
        }
        return null;
    }



    /**
     * 获取分支机构
     * @param user
     * @param name
     * @return
     */
    public EquityRelationshipDataModel.BranchInfo queryBranchInfoByName(User user,String name){
        if(STOP_QUERY_BRANCH_FLAG){
            return null;
        }
        Map<String,String> paramMap = new HashMap<>();
        paramMap.put(EquityRelationshipDataConstants.Param.KEYWORD,name);
        String url = HttpUtilse.splitJointURLOfGet(getDoMainByUseEnvironment(),QUERY_BRANCH_METHOD_NAME,paramMap);
        EquityRelationshipDataModel.QueryResult queryResult = handleIsNeedByProxyToRequest(url);
        if(ObjectUtil.isEmpty(queryResult)){
            return null;
        }

        return JSONObject.parseObject(JSONObject.toJSONString(queryResult.getData()),EquityRelationshipDataModel.BranchInfo.class);
    }

    /**
     * 根据使用的环境获取域名
     * @return
     */
    public String getDoMainByUseEnvironment(){
        if(USE_ENVIRONMENT.equals("prod")){
            return PROD_DOMAIN_NAME;
        }
        return TEST_DOMAIN_NAME;
    }


    public Map<String,String> setHeaderParam(){
        Map<String,String> paramMap = new HashMap<>();
        paramMap.put(EquityRelationshipDataConstants.Param.X_GATEWAY_APIKEY,X_GATEWAY_APIKEY);
        return paramMap;
    }



    /**
     * 单颗树处理，处理完进行保存
     * @param context
     * @param queryResult
     */
    public void singleTreeProcessing(ServiceContext context, EquityRelationshipDataModel.RelationshipModel queryResult,Map<String,Object> tempParamMap){
        log.info("singleTreeProcessing start :{}",new Date().getTime());
        //主id，所有数据都是根据这个数据生产
        String mainEId = setId(queryResult.getEid(),null);
        if(ObjectUtils.isEmpty(tempParamMap)){
            tempParamMap= new HashMap<>();
        }
        tempParamMap.put(EquityRelationshipDataConstants.Param.MAIN_EID,mainEId);
        //所有节点Map
        Map<String, EquityRelationshipData.EquityRelationship> parentNodeMap = new HashMap<>();
        //处理父节点以及本身节点
        log.info("singleTreeProcessing handleParentNode :{}",new Date().getTime());
        handleParentNodeAndTimelySave(context.getUser(),queryResult,parentNodeMap,tempParamMap);

        log.info("singleTreeProcessing handleChildNode :{}",new Date().getTime());
        //处理子节点
        handleChildNodeAndTimelySave(context.getUser(),queryResult.getC_trees(), queryResult.getEid(),parentNodeMap,tempParamMap);
        //处理数据库已经存在的关系查询出来，并把

        log.info("singleTreeProcessing handleDataThatAlreadyExistsInTheDB :{}",new Date().getTime());

        saveData(context.getUser(),null,Lists.newArrayList(parentNodeMap.values()),tempParamMap);

        log.info("singleTreeProcessing end :{}",new Date().getTime());
        saveEnterpriseObj(context.getUser(),tempParamMap);
    }

    public void saveEnterpriseObj(User user,Map<String,Object> tempParamMap){
        //如果是直接保存到pg的话，需要单独处理企业库的数据
        if(!DOWNLOAD_TO_SAVA_PG){
            return;
        }
        log.warn("saveEnterpriseObj tenant_id:{}",user.getTenantId());
        String mainEid = tempParamMap.get(EquityRelationshipDataConstants.Param.MAIN_EID).toString();
        int index=0;
        int limit=100;
        while (true) {
            String findSqlEquity = String.format("SELECT id,enterprise_id,enterprise_name,parent_enterprise_id,parent_enterprise_name from biz_equity_relationship " +
                    "where tenant_id='%s' and find_by_data_id = '%s' and enterprise_type='E' and is_deleted=0 ORDER BY create_time DESC OFFSET %s  limit %s ",SqlEscaper.pg_escape(user.getTenantId()),
                    SqlEscaper.pg_escape(mainEid),index*limit,limit);
           List<Map> result = CommonSqlUtil.findBySql(user.getTenantId(), findSqlEquity);
           if(CollectionUtils.empty(result)){
               return;
           }
           Map<String,String> tempEquity= new HashMap<>();
           //找出所有的企业id
            List<String> enterpriseIds = result.stream().filter(x->ObjectUtils.isNotEmpty(x.get("enterprise_id"))).map(x->x.get("enterprise_id").toString()).collect(Collectors.toList());
            //enterpriseIds.addAll( result.stream().filter(x->ObjectUtils.isNotEmpty(x.get("parent_enterprise_id"))).map(x->x.get("parent_enterprise_id").toString()).collect(Collectors.toList()));

            String findSql = String.format("SELECT id from biz_enterprise where tenant_id = %s and id = %s ;",
                    SqlEscaper.pg_quote(user.getTenantId()),SqlEscaper.any_clause(enterpriseIds));
            List<Map> enterpriseList = CommonSqlUtil.findBySql(user.getTenantId(),findSql);

            if(CollectionUtils.empty(enterpriseList)){
                result.forEach(x->{
                    if(ObjectUtils.isNotEmpty(x.get("enterprise_id"))){
                        tempEquity.put(x.get("enterprise_id").toString(),x.get("enterprise_name").toString());
                    }
//                    if(ObjectUtils.isNotEmpty(x.get("parent_enterprise_id"))){
//                        tempEquity.put(x.get("parent_enterprise_id").toString(),x.get("parent_enterprise_name").toString());
//                    }
                });
            }else {
                List<String> alreadyExistsEIds =enterpriseList.stream().map(x->x.get("id").toString()).collect(Collectors.toList());
                result.forEach(x->{
                    if(ObjectUtils.isNotEmpty(x.get("enterprise_id")) && !alreadyExistsEIds.contains(x.get("enterprise_id").toString())){
                        tempEquity.put(x.get("enterprise_id").toString(),x.get("enterprise_name").toString());
                    }
//                    if(ObjectUtils.isNotEmpty(x.get("parent_enterprise_id")) && !alreadyExistsEIds.contains(x.get("parent_enterprise_id").toString())){
//                        tempEquity.put(x.get("parent_enterprise_id").toString(),x.get("parent_enterprise_name").toString());
//                    }
                });
            }
            if(ObjectUtils.isEmpty(tempEquity)){
                index++;
                continue;
            }
            //在企业库中不存在的放在 tempEquity中。再根据name过滤一遍
            String findNameSql = String.format("SELECT id,name from biz_enterprise where tenant_id = %s and name = %s  and is_deleted = 0;",
                    SqlEscaper.pg_quote(user.getTenantId()),SqlEscaper.any_clause(tempEquity.values()));
            enterpriseList = CommonSqlUtil.findBySql(user.getTenantId(),findNameSql);

            List<EquityRelationshipData.EquityRelationship> insertDataList = new ArrayList<>();
            if(CollectionUtils.notEmpty(enterpriseList)){
                Map<String,Map> enterpriseMapByName = enterpriseList.stream().collect(Collectors.toMap(x->x.get("name").toString(),Function.identity(),(v1,v2)->v1));
                for(Map.Entry<String, String> entry  : tempEquity.entrySet()){
                    if(enterpriseMapByName.containsKey(entry.getValue())){
                        Map map = enterpriseMapByName.get(entry.getValue());
                        //需要替换原来的id
                        replaceOldEid(user,map.get("id").toString(),entry.getKey(),mainEid);
                    }else{
                        EquityRelationshipData.EquityRelationship entity = new EquityRelationshipData.EquityRelationship();
                        entity.setEnterprise_id(entry.getKey());
                        entity.setEnterprise_name(entry.getValue());
                        insertDataList.add(entity);
                    }
                }
            }else{
                tempEquity.entrySet().stream().forEach(entry -> {
                    EquityRelationshipData.EquityRelationship entity = new EquityRelationshipData.EquityRelationship();
                    entity.setEnterprise_id(entry.getKey());
                    entity.setEnterprise_name(entry.getValue());
                    insertDataList.add(entity);
                });
            }
            //保存企业库
            saveEnterpriseObjToPG(user,insertDataList,false);
            index++;
            if(index>10000){
                log.error("replaceOldEid  saveEnterpriseObj Force exit!!!!!");
                break;
            }
            safeThreadSleep(LIMIT_SPEED);
        }
    }

    public void replaceOldEid(User user,String newEid,String oldEid,String mainEid){
        int index=0;
        int limit = 100;
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put("enterprise_id", newEid);
        List<WhereParam> whereParams = Lists.newArrayList();
        CommonSqlUtil.addWhereParam(whereParams, Tenantable.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(user.getTenantId()));
        while (true) {
            String findSqlEquity = String.format("SELECT id,enterprise_id from biz_equity_relationship " +
                            "where tenant_id='%s' and enterprise_id = '%s' and find_by_data_id = '%s' and  is_deleted=0 ORDER BY create_time DESC OFFSET %s  limit %s ",SqlEscaper.pg_escape(user.getTenantId()),
                    SqlEscaper.pg_escape(oldEid),SqlEscaper.pg_escape(mainEid),index*limit,limit);
            List<Map> result = CommonSqlUtil.findBySql(user.getTenantId(), findSqlEquity);
            if(CollectionUtils.empty(result)){
                break;
            }
            List<String> ids = result.stream().map(x->x.get("id").toString()).collect(Collectors.toList());

            CommonSqlUtil.addWhereParam(whereParams, "id", CommonSqlOperator.IN, Lists.newArrayList(ids));
            try {
                CommonSqlUtil.updateData(user.getTenantId(), "biz_equity_relationship",dataMap,whereParams);
            } catch (MetadataServiceException e) {
                log.error("replaceOldEid  updateData enterprise_id error ",e);
            }
            index++;
            if(index>10000){
                log.error("replaceOldEid  updateData enterprise_id Force exit!!!!!");
                break;
            }
            safeThreadSleep(LIMIT_SPEED);
        }

        index=0;
        dataMap = new HashMap<>();
        dataMap.put("parent_enterprise_id", newEid);

        while (true) {
            String findSqlEquity = String.format("SELECT id,parent_enterprise_id from biz_equity_relationship " +
                            "where tenant_id='%s' and parent_enterprise_id = '%s' and find_by_data_id = '%s' and  is_deleted=0 ORDER BY create_time DESC OFFSET %s  limit %s ",SqlEscaper.pg_escape(user.getTenantId()),
                    SqlEscaper.pg_escape(oldEid),SqlEscaper.pg_escape(mainEid),index*limit,limit);
            List<Map> result = CommonSqlUtil.findBySql(user.getTenantId(), findSqlEquity);
            if(CollectionUtils.empty(result)){
                break;
            }
            List<String> ids = result.stream().map(x->x.get("id").toString()).collect(Collectors.toList());

            CommonSqlUtil.addWhereParam(whereParams, "id", CommonSqlOperator.IN, Lists.newArrayList(ids));
            try {
                CommonSqlUtil.updateData(user.getTenantId(), "biz_equity_relationship",dataMap,whereParams);
            } catch (MetadataServiceException e) {
                log.error("replaceOldEid  updateData parent_enterprise_id error ",e);
            }
            index++;
            if(index>10000){
                log.error("replaceOldEid  updateData parent_enterprise_id Force exit!!!!!");
                break;
            }
            safeThreadSleep(LIMIT_SPEED);
        }
        safeThreadSleep(LIMIT_SPEED);
    }


    /**
     * 提前保存数据，如果数据库中已经存在更新或者删除
     * @param parentNodeMap
     */
    public void saveDataInAdvance(Map<String, EquityRelationshipData.EquityRelationship> parentNodeMap,List<EquityRelationshipData.EquityRelationship> list,Map<String,Object> tempParamMap){
        if(CollectionUtils.empty(list)){
            return;
        }
        list.forEach(x->x.setFind_by_data_id(tempParamMap.get(EquityRelationshipDataConstants.Param.MAIN_EID).toString()));
        List<List<EquityRelationshipData.EquityRelationship>> listss = Lists.partition(list,200);
        for(List<EquityRelationshipData.EquityRelationship> tempNeedSaveList : listss){
            Set<String> eIds = tempNeedSaveList.stream().map(EquityRelationshipData.EquityRelationship::getEnterprise_id).collect(Collectors.toSet());
            List<EquityRelationshipData.EquityRelationship> existsRelationshipList = equityRelationshipDataMongoDbDao.queryListByEnterprisId(MOULD_TENANT_ID,Lists.newArrayList(eIds));
            if(CollectionUtils.empty(existsRelationshipList)){
                if(ObjectUtils.isNotEmpty(parentNodeMap)) tempNeedSaveList.forEach(node->{parentNodeMap.remove(node.getUniqueness_data_relationship());});

                equityRelationshipDataMongoDbDao.save(MOULD_TENANT_ID,tempNeedSaveList);
                continue;
            }
            //以存在Mongo的关系按企业节点分组
            Map<String, EquityRelationshipData.EquityRelationship> existsRelationshipMap = existsRelationshipList.stream().collect(Collectors.toMap(this::splitEIdAndPEId, Function.identity(),(v1, v2)->v1));
            List<EquityRelationshipData.EquityRelationship> updateDataList = new ArrayList<>();
            List<EquityRelationshipData.EquityRelationship> insertDataList = new ArrayList<>();
            tempNeedSaveList.forEach(node->{

                if(ObjectUtils.isNotEmpty(parentNodeMap)) parentNodeMap.remove(node.getUniqueness_data_relationship());

                String eIdAndPEId = splitEIdAndPEId(node);
                if(!existsRelationshipMap.containsKey(eIdAndPEId)){
                    insertDataList.add(node);
                    return;
                }
                EquityRelationshipData.EquityRelationship data = existsRelationshipMap.get(eIdAndPEId);
                data.setRelationship_level(node.getRelationship_level());
                data.setRoot_node_path(node.getRoot_node_path());
                data.setParent_investment(node.getParent_investment());
                data.setParent_stock_proportion(node.getParent_stock_proportion());
                data.setFind_by_data_id(node.getFind_by_data_id());
                updateDataList.add(data);
                existsRelationshipMap.remove(eIdAndPEId);
            });
            if(ObjectUtils.isNotEmpty(existsRelationshipMap)){
                List<String> objectId = existsRelationshipMap.values().stream().map(EquityRelationshipData.EquityRelationship::getId).collect(Collectors.toList());
                if(CollectionUtils.notEmpty(objectId)){
                    List<List<String>> lists = Lists.partition(objectId,200);
                    lists.forEach(l->equityRelationshipDataMongoDbDao.deleteByObjectId(MOULD_TENANT_ID,l));
                }
            }
            if(CollectionUtils.notEmpty(updateDataList)){
                List<List<EquityRelationshipData.EquityRelationship>> lists = Lists.partition(updateDataList,200);
                lists.forEach(l->equityRelationshipDataMongoDbDao.batchUpdateByList(MOULD_TENANT_ID,l));
            }
            if(CollectionUtils.notEmpty(insertDataList)){
                equityRelationshipDataMongoDbDao.save(MOULD_TENANT_ID,insertDataList);
            }
        }

    }

    public void saveData(User user,Map<String, EquityRelationshipData.EquityRelationship> parentNodeMap,List<EquityRelationshipData.EquityRelationship> list ,Map<String,Object> tempParamMap){
        if(DOWNLOAD_TO_SAVA_PG){
            saveDataToPg(user,parentNodeMap,list,tempParamMap);
        }else{
            saveDataInAdvance(parentNodeMap,list,tempParamMap);
        }
    }


    public void saveDataToPg(User user,Map<String, EquityRelationshipData.EquityRelationship> parentNodeMap,List<EquityRelationshipData.EquityRelationship> list ,Map<String,Object> tempParamMap){
        if(CollectionUtils.empty(list)){
            return;
        }
        list.forEach(x->x.setFind_by_data_id(tempParamMap.get(EquityRelationshipDataConstants.Param.MAIN_EID).toString()));
        List<List<EquityRelationshipData.EquityRelationship>> listss = Lists.partition(list,200);
        for(List<EquityRelationshipData.EquityRelationship> tempNeedSaveList : listss){
            List<String> uniquenessDataRelationships = tempNeedSaveList.stream().map(EquityRelationshipData.EquityRelationship::getUniqueness_data_relationship).collect(Collectors.toList());
            Map<String,IObjectData> existsRelationshipMap = EquityRelationshipUtils.getEquityRelationshipDataByUniquenessToMap(user,uniquenessDataRelationships);
            if(CollectionUtils.empty(existsRelationshipMap)){
                if(ObjectUtils.isNotEmpty(parentNodeMap)) tempNeedSaveList.forEach(node->{parentNodeMap.remove(node.getUniqueness_data_relationship());});
                EquityRelationshipUtils.bindingAccountMainData(user,tempNeedSaveList,null,tempParamMap);
                bulkSaveData(user,tempNeedSaveList);
                continue;
            }
            //以存在Mongo的关系按企业节点分组
            List<IObjectData> updateDataList = new ArrayList<>();
            List<EquityRelationshipData.EquityRelationship> insertDataList = new ArrayList<>();
            tempNeedSaveList.forEach(node->{

                if(ObjectUtils.isNotEmpty(parentNodeMap)) parentNodeMap.remove(node.getUniqueness_data_relationship());

                if(!existsRelationshipMap.containsKey(node.getUniqueness_data_relationship())){
                    insertDataList.add(node);
                    return;
                }
                IObjectData data = existsRelationshipMap.get(node.getUniqueness_data_relationship());
                data.set(EquityRelationshipDataConstants.Param.ROOT_NODE_PATH, node.getRoot_node_path());
                data.set(EquityRelationshipDataConstants.Param.RELATIONSHIP_TYPE, node.getRelationship_type());
                data.set(EquityRelationshipDataConstants.Param.PARENT_STOCK_PROPORTION,node.getParent_stock_proportion());
                data.set(EquityRelationshipDataConstants.Param.PARENT_INVESTMENT, node.getParent_investment());
                data.set(EquityRelationshipDataConstants.Param.RELATIONSHIP_PATH, node.getRelationship_path());
                data.set(EquityRelationshipDataConstants.Param.RELATIONSHIP_LEVEL, node.getRelationship_level());
                data.set(EquityRelationshipDataConstants.Param.FIND_BY_DATA_ID, node.getFind_by_data_id());
                updateDataList.add(data);
                existsRelationshipMap.remove(node.getUniqueness_data_relationship());
            });
//            if(ObjectUtils.isNotEmpty(existsRelationshipMap)){
//                List<String> objectId = existsRelationshipMap.values().stream().map(EquityRelationshipData.EquityRelationship::getId).collect(Collectors.toList());
//                if(CollectionUtils.notEmpty(objectId)){
//                    List<List<String>> lists = Lists.partition(objectId,200);
//                    lists.forEach(l->equityRelationshipDataMongoDbDao.deleteByObjectId(MOULD_TENANT_ID,l));
//                }
//            }
            if(CollectionUtils.notEmpty(updateDataList)){
                EquityRelationshipUtils.bindingAccountMainData(user,null,updateDataList,tempParamMap);
                serviceFacade.batchUpdate(ActionContextExt.of(user)
                        .setNotValidate(true)
                        .getContext(),updateDataList, user);
            }
            if(CollectionUtils.notEmpty(insertDataList)){
                EquityRelationshipUtils.bindingAccountMainData(user,insertDataList,null,tempParamMap);
                bulkSaveData(user,insertDataList);
            }
        }

    }
    /**
     * 处理孩子节点
     * @param c_trees
     * @param parentEId
     * @param parentNodeMap
     */
    public void handleChildNodeAndTimelySave(User user, List<EquityRelationshipDataModel.Trees> c_trees, String parentEId, Map<String, EquityRelationshipData.EquityRelationship> parentNodeMap ,Map<String,Object> tempParamMap){

        if(CollectionUtils.empty(c_trees)){
            return;
        }
        //过滤出所有父节点的路径
        List<EquityRelationshipData.EquityRelationship> xOfAllPathList = parentNodeMap.values().stream().filter(m->parentEId.equals(m.getEnterprise_id())).collect(Collectors.toList());
        c_trees.forEach(c->{
            handleEId(c,null);
            xOfAllPathList.forEach(pPath->{
                buildChildRelationshipInfo(c,pPath,parentNodeMap);
            });
            handleBranchRelationship(user, buildBaseInfo(c,null),tempParamMap);

            handleChildNodeAndTimelySave(user,c.getItems(),c.getEid(),parentNodeMap,tempParamMap);
            //该节点没有子节点的时候，先将该节点保存
            List<EquityRelationshipData.EquityRelationship> cOfAllPathList = parentNodeMap.values().stream().filter(m->c.getEid().equals(m.getEnterprise_id())).collect(Collectors.toList());
            saveData(user,parentNodeMap,cOfAllPathList,tempParamMap);
        });
    }

    /**
     * 处理父节点，上一级处理完，及时保存上一级，避免parentNodeMap变量越来越大
     * @param data
     * @param parentNodeMap
     */
    public void handleParentNodeAndTimelySave(User user, EquityRelationshipDataModel.RelationshipModel data, Map<String, EquityRelationshipData.EquityRelationship> parentNodeMap ,Map<String,Object> tempParamMap){
        handleEId(null,data);
        if(CollectionUtils.notEmpty(data.getP_trees())){
            data.getP_trees().stream().forEach(x->{
                recursionHandleParentNodeAndTimelySave(user,x,parentNodeMap,tempParamMap);
                //过滤出路x的所有的路径
                Map<String,List<EquityRelationshipData.EquityRelationship>> treesOfOfParentGroupMap = parentNodeMap.values().stream().filter(m->x.getEid().equals(m.getEnterprise_id()))
                        .collect(Collectors.groupingBy(EquityRelationshipData.EquityRelationship::getEnterprise_id));

                for (Map.Entry<String, List<EquityRelationshipData.EquityRelationship>> entry : treesOfOfParentGroupMap.entrySet()) {
                    buildRelationshipInfo(data,entry.getValue(),parentNodeMap);
                }
                handleBranchRelationship(user, buildBaseInfo(null,data),tempParamMap);
                //该节点的所有父节点路径已经否赋值给该路径，先把父节点保存到数据库，再从map中删除
                List<EquityRelationshipData.EquityRelationship> flatList = treesOfOfParentGroupMap.values().stream().flatMap(List::stream).collect(Collectors.toList());
                saveData(user,parentNodeMap,flatList,tempParamMap);
            });
        }else{
            //没有父节点
            buildNodeInfo(data,parentNodeMap);
            handleBranchRelationship(user, buildBaseInfo(null,data),tempParamMap);
        }
    }
    /**
     * 递归处理父节点，处理完上一级及时保存上一级
     * @param trees
     * @param parentNodeMap
     */
    public void recursionHandleParentNodeAndTimelySave(User user, EquityRelationshipDataModel.Trees trees, Map<String, EquityRelationshipData.EquityRelationship> parentNodeMap ,Map<String,Object> tempParamMap){
        handleEId(trees,null);
        //判断是否还有父节点
        if(CollectionUtils.notEmpty(trees.getItems())){
            for(EquityRelationshipDataModel.Trees x : trees.getItems()){
                recursionHandleParentNodeAndTimelySave (user,x,parentNodeMap,tempParamMap);
                List<EquityRelationshipData.EquityRelationship> parentNodeList = parentNodeMap.values().stream().filter(m->x.getEid().equals(m.getEnterprise_id())).collect(Collectors.toList());
                buildRelationshipInfo(trees,parentNodeList,parentNodeMap);
                //处理分支机构
                handleBranchRelationship(user, buildBaseInfo(x,null),tempParamMap);
                //该节点的所有父节点路径已经否赋值给该路径，先把父节点保存到数据库，再从map中删除
                saveData(user,parentNodeMap,parentNodeList,tempParamMap);
            }
        }else {
            //处理根节点的信息
            buildNodeInfo(trees,parentNodeMap);
            handleBranchRelationship(user, buildBaseInfo(trees,null),tempParamMap);
        }
    }


    /**
     * 构建根节点
     * @param trees
     * @param parentNodeMap
     */
    public void buildNodeInfo(EquityRelationshipDataModel.Trees trees, Map<String, EquityRelationshipData.EquityRelationship> parentNodeMap){
        EquityRelationshipData.EquityRelationship equityRelationship = buildBaseInfo(trees,null);

        buildParentNodeInfo(null, equityRelationship);

        equityRelationship.setRoot_node_path(Lists.newArrayList(equityRelationship.getEnterprise_id()));

        equityRelationship.setPercent(convertDoubleByString(trees.getPercent(),trees.getName()));
        equityRelationship.setInvestment(convertDoubleByString(trees.getAmount(),trees.getName()));
        md5Hex(equityRelationship);
        parentNodeMap.put(equityRelationship.getUniqueness_data_relationship(), equityRelationship);
    }
    /**
     * 构建根节点
     * @param data
     * @param parentNodeMap
     */
    public void buildNodeInfo(EquityRelationshipDataModel.RelationshipModel data, Map<String, EquityRelationshipData.EquityRelationship> parentNodeMap){

        EquityRelationshipData.EquityRelationship equityRelationship = buildBaseInfo(null,data);

        buildParentNodeInfo(null, equityRelationship);
        equityRelationship.setRoot_node_path(Lists.newArrayList(equityRelationship.getEnterprise_id()));

        md5Hex(equityRelationship);
        parentNodeMap.put(equityRelationship.getUniqueness_data_relationship(), equityRelationship);
    }

    /**
     * 构建关系
     * @param trees
     * @param parentNodePathList
     * @param parentNodeMap
     */
    public void buildRelationshipInfo(EquityRelationshipDataModel.Trees trees, List<EquityRelationshipData.EquityRelationship> parentNodePathList,
                                      Map<String, EquityRelationshipData.EquityRelationship> parentNodeMap){
        EquityRelationshipData.EquityRelationship equityRelationship = buildBaseInfo(trees,null);

        EquityRelationshipData.EquityRelationship parentNodePath=parentNodePathList.get(0);
        buildParentNodeInfo(parentNodePath, equityRelationship);

        equityRelationship.setParent_stock_proportion(productPercentage(parentNodePath.getPercent()));
        equityRelationship.setParent_investment(parentNodePath.getInvestment());

        equityRelationship.setRoot_node_path(setRootNodePath(parentNodePathList,equityRelationship.getEnterprise_id()));

        equityRelationship.setPercent(convertDoubleByString(trees.getPercent(),trees.getName()));
        equityRelationship.setInvestment(convertDoubleByString(trees.getAmount(),trees.getName()));

        md5Hex(equityRelationship);
        parentNodeMap.put(equityRelationship.getUniqueness_data_relationship(), equityRelationship);
    }
    /**
     * 构建关系
     * @param data 当前节点
     * @param parentNodePathList 父节点路径
     * @param parentNodeMap
     */
    public void buildRelationshipInfo(EquityRelationshipDataModel.RelationshipModel data, List<EquityRelationshipData.EquityRelationship> parentNodePathList,
                                      Map<String, EquityRelationshipData.EquityRelationship> parentNodeMap){
        EquityRelationshipData.EquityRelationship equityRelationship = buildBaseInfo(null,data);;
        EquityRelationshipData.EquityRelationship parentNodePath=parentNodePathList.get(0);
        buildParentNodeInfo(parentNodePath, equityRelationship);

        equityRelationship.setParent_stock_proportion(productPercentage(parentNodePath.getPercent()));
        equityRelationship.setParent_investment(parentNodePath.getInvestment());

        equityRelationship.setRoot_node_path(setRootNodePath(parentNodePathList,equityRelationship.getEnterprise_id()));

//        equityRelationship.setPercent(convertDoubleByString(percent));
//        equityRelationship.setInvestment(convertDoubleByString(percent));


        md5Hex(equityRelationship);
        parentNodeMap.put(equityRelationship.getUniqueness_data_relationship(), equityRelationship);
    }

    public List<String> setRootNodePath(List<EquityRelationshipData.EquityRelationship> parentNodePathList,String eId){
        Set<String> rootNodePathList = new HashSet<>();
        parentNodePathList.forEach(p->{
             rootNodePathList.addAll(p.getRoot_node_path());
        });
//        if(!EquityRelationshipDataConstants.EnterpriseTypeEnum.P.getKey().equals(parentNodePathList.get(0).getEnterprise_type())){
//            rootNodePathList.add(parentNodePathList.get(0).getEnterprise_id());
//        }
        List<String> list = Lists.newArrayList(rootNodePathList);
        list.add(eId);
        return list;
    }


    /**
     * 构建关系
     * @param c 当前节点
     * @param pPath 父节点路径
     * @param parentNodeMap
     */
    public void buildChildRelationshipInfo(EquityRelationshipDataModel.Trees c, EquityRelationshipData.EquityRelationship pPath,
                                           Map<String, EquityRelationshipData.EquityRelationship> parentNodeMap){

        EquityRelationshipData.EquityRelationship equityRelationship = buildBaseInfo(c,null);

        buildParentNodeInfo(pPath, equityRelationship);

        equityRelationship.setParent_stock_proportion(productPercentage(convertDoubleByString(c.getPercent(),c.getName())));
        equityRelationship.setParent_investment(convertDoubleByString(c.getAmount(),c.getName()));

        List<String> rootNodePathList = new ArrayList<>();
        if(CollectionUtils.notEmpty(pPath.getRoot_node_path())){
            rootNodePathList.addAll(pPath.getRoot_node_path());
        }
        rootNodePathList.add(pPath.getEnterprise_id());
        rootNodePathList.add(equityRelationship.getEnterprise_id());
        equityRelationship.setRoot_node_path(rootNodePathList);

//        equityRelationship.setPercent(convertDoubleByString(c.getPercent()));
//        equityRelationship.setInvestment(convertDoubleByString(c.getAmount()));

        md5Hex(equityRelationship);

        parentNodeMap.put(equityRelationship.getUniqueness_data_relationship(), equityRelationship);
    }

    /**
     * 构建关系的基础信息
     * @param trees
     * @return
     */
    public EquityRelationshipData.EquityRelationship buildBaseInfo(EquityRelationshipDataModel.Trees trees, EquityRelationshipDataModel.RelationshipModel data){
        EquityRelationshipData.EquityRelationship equityRelationship = new EquityRelationshipData.EquityRelationship();
        equityRelationship.set_normal(true);
        if(ObjectUtils.isNotEmpty(trees)){
            equityRelationship.setEnterprise_name(trees.getName());
            equityRelationship.setEnterprise_id(trees.getEid());
            equityRelationship.setEnterprise_type(trees.getType());
            return equityRelationship;
        }
        equityRelationship.setEnterprise_name(data.getName());
        equityRelationship.setEnterprise_id(data.getEid());
        equityRelationship.setEnterprise_type(data.getType());
        return equityRelationship;
    }
    /**
     * 构建关系的父节点的信息
     * @param parentNodePath
     * @return
     */
    public void buildParentNodeInfo(EquityRelationshipData.EquityRelationship parentNodePath, EquityRelationshipData.EquityRelationship equityRelationship){
        if(ObjectUtils.isNotEmpty(parentNodePath)){
            equityRelationship.setRelationship_level(parentNodePath.getRelationship_level()+1);
            equityRelationship.setParent_enterprise_id(parentNodePath.getEnterprise_id());
            equityRelationship.setParent_enterprise_name(parentNodePath.getEnterprise_name());
        }else {
            equityRelationship.setRelationship_level(1);
            equityRelationship.setParent_enterprise_id(null);
            equityRelationship.setParent_enterprise_name(null);
            //equityRelationship.setParent_stock_proportion(0);
        }
    }


    public void handleBranchRelationship(User user, EquityRelationshipData.EquityRelationship relationshipData ,Map<String,Object> tempParamMap){
        log.debug("handleBranchRelationship relationshipData:{}",JSONObject.toJSONString(relationshipData));
        rateLimiterService.getEquityRelationshipDataSyncLimiter().acquire();
        if(!EquityRelationshipDataConstants.EnterpriseTypeEnum.E.getKey().equals(relationshipData.getEnterprise_type())){
            return;
        }
        EquityRelationshipDataModel.CompanyDetail queryBranchResult = queryCompanyDetailByNameOfMultipleRequests(user,relationshipData.getEnterprise_name());

        if(ObjectUtils.isEmpty(queryBranchResult) || CollectionUtils.empty(queryBranchResult.getBranches())){
            return;
        }
        List<EquityRelationshipData.EquityRelationship> branchList = new ArrayList<>();
        for(EquityRelationshipDataModel.Lawsuits x : queryBranchResult.getBranches()){
            EquityRelationshipData.EquityRelationship data = new EquityRelationshipData.EquityRelationship();
            data.setEnterprise_id(setId(x.getId(),x.getName()));
            data.setEnterprise_name(x.getName());
            //data.setEnterprise_type(queryResult.getType());
            data.setRelationship_type(EquityRelationshipDataConstants.RelationshipTypeEnum.BRANCH.getKey());

            data.setParent_enterprise_id(relationshipData.getEnterprise_id());
            data.setParent_enterprise_name(relationshipData.getEnterprise_name());
            data.set_normal(true);
            md5Hex(data);
            branchList.add(data);
        }
        //该节点的所有父节点路径已经否赋值给该路径，先把父节点保存到数据库，再从map中删除
        saveData(user,null,branchList,tempParamMap);
    }



    /**
     * 利用 企业id+根节点+父节点生产唯一的值 唯一值
     * @param equityRelationship
     * @return
     */
    public void md5Hex(EquityRelationshipData.EquityRelationship equityRelationship){
        StringBuilder sb = new StringBuilder();
        sb.append(equityRelationship.getEnterprise_id())
                .append(ObjectUtils.isNotEmpty(equityRelationship.getParent_enterprise_id())? equityRelationship.getParent_enterprise_id():"null");
        equityRelationship.setUniqueness_data_relationship(MessageHelper.md5(sb.toString()));
    }

    /**
     * 如果eid为空则生产新的id,如果有则去掉横线-
     * @param trees
     * @return
     */
    public void handleEId(EquityRelationshipDataModel.Trees trees, EquityRelationshipDataModel.RelationshipModel data){
        if(ObjectUtils.isNotEmpty(trees)){
            trees.setEid(setId(trees.getEid(),trees.getName()));
        }else if(ObjectUtils.isNotEmpty(data)){
            data.setEid(setId(data.getEid(),data.getName()));
        }else{
            log.error("handleEId trees is null data is null");
        }
    }

    public String setId(String id,String name){
        if(ObjectUtils.isEmpty(id) || "null".equals(id)){
            if(ObjectUtils.isNotEmpty(name)){
                String eId = getEIdByMongoDb(name);
                if(ObjectUtils.isNotEmpty(eId)){
                    return eId;
                }
            }
            return serviceFacade.generateId();
        }
        return id.replaceAll("-","");
    }

    public String getEIdByMongoDb(String name){
        List<EquityRelationshipData.EquityRelationship> equityRelationshipList = equityRelationshipDataMongoDbDao.queryListByEnterprisName(MOULD_TENANT_ID,name);
        if (CollectionUtils.empty(equityRelationshipList)){
            return null;
        }
        return equityRelationshipList.get(0).getEnterprise_id();
    }

    /**
     * 把关系保存到pg
     * @param user
     * @param list
     */
    private void bulkSaveData(User user,List<EquityRelationshipData.EquityRelationship> list ) {
        List<IObjectData> dataList = new ArrayList<>();
        list.forEach(x->{
            dataList.add(EquityRelationshipUtils.getObjectDataOfEquityRelationshipObj(user,x));
        });
        serviceFacade.bulkSaveObjectData(dataList, user, true, true, p -> {
            return ActionContextExt.of(user)
                    .setNotValidate(true)
                    .getContext();
        });
    }

    public String splitEIdAndPEId(EquityRelationshipData.EquityRelationship x){
        if(ObjectUtils.isEmpty(x.getParent_enterprise_id())){
            return x.getEnterprise_id()+"-null";
        }
        return x.getEnterprise_id()+"-"+x.getParent_enterprise_id();
    }

    public double convertDoubleByString(String str,String name){
        try {
            if(ObjectUtils.isEmpty(str) || "null".equals(str)){
                return 0;
            }
            if(str.contains("shares")){
                str = str.replaceAll("shares","");
            }

            return Double.parseDouble(str);
        }catch (Exception e){
            log.error("convertDoubleByString error trees.getName():{},str:{},e:",name,str,e);
            return 0;
        }
    }

    /**
     * 占比乘以100
     * @param num
     * @return
     */
    public double productPercentage(Double num){
        if(ObjectUtils.isEmpty(num)){
            return 0;
        }
        return num*100;
    }

    /**
     * 是否需要通过代理去请求
     * @param url
     * @return
     */
    public EquityRelationshipDataModel.QueryResult handleIsNeedByProxyToRequest(String url){
        log.debug("handleIsNeedByProxyToRequest IS_NEED_PROXY:{},url:{}",IS_NEED_PROXY,url);
        if(IS_NEED_PROXY){
            return webGetByProxy(url);
        }else{
            return webGet(url);
        }
    }


    public EquityRelationshipDataModel.QueryResult webGet(String url){
        String result="";
        try {
            result = new String(HttpUtils.webGet(new URL(url),setHeaderParam(),HttpUtilse.DEFAULT_TIMEOUT_CONNEC,HttpUtilse.DEFAULT_TIMEOUT), StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.warn("EquityRelationshipDataService webGet error url:{},e:",url,e);
        }
        if(ObjectUtils.isEmpty(result)){
            return null;
        }
        return JSONObject.parseObject(result,EquityRelationshipDataModel.QueryResult.class);
    }

    public EquityRelationshipDataModel.QueryResult webGetByProxy(String url){
        EquityRelationshipDataModel.QueryResult result = null;
        try {
            Request request = new Request.Builder().url(url).header(EquityRelationshipDataConstants.Param.X_GATEWAY_APIKEY,X_GATEWAY_APIKEY).build();
            result = client.parseObject(request, new TypeReference<EquityRelationshipDataModel.QueryResult>() {});
        } catch (Exception e) {
            log.warn("EquityRelationshipDataService webGetByProxy error url:{},e:",url,e);
        }
        return result;
    }

    private void safeThreadSleep(int sleepMillis) {
        try {
            Thread.sleep(sleepMillis);
        } catch (InterruptedException e) {
            log.warn("", e);
            Thread.currentThread().interrupt();
        } catch (Exception ex) {
            log.warn("", ex);
        }
    }
    public EquityRelationshipDataModel.RelationshipModel getIndustryInterfaceByBI(User user,String name){
        EquityRelationshipDataModel.RelationshipModel  relationshipModel = null;
            EquityRelationshipDataModel.QueryResult result = sfaIndustryInterfaceProxy.getFamilyTree(HttpHeaderUtil.getHeaders(user),name);
            if(ObjectUtils.isEmpty(result) || !"200".equals(result.getStatus())){
                log.warn("EquityRelationshipDataService getIndustryInterfaceByBI getFamilyTree error  name:{},result:{}",name,JSONObject.toJSONString(result));
                throw new ValidateException("调用bi接口查询工商信息失败！");
            }
            relationshipModel = result.getResult();
            if(ObjectUtils.isEmpty(relationshipModel) || (ObjectUtils.isEmpty(relationshipModel.getEid()) && ObjectUtils.isEmpty(relationshipModel.getName()))){
                //判断是否有分支机构
                SFABICompanyBaseDTO dto = new SFABICompanyBaseDTO();
                dto.setName(name);
                List<SFABICompanyBaseEntity> baseList = sfabiCompanyBaseMapper.selectCompanyBaseByParam(dto);
                if(CollectionUtils.notEmpty(baseList)){
                    SFABICompanyBaseEntity entity = baseList.get(0);
                    relationshipModel.setEid(entity.getCompanyId());
                    relationshipModel.setName(entity.getCompanyName());
                    return relationshipModel;
                }
                log.warn("EquityRelationshipDataService result.getResult() is null   name:{},result:{}",name,JSONObject.toJSONString(result));
                throw new ValidateException(I18N.text(SfaLtoI18NKeyUtil.SFA_EQUITY_INFORMATION_NOT_EXIST_BY_BI));
            }
        return relationshipModel;
    }
    public EquityRelationshipDataModel.CompanyDetail getCompanyDetailByBI(User user,String name){
        EquityRelationshipDataModel.CompanyDetail  companyDetail = null;
            EquityRelationshipDataModel.CompanyDetailResult result = sfaIndustryInterfaceProxy.companyDetail(HttpHeaderUtil.getHeaders(user),name);
            if(ObjectUtils.isEmpty(result) || !"200".equals(result.getStatus())){
                log.warn("EquityRelationshipDataService getCompanyDetailByBI companyDetail error  name:{},result:{}",name,JSONObject.toJSONString(result));
                throw new ValidateException("调用bi接口查询工商信息失败！");
            }
            companyDetail = result.getResult();
            if(ObjectUtils.isEmpty(companyDetail) || (ObjectUtils.isEmpty(companyDetail.getId())) && ObjectUtils.isEmpty(companyDetail.getName())){
                log.warn("EquityRelationshipDataService getCompanyDetailByBI result is null  name:{},result:{}",name,JSONObject.toJSONString(result));
                throw new ValidateException(I18N.text(SfaLtoI18NKeyUtil.SFA_EQUITY_INFORMATION_DOES_NOT_EXIST));
            }
        return companyDetail;
    }


    public IObjectData getObjectDataOfEnterpriseInfoObj(User user,String id,EquityRelationshipDataModel.CompanyDetail companyDetail){
        IObjectData objectData = ObjectDataUtil.createBaseObjectData(user);
        objectData.setId(id);
        objectData.set("object_describe_api_name", Utils.ENTERPRISE_INFO_API_NAME);
        objectData.set("biz_reg_name", true);
        //处理省市区
        if(ObjectUtils.isNotEmpty(companyDetail.getDistrict_code())){
            List<CountryAreaService.CountryAreaDTO> dtoList = countryAreaService.findAreaByStandardCodes("1",user.getTenantId(),Lists.newArrayList(companyDetail.getDistrict_code()));
            if(CollectionUtils.notEmpty(dtoList) && ObjectUtils.isNotEmpty(dtoList.get(0).getBreadcrumbCode())){
                String[] codeArr = dtoList.get(0).getBreadcrumbCode().split("/");
                if(codeArr.length>=2){
                    objectData.set("country", codeArr[1]);
                    if(codeArr.length>=3){
                        objectData.set("province", codeArr[2]);
                        if(codeArr.length>=4){
                            objectData.set("city", codeArr[3]);
                            if(codeArr.length>=5){
                                objectData.set("district", codeArr[4]);
                            }
                        }
                    }
                }
            }
        }

        Map companyDetailMap = JSONObject.parseObject(JSONObject.toJSONString(companyDetail),Map.class);
        fieldMapping.forEach(x->{
            String value = "";
            String sourceFieldApiName = x.get("source_field_api_name").toString();
            String targetFieldApiName = x.get("target_field_api_name").toString();
            try {
                if(sourceFieldApiName.contains(".")){
                    String[] sourceFieldArr =sourceFieldApiName.split("\\.");
                    boolean isArrTypeFlag = false;
                    if(arrTypeFieldApiName.contains(sourceFieldArr[0])){
                        isArrTypeFlag = true;
                    }
                    for(int i=0;i<sourceFieldArr.length;i++){
                        if(i==0){
                            if(!companyDetailMap.containsKey(sourceFieldArr[i]) || ObjectUtils.isEmpty(companyDetailMap.get(sourceFieldArr[i]))){
                               break;
                            }
                            value = companyDetailMap.get(sourceFieldArr[i]).toString();
                            continue;
                        }
                        if(isArrTypeFlag){
                            List<Map> mapList = JSONObject.parseArray(value,Map.class);
                            if(CollectionUtils.empty(mapList)){
                                value = "";
                                break;
                            }
                            value = mapList.get(0).get(sourceFieldArr[i]).toString();
                        }else{
                            Map map = JSONObject.parseObject(value,Map.class);
                            if(map.containsKey(sourceFieldArr[i]) && ObjectUtils.isNotEmpty(map.get(sourceFieldArr[i]))){
                                value = map.get(sourceFieldArr[i]).toString();
                            }

                        }

                    }
                }else{
                    if(companyDetailMap.containsKey(sourceFieldApiName) && ObjectUtils.isNotEmpty(companyDetailMap.get(sourceFieldApiName))){
                        value = companyDetailMap.get(sourceFieldApiName).toString();
                    }
                }
            }catch (Exception e){
                log.warn("EquityRelationshipDataSyncService getObjectDataOfEnterpriseInfoObj error tenantId:{}, sourceFieldApiName:{},name:{},e:",user.getTenantId(),sourceFieldApiName,companyDetail.getName(),e);
            }
            objectData.set(targetFieldApiName,value);

        });
        return objectData;
    }


    public void  saveEnterpriseObjToPG(User user,List<EquityRelationshipData.EquityRelationship> insertDataList,boolean isCheckType){
        if(CollectionUtils.empty(insertDataList)){
            return;
        }
        //将不存在的id保存在企业库
        List<IObjectData> dataList = new ArrayList<>();
        List<String> idsFlagList = new ArrayList<>();
        insertDataList.forEach(x->{
            if(idsFlagList.contains(x.getEnterprise_id())){
                return;
            }
            if(isCheckType && !EquityRelationshipDataConstants.EnterpriseTypeEnum.E.getKey().equals(x.getEnterprise_type())){
                return;
            }
            EquityRelationshipDataModel.CompanyDetail companyDetail = null;
            try {
                companyDetail = queryCompanyDetailByNameOfMultipleRequests(user,x.getEnterprise_name());
            }catch (Exception e){
                log.error("EquityRelationshipDataSyncService saveEnterpriseObj companyDetail get error tenantId:{}, x.getEnterprise_name:{}",user.getTenantId(),JSONObject.toJSONString(x.getEnterprise_name()));
            }
            if(ObjectUtils.isEmpty(companyDetail) || ObjectUtils.isEmpty(companyDetail.getId()) || ObjectUtils.isEmpty(companyDetail.getName()))
            {
                log.warn("EquityRelationshipDataSyncService saveEnterpriseObj companyDetail is null tenantId:{}, x.getEnterprise_name:{}",user.getTenantId(),JSONObject.toJSONString(x.getEnterprise_name()));
                return;
            }

            dataList.add(getObjectDataOfEnterpriseInfoObj(user,x.getEnterprise_id(),companyDetail));
            idsFlagList.add(x.getEnterprise_id());
        });
        serviceFacade.bulkSaveObjectData(dataList, user, true, true, p -> {
            return ActionContextExt.of(user)
                    .setNotValidate(true)
                    .getContext();
        });
    }

    public void checkEquityRelationIsExist(User user,String name){
        log.info("checkEquityRelationIsExist start name:{}",name);
        EquityRelationshipDataModel.CheckEquityRelationIsExistResult result = sfaIndustryInterfaceProxy.checkEquityRelationIsExist(HttpHeaderUtil.getHeaders(user),name);
        if(ObjectUtils.isEmpty(result) ||  ObjectUtils.isEmpty(result.getResult())){
            throw new ValidateException(I18N.text(SfaLtoI18NKeyUtil.SFA_EQUITY_INFORMATION_DOES_NOT_EXIST));
        }
        EquityRelationshipDataModel.CheckEquityRelationIsExist checkEquityRelationIsExist = result.getResult();
        if(checkEquityRelationIsExist.getBranchNum()==0 && checkEquityRelationIsExist.getRevertNum()==0){
            throw new ValidateException(I18N.text(SfaLtoI18NKeyUtil.SFA_EQUITY_INFORMATION_NOT_EXIST_BY_BI));
        }
//        SFABICompanyBaseDTO sfabiCompanyBaseDTO = new SFABICompanyBaseDTO();
//        sfabiCompanyBaseDTO.setName(name);
//        List<SFABICompanyBaseEntity> baseList = sfabiCompanyBaseMapper.selectCompanyBaseByParam(sfabiCompanyBaseDTO);
//        if(CollectionUtils.empty(baseList)){
//            throw new ValidateException(I18N.text(SfaLtoI18NKeyUtil.SFA_EQUITY_INFORMATION_DOES_NOT_EXIST));
//        }
//        sfabiCompanyBaseDTO.setCompanyId(baseList.get(0).getCompanyId());
//        sfabiCompanyBaseDTO.setName(null);
//        int branchNum = sfabiCompanyBranchMapper.getTotalByCompanyId(sfabiCompanyBaseDTO);
//        int revertNum = sfabiOptEntPartnerCnRevertMapper.getTotalByCompanyId(sfabiCompanyBaseDTO);
//        if(branchNum==0 && revertNum==0){
//            throw new ValidateException(I18N.text(SfaLtoI18NKeyUtil.SFA_EQUITY_INFORMATION_NOT_EXIST_BY_BI));
//        }
    }

    public void handleEquityRelationship(EquityRelationshipDataModel.RelationshipCreateMsg msg){
        log.info("EquityRelationshipService handleEquityRelationship msg:{}",msg);
        try {
            User user = new User(msg.getTenantId(), msg.getUserId());
            EquityRelationshipDataModel.RelationshipModel relationshipModel = queryEquityInfoByName( user, msg.getName());
            Map<String,Object> tempParamMap = new HashMap<>();
            tempParamMap.put(EquityRelationshipDataConstants.Param.DATA_NAME,msg.getName());
            tempParamMap.put(EquityRelationshipDataConstants.Param.DATA_ID,msg.getObjectId());
            long time = System.currentTimeMillis();
            ServiceContext serviceContext = new ServiceContext(RequestContext.builder().tenantId(msg.getTenantId())
                    .user(user).appId(msg.getAppId()).build(), null, null);
            singleTreeProcessing(serviceContext, relationshipModel,tempParamMap);
            if(isOpenAllSyncDataFlag){
                equityRelationshipDataSyncService.realTimeUpdatePGByTime(serviceContext.getTenantId(),time,tempParamMap);
            }
            sendCrmNotification(serviceContext,msg);
        }catch (Exception e){
            log.error("EquityRelationshipService tenantId:{}, name:{},e:",msg.getTenantId(),msg.getName(),e);
        }finally {
            sfaLtoRedisService.releaseLock(msg.getTenantId(), msg.getObjectApiName(), ObjectAction.QUERY_EQUITY_RELATIONSHIP.getActionCode(),msg.getName(), msg.getRedisRequestId());
        }
    }

    public static void sendCrmNotification(ServiceContext serviceContext,EquityRelationshipDataModel.RelationshipCreateMsg msg) {
        User user = new User(serviceContext.getTenantId(), User.SUPPER_ADMIN_USER_ID);
        // 操作人id
        String operationUserId = serviceContext.getUser().getUpstreamOwnerIdOrUserId();
        String title = I18N.text(SfaLtoI18NKeyUtil.SFA_EQUITY_INFORMATION_QUERY_SUCCESS_TITLE);
        String remindContent = String.format(I18N.text(SfaLtoI18NKeyUtil.SFA_EQUITY_INFORMATION_QUERY_SUCCESS_CONTENT),msg.getName());

        //新crm通知
        CRMRecordUtil.sendNewCRMRecord(crmNotificationService,
                user,
                msg.getObjectType(),
                Lists.newArrayList(Integer.valueOf(operationUserId)),
                User.SUPPER_ADMIN_USER_ID,
                title,
                remindContent,
                SfaLtoI18NKeyUtil.SFA_EQUITY_INFORMATION_QUERY_SUCCESS_TITLE,
                com.beust.jcommander.internal.Lists.newArrayList(),
                SfaLtoI18NKeyUtil.SFA_EQUITY_INFORMATION_QUERY_SUCCESS_CONTENT,
                com.beust.jcommander.internal.Lists.newArrayList(msg.getName()),
                CRMRecordUtil.getUrlParameter(msg.getObjectApiName(), msg.getObjectId()), serviceContext.getAppId()
            );
    }

}
