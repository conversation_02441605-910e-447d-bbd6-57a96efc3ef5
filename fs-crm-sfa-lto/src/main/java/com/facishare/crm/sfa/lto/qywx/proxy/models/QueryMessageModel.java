package com.facishare.crm.sfa.lto.qywx.proxy.models;

import com.alibaba.fastjson2.JSON;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

public interface QueryMessageModel {

    @Data
    class QueryMessageArg {
        private String fsEa;
        private String queryWord;
        private Long startTime;
        private Long endTime;
        private Integer limit;
        private String cursor;
        private ChatInfo chatInfo;
    }

    @Data
    class ChatInfo implements Serializable {
        private Integer chatType;
        private List<IdInfo> idList;
        private String chatId;
        private List<Integer> msgTypeList;
        private IdInfo sender;
    }
    @Data
    class IdInfo implements Serializable {
        private String openUserid;
        private String externalUserid;
    }

    @Data
    class QueryMessageResult extends BaseQywxResult {
        private MessageData data;
    }

    @Data
    class MessageData {
        private Integer errcode;
        private String errmsg;
        private List<MessageId> msgList;
        private Integer hasMore;
        private String nextCursor;
    }

    @Data
    class MessageId implements Serializable{
        private String msgid;
    }

}
