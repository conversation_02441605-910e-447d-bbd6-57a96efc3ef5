package com.facishare.crm.sfa.prm.rest.config;

import com.facishare.crm.sfa.prm.rest.client.EmailClient;
import com.facishare.crm.sfa.prm.rest.client.EmailTemplateClient;
import com.facishare.crm.sfa.prm.rest.client.SmsHubClient;
import com.facishare.rest.core.RestServiceProxyFactory;
import com.facishare.rest.core.RestServiceProxyFactoryBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-03-11
 * ============================================================
 */
@Configuration
public class RestClientConfig {
    @Resource
    private RestServiceProxyFactory restServiceProxyFactory;

    @Bean
    public EmailClient emailClient() throws Exception {
        RestServiceProxyFactoryBean proxyBean = new RestServiceProxyFactoryBean();
        proxyBean.setType(EmailClient.class);
        proxyBean.setFactory(restServiceProxyFactory);
        return (EmailClient) proxyBean.getObject();
    }

    @Bean
    public SmsHubClient smsHubClient() throws Exception {
        RestServiceProxyFactoryBean proxyBean = new RestServiceProxyFactoryBean();
        proxyBean.setType(SmsHubClient.class);
        proxyBean.setFactory(restServiceProxyFactory);
        return (SmsHubClient) proxyBean.getObject();
    }

    @Bean
    public EmailTemplateClient emailTemplateClient() throws Exception {
        RestServiceProxyFactoryBean proxyBean = new RestServiceProxyFactoryBean();
        proxyBean.setType(EmailTemplateClient.class);
        proxyBean.setFactory(restServiceProxyFactory);
        return (EmailTemplateClient) proxyBean.getObject();
    }
}
