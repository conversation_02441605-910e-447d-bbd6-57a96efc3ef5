package com.facishare.crm.sfa.lto.integral.core.entity;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class ObjectDataDocument implements Serializable {

    public static final String ID = "_id";

    private Map<String, Object> data;

    private ObjectDataDocument(String id, Map<String, Object> fieldValueMap){
        data = Maps.newHashMap();
        data.put(ID, id);
        fieldValueMap.forEach((name, value) -> {
            data.put(name, value);
        });
    }

    public static ObjectDataDocument of(String id, Map<String, Object> fieldValueMap) {
        if (Objects.isNull(fieldValueMap)) {
            throw new RuntimeException("规则结果为null");
        }
        ObjectDataDocument objectDataDocument = new ObjectDataDocument(id, fieldValueMap);
        return  objectDataDocument;
    }

    public String getObjectId() {
        return (String) data.get(ID);
    }

    public Map<String, Object> getData() {
        return data;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ObjectDataDocument that = (ObjectDataDocument) o;
        return Objects.equals(data.get(ID), that.data.get(ID));
    }

    @Override
    public int hashCode() {
        return Objects.hash(data.get(ID));
    }

    public static List<Map<String, Object>> getDataList(List<ObjectDataDocument> objectDataDocuments) {
        List<Map<String, Object>> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(objectDataDocuments)) {
            return result;
        }
        objectDataDocuments.forEach(x -> {
            result.add(x.getData());
        });
        return result;
    }

}
