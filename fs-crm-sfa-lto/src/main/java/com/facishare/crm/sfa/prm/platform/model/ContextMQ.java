package com.facishare.crm.sfa.prm.platform.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-13
 * ============================================================
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ContextMQ<T> extends Base {
    private T payload;
}

@Data
class Base {
    private String tenantId;
    private String userId;
    private String outTenantId;
    private String outUserId;
    private String appId;
    private String language;
}
