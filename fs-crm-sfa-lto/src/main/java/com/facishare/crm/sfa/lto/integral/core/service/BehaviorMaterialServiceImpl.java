package com.facishare.crm.sfa.lto.integral.core.service;

import com.facishare.crm.sfa.lto.integral.common.constant.IntegralObject;
import com.facishare.crm.sfa.lto.integral.common.constant.RuleDataConstant;
import com.facishare.crm.sfa.lto.integral.core.service.dto.BehaviorInfo;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.function.Consumer;

@Service
public class BehaviorMaterialServiceImpl extends BehaviorObjectService implements BehaviorMaterialService {
    @Autowired
    DataLogicService dataLogicService;

    @Override
    public IObjectData getBehaviorMaterialByApiName(String tenantId, String categoryApiName, String materialApiName) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, IntegralObject.FIELD_CATEGORY_API_NAME, categoryApiName);
        SearchUtil.fillFilterEq(filters, IntegralObject.FIELD_MATERIAL_API_NAME, materialApiName);
        query.addFilters(filters);
        query.setLimit(1);
        query.setNeedReturnCountNum(false);

        List<IObjectData> objectDataList = dataLogicService.findBySearchQuery(tenantId, IntegralObject.BEHAVIOR_MATERIAL_API_NAME, query);
        return CollectionUtils.notEmpty(objectDataList) ? objectDataList.get(0) : null;
    }

    @Override
    public IObjectData getBehaviorMaterialByLabel(String tenantId, String categoryApiName, String materialLabel) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, IntegralObject.FIELD_CATEGORY_API_NAME, categoryApiName);
        SearchUtil.fillFilterEq(filters, IntegralObject.FIELD_MATERIAL_LABEL, materialLabel);
        query.addFilters(filters);
        query.setLimit(1);
        query.setNeedReturnCountNum(false);

        List<IObjectData> objectDataList = dataLogicService.findBySearchQuery(tenantId, IntegralObject.BEHAVIOR_MATERIAL_API_NAME, query);
        return CollectionUtils.notEmpty(objectDataList) ? objectDataList.get(0) : null;
    }

    @Override
    public List<IObjectData> getBehaviorMaterialByCategoryApiName(String tenantId, String categoryApiName, String materialApiName) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, IntegralObject.FIELD_CATEGORY_API_NAME, categoryApiName);
        SearchUtil.fillFilterEq(filters, IntegralObject.FIELD_MATERIAL_API_NAME, materialApiName);
        query.addFilters(filters);
        query.setLimit(1000);
        query.setNeedReturnCountNum(false);

        return dataLogicService.findBySearchQuery(tenantId, IntegralObject.BEHAVIOR_MATERIAL_API_NAME, query);
    }

    @Override
    public List<BehaviorInfo.BehaviorMaterial> getBatchBehaviorMaterialList(String tenantId, String categoryApiName) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, IntegralObject.FIELD_IS_ACTIVE, true);
        SearchUtil.fillFilterEq(filters, IntegralObject.FIELD_CATEGORY_API_NAME, categoryApiName);
        query.addFilters(filters);
        List<OrderBy> orders = Lists.newArrayList();
        SearchUtil.fillOrderBy(orders, DBRecord.CREATE_TIME, true);
        query.setLimit(1000);
        query.setOrders(orders);
        query.setNeedReturnCountNum(false);
        List<BehaviorInfo.BehaviorMaterial> materialList = Lists.newArrayList();
        materialList.add(getAnyOneMaterial());
        //用分类apiName进行分组
        templateQueryAll(query, tenantId, IntegralObject.BEHAVIOR_MATERIAL_API_NAME, dataList -> {
            //dataList 按照分类apiName进行分组
            for (IObjectData materialData : dataList) {
                //从map中获取分类apiName对应的list，如果没有则创建
                materialList.add(getMaterial(materialData.get(IntegralObject.FIELD_MATERIAL_API_NAME, String.class),
                        materialData.get(IntegralObject.FIELD_MATERIAL_LABEL, String.class)));
            }
        });
        return materialList;
    }

    @Override
    public List<IObjectData> getBehaviorMaterialList(String tenantId) {

        return getBehaviorObjectList(tenantId, IntegralObject.BEHAVIOR_MATERIAL_API_NAME);
    }


    private BehaviorInfo.BehaviorMaterial getAnyOneMaterial() {
        return getMaterial(RuleDataConstant.MATERIAL_API_NAME_ANY_ONE, RuleDataConstant.getAnyOneLabel());
    }

    private BehaviorInfo.BehaviorMaterial getMaterial(String materialApiName, String materialLabel) {
        BehaviorInfo.BehaviorMaterial material = new BehaviorInfo.BehaviorMaterial();
        material.setMaterialApiName(materialApiName);
        material.setMaterialLabel(materialLabel);
        return material;
    }

    /**
     * 保证查询searchQuery所有数据，
     * 每次查询出来的limit数据，
     * 执行consumer的入参
     *
     * @param searchQuery searchQuery
     * @param tenantId    tenantId
     * @param apiName     apiName
     * @param consumer    执行结果的消费者
     */
    private void templateQueryAll(SearchTemplateQuery searchQuery, String tenantId, String apiName, Consumer<List<IObjectData>> consumer) {
        int offset;
        int limit = searchQuery.getLimit();
        //目前就查询2次，省得数量太多oom
        for (int i = 0; i < 4; i++) {
            offset = i * limit;
            searchQuery.setOffset(offset);
            //1、根据源价目表ID查询价目表明细数据
            List<IObjectData> detailDates = dataLogicService.findBySearchQuery(tenantId, apiName, searchQuery);
            if (CollectionUtils.empty(detailDates)) {
                break;
            }
            try {
                consumer.accept(detailDates);
            } catch (Exception e) {
                continue;
            }
            if (detailDates.size() < limit) {
                break;
            }
        }
    }

    @Override
    public List<IObjectData> getBehaviorMaterialList(String tenantId, Set<String> categoryApiNames, Set<String> materialApiNames) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, IntegralObject.FIELD_CATEGORY_API_NAME, Lists.newArrayList(categoryApiNames));
        SearchUtil.fillFilterIn(filters, IntegralObject.FIELD_MATERIAL_API_NAME, Lists.newArrayList(materialApiNames));
        query.addFilters(filters);
        query.setLimit(1000);
        query.setNeedReturnCountNum(false);

        return dataLogicService.findBySearchQuery(tenantId, IntegralObject.BEHAVIOR_MATERIAL_API_NAME, query);
    }

    @Override
    public void updateBehaviorMaterial(IObjectData objectData, String userId, String materialLabel) {
        objectData.set(IntegralObject.FIELD_MATERIAL_LABEL, materialLabel);
        objectData.set(IntegralObject.FIELD_IS_ACTIVE, true);
        objectData.setLastModifiedBy(userId);
        objectData.setLastModifiedTime(System.currentTimeMillis());
        List<String> validFieldApiNames = Lists.newArrayList(IntegralObject.FIELD_MATERIAL_LABEL,
                IntegralObject.FIELD_IS_ACTIVE, IObjectData.LAST_MODIFIED_BY, IObjectData.LAST_MODIFIED_TIME);
        dataLogicService.batchUpdateIgnoreOther(Lists.newArrayList(objectData), validFieldApiNames);
    }
}
