package com.facishare.crm.sfa.lto.integral.core.service;

import com.facishare.crm.sfa.lto.integral.core.entity.IntegralRuleDocument;
import com.facishare.crm.sfa.lto.integral.core.rest.dto.DeleteRuleFromEngine;
import com.facishare.crm.sfa.lto.integral.core.rest.dto.GetInUsedDescribe;
import com.facishare.crm.sfa.lto.integral.core.rest.dto.GetRuleDetail;
import com.facishare.crm.sfa.lto.integral.core.rest.dto.GetRuleList;
import com.facishare.crm.sfa.lto.integral.core.rest.dto.QueryResultList;
import com.facishare.crm.sfa.lto.integral.core.rest.dto.SaveRuleOrUpdate;
import com.facishare.crm.sfa.lto.integral.core.rest.dto.UpdateStatus;
import com.facishare.paas.appframework.core.model.RequestContext;

import java.util.List;

/**
 * 调用引擎查询规则业务逻辑
 */
public interface IntegralRuleEngineLogicService {

    /**
     * 创建规则
     */
    void saveRule(SaveRuleOrUpdate.Arg arg);

    /**
     * 更新规则
     */
    void updateRule(SaveRuleOrUpdate.Arg arg);

    /**
     * 更新规则状态
     */
    void updateRuleStatus(UpdateStatus.Arg arg);

    /**
     * 删除规则
     */
    void deleteRule(DeleteRuleFromEngine.Arg arg);

    /**
     * 查询规则列表
     */
    GetRuleList.Result getRuleListByPage(GetRuleList.Arg arg);

    /**
     * 获取规则详情
     */
    GetRuleDetail.Result getRuleDetail(GetRuleDetail.Arg body);

    /**
     * 通过对象apiName查询apiName
     */
    List<String> findRuleApiNameByDescribeApiName(String describeApiName, RequestContext context);

    /**
     * 查询所有规则绑定的对象列表
     */
    GetInUsedDescribe.Result getInUsedDescribeList(GetInUsedDescribe.Arg arg);

    /**
     * 查询规则结果
     * @param arg
     * @return
     */
    QueryResultList.Result queryResultList(QueryResultList.Arg arg);

    /**
     * 根据规则ApiName或者Label获取规则基本信息
     * @param apiName 规则ApiName
     * @param label 规则名称
     * @param tenantId 企业ID
     * @param userId 用户ID
     * @return 规则基本信息
     */
    List<IntegralRuleDocument> getRuleBasicInfo(String apiName, String label, String tenantId, String userId);
}
