package com.facishare.crm.sfa.lto.integral.core.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.lto.integral.core.service.dto.BehaviorInfo;
import com.facishare.paas.I18N;
import com.github.autoconf.ConfigFactory;

import java.util.List;

/**
 * 系统预置行为档案
 *
 * <AUTHOR>
 */
public class SystemBehaviorArchives {

    public static final String LANG_KEY_CATEGORY_PREFIX="sfa.behavior.integral.category.";
    public static final String LANG_KEY_ACTION_PREFIX="sfa.behavior.integral.action.";
    private static List<BehaviorInfo.BehaviorCategory> systemBehaviorCategoryList = null;

    static {
        ConfigFactory.getInstance().getConfig("fs-crm-integral-behavior-archives",
                config -> {
                    systemBehaviorCategoryList = JSON.parseArray(config.getString(), BehaviorInfo.BehaviorCategory.class);
                });
    }

    public static List<BehaviorInfo.BehaviorCategory> getSystemBehaviorCategoryList(){
        handleLang();
        return systemBehaviorCategoryList;
    }

    private static void handleLang() {
        systemBehaviorCategoryList.stream().forEach(category -> {
            category.setCategoryLabel(I18N.text(LANG_KEY_CATEGORY_PREFIX + category.getCategoryApiName()));
            category.getActions().forEach(action -> {
                action.setActionLabel(I18N.text(LANG_KEY_ACTION_PREFIX + action.getActionApiName()));
            });
        });
    }

    public static Boolean existSystemCategoryApiName(String apiName) {
        return systemBehaviorCategoryList.stream().anyMatch(x -> apiName.equals(x.getCategoryApiName()));
    }

    public static Boolean existSystemCategoryLabel(String label) {
        return getSystemBehaviorCategoryList().stream().anyMatch(x -> label.equals(x.getCategoryLabel()));
    }

    public static Boolean existSystemActionApiName(String categoryApiName, String actionApiName) {
        Boolean exist = false;
        for (BehaviorInfo.BehaviorCategory category : systemBehaviorCategoryList) {
            if (categoryApiName.equals(category.getCategoryApiName())) {
                if (category.getActions().stream()
                        .anyMatch(x -> actionApiName.equals(x.getActionApiName()))) {
                    exist = true;
                    break;
                }
            }
        }
        return exist;
    }

    public static Boolean existSystemActionLabel(String categoryApiName, String actionLabel) {
        Boolean exist = false;
        for (BehaviorInfo.BehaviorCategory category : getSystemBehaviorCategoryList()) {
            if (categoryApiName.equals(category.getCategoryApiName())) {
                if (category.getActions().stream()
                        .anyMatch(x -> actionLabel.equals(x.getActionLabel()))) {
                    exist = true;
                    break;
                }
            }
        }
        return exist;
    }
}
