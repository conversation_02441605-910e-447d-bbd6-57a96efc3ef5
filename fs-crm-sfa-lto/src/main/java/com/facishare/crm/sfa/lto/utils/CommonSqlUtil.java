package com.facishare.crm.sfa.lto.utils;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.IGroupByParameter;
import com.facishare.paas.metadata.api.service.ICommonSqlService;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.*;
import com.facishare.paas.metadata.service.impl.CommonSqlServiceImpl;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
public class CommonSqlUtil {
    private static final ICommonSqlService commonSqlService = SpringUtil.getContext().getBean(CommonSqlServiceImpl.class);
    private static final IObjectDataService objectDataService = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);

    private CommonSqlUtil() {
        throw new IllegalStateException("Utility class");
    }

    public static void addWhereParam(List<WhereParam> whereParamList, String columnName, CommonSqlOperator op, List<Object> values){
        WhereParam whereParam = getWhereParam(columnName, op, values);
        whereParamList.add(whereParam);
    }

    public static WhereParam getWhereParam(String columnName, CommonSqlOperator op, List<Object> values) {
        WhereParam whereParam = new WhereParam();
        whereParam.setColumn(columnName);
        whereParam.setOperator(op);
        whereParam.setValue(values);
        return  whereParam;
    }

    public static List<Map> queryData(String tenantId, String tableName, List<WhereParam> whereParams) throws MetadataServiceException {
        return commonSqlService.select(tableName, whereParams, ActionContextUtil.createActionContext(tenantId));
    }

    public static List<Map> queryData(CommonSqlSearchTemplate sqlSearchTemplate, String tenantId) throws MetadataServiceException {
        return commonSqlService.select(sqlSearchTemplate, ActionContextUtil.createActionContext(tenantId));
    }

    public static int insertData(String tenantId, String tableName, List<Map<String, Object>> dataList) throws MetadataServiceException {
        return commonSqlService.insert(tableName, dataList, ActionContextUtil.createActionContext(tenantId));
    }

    public static int updateData(String tenantId, String tableName, Map<String, Object> dataMap, List<WhereParam> whereParams) throws MetadataServiceException {
        return commonSqlService.update(tableName, dataMap, whereParams, ActionContextUtil.createActionContext(tenantId));
    }

    public static int batchUpdate(String tenantId, String tableName, List<Map<String, Object>> dataMaps, List<String> primaryKeyList) throws MetadataServiceException {
        return commonSqlService.batchUpdate(tableName, dataMaps, primaryKeyList, ActionContextUtil.createActionContext(tenantId));
    }

    public static int deleteData(String tenantId, String tableName, List<WhereParam> whereParams) throws MetadataServiceException {
        return commonSqlService.delete(tableName, whereParams, ActionContextUtil.createActionContext(tenantId));
    }

    public static void batchDeleteData(String tenantId, String tableName, List<Map<String, Object>> columnAndValueMapList, List<String> primaryKeyList) throws MetadataServiceException {
        if (CollectionUtils.empty(columnAndValueMapList)) {
            return;
        }
        commonSqlService.batchDelete(tableName, columnAndValueMapList, primaryKeyList, ActionContextUtil.createActionContext(tenantId));
    }

    public static List<IObjectData> getGroupByResult(String tenantId, String apiName, IGroupByParameter groupByParameter, List<IFilter> filters, boolean forceESSearch) {
        try {
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            searchTemplateQuery.setFilters(filters);
            searchTemplateQuery.setGroupByParameter(groupByParameter);
            searchTemplateQuery.setLimit(1000);
            IActionContext actionContext = ActionContextUtil.createActionContext(tenantId, forceESSearch);
            return objectDataService.aggregateFindBySearchQuery(actionContext, searchTemplateQuery, apiName);
        } catch (Exception ex) {
            log.error("getGroupByResult error", ex);
        }
        return Lists.newArrayList();
    }

    public static Map<String, Integer> getGroupByResult(String tenantId, String apiName, String groupByFieldName, List<IFilter> filters, boolean forceESSearch) {
        Map<String, Integer> result = Maps.newHashMap();
        if(StringUtils.isBlank(groupByFieldName)) {
            return result;
        }
        try {
            List<String> dataIds = Lists.newArrayList();
            Optional<IFilter> dataIdsFilter = filters.stream().filter(x -> x.getFieldName().equals(groupByFieldName)).findFirst();
            if(dataIdsFilter.isPresent()) {
                dataIds.addAll(dataIdsFilter.get().getFieldValues());
                dataIds.forEach(x -> result.put(x, 0));
            }
            GroupByParameter groupByParameter = new GroupByParameter();
            groupByParameter.setGroupBy(Lists.newArrayList(groupByFieldName));
            List<IObjectData> groupResult = getGroupByResult(tenantId, apiName, groupByParameter, filters, forceESSearch);
            if(CollectionUtils.empty(groupResult)){
                return result;
            }
            for(IObjectData objectData : groupResult){
                result.put(objectData.get(groupByFieldName).toString(), (Integer) objectData.get("groupbycount")) ;
            }
            return  result;
        } catch (Exception ex) {
            log.error("getGroupByResult error", ex);
        }
        return result;
    }

    public static List<Map> findBySql (String tenantId, String sql) {
        try {
            return objectDataService.findBySql(tenantId, sql);
        } catch (Exception e) {
            log.error("findBySql error", e);
        }
        return Lists.newArrayList();
    }
}