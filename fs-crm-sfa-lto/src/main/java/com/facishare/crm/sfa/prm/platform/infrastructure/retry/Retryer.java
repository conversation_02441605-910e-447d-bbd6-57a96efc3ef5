package com.facishare.crm.sfa.prm.platform.infrastructure.retry;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-05-26
 * ============================================================
 */
public class Retryer<T> {
    private int maxAttempts = 3;
    private long delayMillis = 1000;
    private Function<Integer, Long> backoffStrategy = attempt -> delayMillis;
    private Set<Class<? extends Throwable>> retryOnExceptions = new HashSet<>();
    private Consumer<Throwable> onRetryLogic = e -> {
    };
    private Callable<T> supplier;

    public Retryer<T> maxAttempts(int attempts) {
        this.maxAttempts = attempts;
        return this;
    }

    public Retryer<T> delay(long millis) {
        this.delayMillis = millis;
        return this;
    }

    public Retryer<T> backoff(Function<Integer, Long> strategy) {
        this.backoffStrategy = strategy;
        return this;
    }

    @SafeVarargs
    public final Retryer<T> retryOn(Class<? extends Throwable>... exceptions) {
        retryOnExceptions.addAll(Arrays.asList(exceptions));
        return this;
    }

    public Retryer<T> onRetry(Consumer<Throwable> logic) {
        this.onRetryLogic = logic;
        return this;
    }

    public Retryer<T> execute(Callable<T> supplier) {
        this.supplier = supplier;
        return this;
    }

    public T get() {
        int attempt = 0;
        while (true) {
            try {
                return supplier.call();
            } catch (Throwable e) {
                attempt++;
                if (!shouldRetry(e) || attempt >= maxAttempts) {
                    throw e instanceof RuntimeException ? (RuntimeException) e : new RuntimeException(e);
                }
                onRetryLogic.accept(e);
                sleep(backoffStrategy.apply(attempt));
            }
        }
    }

    private boolean shouldRetry(Throwable e) {
        return retryOnExceptions.isEmpty() || retryOnExceptions.stream().anyMatch(c -> c.isInstance(e));
    }

    private void sleep(long millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException ignored) {
            Thread.currentThread().interrupt();
        }
    }
}
