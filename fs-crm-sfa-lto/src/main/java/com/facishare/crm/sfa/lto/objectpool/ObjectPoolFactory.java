package com.facishare.crm.sfa.lto.objectpool;

import com.facishare.crm.openapi.Utils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

@Component
public class ObjectPoolFactory {
    private static final Map<String, IObjectPoolService> POOL_SERVICE = new HashMap<>();

    @Autowired
    AccountPoolServiceImpl accountPoolService;

    @Autowired
    LeadsPoolServiceImpl leadsPoolService;

    @PostConstruct
    public void init(){
        POOL_SERVICE.put(Utils.ACCOUNT_API_NAME, accountPoolService);
        POOL_SERVICE.put(Utils.LEADS_API_NAME, leadsPoolService);
    }

    public IObjectPoolService getObjectPoolService(String apiName){
        return POOL_SERVICE.get(apiName);
    }
}
