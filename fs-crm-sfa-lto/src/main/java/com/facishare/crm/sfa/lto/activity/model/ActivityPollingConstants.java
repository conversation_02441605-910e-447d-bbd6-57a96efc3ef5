package com.facishare.crm.sfa.lto.activity.model;

import lombok.Builder;
import lombok.Data;
import lombok.ToString;

public interface ActivityPollingConstants {

    enum Function {
        ACTIVITY("activity");

        private final String value;

        // 构造方法
        Function(String value) {
            this.value = value;
        }

        // 获取枚举值
        public String getValue() {
            return value;
        }
    }

    enum Primary {
        // 互动话题
        INTERACTIVE_ISSUES("interactive_issues"),

        // 建议话题
        SUGGEST_ISSUES("suggest_issues"),

        // 互动纪要
        INTERACTIVE_SUMMARY("interactive_summary"),

        // 互动助手
        INTERACTIVE_HELPER("interactive_helper"),

        // 情感分析
        SENTIMENT_ANALYSIS("sentiment_analysis"),

        // 待办
        TO_DO("todo"),

        // 新业务信息
        NEW_BUSINESS_INFO("new_business_info");

        private final String value;

        // 构造方法
        Primary(String value) {
            this.value = value;
        }

        // 获取枚举值
        public String getValue() {
            return value;
        }

    }


    enum Action{
        REFRESH_ALL("refresh_all"),
        REFRESH_ITEM("refresh_item");

        private final String value;

        // 构造函数
        Action(String value) {
            this.value = value;
        }

        // 获取枚举值对应的字符串
        public String getValue() {
            return value;
        }
    }

    @Builder
    @Data
    @ToString
    class PollingMessageData {
        // 功能模块，表示系统中的某个功能部分
        private ActivityPollingConstants.Function functionModule;

        // 一级模块，表示系统中的最高层级模块
        private ActivityPollingConstants.Primary primaryModule;

        // 动作，表示某个特定的操作或行为
        private ActivityPollingConstants.Action action;

        // 补充，表示附加的或补充的内容
        private String supplement;

        // 租户ID
        private String tenantId;

        // 用户ID
        private String userId;
    }
}
