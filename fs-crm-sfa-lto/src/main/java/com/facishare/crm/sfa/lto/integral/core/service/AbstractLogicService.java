package com.facishare.crm.sfa.lto.integral.core.service;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.action.IActionContext;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

@Slf4j
public abstract class AbstractLogicService {
    private static final String tmpLang = "zh-CN";

    protected IActionContext getActionContext() {
        RequestContext requestContext = RequestContextManager.getContext();
        Lang lang = Objects.isNull(requestContext) ? Lang.of(tmpLang)
                : RequestContextManager.getContext().getLang();
        //判断是否直接从i18n平台获取名称
        IActionContext actionContext = new ActionContext();
        actionContext.setLang(lang.getValue());
        actionContext.put("batch", true);
        return actionContext;
    }

}
