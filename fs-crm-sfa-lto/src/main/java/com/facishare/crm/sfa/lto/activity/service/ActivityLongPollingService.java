package com.facishare.crm.sfa.lto.activity.service;

import com.facishare.crm.sfa.lto.activity.model.ActivityPollingConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.metadata.support.GDSHandler;
import com.facishare.polling.api.arg.UpdatePollingDataArg;
import com.facishare.polling.api.enums.PollingOSType;
import com.facishare.polling.api.util.PollingMessageProducer;
import com.facishare.polling.api.util.RangeBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ActivityLongPollingService {
    @Autowired
    private GDSHandler gdsHandler;
    @Autowired
    private PollingMessageProducer realTimePollingMessageProducer;


    public void sendPollingMessage(ActivityPollingConstants.PollingMessageData data) {
        log.info("sendPollingMessage data:{}", data);
        validateData(data);
        String key = getKey(data);
        UpdatePollingDataArg message = getMessage(key, data);
        realTimePollingMessageProducer.sendMessage(message);
    }

    private void validateData(ActivityPollingConstants.PollingMessageData data) {
        if (StringUtils.isAnyBlank(data.getTenantId(), data.getUserId())) {
            throw new ValidateException(I18N.text("sfa.activity.polling.param.error"));
        }
    }

    private UpdatePollingDataArg getMessage(String key, ActivityPollingConstants.PollingMessageData data) {
        UpdatePollingDataArg pollingDataArg = new UpdatePollingDataArg();
        pollingDataArg.setKey(key);
        pollingDataArg.setVersion(System.currentTimeMillis());
        pollingDataArg.setOsType(PollingOSType.WEB);
        String ea = gdsHandler.getEAByEI(data.getTenantId());
        pollingDataArg.setRange(RangeBuilder.buildEmployeeRange(ea, Integer.parseInt(data.getUserId())));
        pollingDataArg.setRealTime(true);
        return pollingDataArg;
    }

    private String getKey(ActivityPollingConstants.PollingMessageData data) {
        StringBuilder sb = new StringBuilder();
        sb.append(data.getFunctionModule().getValue()).append("_")
                .append(data.getPrimaryModule().getValue()).append("_")
                .append(data.getAction().getValue());
        //补充内容不为空时，加入补充内容
        if (StringUtils.isNotBlank(data.getSupplement())) {
            sb.append("_").append(data.getSupplement());
        }
        return sb.toString();
    }



}
