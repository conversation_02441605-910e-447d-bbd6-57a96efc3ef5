package com.facishare.crm.sfa.lto.operations;

import lombok.Getter;

import java.util.Optional;

@Getter
public enum OperationsStrategyBizStatus {
    ENABLE("1", "启用"),
    DISABLE("0", "停用"),
    ;

    private final String value;
    private final String name;

    OperationsStrategyBizStatus(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static Optional<OperationsStrategyBizStatus> from(String value) {
        for (OperationsStrategyBizStatus status : values()) {
            if (status.value.equals(value)) {
                return Optional.of(status);
            }
        }
        return Optional.empty();
    }
}
