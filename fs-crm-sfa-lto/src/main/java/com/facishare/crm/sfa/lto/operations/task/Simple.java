package com.facishare.crm.sfa.lto.operations.task;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.SelectManyExt;
import com.facishare.paas.appframework.metadata.SelectOneExt;
import com.facishare.paas.metadata.api.describe.Date;
import com.facishare.paas.metadata.api.describe.IFieldType;
import org.apache.commons.text.StringSubstitutor;

import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

class Simple implements TemplateVariableField {
    private final String raw;
    private final Set<String> variable;
    private final BiConsumer<OperationsTask, String> setter;

    Simple(String raw, BiConsumer<OperationsTask, String> setter) {
        this.raw = raw;
        this.variable = TemplateVariableField.getVariable(raw);
        this.setter = setter;
    }

    @Override
    public String raw() {
        return raw;
    }

    @Override
    public Set<String> variable() {
        return variable;
    }

    @Override
    public Object replace(Map<String, Value> valueMap) {
        Map<String, Object> temp = valueMap.entrySet().stream().collect(HashMap::new, (map, entry) -> map.put(entry.getKey(), convert(entry)), HashMap::putAll);
        String replaced = StringSubstitutor.replace(raw, temp);
        return TemplateVariableField.replaceVariableRegex(replaced, "");
    }

    @Override
    @SuppressWarnings("unchecked")
    public BiConsumer<OperationsTask, String> setter() {
        return setter;
    }

    @SuppressWarnings("unchecked")
    private Object convert(Map.Entry<String, Value> entry) {
        Value value = entry.getValue();
        if (value == null) {
            return null;
        }
        FieldDescribeExt ext = FieldDescribeExt.of(value.metadata);
        String type = ext.getType();
        switch (type) {
            case IFieldType.DEPARTMENT_MANY:
            case IFieldType.EMPLOYEE_MANY:
            case IFieldType.DEPARTMENT:
            case IFieldType.EMPLOYEE:
                return formatL(value, ext.getFieldExtendName());
            case IFieldType.SELECT_MANY:
                List<String> labels = SelectManyExt.of(ext.getFieldDescribe()).getLabelsByValues((List<String>) value.data.get(ext.getApiName()));
                return String.join(",", labels);
            case IFieldType.SELECT_ONE:
                return SelectOneExt.of(ext.getFieldDescribe()).getLabelByValue(value.data.get(ext.getApiName(), String.class));
            case IFieldType.DATE:
            case IFieldType.DATE_TIME:
                return format(value.data.get(ext.getApiName(), Long.class), ext.getFieldDescribe());
            default: {
                return format(value, ext.getFieldExtendName());
            }
        }
    }

    private static Object formatL(Value value, String fieldExt) {
        Object extValue = value.data.get(fieldExt);
        if (extValue instanceof List) {
            List<?> list = (List<?>) extValue;
            Object index0 = list.get(0);
            if (index0 instanceof Map) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> mapList = (List<Map<String, Object>>) extValue;
                return mapList.stream().map(e -> String.valueOf(findName(e))).collect(Collectors.joining(","));
            } else {
                return formatL(list, index0.getClass());
            }
        }
        return value.raw();
    }

    private static Object findName(Map<String, Object> e) {
        return e.containsKey("name") ? e.get("name") : e.get("deptName");
    }

    private static String formatL(List<?> list, Class<?> clazz) {
        StringJoiner joiner = new StringJoiner(",");
        try {
            PropertyDescriptor[] array = Introspector.getBeanInfo(clazz).getPropertyDescriptors();
            Optional<PropertyDescriptor> op = Arrays.stream(array).filter(descriptor -> "name".equals(descriptor.getName())).findFirst();
            if (op.isPresent()) {
                Method getter = op.get().getReadMethod();
                for (Object obj : list) {
                    joiner.add(String.valueOf(getter.invoke(obj)));
                }
            }
        } catch (Exception ignore) {
            // ignore
        }
        return joiner.toString();
    }

    private static String format(Long epochMilli, Date date) {
        String dateFormat = date.getDateFormat();
        if (dateFormat == null || dateFormat.isEmpty()) {
            dateFormat = "yyyy-MM-dd";
        }
        ZoneId zoneId;
        String timeZone = date.getTimeZone();
        if (timeZone == null || timeZone.isEmpty()) {
            zoneId = Optional.ofNullable(RequestContextManager.getContext()).map(RequestContext::getTimeZone).orElse(ZoneId.systemDefault());
        } else {
            zoneId = ZoneId.of(timeZone);
        }
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(epochMilli), zoneId);
        return DateTimeFormatter.ofPattern(dateFormat).format(localDateTime);
    }

    private static Object format(Value value, String fieldExt) {
        Object extendValue = value.data.get(fieldExt);
        if (extendValue != null) {
            return extendValue;
        }
        return value.raw();
    }
}