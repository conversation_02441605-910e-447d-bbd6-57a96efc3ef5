package com.facishare.crm.sfa.lto.accountreerelation.models;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

public interface AccountTreeRelationModels {
    @Data
    class CreateAccountTreeRelationArg {
        private String companyName;
        private Boolean rootNode;
        //关系树关联的对象
        private String describeApiName = "AccountMainDataObj";
    }

    @Data
    @Builder
    class CreateAccountTreeRelationResult {
        private String errorCode;
        private String message;
    }

    @Data
    @Builder
    class CheckCreateAccountTreeRelationResult {
        private String errorCode;
        private String message;
        private Integer status;
        private String linkId;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class RelateMainData2TreeRelationArg {
        private List<String> objectIds;
        private String describeApiName;
    }

    @Data
    class RelationshipsMapping {
        //关系树对象名称
        private String linkDescribeApiName;
        //关系树Log对象名称
        private String linkDescribeLogApiName;
        //表名称
        private String storeTableName;
        //关联字段
        private String linkFieldApiName;
        //license
        private String licenseName;
        //配额限制
        private String licenseCountLimit;
    }
    @Data
    class CommonParam{
        //关系树对象名称
        private String tenantId;
        private Map<String, String> dataMap;
        private List<String> nameList;
    }
    @Data
    class EnterpriseMatch{
        private String whereString;
        private List<EnterpriseMatchMapping> mappings;
    }
    @Data
    class EnterpriseMatchMapping{
        private String originalField;
        private String targetField;
    }


}
