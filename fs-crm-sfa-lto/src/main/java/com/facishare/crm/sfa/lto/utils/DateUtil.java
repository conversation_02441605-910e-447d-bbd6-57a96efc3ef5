package com.facishare.crm.sfa.lto.utils;

import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import org.apache.rocketmq.common.UtilAll;
import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.Calendar;
import java.util.Date;

@Slf4j
public class DateUtil {
    private static final String yyyyMMdd = "yyyy-MM-dd";
    private DateUtil() {
        throw new IllegalStateException("Utility class");
    }

    public static Long getTimeMillis(Long time) {
        Date date = new Date(time);
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String dateString = format.format(date);
        try {
            date = format.parse(dateString);
        } catch (Exception e) {
            log.error("getTimeMillis error", e);
        }
        return date.getTime();
    }

    public static  Long getOneDayTimeStamp() {
        return  24 * 60 * 60 * 1000L;
    }

    public static Long getTimeMillisWithHourTime(String hourTime) {
        Date date = getDateWithHourTime(hourTime);
        return date.getTime();
    }

    public static Date getDateWithHourTime(String hourTime) {
        Date date = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String dateString = format.format(date);
        dateString = String.format("%s %s", dateString, hourTime);
        SimpleDateFormat longDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            date = longDateFormat.parse(dateString);
        } catch (Exception e) {
            log.error("getTimeStamp error", e);
        }
        return date;
    }

    public static String getCurrentTime(){
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
    }

    public static String getDateString(long time){
       return com.facishare.common.DateUtil.formatDate(new Date(time),"yyyy-MM-dd");
    }

    /**
     * 获取昨天的日期
     * @return
     */
    public static String getYesterday(){
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Calendar   cal   =   Calendar.getInstance();
        cal.add(Calendar.DATE,   -1);
        return format.format(cal.getTime());
    }
    /**
     * 获取明天的日期
     * @return
     */
    public static String getTomorrow(){
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Calendar   cal   =   Calendar.getInstance();
        cal.add(Calendar.DATE,   1);
        return format.format(cal.getTime());
    }

    /**
     * 获取前N天、前N周、前N月的日期
     * @return
     */
    public static Date getDateByTheLastNOfDay(Date yesterday,String type,String nums) {
        Integer num = Integer.parseInt(nums);
        Calendar c = Calendar.getInstance();
        c.setTime(yesterday);
        if ("day".equals(type)) {
            c.add(Calendar.DATE, -num);
            return c.getTime();
        } else if ("Week".equals(type)) {
            c.add(Calendar.DATE, -(num * 7));
            return c.getTime();
        } else if ("Month".equals(type)) {
            c.add(Calendar.MONTH, -num);
            return c.getTime();
        } else {
            return yesterday;
        }
    }

    /**
     * 获取后N天的日期
     * @param tomorrow
     * @param type
     * @param nums
     * @return
     */
    public static Date getDateByNdaysAfterObtaining(Date tomorrow,String type,String nums) {
        Integer num = Integer.parseInt(nums);
        Calendar c = Calendar.getInstance();
        c.setTime(tomorrow);
        if ("day".equals(type)) {
            c.add(Calendar.DATE, num);
            return c.getTime();
        } else if ("Week".equals(type)) {
            c.add(Calendar.DATE, (num * 7));
            return c.getTime();
        } else if ("Month".equals(type)) {
            c.add(Calendar.MONTH, num);
            return c.getTime();
        } else {
            return tomorrow;
        }
    }

    /**
     * 计算两天相差的天数
     * @param date1
     * @param date2
     * @return
     */
    public static Integer calculateTheNumberOfDaysBetweenTwoDates(Date date1,Date date2){
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);

        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        int day1= cal1.get(Calendar.DAY_OF_YEAR);
        int day2 = cal2.get(Calendar.DAY_OF_YEAR);

        int year1 = cal1.get(Calendar.YEAR);
        int year2 = cal2.get(Calendar.YEAR);
        if(year1 != year2) //同一年
        {
            int timeDistance = 0 ;
            for(int i = year1 ; i < year2 ; i ++)
            {
                if(i%4==0 && i%100!=0 || i%400==0) //闰年
                {
                    timeDistance += 366;
                }
                else //不是闰年
                {
                    timeDistance += 365;
                }
            }

            return timeDistance + (day2-day1) ;
        }
        else //不同年
        {
            return day2-day1;
        }
    }


    public static Date getDateByStr(){
        SimpleDateFormat ft=new SimpleDateFormat("yyyy-MM-dd");
        try {
            return ft.parse(getCurrentTime());
        } catch (ParseException e) {
        }
        return null;
    }

    /**
     * 将long类型转化为Date
     *
     * @param timeMillis
     * @return
     */
    public static Date timeMillisToDate(long timeMillis) {
        return new Date(timeMillis);
    }

    /**
     * 将Date类型转化为long
     *
     * @param date
     * @return
     */
    public static long dateToTimeMillis(Date date) {
        return date.getTime();
    }

    /**
     * 将"2022-12-21 18:08:06"型字符串转化为Date
     *
     * @param str
     * @return
     * @throws ParseException
     */
    public static Date formatStrToDate(String str){
        try {
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = (Date) formatter.parse(str);
            return date;
        } catch (ParseException e) {
            throw new MetaDataBusinessException(e.getMessage());
        }
    }

    public static String getTimeByTimeStamp(long millisecond){
        Date d = new Date();
        long time = d.getTime()-(millisecond*1000);
        d.setTime(time);
        SimpleDateFormat sbf = new SimpleDateFormat(UtilAll.YYYY_MM_DD_HH_MM_SS);
        return sbf.format(d);
    }

    public static String getDataTimeStrByTimeStamp(long timeStamp){
        Date d = new Date();
        d.setTime(timeStamp);
        SimpleDateFormat sbf = new SimpleDateFormat(UtilAll.YYYY_MM_DD_HH_MM_SS);
        return sbf.format(d);
    }
    public static String getDataStrByTimeStamp(long timeStamp){
        Date d = new Date();
        d.setTime(timeStamp);
        SimpleDateFormat sbf = new SimpleDateFormat(yyyyMMdd);
        return sbf.format(d);
    }

    /**
     * 当天0点的时间戳
     * @return
     */
    public static Long getCurrentDateTimeStamp() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime().getTime();
    }

    /**
     * 获取当天的剩余时间，返回毫秒
     * @return
     */
    public static long getRemainingTimeOfDay(){
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        // 获取今日结束的时间
        LocalDateTime todayEnd = now.withHour(23).withMinute(59).withSecond(59);
        // 计算剩余时间
        Duration duration = Duration.between(now, todayEnd);
        // 输出剩余时间
        return duration.toMillis();
    }

    /**
     * 获取当月的剩余时间，返回毫秒
     * @return
     */
    public static long getRemainingTimeOfMonth(){
        // 获取当前日期和时间
        LocalDateTime now = LocalDateTime.now();
        // 获取当前日期
        LocalDate currentDate = now.toLocalDate();
        // 获取当月的最后一天
        LocalDate lastDayOfMonth = currentDate.with(TemporalAdjusters.lastDayOfMonth());
        // 计算从当前日期到月底的 duration
        Duration durationToMonthEnd = Duration.between(now, lastDayOfMonth.atTime(23, 59, 59, 999_999_999)); // 确保是月底的最后一刻
        // 获取剩余毫秒数
        return durationToMonthEnd.toMillis();
    }


    public static Date getDateNow() {
        try {
            SimpleDateFormat format = new SimpleDateFormat(UtilAll.YYYY_MM_DD_HH_MM_SS);
            String time = format.format(System.currentTimeMillis());
            Date now = format.parse(time);
            return now;
        } catch (ParseException e) {
            throw new MetaDataBusinessException(e.getMessage());
        }
    }
}
