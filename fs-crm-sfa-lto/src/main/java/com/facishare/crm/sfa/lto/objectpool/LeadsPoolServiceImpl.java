package com.facishare.crm.sfa.lto.objectpool;

import com.facishare.crm.openapi.Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class LeadsPoolServiceImpl extends BaseObjectPoolService {
    @Override
    public String getObjectPoolApiName() {
        return Utils.LEADS_POOL_API_NAME;
    }

    @Override
    public String getObjectApiName() {
        return Utils.LEADS_API_NAME;
    }
}
