package com.facishare.crm.sfa.lto.common.models;

public final class LtoFieldApiConstants {
    public static final String ACCOUNT_ID = "account_id";
    public static final String OBJECT_API_NAME = "object_api_name";
    public static final String DATA_ID = "data_id";
    public static final String TENANT_ID = "tenant_id";
    public static final String OWNER = "owner";
    public static final String OUT_OWNER = "out_owner";
    public static final String OUT_TENANT_ID = "out_tenant_id";
    public static final String BIZ_STATUS = "biz_status";
    public static final String LIFE_STATUS = "life_status";
    public static final String OBJECT_DESCRIBE_API_NAME = "object_describe_api_name";
    public static final String LEADS_ID = "leads_id";
    public static final String STATUS_NORMAL = "normal";
    public static final String ALL_COMPANY_ID = "999999";
    public static final String FAILED = "failed";
    public static final String SUCCESS = "success";
    public static final String ID = "id";
    public static final String OBJECT_ID = "object_id";
    public static final String CREATED_BY = "created_by";
    public static final String CREATE_TIME = "create_time";
    public static final String IS_DELETED = "is_deleted";
    public static final String LAST_MODIFIED_BY = "last_modified_by";
    public static final String LAST_MODIFIED_TIME = "last_modified_time";
    public static final String DATA_OWN_DEPARTMENT = "data_own_department";
    public static final String PARTNER_ID = "partner_id";
    public static final String NAME = "name";
    public static final String API_NAME = "api_name";
    public static final String POOL_ID = "pool_id";

    private LtoFieldApiConstants() {
        throw new IllegalStateException("Utility class");
    }
}
