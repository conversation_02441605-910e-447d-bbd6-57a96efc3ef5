package com.facishare.crm.sfa.lto.relationship.service;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.lto.common.LtoRateLimiterService;
import com.facishare.crm.sfa.lto.enums.LeadsBizStatusEnum;
import com.facishare.crm.sfa.lto.relationship.EnumUtil;
import com.facishare.crm.sfa.lto.relationship.models.Member;
import com.facishare.crm.sfa.lto.relationship.models.RelationshipModels;
import com.facishare.crm.sfa.lto.rest.DuplicatedRestServiceProxy;
import com.facishare.crm.sfa.lto.rest.models.DuplicatedModel;
import com.facishare.crm.sfa.lto.utils.*;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.data.IDuplicatedSearch;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.fxiaoke.common.SqlEscaper;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> lik
 * @date : 2022/7/11 17:04
 */
@Component
@Slf4j
public class RelationshiService {

    private static final List<String> relationshiTypeList = Lists.newArrayList("0","1");

    private static final Map<String,List<String>> objectApiNameMap = new HashMap<>();

    private static String relationshiServiceSkipTenantId = "";

    private static final String LeadsObj = "LeadsObj";
    private static final String AccountObj = "AccountObj";
    private static final String ContactObj = "ContactObj";
    static {
        ConfigFactory.getConfig("fs-crm-sfa_rfm-config", config -> {
            //todo 删除 无用了 11-29修改全网之后删除
            relationshiServiceSkipTenantId = config.get("relationshi_service_skip_tenant_id", "");
            String objectApiNameList = config.get("objectApiNameList", "");
            objectApiNameMap.clear();
            for(int i=0;i<objectApiNameList.split(";").length;i++){
                String str = objectApiNameList.split(";")[i];
                String fieldStr = str.split(":")[1];
                List<String> fieldList = Arrays.asList(fieldStr.split(","));
                objectApiNameMap.put( str.split(":")[0],fieldList);
            }
        });
    }


    private static final String ACCOUNT_ID = "account_id";
    private static final String OBJECT_API_NAME = "object_api_name";
    private static final String DATA_ID = "data_id";
    private static final String TENANT_ID = "tenant_id";

    private static final Integer limit = 200;

    private static final String RESULTS ="results";

    private static final Integer leadsDuplicateSearchSize = 100;

    private static final String COMPANY = "company";

    private static final String NAME = "name";

    private static final String _ID = "_id";
    private static final String ID = "id";
    private static final String BIZ_STATUS = "biz_status";

    private static final String MEMBER_ID = "member_id";
    private static final String ROLE_TYPE = "role_type";
    private static final String MEMBER_TYPE = "member_type";
    private static final String OPERATION_DETAILSC = "%s:%s:%s";
    private static final String CONTACT_MEMBER_RELATIONSHIP_FUNC_SETTING = "contact_member_relationship_func_setting";
    private static final String CONTACT_MEMBER_RELATIONSHI_FUNC_ADDRESS_BOOK_SETTING = "contact_member_relationship_func_addressBook_setting";
    @Autowired
    private ServiceFacade serviceFacade;

    @Autowired
    DuplicatedRestServiceProxy duplicatedRestServiceProxy;

    @Autowired
    private ObjectDataServiceImpl objectDataService;
    @Autowired
    private IObjectDescribeService objectDescribeService;
    @Autowired
    ConfigService configService;

    @Autowired
    private LtoRateLimiterService rateLimiterService;


    /**
     * 处理联系人关系
     * @param arg
     */
    public void dealDataRelationshipByDataId(RelationshipModels.TaskArg arg){
        if(GrayUtil.skipRelationshiServiceSkipTenantId(arg.getTenantId())){
            log.warn("relationshiServiceSkipTenantId skip arg.getTenantId{}",arg.getTenantId());
            return;
        }

        try{
            for(String dataId : arg.getDataIds()){
                try{
                    User user = new User(arg.getTenantId(), User.SUPPER_ADMIN_USER_ID);
                    IObjectData iObjectData = getResourceDataById(user,arg.getObjectApiName(),dataId,getFindFieldListOfApiName(arg.getObjectApiName()));
                    String operationDetailsc = String.format(OPERATION_DETAILSC,arg.getObjectApiName(),dataId,EnumUtil.OperationType.operationTypeOf(arg.getOperationType()));
                    dealDataRelationshipByDataId(dataId,iObjectData,user,arg.getObjectApiName(),operationDetailsc);
                }catch (Exception e){
                    log.error("dealContactRelationshi->Exception1 tenantId：{}， e:",arg.getTenantId(),e);
                }
            }
        }catch (Exception e){
            log.error("dealDataRelationshipByDataId tenantId：{}， e:",arg.getTenantId(),e);
        }
    }

    public void dealDataRelationshipByDataId(String dataId,IObjectData iObjectData,User user,String objectApiName,String operationDetailsc){
        log.warn("dealDataRelationshipByDataId dataId:{},operationDetailsc:{}",dataId,operationDetailsc);
        if(ObjectUtils.isEmpty(iObjectData)){
            deleteData(user.getTenantId(),objectApiName,dataId,relationshiTypeList);
            log.warn("dealDataRelationshipByDataId iObjectData is null tenantId:{},apiName:{},dataId:{}",user.getTenantId(),objectApiName,dataId);
            return;
        }
        rateLimiterService.getDealDataRelationshipMainSwitchLimiter().acquire();
        deleteData(user.getTenantId(),objectApiName,iObjectData.getId(),relationshiTypeList);

        if(LeadsObj.equals(iObjectData.getDescribeApiName())){
            dealLeadsRelationshi(iObjectData.getDescribeApiName(),iObjectData,user,operationDetailsc);
        }else if(ContactObj.equals(iObjectData.getDescribeApiName())){
            dealContactRelationshi(iObjectData.getDescribeApiName(),iObjectData, user,operationDetailsc);
        }else if(AccountObj.equals(iObjectData.getDescribeApiName())){
            dealAccountRelationshi(iObjectData.getDescribeApiName(),iObjectData, user,operationDetailsc);
        }
    }

    /**
     * 处理联系人关系
     */
    public void dealAccountRelationshi(String objectApiName,IObjectData iObjectData,User user,String operationDetailsc){
        log.warn("dealAccountRelationshi dataId:{},operationDetailsc:{}",iObjectData.getId(),operationDetailsc);
        //联合查询是否有数据的标识
        Set<String> duplicateSearchIsHaveDataSet = new HashSet<>();
        //客户联合查询线索
        accountDuplicateSearch(user,iObjectData,objectApiName,duplicateSearchIsHaveDataSet,operationDetailsc);

        if(CollectionUtils.isEmpty(duplicateSearchIsHaveDataSet)){
            //联合查重关闭的，或者找不到，启用兜底逻辑 “客户的【客户名称】-> 线索的【企业名称】字段进行识别”
            findLeadsByCompany(user,iObjectData,operationDetailsc);
        }
        //找寻线索、和联系人是否是转换的根据客户id
        dealTransferDataByAccountId(user,iObjectData,operationDetailsc);

        //根据客户查询联系人
        dealContactByAccount(user,iObjectData,operationDetailsc);
    }

    private void dealContactByAccount(User user,IObjectData iObjectData,String operationDetailsc){
        SearchTemplateQuery searchTemplateQuery = buildSTQ();
        searchTemplateQuery.setLimit(1000);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, TENANT_ID, user.getTenantId());
        SearchUtil.fillFilterEq(filters, ACCOUNT_ID, iObjectData.getId());
        searchTemplateQuery.resetFilters(filters);
        QueryResult<IObjectData> queryResult = getQueryResultBySearchQuery(user,ContactObj,searchTemplateQuery, Lists.newArrayList(ID,NAME));
        if(ObjectUtils.isEmpty(queryResult) || CollectionUtils.isEmpty(queryResult.getData())) {
            return;
        }
        List<String> ids = queryResult.getData().stream().map(result->result.getId()).collect(Collectors.toList());

        Map<String,List<Member.roleType>> memberMap = getMemberByObjectApiNameToMap(user.getTenantId(),ContactObj,ids);

        batchDealData(user,ids,iObjectData,memberMap,operationDetailsc,ContactObj);

    }

    /**
     * 转换的数据处理
     * @param user
     * @param iObjectData
     */
    private void dealTransferDataByAccountId(User user,IObjectData iObjectData,String operationDetailsc){
        String sql = String.format("SELECT account_id,leads_id,contact_id from biz_leads_transfer_log where tenant_id='%s' and account_id='%s' AND is_deleted = 0;",user.getTenantId(),iObjectData.getId());
        //根据线索或者联系人查询转换记录表，查询转换的客户id
        List<Map> transferList = getDataListBySql(user.getTenantId(),sql);
        if(CollectionUtils.isEmpty(transferList)){
            return;
        }
        transferList.stream().forEach(map -> {
            try {
                if(ObjectUtils.isNotEmpty(map.get("leads_id"))){
                    //获取所有的成员拍平
                    List<Member.roleType> memberList = getMemberByObjectApiName(user.getTenantId(),LeadsObj,Lists.newArrayList(map.get("leads_id").toString()));
                    IObjectData leadsData = new ObjectData();
                    leadsData.setId(map.get("leads_id").toString());
                    leadsData.setDescribeApiName(LeadsObj);
                    batchDealData(user, Lists.newArrayList(iObjectData.getId()),leadsData,memberList,operationDetailsc);
                }
//                if(ObjectUtils.isNotEmpty(map.get("contact_id"))){
//                    //获取所有的成员拍平
//                    List<Member.roleType> memberList = getMemberByObjectApiName(user.getTenantId(),LeadsObj,Lists.newArrayList(map.get("contact_id").toString()));
//                    IObjectData leadsData = new ObjectData();
//                    leadsData.setId(map.get("contact_id").toString());
//                    leadsData.setDescribeApiName(ContactObj);
//                    batchDealData(user, Lists.newArrayList(iObjectData.getId()),leadsData,memberList,operationDetailsc);
//                }
            }catch (Exception e){
                log.error("dealTransferDataByAccountId e:",e);
            }
        });
    }
    
    /**
     * 处理联系人关系
     */
    public void dealContactRelationshi(String objectApiName,IObjectData iObjectData,User user,String operationDetailsc){
        log.warn("dealContactRelationshi dataId:{},operationDetailsc:{}",iObjectData.getId(),operationDetailsc);
        if(ObjectUtils.isEmpty(iObjectData.get(ACCOUNT_ID))){
            return;
        }
        //查询客户信息
        IObjectData accountData = getResourceDataById(user,AccountObj,iObjectData.get(ACCOUNT_ID).toString(),Lists.newArrayList(_ID));
        if(ObjectUtils.isEmpty(accountData)){
            log.info("dealContactRelationshi accountData is null tenantId:{},apiName:{},dataId:{}",user.getTenantId(),objectApiName,iObjectData.getId());
            return;
        }

        //获取所有的成员拍平
        List<Member.roleType> memberList = getMemberByObjectApiName(user.getTenantId(),objectApiName, Lists.newArrayList(iObjectData.getId()));
        batchDealData(user, Lists.newArrayList(accountData.getId()),iObjectData,memberList,operationDetailsc);
    }

    private IObjectData getResourceDataById(User user, String apiName,String dataId,List<String> findFieldList ){
        try {
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            searchTemplateQuery.setLimit(1);
            searchTemplateQuery.setFindExplicitTotalNum(false);
            searchTemplateQuery.setNeedReturnCountNum(false);
            searchTemplateQuery.setPermissionType(0);
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, TENANT_ID, user.getTenantId());
            SearchUtil.fillFilterEq(filters, _ID, dataId);
            searchTemplateQuery.setFilters(filters);
            QueryResult<IObjectData> queryResult = getQueryResultBySearchQuery(user,apiName,searchTemplateQuery,findFieldList);
            if(queryResult != null && CollectionUtils.isNotEmpty(queryResult.getData())) {
                return queryResult.getData().get(0);
            }
        }catch (Exception e){
            log.error("getResourceDataById e:",e);
        }
        return null;
    }

    private QueryResult<IObjectData> getQueryResultBySearchQuery(User user, String apiName, SearchTemplateQuery searchTemplateQuery, List<String> fieldList) {
        return ObjectDataUtil.findDataBySearchQuery(user, apiName, searchTemplateQuery, fieldList);
    }

    /**
     * 处理线索
     */
    public void dealLeadsRelationshi(String objectApiName,IObjectData iObjectData,User user,String operationDetailsc){
        log.warn("dealLeadsRelationshi dataId:{},operationDetailsc:{}",iObjectData.getId(),operationDetailsc);
        List<Member.roleType> memberList = new ArrayList<>();
        if(ObjectUtils.isNotEmpty(iObjectData.get(COMPANY)) || ObjectUtils.isNotEmpty(iObjectData.get("tel")) || ObjectUtils.isNotEmpty(iObjectData.get("email"))){
            //获取所有的成员拍平
             memberList = getMemberByObjectApiName(user.getTenantId(),objectApiName,Lists.newArrayList(iObjectData.getId()));
            //联合查询是否有数据的标识
            Set<String> duplicateSearchIsHaveDataSet = new HashSet<>();
            //联合查询
            leadsDuplicateSearch(user,iObjectData,objectApiName,duplicateSearchIsHaveDataSet,memberList,operationDetailsc);

            if(CollectionUtils.isEmpty(duplicateSearchIsHaveDataSet)){
                //联合查重关闭的，或者找不到，启用兜底逻辑 “线索的【企业名称】→客户的【客户名称】字段进行识别”
                findAccountByCompany(user,iObjectData,memberList,operationDetailsc);
            }
        }
        //判断线索是否转换
        if(LeadsBizStatusEnum.TRANSFORMED.getValue().equals(iObjectData.get(BIZ_STATUS))){
            //获取所有的成员拍平
            if(CollectionUtils.isEmpty(memberList)){
                memberList = getMemberByObjectApiName(user.getTenantId(),objectApiName,Lists.newArrayList(iObjectData.getId()));
            }
            dealTransferData(user,objectApiName,iObjectData,memberList,operationDetailsc);
        }
    }

    /**
     * 转换的数据处理
     * @param user
     * @param objectApiName
     * @param iObjectData
     * @param memberList
     */
    private void dealTransferData(User user,String objectApiName,IObjectData iObjectData,List<Member.roleType> memberList,String operationDetailsc){
        StringBuilder sb = new StringBuilder();
        sb.append(String.format("SELECT account_id from biz_leads_transfer_log where tenant_id='%s'",user.getTenantId()));
        if(LeadsObj.equals(objectApiName)){
            sb.append(String.format(" AND leads_id = '%s'",iObjectData.getId()));
        }else{
            return;
        }
        sb.append(" AND is_deleted = 0;");
        //根据线索或者联系人查询转换记录表，查询转换的客户id
        List<Map> transferList = getDataListBySql(user.getTenantId(),sb.toString());
        if(CollectionUtils.isEmpty(transferList) || !transferList.get(0).containsKey(ACCOUNT_ID)){
            log.info("dealTransferData is null sb:{}",sb.toString());
            return;
        }
        String accountId = transferList.get(0).get(ACCOUNT_ID).toString();
        //如果存在转换的客户，利用线索或者联系人+客户id查询是否已经存在关系
        List<Map> relationshipList = isExistAccountIdByLeadsIdOrContactId(accountId,user.getTenantId(),objectApiName,iObjectData.getId());
        if(CollectionUtils.isNotEmpty(relationshipList)){
            //存在关系，则不处理
            return;
        }
        batchDealData(user,Lists.newArrayList(accountId),iObjectData,memberList,operationDetailsc);
    }


    /**
     * 联合查重
     * @param user
     * @param iObjectData
     */
    private void leadsDuplicateSearch(User user, IObjectData iObjectData, String objectApiName,Set<String> flag,List<Member.roleType> memberList,String operationDetailsc){
        rateLimiterService.getDealDataRelationshipLimiter().acquire();
        DuplicatedModel.Arg arg = DuplicatedModel.Arg.builder().describe_api_name(objectApiName).is_need_duplicate(true)
                .page_size(leadsDuplicateSearchSize).object_data(ObjectDataDocument.of(iObjectData)).type(IDuplicatedSearch.Type.NEW).build();
        int index = 1;
        while(true){
            arg.setPage_number(index);
            DuplicatedModel.Result result = duplicatedRestServiceProxy.leadsDuplicateSearch(HttpHeaderUtil.getHeaders(user),arg);
            if(result.getErrCode() != 0){
                log.error("dealLeadsRelationshi-->leadsDuplicateSearch is fail tenantId:{},apiName:{},errMessage:{}",user.getTenantId(),objectApiName,result.getErrMessage());
                return ;
            }
            if(CollectionUtils.isEmpty(result.getResult().get(RESULTS))){
                return;
            }
            List<DuplicatedModel.DataResult> resList = result.getResult().get(RESULTS);
            //过滤客户id
            List<String> ids = resList.get(0).getData_list().stream().map(data->data.getId()).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(ids)){
                log.error("leadsDuplicateSearch>>ids is null tenantId:{},result:{}",user.getTenantId(),JSONObject.toJSONString(result).substring(0,500));
                return;
            }
            if(ObjectUtils.isEmpty(flag)){
                flag.add("true");
            }

            batchDealData(user,ids,iObjectData,memberList,operationDetailsc);
            if(ids.size() < leadsDuplicateSearchSize){
                return ;
            }
            index++;
        }
    }

    /**
     * 根据公司名称查询客户
     */
    private void findAccountByCompany(User user, IObjectData iObjectData,List<Member.roleType> memberList,String operationDetailsc){
        if(ObjectUtils.isEmpty(iObjectData.get(COMPANY))){
            return;
        }

        SearchTemplateQuery searchTemplateQuery = buildSTQ();
        searchTemplateQuery.setLimit(1000);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, TENANT_ID, user.getTenantId());
        SearchUtil.fillFilterEq(filters, NAME, iObjectData.get(COMPANY));
        searchTemplateQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = getQueryResultBySearchQuery(user,AccountObj,searchTemplateQuery, Lists.newArrayList(ID,NAME));
        if(ObjectUtils.isEmpty(queryResult) || CollectionUtils.isEmpty(queryResult.getData())) {
            return;
        }
        List<String> ids = queryResult.getData().stream().map(result->result.getId()).collect(Collectors.toList());
        batchDealData(user,ids,iObjectData,memberList,operationDetailsc);
    }


    /**
     * 根据 客户名称 对应 线索公司名称 查询线索
     */
    private void findLeadsByCompany(User user, IObjectData iObjectData,String operationDetailsc){

        SearchTemplateQuery searchTemplateQuery = buildSTQ();
        searchTemplateQuery.setLimit(1000);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, TENANT_ID, user.getTenantId());
        SearchUtil.fillFilterEq(filters, COMPANY, iObjectData.getName());
        searchTemplateQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = getQueryResultBySearchQuery(user,LeadsObj,searchTemplateQuery, Lists.newArrayList(ID,NAME));
        if(ObjectUtils.isEmpty(queryResult) || CollectionUtils.isEmpty(queryResult.getData())) {
            return;
        }
        List<String> ids = queryResult.getData().stream().map(result->result.getId()).collect(Collectors.toList());
        Map<String,List<Member.roleType>> memberMap = getMemberByObjectApiNameToMap(user.getTenantId(),LeadsObj,ids);
        batchDealData(user,ids,iObjectData,memberMap,operationDetailsc,LeadsObj);
    }

    /**
     * 批量处理关系结果
     * @param user
     * @param accountIds
     * @param iObjectData
     * @param memberList
     */
    private void batchDealData(User user,List<String> accountIds,IObjectData iObjectData,List<Member.roleType> memberList,String operationDetailsc){
        //保存关系
        List<IObjectData> insertResultDataList = new ArrayList<>();
        for(String id : accountIds){
            if(CollectionUtils.isNotEmpty(memberList)){
                for(Member.roleType member : memberList){
                    RelationshipModels.Result relationshipResult = RelationshipModels.Result.builder()
                            .accountId(id).objectApiName(iObjectData.getDescribeApiName())
                            .dataId(iObjectData.getId()).member(member.getMemberId())
                            .memberType(member.getMemberType()).build();
                    if(EnumUtil.RelationshipMemberRole.owner.getValue().equals(member.getRoleType())){
                        relationshipResult.setRelationshipType(EnumUtil.RelationshipType.owner.getValue());
                    }else{
                        relationshipResult.setRelationshipType(EnumUtil.RelationshipType.relatedTeam.getValue());
                    }
                    insertResultDataList.add(getRelationshiData(user,relationshipResult,operationDetailsc));
                }
            }else{
                RelationshipModels.Result relationshipResult = RelationshipModels.Result.builder().accountId(id).objectApiName(iObjectData.getDescribeApiName())
                        .dataId(iObjectData.getId()).relationshipType(EnumUtil.RelationshipType.relatedTeam.getValue()).build();
                insertResultDataList.add(getRelationshiData(user,relationshipResult,operationDetailsc));
            }

            if(insertResultDataList.size()>= 100){
                bulkSaveData(user,insertResultDataList);
                insertResultDataList = new ArrayList<>();
            }
        }
        if(CollectionUtils.isNotEmpty(insertResultDataList)){
            bulkSaveData(user,insertResultDataList);
        }
    }

    /**
     * 批量处理关系结果
     * @param user
     * @param dataIds 线索、联系人id
     * @param iObjectData 客户信息
     * @param memberMap 成员、部门成员组
     */
    private void batchDealData(User user,List<String> dataIds,IObjectData iObjectData,Map<String,List<Member.roleType>> memberMap,String operationDetailsc,String describeApiName){
        //保存关系
        List<IObjectData> insertResultDataList = new ArrayList<>();
        for(String id : dataIds){
            if(ObjectUtils.isNotEmpty(memberMap) && memberMap.containsKey(id)){
                List<Member.roleType> memberList =memberMap.get(id);
                for(Member.roleType member : memberList){
                    RelationshipModels.Result relationshipResult = RelationshipModels.Result.builder()
                            .accountId(iObjectData.getId()).objectApiName(describeApiName)
                            .dataId(id).member(member.getMemberId())
                            .memberType(member.getMemberType()).build();
                    if(EnumUtil.RelationshipMemberRole.owner.getValue().equals(member.getRoleType())){
                        relationshipResult.setRelationshipType(EnumUtil.RelationshipType.owner.getValue());
                    }else{
                        relationshipResult.setRelationshipType(EnumUtil.RelationshipType.relatedTeam.getValue());
                    }
                    insertResultDataList.add(getRelationshiData(user,relationshipResult,operationDetailsc));
                }
            }else{
                RelationshipModels.Result relationshipResult = RelationshipModels.Result.builder().accountId(iObjectData.getId()).objectApiName(describeApiName)
                        .dataId(id).relationshipType(EnumUtil.RelationshipType.relatedTeam.getValue()).build();
                insertResultDataList.add(getRelationshiData(user,relationshipResult,operationDetailsc));
            }

            if(insertResultDataList.size()>= 100){
                bulkSaveData(user,insertResultDataList);
                insertResultDataList = new ArrayList<>();
            }
        }
        if(CollectionUtils.isNotEmpty(insertResultDataList)){
            bulkSaveData(user,insertResultDataList);
        }
    }



    private IObjectData getRelationshiData(User user, RelationshipModels.Result result,String operationDetailsc) {
        IObjectData objectData = new ObjectData();
        objectData.set("tenant_id", user.getTenantId());
        objectData.set("name", ObjectDataUtil.getNameCode());
        objectData.set("_id", serviceFacade.generateId());
        objectData.set("account_id", result.getAccountId());
        objectData.set("object_api_name",result.getObjectApiName());
        objectData.set("data_id", result.getDataId());
        if(ObjectUtils.isNotEmpty(result.getMember())){
            objectData.set("member", result.getMember());
        }
        objectData.set("relationship_type", result.getRelationshipType());
        objectData.set("member_type", result.getMemberType());
        objectData.set("operation_details", operationDetailsc);
        objectData.set("object_describe_api_name", "ContactMemberRelationshipObj");
        objectData.set("is_deleted", false);
        return objectData;
    }

    private void bulkSaveData(User user, List<IObjectData> dataList) {
        if(CollectionUtils.isEmpty(dataList)) {
            return;
        }
        serviceFacade.bulkSaveObjectData(dataList, user, true, true, x -> {
            return ActionContextExt.of(user)
                    .setNotValidate(true)
                    .getContext();
        });
    }

    /**
     * 删除关系---通过通讯录创建的关系
     */
    private void deleteRelationshiByMemberAndType(String tenantId,String member){
       int index =0;
        String sql =  String.format("SELECT id from biz_contact_member_relationship where tenant_id = '%s' and member ='%s' and relationship_type = '3'",tenantId,member);
        try {
            while(true){

                List<Map> queryIdResult = objectDataService.findBySql(tenantId, sql);
                if(CollectionUtils.isEmpty(queryIdResult)){
                    break;
                }
                List<String> ids = queryIdResult.stream().map(x->x.get("id").toString()).collect(Collectors.toList());
                String deleteSql = String.format("delete from biz_contact_member_relationship WHERE tenant_id ='%s' and id = %s ;",tenantId, SqlEscaper.any_clause(ids));
                objectDataService.deleteBySql(tenantId,deleteSql);
                if(ids.size()<leadsDuplicateSearchSize){
                    break;
                }
                Thread.sleep(100);
                index ++;
                if(index>10000000){
                    return;
                }
            }
        }catch (InterruptedException e){
            log.error("deleteData InterruptedException tenantId:{}",tenantId,e);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("deleteData tenantId:{}",tenantId,e);
        }
    }

    /**
     * 处理通讯录查找关系
     * @param arg
     */
    public void dealAddressBookMsg(RelationshipModels.TaskArg arg){
        if(!getAddressBookSettingConfigValue(arg.getTenantId())){
            return;
        }
        if(CollectionUtils.isEmpty(arg.getPhoneList())){
            return;
        }
        //删除之前的逻辑关系
        deleteRelationshiByMemberAndType(arg.getTenantId(),arg.getUserId());
        List<List<String>> phoneList = ListsUtil.splitList(arg.getPhoneList(),50);
        for(List<String> phone : phoneList){
            objectApiNameMap.keySet().stream().forEach(objectApiName->{
                handleRelationshiByPhone(objectApiName,arg.getTenantId(),arg.getUserId(),phone,objectApiNameMap.get(objectApiName));
            });
        }
    }

    private void handleRelationshiByPhone(String objectApiName,String tenantId,String usetId,List<String> phoneList,List<String> findFields){
        User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
        SearchTemplateQuery searchTemplateQuery = createSearchTemplate(tenantId,objectApiName,phoneList);
        if(ObjectUtils.isEmpty(searchTemplateQuery)){
            return;
        }
        String operationDetailsc = usetId+":上传通讯录";
        try {
            int index = 0;
            while (true) {
                SearchTemplateQuery searchTemplateQueryTemp = SearchUtil.copySearchTemplateQuery(searchTemplateQuery);
                searchTemplateQueryTemp.setOffset(limit * index);
                QueryResult<IObjectData> queryResult;
                try {
                     queryResult = getQueryResultBySearchQuery(user,objectApiName,searchTemplateQueryTemp,findFields);
                } catch (Exception e) {
                    log.error("handleContactByPhone findIdenticalSql error tenantId:{}", tenantId, e);
                    break;
                }
                if(ObjectUtils.isEmpty(queryResult) || CollectionUtils.isEmpty(queryResult.getData())){
                    break;
                }
               List<IObjectData> dataList = queryResult.getData();
                //过滤不允许为空的字段
                if(findFields.size()>1){
                    for(String field : findFields){
                        if(!_ID.equals(field)){
                            dataList = dataList.stream().filter(data->ObjectUtils.isNotEmpty(data.get(field))).collect(Collectors.toList());
                        }
                    }
                }
                List<String> ids = dataList.stream().map(data->data.getId()).collect(Collectors.toList());
                if(CollectionUtils.isEmpty(ids)){
                    if (dataList.size() < limit) {
                        break;
                    }
                    index++;
                    continue;
                }
                List<Map> result = getAccountIdByLeadsIdOrContactId(tenantId,ids,objectApiName);
                if(CollectionUtils.isEmpty(result)){
                    if (dataList.size() < limit) {
                        break;
                    }
                    index++;
                    continue;
                }
                batchDealData(new User(tenantId, User.SUPPER_ADMIN_USER_ID),result,usetId,operationDetailsc);
                if (dataList.size() < limit) {
                    break;
                }
                index++;
            }

        } catch (Exception e) {
            log.error("handleRelationshiByPhone e:",e);
        }
    }

    private SearchTemplateQuery createSearchTemplate(String tenantId, String apiName, List<String> phoneList){
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        StringBuilder patternSb = new StringBuilder();
        try {
            IObjectDescribe iObjectDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(tenantId,apiName);
            //获取所有的电话字段
            Map<String, ObjectFieldDescribeDocument> fields = (Map<String, ObjectFieldDescribeDocument>) iObjectDescribe.get("fields");
            List<String> phoneFields = fields.keySet().stream().filter(x ->"phone_number".equals(((Map<String,Object>)fields.get(x)).get("type").toString()) && !"custom".equals(((Map<String,Object>)fields.get(x)).get("define_type").toString()) ).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(phoneFields)){
                   return null;
            }
            searchTemplateQuery.setWhatDescribeApiName(apiName);
            patternSb.append("1 and (");
            List<IFilter> filters = new ArrayList<>();
            SearchUtil.fillFilterEq(filters, TENANT_ID, tenantId);
            int index = 2;
            for(int i= 0;i<phoneFields.size();i++){
                patternSb.append(index);
                SearchUtil.fillFilterIn(filters, phoneFields.get(i), phoneList);
                if(i+1<phoneFields.size()){
                    patternSb.append(" or ");
                }
                index++;
            }
            patternSb.append(")");
            searchTemplateQuery.setPattern(patternSb.toString());
            searchTemplateQuery.setFilters(filters);
        } catch (MetadataServiceException e) {
           log.error("splicingSqlOfPhoneFields e:",e);
        }

        searchTemplateQuery.setLimit(limit);
        searchTemplateQuery.setFindExplicitTotalNum(false);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setPermissionType(0);
        List<OrderBy> orders = new ArrayList<>();
        orders.add(new OrderBy("create_time", Boolean.TRUE));
        searchTemplateQuery.setOrders(orders);
       return searchTemplateQuery;
    }



    private List<Map> getAccountIdByLeadsIdOrContactId(String tenantId,List<String> ids,String objectApiName){
            String sql = String.format("SELECT DISTINCT account_id,object_api_name,data_id from biz_contact_member_relationship where tenant_id = '%s' and object_api_name = '%s' and data_id = %s and is_deleted = 0 ",tenantId,objectApiName,SqlEscaper.any_clause(ids));
        return getDataListBySql(tenantId,sql);
    }

    /**
     * 判断是否存在关系
     * @param accountId
     * @param tenantId
     * @param id
     * @param objectApiName
     */
    private List<Map> isExistAccountIdByLeadsIdOrContactId(String accountId, String tenantId, String id, String objectApiName){
        String sql = String.format("SELECT account_id from biz_contact_member_relationship where tenant_id = '%s' and account_id = '%s' and object_api_name = '%s' and data_id = '%s' and is_deleted = 0 ",tenantId,accountId,objectApiName,id);
        return getDataListBySql(tenantId,sql);

    }

    private void batchDealData(User user,List<Map> result,String usetId,String operationDetailsc){
        //保存关系
        List<IObjectData> insertResultDataList = new ArrayList<>();
        for(Map map : result){
            RelationshipModels.Result relationshipResult = RelationshipModels.Result.builder().accountId(map.get(ACCOUNT_ID).toString())
                    .objectApiName(map.get(OBJECT_API_NAME).toString()).dataId(map.get(DATA_ID).toString()).member(usetId)
                    .relationshipType(EnumUtil.RelationshipType.addressBook.getValue()).build();
            insertResultDataList.add(getRelationshiData(user,relationshipResult,operationDetailsc));
            if(insertResultDataList.size()>= 100){
                bulkSaveData(user,insertResultDataList);
                insertResultDataList = new ArrayList<>();
            }
        }
        if(CollectionUtils.isNotEmpty(insertResultDataList)){
            bulkSaveData(user,insertResultDataList);
        }
    }


    public boolean getConfigValueByKey(String tenantId){
        String value = configService.findTenantConfig(new User(tenantId, User.SUPPER_ADMIN_USER_ID),CONTACT_MEMBER_RELATIONSHIP_FUNC_SETTING);
        if(ObjectUtils.isEmpty(value)  || "0".equals(value)){
            return false;
        }
        if("1".equals(value) || "2".equals(value)){
            return true;
        }
        return false;
    }
    public boolean getAddressBookSettingConfigValue(String tenantId) {
        String value = configService.findTenantConfig(new User(tenantId, User.SUPPER_ADMIN_USER_ID),CONTACT_MEMBER_RELATIONSHI_FUNC_ADDRESS_BOOK_SETTING);
        if(Strings.isNullOrEmpty(value) || !"2".equals(value)){
            return false;
        }
        return true;
    }

    /**
     * 处理历史数据
     * @param arg
     */
    public void dealHistoryDataMsg(RelationshipModels.TaskArg arg){
        if(ObjectUtils.isEmpty(arg.getTenantId())){
            return;
        }
        String id = getFirstId(arg.getTenantId());
        if(ObjectUtils.isEmpty(id)){
            log.info("dealHistoryDataMsg id is null,TenantId:{}",arg.getTenantId());
            return;
        }
        User user = new User(arg.getTenantId(), User.SUPPER_ADMIN_USER_ID);
        SearchTemplateQuery searchTemplateQuery = buildSTQ();
        List<OrderBy> orders = new ArrayList<>();
        orders.add(new OrderBy(_ID, Boolean.TRUE));
        searchTemplateQuery.setOrders(orders);

        String cursorId = id;
        int index=0;
        try {
            while (true) {
                if(index > 5000){
                    return;
                }
                index ++;
                log.info("dealHistoryDataMsg TenantId:{},index:{},cursorId:{}",user.getTenantId(),index,cursorId);
                if(GrayUtil.skipMemberRelationshipProcess(user.getTenantId())){
                    log.info("dealHistoryDataMsg skipTenant TenantId:{}",user.getTenantId());
                    return;
                }
                SearchTemplateQuery searchTemplateQueryTemp = SearchUtil.copySearchTemplateQuery(searchTemplateQuery);
                List<IFilter> filters = Lists.newArrayList();
                SearchUtil.fillFilterEq(filters, TENANT_ID, user.getTenantId());
                SearchUtil.fillFilterEq(filters, "is_deleted", 0);
                SearchUtil.fillFilterGT(filters, _ID, cursorId);
                searchTemplateQueryTemp.resetFilters(filters);
                QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(user,AccountObj,searchTemplateQueryTemp,getFindFieldListOfApiName(AccountObj));
                if(ObjectUtils.isEmpty(queryResult) || CollectionUtils.isEmpty(queryResult.getData())){
                    break;
                }
                List<IObjectData> dataList = queryResult.getData();
                //设置游标
                cursorId = dataList.get(dataList.size() - 1).getId();

                dataList.stream().forEach(data -> {
                    String operationDetailsc = String.format(OPERATION_DETAILSC,AccountObj,data.getId(),"初始化");
                    dealDataRelationshipByDataId(null,data,user,AccountObj,operationDetailsc);
                });
                if (dataList.size() < limit) {
                    break;
                }
            }
        }catch (Exception e){
           log.error("dealHistoryDataMsg e:",e);
        }
        setConfigKeyValue(user);
    }

    private void setConfigKeyValue(User user){
        try {
            configService.updateTenantConfig(user,CONTACT_MEMBER_RELATIONSHIP_FUNC_SETTING, "2", ConfigValueType.STRING);
        }catch (Exception e){
            log.error("setConfigKeyValue e:",e);
        }
    }


    private List<Map> getDataListBySql(String tenantId,String sql){
        List<Map> dataList = new ArrayList<>();
        try {
            dataList = objectDataService.findBySql(tenantId, sql);
        } catch (MetadataServiceException e) {
            throw new RuntimeException(e);
        }
        return dataList;
    }

    /**
     * 获取相关团队
     */
    public List<Member.roleType> getMemberByObjectApiName(String tenantId, String objectApiName, List<String> dataIds){
        if(CollectionUtils.isEmpty(dataIds)){
            return null;
        }
        String findSql = String.format("SELECT object_id,member_type,member_id,role_type from dt_team where object_id = %s " +
                "and tenant_id = '%s' AND object_describe_api_name = '%s' and   is_deleted = 0; ",SqlEscaper.any_clause(dataIds),tenantId,objectApiName);
        List<Map> ret = getDataListBySql(tenantId,findSql);
        if(CollectionUtils.isEmpty(ret)){
            return null;
        }
        List<Member.roleType> list = new ArrayList<>(ret.size());
        ret.stream().forEach(map->{
            list.add(Member.roleType.builder().objectDataId(map.get("object_id").toString()).memberId(map.get(MEMBER_ID).toString()).roleType(map.get(ROLE_TYPE).toString()).memberType(map.get(MEMBER_TYPE).toString()).build());
        });
        return  list;
    }
    /**
     * 获取相关团队转换为map
     */
    public Map<String,List<Member.roleType>> getMemberByObjectApiNameToMap(String tenantId, String objectApiName, List<String> dataIds){
        List<Member.roleType> list = getMemberByObjectApiName(tenantId,objectApiName,dataIds);
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        return list.stream().collect(Collectors.groupingBy(Member.roleType::getObjectDataId));
    }

    /**
     * 客户联合查重线索
     * @param user
     * @param iObjectData
     */
    private void accountDuplicateSearch(User user, IObjectData iObjectData, String objectApiName,Set<String> flag,String operationDetailsc){

        rateLimiterService.getDealDataRelationshipLimiter().acquire();
        DuplicatedModel.Arg arg = DuplicatedModel.Arg.builder().describe_api_name(objectApiName).is_need_duplicate(true)
                .page_size(leadsDuplicateSearchSize).object_data(ObjectDataDocument.of(iObjectData)).type(IDuplicatedSearch.Type.NEW).build();
        int index = 1;
        while(true){
            arg.setPage_number(index);
            DuplicatedModel.Result result = duplicatedRestServiceProxy.accountDuplicateSearch(HttpHeaderUtil.getHeaders(user),arg);
            if(result.getErrCode() != 0){
                log.warn("dealAccountRelationshi-->accountDuplicateSearch is fail tenantId:{},apiName:{},errMessage:{}",user.getTenantId(),objectApiName,result.getErrMessage());
                return ;
            }
            if(CollectionUtils.isEmpty(result.getResult().get(RESULTS))){
                return;
            }
            List<DuplicatedModel.DataResult> resList = result.getResult().get(RESULTS);
            //过滤线索id
            List<String> ids = resList.get(0).getData_list().stream().map(data->data.getId()).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(ids)){
                log.error("accountDuplicateSearch>>ids is null tenantId:{},result:{}",user.getTenantId(),JSONObject.toJSONString(result).substring(0,500));
                return;
            }
            if(ObjectUtils.isEmpty(flag)){
                flag.add("true");
            }
            Map<String,List<Member.roleType>> memberMap = getMemberByObjectApiNameToMap(user.getTenantId(),LeadsObj,ids);
            batchDealData(user,ids,iObjectData,memberMap,operationDetailsc,LeadsObj);
            if(ids.size() < leadsDuplicateSearchSize){
                return ;
            }
            index++;
        }
    }



    private void deleteData(String tenantId,String objectApiName,String dataId,List<String> types){
        int index =0;
        String sql = "";
        if(AccountObj.equals(objectApiName)){
            sql = String.format("SELECT id from biz_contact_member_relationship where tenant_id ='%s' and account_id = '%s' and is_deleted = 0  ORDER BY create_time limit %s OFFSET 0",tenantId,dataId,leadsDuplicateSearchSize);
        }else{
            sql = String.format("SELECT id from biz_contact_member_relationship where tenant_id ='%s' and object_api_name = '%s' AND data_id ='%s' and is_deleted = 0 and  relationship_type in %s ORDER BY create_time limit %s OFFSET 0",
                    tenantId,objectApiName,dataId,SqlEscaper.in_clause(types),leadsDuplicateSearchSize);
        }
        try {
            while(true){

                    List<Map> queryIdResult = objectDataService.findBySql(tenantId, sql);
                    if(CollectionUtils.isEmpty(queryIdResult)){
                        break;
                    }
                    List<String> ids = queryIdResult.stream().map(x->x.get("id").toString()).collect(Collectors.toList());
                    String deleteSql = String.format("delete from biz_contact_member_relationship WHERE tenant_id ='%s' and id = %s ;",tenantId, SqlEscaper.any_clause(ids));
                    objectDataService.deleteBySql(tenantId,deleteSql);
                    if(ids.size()<leadsDuplicateSearchSize){
                        break;
                    }
                    Thread.sleep(300);
                    index ++;
                    if(index>1000000){
                       return;
                    }
            }
        }catch (InterruptedException e){
            log.error("deleteData InterruptedException 1 tenantId:{}",tenantId,e);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("deleteData tenantId:{}",tenantId,e);
            throw new RuntimeException(e);
        }
    }

    public void deleteDataByTenantId(String tenantId,String dataId,String apiName){
        int index =0;
        String  sql = "";
        if(AccountObj.equals(apiName)){
            sql = String.format("SELECT id from biz_contact_member_relationship where tenant_id ='%s' and account_id = '%s' and is_deleted = 0  ORDER BY create_time limit %s OFFSET 0",tenantId,dataId,leadsDuplicateSearchSize);
        }else{
            sql = String.format("SELECT id from biz_contact_member_relationship where tenant_id ='%s' and object_api_name = '%s' AND data_id ='%s' and is_deleted = 0  ORDER BY create_time limit %s OFFSET 0",
                    tenantId,apiName,dataId,leadsDuplicateSearchSize);
        }
        try {
            while(true){
                    List<Map> queryIdResult = objectDataService.findBySql(tenantId, sql);
                    if(CollectionUtils.isEmpty(queryIdResult)){
                        break;
                    }
                    List<String> ids = queryIdResult.stream().map(x->x.get("id").toString()).collect(Collectors.toList());
                    String deleteSql = String.format("delete from biz_contact_member_relationship WHERE tenant_id ='%s' and id = %s ;",tenantId, SqlEscaper.any_clause(ids));
                    objectDataService.deleteBySql(tenantId,deleteSql);
                    if(ids.size()<limit){
                        break;
                    }
                    index ++;
                    if(index>1000000){
                        return;
                    }
            }
        }catch (Exception e){
            log.error("deleteDataByTenantId tenantId:{}",tenantId,e);
        }
    }

    private List<String> getFindFieldListOfApiName(String apiName){
        if(AccountObj.equals(apiName)){
            return Lists.newArrayList(_ID,NAME,"tel","email","object_describe_api_name");
        }else if(LeadsObj.equals(apiName)){
            return Lists.newArrayList(_ID,NAME,COMPANY,"tel","email","object_describe_api_name",BIZ_STATUS);
        }else if(ContactObj.equals(apiName)){
            return Lists.newArrayList(_ID,NAME,"account_id","object_describe_api_name");
        }
        return null;
    }

    private SearchTemplateQuery buildSTQ(){
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(limit);
        searchTemplateQuery.setFindExplicitTotalNum(false);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setPermissionType(0);
        return searchTemplateQuery;
    }

    private String getFirstId(String tenantId){
        String sql = String.format("SELECT id from biz_account where tenant_id ='%s' and is_deleted = 0  ORDER BY id ASC limit 1", tenantId);
        List<Map> queryIdResult =null;
        try {
            queryIdResult = objectDataService.findBySql(tenantId, sql);
        } catch (MetadataServiceException e) {
            log.error("RelationshiService getFirstId tenantId:{}",tenantId,e);
            return null;
        }
        if(CollectionUtils.isEmpty(queryIdResult)){
            return null;
        }
        return queryIdResult.get(0).get("id").toString();
    }
}
