package com.facishare.crm.sfa.lto.activity.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;

public interface ActivityUserModel {
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class ActivityUser {
        private String id;
        private String tenantId;
        private String name;
        private String activeRecordId;
        private String personnelId;
        private String contactId;
        private String publicEmployeeId;
        private String userId;
        private String userName;
        private String originalUserId;
        private String originalUserName;
        private String userApiName;
        private Long nameAvaId;
        private String avatarBgColor;
        private int isDefaultSpearker;
        private String participantTypes;
        private BigDecimal participationProportion;
        private String createdBy;
        private Long createTime;
        private String lastModifiedBy;
        private Long lastModifiedTime;
        private String objectDescribeApiName;
        private Long version;
        private String recordType;
        private Long sysModifiedTime;
    }
}
