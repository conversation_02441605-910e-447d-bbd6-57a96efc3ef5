package com.facishare.crm.sfa.lto.rest;

import com.facishare.crm.sfa.lto.rest.models.CustomProxyModel;
import com.facishare.crm.sfa.lto.rest.models.UserRoleModel;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

/**
 * <AUTHOR> lik
 * @date : 2022/9/9 11:21
 */
@RestResource(value = "SFA-CHECK-WORK", desc = "CRM Rest API Call", contentType = "application/json",
        codec = "com.facishare.paas.appframework.metadata.util.CRMRestServiceCodec")
public interface LtoCustomProxy {
    @POST(value = "/customProxy/getRuleNameByIds", desc = "根据考勤规则id查询规则名称")
    CustomProxyModel.Result getRuleNameByIds(@HeaderMap Map<String, String> headers, @Body CustomProxyModel.Arg body);

    @POST(value = "/customProxy/getSectionIsWorkByRuleId", desc = "根据考勤规则id查询时间区间有哪些工作日")
    CustomProxyModel.Result getSectionIsWorkByRuleId(@HeaderMap Map<String, String> headers, @Body CustomProxyModel.Arg body);

    @POST(value = "/customProxy/GetDetailByRuleId", desc = "根据考勤规则id查询时间区间有哪些工作日")
    Object GetDetailByRuleId(@HeaderMap Map<String, String> headers, @Body CustomProxyModel.Arg body);
}
