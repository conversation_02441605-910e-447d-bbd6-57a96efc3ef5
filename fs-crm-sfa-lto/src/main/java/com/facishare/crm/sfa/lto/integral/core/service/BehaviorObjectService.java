package com.facishare.crm.sfa.lto.integral.core.service;

import com.facishare.crm.sfa.lto.integral.common.constant.IntegralObject;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class BehaviorObjectService {
    @Autowired
    DataLogicService dataLogicService;

    protected List<IObjectData> getBehaviorObjectList(String tenantId, String objectApiName) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, IntegralObject.FIELD_IS_ACTIVE, true);
        query.addFilters(filters);
        List<OrderBy> orders = Lists.newArrayList();
        SearchUtil.fillOrderBy(orders, IObjectData.CREATE_TIME, true);
        query.setLimit(2000);
        query.setOrders(orders);
        query.setNeedReturnCountNum(false);

        return dataLogicService.findBySearchQuery(tenantId, objectApiName, query);
    }
}
