package com.facishare.crm.sfa.lto.utils;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.license.Result.ModuleInfoResult;
import com.facishare.paas.license.arg.QueryModuleArg;
import com.facishare.paas.license.common.LicenseContext;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.paas.license.pojo.ModuleInfoPojo;
import com.facishare.paas.metadata.util.SpringUtil;
import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/12/25 10:31
 * @description:
 */
public class LicenseCheckUtil {

    private static final LicenseClient licenseClient = SpringUtil.getContext().getBean(LicenseClient.class);

    public static boolean checkAIExist(String tenantId) {
        return checkModuleLicenseExist(tenantId, "ai_interactive_assistant_app");
    }

    public static boolean checkModuleLicenseExist(String tenantId, String packageName) {
        QueryModuleArg queryModuleArg = new QueryModuleArg();
        LicenseContext licenseContext = getLicenseContext(tenantId);
        queryModuleArg.setLicenseContext(licenseContext);
        ModuleInfoResult result = licenseClient.queryModule(queryModuleArg);
        if (result == null) {
            return false;
        }
        List<ModuleInfoPojo> modules = result.getResult();
        return (modules != null && modules.stream().anyMatch(module -> Objects.equals(module.getModuleCode(), packageName)));
    }


    @NotNull
    private static LicenseContext getLicenseContext(String tenantId) {
        LicenseContext licenseContext = new LicenseContext();
        licenseContext.setAppId("CRM");
        licenseContext.setTenantId(tenantId);
        licenseContext.setUserId(User.SUPPER_ADMIN_USER_ID);
        return licenseContext;
    }
}
