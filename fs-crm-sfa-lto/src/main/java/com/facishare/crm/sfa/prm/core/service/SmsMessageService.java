package com.facishare.crm.sfa.prm.core.service;

import com.alibaba.fastjson.TypeReference;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.sfa.prm.api.enums.NotificationType;
import com.facishare.crm.sfa.prm.api.notification.MessageService;
import com.facishare.crm.sfa.prm.model.MessageContent;
import com.facishare.crm.sfa.prm.model.SmsMessageContent;
import com.facishare.crm.sfa.prm.platform.model.RestResponse;
import com.facishare.crm.sfa.prm.rest.client.SmsHubClient;
import com.facishare.crm.sfa.prm.rest.model.SmsHubModel;
import com.facishare.paas.appframework.common.service.SmsServiceProxy;
import com.facishare.paas.appframework.common.service.dto.SendSms;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.util.SFAHeaderUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @time 2024-06-29 16:17
 * @Description
 */
@Service
@Slf4j
public class SmsMessageService implements MessageService {
    @Resource
    private SmsServiceProxy smsServiceProxy;
    @Resource
    private SmsHubClient smsHubClient;
    @Resource
    private EIEAConverter converter;


    @Override
    public NotificationType getMessageType() {
        return NotificationType.SMS;
    }

    @Override
    public boolean sendMessage(User user, MessageContent content) {
        if (!(content instanceof SmsMessageContent)) {
            throw new IllegalArgumentException("Invalid content type for SmsMessageService");
        }
        SmsMessageContent smsMessageContent = (SmsMessageContent) content;
        if (StringUtils.isBlank(smsMessageContent.getContent()) && StringUtils.isBlank(smsMessageContent.getTemplateId())) {
            log.warn("短信消息发送失败，短信模板ID和短信内容不能同时为空，tenant:{}, content:{}, templateId:{}", user.getTenantId(), smsMessageContent.getContent(), smsMessageContent.getTemplateId());
            return false;
        }
        try {
            if (StringUtils.isNotBlank(smsMessageContent.getTemplateId())) {
                return sendSmsByHub(user, smsMessageContent);
            } else if (StringUtils.isNotBlank(smsMessageContent.getContent())) {
                return sendMWSms(user, smsMessageContent);
            }
        } catch (Exception e) {
            log.error("SmsMessageService#sendMessage error, tenant:{}, smsMessageContent:{}", user.getTenantId(), smsMessageContent, e);
        }
        return false;
    }

    private boolean sendSmsByHub(User user, SmsMessageContent smsMessageContent) {
        String ea = converter.enterpriseIdToAccount(user.getTenantIdInt());
        SmsHubModel.PhoneData phoneData = new SmsHubModel.PhoneData();
        phoneData.setPhoneList(Lists.newArrayList(smsMessageContent.getRecipients()));
        List<SmsMessageContent.SmsContentParam> smsContentParams = smsMessageContent.getSmsContentParams();
        Map<String, String> paramMapping = smsContentParams.stream()
                .filter(contentParam -> StringUtils.isNotBlank(contentParam.getKey()))
                .collect(Collectors.toMap(SmsMessageContent.SmsContentParam::getKey, SmsMessageContent.SmsContentParam::getValue));
        phoneData.setParamMap(paramMapping);
        SmsHubModel.SendSmsArg arg = SmsHubModel.SendSmsArg.builder()
                .ea(ea)
                .templateId(smsMessageContent.getTemplateId())
                .channelType(smsMessageContent.getChannelType().getType())
                .businessType(smsMessageContent.getBusinessType())
                .receiver(smsMessageContent.getReceiver())
                .sendNode(smsMessageContent.getSendNode())
                .objectId(smsMessageContent.getObjectDataId())
                .phoneDataList(Lists.newArrayList(phoneData))
                .build();
        RestResponse restResponse = smsHubClient.send(SFAHeaderUtil.getHeaders(user), arg);
        if (restResponse == null || !restResponse.isSuccess()) {
            log.warn("sendSmsByHub warn, tenant:{}, arg:{}", user.getTenantId(), arg);
            return false;
        }
        String response = restResponse.getResponse(() -> new TypeReference<String>() {
        });
        log.info("sendSmsByHub response:{}", response);
        return true;
    }

    /**
     * 发送梦网 短信
     *
     * @param user              用户
     * @param smsMessageContent 短信内容
     * @return 是否发送成功
     */
    private boolean sendMWSms(User user, SmsMessageContent smsMessageContent) {
        SendSms.Arg arg = buildSmsArg(user, smsMessageContent);
        SendSms.Result result = smsServiceProxy.sendSms(arg);
        return result != null && result.getStatus() == 0;
    }

    private SendSms.Arg buildSmsArg(User user, SmsMessageContent smsMessageContent) {
        // 无身份
        Integer sender = -5000;
        String ea = converter.enterpriseIdToAccount(user.getTenantIdInt());
        SendSms.Arg arg = new SendSms.Arg();
        arg.setObjectId(smsMessageContent.getObjectDataId());
        arg.setType(smsMessageContent.getType());
        arg.setChannelType(smsMessageContent.getChannelType().getType());
        arg.setEa(ea);
        arg.setUserId(sender);
        arg.setTemplateId(smsMessageContent.getTemplateId());
        if (StringUtils.isBlank(smsMessageContent.getTemplateId())) {
            arg.setTemplateContent(smsMessageContent.getContent());
        }
        SendSms.PhoneData phoneData = new SendSms.PhoneData();
        phoneData.setPhoneList(Lists.newArrayList(smsMessageContent.getRecipients()));
        List<SmsMessageContent.SmsContentParam> smsContentParams = smsMessageContent.getSmsContentParams();
        Map<String, String> paramMapping = smsContentParams.stream()
                .filter(contentParam -> StringUtils.isNotBlank(contentParam.getKey()))
                .collect(Collectors.toMap(SmsMessageContent.SmsContentParam::getKey, SmsMessageContent.SmsContentParam::getValue));
        phoneData.setParamMap(paramMapping);
        arg.setPhoneDataList(Lists.newArrayList(phoneData));
        return arg;
    }
}
