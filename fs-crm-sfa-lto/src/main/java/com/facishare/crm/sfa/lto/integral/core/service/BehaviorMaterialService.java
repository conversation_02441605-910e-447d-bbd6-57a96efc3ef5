package com.facishare.crm.sfa.lto.integral.core.service;

import com.facishare.crm.sfa.lto.integral.core.service.dto.BehaviorInfo;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;
import java.util.Set;

/**
 * 行为物料服务类
 *
 * <AUTHOR>
 */
public interface BehaviorMaterialService {
    IObjectData getBehaviorMaterialByApiName(String tenantId, String categoryApiName, String materialApiName);

    IObjectData getBehaviorMaterialByLabel(String tenantId, String categoryApiName, String materialLabel);

    List<IObjectData> getBehaviorMaterialList(String tenantId);

    List<IObjectData> getBehaviorMaterialList(String tenantId, Set<String> categoryApiNames, Set<String> materialApiNames);

    List<IObjectData> getBehaviorMaterialByCategoryApiName(String tenantId, String categoryApiName, String materialApiName);

    List<BehaviorInfo.BehaviorMaterial> getBatchBehaviorMaterialList(String tenantId, String categoryApiName);

    void updateBehaviorMaterial(IObjectData objectData, String userId, String materialLabel);
}
