package com.facishare.crm.sfa.lto.integral.core.rest.dto;

import com.facishare.crm.sfa.lto.integral.core.service.dto.DeleteRule;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

public interface DeleteRuleFromEngine {

    @EqualsAndHashCode(callSuper = true)
	@Data
    class Arg extends BaseEngine.Arg {
        @SerializedName("macroGroupApiNames")
        List<String> apiNameList;

        public static Arg build(DeleteRule.Arg in, RequestContext requestContext) {

            Arg out = new Arg();
            BaseEngine.Context context = new BaseEngine.Context();
            context.setTenantId(requestContext.getTenantId());
            context.setUserId(requestContext.getUser().getUserId());
            out.setContext(context);

            List<String> apiNameList = Lists.newArrayList();
            apiNameList.add(in.getApiName());
            out.setApiNameList(apiNameList);
            return out;
        }
    }

    @EqualsAndHashCode(callSuper = true)
	@Data
    class Result extends BaseEngine.Result<Object> { }
}
