package com.facishare.crm.sfa.lto.qywx.proxy.models;

import lombok.Builder;
import lombok.Data;

import java.util.List;

public class EnterpriseWechatChangeOwnerSyncModel {

	@Data
	@Builder
	public static class Arg {
		private String ea;
		private String handoverUserId;//原跟进成员的userid
		private String takeoverUserId;//接替成员的userid
		private List<String> externalUserId;//客户的external_userid列表，每次最多分配100个客户
		private String transferSuccessMsg;//转移成功后发给客户的消息，最多200个字符，不填则使用默认文案
		private String handoverDeptId;//原跟进成员的CRM部门id
		private String handoverDeptName;//原跟进成员的CRM部门名称
		private String takeoverDeptId;//接替成员的CRM部门id
		private String takeoverDeptName;//接替成员的CRM部门名称
		private String externalApiName;//CRM对象
		private String externalUserName;//CRM客户的名称
		private String externalNickname;// 企微昵称
	}

	@Data
	public static class SyncResult {
		private String externalUserId;//客户的external_userid
		private Integer errCode;//对此客户进行分配的结果, 具体可参考全局错误码, 0表示成功发起接替,待24小时后自动接替,并不代表最终接替成功
	}

}
