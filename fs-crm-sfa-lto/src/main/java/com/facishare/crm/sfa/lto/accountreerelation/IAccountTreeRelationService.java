package com.facishare.crm.sfa.lto.accountreerelation;

import com.facishare.crm.sfa.lto.accountreerelation.models.AccountTreeRelationModels.*;
import com.facishare.paas.appframework.core.model.User;

public interface IAccountTreeRelationService {
    CreateAccountTreeRelationResult createAccountTreeRelation(User user, CreateAccountTreeRelationArg arg);
    CreateAccountTreeRelationResult createAccountTreeRelationAsync(User user, CreateAccountTreeRelationArg arg);
    CheckCreateAccountTreeRelationResult checkCreateAccountTreeRelation(User user, CreateAccountTreeRelationArg arg);
    CreateAccountTreeRelationResult relateMainData2TreeRelation(User user, RelateMainData2TreeRelationArg arg);
    CreateAccountTreeRelationResult relateMainData2TreeRelationAsync(User user, RelateMainData2TreeRelationArg arg);
    CheckCreateAccountTreeRelationResult deleteAccountTreeRelationUniqueData(User user, CreateAccountTreeRelationArg arg);
    CheckCreateAccountTreeRelationResult deleteAccountTreeRelationData(User user, CreateAccountTreeRelationArg arg);
}
