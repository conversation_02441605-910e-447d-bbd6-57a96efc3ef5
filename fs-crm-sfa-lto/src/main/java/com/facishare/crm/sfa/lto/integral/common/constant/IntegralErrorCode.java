package com.facishare.crm.sfa.lto.integral.common.constant;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ErrorCode;

public enum IntegralErrorCode implements ErrorCode {

    VALIDATION_ERROR(320001401, ""),
    PARAMETER_IS_NULL(201140100, I18N.text("crm.integral.parameter.not.null")),
    API_NAME_DUPLICATED(201140114, I18N.text("crm.integral.rule.api.name.repeat")),
    LABEL_DUPLICATED(201140117, I18N.text("crm.integral.rule.label.repeat")),
    DESCRIBE_DUPLICATED(201140115, I18N.text("crm.integral.object.only.applied.to.one.rule")),
    RULE_NOT_FOUND(201140113, I18N.text("crm.integral.rule.not.exist")),
    BEHAVIOR_CATEGORY_NOT_FOUND(201140102, I18N.text("crm.integral.category.not.exist")),
    BEHAVIOR_CATEGORY_API_NAME_EXIST(201140103, I18N.text("crm.integral.category.api.name.already.exist")),
    BEHAVIOR_CATEGORY_LABEL_EXIST(201140104, I18N.text("crm.integral.category.label.already.exist")),
    BEHAVIOR_ACTION_NOT_FOUND(201140116, I18N.text("crm.integral.action.not.exist")),
    BEHAVIOR_ACTION_API_NAME_EXIST(201140117, I18N.text("crm.integral.action.api.name.already.exist")),
    BEHAVIOR_ACTION_LABEL_EXIST(201140118, I18N.text("crm.integral.action.label.already.exist")),
    BEHAVIOR_MATERIAL_NOT_FOUND(201140119, I18N.text("crm.integral.material.not.exist")),
    BEHAVIOR_MATERIAL_LABEL_EXIST(201140121, I18N.text("crm.integral.material.label.already.exist")),
    MORE_THAN_MAX_RULE_GROUP(201140122, I18N.text("crm.integral.rule.create.max.number")),
    MORE_THAN_MAX_RULE_ITEM(201140123, I18N.text("crm.integral.rule.group.max.number"));

    IntegralErrorCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    int code;
    String message;

    @Override
    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static final int SUCCESS = 0;
    public static final int ERROR_CODE_RULE_ENGINE = 100;
    public static final int ERROR_CODE_UDOBJ = 200;
    public static final int ERROR_API_NOT_FOUND = 201140113;
    public static final int ERROR_API_DUPLICATE = 201140114;
    public static final int ERROR_NAME_DUPLICATE = 201140117;
    public static final int ERROR_NAME_UNKNOWN = 2011140118;
    public static final int COMPUTING_ERROR = 2011140119;
}
