package com.facishare.crm.sfa.lto.duplicated;

import com.facishare.crm.sfa.lto.duplicated.models.DuplicatedModels;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class DefaultDuplicatedDataProcessor extends AbstractDuplicatedDataProcessor {
    @Override
    public String getProcessorModel() {
        return "DEFAULT_PROCESSOR";
    }

    @Override
    public boolean matchProcessor(DuplicatedModels.TriggerAction triggerAction) {
        return !DuplicatedModels.TriggerAction.INVALID.equals(triggerAction)
                && !DuplicatedModels.TriggerAction.RULE_CHANGE.equals(triggerAction)
                && !DuplicatedModels.TriggerAction.NONE.equals(triggerAction)
                && !DuplicatedModels.TriggerAction.DIRECT_PROCESSOR.equals(triggerAction)
                && !DuplicatedModels.TriggerAction.CLEAR_DUPLICATED.equals(triggerAction);
    }

    @Override
    protected boolean isProcessingWithMode() {
        return true;
    }

    @Override
    public void process(DuplicatedDataProcessor.DuplicatedDataProcessArg processArg) {
        List<IObjectData> objectDataList = processArg.getDataList();
        User user = processArg.getUser();
        if (skipLeadsDuplicated(user.getTenantId()) || CollectionUtils.isEmpty(objectDataList)) {
            return;
        }
        List<DuplicatedModels.DuplicatedProcessing> processingRuleList = duplicatedProcessingRuleService.getDuplicatedProcessingListByTriggerAction(user, processArg.getAction());
        int refreshVersion = duplicatedProcessingRuleService.getRefreshVersion(user);
        process(user, objectDataList, processingRuleList, processArg.getAction(), refreshVersion);
    }
}