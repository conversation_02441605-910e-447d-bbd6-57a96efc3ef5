package com.facishare.crm.sfa.lto.marketingattribution;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.lto.utils.ObjectDataUtil;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;


@Slf4j
@Component
public class MarketingAttributionUStrategy extends BaseMarketingAttributionStrategy {
    @Override
    public String getStrategyModel() {
        return "u_strategy";
    }

    @Override
    public void process(RequestContext context, IObjectData triggerData, IObjectData strategyRule, List<IObjectData> attributionDataList) {
        String attributionModelSetting = ObjectDataUtil.getStringValue(strategyRule, "attribution_model_setting", "");
        if(StringUtils.isBlank(attributionModelSetting)) {
            log.error("MarketingAttributionUStrategy setting error, strategyRuleData {}", strategyRule);
            return;
        }
        deleteMarketingEventInfluenceData(context.getTenantId(), triggerData.getDescribeApiName(), triggerData.getId());
        JSONObject jsonObject = JSON.parseObject(attributionModelSetting);
        Double firstRatio = jsonObject.getDouble("first_ratio");
        Double middleRatio = jsonObject.getDouble("middle_ratio");
        Double lastRatio = jsonObject.getDouble("last_ratio");
        List<IObjectData> firstDataList = getFistStageDataList(attributionDataList);
        List<IObjectData> middleDataList = getMiddleStageDataList(attributionDataList);
        List<IObjectData> lastDataList = getLastStageDataList(attributionDataList, true);
        if(CollectionUtils.isEmpty(middleDataList) && CollectionUtils.isEmpty(lastDataList)) {
            List<Double> stagePercentList = calculateRatio(100.00, firstDataList.size());
            createMarketingEventInfluence(context, triggerData, strategyRule, firstDataList, stagePercentList);
        } else if (CollectionUtils.isEmpty(middleDataList)) {
            List<Double> stagePercentList = calculateRatio(50.00, firstDataList.size());
            createMarketingEventInfluence(context, triggerData, strategyRule, firstDataList, stagePercentList);
            stagePercentList = calculateRatio(50.00, lastDataList.size());
            createMarketingEventInfluence(context, triggerData, strategyRule, lastDataList, stagePercentList);
        } else {
            List<Double> stagePercentList = calculateRatio(firstRatio, firstDataList.size());
            createMarketingEventInfluence(context, triggerData, strategyRule, firstDataList, stagePercentList);
            stagePercentList = calculateRatio(middleRatio, middleDataList.size());
            createMarketingEventInfluence(context, triggerData, strategyRule, middleDataList, stagePercentList);
            stagePercentList = calculateRatio(lastRatio, lastDataList.size());
            createMarketingEventInfluence(context, triggerData, strategyRule, lastDataList, stagePercentList);
        }
    }
}
