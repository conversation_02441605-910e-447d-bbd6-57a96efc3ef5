package com.facishare.crm.sfa.lto.loyalty.service;

import com.facishare.crm.sfa.lto.loyalty.constants.LoyaltyConstants;
import com.facishare.crm.sfa.lto.loyalty.model.Loyalty;
import com.facishare.crm.sfa.lto.loyalty.task.LoyaltyMqProducer;
import com.facishare.paas.appframework.core.model.InfraServiceFacade;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.flow.mq.WorkflowProducer;
import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.api.IdGenerator;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class LoyaltyMemberChangeRecordsService {

    @Resource
    ServiceFacade serviceFacade;
    @Resource
    InfraServiceFacade infraServiceFacade;
    @Resource
    LoyaltyMqProducer loyaltyMqProducer;
    @Resource
    ApplicationEventPublisher applicationEventPublisher;

    /**
     * 保存会员信息变动记录
     */
    public void saveMemberChangeRecord(Loyalty.PointsOperationParam param, List<ObjectDataDocument> recordDataDocumentList) {
        String tenantId = param.getTenantId();
        List<IObjectData> insertList = new ArrayList<>();
        for (ObjectDataDocument recordDataDocument : recordDataDocumentList) {
            recordDataDocument.put(IObjectData.NAME, param.getUniqueId());
            recordDataDocument.put(LoyaltyConstants.LoyaltyMemberChangeRecords.PROGRAM_ID, param.getProgramId());
            if (StringUtils.isEmpty(recordDataDocument.get(LoyaltyConstants.LoyaltyMemberChangeRecords.MEMBER_ID))) {
                recordDataDocument.put(LoyaltyConstants.LoyaltyMemberChangeRecords.MEMBER_ID, param.getMemberId());
            }
            recordDataDocument.put(LoyaltyConstants.LoyaltyMemberChangeRecords.MEMBER_BEHAVIOR, param.getDesc());
            if (!StringUtils.isEmpty(param.getOperator())) {
                recordDataDocument.put(LoyaltyConstants.LoyaltyMemberChangeRecords.OPERATOR, Lists.newArrayList(param.getOperator()));
            }
            recordDataDocument.put(LoyaltyConstants.LoyaltyMemberChangeRecords.INCENTIVE_POLICY_ID, param.getIncentivePolicyId());
            recordDataDocument.put(LoyaltyConstants.LoyaltyMemberChangeRecords.INCENTIVE_POLICY_RULE_ID, param.getIncentivePolicyRuleId());
            recordDataDocument.put(LoyaltyConstants.LoyaltyMemberChangeRecords.TRANSACTION_EVENT, param.getTransactionEventId());
            recordDataDocument.put(LoyaltyConstants.LoyaltyMemberChangeRecords.SOURCE_API_NAME, param.getSourceApiName());
            recordDataDocument.put(LoyaltyConstants.LoyaltyMemberChangeRecords.SOURCE_ID, param.getSourceId());
            insertList.add(recordDataDocument.toObjectData());
        }
        saveMemberChangeRecord(tenantId, insertList);
    }

    public void saveMemberChangeRecord(String tenantId, IObjectData member, String tierBefore, String tierAfter) {
        ObjectDataDocument recordTierChange = new ObjectDataDocument();
        recordTierChange.put(LoyaltyConstants.LoyaltyMemberChangeRecords.PROGRAM_ID, member.get(LoyaltyConstants.LoyaltyMember.PROGRAM_ID, String.class));
        recordTierChange.put(LoyaltyConstants.LoyaltyMemberChangeRecords.MEMBER_ID, member.getId());
        recordTierChange.put(LoyaltyConstants.LoyaltyMemberChangeRecords.LOY_CHANGE_TIER_BEFORE, tierBefore);
        recordTierChange.put(LoyaltyConstants.LoyaltyMemberChangeRecords.LOY_CHANGE_TIER_AFTER, tierAfter);
        recordTierChange.put(LoyaltyConstants.LoyaltyMemberChangeRecords.LOY_CHANGE_TYPE, "SET_LEVEL_BY_SYS");
        saveMemberChangeRecord(tenantId, Lists.newArrayList(recordTierChange.toObjectData()));
    }

    public void saveMemberChangeRecord(String tenantId, List<IObjectData> dataList) {
        for (IObjectData data : dataList) {
            data.set(IObjectData.ID, IdGenerator.get());
            if (StringUtils.isEmpty(data.getName())) {
                data.set(IObjectData.NAME, data.getId());
            }
            data.set(IObjectData.DESCRIBE_API_NAME, LoyaltyConstants.LoyaltyMemberChangeRecords.API_NAME);
            data.set(IObjectData.TENANT_ID, tenantId);
            data.set(IObjectData.RECORD_TYPE, IObjectData.RECORD_TYPE_DEFAULT);
        }
        serviceFacade.bulkSaveObjectData(dataList, User.systemUser(tenantId));
        dataList.forEach(data -> loyaltyMqProducer.asyncMemberUpgrade(data));
        //触发工作流
        MemberChangeRecordSaveEvent event = new MemberChangeRecordSaveEvent(LoyaltyConstants.LoyaltyMemberChangeRecords.API_NAME, dataList);
        applicationEventPublisher.publishEvent(event);
    }

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    private void eventHandler(MemberChangeRecordSaveEvent event) {
        List<IObjectData> dataList = event.getDataList();
        if (LoyaltyConstants.LoyaltyMemberChangeRecords.API_NAME.equals(event.getSource())) {
            dataList.forEach(data -> infraServiceFacade.startWorkFlow(data.getId(), data.getDescribeApiName(), WorkflowProducer.TRIGGER_START, User.systemUser(data.getTenantId()), Maps.newHashMap()));
        }
    }

    @Getter
    static class MemberChangeRecordSaveEvent extends ApplicationEvent {

        private final List<IObjectData> dataList;

        public MemberChangeRecordSaveEvent(Object source, List<IObjectData> idList) {
            super(source);
            this.dataList = idList;
        }
    }
}
