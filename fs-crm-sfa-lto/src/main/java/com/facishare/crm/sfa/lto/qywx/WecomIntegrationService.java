package com.facishare.crm.sfa.lto.qywx;

import com.facishare.crm.sfa.lto.qywx.proxy.WecomServiceProxy;
import com.facishare.crm.sfa.lto.qywx.proxy.models.EnterpriseWeChatResult;
import com.facishare.crm.sfa.lto.utils.Safes;
import com.fxiaoke.functions.utils.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class WecomIntegrationService {

    @Autowired
    private WecomServiceProxy wecomServiceProxy;



    public Map<String, String> getUserIdMapping(String ea) {
        EnterpriseWeChatResult<Map<String, String>> employeeIdResult = wecomServiceProxy.queryEmployeeId(ea);
        log.info("查询开启联系功能的员工绑定关系:{}", employeeIdResult);
        if (employeeIdResult == null) {
            return new HashMap<>();
        }
        // 开启了外部联系功能的员工id <企微id，fxId>
        Map<String, String> employeeIdMap = Safes.of(employeeIdResult.getData());
        employeeIdMap.forEach((k, v) -> {
            if (StringUtils.isNotEmpty(v)) {
                String[] splitArray = v.split("\\.");
                employeeIdMap.put(k, splitArray[splitArray.length - 1]);
            }
        });
        return employeeIdMap;
    }
}
