package com.facishare.crm.sfa.lto.integral.core.rest.dto;

import com.facishare.crm.sfa.lto.integral.common.constant.RuleDataConstant;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

public interface BaseEngine {

    @Data
    class Arg {
        @SerializedName("context")
        private Context context;
    }

    @Data
    class Context {
        @SerializedName("appId")
        private String appId = "CRM";
        @SerializedName("objectProperties")
        private Object objectProperties = new Object();
        @SerializedName("properties")
        private Object properties = new Object();
        @SerializedName("tenantId")
        private String tenantId;
        @SerializedName("userId")
        private String userId;
        @SerializedName("scene")
        private String scene = RuleDataConstant.CRM_BEHAVIOR_INTEGRAL;

        public Context() {}

        public Context(String tenantId, String userId) {
            this.tenantId = tenantId;
            this.userId = userId;
        }
    }

    @Data
    class Result<T> {
        @SerializedName("errCode")
        private Integer errCode;
        @SerializedName("errMessage")
        private String errMessage;
        @SerializedName("result")
        private T result;
    }


}
