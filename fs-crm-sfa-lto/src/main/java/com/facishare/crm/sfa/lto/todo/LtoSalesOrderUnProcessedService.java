package com.facishare.crm.sfa.lto.todo;

import com.facishare.crm.sfa.lto.todo.enums.LtoSessionBOCItemKeys;
import com.facishare.crm.sfa.lto.utils.SFALtoBizLogUtil;
import com.facishare.crm.sfa.lto.utils.TodoUtils;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * Demo class
 *
 * <AUTHOR>
 * @date 2019/10/23
 */
@Service
@Slf4j
public class LtoSalesOrderUnProcessedService implements LtoIUnProcessedService {
    @Autowired
    private ServiceFacade serviceFacade;
    private static final int MAX_LOOP_COUNT = 100;

    @Override
    public String getObjectApiName() {
        return "SalesOrderObj";
    }

    @Override
    public QueryResult<IObjectData> getOrderTotalUnProcessedData(User user, LtoSessionBOCItemKeys sessionBOCItemKey) {
        QueryResult<IObjectData> result = new QueryResult<>();
        List<IObjectData> objectDataList = Lists.newArrayList();
        if (sessionBOCItemKey == LtoSessionBOCItemKeys.TobeDeliveryCustomerOrder) {
            return getToDeliveryDataResult(user);
        } else {
            // 票数的请求待办走sqlserver请求数据，不走pg请求。 数据存在延迟5s不够用 延迟太多，订单创建太多，线程用量会比较大。
            result.setTotalNumber(0);
            result.setData(objectDataList);
//            objectDataList = SalesOrderUtil.getToConfirmDataFromPG(user, getObjectApiName());
        }

        return result;
    }

    @Override
    public List<IObjectData> getTotalUnProcessedData(User user, LtoSessionBOCItemKeys sessionBOCItemKey) {
        List<IObjectData> objectDataList = Lists.newArrayList();
        if (sessionBOCItemKey == LtoSessionBOCItemKeys.TobeDeliveryCustomerOrder) {
            objectDataList = getToDeliveryData(user);
        }
        return objectDataList;
    }

    private QueryResult<IObjectData> getToDeliveryDataResult(User user) {
        QueryResult<IObjectData> result = new QueryResult<>();
        result.setTotalNumber(0);
        result.setData(Lists.newArrayList());
        if (checkDeliveryRole(user)) {
            int offset = 0;
            int limit = 2000;
            SearchTemplateQuery query = buildDeliveryQuery(offset, limit, user);
            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, getObjectApiName(), query);
            if(!Objects.isNull(queryResult)){
                return queryResult;
            }

        }
        return result;
    }


    private List<IObjectData> getToDeliveryData(User user) {
        List<IObjectData> dataList = Lists.newArrayList();
        if (checkDeliveryRole(user)) {
            int offset = 0;
            int limit = 1000;
            int loopCount = 0;
            SearchTemplateQuery query = buildDeliveryQuery(offset, limit, user,true);
            while (true) {
                if (MAX_LOOP_COUNT == loopCount) {
                    log.warn("getToDeliveryData reaches loop limit, limit:{}", MAX_LOOP_COUNT);
                    SFALtoBizLogUtil.sendAuditLog(SFALtoBizLogUtil.Arg.builder()
                            .action("sfa_loop_limit")
                            .objectApiNames(getObjectApiName())
                            .message("SalesOrderUnProcessedService.getToDeliveryData").build(), user);
                }
                loopCount++;
                QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, getObjectApiName(), query);
                if (!Objects.isNull(queryResult)) {
                    dataList.addAll(queryResult.getData());
                }
                if (Objects.isNull(queryResult)||queryResult.getTotalNumber() < 1000) {
                    break;
                }
                offset += limit;
                query.setOffset(offset);
            }
        }
        return dataList;
    }

    private boolean checkDeliveryRole(User user) {
        List<String> roleCodes = serviceFacade.getUserRole(user);
        if (CollectionUtils.notEmpty(roleCodes) && roleCodes.contains("00000000000000000000000000000020")) {
            return true;
        }
        return false;
    }

    private SearchTemplateQuery buildDeliveryQuery(int offset, int limit, User user) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = TodoUtils.getDeliveryFilters(user, checkDeliveryRole(user));
        query.setFilters(filters);
        query.setOffset(offset);
        query.setLimit(limit);
        return query;
    }

    private SearchTemplateQuery buildDeliveryQuery(int offset, int limit, User user,boolean hasDeliveryRole) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = TodoUtils.getDeliveryFilters(user, hasDeliveryRole);
        query.setFilters(filters);
        query.setOffset(offset);
        query.setLimit(limit);
        return query;
    }

}
