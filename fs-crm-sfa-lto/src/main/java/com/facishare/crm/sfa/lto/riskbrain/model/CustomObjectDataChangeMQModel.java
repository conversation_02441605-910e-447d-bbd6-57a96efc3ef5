package com.facishare.crm.sfa.lto.riskbrain.model;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.List;

/**
 *
 * 自定义对象: 对象级变更事件 对象消息格式
 * <AUTHOR>
 * @create 2018-11-01 2:44 PM
 */
@Data
public class CustomObjectDataChangeMQModel {

    private String tenantId;
    private String op;
    private String name;
    private List<Content> body;
    private Long bornTimestamp;

    @Data
    public static class Content{
        private String eventId;
        private FieldDescribe context;
        private String name;
        private String entityId;
        private String triggerType;
        private String objectId;
        private String batch;
        private JSONObject beforeTriggerData;
        private JSONObject afterTriggerData;

        @Override
        public String toString() {
            return "Content{" +
                    "eventId='" + eventId + '\'' +
                    ", entityId='" + entityId + '\'' +
                    ", triggerType='" + triggerType + '\'' +
                    ", objectId='" + objectId + '\'' +
                    ", batch='" + batch + '\'' +
                    '}';
        }
    }

    @Data
    public static class FieldDescribe{
        private String appId;
        private String tenantId;
        private String userId;
    }

}
