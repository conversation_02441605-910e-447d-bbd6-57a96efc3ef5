package com.facishare.crm.sfa.lto.utils;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.facishare.uc.api.model.enterprise.arg.BatchGetSimpleEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.arg.GetSimpleEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.BatchGetSimpleEnterpriseDataResult;
import com.facishare.uc.api.model.enterprise.result.GetSimpleEnterpriseDataResult;
import com.facishare.uc.api.model.fscore.SimpleEnterpriseData;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.ImmutableSet;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TenantUtil {
    @Autowired
    EnterpriseEditionService enterpriseEditionService;

    private static int env;
    private static final int NEED_GET_SIMPLE_ENTERPRISE_DATA_FLAG = 1;
    //配置信息地址 https://oss.foneshare.cn/cms/edit/config/15963
    public static final ImmutableSet<Integer> FACISHARE_CLOUD_FLAG = ImmutableSet.of(0, 1);

    static {
        ConfigFactory.getInstance().getConfig("variables_endpoint", iConfig ->
                env = iConfig.getInt("enterprise_environment")
        );
    }

    /**
     * 过滤非当前云企业
     *
     * @return 当前云企业列表
     */
    public List<String> batchGetSimpleEnterprise(List<String> tenantIds) {
        List<Integer> enterpriseIds = tenantIds.stream().map(this::getEnterpriseId).collect(Collectors.toList());
        BatchGetSimpleEnterpriseDataArg arg = new BatchGetSimpleEnterpriseDataArg();
        arg.setEnterpriseIds(enterpriseIds);
        BatchGetSimpleEnterpriseDataResult result = enterpriseEditionService.batchGetSimpleEnterpriseData(arg);
        if (result == null || CollectionUtils.isEmpty(result.getSimpleEnterpriseList())) {
            log.warn("未查询到当前云数据{}", JSON.toJSONString(enterpriseIds));
            return Lists.newArrayList();
        }
        return result.getSimpleEnterpriseList().stream()
                .filter(e -> e.getEnv() == env)
                .map(e -> String.valueOf(e.getEnterpriseId()))
                .collect(Collectors.toList());
    }

    /**
     * 是否是专属云企业
     *
     * @param tenantId 租户Id
     * @return boolean
     */
    public boolean isExclusiveCloudEnterprise(String tenantId) {
        Integer ei = getEnterpriseId(tenantId);
        if (ei == null) return true;
        if (NEED_GET_SIMPLE_ENTERPRISE_DATA_FLAG == env) {
            GetSimpleEnterpriseDataArg simpleEnterpriseDataArg = new GetSimpleEnterpriseDataArg();
            simpleEnterpriseDataArg.setEnterpriseId(ei);
            Optional<Integer> envOpt = Optional.ofNullable(enterpriseEditionService.getSimpleEnterpriseData(simpleEnterpriseDataArg))
                    .map(GetSimpleEnterpriseDataResult::getEnterpriseData)
                    .map(SimpleEnterpriseData::getEnv);
            //返回值为null，则视为分享云企业，继续消费
            return envOpt.map(rtnEvn -> !FACISHARE_CLOUD_FLAG.contains(rtnEvn)).orElse(false);
        }
        return false;
    }

    /**
     * 有效且是纷享云企业
     */
    public boolean isFoneShareEffective(String tenantId) {
        Integer ei = getEnterpriseId(tenantId);
        if (ei == null) return false;
        GetSimpleEnterpriseDataArg simpleEnterpriseDataArg = new GetSimpleEnterpriseDataArg();
        simpleEnterpriseDataArg.setEnterpriseId(ei);
        GetSimpleEnterpriseDataResult simpleEnterpriseDataResult = enterpriseEditionService.getSimpleEnterpriseData(simpleEnterpriseDataArg);
        boolean isEffective = null == simpleEnterpriseDataResult || null == simpleEnterpriseDataResult.getEnterpriseData()
                || simpleEnterpriseDataResult.getEnterpriseData().getRunStatus() < 4;
        if (!isEffective) {
            // 无效企业
            return false;
        }
        // 只在纷享云环境判断
        if (NEED_GET_SIMPLE_ENTERPRISE_DATA_FLAG == env) {
            Optional<Integer> envOpt = Optional.ofNullable(simpleEnterpriseDataResult)
                    .map(GetSimpleEnterpriseDataResult::getEnterpriseData)
                    .map(SimpleEnterpriseData::getEnv);
            // 空视为纷享云
            return envOpt.map(FACISHARE_CLOUD_FLAG::contains).orElse(true);
        }
        // 非纷享云环境
        return true;
    }

    @Nullable
    private Integer getEnterpriseId(String tenantId) {
        Integer ei;
        try {
            ei = Integer.parseInt(tenantId);
        } catch (Exception e) {
            log.warn("Invalid tenantId {} ", tenantId);
            return null;
        }
        return ei;
    }
}

