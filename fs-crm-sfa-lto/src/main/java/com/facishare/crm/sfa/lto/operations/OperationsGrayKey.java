package com.facishare.crm.sfa.lto.operations;

public class OperationsGrayKey {
    /**
     * 固定创建实例定时任务的执行时间：时，分
     */
    public static final String OPERATIONS_INSTANCE_CREATE_LOCAL_TIME = "operations_instance_create_local_time";
    /**
     * 立即执行实例创建
     */
    public static final String OPERATIONS_INSTANCE_CREATE_IMMEDIATELY = "operations_instance_create_immediately";
    /**
     * 启用中策略上限
     */
    public static final String OPERATIONS_STRATEGY_ENABLE_LIMIT = "operations_strategy_enable_limit";

    private OperationsGrayKey() {}
}
