package com.facishare.crm.sfa.lto.utils;

import com.alibaba.fastjson.JSONObject;

import javax.crypto.Cipher;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

/**
 * RSA⼯具
 */
public class RSARiskBrainTool {
    public static final String KEY_ALGORITHM = "RSA";
    public static final String RSA_CHARSET = "UTF-8";
    /**
     * RSA公钥加密
     *
     * @param str 加密字符串
     * @param encryptKey 公钥
     * @return 密⽂
     * @throws Exception 加密过程中的异常信息
     */
    public static String encryptStr(String str, String encryptKey) throws
            Exception {
        //base64编码的公钥
        byte[] decoded = java.util.Base64.getDecoder().decode(encryptKey) ;
        RSAPublicKey pubKey = (RSAPublicKey) KeyFactory.getInstance(KEY_ALGORITHM).generatePublic(new X509EncodedKeySpec(decoded));
        //RSA加密
        Cipher cipher = Cipher.getInstance(KEY_ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, pubKey);
        return new String(java.util.Base64.getEncoder().encode(cipher.doFinal(str.getBytes(RSA_CHARSET))));
    }
    /**
     Java 复制代码
     5
     * RSA私钥解密
     *
     * @param str 解密字符串
     * @param decryptKey 解密key
     * @return 铭⽂
     * @throws Exception 解密过程中的异常信息
     */
    public static String decryptStr(String str, String decryptKey) throws
            Exception {
        //64位解码加密后的字符串
        byte[] inputByte = java.util.Base64.getDecoder().decode(str.getBytes(RSA_CHARSET));
        //base64编码的私钥
        byte[] decoded = java.util.Base64.getDecoder().decode(decryptKey) ;
        RSAPrivateKey priKey = (RSAPrivateKey) KeyFactory.getInstance(KEY_ALGORITHM).generatePrivate(new PKCS8EncodedKeySpec(decoded));
        //RSA解密
        Cipher cipher = Cipher.getInstance(KEY_ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, priKey);
        return new String(cipher.doFinal(inputByte));
    }
    /**
     * 随机⽣成密钥对
     */
    public static KeyResult genKeyPair() {
        try {
            // KeyPairGenerator类⽤于⽣成公钥和私钥对，基于RSA算法⽣成对象
            KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance(KEY_ALGORITHM);
            // 初始化密钥对⽣成器
            keyPairGen.initialize(2048, new SecureRandom());
            // ⽣成⼀个密钥对，保存在keyPair中
            KeyPair keyPair = keyPairGen.generateKeyPair();
            // 得到私钥
            PrivateKey privateKey = keyPair.getPrivate();
            // 得到公钥
            PublicKey publicKey = keyPair.getPublic();
            String publicKeyString = new String(java.util.Base64.getEncoder().encode(publicKey.getEncoded()));
            // 得到私钥字符串
            String privateKeyString = new String(java.util.Base64.getEncoder().encode((privateKey.getEncoded())));
            return new KeyResult(publicKeyString, privateKeyString);
        } catch (NoSuchAlgorithmException e) {
            //测试代码
            throw new RuntimeException(e);
        }
    }

    public static void main(String[] ewew){
        System.out.println(JSONObject.toJSONString(RSARiskBrainTool.genKeyPair()));
    }
    static class KeyResult {
        private String publicKeyString;
        private String privateKeyString;
        public String getPublicKeyString() {
            return publicKeyString;
        }
        public void setPublicKeyString(String publicKeyString) {
            this.publicKeyString = publicKeyString;
        }
        public String getPrivateKeyString() {
            return privateKeyString;
        }
        public void setPrivateKeyString(String privateKeyString) {
            this.privateKeyString = privateKeyString;
        }
        public KeyResult(String publicKeyString, String privateKeyString) {
            this.publicKeyString = publicKeyString;
            this.privateKeyString = privateKeyString;
        }
    } }
