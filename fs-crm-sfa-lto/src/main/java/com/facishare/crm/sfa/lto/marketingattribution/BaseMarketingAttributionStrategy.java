package com.facishare.crm.sfa.lto.marketingattribution;

import com.facishare.crm.sfa.lto.utils.ObjectDataUtil;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IObjectReferenceField;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.facishare.crm.sfa.lto.marketingattribution.models.MarketingAttributionConstants.MARKETING_EVENT_INFLUENCE_OBJ;

@Slf4j
public abstract class BaseMarketingAttributionStrategy implements IMarketingAttributionStrategy{
    @Autowired
    private ServiceFacade serviceFacade;

    protected Map<Long, List<IObjectData>> getSortedDataMap(List<IObjectData> objectDataList) {
        if(CollectionUtils.isEmpty(objectDataList)) {
            return Maps.newHashMap();
        }
        Map<Long, List<IObjectData>> result = Maps.newHashMap();
        for(IObjectData item : objectDataList) {
            Object objValue = item.get("begin_time");
            if(objValue == null) {
                continue;
            }
            Long beginTime = Long.valueOf(String.valueOf(objValue));
            if(result.containsKey(beginTime)) {
                result.get(beginTime).add(item);
            } else {
                result.put(beginTime, Lists.newArrayList(item));
            }
        }
        return  result;
    }

    protected List<IObjectData> getFistStageDataList(List<IObjectData> objectDataList) {
        Map<Long, List<IObjectData>> sortedDataMap = getSortedDataMap(objectDataList);
        List<Long> beginTimeList = Lists.newArrayList(sortedDataMap.keySet());
        Collections.sort(beginTimeList);
        Long firstTime = beginTimeList.get(0);
        return sortedDataMap.get(firstTime);
    }

    protected List<IObjectData> getLastStageDataList(List<IObjectData> objectDataList, boolean exceptFirst) {
        Map<Long, List<IObjectData>> sortedDataMap = getSortedDataMap(objectDataList);
        if(sortedDataMap.keySet().size() == 1 && exceptFirst) {
            return Lists.newArrayList();
        }
        List<Long> beginTimeList = Lists.newArrayList(sortedDataMap.keySet());
        Collections.sort(beginTimeList);
        Long lastTime = beginTimeList.get(beginTimeList.size() - 1);
        return sortedDataMap.get(lastTime);
    }

    protected List<IObjectData> getMiddleStageDataList(List<IObjectData> objectDataList) {
        Map<Long, List<IObjectData>> sortedDataMap = getSortedDataMap(objectDataList);
        List<Long> beginTimeList = Lists.newArrayList(sortedDataMap.keySet());
        Collections.sort(beginTimeList);
        List<IObjectData> result = Lists.newArrayList();
        for(int index = 1; index < beginTimeList.size() - 1; ++index) {
            result.addAll(sortedDataMap.get(beginTimeList.get(index)));
        }
        return result;
    }

    protected List<Double> calculateRatio (Double totalValue, int divCount) {
        if(divCount <= 1) {
        	totalValue = BigDecimal.valueOf(totalValue)
					.setScale(2, BigDecimal.ROUND_UNNECESSARY).doubleValue();
            return Lists.newArrayList(totalValue, totalValue);
        }

		BigDecimal total = BigDecimal.valueOf(totalValue);
		BigDecimal divide = total.divide(BigDecimal.valueOf(divCount), 2, RoundingMode.HALF_UP);

		BigDecimal tempTotalValue = BigDecimal.ZERO;
		for(int index = 0; index < divCount -1; ++index) {
			tempTotalValue = tempTotalValue.add(divide);
		}
		BigDecimal lastValue = total.subtract(tempTotalValue)
				.setScale(2, BigDecimal.ROUND_UNNECESSARY);
		return Lists.newArrayList(divide.doubleValue(), lastValue.doubleValue());
    }

    protected void createMarketingEventInfluence(RequestContext context, IObjectData triggerData, IObjectData strategyRule, List<IObjectData> attributionDataList, List<Double> ratioValues) {
        String objectIdKey = getFiledApiNameByObjectApiName(context.getTenantId(), triggerData.getDescribeApiName());
        if(StringUtils.isBlank(objectIdKey)) {
            return;
        }

        for(int index = 0; index < attributionDataList.size(); ++index) {
            IObjectData objectData = attributionDataList.get(index);
            IObjectData marketingEventInfluenceData = new ObjectData();
            marketingEventInfluenceData.set("marketing_event_id", objectData.getId());
            marketingEventInfluenceData.set(objectIdKey, triggerData.getId());
            if(index == attributionDataList.size() - 1) {
                marketingEventInfluenceData.set("attribution_percent", ratioValues.get(1));
            } else {
                marketingEventInfluenceData.set("attribution_percent", ratioValues.get(0));
            }
            marketingEventInfluenceData.set("marketing_attribution_id", strategyRule.getId());
            marketingEventInfluenceData.set("owner", strategyRule.getOwner());
            marketingEventInfluenceData.set("data_own_department", strategyRule.getDataOwnDepartment());
            marketingEventInfluenceData.set("created_by", Lists.newArrayList(strategyRule.getCreatedBy()));
            marketingEventInfluenceData.set("object_describe_id", null);
            marketingEventInfluenceData.set("object_describe_api_name", MARKETING_EVENT_INFLUENCE_OBJ);
            marketingEventInfluenceData.set("record_type", "default__c");
            BaseObjectSaveAction.Arg arg = new BaseObjectSaveAction.Arg();
            arg.setObjectData(ObjectDataDocument.of(marketingEventInfluenceData));
            ActionContext actionContext = new ActionContext(context, MARKETING_EVENT_INFLUENCE_OBJ, ObjectAction.CREATE.getActionCode());
            actionContext.setAttribute("triggerFlow", false);
            actionContext.setAttribute("skipBaseValidate", true);
            actionContext.setAttribute("isSpecifyCreatedBy", true);
            serviceFacade.triggerAction(actionContext, arg, BaseObjectSaveAction.Result.class);
        }
    }

    protected void deleteMarketingEventInfluenceData (String tenantId, String apiName, String objectId) {
        String objectIdKey = getFiledApiNameByObjectApiName(tenantId, apiName);
        if(StringUtils.isBlank(objectIdKey)) {
            return;
        }
        int maxExecuteCount = 10000;
        int executeCount = 0;
        User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
        IObjectDescribe objectDescribe = this.serviceFacade.findObject(user.getTenantId(), MARKETING_EVENT_INFLUENCE_OBJ);
        while (true) {
            try {
                ++executeCount;
                SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
                searchTemplateQuery.setLimit(100);
                searchTemplateQuery.setFindExplicitTotalNum(false);
                searchTemplateQuery.setNeedReturnCountNum(false);
                searchTemplateQuery.setPermissionType(0);
                List<IFilter> filters = Lists.newArrayList();
                SearchUtil.fillFilterEq(filters, Tenantable.TENANT_ID, tenantId);
                SearchUtil.fillFilterEq(filters, "object_describe_api_name", MARKETING_EVENT_INFLUENCE_OBJ);
                SearchUtil.fillFilterEq(filters, objectIdKey, objectId);
                SearchUtil.fillFilterGTE(filters, "is_deleted", 0);
                searchTemplateQuery.setFilters(filters);
                searchTemplateQuery.setSearchSource("db");
                QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, MARKETING_EVENT_INFLUENCE_OBJ, searchTemplateQuery);
                if(queryResult == null || CollectionUtils.isEmpty(queryResult.getData())) {
                    return;
                }
                serviceFacade.bulkInvalidAndDeleteWithSuperPrivilege(queryResult.getData(), user);
                dealWithDetail(user, objectDescribe, queryResult);
                if (executeCount >= maxExecuteCount) {
                    return;
                }
            } catch (Exception e) {
                log.error("deleteMarketingEventInfluenceData error", e);
                throw e;
            }
        }
    }

    private void dealWithDetail(User user, IObjectDescribe objectDescribe, QueryResult<IObjectData> queryResult) {
        try {
            //删除从对象
            ObjectDataUtil.dealWithDetail(queryResult.getData(), user, objectDescribe);
        } catch (Exception e) {
            log.error("dealWithDetail error:", e);
        }
    }

    protected String getFiledApiNameByObjectApiName(String tenantId, String apiName) {
        IObjectDescribe objectDescribe = serviceFacade.findObject(tenantId, MARKETING_EVENT_INFLUENCE_OBJ);
        if(objectDescribe == null) {
            return null;
        }
        Optional<IFieldDescribe> fieldDescribe = objectDescribe.getFieldDescribes().stream().filter(x -> "package".equals(x.getDefineType())
                && x instanceof IObjectReferenceField && apiName.equals(((IObjectReferenceField)x).getTargetApiName())).findFirst();
        if(fieldDescribe.isPresent()) {
            return fieldDescribe.get().getApiName();
        }
        return null;
    }
}
