package com.facishare.crm.sfa.lto.marketingattribution;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.expression.SFAExpressionServiceImpl;
import com.facishare.crm.sfa.lto.common.models.LtoFieldApiConstants;
import com.facishare.crm.sfa.lto.exception.ExceptionUtil;
import com.facishare.crm.sfa.lto.marketingattribution.models.MarketingAttributionModel;
import com.facishare.crm.sfa.lto.utils.CommonUtil;
import com.facishare.crm.sfa.lto.utils.ObjectDataUtil;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.lto.marketingattribution.models.MarketingAttributionConstants.*;

@Component
@Slf4j
public class MarketingAttributionService {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private ObjectDataServiceImpl objectDataService;
    @Autowired
    private MarketingAttributionStrategyManager marketingAttributionStrategyManager;
    @Autowired
    private SFAExpressionServiceImpl sfaExpressionService;
    private static final FsGrayReleaseBiz gray = FsGrayRelease.getInstance("sfa");
    private static final Long ONE_DAY_TIME_STAMP = 24 * 60 * 60 * 1000L;
    private static final List<String> normalDataLifeStatus = Lists.newArrayList(LtoFieldApiConstants.STATUS_NORMAL, "in_change");

    private static final String NEW_OPPORTUNITY_OBJ ="NewOpportunityObj";

    public MarketingAttributionModel.Result processMarketingAttribution(ServiceContext context, MarketingAttributionModel.CalculateMarketingAttributionArg arg) {
        String apiName = arg.getObjectApiName();
        String tenantId = context.getTenantId();
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIds(tenantId, arg.getObjectIds(), apiName);
        processMarketingAttribution(context.getRequestContext(), apiName, objectDataList);
        return new MarketingAttributionModel.Result();
    }

    public void handleMq(String tenantId,List<String> opportunityIds,List<String> salesOrderIds){
        try {
            RequestContext requestContext = RequestContext.builder().user(new User(tenantId, "-10000", "", "")).build();
            if(CollectionUtils.isNotEmpty(opportunityIds)) {
                List<IObjectData> calculateDataList = serviceFacade.findObjectDataByIds(tenantId, Lists.newArrayList(opportunityIds), NEW_OPPORTUNITY_OBJ);
                processMarketingAttribution(requestContext, NEW_OPPORTUNITY_OBJ, calculateDataList);
            }

            if(CollectionUtils.isNotEmpty(salesOrderIds)) {
                List<IObjectData> calculateDataList = serviceFacade.findObjectDataByIds(tenantId, Lists.newArrayList(salesOrderIds), Utils.SALES_ORDER_API_NAME);
                processMarketingAttribution(requestContext, Utils.SALES_ORDER_API_NAME, calculateDataList);
            }
        }catch (Exception e){
            log.error("MarketingAttributionService handleMq error tenantId:{},e:",tenantId,e);
        }
    }
    public void processMarketingAttribution(RequestContext context, String apiName, List<IObjectData> objectDataList) {
        try {
            if(CollectionUtils.isEmpty(objectDataList)) {
                return;
            }
            String tenantId = context.getTenantId();
            if(!isGrayMarketingAttribution(tenantId)) {
                return;
            }
            List<IObjectData> marketingAttributionRules = getMarketingAttributionRules(tenantId, apiName);
            if(CollectionUtils.isEmpty(marketingAttributionRules)) {
                return;
            }
            List<IObjectData> sortedStrategyRules = getSortedStrategyRules(marketingAttributionRules);
            IObjectDescribe objectDescribe = serviceFacade.findObject(tenantId, apiName);
            IObjectDescribe marketingEventDescribe = serviceFacade.findObject(tenantId, MARKETING_EVENT_OBJ);
            for(IObjectData objectData : objectDataList) {
                IObjectData ruleData = getStrategyRuleData(objectDescribe, objectData, sortedStrategyRules);
                if(ruleData == null) {
                    continue;
                }
                processMarketingAttribution(context, marketingEventDescribe, objectData, ruleData);
            }
        } catch (Exception e) {
            log.error("MarketingAttributionHandler error", e);
            throw e;
        }
    }

    private boolean isGrayMarketingAttribution(String tenantId) {
        return gray.isAllow("marketing_attribution_enable", tenantId);
    }

    protected List<IObjectData> getMarketingAttributionRules (String tenantId, String apiName) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(100);
        searchTemplateQuery.setFindExplicitTotalNum(false);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setPermissionType(0);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, Tenantable.TENANT_ID, tenantId);
        SearchUtil.fillFilterEq(filters, LtoFieldApiConstants.OBJECT_DESCRIBE_API_NAME, MARKETING_ATTRIBUTION_OBJ);
        SearchUtil.fillFilterEq(filters, "trigger_object_api_name", apiName);
        SearchUtil.fillFilterEq(filters, LtoFieldApiConstants.BIZ_STATUS, LtoFieldApiConstants.STATUS_NORMAL);
        SearchUtil.fillFilterEq(filters, LtoFieldApiConstants.LIFE_STATUS, LtoFieldApiConstants.STATUS_NORMAL);
        searchTemplateQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(new User(tenantId, User.SUPPER_ADMIN_USER_ID), MARKETING_ATTRIBUTION_OBJ, searchTemplateQuery);
        return queryResult.getData();
    }

    private void processMarketingAttribution(RequestContext context, IObjectDescribe marketingEventDescribe, IObjectData objectData, IObjectData ruleData) {
        try {
            String tenantId = context.getTenantId();
            String triggerAction = ObjectDataUtil.getStringValue(ruleData, "trigger_action", "");
            switch (triggerAction) {
                case "add":
                    String lifeStatus = ObjectDataUtil.getStringValue(objectData, LtoFieldApiConstants.LIFE_STATUS, "");
                    if(!normalDataLifeStatus.contains(lifeStatus)) {
                        return;
                    }
                    break;
                case "win":
                    String salesStatus = ObjectDataUtil.getStringValue(objectData, "sales_status", "");
                    if(!"2".equals(salesStatus)) {
                        return;
                    }
                    break;
                default:
                    return;
            }
            String attributionModel = ObjectDataUtil.getStringValue(ruleData, "attribution_model", "");
            if (StringUtils.isBlank(attributionModel)) {
                return;
            }
            String retroactiveBeginTime = ObjectDataUtil.getStringValue(ruleData, "retroactive_begin_time", "");
            Long endTime;
            switch (retroactiveBeginTime) {
                case "add":
                    endTime = objectData.getCreateTime();
                    break;
                case "win":
                    endTime = ObjectDataUtil.getLongValue(objectData, "stg_changed_time", objectData.getCreateTime());
                    break;
                default:
                    return;
            }
            endTime = getTimeMillis(endTime);
            String retroactiveDataCondition = ObjectDataUtil.getStringValue(ruleData, "retroactive_data_condition", "[]");
            Integer retroactivePeriod = ObjectDataUtil.getIntegerValue(ruleData, "retroactive_period", 0);
            Long startTime = endTime - (retroactivePeriod) * ONE_DAY_TIME_STAMP;
            endTime = endTime + ONE_DAY_TIME_STAMP - 1;
            List<IObjectData> marketingEventDataList = getMarketingEventDataList(tenantId, objectData, startTime, endTime);
            if (CollectionUtils.isEmpty(marketingEventDataList)) {
                return;
            }
            IMarketingAttributionStrategy marketingAttributionStrategy = marketingAttributionStrategyManager.getStrategy(attributionModel);
            if (marketingAttributionStrategy == null) {
                return;
            }
            List<String> dataIds = marketingEventDataList.stream().map(x -> ObjectDataUtil.getStringValue(x, "marketing_event_id", "")).collect(Collectors.toList());
            List<IObjectData> dataList = serviceFacade.findObjectDataByIds(tenantId, dataIds, MARKETING_EVENT_OBJ);
            if (CollectionUtils.isEmpty(dataList)) {
                return;
            }
            List<IObjectData> matchDataList = Lists.newArrayList();
            if (!StringUtils.isBlank(retroactiveDataCondition)) {
                for (IObjectData item : dataList) {
                    Boolean isMatch = sfaExpressionService.evaluate(retroactiveDataCondition, item, marketingEventDescribe);
                    if (isMatch != null && isMatch) {
                        matchDataList.add(item);
                    }
                }
            }
            if (CollectionUtils.isEmpty(matchDataList)) {
                return;
            }
            marketingAttributionStrategy.process(context, objectData, ruleData, matchDataList);
        } catch (Exception e) {
            log.error("MarketingAttributionHandler error", e);
            throw e;
        }
    }

    private IObjectData getStrategyRuleData(IObjectDescribe objectDescribe, IObjectData objectData, List<IObjectData> sortedMarketingAttributionRules) {
        if(CollectionUtils.isEmpty(sortedMarketingAttributionRules)) {
            return null;
        }
        for (IObjectData ruleData : sortedMarketingAttributionRules) {
            String dataCondition = ObjectDataUtil.getStringValue(ruleData, "trigger_data_condition", "[]");
            if(StringUtils.isBlank(dataCondition)) {
                return ruleData;
            }
            try {
                Boolean isMatch = sfaExpressionService.evaluate(dataCondition, objectData, objectDescribe);
                if(isMatch != null && isMatch){
                    return ruleData;
                }
            } catch (Exception e) {
                log.error("MarketingAttributionHandler evaluate error, apiName:{},dataId:{},wheres:{}", objectDescribe.getApiName(), objectData.getId(), dataCondition, e);
                ExceptionUtil.throwCommonBusinessException();
            }
        }
        return null;
    }

    private List<IObjectData> getSortedStrategyRules(List<IObjectData> marketingAttributionRules) {
        if(CollectionUtils.isEmpty(marketingAttributionRules) || marketingAttributionRules.size() < 2) {
            return marketingAttributionRules;
        }
        List<IObjectData> result = Lists.newArrayList();
        Set<Integer> prioritySet = marketingAttributionRules.stream().map(x -> ObjectDataUtil.getIntegerValue(x, "priority", 0)).collect(Collectors.toSet());
        List<Integer> priorityList = Lists.newArrayList(prioritySet);
        Collections.sort(priorityList);
        for(Integer priority : priorityList) {
            result.addAll(marketingAttributionRules.stream().filter(x -> Objects.equals(priority, ObjectDataUtil.getIntegerValue(x, "priority", 0))).collect(Collectors.toList()));
        }
        return result;
    }

    private List<IObjectData> getMarketingEventDataList(String tenantId, IObjectData objectData, Long startTime, Long endTime) {
        if(objectData == null) {
            return Lists.newArrayList();
        }
        String accountId = ObjectDataUtil.getStringValue(objectData, LtoFieldApiConstants.ACCOUNT_ID, "");
        String leadsId = ObjectDataUtil.getStringValue(objectData, LtoFieldApiConstants.LEADS_ID, "");
        List<String> accountIds = Lists.newArrayList();
        List<String> contactIds = Lists.newArrayList();
        List<String> leadsIds = Lists.newArrayList();
        if(!StringUtils.isBlank(accountId)) {
            accountIds.add(accountId);
            contactIds.addAll(getContactIdsByAccountIds(tenantId, accountIds));
            leadsIds.addAll(getLeadsIdsByAccountIds(tenantId, accountIds));
        }
        if(!CollectionUtils.isEmpty(contactIds)) {
            leadsIds.addAll(getLeadsIdsByContactIds(tenantId, contactIds));
        }
        if(!StringUtils.isBlank(leadsId)) {
            leadsIds.add(leadsId);
        }
        if(CollectionUtils.isEmpty(accountIds) && CollectionUtils.isEmpty(leadsIds) && CollectionUtils.isEmpty(contactIds)) {
            return Lists.newArrayList();
        }
        String queryString = String.format("SELECT CampaignMembersObj.marketing_event_id,MarketingEventObj.begin_time," +
               "MarketingEventObj.end_time FROM biz_campaign_members CampaignMembersObj INNER JOIN biz_marketing_event MarketingEventObj " +
               "ON CampaignMembersObj.tenant_id = MarketingEventObj.tenant_id AND CampaignMembersObj.marketing_event_id = MarketingEventObj.id " +
               "WHERE CampaignMembersObj.tenant_id='%s' AND CampaignMembersObj.is_deleted=0 AND CampaignMembersObj.life_status IN ('normal','in_change') " +
               "AND MarketingEventObj.is_deleted=0 AND MarketingEventObj.life_status IN ('normal','in_change') " +
               "AND ((MarketingEventObj.begin_time BETWEEN %s AND %s) OR (MarketingEventObj.end_time BETWEEN %s AND %s) OR (MarketingEventObj.begin_time<%s AND MarketingEventObj.end_time>%s)) AND (" ,
                tenantId, startTime, endTime, startTime, endTime, startTime, endTime);
        List<String> orCondition = Lists.newArrayList();
        if(!CollectionUtils.isEmpty(accountIds)) {
            String accountIdString = CommonUtil.buildSqlInString(accountIds);
            orCondition.add(String.format("CampaignMembersObj.account_id=any(array[%s])", accountIdString));
        }
        if(!CollectionUtils.isEmpty(leadsIds)) {
            String leadsIdString = CommonUtil.buildSqlInString(leadsIds);
            orCondition.add(String.format("CampaignMembersObj.leads_id=any(array[%s])", leadsIdString));
        }
        if(!CollectionUtils.isEmpty(contactIds)) {
            String contactIdString = CommonUtil.buildSqlInString(contactIds);
            orCondition.add(String.format("CampaignMembersObj.contact_id=any(array[%s])", contactIdString));
        }
        String orConditionString = String.join(" OR ", orCondition);
        queryString = queryString + orConditionString + ") ";
        List<IObjectData> result = Lists.newArrayList();
        try {
            List<Map> queryResult = objectDataService.findBySql(tenantId, queryString);
            for(Map dataMap : queryResult) {
                IObjectData item = ObjectDataExt.of(dataMap);
                result.add(item);
            }
        } catch (Exception e) {
            log.error("MarketingAttributionHandler getMarketingEventDataList error", e);
        }

        return result;
    }

    private List<String> getContactIdsByAccountIds(String tenantId, List<String> accountIds) {
        List<String> result = Lists.newArrayList();
        try {
            String accountIdString = CommonUtil.buildSqlInString(accountIds);
            String queryString = String.format("SELECT id FROM biz_contact WHERE tenant_id='%s' " +
                            "AND is_deleted=0 AND account_id= any(array[%s]) AND  life_status IN ('normal','in_change') ",
                    tenantId, accountIdString);
            List<Map> queryResult = objectDataService.findBySql(tenantId, queryString);
            List<String> contactIds = queryResult.stream().map(x -> x.get("id").toString()).collect(Collectors.toList());
            result.addAll(contactIds);
        } catch (Exception e) {
            log.error("MarketingAttributionHandler getContactIdsByAccountIds error", e);
        }
        return result;
    }

    private List<String> getLeadsIdsByAccountIds(String tenantId, List<String> accountIds) {
        List<String> result = Lists.newArrayList();
        try {
            String accountIdString = CommonUtil.buildSqlInString(accountIds);
            String queryString = String.format("SELECT leads_id FROM biz_leads_transfer_log WHERE tenant_id='%s' " +
                            "AND is_deleted=0 AND account_id= any(array[%s]) ",
                    tenantId, accountIdString);
            List<Map> queryResult = objectDataService.findBySql(tenantId, queryString);
            List<String> leadsIds = queryResult.stream().map(x -> x.get(LtoFieldApiConstants.LEADS_ID).toString()).collect(Collectors.toList());
            result.addAll(leadsIds);
        } catch (Exception e) {
            log.error("MarketingAttributionHandler getLeadsIdsByAccountIds error", e);
        }
        return result;
    }

    private List<String> getLeadsIdsByContactIds(String tenantId, List<String> contactIds) {
        List<String> result = Lists.newArrayList();
        try {
            String contactIdString = CommonUtil.buildSqlInString(contactIds);
            String queryString = String.format("SELECT leads_id FROM biz_leads_transfer_log WHERE tenant_id='%s' " +
                            "AND is_deleted=0 AND contact_id= any(array[%s]) ",
                    tenantId, contactIdString);
            List<Map> queryResult = objectDataService.findBySql(tenantId, queryString);
            List<String> leadsIds = queryResult.stream().map(x -> x.get(LtoFieldApiConstants.LEADS_ID).toString()).collect(Collectors.toList());
            result.addAll(leadsIds);
        } catch (Exception e) {
            log.error("MarketingAttributionHandler getLeadsIdsByContactIds error", e);
        }
        return result;
    }

    private Long getTimeMillis(Long time) {
        Date date = new Date(time);
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String dateString = format.format(date);
        try {
            date = format.parse(dateString);
        } catch (Exception e) {
            log.error("MarketingAttributionHandler getTimeMillis error", e);
        }
        return date.getTime();
    }
}
