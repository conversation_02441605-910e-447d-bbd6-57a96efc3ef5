package com.facishare.crm.sfa.lto.maincontrolstrategy;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.lto.maincontrolstrategy.models.MainControlStrategyModels;
import com.facishare.crm.sfa.lto.utils.CommonSqlUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> lik
 * @date : 2023/4/11 15:12
 */
@Component
@Slf4j
public class MainControlStrategySerivce {

    @Autowired
    private ServiceFacade serviceFacade;

    private static final Integer NOT_INHERIT_TYPE_FLAG = 3;

    private static String mainDataSupportObjectApiName = "";

    static {
        ConfigFactory.getConfig("fs-crm-sfa_rfm-config", config -> {
            mainDataSupportObjectApiName = config.get("main_data_support_object_api_name", "");
        });
    }

    public void execute(MainControlStrategyModels.TaskArg taskArg){
        if(!"field_update".equals(taskArg.getOp()) || ObjectUtils.isEmpty(mainDataSupportObjectApiName)){
            return;
        }
        List<Map> body = taskArg.getBody();
        body.stream().forEach(field->{
            if(ObjectUtils.isEmpty(field) || !field.containsKey(MainControlStrategyModels.Field.OBJECT_DESCRIBE_API_NAME)
                    || ObjectUtils.isEmpty(field.get(MainControlStrategyModels.Field.OBJECT_DESCRIBE_API_NAME))
                    || !field.containsKey(MainControlStrategyModels.Field.FIELDS)
                    || ObjectUtils.isEmpty(field.get(MainControlStrategyModels.Field.FIELDS))){
                return;
            }
            String objectDescribeApiName = field.get(MainControlStrategyModels.Field.OBJECT_DESCRIBE_API_NAME).toString();

            if(!mainDataSupportObjectApiName.contains(objectDescribeApiName)){
                return;
            }
            IObjectDescribe describe = serviceFacade.findObject(taskArg.getTenantId(), objectDescribeApiName);
            if(ObjectUtils.isEmpty(describe)){
                return;
            }
            List<Map> fields = (List<Map>)field.get(MainControlStrategyModels.Field.FIELDS);
            fields.stream().forEach(fieldDetail->{
                if(ObjectUtils.isEmpty(fieldDetail) || !fieldDetail.containsKey(MainControlStrategyModels.Field.FIELD_TYPE)
                        || ObjectUtils.isEmpty(fieldDetail.get(MainControlStrategyModels.Field.FIELD_TYPE))
                        || !fieldDetail.containsKey(MainControlStrategyModels.Field.FIELD_API_NAME)
                        || ObjectUtils.isEmpty(fieldDetail.get(MainControlStrategyModels.Field.FIELD_API_NAME))){
                    return;
                }
                String fieldType = fieldDetail.get(MainControlStrategyModels.Field.FIELD_TYPE).toString();
                String fieldApiName = fieldDetail.get(MainControlStrategyModels.Field.FIELD_API_NAME).toString();
                IFieldDescribe iFieldDescribe = describe.getFieldDescribe(fieldApiName);

                if(ObjectUtils.isEmpty(iFieldDescribe.getInheritType()) || !NOT_INHERIT_TYPE_FLAG.equals(iFieldDescribe.getInheritType()) ){
                    return;
                }

                Integer index = 0;
                while (true){
                    List<Map> list = getControlStrategy(taskArg.getTenantId(),index * 200,objectDescribeApiName);
                    if(CollectionUtils.isEmpty(list)){
                        return;
                    }
                    List<Map<String, Object>> updateSql = new ArrayList<>();
                    list.stream().forEach(x->{
                        List<Map> fieldSettingsList = JSONObject.parseArray(x.get("field_settings").toString(),Map.class);
                        List<Map> fieldSettingsListNew = fieldSettingsList.stream().filter(m->!fieldApiName.equals(m.get("api_name").toString())).collect(Collectors.toList());
                        if(fieldSettingsList.size()!=fieldSettingsListNew.size()){
                            Map<String,Object> mapUpdate = new HashMap<>();
                            mapUpdate.put("id",x.get("id"));
                            mapUpdate.put("field_settings",JSONObject.toJSONString(fieldSettingsListNew));
                            updateSql.add(mapUpdate);
                        }
                    });

                    if(CollectionUtils.isNotEmpty(updateSql)){
                        try {
                            CommonSqlUtil.batchUpdate(taskArg.getTenantId(),"biz_main_control_strategy",updateSql, Lists.newArrayList("id","tenant_id"));
                        }catch (Exception e){
                            log.error("MainControlStrategySerivce batchUpdate error tenantId:{},e",taskArg.getTenantId(),e);
                        }
                    }
                    if(index>1000){
                        return;
                    }
                    index++;
                }
            });

        });

    }

    public List<Map> getControlStrategy(String tenantId,Integer index,String objectApiName){
        String findSql = String.format("SELECT id,field_settings from biz_main_control_strategy where tenant_id='%s' and object_api_name ='%s'  and is_deleted =0  AND settings_type ='field_name' ORDER BY create_time ASC OFFSET %s LIMIT 200;",tenantId,objectApiName,index);
       return CommonSqlUtil.findBySql(tenantId,findSql);
    }
}
