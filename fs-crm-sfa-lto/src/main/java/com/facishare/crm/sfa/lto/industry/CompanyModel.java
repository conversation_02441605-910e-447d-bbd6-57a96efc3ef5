package com.facishare.crm.sfa.lto.industry;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/4/1 18:39
 * @description:
 */
public interface CompanyModel {

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class CompanyDetailArg {
        private String companyId;
        private String companyName;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class CompanyDetailResult{
        private CompanyBusinessInfo companyBusinessInfo;
    }

    /**
     * "companyManagerInfo": {
     *             "managers": [],
     *             "total": 0
     *         },
     *         "companyShareholderInfo": {
     *             "companyShareholders": [],
     *             "total": 0
     *         },
     *         "companyBranchInfo": {
     *             "companyBranchs": [],
     *             "total": 0
     *         },
     *         "companyInvestsInfo": {
     *             "companyInvestsObjects": [],
     *             "total": 0
     *         },
     *         "companyStockInfo": null,
     *         "companyAssessmentInfo": null,
     *         "companyFamilyTreeInfo": null,
     *         "labels": [
     *             "高新技术企业",
     *             "专精特新中小企业",
     *             "独角兽企业"
     *         ]
     */

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class CompanyBusinessInfo {
        private CompanyBaseInfo companyBaseInfo;
        private CompanyManagerInfo companyManagerInfo;
        private CompanyShareholderInfo companyShareholderInfo;
        private CompanyBranchInfo companyBranchInfo;
        private CompanyInvestsInfo companyInvestsInfo;
        private CompanyStockInfo companyStockInfo;
        private CompanyAssessmentInfo companyAssessmentInfo;
        private CompanyFamilyTreeInfo companyFamilyTreeInfo;
        private List<String> labels;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class CompanyBaseInfo {
        private NormalObject normalObject;
        private HkObject hkObject;
        private GovUnitObject govUnitObject;
        private NpoObject npoObject;
        private String dataSource;
        private TitleObject titleObject;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class CompanyShareholderInfo {
        private List<CompanyShareholderObject> companyShareholders;
        private int total;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class CompanyShareholderObject{
        private String stockId;

        /**
         * 公司股东
         */
        private String companyShareholder;

        /**
         * 认缴出资额度
         */
        private String submitContributionAmount;

        /**
         * 实缴出资额度
         */
        private String actualContributionAmount;

        /**
         * 实缴时间
         */
        private String actualSubmitTime;

        /**
         * 股东类型
         */
        private String stockType;

        /**
         * 股东证件类型
         */
        private String identifyType;

        /**
         * 股东证件号码
         */
        private String identifyNo;

        /**
         * 认缴出资记录
         */
        private String pays;

        /**
         * 实缴出资记录
         */
        private String realPays;
    }

    @Data
    class CompanyBranchInfo {
    }

    @Data
    class CompanyInvestsInfo {
    }

    @Data
    class CompanyStockInfo {
    }
    @Data
    class CompanyAssessmentInfo {
    }

    @Data
    class CompanyFamilyTreeInfo {
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class NormalObject {
        private String companyId;
        private String companyName;
        private String operName;
        private String registNo;
        private String organizationalCode;
        private String status;
        private String registCapi;
        private String econKind;
        private String started;
        private String province;
        private String industryName;
        private String registAddress;
        private String termStart;
        private String termEnd;
        private String term;
        private String belongOrg;
        private String checked;
        private String scope;
        private String shortName;
        private String logo;
        private String telephone;
        private String mail;
        private String url;
        private String actualCapitalAmount;
        private String identificationNumber;
        private String qualificationType;
        private String historyNames;
        private String nameEn;
        private String employeesNum;
        private String socialSecurityStaffNum;
        private String dunsCode;
        private String principalName;
        private String principalJob;
        private String sales;
        private String salesUnit;
        private String listingStatus;
        private String postalCode;
        private String SocialCreditCode;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class HkObject {
        private String chairman;
        private String nameEn;
        private String companyNum;
        private String companyOrgTypeS;
        private String estiblishTime;
        private String regStatusS;
        private String importantItemsS;
        private String nameCn;
        private String cid;
        private String logo;
        private String listingStatus;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class GovUnitObject {
        private String legalPerson;
        private String regCapital;
        private String usCreditCode;
        private String expendSource;
        private String regStatus;
        private String holdUnit;
        private String oldCert;
        private String regUnit;
        private String validTime;
        private String address;
        private String scope;
        private String name;
        private String regTime;
        private String cid;
        private String logo;
        private String listingStatus;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class NpoObject {
        private String legalPerson;
        private String types;
        private String unifiedSocialCreditCode;
        private String registeredCapital;
        private String status;
        private String registrationAuthority;
        private String registrationDate;
        private String businessUnit;
        private String expiryDate;
        private String address;
        private String businessScope;
        private String name;
        private String phone;
        private String website;
        private String cid;
        private String logo;
        private String listingStatus;
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class TitleObject {
        private String companyId;
        private String companyName;
        private String operName;
        private String started;
        private String registCapi;
        private String registAddress;
        private String telephone;
        private String url;
        private String mail;
        private String logo;
        private String shortName;
        private String principalName;
        private String principalJob;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class CompanyManagerInfo {
        private List<Managers> managers;
        private int total;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Managers {
        private String companyManagerName;
        private String companyPositionName;
        private String companyId;
    }



}
