package com.facishare.crm.sfa.lto.objectpool;

import com.facishare.crm.openapi.Utils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

@Component
public class ObjectPoolActionFactory {
    private static final Map<String, IObjectPoolActionService> POOL_SERVICE = new HashMap<>();

    @Autowired
    AccountPoolActionServiceImpl accountPoolActionService;

    @Autowired
    LeadsPoolActionServiceImpl leadsPoolActionService;

    @PostConstruct
    public void init(){
        POOL_SERVICE.put(Utils.ACCOUNT_API_NAME, accountPoolActionService);
        POOL_SERVICE.put(Utils.LEADS_API_NAME, leadsPoolActionService);
    }

    public IObjectPoolActionService getObjectPoolActionService(String apiName){
        return POOL_SERVICE.get(apiName);
    }
}
