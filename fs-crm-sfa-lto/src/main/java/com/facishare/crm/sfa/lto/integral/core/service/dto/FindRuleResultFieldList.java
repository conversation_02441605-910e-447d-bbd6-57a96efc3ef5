package com.facishare.crm.sfa.lto.integral.core.service.dto;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.sfa.lto.integral.core.rest.dto.QueryResultList;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Sets;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Set;

public interface FindRuleResultFieldList {
    @Data
    class Arg {

        @JSONField(name = "api_name")
		@JsonProperty("api_name")
        private String apiName;

        private String scene;
    }

    @Data
    class Result {
        @JSONField(name = "field_name")
		@JsonProperty("field_name")
        private Set<String> fieldNames;

        public static Result build(List<QueryResultList.RuleMacroGroupResult> ruleMacroGroupResults) {
            Result result = new Result();
            if (CollectionUtils.notEmpty(ruleMacroGroupResults)) {
                Set<String> fieldNames = Sets.newHashSet();
                JSONArray jsonArray = JSON.parseArray(ruleMacroGroupResults.get(0).getResult());
                for (Object o : jsonArray) {
                    JSONObject jsonObject = (JSONObject) o;
                    String fieldName = jsonObject.getString("fieldName");
                    if(StringUtils.isNotBlank(fieldName)){
                        fieldNames.add(fieldName);
                    }
                }
                result.setFieldNames(fieldNames);
            }
            return result;
        }
    }
}
