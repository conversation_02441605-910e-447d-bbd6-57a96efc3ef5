package com.facishare.crm.sfa.lto.qywx.proxy.models;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class QualityInspectionRule {

    private RuleModel data;
    private String fsEa;

    @Data
    public static class RuleModel {
        /**
         * 规则id
         */
        @JSONField(name = "rule_id")
        @JsonProperty("rule_id")
        private String ruleId;
        /**
         * 规则名称
         */
        @JSONField(name = "name")
        @JsonProperty("name")
        private String name;

        /**
         * 关键词配置
         */
        @JSONField(name = "keyword")
        @JsonProperty("keyword")
        private Keyword keyword;
        /**
         * 语义配置
         */
        @JSONField(name = "semantics")
        @JsonProperty("semantics")
        private Semantics semantics;
        /**
         * 适用范围配置
         */
        @JSONField(name = "applicable_range")
        @JsonProperty("applicable_range")
        private ApplicableRange applicableRange;
    }

    @Data
    public static class Semantics {
        /**
         * 语义列表
         */
        @J<PERSON>NField(name = "semantics_list")
        @JsonProperty("semantics_list")
        private List<Long> semanticsList;
    }

    @Data
    public static class ApplicableRange {
        /**
         * 会话配置
         */
        @JSONField(name = "chat")
        @JsonProperty("chat")
        private Chat chat;
        /**
         * 会话类型配置
         */
        @JSONField(name = "chat_type")
        @JsonProperty("chat_type")
        private ChatType chatType;
        /**
         * 部门配置
         */
        @JSONField(name = "department")
        @JsonProperty("department")
        private Department department;
        /**
         * 排除银行卡配置
         */
        @JSONField(name = "exclude_bank_card")
        @JsonProperty("exclude_bank_card")
        private ExcludeBankCard excludeBankCard;
        /**
         * 排除邮箱配置
         */
        @JSONField(name = "exclude_email")
        @JsonProperty("exclude_email")
        private ExcludeEmail excludeEmail;
        /**
         * 排除关键词配置
         */
        @JSONField(name = "exclude_keyword")
        @JsonProperty("exclude_keyword")
        private ExcludeKeyword excludeKeyword;
        /**
         * 排除手机号配置
         */
        @JSONField(name = "exclude_mobile")
        @JsonProperty("exclude_mobile")
        private ExcludeMobile excludeMobile;
        /**
         * 外部联系人配置
         */
        @JSONField(name = "external_contact")
        @JsonProperty("external_contact")
        private ExternalContact externalContact;
        /**
         * 消息范围
         */
        @JSONField(name = "msg_scope")
        @JsonProperty("msg_scope")
        private Long msgScope;
        /**
         * 会话类型配置
         */
        @JSONField(name = "session_type")
        @JsonProperty("session_type")
        private SessionType sessionType;
        /**
         * 目标类型配置
         */
        @JSONField(name = "target_type")
        @JsonProperty("target_type")
        private TargetType targetType;
        /**
         * 用户配置
         */
        @JSONField(name = "user")
        @JsonProperty("user")
        private User user;
    }

    @Data
    public static class Chat {
        /**
         * 会话ID列表
         */
        @JSONField(name = "id_list")
        @JsonProperty("id_list")
        private List<String> idList;
    }


    @Data
    public static class ChatType {
        /**
         * 会话类型列表
         */
        @JSONField(name = "type_list")
        @JsonProperty("type_list")
        private List<Long> typeList;

    }

    @Data
    public static class Department {
        /**
         * 部门ID列表
         */
        @JSONField(name = "id_list")
        @JsonProperty("id_list")
        private List<Long> idList;

    }

    @Data
    public static class ExcludeBankCard {
        /**
         * 排除的银行卡列表
         */
        @JSONField(name = "bank_card_list")
        @JsonProperty("bank_card_list")
        private List<String> bankCardList;
    }


    @Data
    public static class ExcludeEmail {
        /**
         * 排除的邮箱列表
         */
        @JSONField(name = "email_list")
        @JsonProperty("email_list")
        private List<String> emailList;
    }


    @Data
    public static class ExcludeKeyword {
        /**
         * 排除的关键词列表
         */
        @JSONField(name = "word_list")
        @JsonProperty("word_list")
        private List<String> wordList;
    }


    @Data
    public static class ExcludeMobile {
        /**
         * 排除的手机号列表
         */
        @JSONField(name = "mobile_list")
        @JsonProperty("mobile_list")
        private List<String> mobileList;
    }

    @Data
    public static class ExternalContact {
        /**
         * 外部联系人ID列表
         */
        @JSONField(name = "id_list")
        @JsonProperty("id_list")
        private List<String> idList;
    }


    @Data
    public static class SessionType {
        /**
         * 会话类型列表
         */
        @JSONField(name = "session_type_list")
        @JsonProperty("session_type_list")
        private List<Long> sessionTypeList;
    }

    @Data
    public static class TargetType {
        /**
         * 类型列表
         */
        @JSONField(name = "type_list")
        @JsonProperty("type_list")
        private List<Long> typeList;
    }

    @Data
    public static class User {
        /**
         * 用户ID列表
         */
        @JSONField(name = "id_list")
        @JsonProperty("id_list")
        private List<String> idList;
    }


    @Data
    public static class Keyword {
        /**
         * 是否大小写敏感
         */
        @JSONField(name = "is_case_sensitive")
        @JsonProperty("is_case_sensitive")
        private Long isCaseSensitive;
        /**
         * 是否去除消息空格
         */
        @JSONField(name = "is_trim_msg")
        @JsonProperty("is_trim_msg")
        private Long isTrimMsg;
        /**
         * 匹配规则
         */
        @JSONField(name = "match_rule")
        @JsonProperty("match_rule")
        private Long matchRule;
        /**
         * 关键词列表
         */
        @JSONField(name = "word_list")
        @JsonProperty("word_list")
        private List<String> wordList;
    }

}