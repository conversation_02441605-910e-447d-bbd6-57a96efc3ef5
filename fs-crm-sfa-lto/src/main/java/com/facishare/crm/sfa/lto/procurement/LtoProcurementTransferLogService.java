package com.facishare.crm.sfa.lto.procurement;

import com.facishare.crm.sfa.lto.procurement.models.LtoProcurementConstants.CommonConstants;
import com.facishare.crm.sfa.lto.procurement.models.LtoProcurementConstants.ProcurementTransfer;
import com.facishare.crm.sfa.lto.procurement.models.LtoProcurementModel.OPJApiEnum;
import com.facishare.crm.sfa.lto.utils.CommonSqlUtil;
import com.facishare.enterprise.common.util.CollectionUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.CommonSqlOperator;
import com.facishare.paas.metadata.impl.search.WhereParam;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName com.facishare.crm.sfa.lto.procurement.ProcurementTransferLogService
 * @Description
 * <AUTHOR>
 * @Date 2022/10/20 18:20
 * @Version 1.0
 **/
@Slf4j
@Component
public class LtoProcurementTransferLogService {
    @Autowired
    ServiceFacade serviceFacade;

    /**
     * 对比成功后更新关联到转换对象表
     * @param infoId
     * @param tenantId
     * @param idsMap
     */
    public void attach(String infoId, String tenantId, Map<String, List<String>> idsMap){
        if(!CollectionUtil.isEmpty(idsMap)){
            Map<String, Object> dataMap = Maps.newHashMap();

            long currentTimeMillis = System.currentTimeMillis();
            dataMap.put(CommonConstants.ID, serviceFacade.generateId());
            dataMap.put(CommonConstants.TENANT_ID, tenantId);
            dataMap.put(ProcurementTransfer.INFO_ID, infoId);
            dataMap.put(CommonConstants.OBJECT_DESCRIBE_API_NAME, ProcurementTransfer.OBJ_NAME);
            dataMap.put(CommonConstants.CREATED_BY, 1000);
            dataMap.put(CommonConstants.CREATE_TIME, currentTimeMillis);
            dataMap.put(CommonConstants.LAST_MODIFIED_BY, 1000);
            dataMap.put(CommonConstants.LAST_MODIFIED_TIME, currentTimeMillis);
            dataMap.put(CommonConstants.IS_DELETED, 0);

            if(idsMap.containsKey(OPJApiEnum.Enterprise.apiName)){
                dataMap.put(ProcurementTransfer.ENTERPRISE_INFO_ID, idsMap.get(OPJApiEnum.Enterprise.apiName));
            }
            if(idsMap.containsKey(OPJApiEnum.Account.apiName)){
                dataMap.put(ProcurementTransfer.ACCOUNT_ID, idsMap.get(OPJApiEnum.Account.apiName));
            }
            if(idsMap.containsKey(OPJApiEnum.Leads.apiName)){
                dataMap.put(ProcurementTransfer.LEADS_ID, idsMap.get(OPJApiEnum.Leads.apiName));
            }
            if(idsMap.containsKey(OPJApiEnum.Competitor.apiName)){
                dataMap.put(ProcurementTransfer.COMPETITOR_ID, idsMap.get(OPJApiEnum.Competitor.apiName));
            }
            if(idsMap.containsKey(OPJApiEnum.Partner.apiName)){
                dataMap.put(ProcurementTransfer.PARTNER_ID, idsMap.get(OPJApiEnum.Partner.apiName));
            }
            int row = insert( tenantId, dataMap);
            log.info("attach:{}, infoId:{}, tenantId:{}, ids:{}, dataMap:{}", row, infoId, tenantId, idsMap, dataMap);
        }
    }

    /**
     * 更新转换对象表
     * @param tenantId
     * @param dataMap
     */
    private int insert(String tenantId, Map dataMap){
        try {
            return CommonSqlUtil.insertData(tenantId, ProcurementTransfer.TABLE_NAME, Lists.newArrayList(dataMap));
        } catch (MetadataServiceException ex) {
            log.error("insertProcurementTransferLog error", ex);
        }
        return 0;
    }

    /**
     * 更新转换对象表
     * @param infoId
     * @param tenantId
     * @param dataMap
     */
    private void update(String infoId, String tenantId, Map dataMap){
        List<WhereParam> whereParams = Lists.newArrayList();
        CommonSqlUtil.addWhereParam(whereParams, ProcurementTransfer.INFO_ID, CommonSqlOperator.EQ, Lists.newArrayList(infoId));
        CommonSqlUtil.addWhereParam(whereParams, CommonConstants.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(tenantId));
        CommonSqlUtil.addWhereParam(whereParams, CommonConstants.IS_DELETED, CommonSqlOperator.EQ, Lists.newArrayList(0));

        try {
            CommonSqlUtil.updateData(tenantId, ProcurementTransfer.TABLE_NAME, dataMap, whereParams);
        } catch (MetadataServiceException e) {
            log.error("updateProcurementTransferLog error", e);
        }
    }
}