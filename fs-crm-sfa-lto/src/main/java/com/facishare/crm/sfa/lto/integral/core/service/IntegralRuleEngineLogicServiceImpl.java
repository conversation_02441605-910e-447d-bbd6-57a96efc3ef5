package com.facishare.crm.sfa.lto.integral.core.service;

import com.facishare.crm.sfa.lto.integral.common.constant.RuleDataConstant;
import com.facishare.crm.sfa.lto.integral.core.entity.IntegralRuleDocument;
import com.facishare.crm.sfa.lto.integral.core.proxy.IntegralRuleEngineRestProxy;
import com.facishare.crm.sfa.lto.integral.core.rest.dto.BaseEngine;
import com.facishare.crm.sfa.lto.integral.core.rest.dto.DeleteRuleFromEngine;
import com.facishare.crm.sfa.lto.integral.core.rest.dto.GetInUsedDescribe;
import com.facishare.crm.sfa.lto.integral.core.rest.dto.GetRuleDetail;
import com.facishare.crm.sfa.lto.integral.core.rest.dto.GetRuleList;
import com.facishare.crm.sfa.lto.integral.core.rest.dto.QueryResultList;
import com.facishare.crm.sfa.lto.integral.core.rest.dto.SaveRuleOrUpdate;
import com.facishare.crm.sfa.lto.integral.core.rest.dto.UpdateStatus;
import com.facishare.crm.sfa.lto.integral.core.util.IntegralHeaderUtils;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.lto.integral.core.service.RestResponseHandler.checkEngineResponse;

@Slf4j
@Service
public class IntegralRuleEngineLogicServiceImpl implements IntegralRuleEngineLogicService {

    @Autowired
    private IntegralRuleEngineRestProxy integralRuleEngineRestProxy;

    @Override
    public void saveRule(SaveRuleOrUpdate.Arg arg) {
        SaveRuleOrUpdate.Result response = integralRuleEngineRestProxy.saveRule(IntegralHeaderUtils.getHeaders(arg.getContext()), arg);
        //检查响应
        checkEngineResponse(response, arg.getContext().getTenantId(), arg.getContext().getUserId());
    }

    @Override
    public void updateRule(SaveRuleOrUpdate.Arg arg) {
        SaveRuleOrUpdate.Result response = integralRuleEngineRestProxy.updateRule(IntegralHeaderUtils.getHeaders(arg.getContext()), arg);
        //引擎响应异常处理
        checkEngineResponse(response, arg.getContext().getTenantId(), arg.getContext().getUserId());
    }

    @Override
    public void updateRuleStatus(UpdateStatus.Arg arg) {
        UpdateStatus.Result response = integralRuleEngineRestProxy.updateRuleStatus(IntegralHeaderUtils.getHeaders(arg.getContext()), arg);
        checkEngineResponse(response, arg.getContext().getTenantId(), arg.getContext().getUserId());
    }

    @Override
    public void deleteRule(DeleteRuleFromEngine.Arg arg) {
        DeleteRuleFromEngine.Result response = integralRuleEngineRestProxy.deleteRule(IntegralHeaderUtils.getHeaders(arg.getContext()), arg);
        checkEngineResponse(response, arg.getContext().getTenantId(), arg.getContext().getUserId());
    }

    @Override
    public GetRuleList.Result getRuleListByPage(GetRuleList.Arg arg) {
        GetRuleList.Result result = integralRuleEngineRestProxy.getRuleListByPage(IntegralHeaderUtils.getHeaders(arg.getContext()), arg);
        checkEngineResponse(result, arg.getContext().getTenantId(), arg.getContext().getUserId());
        return result;
    }

    @Override
    public GetRuleDetail.Result getRuleDetail(GetRuleDetail.Arg body) {
        GetRuleDetail.Result result = integralRuleEngineRestProxy.getRuleDetail(IntegralHeaderUtils.getHeaders(body.getContext()), body);
        checkEngineResponse(result, body.getContext().getTenantId(), body.getContext().getUserId());
        return result;
    }

    @Override
    public List<String> findRuleApiNameByDescribeApiName(String describeApiName, RequestContext context) {
        QueryResultList.Arg arg = QueryResultList.Arg.build(describeApiName, RuleDataConstant.CRM_BEHAVIOR_INTEGRAL, context);
        QueryResultList.Result ruleResponse = integralRuleEngineRestProxy.queryResultList(IntegralHeaderUtils.getHeaders(arg.getContext()), arg);
        checkEngineResponse(ruleResponse, context.getTenantId(), context.getUser().getUserId());
        Set<String> apiNames = ruleResponse.getResult().stream().map(QueryResultList.RuleMacroGroupResult::getDescribeApiName)
                .collect(Collectors.toSet());
        return Lists.newArrayList(apiNames);
    }

    @Override
    public GetInUsedDescribe.Result getInUsedDescribeList(GetInUsedDescribe.Arg arg) {
        GetInUsedDescribe.Result ruleResponse = integralRuleEngineRestProxy.getInUsedDescribeList(IntegralHeaderUtils.getHeaders(arg.getContext()), arg);
        checkEngineResponse(ruleResponse, arg.getContext().getTenantId(), arg.getContext().getUserId());
        return ruleResponse;
    }

    @Override
    public QueryResultList.Result queryResultList(QueryResultList.Arg arg) {
        QueryResultList.Result ruleResponse = integralRuleEngineRestProxy.queryResultList(IntegralHeaderUtils.getHeaders(arg.getContext()), arg);
        checkEngineResponse(ruleResponse, arg.getContext().getTenantId(), arg.getContext().getUserId());
        return ruleResponse;
    }

    @Override
    public List<IntegralRuleDocument> getRuleBasicInfo(String apiName, String label, String tenantId, String userId) {
        GetRuleList.Arg requestBody = new GetRuleList.Arg();
        BaseEngine.Context context = new BaseEngine.Context(tenantId, userId);
        requestBody.setContext(context);
        if (Objects.nonNull(apiName)) {
            requestBody.setApiName(apiName);
        }
        if (Objects.nonNull(label)) {
            requestBody.setName(label);
        }
        GetRuleList.Result response = integralRuleEngineRestProxy.getRuleListByPage(IntegralHeaderUtils.getHeaders(context), requestBody);
        checkEngineResponse(response, tenantId, userId);
        List<GetRuleList.RuleListInfo> ruleInfoList = response.getResult().getContent();
        return IntegralRuleDocument.ofList(ruleInfoList);
    }
}
