package com.facishare.crm.sfa.prm.model;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.sfa.prm.api.enums.SmsChannelType;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024-06-29 17:23
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SmsMessageContent extends TemplateMessageContent {
    private String appId;
    @Builder.Default
    private Integer type = 1;
    private Long scheduleTime;
    @Builder.Default
    private List<SmsContentParam> smsContentParams = Lists.newArrayList();
    private SmsChannelType channelType;
    private String sceneType;
    private String businessType;
    private String receiver;
    private String sendNode;
    private String nodeType;

    @Builder
    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SmsContentParam {
        private String key;
        private String type;
        private String value;
    }
}
