package com.facishare.crm.sfa.lto.qywx.proxy.models;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

public interface QywxTransferStatusModel {
  @Data
	@Builder
	class Arg {
    /**
     * 必填
     */
    private String ea;
    /**
     * 必填，原跟进成员的userid，纷享的员工id
     */
    private String handoverUserId;
    /**
     * 必填，接替成员的userid，纷享的员工id
     */
    private String takeoverUserId;
    /**
     * 非必填，分页查询的cursor，每个分页返回的数据不会超过1000条；不填或为空表示获取第一个分页
     */
    private String cursor;
  }

  @Data
  class TransferCustomerStatusResult implements Serializable {
    private List<QyweixinCustomer> customer;
    private String nextCursor;
  }

  @Data
  class QyweixinCustomer implements Serializable{
    private String externalUserId;//转接客户的外部联系人userid
    private Integer status;//接替状态， 1-接替完毕 2-等待接替 3-客户拒绝 4-接替成员客户达到上限 5-无接替记录
    private Long takeoverTime;//接替客户的时间，如果是等待接替状态，则为未来的自动接替时间
  }
}