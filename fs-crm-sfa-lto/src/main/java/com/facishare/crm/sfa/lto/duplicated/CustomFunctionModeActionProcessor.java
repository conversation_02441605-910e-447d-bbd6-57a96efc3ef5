package com.facishare.crm.sfa.lto.duplicated;

import com.facishare.crm.sfa.lto.duplicated.models.DuplicatedModels;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefFunction;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class CustomFunctionModeActionProcessor extends AbstractModeActionProcessor {
    @Override
    public DuplicatedModels.ModeAction getModelAction() {
        return DuplicatedModels.ModeAction.CUSTOM_FUNCTION;
    }

    @Override
    public void process(User user, DuplicatedModels.DuplicatedProcessingMode processingMode,
                                                  IObjectData freshData, List<IObjectData> duplicatedDataList,
                                                  DuplicatedModels.TriggerAction triggerAction) {
        executeCustomFunction(user, freshData, processingMode);
    }

    private void executeCustomFunction(User user, IObjectData freshData,
                                       DuplicatedModels.DuplicatedProcessingMode processingMode) {

        if(processingMode.getModeRule() == null || processingMode.getModeRule().getCustomFunction() == null) {
            return;
        }
        DuplicatedModels.CustomFunction function = processingMode.getModeRule().getCustomFunction();
        String functionApiName = function.getFunctionApiName();
        String bindingObjectApiName = function.getBindingObjectApiName();
        executeCustomFunction(user, functionApiName, bindingObjectApiName, freshData);
    }

    /**
     * 执行自定义函数
     *
     * @param user
     * @param apiName              自定义函数apiName
     * @param bindingObjectApiName 绑定对象apiName
     * @return
     */
    private void executeCustomFunction(User user, String apiName, String bindingObjectApiName, IObjectData objectData) {
        if (StringUtils.isEmpty(apiName) || StringUtils.isEmpty(bindingObjectApiName)) {
            return;
        }
        IUdefFunction function = serviceFacade.getFunctionLogicService().findUDefFunction(user, apiName, bindingObjectApiName);
        if (function == null) {
            log.warn("leads_duplicated_processing function is null");
            return;
        }
        if (!function.isActive()) {
            log.warn("leads_duplicated_processing function is not active");
            return;
        }
        if (!"button".equals(function.getNameSpace())) {
            log.warn("leads_duplicated_processing function name space is not button");
            return;
        }
        List<String> fieldNames = function.getParameters();
        Map<String, Object> paramMap = Maps.newHashMap();
        for (String fieldName : fieldNames) {
            paramMap.put(fieldName, objectData.get(fieldName));
        }
        serviceFacade.getFunctionLogicService().executeUDefFunction(user, function, paramMap, objectData, Maps.newHashMap());
    }
}