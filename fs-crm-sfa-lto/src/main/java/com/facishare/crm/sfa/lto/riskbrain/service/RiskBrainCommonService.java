package com.facishare.crm.sfa.lto.riskbrain.service;

import cn.com.antcloud.api.AntFinTechApiClient;
import cn.com.antcloud.api.AntFinTechProfile;
import cn.com.antcloud.api.acapi.HttpConfig;
import cn.com.antcloud.api.product.AntCloudProdRequest;
import cn.com.antcloud.api.product.AntCloudProdResponse;
import cn.com.antcloud.api.riskplus.v1_0.request.QueryGeneralRequest;
import cn.com.antcloud.api.riskplus.v1_0.response.QueryGeneralResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.lto.riskbrain.model.RiskBrainModel;
import com.facishare.crm.sfa.lto.utils.DateUtil;
import com.facishare.crm.sfa.lto.utils.RSARiskBrainTool;
import com.facishare.crm.sfa.lto.utils.constants.RiskBrainConstants;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.github.autoconf.ConfigFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class RiskBrainCommonService {

    @Autowired
    private ServiceFacade serviceFacade;

    private static String ENDPOINT = "";
    private static String PRODUCT_INSTANCE_ID = "";
    private static Integer QUERY_SECOND = 0;
    private static String ACCESS_KEY = "";
    private static String SECRET_KEY = "";
    private static String PUBLIC_KEY = "";
    private static String PRIVATE_KEY = "";

    private static String proxyHost;
    private static Integer proxyPort;

    static {
        ConfigFactory.getConfig("crm-sfa-risk-brain-config", config -> {
            ENDPOINT = config.get("endpoint", "https://openapi.antchain.antgroup.com");
            PRODUCT_INSTANCE_ID = config.get("product_instance_id", "rbb-prod");
            QUERY_SECOND = config.getInt("query_second", 8640);
            ACCESS_KEY = config.get("accessKey", "ACA7Rs1KVMnf1ptg");
            SECRET_KEY = config.get("secretKey", "bxXJWwtaVVGfvhLHeimBw2fDDdrAGGnz");
            PRIVATE_KEY = config.get("privateKey", "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCsoPybrDEdf3kwFIjDF/MdG9QW7k5K9FTjoY/hfHLZOB8NhKO3w9bMcq9d72Qjepgf/hqMfnbwuCXoG5JZT8Di5AQIIxQHCaj93X1TOWmGa/hNKAteYeabJMAYZYgcBoKC9WvYKoOrS1u//e3XG4PbZE5M1Z0Kbr0m5SvjtbSiffA64FX0OdGWgVu6k4TtNsGiGl19M22/sLg+5XOrSG6qHOAwOqjFCirANRvnlRm1/xMEASx2AZY94591OAFTDqlXnQ2fClFR7maSBS8XWHIW6XEWjmAFW5zzK1xUNRgZEgLceKpfGTafCFmx22EApJem+/WjgrxSGd+XtmvonofvAgMBAAECggEAPmtji6KCi+j41/LE1M5HIOJcGQKIGsAoJbrYfK70FCkfyVKJpDvS4lAhqvSRn+aHNSI7VUuxFC9xvXCixVp6I1BRSOPuLPFIQqsg0sPyrSFDQhrVqYX6zWRHdUrZIVEVRecZ3Krau0hfX5egtWQElIK7cTrYMtqB2JuK+CZOr2YCBx1BKaKPrQyV2xxHO+yzbP9kLd3j0yy1TC5yAX1clxocwZBU08vmtaVDcxT0G0k0QlTmQ209ze3jUQiJWSUkYpjW3bq3SDRHOK8ezxBanQsCvAe1pQNtU0JR/rRJ3x9ppAnD9Os48dj1vtzbGHeL4xd6XnFqPQTesGwyYKINwQKBgQD5RTwd8pN4+4Ozx9v1uvrPJPYQLLeY6mcLJ2idbmrc4Yp5Dp9NsaOrKmrOoBqOvZwcnZp5blEjeHU+7RJoN9iD+HYYC1wA5u+zvl8XAJ8/Gbt8e9BdA/iEw1Wa6aoJwfkqOadxcb6Kf+fjNWjSuzOvXVEJajP1N0E31UIA5QfeDwKBgQCxShB2X6k0n+oJ6dtpNeLk04OjFn3+zWVTrRHMVQGhE1qVwsFNe/HJmgwEDWhskWBHBzl7bFX7JasImIaggoQNSKAZiLsrbdzLQLv/U3/p/88IDK1g6Jfipnpb44jKvbZTLlWXno4FT5Rcxto46a8K7+Ps7Un5TU5j4X5APx6YIQKBgAalDNQzijDYa4tYUYm8YUEyWAjYWFCQNwz6EPTZY6ndrUZK2H5GtPk0GULGy85wSYANx3djNHCAnod95Fa43mGuNt+vNdWkUlF1Sq2fS59v/Sp1VnmUSISpfFez0N+Ptyej/82rXYvtmu3Yl6G+ccND9owMs7PetDNb0PWbJVmLAoGARSyT3cwXdkwLAlE35gY7gHMzAphNEvVLWunFbwef+hxWFOcHi1cYEURE+fPNUccrXKtp90lfBv48jL8OkGQ8Z/51UWEzPo/APaIFeGs5FrFUM1zW7V/zm2C/j0jpX8nD8RK8HMuluFbDmauRC/HJ9aeCNDily1spgflCzIiURGECgYEA+OSthBEDsUGnt8Jnd3U85P7DBhspkPanjDJU67rfJ0EQHakt+r2rJjJfcjmJvYVeJCejkTA9Zdsc2v6w7KUa+oME1E3QC6aDBjOPptgu7z7nhqrYCTQzIKe/p1xfDU8d/NVWL1S+ajYbfQ0nYqKufF1zDCYUoigxlPjkkODS7IQ=");
            PUBLIC_KEY = config.get("publicKey", "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEArKD8m6wxHX95MBSIwxfzHRvUFu5OSvRU46GP4Xxy2TgfDYSjt8PWzHKvXe9kI3qYH/4ajH528Lgl6BuSWU/A4uQECCMUBwmo/d19Uzlphmv4TSgLXmHmmyTAGGWIHAaCgvVr2CqDq0tbv/3t1xuD22ROTNWdCm69JuUr47W0on3wOuBV9DnRloFbupOE7TbBohpdfTNtv7C4PuVzq0huqhzgMDqoxQoqwDUb55UZtf8TBAEsdgGWPeOfdTgBUw6pV50NnwpRUe5mkgUvF1hyFulxFo5gBVuc8ytcVDUYGRIC3HiqXxk2nwhZsdthAKSXpvv1o4K8Uhnfl7Zr6J6H7wIDAQAB");
            if (config.getBool("risk.brain.encrypt", false)) {
                ACCESS_KEY = config.getDecrypt("accessKey");
                SECRET_KEY = config.getDecrypt("secretKey");
                PRIVATE_KEY = config.getDecrypt("privateKey");
                PUBLIC_KEY = config.getDecrypt("publicKey");
            }
        });
        ConfigFactory.getConfig("variables_endpoint", config -> {
            proxyHost = config.get("http_proxy_host");
            proxyPort = config.getInt("http_proxy_port");
            log.info("http代理地址{}:{}", proxyHost, proxyPort);
        });
    }

    public String getPrivateKey() {
        return PRIVATE_KEY;
    }

    public Integer getQuerySecond() {
        return QUERY_SECOND;
    }

    public RiskBrainModel.Arg getRiskBrainUser(String tenantId) {
        String queryString = serviceFacade.findTenantConfig(User.systemUser(tenantId), RiskBrainConstants.KEY);
        return JSON.parseObject(queryString, RiskBrainModel.Arg.class);
    }

    public RiskBrainModel.Arg getRiskBrainUserInfo(String tenantId) {
        RiskBrainModel.Arg riskBrainAccountInfo = getRiskBrainUser(tenantId);
        if (riskBrainAccountInfo == null) {
            log.warn("RiskBrainService getToken paramArg is null paramArg:{}", JSONObject.toJSONString(riskBrainAccountInfo));
            throw new ValidateException("获取风险大脑用户信息失败！");
        }
        return riskBrainAccountInfo;
    }

    public AntFinTechApiClient getAntFinTechApiClient() {
        try {
            // 初始化Client
            AntFinTechProfile antFinTechProfile = AntFinTechProfile.getProfile(ENDPOINT, ACCESS_KEY, SECRET_KEY);
            if (StringUtils.isNotEmpty(proxyHost) && proxyPort != null) {
                HttpConfig httpConfig = new HttpConfig();
                httpConfig.setProxyHost(proxyHost);
                httpConfig.setProxyPort(proxyPort);
                antFinTechProfile.setHttpConfig(httpConfig);
            }
            return new AntFinTechApiClient(antFinTechProfile);
        } catch (InterruptedException e) {
            log.error("RiskBrainService getToken is null.:", e);
            Thread.currentThread().interrupt();
            throw new ValidateException("获取风险大脑用户信息失败！");
        }
    }

    public void initRiskBrainAccount(RiskBrainModel.Message message, int productType, String code) {
        String tenantId = message.getTenantId();
        AntFinTechApiClient antFinTechApiClient = getAntFinTechApiClient();
        JSONObject request = new JSONObject();
        request.put("clientName", tenantId);
        request.put("orderId", message.getOrderNumber());
        request.put("orderTime", DateUtil.getDataTimeStrByTimeStamp(message.getCreateTime()));
        request.put("expiryDate", DateUtil.getDataStrByTimeStamp(message.getExpiredTime()));
        request.put("effectiveTime", DateUtil.getDataTimeStrByTimeStamp(message.getStartTime()));
        //productType 必填, 1 ⻛险模块-基础版 2⻛险模块-⾼级版
        request.put("productType", productType);
        request.put("applyProductQuota", analysisApplyProductQuota(code));

        QueryGeneralResponse responseCompany = queryGeneral(antFinTechApiClient, "irap.account.create.apply", request);
        if (!responseCompany.isSuccess()) {
            log.error("蚂蚁风险下单失败:{}", responseCompany.getData());
            return;
        }
        if (ObjectUtils.isEmpty(responseCompany) || ObjectUtils.isEmpty(responseCompany.getData())) {
            log.error("initRiskBrainAccount error tenantId:{},responseCompany:{}", tenantId, JSONObject.toJSONString(responseCompany));
            return;
        }
        RiskBrainModel.AccountCreateApplyResult accountCreateApplyResult = JSONObject.parseObject(responseCompany.getData(), RiskBrainModel.AccountCreateApplyResult.class);
        RiskBrainModel.AccountCreateApplyQueryResult accountCreateApplyQueryResult = accountCreateApplyResult.getQueryResult();
        try {
            String password = RSARiskBrainTool.decryptStr(accountCreateApplyQueryResult.getEncryptedPassword(), PRIVATE_KEY);
            RiskBrainModel.Arg arg = new RiskBrainModel.Arg();
            arg.setTenantId(tenantId);
            arg.setOriginalTenantId(accountCreateApplyQueryResult.getAccountName());
            arg.setTenantCode(accountCreateApplyQueryResult.getTenantCode());
            arg.setUserName(accountCreateApplyQueryResult.getAccountName());
            arg.setPassword(password);
            serviceFacade.upsertTenantConfig(User.systemUser(tenantId), RiskBrainConstants.KEY, JSONObject.toJSONString(arg), ConfigValueType.JSON);
        } catch (Exception e) {
            log.error("initRiskBrainAccount decryptStr error tenantId:{},responseCompany:{},e:", tenantId, JSONObject.toJSONString(responseCompany), e);
            throw new RuntimeException(e);
        }
    }

    public Integer analysisApplyProductQuota(String code) {
        //code值是 customer_risk_management_advance_20000_app，解析出其中的数字
        return Integer.parseInt(code.split("_")[4]);
    }

    public QueryGeneralResponse queryGeneral(String queryName, JSONObject param) {
        AntFinTechApiClient antFinTechApiClient = getAntFinTechApiClient();
        return queryGeneral(antFinTechApiClient, queryName, param);
    }

    public QueryGeneralResponse queryGeneral(AntFinTechApiClient antFinTechApiClient, String queryName, JSONObject param) {
        QueryGeneralRequest req = new QueryGeneralRequest();
        req.setProductInstanceId(PRODUCT_INSTANCE_ID);
        req.setQueryname(queryName);
        req.setQueryparas(param.toJSONString());
        QueryGeneralResponse response = new QueryGeneralResponse();
        try {
            response = antFinTechApiClient.execute(req);
        } catch (InterruptedException e) {
            response.setResultCode("ERROR");
            response.setResultMsg("风险大脑接口调用失败");
            log.warn("风险大脑接口调用失败", e);
        }
        log.warn("[风险大脑接口]request:[{}]response:[{}]", JSON.toJSONString(req), JSON.toJSONString(response));
        return response;
    }

    public <T extends AntCloudProdResponse> T queryGeneral(AntCloudProdRequest<T> request) throws InterruptedException {
        AntFinTechApiClient antFinTechApiClient = getAntFinTechApiClient();
        request.setProductInstanceId(PRODUCT_INSTANCE_ID);
        return antFinTechApiClient.execute(request);
    }
}

