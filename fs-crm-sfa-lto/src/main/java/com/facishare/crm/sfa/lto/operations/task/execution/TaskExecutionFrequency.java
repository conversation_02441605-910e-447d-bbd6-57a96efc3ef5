package com.facishare.crm.sfa.lto.operations.task.execution;

import java.util.Arrays;
import java.util.Optional;

public enum TaskExecutionFrequency {
    REBOOT,
    DAILY,
    WEEKLY,
    MONTHLY,
    QUARTERLY,
    ANNUALLY,
    ;

    public static Optional<TaskExecutionFrequency> from(String value) {
        return Arrays.stream(values()).filter(v -> v.name().toLowerCase().equals(value)).findFirst();
    }
}
