package com.facishare.crm.sfa.prm.api.enums;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import lombok.Getter;

import static com.facishare.crm.sfa.prm.core.constants.PrmI18NConstants.PRM_ENUM_PARAM_TYPE_ERROR;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-03-04
 * ============================================================
 */
@Getter
public enum AgreementStatus {
    ACTIVE("active"),
    EXPIRED("expired"),
    COMPLETED("completed");
    private final String status;

    AgreementStatus(String status) {
        this.status = status;
    }

    public static AgreementStatus from(String status) {
        for (AgreementStatus e : values()) {
            if (e.status.equals(status)) {
                return e;
            }
        }
        throw new ValidateException(I18N.text(PRM_ENUM_PARAM_TYPE_ERROR, status));
    }

    public static AgreementStatus find(String status) {
        return find(status, null);
    }

    public static AgreementStatus find(String status, AgreementStatus defaultValue) {
        for (AgreementStatus e : values()) {
            if (e.status.equals(status)) {
                return e;
            }
        }
        return defaultValue;
    }
}
