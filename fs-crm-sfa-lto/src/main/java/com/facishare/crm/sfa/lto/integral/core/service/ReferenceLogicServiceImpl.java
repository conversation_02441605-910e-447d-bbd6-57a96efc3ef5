package com.facishare.crm.sfa.lto.integral.core.service;

import com.facishare.paas.reference.data.EntityReferenceArg;
import com.facishare.paas.reference.service.EntityReferenceService;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ReferenceLogicServiceImpl implements ReferenceLogicService {

    @Autowired
    private EntityReferenceService entityReferenceService;

    /** 引用类型：行为积分规则*/
    private static final String REFERENCE_TYPE = "CRM_BEHAVIOR_INTEGRAL";
    /** 引用名称: 行为积分规则*/
    private static final String REFERENCE_NAME = "行为积分规则";
    /** 被引用的数据类型：字段*/
    private static final String REFERENCED_DATA_TYPE = "Describe.Field";

    @Override
    public void batchCreateReferenceWithFields(String tenantId, String describeApiName, String ruleApiName,
                                               List<String> fieldApiNames) {
        List<EntityReferenceArg> args = Lists.newArrayList();
        for (String name : fieldApiNames) {
            //被引用字段值
            String fieldValue = String.join(".", describeApiName, name);
            args.add(EntityReferenceArg.builder()
                    .tenantId(tenantId)
                    .sourceType(REFERENCE_TYPE)
                    .sourceLabel(REFERENCE_NAME)
                    .sourceValue(ruleApiName)
                    .targetType(REFERENCED_DATA_TYPE)
                    .targetValue(fieldValue).build());
        }
        entityReferenceService.create(args);
    }

    @Override
    public void deleteReference(String tenantId, String describeApiName, String ruleApiName, String fieldApiName) {
        entityReferenceService.delete(tenantId, REFERENCE_TYPE, ruleApiName,
                String.join(".", describeApiName, fieldApiName),REFERENCED_DATA_TYPE);
    }

    @Override
    public void deleteReferenceByRuleApiName(String tenantId, String ruleApiName) {
        entityReferenceService.delete(tenantId, REFERENCE_TYPE, ruleApiName);
    }
}
