package com.facishare.crm.sfa.lto.marketingattribution;

import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class MarketingAttributionStrategyManager implements ApplicationContextAware {
    private Map<String, IMarketingAttributionStrategy> strategyMap = Maps.newHashMap();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        initStrategyMap(applicationContext);
    }

    private void initStrategyMap(ApplicationContext applicationContext) {
        Map<String, IMarketingAttributionStrategy> springBeanMap = applicationContext.getBeansOfType(IMarketingAttributionStrategy.class);
        springBeanMap.values().forEach(provider -> {
            if (StringUtils.isNotEmpty(provider.getStrategyModel())) {
                strategyMap.put(provider.getStrategyModel(), provider);
            }
        });
    }

    public IMarketingAttributionStrategy getStrategy(String strategyModel) {
        return strategyMap.getOrDefault(strategyModel, null);
    }
}
