package com.facishare.crm.sfa.lto.integral.core.service;

import com.facishare.crm.sfa.lto.integral.common.constant.IntegralErrorCode;
import com.facishare.crm.sfa.lto.integral.common.constant.IntegralObject;
import com.facishare.crm.sfa.lto.integral.common.constant.RuleDataConstant;
import com.facishare.crm.sfa.lto.integral.core.entity.IntegralRuleDocument;
import com.facishare.crm.sfa.lto.integral.core.rest.dto.*;
import com.facishare.crm.sfa.lto.integral.core.service.dto.*;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.service.dto.UserInfo;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.google.common.base.Strings;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.collect.Table;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class IntegralRuleServiceImpl implements IntegralRuleService {
    @Autowired
	IntegralRuleEngineLogicService integralRuleEngineLogicService;
    @Autowired
    DescribeLogicService describeLogicService;
    @Autowired
    IObjectDescribeService objectDescribeService;
    @Autowired
    OrgService orgService;
    @Autowired
    BehaviorLogicService behaviorLogicService;
    @Autowired
    LogService logService;
    @Autowired
    ReferenceLogicService referenceLogicService;
    @Autowired
    LicenseService licenseService;
    @Autowired
    BehaviorMaterialService behaviorMaterialService;

    @Override
    public CreateRuleOrUpdate.Result create(CreateRuleOrUpdate.Arg arg, RequestContext context) {

        validRule(arg);

        // 校验对象是否已被其它规则使用
        List<String> apiNames = integralRuleEngineLogicService.findRuleApiNameByDescribeApiName(arg.getObjectApiName(), context);
        if (!CollectionUtils.isEmpty(apiNames)) {
            throw new ValidateException(IntegralErrorCode.DESCRIBE_DUPLICATED.getMessage());
        }

        SaveRuleOrUpdate.Arg body = SaveRuleOrUpdate.Arg.build(arg, context);
        integralRuleEngineLogicService.saveRule(body);

        //创建字段引用
        Set<String> resultFiledNames = arg.getRuleResults().stream().map(RuleDetail.RuleResult::getFieldName)
                .collect(Collectors.toSet());
        referenceLogicService.batchCreateReferenceWithFields(context.getTenantId(), arg.getObjectApiName(), arg.getApiName(),
                Lists.newArrayList(resultFiledNames));

        logService.logRuleSetting(context.getUser(), EventType.ADD, ActionType.Add, arg.getObjectApiName(),
                getLogMessage(arg.getLabel()));
        return CreateRuleOrUpdate.Result.builder().success(true).build();
    }

    @Override
    public CreateRuleOrUpdate.Result update(CreateRuleOrUpdate.Arg arg, RequestContext context) {

        validRule(arg);

        SaveRuleOrUpdate.Arg body = SaveRuleOrUpdate.Arg.build(arg, context);
        integralRuleEngineLogicService.updateRule(body);

        //删除字段引用
        referenceLogicService.deleteReferenceByRuleApiName(context.getTenantId(), arg.getApiName());
        //创建字段引用
        Set<String> resultFiledNames = arg.getRuleResults().stream().map(RuleDetail.RuleResult::getFieldName)
                .collect(Collectors.toSet());
        referenceLogicService.batchCreateReferenceWithFields(context.getTenantId(), arg.getObjectApiName(), arg.getApiName(),
                Lists.newArrayList(resultFiledNames));

        logService.logRuleSetting(context.getUser(), EventType.MODIFY, ActionType.Modify, arg.getObjectApiName(),
                getLogMessage(arg.getLabel()));
        return CreateRuleOrUpdate.Result.builder().success(true).build();
    }

    @Override
    public EnableRule.Result enable(EnableRule.Arg arg, RequestContext context) {
        integralRuleEngineLogicService.updateRuleStatus(UpdateStatus.Arg.build(
                RuleDataConstant.RULE_STATUS_ACTIVE, context, arg.getApiName()));

        GetRuleDetail.RuleDetail ruleDetail = getRuleDetail(arg.getApiName(), context.getTenantId(),
                context.getUser().getUserId());
        logService.logRuleSetting(context.getUser(), EventType.ENABLE, ActionType.Enable,
                ruleDetail.getBasicInfo().getObjectDescribeApiName(),
                getLogMessage(ruleDetail.getBasicInfo().getLabel()));
        return EnableRule.Result.builder().success(true).build();
    }

    @Override
    public DisableRule.Result disable(DisableRule.Arg arg, RequestContext context) {
        integralRuleEngineLogicService.updateRuleStatus(UpdateStatus.Arg.build(
                RuleDataConstant.RULE_STATUS_STOP, context, arg.getApiName()));

        GetRuleDetail.RuleDetail ruleDetail = getRuleDetail(arg.getApiName(), context.getTenantId(),
                context.getUser().getUserId());
        logService.logRuleSetting(context.getUser(), EventType.DISABLE, ActionType.Disable,
                ruleDetail.getBasicInfo().getObjectDescribeApiName(),
                getLogMessage(ruleDetail.getBasicInfo().getLabel()));
        return DisableRule.Result.builder().success(true).build();
    }

    @Override
    public DeleteRule.Result delete(DeleteRule.Arg arg, RequestContext context) {
        GetRuleDetail.RuleDetail ruleDetail = getRuleDetail(arg.getApiName(), context.getTenantId(),
                context.getUser().getUserId());

        integralRuleEngineLogicService.deleteRule(DeleteRuleFromEngine.Arg.build(arg, context));

        //删除该规则所有字段引用
        referenceLogicService.deleteReferenceByRuleApiName(context.getTenantId(), arg.getApiName());

        logService.logRuleSetting(context.getUser(), EventType.DELETE, ActionType.Delete,
                ruleDetail.getBasicInfo().getObjectDescribeApiName(),
                getLogMessage(ruleDetail.getBasicInfo().getLabel()));
        return DeleteRule.Result.builder().success(true).build();
    }

    @Override
    public FindRuleList.Result findRuleList(FindRuleList.Arg arg, RequestContext context) {
        GetRuleList.Arg body = GetRuleList.Arg.build(arg, context);
        GetRuleList.Result result = integralRuleEngineLogicService.getRuleListByPage(body);

        Set<String> apiSet = new HashSet<>();
        Set<String> userSet = Sets.newHashSet();
        result.getResult().getContent().forEach(rule -> {
            apiSet.add(rule.getObjectApiName());
            userSet.add(rule.getCreatedUserId());
            userSet.add(rule.getLastModifiedUserId());
        });
        Map<String, IObjectDescribe> objectDescribeMap = describeLogicService.findObjectsIncludeMultiField(
                context.getTenantId(), apiSet);
        Map<String, UserInfo> userInfoMap = orgService.getUserNameByIds(context.getTenantId(), context.getUser().getUserId(),
                Lists.newArrayList(userSet)).stream().collect(Collectors.toMap(UserInfo::getId, x -> x));
        return FindRuleList.Result.build(result, objectDescribeMap, userInfoMap);
    }

    @Override
    public FindDetailByApiName.Result findRule(FindDetailByApiName.Arg arg, RequestContext context) {
        GetRuleDetail.RuleDetail ruleDetail = getRuleDetail(arg.getApiName(), context.getTenantId(), context.getUser().getUserId());

        IObjectDescribe objectDescribe = describeLogicService.findObjectIncludeMultiField(context.getTenantId()
                , arg.getObjectDescribeApiName());

        Set<String> userSet = Sets.newHashSet();
        userSet.addAll(Arrays.asList(ruleDetail.getBasicInfo().getCreatedBy(),
                ruleDetail.getBasicInfo().getLastModifiedBy()));
        List<UserInfo> userInfos = orgService.getUserNameByIds(context.getTenantId(), context.getUser().getUserId(),
                Lists.newArrayList(userSet));
        List<GetRuleDetail.RuleGroup> ruleGroups = ruleDetail.getRuleGroups();

        Set<String> categoryApiNames = ruleGroups.stream()
                .map(GetRuleDetail.RuleGroup::getFieldApiName)
                .collect(Collectors.toSet());
        Set<String> materialApiNames = ruleGroups.stream()
                .flatMap(x -> x.getRules().stream())
                .filter(x -> IntegralObject.FIELD_MATERIAL_API_NAME.equals(x.getFieldName()))
                .flatMap(x -> x.getFieldValue().stream())
                .collect(Collectors.toSet());
        Table<String, String, String> categoryApiNameToMaterialApiNameToLabel = queryMaterialLabel(context.getTenantId(), categoryApiNames, materialApiNames);
        return FindDetailByApiName.Result.build(ruleDetail, objectDescribe, userInfos, categoryApiNameToMaterialApiNameToLabel);
    }

    private Table<String, String, String> queryMaterialLabel(String tenantId, Set<String> categoryApiNames, Set<String> materialApiNames) {
        //用categoryApi,materialApi作为key,使用guava包下的table
        Table<String, String, String> categoryApiNameToMaterialApiNameToLabel = HashBasedTable.create();
        if(CollectionUtils.isEmpty(materialApiNames)){
            return categoryApiNameToMaterialApiNameToLabel;
        }
        List<IObjectData> behaviorMaterialList = behaviorMaterialService.getBehaviorMaterialList(tenantId, categoryApiNames, materialApiNames);
        for (IObjectData objectData : behaviorMaterialList) {
            categoryApiNameToMaterialApiNameToLabel.put(objectData.get(IntegralObject.FIELD_CATEGORY_API_NAME, String.class),
                    objectData.get(IntegralObject.FIELD_MATERIAL_API_NAME, String.class),
                    objectData.get(IntegralObject.FIELD_MATERIAL_LABEL, String.class));
        }
        return categoryApiNameToMaterialApiNameToLabel;
    }


    @Override
    public FindBehaviorList.Result findBehaviorList(FindBehaviorList.Arg arg, RequestContext context) {
        return behaviorLogicService.findBehaviorList(context.getTenantId());
    }

    @Override
    public FindSupportObjects.Result findSupportObjectList(RequestContext context) {
        //目前只支持线索
        List<String> apiNames = Lists.newArrayList("LeadsObj");
        List<IObjectDescribe> describeList = findDescribeListWithoutFields(context, apiNames);
        GetInUsedDescribe.Result ruleResponse = integralRuleEngineLogicService.getInUsedDescribeList(
                GetInUsedDescribe.Arg.build(context.getTenantId(), context.getUser().getUserId()));
        return FindSupportObjects.Result.build(describeList, ruleResponse.getResult());
    }

    private List<IObjectDescribe> findDescribeListWithoutFields(RequestContext context,List<String> apiNames){
        IActionContext actionContext = ActionContextExt.of(context.getUser()).getContext();
        actionContext.setLang(context.getLang().getValue());
        try {
            return objectDescribeService.findDescribeListByApiNames(context.getTenantId(),apiNames,actionContext);
        } catch (MetadataServiceException e) {
            log.error("findDescribeListByApiNames error",e);
            return Lists.newArrayList();
        }
    }

    private GetRuleDetail.RuleDetail getRuleDetail(String apiName, String tenantId, String userId) {
        GetRuleDetail.Arg body = GetRuleDetail.Arg.build(apiName, tenantId, userId);
        return integralRuleEngineLogicService.getRuleDetail(body).getResult();
    }

    @Override
    public FindRuleResultFieldList.Result findRuleResultFieldList(FindRuleResultFieldList.Arg arg, RequestContext context) {
        QueryResultList.Arg body = QueryResultList.Arg.build(arg.getApiName(), arg.getScene(), context);
        QueryResultList.Result rst = integralRuleEngineLogicService.queryResultList(body);
        return FindRuleResultFieldList.Result.build(rst.getResult());
    }

    @Override
    public CheckRule.Result checkRuleApiNameOrLabel(CheckRule.Arg arg, RequestContext context) {

        CheckRule.Result result = new CheckRule.Result();
        if (Strings.isNullOrEmpty(arg.getLabel())) {
            //新建规则校验ApiName
            List<IntegralRuleDocument> rules = integralRuleEngineLogicService.getRuleBasicInfo(arg.getRuleApiName(),
                    null, context.getTenantId(), context.getUser().getUserId());
            if (!CollectionUtils.isEmpty(rules)) {
                //规则apiName重复
                throw new ValidateException(IntegralErrorCode.API_NAME_DUPLICATED.getMessage());
            }
        } else if (Strings.isNullOrEmpty((arg.getRuleApiName()))) {
            //新建规则校验label
            List<IntegralRuleDocument> rules = integralRuleEngineLogicService.getRuleBasicInfo(null,
                    arg.getLabel(), context.getTenantId(), context.getUser().getUserId());
            if (!CollectionUtils.isEmpty(rules)) {
                //查询是根据label模糊匹配
                boolean duplicate = rules.stream().anyMatch(rule -> rule.getName().equals(arg.getLabel()));
                //规则Label重复
                if (duplicate) {
                    throw new ValidateException(IntegralErrorCode.LABEL_DUPLICATED.getMessage());
                }
            }
        } else {
            //规则名称、规则Api均不为空，说明是编辑,编辑只校验Label
            //根据规则名称查询是模糊查询
            List<IntegralRuleDocument> rules = integralRuleEngineLogicService.getRuleBasicInfo(null,
                    arg.getLabel(), context.getTenantId(), context.getUser().getUserId());
            boolean flag = false;
            for (IntegralRuleDocument rule : rules) {
                if (arg.getRuleApiName().equals(rule.getApiName())) {
                    continue;
                }
                if (arg.getLabel().equals(rule.getName())) {
                    //精确匹配label
                    flag = true;
                }
            }
            if (flag) {
                throw new ValidateException(IntegralErrorCode.LABEL_DUPLICATED.getMessage());
            }
        }
        return result;
    }

    @Override
    public ExistModule.Result existModule(RequestContext context) {
        Set<String> modules = licenseService.getModule(context.getTenantId());
        return ExistModule.Result.builder()
                .value(modules.contains(RuleDataConstant.LICENSE_BEHAVIOR_INTEGRAL_APP))
                .build();
    }

    @Override
    public FindMaterial.Result findMaterialListByCategory(FindMaterial.Arg arg, RequestContext context) {
        FindMaterial.Result result = new FindMaterial.Result();
        if(StringUtils.isBlank(arg.getCategoryApiName())){
            return result;
        }
        List<BehaviorInfo.BehaviorMaterial> batchBehaviorMaterialList = behaviorMaterialService.getBatchBehaviorMaterialList(context.getTenantId(), arg.getCategoryApiName());
        result.setBehaviorMaterialList(batchBehaviorMaterialList);
        return result;    }

    private String getLogMessage(String ruleLabel) {
        return "行为积分规则:" + ruleLabel + ",对象:销售线索";
    }

    private void validRule(CreateRuleOrUpdate.Arg arg) {
        if (arg.getRuleItems().size() > RuleDataConstant.MAX_RULE_GROUP) {
            throw new ValidateException(IntegralErrorCode.MORE_THAN_MAX_RULE_GROUP.getMessage());
        }

        arg.getRuleItems().forEach(ruleItem -> {
            if (ruleItem.getBranches().size() > RuleDataConstant.MAX_RULE_ITEM) {
                throw new ValidateException(IntegralErrorCode.MORE_THAN_MAX_RULE_ITEM.getMessage());
            }
        });
    }
}
