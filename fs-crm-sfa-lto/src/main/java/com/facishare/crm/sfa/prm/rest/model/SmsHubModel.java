package com.facishare.crm.sfa.prm.rest.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-03-11
 * ============================================================
 */
public interface SmsHubModel {

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class SendSmsArg {
        // 必填
        private String ea;
        // 模板id
        private String templateId;
        // 必填
        private Integer channelType;
        // 场景类型，发送方使用场景，每个发送方默认分配1-20数值，共20个数值，发送方可根据自己的需求自行定义场景对应的数值，后续做安全拦截时需要用到
        private String sceneType;
        // 发送方具体业务，文本描述，非必填
        private String businessType;
        // 发送对象，非必填
        private String receiver;
        // 发送节点，非必填
        private String sendNode;
        // 发送节点类型，非必填，processing:流程中节点，end:流程结束
        private String nodeType;
        // 对象id，非必填
        private String objectId;

        private List<PhoneData> phoneDataList;
    }

    @Data
    class PhoneData {
        //必填，手机号集合，支持向多个手机号发送相同内容
        private List<String> phoneList;
        // 变量参数，例如：{"name":"张三","age":"18"}
        private Map<String, String> paramMap;
    }
}
