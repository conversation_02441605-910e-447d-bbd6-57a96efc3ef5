package com.facishare.crm.sfa.lto.accountreerelation;

import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;
import java.util.Map;

public interface IAccountMainDataMatchService {
    void matchAccountMainData(String tenantId, Map<String, String> accountMainDataMap, List<String> nameList);
    List<IObjectData> getMatchedAccountMainData(String tenantId, List<String> objectIds);
}
