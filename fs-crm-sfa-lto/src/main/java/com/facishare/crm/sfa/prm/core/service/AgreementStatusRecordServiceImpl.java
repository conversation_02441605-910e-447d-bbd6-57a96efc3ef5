package com.facishare.crm.sfa.prm.core.service;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.sfa.prm.api.channel.AgreementStatusRecordService;
import com.facishare.crm.sfa.prm.api.channel.ChannelDataChangeService;
import com.facishare.crm.sfa.prm.api.dto.AgreementStatusRecordDTO;
import com.facishare.crm.sfa.prm.api.enhancer.DescribeEnhancer;
import com.facishare.crm.sfa.prm.api.enums.SignStatus;
import com.facishare.crm.sfa.prm.core.constants.ChannelConstants;
import com.facishare.crm.sfa.prm.platform.utils.DataUtils;
import com.facishare.crm.sfa.prm.platform.utils.SystemPropertiesUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static com.facishare.crm.sfa.prm.core.constants.PrmI18NConstants.SFA_CHANNEL_MANAGEMENT_TITLE;
import static com.facishare.crm.sfa.prm.core.constants.PrmI18NConstants.SFA_CHANNEL_STATUS_RECORD_TITLE;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-03-20
 * ============================================================
 */
@Service
@Slf4j
public class AgreementStatusRecordServiceImpl implements AgreementStatusRecordService {
    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private ChannelDataChangeService channelDataChangeService;
    @Resource
    private DescribeEnhancer describeEnhancer;

    @Override
    public void record(User user, AgreementStatusRecordDTO statusRecord) {
        if (statusRecord.getChangeBeforeStatus() == statusRecord.getChangeAfterStatus()) {
            return;
        }
        IObjectData objectData = convertObjectData(user.getTenantId(), statusRecord);
        IObjectData saveObjectData = serviceFacade.saveObjectData(user, objectData);
        IObjectDescribe objectDescribe = describeEnhancer.fetchObject(user, AGREEMENT_STATUS_RECORD_OBJ);
        if (objectDescribe == null) {
            log.warn("objectDescribe is null");
            return;
        }
        serviceFacade.logWithCustomMessage(user, EventType.ADD, ActionType.Add, objectDescribe,
                Lists.newArrayList(saveObjectData), Maps.newHashMap(), Lists.newArrayList(),
                SystemPropertiesUtils.getAppName(), SFA_CHANNEL_MANAGEMENT_TITLE,
                I18N.text(SFA_CHANNEL_STATUS_RECORD_TITLE));
    }

    @Override
    public IObjectData changeSignStatusWithRecord(User user, String rationale, String objectApiName, IObjectData objectData, SignStatus targetStatus) {
        SignStatus originalSignStatus = SignStatus.find(DataUtils.getValue(objectData, ChannelConstants.SIGNING_STATUS, String.class, null), null);
        return changeSignStatusWithRecord(user, rationale, objectApiName, objectData, targetStatus, originalSignStatus);
    }

    @Override
    public IObjectData changeSignStatusWithRecord(User user, String rationale, String objectApiName, IObjectData objectData, SignStatus targetStatus, SignStatus originalSignStatus) {
        IObjectData changedData = channelDataChangeService.changeSignStatus(user, objectData, targetStatus);
        Long changeStatusTime = DataUtils.getValue(changedData, LAST_MODIFY_TIME, Long.class, System.currentTimeMillis());
        AgreementStatusRecordDTO recordDTO = AgreementStatusRecordDTO.builder()
                .changeBeforeStatus(originalSignStatus)
                .changeAfterStatus(targetStatus)
                .objectApiName(objectApiName)
                .dataId(objectData.getId())
                .changeRationale(rationale)
                .changeStatusTime(changeStatusTime)
                .build();
        record(user, recordDTO);
        return changedData;
    }

    private IObjectData convertObjectData(String tenantId, AgreementStatusRecordDTO statusRecord) {
        IObjectData data = ObjectDataDocument.of(Maps.newHashMap()).toObjectData();
        data.setTenantId(tenantId);
        data.setDescribeApiName(AGREEMENT_STATUS_RECORD_OBJ);
        data.setRecordType("default__c");
        data.set(CHANGE_BEFORE_STATUS, Optional.ofNullable(statusRecord.getChangeBeforeStatus()).map(SignStatus::getStatus).orElse(null));
        data.set(CHANGE_AFTER_STATUS, Optional.ofNullable(statusRecord.getChangeAfterStatus()).map(SignStatus::getStatus).orElse(null));
        data.set(CHANGE_RATIONALE, statusRecord.getChangeRationale());
        Map<String, String> map = new HashMap<>(2);
        map.put(DESCRIBE_API_NAME, statusRecord.getObjectApiName());
        map.put(ID, statusRecord.getDataId());
        data.set(RELATED_OBJECT_DATA, Lists.newArrayList(map));
        data.set(RELATED_API_NAMES, Lists.newArrayList(statusRecord.getObjectApiName()));
        data.set(CHANGE_STATUS_TIME, statusRecord.getChangeStatusTime());
        data.setOwner(Lists.newArrayList(User.SUPPER_ADMIN_USER_ID));
        return data;
    }
}
