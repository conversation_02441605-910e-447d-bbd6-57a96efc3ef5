package com.facishare.crm.sfa.lto.qywx.proxy.models;

import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface QywxUnassignedExternalContactModel {

  @Data
	@Builder
	class Arg {
    /**
     * 必填
     */
    private String ea;
    /**
     * 分页查询，要查询页号，从0开始
     */
    private Integer pageId;
    /**
     * 分页查询游标，字符串类型，适用于数据量较大的情况，如果使用该参数则无需填写page_id，该参数由上一次调用返回
     */
    private String cursor;
    /**
     * 每次返回的最大记录数，默认为1000，最大值为1000
     */
    private Integer pageSize;
  }

  @Data
  class UnassignedExternalContactResult {
    private List<QyweixinUnassigned> info;
    private Boolean isLast = false;//是否是最后一条记录
    private String nextCursor;//分页查询游标,已经查完则返回空("")，使用page_id作为查询参数时不返回
  }

  @Data
  class QyweixinUnassigned  {
    private String handoverUserId;//离职成员的userid
    private String externalUserId;//外部联系人userid
    private Long diMissionTime;//成员离职时间
  }

}