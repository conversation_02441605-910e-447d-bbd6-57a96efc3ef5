package com.facishare.crm.sfa.prm.platform.infrastructure.statemachine.core;

import com.facishare.crm.sfa.prm.platform.infrastructure.statemachine.rule.TransitionRule;
import com.facishare.crm.sfa.prm.platform.infrastructure.statemachine.exception.StateMachineException;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-04-09
 * ============================================================
 */
@Component
public class StateMachineDispatcher implements ApplicationContextAware {
    private final Map<String, TransitionRule> TRANSITIONS = new ConcurrentHashMap<>();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, TransitionRule> springBeanMapping = applicationContext.getBeansOfType(TransitionRule.class);
        springBeanMapping.values().stream()
                .filter(bean -> bean.getTransitionKey() != null && !bean.getTransitionKey().isEmpty())
                .forEach(bean -> {
                    TransitionRule transitionRule = TRANSITIONS.putIfAbsent(bean.getTransitionKey(), bean);
                    if (transitionRule != null) {
                        throw new StateMachineException("Duplicate transitionKey found: " + bean.getTransitionKey());
                    }
                });
    }

    public <S, E, C> StateMachine<S, E, C> newStateMachine(String transitionKey) {
        @SuppressWarnings("unchecked")
        TransitionRule<S, E, C> transitionRule = TRANSITIONS.get(transitionKey);
        if (transitionRule == null) {
            throw new StateMachineException("can't find transition by transitionKey" + transitionKey);
        }
        return new StateMachine<>(transitionRule.getTransitionRules());
    }
}
