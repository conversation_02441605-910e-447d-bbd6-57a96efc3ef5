package com.facishare.crm.sfa.prm.platform.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import lombok.Data;

import java.util.function.Supplier;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-03-10
 * ============================================================
 */
@Data
public class RestResponse {
    private int errCode;
    private String errMessage;
    private String errMsg;
    // 兼容返回结果是 result 和 data 两种情况
    private Object result;
    private Object data;

    public boolean isSuccess() {
        return result != null || data != null;
    }

    public <T> T getResponse(Supplier<TypeReference<T>> typeReferenceSupplier) {
        if (result != null) {
            return JSON.parseObject(JSON.toJSONString(result), typeReferenceSupplier.get());
        }
        if (data != null) {
            return JSON.parseObject(JSON.toJSONString(data), typeReferenceSupplier.get());
        }
        return null;
    }
}
