package com.facishare.crm.sfa.prm.api.channel;

import com.facishare.crm.sfa.prm.api.dto.AdmissionConfigDTO;
import com.facishare.paas.appframework.core.model.User;

/**
 * ============================================================
 *
 * @Description: 渠道准入后台管理服务
 * @CreatedBy: Sundy on 2025-02-27
 * ============================================================
 */
public interface ChannelAdmissionService {
    String ADMISSION_CONFIG = "admission_config";
    String OPEN_CHANNEL_ACCESS = "open_channel_access";

    AdmissionConfigDTO fetchChannelAdmissionConfig(User user);

    String fetchOpenChannelAccess(User user);
}
