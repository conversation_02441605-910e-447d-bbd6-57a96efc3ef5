package com.facishare.crm.sfa.prm.rest.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-03-17
 * ============================================================
 */
public interface TemplateModel {
    @Data
    class Arg {
        private String templateId;
        private String objectId;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        private String subject;
        private String template;
        private boolean success;
        private String errorMessage;
    }
}
