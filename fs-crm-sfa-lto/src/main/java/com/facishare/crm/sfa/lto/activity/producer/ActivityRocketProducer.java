package com.facishare.crm.sfa.lto.activity.producer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.common.message.Message;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/12/9 11:16
 * @description: 活动相关的 MQ 生产者
 */
@Slf4j
@Component
public class ActivityRocketProducer {

    private static final String MQ_PRODUCER_CONFIG_NAME = "fs-crm-task-sfa-mq.ini";
    private static final String MQ_PRODUCER_CONFIG_SECTION_NAME = "sfa-ai-activity-producer";
    private static final String ACTIVITY_TO_TEXT_TAG = "activity-to-text";

    private AutoConfMQProducer producer;

    @PostConstruct
    public void init() {
        producer = new AutoConfMQProducer(MQ_PRODUCER_CONFIG_NAME, MQ_PRODUCER_CONFIG_SECTION_NAME);
    }

    @PreDestroy
    public void destroy() {
        if (producer != null) {
            producer.close();
        }
    }

    /**
     * 发送活动消息
     *
     * @param tag 消息标签
     * @param activityMessage 活动消息对象
     */
    public void sendActivityMessage(String tag, ActivityMessage activityMessage) {
        if (activityMessage == null) {
            log.error("活动消息对象不能为空");
            return;
        }
        if (StringUtils.isBlank(activityMessage.getTenantId())) {
            log.error("租户ID不能为空, message: {}", activityMessage);
            return;
        }

        JSONObject messageObject = (JSONObject) JSON.toJSON(activityMessage);
        sendMessage(tag, messageObject, activityMessage.getTenantId());
    }

    /**
     * 发送活动转文本的消息
     *
     * @param activityMessage 活动消息对象
     */
    public void sendActivityToTextMessage(ActivityMessage activityMessage) {
        sendActivityMessage(ACTIVITY_TO_TEXT_TAG, activityMessage);
    }

    /**
     * 快速构建并发送活动消息<br/>
     * 过时方法，请使用 {@link #sendActivityToTextMessage(String, String, String, String)}
     *
     * @param tenantId 租户ID
     * @param objectId 对象ID
     * @param objectApiName 对象API名称
     */
    @Deprecated
    public void sendActivityToTextMessage(String tenantId, String objectId, String objectApiName) {
        log.error("Multilingual methods are not supported!");
        ActivityMessage message = ActivityMessage.builder()
                .tenantId(tenantId)
                .objectId(objectId)
                .objectApiName(objectApiName)
                .build();
        sendActivityToTextMessage(message);
    }

    /**
     * 快速构建并发送活动消息
     *
     * @param tenantId 租户ID
     * @param objectId 对象ID
     * @param objectApiName 对象API名称
     * @param userId 用户ID
     */
    public void sendActivityToTextMessage(String tenantId, String objectId, String objectApiName, String userId) {
        ActivityMessage message = ActivityMessage.builder()
                .tenantId(tenantId)
                .objectId(objectId)
                .objectApiName(objectApiName)
                .opId(userId)
                .build();
        sendActivityToTextMessage(message);
    }

    /**
     * 通用的消息发送方法
     *
     * @param tag 消息标签
     * @param messageObject 消息内容
     * @param tenantId 租户ID
     */
    public void sendMessage(String tag, JSONObject messageObject, String tenantId) {
        if (StringUtils.isBlank(tag)) {
            log.error("消息tag不能为空，tenantId: {}, message: {}", tenantId, messageObject);
            return;
        }

        Message message = getMessage(tag, messageObject, tenantId);
        try {
            SendResult sendResult = producer.send(message);
            if (sendResult.getSendStatus() != SendStatus.SEND_OK) {
                log.error("发送消息失败，tag: {}, tenantId: {}, message: {}, status: {}", 
                    tag, tenantId, messageObject, sendResult.getSendStatus());
                return;
            }
            log.info("发送消息成功，tag: {}, tenantId: {}, message: {}", tag, tenantId, messageObject);
        } catch (Exception e) {
            log.error("发送消息异常，tag: {}, tenantId: {}, message: {}", tag, tenantId, messageObject, e);
        }
    }

    /**
     * 组装消息
     */
    private Message getMessage(String tags, JSONObject messageObject, String tenantId) {
        String messageString = JSON.toJSONString(messageObject);
        Message message = new Message(producer.getDefaultTopic(), tags, messageString.getBytes());
        message.putUserProperty("x-fs-ei", tenantId);
        message.setTags(tags);
        return message;
    }
}
