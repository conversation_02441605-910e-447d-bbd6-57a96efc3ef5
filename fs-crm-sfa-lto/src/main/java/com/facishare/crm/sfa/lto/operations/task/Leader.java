package com.facishare.crm.sfa.lto.operations.task;

import com.facishare.paas.metadata.api.IObjectData;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;

class Leader implements TemplateMemberField {

    private final BiConsumer<OperationsTask, List<String>> setter;
    private final Map<String, String> map;

    Leader(BiConsumer<OperationsTask, List<String>> setter, Map<String, String> map) {
        this.setter = setter;
        this.map = map;
    }

    @Override
    public List<String> getMember(IObjectData bizData) {
        List<String> leaders = new ArrayList<>();
        for (String owner : bizData.getOwner()) {
            String leader = map.get(owner);
            if (leader != null) {
                leaders.add(leader);
            }
        }
        return leaders;
    }

    @Override
    public BiConsumer<OperationsTask, List<String>> setter() {
        return setter;
    }
}
