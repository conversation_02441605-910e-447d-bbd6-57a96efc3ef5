package com.facishare.crm.sfa.lto.loyalty.service.memberOperate;

import com.facishare.crm.sfa.lto.loyalty.constants.LoyaltyConstants;
import com.facishare.crm.sfa.lto.loyalty.i18n.LoyaltyI18nException;
import com.facishare.crm.sfa.lto.loyalty.model.Loyalty;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

@Service
public class LoyaltySetTierService extends AbstractLoyaltyPointsOperateService {


    @Override
    public Loyalty.PointsOperationParam.Type type() {
        return Loyalty.PointsOperationParam.Type.SET_LEVEL;
    }

    @Transactional
    @Override
    public void action(Loyalty.PointsOperationParam param) {
        String tenantId = param.getTenantId();
        if (StringUtils.isEmpty(param.getTierId())) {
            throw LoyaltyI18nException.buildMissingParametersException("tierId");
        }
        IObjectData memberInfo = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), param.getMemberId(), LoyaltyConstants.LoyaltyMember.API_NAME);
        String changeTierBefore = memberInfo.get(LoyaltyConstants.LoyaltyMember.TIER_ID, String.class);
        //更新员工
        Map<String, Object> memberUpdateFields = new HashMap<>();
        memberUpdateFields.put(LoyaltyConstants.LoyaltyMember.TIER_ID, param.getTierId());
        memberUpdateFields.put(LoyaltyConstants.LoyaltyMember.TIER_START_TIME, System.currentTimeMillis());
        serviceFacade.updateWithMap(User.systemUser(tenantId), memberInfo, memberUpdateFields);
        //插入记录
        ObjectDataDocument recordDataDocument = new ObjectDataDocument();
        recordDataDocument.put(LoyaltyConstants.LoyaltyMemberChangeRecords.LOY_CHANGE_TYPE, Loyalty.PointsOperationParam.Type.SET_LEVEL.toString());
        recordDataDocument.put(LoyaltyConstants.LoyaltyMemberChangeRecords.LOY_CHANGE_TIER_BEFORE, changeTierBefore);
        recordDataDocument.put(LoyaltyConstants.LoyaltyMemberChangeRecords.LOY_CHANGE_TIER_AFTER, param.getTierId());
        saveMemberChangeRecord(param, Lists.newArrayList(recordDataDocument));
    }

    @Override
    public void fallback(Loyalty.FallbackUpdateData updateData, Loyalty.FallbackInfo fallbackInfo) {
        //暂不支持等级回退
    }

}
