package com.facishare.crm.sfa.lto.activity.util;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.I18N;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> gongchunru
 * @date : 2025/4/11 16:06
 * @description:
 */
public class ActivityProcessesContentUtils {

    /**
     * 会议进行中
     */
    private static final String MEETING_PROCESSING = "sfa.activity.processes.meeting.processing";
    /**
     * AI洞察生成中
     */
    private static final String AI_GENERATING = "sfa.activity.processes.ai.generating";

    public static void setMeetingProcessingContent(IObjectData objectData) {
        String content = createActiveRecordContent(MEETING_PROCESSING);
        objectData.set("active_record_content",StringUtils.isBlank(content) ? "" :I18N.text(MEETING_PROCESSING));
        objectData.set("active_record_content__e", I18N.text(MEETING_PROCESSING));
    }

    public static void setAiGeneratingContent(IObjectData objectData) {
        String content = createActiveRecordContent(AI_GENERATING);
        objectData.set("active_record_content",StringUtils.isBlank(content) ? "" :I18N.text(AI_GENERATING));
        objectData.set("active_record_content__e", I18N.text(AI_GENERATING));
    }

    /**
     * 创建会议进行中的活动记录内容
     *
     * @param text 文本内容
     * @return 活动记录内容JSON字符串
     */
    public static String createActiveRecordContent(String text) {
        // 创建marks列表
        List<Map<String, Object>> marks = buildMarks("#903DFF", "13px");

        // 使用buildContent构建段落内容
        Map<String, Object> paragraph = buildContent(text, marks);

        // 创建doc
        Map<String, Object> doc = new HashMap<>();
        doc.put("type", "doc");
        doc.put("content", Lists.newArrayList(paragraph));

        // 创建xt
        Map<String, Object> xt = new HashMap<>();
        Map<String, Object> json = new HashMap<>();
        json.put("type", "doc");
        json.put("content", Lists.newArrayList(paragraph));
        xt.put("__json", json);
        xt.put("__summeryNodeCount", 1);
        xt.put("version", 2);

        // 创建最终结果
        Map<String, Object> result = new HashMap<>();
        result.put("content", text);
        result.put("__xt", xt);
        result.put("cmpt", "XT_TEXT");

        return JSON.toJSONString(result);
    }


    /**
     * 构建文本标记列表
     *
     * @param color 文本颜色
     * @param fontSize 字体大小
     * @return 标记列表
     */
    private static List<Map<String, Object>> buildMarks(String color, String fontSize) {
        List<Map<String, Object>> marks = Lists.newArrayList();

        // 添加bold标记
        Map<String, Object> boldMark = new HashMap<>();
        boldMark.put("type", "bold");
        marks.add(boldMark);

        // 添加textStyle标记
        Map<String, Object> textStyleMark = new HashMap<>();
        textStyleMark.put("type", "textStyle");
        Map<String, Object> textStyleAttrs = new HashMap<>();
        textStyleAttrs.put("color", color);
        textStyleAttrs.put("fontSize", fontSize);
        textStyleMark.put("attrs", textStyleAttrs);
        marks.add(textStyleMark);

        return marks;
    }


    private static Map<String, Object> buildContent(String text, List<Map<String, Object>> marks) {
        Map<String, Object> contentMap = new HashMap<>();
        contentMap.put("type", "text");
        contentMap.put("text", removeMarkdownSymbols(text));

        if (!CollectionUtils.isEmpty(marks)) {
            contentMap.put("marks", marks);
        }

        List<Object> contentList = Lists.newArrayList();
        contentList.add(contentMap);

        Map<String, Object> attrs = new HashMap<>();
        attrs.put("textAlign", "left");

        Map<String, Object> paragraph = new HashMap<>();
        paragraph.put("type", "paragraph");
        paragraph.put("attrs", attrs);
        paragraph.put("content", contentList);
        return paragraph;
    }

    private static String removeMarkdownSymbols(String text) {
        // 实现移除Markdown符号的逻辑
        return text;
    }

}

