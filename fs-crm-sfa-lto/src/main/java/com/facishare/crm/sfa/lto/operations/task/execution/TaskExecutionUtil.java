package com.facishare.crm.sfa.lto.operations.task.execution;

import org.redisson.executor.CronExpression;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;
import java.util.StringJoiner;
import java.util.TimeZone;

public class TaskExecutionUtil {
    private TaskExecutionUtil() {}

    public static Date getNextTimeAfterNow(TaskExecutionFrequency frequency, String exp, LocalTime time, TimeZone timeZone) {
        return getNextTimeAfter(frequency, exp, time, timeZone, new Date());
    }

    /**
     * <ul>
     *  <li>frequency = 一次 exp = 时间戳
     *  <li>frequency = 每天 exp可为空
     *  <li>frequency = 每周  exp = 1,2,3,4,5,6   (1-7, 星期日,星期一,...星期六)
     *  <li>frequency = 每月  exp = 1,2,3...,31 or L    (1-31, L代表最后一天)
     *  <li>frequency = 每季度  exp = 1,2,3|1,2,3...31 or L (1-3 | 1-31, L代表最后一天)
     *  <li>frequency = 每年 exp = 标准cron
     * </ul>
     *
     * @param frequency 频率
     * @param exp       表达式
     * @param time      当日执行时间
     * @param timeZone  时区
     */
    public static Date getNextTimeAfter(TaskExecutionFrequency frequency, String exp, LocalTime time, TimeZone timeZone, Date after) {
        int hour = time.getHour();
        String expression;
        switch (frequency) {
            case REBOOT:
                LocalDateTime temp = LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(exp)), timeZone.toZoneId());
                // **WARNING** cron中必须含有年份
                expression = toCronExpression(hour, temp.getDayOfMonth(), temp.getMonth().getValue(), temp.getYear());
                break;
            case DAILY:
                expression = toCronExpression(hour, "*", "*", "?");
                break;
            case WEEKLY:
                expression = toCronExpression(hour, "?", "*", exp);
                break;
            case MONTHLY:
                expression = toCronExpression(hour, exp, "*", "?");
                break;
            case QUARTERLY:
                String[] split = exp.split("\\|");
                expression = toCronExpression(hour, split[1], toMonth(split[0]), "?");
                break;
            case ANNUALLY:
                String[] split1 = exp.split(" ");
                split1[0] = "0";
                split1[1] = "0";
                split1[2] = String.valueOf(hour);
                expression = String.join(" ", split1);
                break;
            default:
                throw new IllegalArgumentException("Unsupported frequency: " + frequency);
        }
        CronExpression cronExpression = new CronExpression(expression);
        cronExpression.setTimeZone(timeZone);
        return cronExpression.getNextValidTimeAfter(after);
    }

    /**
     * @param hour       0-23
     * @param dayOfMonth 1-31 (or L, last day of month)
     * @param month      1-12 (or names, JAN-DEC)
     */
    private static String toCronExpression(int hour, String dayOfMonth, String month, String dayOfWeek) {
        return "0 0 " + hour + " " + dayOfMonth + " " + month + " " + dayOfWeek;
    }

    private static String toCronExpression(int hour, int dayOfMonth, int month, int year) {
        return "0 0 " + hour + " " + dayOfMonth + " " + month + " ? " + year;
    }

    /**
     * @param monthOfQuarterExp 1,2,3
     * @return month 1-12
     */
    private static String toMonth(String monthOfQuarterExp) {
        StringJoiner joiner = new StringJoiner(",");
        for (String monthOfQuarter : monthOfQuarterExp.split(",")) {
            int m = Integer.parseInt(monthOfQuarter);
            for (int i = 0; i < 4; i++) {
                joiner.add(Integer.toString(m + i * 3));
            }
        }
        return joiner.toString();
    }
}
