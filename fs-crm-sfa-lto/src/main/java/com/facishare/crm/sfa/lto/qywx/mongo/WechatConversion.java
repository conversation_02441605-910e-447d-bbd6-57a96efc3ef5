package com.facishare.crm.sfa.lto.qywx.mongo;

import lombok.Data;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Property;

import java.util.List;

@Data
@Entity(value = "message_save_", noClassnameStored = true)
public class WechatConversion {

	public static final String FIELD_ROOM_ID = "roomId";
	public static final String FIELD_FROM_USER = "fromUser";
	public static final String FIELD_TO_LIST = "toList";
	public static final String FIELD_SEQ = "seq";
	public static final String FIELD_FS_EA = "fsEa";
	public static final String FIELD_MSG_TIME = "messageTime";
	public static final String FIELD_MESSAGE_TIME = "messageTime";
	public static final String FIELD_MESSAGE_TYPE= "messageType";
	public static final String FIELD_CONTENT = "content";
	public static final String FIELD_DESCRY_KEY = "descryKey";
	public static final String FIELD_APP_ID = "appId";


	@Id
	private String id;
	@Property("fsEa")
	private String fsEa;
	@Property("messageId")
	private String messageId;
	@Property("seq")
	private Integer seq;
	@Property("keyVersion")
	private Integer keyVersion;
	@Property("fromUser")
	private String fromUser;
	@Property("toList")
	private List<String> toList;
	@Property("roomId")
	private String roomId;
	@Property("messageTime")
	private Long messageTime;
	@Property("messageType")
	private String messageType;
	@Property("content")
	private String content;
	@Property("md5sum")
	private String md5sum;
	@Property("sdkFileId")
	private String sdkFileId;
	@Property("fileSize")
	private Integer fileSize;
	@Property("attachment")
	private String attachment;
	@Property("npath")
	private String npath;
	@Property("fileName")
	private String fileName;
	@Property("fileExt")
	private String fileExt;
	@Property("createTime")
	private long createTime;
	@Property("updateTime")
	private long updateTime;
	@Property("descryKey")
	private String descryKey;
	@Property("appId")
	private String appId;

	/**
	 * 存了企微返回的原始数据
	 * @see <a href="url">https://developer.work.weixin.qq.com/document/path/91774#%E8%AF%AD%E9%9F%B3<a/>
	 */
	private String messageData;

	private Integer playLength;

}
