package com.facishare.crm.sfa.lto.activity.mongo;


import lombok.Data;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Id;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;

@Data
@Entity(value = "interaction_strategy_task", noClassnameStored = true)
public class InteractionStrategyTaskDocument implements Serializable {
    @Id
    private ObjectId id; // MongoDB主键

    @Property("tenantId")
    private String tenantId; // 企业id

    @Property("strategyId")
    private String strategyId; // 策略id

    /**
     * 任务状态
     * init-初始化
     * running-执行中
     * finished-执行结束
     * fail-执行失败
     */
    @Property("status")
    private String status;

    @Property("failReason")
    private String failReason; // 失败原因

    @Property("submitTotal")
    private Integer submitTotal; // 提交任务时，客户总数

    @Property("runningTotal")
    private Integer runningTotal; // 任务执行时，客户总数

    @Property("batchTotal")
    private Integer batchTotal; // 批次总数

    @Property("runningBatchTotal")
    private Integer runningBatchTotal; // 当前执行批次

    @Property("createBy")
    private String createBy; // 创建人

    @Property("createTime")
    private Long createTime; // 创建时间

    @Property("lastModifyTime")
    private Long lastModifyTime; // 最后修改时间
}
