package com.facishare.crm.sfa.lto.integral.core.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

public interface CheckRule {

    @Data
    class Arg {
        @JSONField(name = "api_name")
		@JsonProperty("api_name")
        String ruleApiName;
        @JSONField(name = "label")
		@JsonProperty("label")
        private String label;
    }

    @Data
    class Result {
    }
}
