package com.facishare.crm.sfa.lto.procurement;

import com.facishare.crm.sfa.lto.procurement.models.LtoProcurementConstants.CommonConstants;
import com.facishare.crm.sfa.lto.procurement.models.LtoProcurementConstants.ProcurementInfo;
import com.facishare.crm.sfa.lto.utils.CommonSqlUtil;
import com.facishare.enterprise.common.util.CollectionUtil;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.CommonSqlOperator;
import com.facishare.paas.metadata.impl.search.WhereParam;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @ClassName com.facishare.crm.sfa.lto.procurement.ProcurementMarkingService
 * @Description
 * <AUTHOR>
 * @Date 2022/10/28 18:20
 * @Version 1.0
 **/
@Slf4j
@Component
public class LtoProcurementInfoService {
    @Autowired
    private LtoProcurementDataService ltoProcurementDataService;

    /**
     * 检索公告需比对的源数据
     * @param ids ：公告id, DB主键id
     * @param tenantId
     * @return
     */
    public List<IObjectData> search(List<String> ids, String tenantId){
        return ltoProcurementDataService.findByIdList(ids, tenantId, ProcurementInfo.NAME_OBJ);
    }


    /**
     * 通过Enterprise检索公告源数据
     * @param id ：Enterprise id
     * @param tenantId
     * @return
     */
    public List<Map> searchByEnterpriseId(String id, String tenantId){
        return ltoProcurementDataService.searchInfoByEnterpriseId(id, tenantId);
    }

    /**
     * 比对后更新对应公告数据
     * update biz_procurement_info set caller_status='{EnterpriseInfoObj, AccountObj, LeadsObj} , 'winner_status = '' where id = '634cf0e57197c611da66ec81' and is_delete = 0
     * @param id
     * @param tenantId
     * @param c_status
     * @param w_status
     */
    public void markStatus(String id, String tenantId, List<String> c_status, List<String> w_status){
        Map<String, Object> dataMap = Maps.newHashMap();
        if(!CollectionUtil.isEmpty(c_status) ){
            dataMap.put(ProcurementInfo.CALLER_STATUS, c_status);
        }
        if(!CollectionUtil.isEmpty(w_status) ){
            dataMap.put(ProcurementInfo.WINNER_STATUS, w_status);
        }
        dataMap.put(CommonConstants.LAST_MODIFIED_TIME, System.currentTimeMillis());

        List<WhereParam> whereParams = Lists.newArrayList();
        CommonSqlUtil.addWhereParam(whereParams, CommonConstants.ID, CommonSqlOperator.EQ, Lists.newArrayList(id));
        CommonSqlUtil.addWhereParam(whereParams, CommonConstants.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(tenantId));
        CommonSqlUtil.addWhereParam(whereParams, CommonConstants.IS_DELETED, CommonSqlOperator.EQ, Lists.newArrayList(0));

        int row = 0;
        try {
            row = CommonSqlUtil.updateData(tenantId, ProcurementInfo.TABLE_NAME, dataMap, whereParams);
        } catch (MetadataServiceException e) {
            log.error("updateProcurementInfo error", e);
        }
        log.info("mark:{}, dataMap:{}", row, dataMap);
    }
}