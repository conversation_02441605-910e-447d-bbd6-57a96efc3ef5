package com.facishare.crm.sfa.prm.rest.client;

import com.facishare.crm.sfa.prm.platform.model.RestResponse;
import com.facishare.crm.sfa.prm.rest.model.SmsHubModel;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-03-11
 * ============================================================
 */
@RestResource(value = "SMS_HUB", desc = "短信平台", contentType = "application/json", codec = "com.facishare.paas.appframework.metadata.util.CRMRestServiceCodec")
public interface SmsHubClient {
    @POST(value = "smsPlatformOut/send", desc = "发送短信")
    RestResponse send(@HeaderMap Map<String, String> headers, @Body SmsHubModel.SendSmsArg arg);
}
