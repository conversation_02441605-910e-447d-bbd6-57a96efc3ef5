package com.facishare.crm.sfa.lto.objectlimit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.expression.SFAExpressionServiceImpl;
import com.facishare.crm.sfa.lto.common.LtoLicenseService;
import com.facishare.crm.sfa.lto.common.LtoOrgCommonService;
import com.facishare.crm.sfa.lto.common.models.LtoFieldApiConstants;
import com.facishare.crm.sfa.lto.objectlimit.models.*;
import com.facishare.crm.sfa.lto.utils.ActionContextUtil;
import com.facishare.crm.sfa.lto.utils.ObjectDataUtil;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ObjectLimitService {
    @Autowired
    private ObjectLimitRuleService objectLimitRuleService;
    @Autowired
    private SFAExpressionServiceImpl sfaExpressionService;
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private LtoOrgCommonService ltoOrgCommonService;
    @Autowired
    private LtoLicenseService ltoLicenseService;

    private static final String OBJECT_LIMIT_APP = "accounts_leads_limit_app";

    public CheckLimitResult checkObjectLimit(User user, String objectApiName, String owner, List<IObjectData> objectDataList, IObjectDescribe objectDescribe) {
        return checkObjectLimit(user, objectApiName, owner, objectDataList, objectDataList, objectDescribe);
    }

    public CheckLimitResult checkObjectLimit(User user, String objectApiName, List<IObjectData> objectDataList, IObjectDescribe objectDescribe) {
        CheckLimitResult result = CheckLimitResult.builder().successIds(Lists.newArrayList())
                .failureIds(Lists.newArrayList()).build();
        if (CollectionUtils.empty(objectDataList) || StringUtils.isEmpty(objectApiName)) {
            return result;
        }
        Set<String> ownerIds = objectDataList.stream().filter(ObjectDataUtil::hasOwner)
                .map(ObjectDataUtil::getOwner).collect(Collectors.toSet());
        if (CollectionUtils.empty(ownerIds)) {
            return result;
        }

        for (String owner : ownerIds) {
            List<IObjectData> dataList = objectDataList.stream()
                    .filter(x -> ObjectDataUtil.hasOwner(x) && owner.equals(ObjectDataUtil.getOwner(x)))
                    .collect(Collectors.toList());
            CheckLimitResult checkLimitResult = checkObjectLimit(user, objectApiName, owner, dataList, objectDescribe);
            if (CollectionUtils.notEmpty(checkLimitResult.getFailureIds())) {
                result.getFailureIds().addAll(checkLimitResult.getFailureIds());
                break;
            }
            if (CollectionUtils.notEmpty(checkLimitResult.getSuccessIds())) {
                result.getSuccessIds().addAll(checkLimitResult.getSuccessIds());
            }
        }

        return result;
    }

    public CheckLimitResult checkObjectLimit(User user, String objectApiName, String owner, List<IObjectData> copyOfObjectDataList, List<IObjectData> dataList, IObjectDescribe objectDescribe) {
        CheckLimitResult result = CheckLimitResult.builder().successIds(Lists.newArrayList())
                .failureIds(Lists.newArrayList()).build();

        List<String> objectIds = copyOfObjectDataList.stream().map(DBRecord::getId).collect(Collectors.toList());
        List<ObjectLimitRuleModel.ObjectLimitRule> employeeLimitRuleList = objectLimitRuleService.getObjectLimitRuleByEmployeeId(user, objectApiName, owner);
        log.info("checkObjectLimit objectIds:{}, employeeLimitRuleList :{}", objectIds, employeeLimitRuleList);
        if (CollectionUtils.empty(employeeLimitRuleList)) {
            result.getSuccessIds().addAll(objectIds);
            return result;
        }

        List<ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter> globalFilterList = objectLimitRuleService.getObjectLimitRuleGlobalFilter(user.getTenantId(), objectApiName);
        Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> limitFilterDataListMap = getLimitFilterDataListMap(globalFilterList, employeeLimitRuleList, copyOfObjectDataList, objectDescribe);
        if (CollectionUtils.empty(limitFilterDataListMap)) {
            result.getSuccessIds().addAll(objectIds);
            return result;
        }
        List<ObjectLimitRuleModel.ObjectLimitRule> employeeRuleTypeLimitList = employeeLimitRuleList.stream().filter(x -> ObjectLimitRuleModel.RuleTypeEnum.EMPLOYEE.getCode().equals(x.getRuleType())).collect(Collectors.toList());
        List<ObjectLimitRuleModel.ObjectLimitRule> organizationRuleTypeLimitList = employeeLimitRuleList.stream().filter(x -> ObjectLimitRuleModel.RuleTypeEnum.ORGANIZATION.getCode().equals(x.getRuleType())).collect(Collectors.toList());
        Map<String, List<String>> ruleIdObjectDataIdMap = getLimitFailureDataIds(user, owner, globalFilterList, limitFilterDataListMap, employeeRuleTypeLimitList, objectDescribe);
        Map<String, List<String>> ruleIdObjectDataIdMapOrganization;
        if (CollectionUtils.notEmpty(organizationRuleTypeLimitList)) {
            ruleIdObjectDataIdMapOrganization = getLimitFailureDataIdsByOrganization(user, owner, globalFilterList, limitFilterDataListMap, organizationRuleTypeLimitList, dataList, objectDescribe);
            if (CollectionUtils.notEmpty(ruleIdObjectDataIdMapOrganization)) {
                ruleIdObjectDataIdMap.putAll(ruleIdObjectDataIdMapOrganization);
            }
        }

        if (CollectionUtils.empty(ruleIdObjectDataIdMap)) {
            result.getSuccessIds().addAll(objectIds);
            return result;
        }
        Set<String> failureIds = new HashSet<>();
        Map<String, String> objectIdRuleIdMap = Maps.newHashMap();
        for (Map.Entry<String, List<String>> entry : ruleIdObjectDataIdMap.entrySet()) {
            Optional<ObjectLimitRuleModel.ObjectLimitRule> first = employeeLimitRuleList.stream().filter(x -> x.getGroupId().equals(entry.getKey())).findFirst();
            if (first.isPresent()) {
                String name = first.get().getName();
                for (String objectId : entry.getValue()) {
                    objectIdRuleIdMap.put(objectId, name);
                    failureIds.add(objectId);
                }
            }
        }
        List<String> successIds = Lists.newArrayList(objectIds);
        successIds.removeIf(failureIds::contains);
        result.getSuccessIds().addAll(successIds);
        result.getFailureIds().addAll(failureIds);
        result.setFailureIdAndRuleName(objectIdRuleIdMap);
        return result;
    }

    public CheckLimitResult checkOutUserObjectLimit(User user, String objectApiName, String outTenantId, String outOwner, List<IObjectData> objectDataList, IObjectDescribe objectDescribe, boolean checkOrganizationLimit) {
        CheckLimitResult result = CheckLimitResult.builder().successIds(Lists.newArrayList())
                .failureIds(Lists.newArrayList()).build();

        List<String> objectIds = objectDataList.stream().map(DBRecord::getId).collect(Collectors.toList());
        List<ObjectLimitRuleModel.ObjectLimitRule> allEmployeeLimitRuleList = objectLimitRuleService.getObjectLimitRuleByEmployeeId(user, objectApiName, outOwner);
        if (CollectionUtils.empty(allEmployeeLimitRuleList)) {
            result.getSuccessIds().addAll(objectIds);
            return result;
        }

        List<ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter> globalFilterList = objectLimitRuleService.getObjectLimitRuleGlobalFilter(user.getTenantId(), objectApiName);

        List<ObjectLimitRuleModel.ObjectLimitRule> employeeLimitRuleList = allEmployeeLimitRuleList.stream()
                .filter(x -> ObjectLimitRuleModel.RuleTypeEnum.EMPLOYEE.getCode().equals(x.getRuleType()))
                .collect(Collectors.toList());

        Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> limitFilterDataListMap = getLimitFilterDataListMap(globalFilterList, employeeLimitRuleList, objectDataList, objectDescribe);
        if (CollectionUtils.empty(limitFilterDataListMap)) {
            if (!checkOrganizationLimit) {
                result.getSuccessIds().addAll(objectIds);
                return result;
            }
        }

        List<String> failureIds = getFailureDataIds(user, outTenantId, outOwner, globalFilterList, limitFilterDataListMap, objectDescribe, false);
        List<String> successIds = Lists.newArrayList(objectIds);
        Set<String> allFailureIds = Sets.newHashSet(failureIds);
        if (CollectionUtils.notEmpty(failureIds)) {
            successIds.removeIf(failureIds::contains);
        }
        if (CollectionUtils.empty(successIds)) {
            result.getFailureIds().addAll(Lists.newArrayList(allFailureIds));
            return result;
        }

        List<ObjectLimitRuleModel.ObjectLimitRule> organizationLimitRuleList = allEmployeeLimitRuleList.stream()
                .filter(x -> ObjectLimitRuleModel.RuleTypeEnum.ORGANIZATION.getCode().equals(x.getRuleType()))
                .collect(Collectors.toList());

        if (CollectionUtils.empty(organizationLimitRuleList)) {
            result.getFailureIds().addAll(Lists.newArrayList(allFailureIds));
            result.getSuccessIds().addAll(successIds);
            return result;
        }

        Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> organizationLimitFilterDataListMap = getLimitFilterDataListMap(globalFilterList, organizationLimitRuleList, objectDataList, objectDescribe);
        if (CollectionUtils.empty(organizationLimitFilterDataListMap)) {
            result.getFailureIds().addAll(Lists.newArrayList(allFailureIds));
            result.getSuccessIds().addAll(successIds);
            return result;
        }

        List<String> orgFailureIds = getFailureDataIds(user, outTenantId, outOwner, globalFilterList, organizationLimitFilterDataListMap, objectDescribe, true);
        allFailureIds.addAll(orgFailureIds);
        successIds = Lists.newArrayList(objectIds);
        if (CollectionUtils.notEmpty(allFailureIds)) {
            successIds.removeIf(allFailureIds::contains);
        }

        result.getSuccessIds().addAll(successIds);
        result.getFailureIds().addAll(Lists.newArrayList(allFailureIds));
        return result;
    }

    public CheckLimitResult checkObjectLimitForEdit(User user, String objectApiName, String owner, List<IObjectData> oldObjectDataList, List<IObjectData> objectDataList, IObjectDescribe objectDescribe) {
        CheckLimitResult result = CheckLimitResult.builder().successIds(Lists.newArrayList())
                .failureIds(Lists.newArrayList()).build();

        List<String> objectIds = objectDataList.stream().map(DBRecord::getId).collect(Collectors.toList());
        List<ObjectLimitRuleModel.ObjectLimitRule> employeeLimitRuleList = objectLimitRuleService.getObjectLimitRuleByEmployeeId(user, objectApiName, owner);
        if (CollectionUtils.empty(employeeLimitRuleList)) {
            result.getSuccessIds().addAll(objectIds);
            return result;
        }
        //分离【为员工设置保有量】，【为部门、用户组设置保有量】
        List<ObjectLimitRuleModel.ObjectLimitRule> employeeRuleTypeLimitList = employeeLimitRuleList.stream().filter(x -> ObjectLimitRuleModel.RuleTypeEnum.EMPLOYEE.getCode().equals(x.getRuleType())).collect(Collectors.toList());
        List<ObjectLimitRuleModel.ObjectLimitRule> organizationRuleTypeLimitList = employeeLimitRuleList.stream().filter(x -> ObjectLimitRuleModel.RuleTypeEnum.ORGANIZATION.getCode().equals(x.getRuleType())).collect(Collectors.toList());
        List<ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter> globalFilterList = objectLimitRuleService.getObjectLimitRuleGlobalFilter(user.getTenantId(), objectApiName);
        boolean checkLimit = false;
        Map<String, List<String>> ruleIdObjectDataIdMap = new HashMap<>();
        if (CollectionUtils.notEmpty(employeeRuleTypeLimitList)) {
            Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> limitFilterOldDataListMap = getLimitFilterDataListMap(globalFilterList, employeeRuleTypeLimitList, oldObjectDataList, objectDescribe);
            Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> limitFilterDataListMap = getLimitFilterDataListMap(globalFilterList, employeeRuleTypeLimitList, objectDataList, objectDescribe);
            if (CollectionUtils.notEmpty(limitFilterDataListMap)) {
                checkLimit = true;
                Map<String, List<String>> ruleIdDataIdMap = getFailureDataIds(user, owner, globalFilterList, limitFilterOldDataListMap, limitFilterDataListMap, objectDescribe);
                if (CollectionUtils.notEmpty(ruleIdDataIdMap)) {
                    ruleIdObjectDataIdMap.putAll(ruleIdDataIdMap);
                }
            }
        }
        if (CollectionUtils.notEmpty(organizationRuleTypeLimitList)) {
            Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> limitFilterOldDataListMap = getLimitFilterDataListMap(globalFilterList, organizationRuleTypeLimitList, oldObjectDataList, objectDescribe);
            Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> limitFilterDataListMap = getLimitFilterDataListMap(globalFilterList, organizationRuleTypeLimitList, objectDataList, objectDescribe);
            if (CollectionUtils.notEmpty(limitFilterDataListMap)) {
                checkLimit = true;
                Map<String, List<String>> ruleIdDataIdMap = getLimitFailureDataIdsByOrganization(user, owner, globalFilterList, limitFilterOldDataListMap, limitFilterDataListMap, oldObjectDataList, organizationRuleTypeLimitList, objectDescribe);
                if (CollectionUtils.notEmpty(ruleIdDataIdMap)) {
                    ruleIdObjectDataIdMap.putAll(ruleIdDataIdMap);
                }
            }
        }
        //未触发保有量，返回
        if (!checkLimit) {
            result.getSuccessIds().addAll(objectIds);
            return result;
        }

        if (CollectionUtils.empty(ruleIdObjectDataIdMap)) {
            result.getSuccessIds().addAll(objectIds);
            return result;
        }
        Set<String> failureIds = new HashSet<>();
        Map objectIdRuleIdMap = Maps.newHashMap();
        for (Map.Entry<String, List<String>> entry : ruleIdObjectDataIdMap.entrySet()) {
            Optional<ObjectLimitRuleModel.ObjectLimitRule> first = employeeLimitRuleList.stream().filter(x -> x.getGroupId().equals(entry.getKey())).findFirst();
            if (first.isPresent()) {
                String name = first.get().getName();
                for (String objectId : entry.getValue()) {
                    objectIdRuleIdMap.put(objectId, name);
                    failureIds.add(objectId);
                }
            }
        }
        List<String> successIds = Lists.newArrayList(objectIds);
        successIds.removeIf(failureIds::contains);
        result.getSuccessIds().addAll(successIds);
        result.getFailureIds().addAll(failureIds);
        result.setFailureIdAndRuleName(objectIdRuleIdMap);
        return result;
    }

    public CheckLimitResult checkOutUserObjectLimitForEdit(User user, String objectApiName, String outTenantId, String outOwner, List<IObjectData> oldObjectDataList, List<IObjectData> objectDataList, IObjectDescribe objectDescribe, boolean checkOrganizationLimit) {
        CheckLimitResult result = CheckLimitResult.builder().successIds(Lists.newArrayList())
                .failureIds(Lists.newArrayList()).build();

        List<String> objectIds = objectDataList.stream().map(DBRecord::getId).collect(Collectors.toList());
        List<ObjectLimitRuleModel.ObjectLimitRule> allEmployeeLimitRuleList = objectLimitRuleService.getObjectLimitRuleByEmployeeId(user, objectApiName, outOwner);
        if (CollectionUtils.empty(allEmployeeLimitRuleList)) {
            result.getSuccessIds().addAll(objectIds);
            return result;
        }

        List<ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter> globalFilterList = objectLimitRuleService.getObjectLimitRuleGlobalFilter(user.getTenantId(), objectApiName);

        List<ObjectLimitRuleModel.ObjectLimitRule> employeeLimitRuleList = allEmployeeLimitRuleList.stream()
                .filter(x -> ObjectLimitRuleModel.RuleTypeEnum.EMPLOYEE.getCode().equals(x.getRuleType()))
                .collect(Collectors.toList());

        Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> limitFilterOldDataListMap = getLimitFilterDataListMap(globalFilterList, employeeLimitRuleList, oldObjectDataList, objectDescribe);
        Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> limitFilterDataListMap = getLimitFilterDataListMap(globalFilterList, employeeLimitRuleList, objectDataList, objectDescribe);
        if (CollectionUtils.empty(limitFilterDataListMap)) {
            if (!checkOrganizationLimit) {
                result.getSuccessIds().addAll(objectIds);
                return result;
            }
        }

        List<String> failureIds = getFailureDataIds(user, outTenantId, outOwner, globalFilterList, limitFilterOldDataListMap, limitFilterDataListMap, objectDescribe, false);
        List<String> successIds = Lists.newArrayList(objectIds);
        Set<String> allFailureIds = Sets.newHashSet(failureIds);
        if (CollectionUtils.notEmpty(failureIds)) {
            successIds.removeIf(failureIds::contains);
        }
        if (CollectionUtils.empty(successIds)) {
            result.getFailureIds().addAll(Lists.newArrayList(allFailureIds));
            return result;
        }

        List<ObjectLimitRuleModel.ObjectLimitRule> organizationLimitRuleList = allEmployeeLimitRuleList.stream()
                .filter(x -> ObjectLimitRuleModel.RuleTypeEnum.ORGANIZATION.getCode().equals(x.getRuleType()))
                .collect(Collectors.toList());

        if (CollectionUtils.empty(organizationLimitRuleList)) {
            result.getFailureIds().addAll(Lists.newArrayList(allFailureIds));
            result.getSuccessIds().addAll(successIds);
            return result;
        }

        Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> organizationLimitFilterOldDataListMap = getLimitFilterDataListMap(globalFilterList, organizationLimitRuleList, oldObjectDataList, objectDescribe);
        Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> organizationLimitFilterDataListMap = getLimitFilterDataListMap(globalFilterList, organizationLimitRuleList, objectDataList, objectDescribe);
        if (CollectionUtils.empty(organizationLimitFilterDataListMap)) {
            result.getFailureIds().addAll(Lists.newArrayList(allFailureIds));
            result.getSuccessIds().addAll(successIds);
            return result;
        }

        List<String> orgFailureIds = getFailureDataIds(user, outTenantId, outOwner, globalFilterList, organizationLimitFilterOldDataListMap, organizationLimitFilterDataListMap, objectDescribe, true);

        allFailureIds.addAll(orgFailureIds);
        successIds = Lists.newArrayList(objectIds);
        if (CollectionUtils.notEmpty(allFailureIds)) {
            successIds.removeIf(allFailureIds::contains);
        }

        result.getSuccessIds().addAll(successIds);
        result.getFailureIds().addAll(Lists.newArrayList(allFailureIds));
        return result;
    }

    private Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> getLimitFilterDataListMap(List<ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter> globalFilterList, List<ObjectLimitRuleModel.ObjectLimitRule> employeeLimitRuleList, List<IObjectData> objectDataList, IObjectDescribe objectDescribe) {
        Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> result = Maps.newHashMap();
        if (CollectionUtils.empty(employeeLimitRuleList)) {
            return result;
        }
        for(ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter filter : globalFilterList) {
            Map<String, Boolean> evaluateMap = sfaExpressionService.evaluate(filter.getWheres(), objectDataList, objectDescribe);
            objectDataList.removeIf(x -> !Boolean.TRUE.equals(evaluateMap.get(x.getId())));
        }
        if(CollectionUtils.empty(objectDataList)) {
            return result;
        }

        List<ObjectLimitRuleModel.ObjectLimitFilter> employeeLimitRuleFilterList = Lists.newArrayList();
        Optional<ObjectLimitRuleModel.ObjectLimitRule> defaultLimitRule = employeeLimitRuleList.stream()
                .filter(ObjectLimitRuleModel.ObjectLimitRule::isDefaultRule).findFirst();
        if (defaultLimitRule.isPresent()) {
            employeeLimitRuleFilterList.addAll(defaultLimitRule.get().getObjectLimitFilterList());
        } else {
            for (ObjectLimitRuleModel.ObjectLimitRule objectLimitRule : employeeLimitRuleList) {
                employeeLimitRuleFilterList.addAll(objectLimitRule.getObjectLimitFilterList());
            }
        }

        employeeLimitRuleFilterList = employeeLimitRuleFilterList.stream()
                .sorted(Comparator.comparing(ObjectLimitRuleModel.ObjectLimitFilter::getLimitNumber)
                        .thenComparing(ObjectLimitRuleModel.ObjectLimitFilter::getCreateTime)
                        .thenComparing(ObjectLimitRuleModel.ObjectLimitFilter::getId))
                .collect(Collectors.toList());

        for (ObjectLimitRuleModel.ObjectLimitFilter limitFilter : employeeLimitRuleFilterList) {
            List<String> limitObjectIds = Lists.newArrayList();
            Map<String, Boolean> evaluateMap = sfaExpressionService.evaluate(limitFilter.getWheres(), objectDataList, objectDescribe);
            for(Map.Entry<String, Boolean> entry : evaluateMap.entrySet()) {
                if(Boolean.TRUE.equals(entry.getValue())) {
                    limitObjectIds.add(entry.getKey());
                }
            }
            if (CollectionUtils.notEmpty(limitObjectIds)) {
                result.put(limitFilter, limitObjectIds);
            }
        }

        return result;
    }

    /**
     * 根据组织，部门来计算保有量
     *
     * @param user
     * @param owner
     * @param globalFilterList
     * @param limitFilterDataListMap
     * @param employeeLimitRuleList
     * @param objectDescribe
     * @return
     */
    private Map<String, List<String>> getLimitFailureDataIdsByOrganization(User user
            , String owner
            , List<ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter> globalFilterList
            , Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> limitFilterDataListMap
            , List<ObjectLimitRuleModel.ObjectLimitRule> employeeLimitRuleList
            , List<IObjectData> objectDataList
            , IObjectDescribe objectDescribe) {
        Map<String, List<String>> ruleIdObjectDataIdMap = new HashMap<>();
        String tenantId = objectDescribe.getTenantId();
        List<String> deptIds = Lists.newArrayList();
        List<String> userGroupIds = Lists.newArrayList();
        Optional<ObjectLimitRuleModel.ObjectLimitRule> departmentExist = employeeLimitRuleList.stream().filter(x -> ObjectLimitRuleModel.DataTypeEnum.DEPARTMENT.getCode().equals(x.getDataType())).findAny();
        Optional<ObjectLimitRuleModel.ObjectLimitRule> userGroupExist = employeeLimitRuleList.stream().filter(x -> ObjectLimitRuleModel.DataTypeEnum.USER_GROUP.getCode().equals(x.getDataType())).findAny();
        if (departmentExist.isPresent()) {
            deptIds = ltoOrgCommonService.getNDeptPathByUserId(tenantId, owner);
            deptIds.add(LtoFieldApiConstants.ALL_COMPANY_ID);
        }
        if (userGroupExist.isPresent()) {
            userGroupIds = ltoOrgCommonService.getUserGroupIdsByMemberId(tenantId, owner);
        }

        // 已经达到保有量的数据id，下次就跳过
        List<String> allFailureDataIds = Lists.newArrayList();
        for (Map.Entry<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> entry : limitFilterDataListMap.entrySet()) {
            String objectApiName = entry.getKey().getObjectApiName();
            String groupId = entry.getKey().getGroupId();
            log.info("getLimitFailureDataIdsByOrganization:{},groupId:{}", entry.getValue(), groupId);

            List<ObjectLimitRuleModel.ObjectLimitRule> limitRules = employeeLimitRuleList.stream().filter(x -> x.getGroupId().equals(groupId)).collect(Collectors.toList());
            if (CollectionUtils.empty(limitRules)) {
                log.info("ObjectLimitRule is not exist:{}", groupId);
                continue;
            }
            // 如果所有的数据都已经达到保有量则阻断。
            if (allFailureDataIds.containsAll(entry.getValue())) {
                //该规则已经阻断了,
                continue;
            }
            List<String> owners = Lists.newArrayList();
            boolean isSameGroup = false;
            Set<String> oldOwners = new HashSet<>();
            if (CollectionUtils.notEmpty(objectDataList)) {
                oldOwners = objectDataList.stream().filter(x -> entry.getValue().contains(x.getId()) && CollectionUtils.notEmpty(x.getOwner())).map(IObjectData::getOwner)
                        .flatMap(Collection::stream)
                        .collect(Collectors.toSet());
            }
            for (ObjectLimitRuleModel.ObjectLimitRule limitRule : limitRules) {
                if (ObjectLimitRuleModel.DataTypeEnum.DEPARTMENT.getCode().equals(limitRule.getDataType())) {
                    if (CollectionUtils.notEmpty(deptIds) && deptIds.contains(limitRule.getDataId())) {
                        List<String> membersByDeptIds = ltoOrgCommonService.getMembersByDeptIds(tenantId, Lists.newArrayList(limitRule.getDataId()), false);
                        if (CollectionUtils.notEmpty(oldOwners) && membersByDeptIds.containsAll(oldOwners)) {
                            isSameGroup = true;
                        }
                        log.info("membersByDeptIds:{},deptId:{},objectId:{}", membersByDeptIds, limitRule.getDataId(), entry.getValue());
                        owners.addAll(membersByDeptIds);
                    } else {//改用户的部门 不在此规则跳过
                        continue;
                    }
                } else if (ObjectLimitRuleModel.DataTypeEnum.USER_GROUP.getCode().equals(limitRule.getDataType())) {
                    if (CollectionUtils.notEmpty(userGroupIds) && userGroupIds.contains(limitRule.getDataId())) {
                        List<String> membersByUserGroupIds = ltoOrgCommonService.batchGetMembersByUserGroupIds(tenantId, Lists.newArrayList(limitRule.getDataId()), false);
                        log.info("membersByDeptIds:{},userGroupId:{},objectId:{}", membersByUserGroupIds, limitRule.getDataId(), entry.getValue());
                        if (CollectionUtils.notEmpty(oldOwners) && membersByUserGroupIds.containsAll(oldOwners)) {
                            isSameGroup = true;
                        }
                        owners.addAll(membersByUserGroupIds);
                    } else {//此用户的用户组 不在此规则跳过
                        continue;
                    }
                }
                List<IFilter> filters = Lists.newArrayList();
                if (CollectionUtils.empty(owners)) {
                    owners.add(owner);
                }
                SearchUtil.fillFilterIn(filters, LtoFieldApiConstants.OWNER, owners);
                SearchTemplateQuery searchTemplateQuery = getSearchTemplateQuery(filters, globalFilterList, entry.getKey(), objectDescribe);
                QueryResult<IObjectData> queryResult = getDataByQuery(user, objectApiName, searchTemplateQuery);
                log.info("checkObjectLimit getLimitFailureDataIdsByOrganization objectId:{},groupId:{},queryResult: {},limit:{} ", entry.getValue(), groupId, queryResult, entry.getKey().getLimitNumber());
                // groupId重复，获取上次的累计
                List<String> lastTimeFailureIds = ruleIdObjectDataIdMap.getOrDefault(groupId, Lists.newArrayList());
                if (entry.getKey().getLimitNumber() > queryResult.getTotalNumber()) {
                    int ownNumber = entry.getKey().getLimitNumber() - queryResult.getTotalNumber();
                    if (ownNumber < entry.getValue().size()) {
                        lastTimeFailureIds.addAll(entry.getValue().subList(ownNumber, entry.getValue().size()));
                    } else {
                        //没有达到保有量
                        log.info("getLimitFailureDataIdsByOrganization skip:{},groupId:{}", entry.getValue(), groupId);
                        continue;
                    }
                } else if (entry.getKey().getLimitNumber() == queryResult.getTotalNumber() && isSameGroup) {
                    //没有达到保有量
                    log.info("getLimitFailureDataIdsByOrganization skip:{},groupId:{}, oldOwners:{}", entry.getValue(), groupId, oldOwners);
                    continue;
                } else {
                    lastTimeFailureIds.addAll(entry.getValue());
                }
                Set<String> lastTimeFailureIdsSet = Sets.newHashSet(lastTimeFailureIds);
                ruleIdObjectDataIdMap.put(groupId, Lists.newArrayList(lastTimeFailureIdsSet));
                allFailureDataIds.addAll(lastTimeFailureIds);
                log.info("getLimited break :{},groupId:{},limitRule:{}", entry.getValue(), groupId, limitRule);
                break;
            }
        }
        return ruleIdObjectDataIdMap;
    }

    /**
     * 根据组织，部门来计算保有量，过滤对比数据变化后，匹配的规则没有变化的数据
     *
     * @param user
     * @param owner
     * @param globalFilterList
     * @param limitFilterDataListMap
     * @param employeeLimitRuleList
     * @param objectDescribe
     * @return
     */
    private Map<String, List<String>> getLimitFailureDataIdsByOrganization(User user
            , String owner
            , List<ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter> globalFilterList
            , Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> limitFilterOldDataListMap
            , Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> limitFilterDataListMap
            , List<IObjectData> objectDataList
            , List<ObjectLimitRuleModel.ObjectLimitRule> employeeLimitRuleList
            , IObjectDescribe objectDescribe) {
        Map<String, List<String>> ruleIdObjectDataIdMap = new HashMap<>();
        String tenantId = objectDescribe.getTenantId();

        List<String> deptIds = Lists.newArrayList();
        List<String> userGroupIds = Lists.newArrayList();
        Optional<ObjectLimitRuleModel.ObjectLimitRule> departmentExist = employeeLimitRuleList.stream().filter(x -> ObjectLimitRuleModel.DataTypeEnum.DEPARTMENT.getCode().equals(x.getDataType())).findAny();
        Optional<ObjectLimitRuleModel.ObjectLimitRule> userGroupExist = employeeLimitRuleList.stream().filter(x -> ObjectLimitRuleModel.DataTypeEnum.USER_GROUP.getCode().equals(x.getDataType())).findAny();
        if (departmentExist.isPresent()) {
            deptIds = ltoOrgCommonService.getNDeptPathByUserId(tenantId, owner);
            deptIds.add(LtoFieldApiConstants.ALL_COMPANY_ID);
        }
        if (userGroupExist.isPresent()) {
            userGroupIds = ltoOrgCommonService.getUserGroupIdsByMemberId(tenantId, owner);
        }

        // 已经达到保有量的数据id，下次就跳过
        List<String> allFailureDataIds = Lists.newArrayList();
        for (Map.Entry<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> entry : limitFilterDataListMap.entrySet()) {
            String objectApiName = entry.getKey().getObjectApiName();
            String groupId = entry.getKey().getGroupId();

            List<String> exclusiveIds = Lists.newArrayList();
            Optional<Map.Entry<ObjectLimitRuleModel.ObjectLimitFilter, List<String>>> optionalObjectLimitFilterListEntry = limitFilterOldDataListMap.entrySet().stream()
                    .filter(x -> x.getKey().getId().equals(entry.getKey().getId())).findFirst();
            if (optionalObjectLimitFilterListEntry.isPresent()) {
                exclusiveIds.addAll(optionalObjectLimitFilterListEntry.get().getValue());
                exclusiveIds.removeIf(x -> !entry.getValue().contains(x));
            }

            List<ObjectLimitRuleModel.ObjectLimitRule> limitRules = employeeLimitRuleList.stream().filter(x -> x.getGroupId().equals(groupId)).collect(Collectors.toList());
            if (CollectionUtils.empty(limitRules)) {
                log.info("ObjectLimitRule is not exist:{}", groupId);
                continue;
            }
            // 如果所有的数据都已经达到保有量则阻断。
            if (allFailureDataIds.containsAll(entry.getValue())) {
                //该规则已经阻断了,
                continue;
            }
            List<String> owners = Lists.newArrayList();
            for (ObjectLimitRuleModel.ObjectLimitRule limitRule : limitRules) {
                if (ObjectLimitRuleModel.DataTypeEnum.DEPARTMENT.getCode().equals(limitRule.getDataType())) {
                    if (CollectionUtils.notEmpty(deptIds) && deptIds.contains(limitRule.getDataId())) {
                        List<String> membersByDeptIds = ltoOrgCommonService.getMembersByDeptIds(tenantId, Lists.newArrayList(limitRule.getDataId()), false);
                        log.info("membersByDeptIds:{},deptId:{},objectId:{}", membersByDeptIds, limitRule.getDataId(), entry.getValue());
                        owners.addAll(membersByDeptIds);
                    } else {//改用户的部门 不在此规则跳过
                        continue;
                    }
                } else if (ObjectLimitRuleModel.DataTypeEnum.USER_GROUP.getCode().equals(limitRule.getDataType())) {
                    if (CollectionUtils.notEmpty(userGroupIds) && userGroupIds.contains(limitRule.getDataId())) {
                        List<String> membersByUserGroupIds = ltoOrgCommonService.batchGetMembersByUserGroupIds(tenantId, Lists.newArrayList(limitRule.getDataId()), false);
                        log.info("membersByDeptIds:{},userGroupId:{},objectId:{}", membersByUserGroupIds, limitRule.getDataId(), entry.getValue());

                        owners.addAll(membersByUserGroupIds);
                    } else {//此用户的用户组 不在此规则跳过
                        continue;
                    }
                }
                List<IFilter> filters = Lists.newArrayList();
                if (CollectionUtils.empty(owners)) {
                    owners.add(owner);
                }
                SearchUtil.fillFilterIn(filters, LtoFieldApiConstants.OWNER, owners);
                SearchTemplateQuery searchTemplateQuery = getSearchTemplateQuery(filters, globalFilterList, entry.getKey(), objectDescribe);
                QueryResult<IObjectData> queryResult = getDataByQuery(user, objectApiName, searchTemplateQuery);
                log.info("checkObjectLimit getLimitFailureDataIdsByOrganization objectId:{},groupId:{},queryResult: {},limit:{} ", entry.getValue(), groupId, queryResult, entry.getKey().getLimitNumber());
                int totalNumber = queryResult.getTotalNumber() - exclusiveIds.size();
                // groupId重复，获取上次的累计
                List<String> lastTimeFailureIds = ruleIdObjectDataIdMap.getOrDefault(groupId, Lists.newArrayList());
                if (entry.getKey().getLimitNumber() > totalNumber) {
                    int ownNumber = entry.getKey().getLimitNumber() - totalNumber;
                    if (ownNumber < entry.getValue().size()) {
                        lastTimeFailureIds.addAll(entry.getValue().subList(ownNumber, entry.getValue().size()));
                    } else {
                        //没有达到保有量
                        log.info("getLimitFailureDataIdsByOrganization skip:{},groupId:{}", entry.getValue(), groupId);
                        continue;
                    }
                } else {
                    lastTimeFailureIds.addAll(entry.getValue());
                }
                Set<String> lastTimeFailureIdsSet = Sets.newHashSet(lastTimeFailureIds);
                ruleIdObjectDataIdMap.put(groupId, Lists.newArrayList(lastTimeFailureIdsSet));
                allFailureDataIds.addAll(lastTimeFailureIds);
                log.info("getLimited break :{},groupId:{},limitRule:{}", entry.getValue(), groupId, limitRule);
                break;
            }

        }
        return ruleIdObjectDataIdMap;
    }

    /**
     * 按照ruleType = employee 来 查询达到保有量的员工
     *
     * @param user
     * @param owner
     * @param globalFilterList
     * @param limitFilterDataListMap
     * @param employeeLimitRuleList
     * @param objectDescribe
     * @return
     */
    private Map<String, List<String>> getLimitFailureDataIds(User user, String owner
            , List<ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter> globalFilterList
            , Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> limitFilterDataListMap
            , List<ObjectLimitRuleModel.ObjectLimitRule> employeeLimitRuleList
            , IObjectDescribe objectDescribe) {
        Map<String, List<String>> ruleIdObjectDataIdMap = new HashMap<>();
        List<String> allFailureDataIds = Lists.newArrayList();
        for (Map.Entry<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> entry : limitFilterDataListMap.entrySet()) {
            String objectApiName = entry.getKey().getObjectApiName();
            String groupId = entry.getKey().getGroupId();

            Optional<ObjectLimitRuleModel.ObjectLimitRule> any = employeeLimitRuleList.stream().filter(x -> x.getGroupId().equals(groupId)).findAny();
            //跳过 ruleType 不是 EMPLOYEE 的规则
            if (!any.isPresent()) {
                continue;
            }

            Map<String, String> filterValues = Maps.newHashMap();
            filterValues.put(LtoFieldApiConstants.OWNER, owner);
            SearchTemplateQuery searchTemplateQuery = getSearchTemplateQuery(filterValues, globalFilterList, entry.getKey(), objectDescribe);
            log.info("checkObjectLimit searchTemplateQuery: {} ", searchTemplateQuery.getFilters());
            QueryResult<IObjectData> queryResult = getDataByQuery(user, objectApiName, searchTemplateQuery);

            log.info("checkObjectLimit queryResult: {} ", queryResult);
            log.info("checkObjectLimit objectLimitFilter: {} ", entry.getKey());

            // groupId重复，获取上次的累计
            List<String> lastTimeFailureIds = ruleIdObjectDataIdMap.getOrDefault(groupId, Lists.newArrayList());
            if (entry.getKey().getLimitNumber() > queryResult.getTotalNumber()) {
                int ownNumber = entry.getKey().getLimitNumber() - queryResult.getTotalNumber();
                if (ownNumber < entry.getValue().size()) {
                    lastTimeFailureIds.addAll(entry.getValue().subList(ownNumber, entry.getValue().size()));
                }
            } else {
                lastTimeFailureIds.addAll(entry.getValue());
            }
            Set<String> lastTimeFailureIdsSet = Sets.newHashSet(lastTimeFailureIds);
            ruleIdObjectDataIdMap.put(groupId, Lists.newArrayList(lastTimeFailureIdsSet));
            allFailureDataIds.addAll(lastTimeFailureIds);
        }
        return ruleIdObjectDataIdMap;
    }

    private List<String> getFailureDataIds(User user, String outTenantId, String outOwner
            , List<ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter> globalFilterList
            , Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> limitFilterDataListMap
            , IObjectDescribe objectDescribe, boolean isOrgCheck) {
        Set<String> failureIds = Sets.newHashSet();
        for (Map.Entry<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> entry : limitFilterDataListMap.entrySet()) {
            String objectApiName = entry.getKey().getObjectApiName();
            Map<String, String> filterValues = Maps.newHashMap();
            filterValues.put(LtoFieldApiConstants.OUT_TENANT_ID, outTenantId);
            if (!isOrgCheck) {
                filterValues.put(LtoFieldApiConstants.OUT_OWNER, outOwner);
            }

            SearchTemplateQuery searchTemplateQuery = getSearchTemplateQuery(filterValues, globalFilterList, entry.getKey(), objectDescribe);
            log.info("checkObjectLimit searchTemplateQuery: {} ", searchTemplateQuery);

            QueryResult<IObjectData> queryResult = getDataByQuery(user, objectApiName, searchTemplateQuery);

            log.info("checkObjectLimit queryResult: {} ", queryResult);
            log.info("checkObjectLimit objectLimitFilter: {} ", entry.getKey());

            if (entry.getKey().getLimitNumber() > queryResult.getTotalNumber()) {
                int ownNumber = entry.getKey().getLimitNumber() - queryResult.getTotalNumber();
                if (ownNumber < entry.getValue().size()) {
                    failureIds.addAll(entry.getValue().subList(ownNumber, entry.getValue().size()));
                }
            } else {
                failureIds.addAll(entry.getValue());
            }
        }
        if (CollectionUtils.empty(failureIds)) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(failureIds);
    }

    private Map<String, List<String>> getFailureDataIds(User user, String owner
            , List<ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter> globalFilterList
            , Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> limitFilterOldDataListMap
            , Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> limitFilterDataListMap
            , IObjectDescribe objectDescribe) {
        Map<String, List<String>> ruleIdObjectDataIdMap = new HashMap<>();
        List<String> allFailureDataIds = Lists.newArrayList();
        for (Map.Entry<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> entry : limitFilterDataListMap.entrySet()) {
            String objectApiName = entry.getKey().getObjectApiName();
            String groupId = entry.getKey().getGroupId();
            List<String> exclusiveIds = Lists.newArrayList();
            Optional<Map.Entry<ObjectLimitRuleModel.ObjectLimitFilter, List<String>>> optionalObjectLimitFilterListEntry = limitFilterOldDataListMap.entrySet().stream()
                    .filter(x -> x.getKey().getId().equals(entry.getKey().getId())).findFirst();
            if (optionalObjectLimitFilterListEntry.isPresent()) {
                exclusiveIds.addAll(optionalObjectLimitFilterListEntry.get().getValue());
                exclusiveIds.removeIf(x -> !entry.getValue().contains(x));
            }
            Map<String, String> filterValues = Maps.newHashMap();
            filterValues.put(LtoFieldApiConstants.OWNER, owner);
            SearchTemplateQuery searchTemplateQuery = getSearchTemplateQuery(filterValues, globalFilterList, entry.getKey(), objectDescribe);
            log.info("checkObjectLimit searchTemplateQuery: {} ", searchTemplateQuery);

            QueryResult<IObjectData> queryResult = getDataByQuery(user, objectApiName, searchTemplateQuery);

            log.info("checkObjectLimit queryResult: {} ", queryResult);
            log.info("checkObjectLimit objectLimitFilter: {} ", entry.getKey());

            int totalNumber = queryResult.getTotalNumber() - exclusiveIds.size();

            // groupId重复，获取上次的累计
            List<String> lastTimeFailureIds = ruleIdObjectDataIdMap.getOrDefault(groupId, Lists.newArrayList());
            if (entry.getKey().getLimitNumber() > totalNumber) {
                int ownNumber = entry.getKey().getLimitNumber() - totalNumber;
                if (ownNumber < entry.getValue().size()) {
                    lastTimeFailureIds.addAll(entry.getValue().subList(ownNumber, entry.getValue().size()));
                }
            } else {
                lastTimeFailureIds.addAll(entry.getValue());
            }
            Set<String> lastTimeFailureIdsSet = Sets.newHashSet(lastTimeFailureIds);
            ruleIdObjectDataIdMap.put(groupId, Lists.newArrayList(lastTimeFailureIdsSet));
            allFailureDataIds.addAll(lastTimeFailureIds);
        }
        return ruleIdObjectDataIdMap;
    }

    private List<String> getFailureDataIds(User user, String outTenantId, String outOwner
            , List<ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter> globalFilterList
            , Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> limitFilterOldDataListMap
            , Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> limitFilterDataListMap
            , IObjectDescribe objectDescribe, boolean isOrgCheck) {
        Set<String> failureIds = Sets.newHashSet();
        for (Map.Entry<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> entry : limitFilterDataListMap.entrySet()) {
            List<String> exclusiveIds = Lists.newArrayList();
            Optional<Map.Entry<ObjectLimitRuleModel.ObjectLimitFilter, List<String>>> optionalObjectLimitFilterListEntry = limitFilterOldDataListMap.entrySet().stream()
                    .filter(x -> x.getKey().getId().equals(entry.getKey().getId())).findFirst();
            if (optionalObjectLimitFilterListEntry.isPresent()) {
                exclusiveIds.addAll(optionalObjectLimitFilterListEntry.get().getValue());
                exclusiveIds.removeIf(x -> !entry.getValue().contains(x));
            }

            String objectApiName = entry.getKey().getObjectApiName();
            Map<String, String> filterValues = Maps.newHashMap();
            filterValues.put(LtoFieldApiConstants.OUT_TENANT_ID, outTenantId);
            if (!isOrgCheck) {
                filterValues.put(LtoFieldApiConstants.OUT_OWNER, outOwner);
            }

            SearchTemplateQuery searchTemplateQuery = getSearchTemplateQuery(filterValues, globalFilterList, entry.getKey(), objectDescribe);
            log.info("checkObjectLimit searchTemplateQuery: {} ", searchTemplateQuery);

            QueryResult<IObjectData> queryResult = getDataByQuery(user, objectApiName, searchTemplateQuery);

            log.info("checkObjectLimit queryResult: {} ", queryResult);
            log.info("checkObjectLimit objectLimitFilter: {} ", entry.getKey());

            int totalNumber = queryResult.getTotalNumber() - exclusiveIds.size();

            if (entry.getKey().getLimitNumber() > totalNumber) {
                int ownNumber = entry.getKey().getLimitNumber() - totalNumber;
                if (ownNumber < entry.getValue().size()) {
                    failureIds.addAll(entry.getValue().subList(ownNumber, entry.getValue().size()));
                }
            } else {
                failureIds.addAll(entry.getValue());
            }
        }
        if (CollectionUtils.empty(failureIds)) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(failureIds);
    }

    private SearchTemplateQuery getSearchTemplateQuery(Map<String, String> filterValues,
                                                              List<ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter> globalFilterList
            , ObjectLimitRuleModel.ObjectLimitFilter objectLimitFilter, IObjectDescribe objectDescribe) {
        List<Wheres> globalWheresList = Lists.newArrayList();
        for (ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter globalFilter : globalFilterList) {
            convert2WheresList(globalFilter.getWheres(), globalWheresList);
        }
        convertFilter(globalWheresList, objectDescribe);

        List<IFilter> filters = Lists.newArrayList();
        List<Wheres> wheresList = Lists.newArrayList();
        convert2WheresList(objectLimitFilter.getWheres(), wheresList);
        convertFilter(wheresList, objectDescribe);

        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(100);
        searchTemplateQuery.setFindExplicitTotalNum(true);
        searchTemplateQuery.setNeedReturnCountNum(true);
        searchTemplateQuery.setPermissionType(0);
        for (Map.Entry<String, String> entry : filterValues.entrySet()) {
            SearchUtil.fillFilterEq(filters, entry.getKey(), entry.getValue());
        }
        if (CollectionUtils.notEmpty(wheresList)) {
            String pattern = "";
            int index = 1;
            for (IFilter item : filters) {
                pattern = pattern + index + " AND ";
                index = index + 1;
            }
            pattern = pattern.substring(0, pattern.length() - 4);
            pattern = String.format(" (%s) AND (%s) ", pattern, getPattern(globalWheresList, filters, index));
            index = filters.size() + 1;
            pattern = String.format(" (%s) AND (%s) ", pattern, getPattern(wheresList, filters, index));

            searchTemplateQuery.setPattern(pattern);
        } else {
            searchTemplateQuery.setWheres(globalWheresList);
        }
        searchTemplateQuery.setFilters(filters);
        return searchTemplateQuery;
    }

    private SearchTemplateQuery getSearchTemplateQuery(List<IFilter> filters,
                                                       List<ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter> globalFilterList
            , ObjectLimitRuleModel.ObjectLimitFilter objectLimitFilter, IObjectDescribe objectDescribe) {
        List<Wheres> globalWheresList = Lists.newArrayList();
        for (ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter globalFilter : globalFilterList) {
            convert2WheresList(globalFilter.getWheres(), globalWheresList);
        }
        convertFilter(globalWheresList, objectDescribe);

        List<Wheres> wheresList = Lists.newArrayList();
        convert2WheresList(objectLimitFilter.getWheres(), wheresList);
        convertFilter(wheresList, objectDescribe);

        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(100);
        searchTemplateQuery.setFindExplicitTotalNum(true);
        searchTemplateQuery.setNeedReturnCountNum(true);
        searchTemplateQuery.setPermissionType(0);

        if (CollectionUtils.notEmpty(wheresList) && CollectionUtils.notEmpty(filters)) {
            String pattern = "";
            int index = 1;
            for (IFilter item : filters) {
                pattern = pattern + index + " AND ";
                index = index + 1;
            }
            pattern = pattern.substring(0, pattern.length() - 4);
            pattern = String.format(" (%s) AND (%s) ", pattern, getPattern(globalWheresList, filters, index));
            index = filters.size() + 1;
            pattern = String.format(" (%s) AND (%s) ", pattern, getPattern(wheresList, filters, index));

            searchTemplateQuery.setPattern(pattern);
        } else {
            searchTemplateQuery.setWheres(globalWheresList);
        }
        searchTemplateQuery.setFilters(filters);
        return searchTemplateQuery;
    }

    private void convert2WheresList(String wheresString, List<Wheres> wheresList) {
        if (StringUtils.isEmpty(wheresString)) {
            return;
        }
        List<JSONObject> wheresJSONObjectList = JSON.parseObject(wheresString, List.class);
        if (CollectionUtils.notEmpty(wheresJSONObjectList)) {
            for (JSONObject jsonObject : wheresJSONObjectList) {
                Wheres wheres = JSON.parseObject(jsonObject.toJSONString(), Wheres.class);
                if (CollectionUtils.notEmpty(wheres.getFilters())) {
                    for (IFilter filter : wheres.getFilters()) {
                        if (filter.getFieldValues() == null) {
                            filter.setFieldValues(Lists.newArrayList());
                        }
                    }
                }
                wheresList.add(wheres);
            }
        }
    }

    private void convertFilter(List<Wheres> wheresList, IObjectDescribe objectDescribe) {
        if (CollectionUtils.empty(wheresList) || objectDescribe == null) {
            return;
        }

        for (Wheres wheres : wheresList) {
            if (CollectionUtils.empty(wheres.getFilters())) {
                continue;
            }
            for (IFilter filter : wheres.getFilters()) {
                IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(filter.getFieldName());
                if (fieldDescribe == null) {
                    continue;
                }
                if (IFieldType.OBJECT_REFERENCE.equals(fieldDescribe.getType())) {
                    filter.setFieldName(String.format("%s.name", filter.getFieldName()));
                } else if (IFieldType.SELECT_MANY.equals(fieldDescribe.getType())) {
                    if (Operator.IN.equals(filter.getOperator())) {
                        filter.setOperator(Operator.HASANYOF);
                    } else if (Operator.NIN.equals(filter.getOperator())) {
                        filter.setOperator(Operator.NHASANYOF);
                    }
                }
            }
        }
    }

    private String getPattern(List<Wheres> wheresList, List<IFilter> filters, Integer startIndex) {
        if (CollectionUtils.empty(wheresList)) {
            return " 1=1 ";
        }
        List<String> patternList = Lists.newArrayList();
        for (Wheres wheres : wheresList) {
            if (CollectionUtils.empty(wheres.getFilters())) {
                continue;
            }
            StringBuilder pattern = new StringBuilder();
            for (IFilter filter : wheres.getFilters()) {
                pattern.append(startIndex.toString()).append(" AND ");
                startIndex = startIndex + 1;
                filters.add(filter);
            }
            pattern = new StringBuilder(pattern.substring(0, pattern.length() - 4));
            pattern = new StringBuilder(String.format(" (%s) ", pattern));
            patternList.add(pattern.toString());
        }
        String pattern = String.join(" OR ", patternList);

        if (StringUtils.isBlank(pattern)) {
            pattern = " 1=1 ";
        }
        return pattern;
    }

    private QueryResult<IObjectData> getDataByQuery(User user, String objectApiName, SearchTemplateQuery searchTemplateQuery)  {
        ActionContextUtil.SearchActionContextOp op = ActionContextUtil.SearchActionContextOp.builder()
                .skipRelevantTeam(true).calculateFormula(false).needDeepQuote(false)
                .esRedisRecentUpdateCheck(true).privilegeCheck(false).forceESSearch(false)
                .build();
        IActionContext actionContext= ActionContextUtil.createSearchActionContext(user, op);
        actionContext.setDbType("pg");
        searchTemplateQuery.setSearchSource("db");
        return serviceFacade.findBySearchQuery(actionContext, objectApiName, searchTemplateQuery);
    }

    public boolean isGrayObjectLimit(String tenantId) {
        return ltoLicenseService.checkModuleLicenseExist(tenantId, OBJECT_LIMIT_APP);
    }

    @Data
    @Builder
    public static class CheckLimitResult {
        private List<String> successIds;
        private List<String> failureIds;
        private Map<String, String> failureIdAndRuleName;
    }
}