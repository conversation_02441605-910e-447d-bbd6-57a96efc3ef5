package com.facishare.crm.sfa.lto.procurement.qianlima;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.Lang;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.github.autoconf.ConfigFactory;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;

@Slf4j
@Component
public class QlmProcurementService {

    @Resource(name = "httpClientSupport")
    private OkHttpSupport client;
    @Autowired
    private ServiceFacade serviceFacade;

    private static final String CONF_KEY = "biz_qlm_procurement_key";
    @Getter
    private static String HOST = "";
    @Getter
    private static String WEB_HOST = "";
    private static String PUBLIC_KEY = "";
    private static String SECRET_USER = "";
    @Getter
    private static String province;
    @Getter
    private static String city;
    // 中文一二级行业缓存
    @Getter
    private static List<Map<String,Object>> oneAndTwoIndustries;
    private static String oneAndTwoIndustriesJsonString;
    private static final JsonMapper jsonMapper = new JsonMapper();

    static {
        ConfigFactory.getConfig("fs-crm-sales-config", config -> {
            HOST = config.get("qlm_host", "http://open.btest.qianlima.com/third");
            WEB_HOST = config.get("qlm_web_host", "http://open.qianlima.com");
            String qlm_public_key = "qlm_public_key";
            PUBLIC_KEY = config.get(qlm_public_key, "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCO1sf1NGmj4Clq/1+KYDtNtycvnvjiGmbxwN/B85JNnNg6n2yvit8avPHr4RS2PtvernhDHWmdyyt1a/3x+o4Dg72AwP3Fpm7JOlucF2BKUmMGBVS5UtXsTMqQDsjmlAGb9K5DEN9FpxqhyE4OGlipnxNugRyb1O1GBRk6AE44zwIDAQAB");
            if (config.getBool(qlm_public_key + ".encrypt", false)) {
                PUBLIC_KEY = config.getDecrypt(qlm_public_key);
            }
            SECRET_USER = config.get("qlm_secret_user", "fenxiang");
            province = config.get("qlm_area_province", "[]");
            city = config.get("qlm_area_city", "[]");
            try {
                oneAndTwoIndustriesJsonString = config.get("qlm_industries_new","{}");
                oneAndTwoIndustries = jsonMapper.readValue(oneAndTwoIndustriesJsonString, new TypeReference<List<Map<String,Object>>>() {});
            } catch (Exception e) {
                oneAndTwoIndustries = new ArrayList<>();
                log.error("千里马一二级行业初始化错误。", e);
            }
        });
    }

    // 获取国际化的一二级行业
    public static List<Map<String, Object>> getI18nOneAndTwoIndustries(Lang lang) {
        if (Lang.zh_CN.equals(lang)) {
            return oneAndTwoIndustries;
        }
        List<Map<String, Object>> industries = new ArrayList<>();
        try {
            industries = jsonMapper.readValue(oneAndTwoIndustriesJsonString, new TypeReference<List<Map<String, Object>>>() {});
            industries.forEach(map -> {
                String oneLabel = I18N.text(String.format("sfa.bidding.qlm.industries.one.%s", map.get("value")));
                map.put("label", oneLabel);
                map.put("label__r", oneLabel);
                List<Map<String, String>> children = (List<Map<String, String>>) map.get("children");
                children.forEach(o -> {
                    String twoLabel = I18N.text(String.format("sfa.bidding.qlm.industries.two.%s", o.get("value")));
                    o.put("label", twoLabel);
                    o.put("label__r", twoLabel);
                });
            });
        } catch (JsonProcessingException e) {
            log.error("千里马一二级行业转换错误。", e);
        }
        return industries;
    }

    public String cryptStr(String str) {
        RSA rsa = new RSA(null, PUBLIC_KEY);
        String encryptStr = rsa.encryptHex(StrUtil.bytes(str, CharsetUtil.CHARSET_UTF_8), KeyType.PublicKey);
        return Base64.encode(encryptStr);
    }

    public String crypt(JSONObject json) {
        RSA rsa = new RSA(null, PUBLIC_KEY);
        //签名json 包含timeStamp，secretKey
        JSONObject encrypt = new JSONObject();
        encrypt.put("timeStamp", new Date()); //请求发起时间
        encrypt.put("requestParam", json);
        //对签名json 利用公钥加密，放入secretKey
        String encryptJson = JSONObject.toJSONString(encrypt);
        String encryptStr = rsa.encryptHex(StrUtil.bytes(encryptJson, CharsetUtil.CHARSET_UTF_8), KeyType.PublicKey);
        return Base64.encode(encryptStr);
    }

    public void placeOrder(String tenantId, int orderId, long startTime, long endTime) {
        Account account = register(tenantId);

        JSONObject json = new JSONObject();
        json.put("accountKey", account.getAccountKey());
        json.put("vipId", orderId);
        json.put("openStartTime", startTime);
        json.put("openEndTime", endTime);

        Result result = commonPost("/open/account/vip", json);
        if (result.isSuccess()) {
            log.info("千里马下单成功{}", tenantId);
        } else {
            log.error("千里马下单失败{}", JSON.toJSONString(result));
        }
    }

    public Account getAccount(String tenantId) {
        String accountStr = serviceFacade.findTenantConfig(User.systemUser(tenantId), CONF_KEY);
        if (!StringUtils.isEmpty(accountStr)) {
            return JSON.parseObject(accountStr, Account.class);
        }
        return null;
    }

    public Account register(String tenantId) {
        Account account = getAccount(tenantId);
        if (account != null) {
            return account;
        }
        JSONObject json = new JSONObject();
        json.put("customerUserId", tenantId);
        json.put("customerUserName", tenantId);
        json.put("companyName", tenantId);

        Result result = commonPost("/open/account/register", json);
        if (!result.isSuccess()) {
            log.error("千里马创建账号失败{}", JSON.toJSONString(result));
            throw new ValidateException("千里马创建账号失败！");
        }
        serviceFacade.createTenantConfig(User.systemUser(tenantId), CONF_KEY, result.getData(), ConfigValueType.JSON);
        return result.getData(Account.class);
    }

    public Result commonPost(String url, JSONObject json) {
        Request request = new Request.Builder()
                .url(HOST + url)
                .header("secretUser", SECRET_USER)
                .header("secretContent", crypt(json))
                .post(RequestBody.create("".getBytes(StandardCharsets.UTF_8)))
                .build();
        return (Result) client.syncExecute(request, new SyncCallback() {
            @Override
            public Object response(Response response) throws IOException {
                String bodyStr = null;
                if (response.body() != null) {
                    bodyStr = response.body().string();
                }
                log.info("千里马接口请求,{},入参{},出参[{}]{}", HOST + url, json.toJSONString(), response.code(), bodyStr);
                if (response.code() != 200 || response.body() == null) {
                    throw new ValidateException("千里马接口异常code:" + response.code() + "message:" + response.message());
                }
                return JSON.parseObject(bodyStr, Result.class);
            }
        });
    }

    public Result commonPost(String url, JSONObject json, long timeOutMillis) {
        Request request = new Request.Builder()
                .url(HOST + url)
                .header("secretUser", SECRET_USER)
                .header("secretContent", crypt(json))
                .post(RequestBody.create("".getBytes(StandardCharsets.UTF_8)))
                .build();
        return (Result) client.syncExecute(request, new SyncCallback() {
            @Override
            public Object response(Response response) throws IOException {
                String bodyStr = null;
                if (response.body() != null) {
                    bodyStr = response.body().string();
                }
                log.info("千里马接口请求,{},入参{},出参[{}]{}", HOST + url, json.toJSONString(), response.code(), bodyStr);
                if (response.code() != 200 || response.body() == null) {
                    throw new ValidateException("千里马接口异常code:" + response.code() + "message:" + response.message());
                }
                return JSON.parseObject(bodyStr, Result.class);
            }
        }, timeOutMillis);
    }

    public static double similarity(String str1, String str2) {
        if (StringUtils.isEmpty(str1) || StringUtils.isEmpty(str2)) {
            return 0d;
        }
        int[][] dp = new int[str1.length() + 1][str2.length() + 1];
        for (int i = 0; i <= str1.length(); i++) {
            for (int j = 0; j <= str2.length(); j++) {
                if (i == 0) {
                    dp[i][j] = j;
                } else if (j == 0) {
                    dp[i][j] = i;
                } else if (str1.charAt(i - 1) == str2.charAt(j - 1)) {
                    dp[i][j] = dp[i - 1][j - 1];
                } else {
                    dp[i][j] = 1 + Math.min(Math.min(dp[i][j - 1], dp[i - 1][j]), dp[i - 1][j - 1]);
                }
            }
        }
        int distance = dp[str1.length()][str2.length()];
        int max = Math.max(str1.length(), str2.length());
        return (double) (max - distance) / max;
    }

    @Data
    public static class Account {
        private String accountId;
        private String accountKey;
        private String accountSecret;
    }

    @Data
    public static class Result {
        private int code;
        private String msg;
        private String data;

        public boolean isSuccess() {
            return 200 == code;
        }

        public <T> T getData(Class<T> clazz) {
            return JSON.parseObject(data, clazz);
        }
    }
}
