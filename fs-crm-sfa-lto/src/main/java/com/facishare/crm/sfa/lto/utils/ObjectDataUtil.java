package com.facishare.crm.sfa.lto.utils;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.ServiceFacadeImpl;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.metadata.TeamMember;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.security.SecureRandom;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

@Slf4j
public class ObjectDataUtil {
    private static final ServiceFacade serviceFacade = SpringUtil.getContext().getBean(ServiceFacadeImpl.class);
    private static final int PAGE_SIZE = 1000;

    private ObjectDataUtil() {
        throw new IllegalStateException("Utility class");
    }

    public static IObjectData buildObjectData(String tenantId, String apiName, String id) {
        IObjectData objectData = new ObjectData();
        objectData.set("tenant_id", tenantId);
        objectData.set("object_describe_api_name", apiName);
        objectData.set("_id", id);
        return objectData;
    }

    public static List<String> getListValue(IObjectData objectData, String key, List<String> defaultValue) {
        Object value = objectData.get(key);
        if (null == value) {
            return defaultValue;
        } else {
            String str;
            if (value instanceof String) {
                str = (String) value;
            } else {
                str = JSON.toJSONString(value);
            }

            return JSON.parseObject(str, List.class);
        }
    }

    public static Integer getIntegerValue(IObjectData objectData, String key, Integer defaultValue) {
        if (objectData == null || StringUtils.isEmpty(key)) {
            return defaultValue;
        }
        Object tempValue = objectData.get(key);
        if (tempValue != null) {
            try {
                return Integer.parseInt(tempValue.toString());
            } catch (Exception e) {
                return defaultValue;
            }
        }
        return defaultValue;
    }

    public static Long getLongValue(IObjectData objectData, String key, Long defaultValue) {
        if (objectData == null || StringUtils.isEmpty(key)) {
            return defaultValue;
        }
        Object tempValue = objectData.get(key);
        if (tempValue != null) {
            try {
                return Long.valueOf(tempValue.toString());
            } catch (Exception e) {
                return defaultValue;
            }
        }
        return defaultValue;
    }

    public static String getStringValue(IObjectData objectData, String key, String defaultValue) {
        if (objectData == null || StringUtils.isEmpty(key)) {
            return defaultValue;
        }
        Object tempValue = objectData.get(key);
        if (tempValue != null) {
            return tempValue.toString();
        }
        return defaultValue;
    }

    public static boolean getBooleanValue(IObjectData objectData, String key, boolean defaultValue) {
        if (objectData == null || StringUtils.isEmpty(key)) {
            return defaultValue;
        }
        Object tempValue = objectData.get(key);
        if (tempValue != null) {
            try {
                return Boolean.parseBoolean(tempValue.toString());
            } catch (Exception e) {
                return defaultValue;
            }
        }
        return defaultValue;
    }

    public static Boolean getBooleanValue(IObjectData objectData, String key, Boolean defaultValue) {
        if (objectData == null || StringUtils.isEmpty(key)) {
            return defaultValue;
        }
        Object tempValue = objectData.get(key);
        if (tempValue != null) {
            try {
                return Boolean.parseBoolean(tempValue.toString());
            } catch (Exception e) {
                return defaultValue;
            }
        }
        return defaultValue;
    }

    public static String getStringValue(Map<String, Object> dataMap, String key, String defaultValue) {
        if (dataMap.containsKey(key)) {
            Object objectValue = dataMap.get(key);
            if (objectValue == null) {
                return defaultValue;
            }
            return objectValue.toString();
        } else {
            return defaultValue;
        }
    }

    public static Long getLongValue(Map<String, Object> dataMap, String key, Long defaultValue) {
        String longStringValue = getStringValue(dataMap, key, String.valueOf(defaultValue));
        try {
            return Long.valueOf(longStringValue);
        } catch (Exception e) {
            return defaultValue;
        }
    }

    public static Integer getIntegerValue(Map<String, Object> dataMap, String key, Integer defaultValue) {
        String integerStringValue = getStringValue(dataMap, key, String.valueOf(defaultValue));
        try {
            return Integer.parseInt(integerStringValue);
        } catch (Exception e) {
            return defaultValue;
        }
    }

    public static boolean getBooleanValue(Map<String, Object> dataMap, String key, boolean defaultValue) {
        String boolStringValue = getStringValue(dataMap, key, String.valueOf(defaultValue));
        try {
            return Boolean.valueOf(boolStringValue);
        } catch (Exception e) {
            return defaultValue;
        }
    }

    public static Double getDoubleValue(Map<String, Object> dataMap, String key, Double defaultValue) {
        String doubleStringValue = getStringValue(dataMap, key, String.valueOf(defaultValue));
        try {
            return Double.valueOf(doubleStringValue);
        } catch (Exception e) {
            return defaultValue;
        }
    }

    public static String getNameCode() {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        String result = formatter.format(new Date(System.currentTimeMillis()));
        result = result.replace(":", "");
        result = result.replace(".", "");
        result = result.replace(" ", "-");
        int lastNumber = safeGetRandomInteger(100);
        DecimalFormat decimalFormat = new DecimalFormat("###");
        result = String.format("%s%s", result, decimalFormat.format(lastNumber));
        return result;
    }

    public static int safeGetRandomInteger(int bound) {
        int randomValue = bound;
        try {
            SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
            randomValue = secureRandom.nextInt(bound);
        } catch (Exception e) {
            log.warn("safeGetRandomInteger error", e);
        }
        return  randomValue;
    }

    public static void updateFields(User user, List<IObjectData> objectDataList, List<String> updateFieldList) {
        try {
            if (CollectionUtils.isEmpty(objectDataList)) {
                return;
            }
            objectDataList = objectDataList.stream().filter(d -> !StringUtils.isEmpty(d.getId()))
                    .collect(Collectors.toList());
            serviceFacade.batchUpdateByFields(user, objectDataList, updateFieldList);
        } catch (Exception e) {
            log.error("updateFields error", e);
            throw new MetaDataException(e.getMessage());
        }
    }

    public static void updateFields(User user, List<IObjectData> objectDataList, List<String> updateFieldList,
                                    ActionContextUtil.ActionContextOp op) {
        try {
            if (CollectionUtils.isEmpty(objectDataList)) {
                return;
            }
            objectDataList = objectDataList.stream().filter(d -> !StringUtils.isEmpty(d.getId()))
                    .collect(Collectors.toList());
            IActionContext actionContext = ActionContextUtil.createActionContext(user, op);
            serviceFacade.batchUpdateByFields(actionContext, objectDataList, updateFieldList);
        } catch (Exception e) {
            log.error("updateFields error", e);
            throw new MetaDataException(e.getMessage());
        }
    }

    public static void updateFields(User user, List<IObjectData> objectDataList, List<String> updateFieldList,
                                    boolean updateLastModifyTime, boolean skipVersionChange) {
        try {
            if (CollectionUtils.isEmpty(objectDataList)) {
                return;
            }
            objectDataList = objectDataList.stream().filter(d -> !StringUtils.isEmpty(d.getId()))
                    .collect(Collectors.toList());
            ActionContextUtil.ActionContextOp op = ActionContextUtil.ActionContextOp.builder()
                    .updateLastModifyTime(updateLastModifyTime).skipVersionChange(skipVersionChange)
                    .allowUpdateInvalid(true).notValidate(true).build();
            IActionContext actionContext = ActionContextUtil.createActionContext(user, op);
            serviceFacade.batchUpdateByFields(actionContext, objectDataList, updateFieldList);
        } catch (Exception e) {
            log.error("updateFields error", e);
            throw new MetaDataException(e.getMessage());
        }
    }

    public static void dealWithDetail(List<IObjectData> masterDataList, User user, IObjectDescribe objectDescribe) {
        if (CollectionUtils.isEmpty(masterDataList)) {
            return;
        }

        //查从对象
        List<IObjectDescribe> detailDescribes =
                serviceFacade.findDetailDescribes(user.getTenantId(), objectDescribe.getApiName());

        if (CollectionUtils.isEmpty(detailDescribes)) {
            return;
        }

        ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
        detailDescribes.forEach(detailDescribe -> task.submit(() -> dealOneDetail(detailDescribe, masterDataList, user, objectDescribe)));
        try {
            task.await(15000, TimeUnit.MILLISECONDS);
        } catch (TimeoutException e) {
            throw new MetaDataBusinessException(I18N.text(I18NKey.MD_INVALID_DETAIL_DATA_TIME_OUT));
        }
    }

    private static void dealOneDetail(IObjectDescribe detailDescribe, List<IObjectData> masterDataList, User user, IObjectDescribe objectDescribe) {
        List<String> masterDataIds = masterDataList.stream().map(DBRecord::getId).collect(Collectors.toList());
        try {
            QueryResult<IObjectData> detailDataResult = serviceFacade.findDetailObjectDataBatchWithPage(user,
                    objectDescribe.getApiName(), masterDataIds, detailDescribe, 1, PAGE_SIZE, null);

            int totalPage = SearchTemplateQueryExt.calculateTotalPage(detailDataResult.getTotalNumber(), PAGE_SIZE);
            for (int pageNum = 1; pageNum <= totalPage; pageNum++) {
                if (pageNum > 1) {
                    detailDataResult = serviceFacade.findDetailObjectDataBatchWithPage(user,
                            objectDescribe.getApiName(), masterDataIds, detailDescribe, pageNum, PAGE_SIZE, null);
                }
                if (CollectionUtils.isEmpty(detailDataResult.getData())) {
                    break;
                }
                serviceFacade.bulkInvalidAndDeleteWithSuperPrivilege(detailDataResult.getData(), user);
            }
        } catch (MetaDataBusinessException e) {
            log.warn("dealOneDetail error,user:{},masterApiName:{},masterDataId:{},detailApiName:{}",
                    user, objectDescribe.getApiName(), masterDataIds, detailDescribe.getApiName(), e);
        } catch (Exception e) {
            log.error("dealOneDetail error,user:{},masterApiName:{},masterDataId:{},detailApiName:{}",
                    user, objectDescribe.getApiName(), masterDataIds, detailDescribe.getApiName(), e);
        }
    }

    public static boolean hasOwner(IObjectData objectData) {
        List<String> ownerList = objectData.getOwner();
        if (com.facishare.paas.appframework.common.util.CollectionUtils.empty(ownerList)) {
            return false;
        }
        String owner = ownerList.get(0);
        return !StringUtils.isEmpty(owner) && !"0".equals(owner);
    }

    public static boolean hasOwner(ObjectDataDocument dataDocument) {
        IObjectData objectData = dataDocument.toObjectData();
        return hasOwner(objectData);
    }

    public static String getOwner(ObjectDataDocument dataDocument) {
        IObjectData objectData = dataDocument.toObjectData();
        return getOwner(objectData);
    }

    public static String getOwner(IObjectData objectData) {
        List<String> ownerList = objectData.getOwner();
        String owner = "";
        if (com.facishare.paas.appframework.common.util.CollectionUtils.notEmpty(ownerList)) {
            owner = ownerList.get(0);
        }
        return owner;
    }

    public static IObjectData createBaseObjectData(User user) {
        ObjectData objectData = new ObjectData();
        objectData.setTenantId(user.getTenantId());
        objectData.setRecordType("default__c");
        objectData.set("life_status", "normal");
        objectData.set("lock_status", "0");
        objectData.set("package", "CRM");
        objectData.set("is_deleted", false);
        objectData.setOwner(Lists.newArrayList(user.getUserId()));
        objectData.setDataOwnOrganization(Lists.newArrayList());
        objectData.setCreatedBy(user.getUserId());
        objectData.setCreateTime(System.currentTimeMillis());
        objectData.setLastModifiedBy(user.getUserId());
        objectData.setLastModifiedTime(System.currentTimeMillis());
        TeamMember teamMember = new TeamMember(user.getUserId(), TeamMember.Role.OWNER, TeamMember.Permission.READANDWRITE, "", TeamMember.MemberType.EMPLOYEE, "");
        ObjectDataExt.of(objectData).setTeamMembers(Lists.newArrayList(teamMember));
        return objectData;
    }

    public static QueryResult<IObjectData> findDataBySearchQuery(User user, String apiName, SearchTemplateQuery query, List<String> fieldNameList) {
        ActionContextUtil.SearchActionContextOp op = ActionContextUtil.SearchActionContextOp.builder()
                .skipRelevantTeam(true).calculateFormula(false).needDeepQuote(false)
                .esRedisRecentUpdateCheck(true).privilegeCheck(false).forceESSearch(false)
                .build();
        IActionContext actionContext= ActionContextUtil.createSearchActionContext(user, op);
        if(CollectionUtils.isEmpty(fieldNameList)) {
            return serviceFacade.findBySearchQuery(actionContext, apiName, query);
        } else {
            return serviceFacade.findBySearchTemplateQueryWithFields(actionContext, apiName, query, fieldNameList);
        }
    }
}