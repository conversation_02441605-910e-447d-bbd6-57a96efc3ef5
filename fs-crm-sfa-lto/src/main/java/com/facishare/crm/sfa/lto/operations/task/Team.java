package com.facishare.crm.sfa.lto.operations.task;

import com.facishare.paas.metadata.api.IObjectData;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;

class Team implements TemplateMemberField {

    private final BiConsumer<OperationsTask, List<String>> setter;
    private final Map<String, List<String>> map;

    Team(BiConsumer<OperationsTask, List<String>> setter, Map<String, List<String>> map) {
        this.setter = setter;
        this.map = map;
    }

    @Override
    public List<String> getMember(IObjectData bizData) {
        return map.getOrDefault(bizData.getId(), Collections.emptyList());
    }

    @Override
    public BiConsumer<OperationsTask, List<String>> setter() {
        return setter;
    }
}
