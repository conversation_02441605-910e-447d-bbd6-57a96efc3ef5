package com.facishare.crm.sfa.lto.duplicated;


import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.expression.SFAExpressionServiceImpl;
import com.facishare.crm.sfa.lto.duplicated.models.DuplicatedModels;
import com.facishare.crm.sfa.lto.utils.ObjectDataUtil;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.expression.ExpressionService;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IObjectReferenceField;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.service.impl.FilterHandlerService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Component
public class RuleMatcher {
    @Autowired
    ServiceFacade serviceFacade;
    @Autowired
    private ExpressionService expressionService;
    @Autowired
    private FilterHandlerService filterHandlerService;

    @Autowired
    private SFAExpressionServiceImpl sfaExpressionService;


    private static final String NO_SAME_OPERATOR="NSAME";

    private static final String SAME_OPERATOR="SAME";

    private static final String CREATE_TIME_INTERVAL_FILED_NAME="create_time_interval";

    /**
     * 匹配符合条件的处理规则
     *
     * @param tenantId
     * @param apiName
     * @param ruleList
     * @param dataList
     * @return  map key:重复处理规则id，value 符合条件的数据列表 objectData
     */
    public Map<String, List<IObjectData>> matches(String tenantId, String apiName, List<DuplicatedModels.DuplicatedProcessing> ruleList, List<IObjectData> dataList) {
        Map<String, List<IObjectData>> matchMap = Maps.newHashMap();
        IObjectDescribe objectDescribe = serviceFacade.findObject(tenantId, apiName);
        Map<String, IFieldDescribe> fieldDescribeMap = objectDescribe.getFieldDescribeMap();
        List<IObjectData> matchedDataList = Lists.newArrayList();
        for (DuplicatedModels.DuplicatedProcessing processing : ruleList) {
            String processId = processing.getId();
            if (StringUtils.isEmpty(processId)) {
                throw new ValidateException("重复数据处理规则不存在");
            }
            List<DuplicatedModels.Filter> filters = processing.getFilters();
            //没有过滤条件，所有数据都满足
            if(CollectionUtils.isEmpty(filters)) {
                List<String> matchIds = matchedDataList.stream().map(DBRecord::getId).collect(Collectors.toList());
                List<IObjectData> remindDataList = dataList.stream().filter(x -> !matchIds.contains(x.getId())).collect(Collectors.toList());
                matchMap.put(processId, Lists.newArrayList(remindDataList));
                return matchMap;
            }

            String expressStr =  getExpression(filters, fieldDescribeMap);
            if(StringUtils.isBlank(expressStr)) {
                log.warn(String.format(String.format("tenant_id : %s, 重复数据处理过滤规则有误!", tenantId)));
                continue;
            }

            for (IObjectData objectData : dataList) {
                if (matchedDataList.contains(objectData)) {
                    continue; //比较对象是否存在(hash)，存在说明已匹配规则，不再次进行匹配。
                }
                Boolean ret = true;
                if (CollectionUtils.isNotEmpty(filters)) {
                    Map<String, Object> valueMap = getFilterValue(fieldDescribeMap, filters, objectData);
                    ret = expressionService.evaluate(expressStr, valueMap);
                    if (sfaExpressionService.enabledLog(tenantId)) {
                        log.info("tenantId: {},objectId:{}, expressStr: {}, valueMap: {}, ret: {}", tenantId, objectData.getId(), expressStr, valueMap, ret);
                    }

                }
                if (Boolean.TRUE.equals(ret)) {
                    if (!matchMap.containsKey(processId)) {
                        matchMap.put(processId, Lists.newArrayList());
                    }
                    matchMap.get(processId).add(objectData);
                    matchedDataList.add(objectData);
                }
            }
        }
        return matchMap;
    }

    /**
     * 匹配符合条件的处理方式
     *
     * @param tenantId
     * @param modeList
     * @param objectData
     * @param duplicatedDataList
     * @return
     */
    public Tuple<DuplicatedModels.DuplicatedProcessingMode,List<IObjectData>> matches(String tenantId, String apiName,
                                                                                      List<DuplicatedModels.DuplicatedProcessingMode> modeList,
                                                                                      IObjectData objectData,
                                                                                      List<IObjectData> duplicatedDataList) {
        IObjectDescribe objectDescribe = serviceFacade.findObject(tenantId, apiName);
        Map<String, IFieldDescribe> fieldDescribeMap = objectDescribe.getFieldDescribeMap();
        DuplicatedModels.DuplicatedProcessingMode matchedMode = null;
        Tuple<DuplicatedModels.DuplicatedProcessingMode,List<IObjectData>> tuple =new Tuple<>();
        // 因为处理方式在数据库中以json 形式存在,所以需要对其进行一次排序
        modeList = modeList.stream()
                .sorted(Comparator.comparing(DuplicatedModels.DuplicatedProcessingMode::getPriority))
                .collect(Collectors.toList());
        List<IObjectData> matchedDataList = Lists.newArrayList();
        for (DuplicatedModels.DuplicatedProcessingMode mode : modeList) {
            List<DuplicatedModels.Filter> filters = mode.getFilters();
            boolean isMatch = false;
            if (CollectionUtils.isNotEmpty(filters)) {
                Map<String, Object> valueMap = getFilterValue(fieldDescribeMap, filters, objectData);
                for (IObjectData duplicatedData : duplicatedDataList) { //数字、金额、百分数、计算/统计字段返回值是数值时有确定值的在这里校验也问题不大,只有有重复数据的情况下,才会有处理方式的判断
                    String expressStr = getExpression(filters, fieldDescribeMap, duplicatedData);
                    if(StringUtils.isBlank(expressStr)) {
                        log.warn(String.format("tenantId: %s,  no express", tenantId));
                        continue;
                    }
                    log.info(String.format("expressStr：%s", expressStr));
                    Boolean ret = expressionService.evaluate(expressStr, valueMap);
                    if (Boolean.TRUE.equals(ret)) {
                        isMatch = true;
                        if (!matchedDataList.contains(duplicatedData)) {
                            matchedDataList.add(duplicatedData);
                        }
                    }
                }
            } else {
                isMatch = true;
                matchedDataList.addAll(duplicatedDataList);
            }
            if (isMatch) {
                matchedMode = mode;
                break;
            }
        }
        tuple.setKey(matchedMode);
        tuple.setValue(matchedDataList);
        return tuple;
    }

    public Map<String, List<IObjectData>> matches(User user, String apiName, List<DuplicatedModels.DuplicatedProcessing> ruleList, List<IObjectData> dataList) {
        Map<String, List<IObjectData>> matchMap = Maps.newHashMap();
        IObjectDescribe objectDescribe = serviceFacade.findObject(user.getTenantId(), apiName);
        Map<String, IFieldDescribe> fieldDescribeMap = objectDescribe.getFieldDescribeMap();
        for (DuplicatedModels.DuplicatedProcessing processing : ruleList) {
            String processId = processing.getId();
            if (StringUtils.isEmpty(processId)) {
                continue;
            }
            List<DuplicatedModels.Filter> filters = processing.getFilters();
            //没有过滤条件，所有数据都满足优先级低的规则也不需要再处理
            if(CollectionUtils.isEmpty(filters)) {
                matchMap.put(processId, Lists.newArrayList(dataList));
                continue;
            }
            String expressStr = getExpression(filters, fieldDescribeMap);
            if(StringUtils.isBlank(expressStr)) {
                log.warn(String.format(String.format("tenantId: %s, 重复数据处理过滤规则有误!", user.getTenantId())));
                continue;
            }
            log.info(String.format("expressStr：%s", expressStr));
            for (IObjectData objectData : dataList) {
                Boolean ret = true;
                if (CollectionUtils.isNotEmpty(filters)) {
                    Map<String, Object> valueMap = getFilterValue(fieldDescribeMap, filters, objectData);
                    ret = expressionService.evaluate(expressStr, valueMap);
                }
                if (Boolean.TRUE.equals(ret)) {
                    if (!matchMap.containsKey(processId)) {
                        matchMap.put(processId, Lists.newArrayList());
                    }
                    matchMap.get(processId).add(objectData);
                }
            }
        }
        return matchMap;
    }

    //非正常情况下的计算表达式,需要将查重重复数据 与当前创建数据进行比较,Condition 需要重新构造
    private String getExpression(List<DuplicatedModels.Filter> filters, Map<String, IFieldDescribe> fieldsMap, IObjectData objectData) {
        List<String> list = Lists.newArrayList();
        for (DuplicatedModels.Filter filter : filters) {
            List<DuplicatedModels.Condition> conditions = buildConditions(filter.getConditions(), objectData,fieldsMap);
            String andExpression = getAndExpression(conditions, fieldsMap);
            list.add(andExpression);
        }
        return String.join(" || ", list);
    }

    private List<DuplicatedModels.Condition> buildConditions(List<DuplicatedModels.Condition> conditions, IObjectData objectData, Map<String, IFieldDescribe> fieldsMap) {
        List<DuplicatedModels.Condition> buildConditions = Lists.newArrayList();
        Long createTime = objectData.getCreateTime();
        for (DuplicatedModels.Condition condition : conditions) {
            String fieldName = condition.getFieldName();
            String operator = condition.getOperator();
            List<String> values = condition.getFieldValues();

            if (CREATE_TIME_INTERVAL_FILED_NAME.equals(fieldName)) {
                DuplicatedModels.Condition buildCondition = new DuplicatedModels.Condition();
                double interval = 0.00;
                fieldName = "create_time";
                buildCondition.setFieldName(fieldName);
                if (CollectionUtils.isNotEmpty(values)) {
                    String v = values.get(0);
                    if (v != null) {
                        interval = Double.parseDouble(v);
                    }
                }
                //创建数据时间(参与比较数据)-重复数据创建时间(objectData)>间隔时间(给定值)
                //重复数据创建时间+间隔时间 < 创建数据时间（参与比较数据）
                //compareTime < 创建数据时间（参与比较数据）
                // 条件为 创建时间间隔（小时）< 给定值
                long compareTime = createTime + getInt(interval * 60 * 60 * 1000);
                buildCondition.setOperator(operator);
                buildCondition.setFieldValues(Lists.newArrayList(String.valueOf(compareTime)));
                buildConditions.add(buildCondition);
            } else if ((SAME_OPERATOR.equals(operator) || NO_SAME_OPERATOR.equals(operator))) {
                DuplicatedModels.Condition buildCondition = new DuplicatedModels.Condition();
                buildCondition.setFieldName(fieldName);
                String fieldType = null;
                if (fieldsMap.get(fieldName) != null) {
                    fieldType = fieldsMap.get(fieldName).getType();
                }
                if (StringUtils.isEmpty(fieldType)) continue;
                operator = convertOperator(operator, fieldType);
                buildCondition.setOperator(operator);
                Object v = ObjectDataDocument.of(objectData).get(fieldName);
                List<String> vList;
                if (v instanceof List) {
                    vList = (List<String>) v;
                } else {
                    vList = Lists.newArrayList(String.valueOf(v));

                }
                if (IFieldType.OBJECT_REFERENCE.equals(fieldType)) {
                    Map<String, Object> valueMap = getReferenceObjectName(objectData, fieldName, fieldsMap.get(fieldName));
                    if (valueMap.containsKey(fieldName)) {
                        v = valueMap.get(fieldName);
                        vList = Lists.newArrayList(String.valueOf(v));
                    }
                }
                buildCondition.setFieldValues(vList);
                buildConditions.add(buildCondition);
            } else {
                DuplicatedModels.Condition buildCondition = new DuplicatedModels.Condition();
                buildCondition.setFieldName(fieldName);
                buildCondition.setOperator(operator);
                buildCondition.setFieldValues(values);
                buildConditions.add(buildCondition);
            }
        }
        return buildConditions;
    }

    //四舍五入把double转化int整型，0.5进一，小于0.5不进一
    private long getInt(double number) {
        BigDecimal bd = BigDecimal.valueOf(number).setScale(0, BigDecimal.ROUND_HALF_UP);
        return Long.parseLong(bd.toString());
    }

    //正常情况下的计算表达式
    private String getExpression(List<DuplicatedModels.Filter> filters, Map<String, IFieldDescribe> fieldsMap) {
        List<String> list = Lists.newArrayList();
        for (DuplicatedModels.Filter filter : filters) {
            String andExpression = getAndExpression(filter.getConditions(), fieldsMap);
            list.add(andExpression);
        }
        return String.join(" || ", list);
    }

    private String getAndExpression(List<DuplicatedModels.Condition> conditions, Map<String, IFieldDescribe> fieldsMap) {
        List<String> list = new ArrayList<>();
        for (DuplicatedModels.Condition condition : conditions) {
            String singleExpression = getSingleFilterExpression(condition, fieldsMap);
            if (singleExpression.isEmpty()) {
                continue;
            }
            list.add(singleExpression);
        }
        return String.join(" && ", list);
    }

    private String getSingleFilterExpression(DuplicatedModels.Condition condition, Map<String, IFieldDescribe> fieldsMap) {
        if (condition == null)
            throw new ValidateException("condition is null");
        if (StringUtils.isEmpty(condition.getFieldName()))
            throw new ValidateException("field_name is null or empty");
        String fieldName = condition.getFieldName();
        IFieldDescribe fieldDescribe = fieldsMap.get(fieldName);
        if (fieldDescribe == null) {
            throw new ValidateException(String.format("fieldDescribe has no field name %s", fieldName));
        }
        if (fieldDescribe.getType() == null)
            throw new ValidateException(String.format("fieldDescribe has no type %s", fieldName));
        String fieldType = fieldDescribe.getType();
        String rst;
        switch (fieldType) {
            case IFieldType.NUMBER:
            case IFieldType.CURRENCY:
                rst = getNumberExpression(condition);
                break;
            case IFieldType.DATE:
            case IFieldType.DATE_TIME:
            case IFieldType.TIME:
                rst = getDateExpression(condition);
                break;
            case IFieldType.RECORD_TYPE:
            case IFieldType.SELECT_ONE:
            case IFieldType.COUNTRY:
            case IFieldType.PROVINCE:
            case IFieldType.CITY:
            case IFieldType.DISTRICT:
            case IFieldType.EMPLOYEE:
            case IFieldType.DEPARTMENT:
                rst = getSelectOneExpression(condition);
                break;
            case IFieldType.MULTI_LEVEL_SELECT_ONE:
            case IFieldType.SELECT_MANY:
                rst = getSelectManyExpression(condition);
                break;
            default:
                rst = getTextExpression(condition);
                break;
        }
        return rst;
    }

    private String getSelectManyExpression(DuplicatedModels.Condition condition) {
        Operator operator = Operator.valueOf(condition.getOperator());
        String fieldName = condition.getFieldName();
        List<String> values = condition.getFieldValues();
        String rst;
        switch (operator) {
            case EQ:
                StringBuilder sb = new StringBuilder("(" + fieldName + " == [");
                if (CollectionUtils.isNotEmpty(values)) {
                    sb.append("'" + values.get(0) + "'");
                }
                for (int i = 1; i < values.size(); i++) {
                    sb.append(",'" + values.get(i) + "'");
                }
                sb.append("])");
                rst = sb.toString();
                break;
            case N:
            case NEQ:
                sb = new StringBuilder("(" + fieldName + " != [");
                if (CollectionUtils.isNotEmpty(values)) {
                    sb.append("'" + values.get(0) + "'");
                }
                for (int i = 1; i < values.size(); i++) {
                    sb.append(",'" + values.get(i) + "'");
                }
                sb.append("])");
                rst = sb.toString();
                break;
            case LIKE:
            case IN:
            case HASANYOF:
                if (CollectionUtils.isEmpty(values)) rst = "";
                else {
                    sb = new StringBuilder(" INCLUDES(" + fieldName + " ");
                    for (int i = 0; i < values.size(); i++) {
                        sb.append(",'" + values.get(i) + "'");
                    }
                    sb.append(") ");
                    rst = sb.toString();
                }
                break;
            case NLIKE:
            case NIN:
            case NHASANYOF:
                if (CollectionUtils.isEmpty(values)) rst = "";
                else {
                    sb = new StringBuilder(" !INCLUDES(" + fieldName + " ");
                    for (int i = 0; i < values.size(); i++) {
                        sb.append(",'" + values.get(i) + "'");
                    }
                    sb.append(") ");
                    rst = sb.toString();
                }
                break;
            case IS:
                rst =  String.format(" (!%s) ", fieldName);
                break;
            case ISN:
                rst = String.format(" (%s) ", fieldName);
                break;
            default:
                rst = "";
                break;
        }
        return rst;
    }

    private String getNumberExpression(DuplicatedModels.Condition condition) {
        Operator operator = Operator.valueOf(condition.getOperator());
        String fieldName = condition.getFieldName();
        List<String> values = condition.getFieldValues();
        if (CollectionUtils.isEmpty(values)) {
            throw new ValidateException("field value not exist");
        }

        String rst;
        switch (operator) {
            case EQ:
                rst = String.format(" (%s ==  ", fieldName);
                break;
            case N:
            case NEQ:
                rst = String.format(" NOT(%s == ", fieldName);
                break;
            case LT:
                rst = String.format(" (%s <  ", fieldName);
                break;
            case LTE:
                rst = String.format(" (%s <= ", fieldName);
                break;
            case GT:
                rst = String.format(" (%s > ", fieldName);
                break;
            case GTE:
                rst = String.format(" (%s >=  ", fieldName);
                break;
            default:
                rst = "";
                break;
        }
        rst += Double.valueOf(values.get(0));
        rst += " ) ";
        return rst;
    }

    private String getTextExpression(DuplicatedModels.Condition condition) {
        Operator operator = Operator.valueOf(condition.getOperator());
        String fieldName = condition.getFieldName();
        List<String> values = condition.getFieldValues();
        String fieldValue = "";
        if (CollectionUtils.isNotEmpty(values)) {
            fieldValue = values.get(0);
            if (StringUtils.isNotBlank(fieldValue) && fieldValue.contains("'")) {
                fieldValue = fieldValue.replaceAll("'", "\\\\'");
            }
        }
        String rst;
        switch (operator) {
            case EQ:
                rst = String.format(" EQUALS(%s,'%s') ", fieldName, fieldValue);
                break;
            case N:
            case NEQ:
                rst = String.format(" !EQUALS(%s,'%s') ", fieldName, fieldValue);
                break;
            case IN:
            case LIKE:
                rst = String.format(" CONTAINS(%s,'%s')", fieldName, fieldValue);
                break;
            case NIN:
            case NLIKE:
                rst = String.format(" !CONTAINS(%s,'%s') ", fieldName, fieldValue);
                break;
            case IS:
                rst = String.format(" (%s == null || %s =='') ", fieldName,fieldName);
                break;
            case ISN:
                rst = String.format(" (%s != null && %s !='') ", fieldName,fieldName);
                break;
            case STARTWITH:
                rst = String.format(" STARTWITH(%s,'%s') ", fieldName, fieldValue);
                break;
            case ENDWITH:
                rst = String.format(" ENDWITH(%s,'%s') ", fieldName, fieldValue);
                break;
            default:
                rst = "";
                break;
        }
        return rst;
    }

    private String getDateExpression(DuplicatedModels.Condition condition) {
        Operator operator = Operator.valueOf(condition.getOperator());
        String fieldName = condition.getFieldName();
        List<String> values = condition.getFieldValues();
        String rst = "";
        long time = 0;

        // 处理动态时间范围解析为原格式
        IFilter dateFilters = new Filter();
        dateFilters.setFieldName(fieldName);
        dateFilters.setOperator(operator);
        dateFilters.setFieldValues(values);
        try {
            if (CollectionUtils.isNotEmpty(dateFilters.getFieldValues())) {
                if (!Operator.ISN.equals(operator) && !Operator.IS.equals(operator)) {
                    if (CollectionUtils.size(dateFilters.getFieldValues()) == 1) {
                        //  兼容下特殊情况，并且长度小于三位数字(说明不是时间戳)
                        if (StringUtils.isNotEmpty(dateFilters.getFieldValues().get(0))
                                && dateFilters.getFieldValues().get(0).length() < 3) {
                            dateFilters = filterHandlerService.getDateFilters(dateFilters);
                            operator = dateFilters.getOperator();
                        }
                    } else {
                        dateFilters = filterHandlerService.getDateFilters(dateFilters);
                        operator = dateFilters.getOperator();
                    }
                }
            }
        } catch (MetadataServiceException e) {
            log.warn("get date filter error:{}", JSON.toJSONString(dateFilters), e);
        }
        values = dateFilters.getFieldValues();

        if (CollectionUtils.isNotEmpty(values) && !Operator.IS.equals(operator) && !Operator.ISN.equals(operator)) {
            try {
                time = Long.parseLong(values.get(0));
            } catch (Exception e) {
                throw new ValidateException("field value not exist");
            }
        }
        switch (operator) {
            case EQ:
                rst = String.format(" (%s >= %d)  && (%s <= %d)", fieldName,
                        time, fieldName, time + 24 * 60 * 60 * 1000 - 1);
                break;
            case LT:
                rst = String.format(" (%s < %d)  ", fieldName, time);
                break;
            case LTE:
                rst = String.format(" (%s <= %d)  ", fieldName, time);
                break;
            case GT: // 晚于当天最后一毫秒
                rst = String.format(" (%s > %d)  ", fieldName, time + 24 * 60 * 60 * 1000 - 1);
                break;
            case GTE:
                rst = String.format(" (%s >= %d)  ", fieldName, time + 24 * 60 * 60 * 1000 - 1);
                break;
            case BETWEEN:
                if(CollectionUtils.isEmpty(values)) {
                    rst = " (1 < 1) ";
                    break;
                }
                Long   time2 = Long.valueOf(values.get(1));
                rst = String.format(" (%s >= %d)  && (%s <= %d)", fieldName,
                        time, fieldName, time2 + 24 * 60 * 60 * 1000 - 1);
                break;
            case IS:
                rst = String.format(" (%s == null ) ", fieldName);
                break;
            case ISN:
                rst = String.format(" (%s != null ) ", fieldName);
                break;
            default:
                rst = "";
                break;
        }
        return rst;
    }

    private String getSelectOneExpression(DuplicatedModels.Condition condition) {
        Operator operator = Operator.valueOf(condition.getOperator());
        String fieldName = condition.getFieldName();
        List<String> values = condition.getFieldValues();
        String rst;
        switch (operator) {
            case EQ:
            case IN:
            case LIKE:
            case HASANYOF:
                StringBuilder sb = new StringBuilder("ISSelectOptionVAL(" + fieldName);
                for (int i = 0; i < values.size(); i++) {
                    sb.append(",'" + values.get(i) + "'");
                }
                sb.append(")");
                rst = sb.toString();
                break;
            case NIN:
            case NLIKE:
            case N:
            case NEQ:
            case NHASANYOF:
                sb = new StringBuilder("!ISSelectOptionVAL(" + fieldName);
                for (int i = 0; i < values.size(); i++) {
                    sb.append(",'" + values.get(i) + "'");
                }
                sb.append(")");
                rst = sb.toString();
                break;
            case IS:
                rst = String.format(" (%s == null) ", fieldName);
                break;
            case ISN:
                rst = String.format(" (%s != null) ", fieldName);
                break;
            default:
                rst = "";
                break;
        }
        return rst;
    }

    private Map<String, Object> getFilterValue(Map<String, IFieldDescribe> fieldsMap, List<DuplicatedModels.Filter> filters,
                                               IObjectData objectData) {
        Map<String, Object> rst = new HashMap<>();
        List<DuplicatedModels.Condition> conditions = Lists.newArrayList();
        filters.stream().forEach(f -> conditions.addAll(f.getConditions()));
        for (DuplicatedModels.Condition condition : conditions) {
            if (condition == null)
                throw new ValidateException("condition is null");
            if (StringUtils.isEmpty(condition.getFieldName()))
                throw new ValidateException("field_name is null or empty");
            if (StringUtils.isEmpty(condition.getOperator()))
                throw new ValidateException("operator is null or empty");
            String fieldName = condition.getFieldName();
            if (CREATE_TIME_INTERVAL_FILED_NAME.equals(fieldName)) fieldName = "create_time";
            IFieldDescribe fieldDescribe = fieldsMap.get(fieldName);
            if (fieldDescribe == null) {
                throw new ValidateException(String.format("field describe is not exist, filed name is %s", fieldName));
            }
            String fieldType = fieldDescribe.getType();
            if (fieldDescribe.getType() == null)
                throw new ValidateException(String.format("fieldDescribe has no type %s", fieldName));
            switch (fieldType) {
                case IFieldType.NUMBER:
                    if (objectData.get(fieldName) == null) {
                        rst.put(fieldName, null);
                    } else {
                        rst.put(fieldName, new Double(objectData.get(fieldName).toString()));
                    }
                    break;
                case IFieldType.CURRENCY:
                    if (objectData.get(fieldName) == null) {
                        rst.put(fieldName, null);
                    } else {
                        rst.put(fieldName, new BigDecimal(objectData.get(fieldName).toString()).doubleValue());
                    }
                    break;
                case IFieldType.EMPLOYEE:
                case IFieldType.DEPARTMENT:
                    Object value = objectData.get(fieldName);
                    if (value == null || "".equals(value)) {
                        rst.put(fieldName, null);
                    } else {
                        String strValue = "";
                        if (value instanceof String) {
                            strValue = (String) value;
                        } else {
                            strValue = JSON.toJSONString(value);
                        }
                        if(!strValue.startsWith("[") && !strValue.endsWith("]")) {
                            break;
                        }
                        List<String> values = ObjectDataUtil.getListValue(objectData,fieldName, Lists.newArrayList());
                        if (CollectionUtils.isNotEmpty(values)) {
                            rst.put(fieldName, values.get(0));
                        } else {
                            rst.put(fieldName, null);
                        }
                    }
                    break;
                case IFieldType.SELECT_MANY:
                    List<String> arrayList = ObjectDataUtil.getListValue(objectData,fieldName, Lists.newArrayList());
                    if (CollectionUtils.isEmpty(arrayList)) {
                        rst.put(fieldName, null);
                    } else {
                        Collections.sort(arrayList);
                        String[] strArray = new String[arrayList.size()];
                        for (int i = 0; i < arrayList.size(); i++) {
                            strArray[i] = arrayList.get(i);
                        }
                        rst.put(fieldName, strArray);
                    }
                    break;
                case IFieldType.OBJECT_REFERENCE:
                    rst =getReferenceObjectName(objectData,fieldName,fieldDescribe);
                    break;
                case IFieldType.LOCATION:
                    if (objectData.get(fieldName) == null)
                        rst.put(fieldName, null);
                    else {
                        String[] dataLocation = objectData.get(fieldName).toString().split("\\u0024");
                        if (dataLocation == null || dataLocation.length == 0)
                            rst.put(fieldName, null);
                        else
                            rst.put(fieldName, dataLocation[dataLocation.length - 1]);
                    }
                    break;
                default:
                    rst.put(fieldName, objectData.get(fieldName));
                    break;
            }
        }
        return rst;
    }

    private Map<String,Object> getReferenceObjectName(IObjectData objectData, String fieldName, IFieldDescribe fieldDescribe) {
        Map<String, Object> result = new HashMap<>();
        String referenceFieldName = String.format("%s__r", fieldName);
        if(StringUtils.isNotEmpty(objectData.get(referenceFieldName,String.class))) {
            result.put(fieldName, objectData.get(referenceFieldName));
        }
        else {
            if (StringUtils.isNotEmpty(objectData.get(fieldName, String.class))) {
                IObjectReferenceField referenceField = null;
                if (fieldDescribe instanceof IObjectReferenceField) {
                    referenceField = (IObjectReferenceField) fieldDescribe;
                }
                if (referenceField == null) {
                    result.put(fieldName, null);
                    return result;
                }
                String fieldValue = objectData.get(fieldName, String.class);
                User user = new User(objectData.getTenantId(), User.SUPPER_ADMIN_USER_ID);
                List<IObjectData> referenceObjectDataList = serviceFacade.findObjectDataByIdsIncludeDeleted(user, Lists.newArrayList(fieldValue), referenceField.getTargetApiName());
                if (CollectionUtils.isEmpty(referenceObjectDataList)) {
                    result.put(fieldName, null);
                } else {
                    result.put(fieldName, referenceObjectDataList.get(0).getName());
                }
            } else {
                result.put(fieldName, null);
            }
        }
        return result;
    }

    private String convertOperator( String operator,String fieldType) {
        String result = operator;

        if(IFieldType.SELECT_MANY.equals(fieldType)) {
            if (SAME_OPERATOR.equals(operator)) {
                result = "IN";
            }
            if (NO_SAME_OPERATOR.equals(operator)) {
                result = "NIN";
            }
        } else {
            if (SAME_OPERATOR.equals(operator)) {
                result = "EQ";
            }
            if (NO_SAME_OPERATOR.equals(operator)) {
                result = "NEQ";
            }
        }
        return result;
    }
}