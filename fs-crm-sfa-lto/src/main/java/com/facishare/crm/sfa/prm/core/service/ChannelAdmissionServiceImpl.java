package com.facishare.crm.sfa.prm.core.service;

import com.facishare.crm.sfa.prm.api.channel.ChannelAdmissionService;
import com.facishare.crm.sfa.prm.api.dto.AdmissionConfigDTO;
import com.facishare.crm.sfa.prm.platform.utils.ObjectUtils;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.model.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * ============================================================
 *
 * @Description: 渠道准入后台管理服务实现
 * @CreatedBy: Sundy on 2025-02-27
 * ============================================================
 */
@Service
@Slf4j
public class ChannelAdmissionServiceImpl implements ChannelAdmissionService {
    @Resource(name = "configService")
    private ConfigService configService;

    @Override
    public AdmissionConfigDTO fetchChannelAdmissionConfig(User user) {
        String channelAccess = fetchOpenChannelAccess(user);
        String admissionConfigStr = configService.findTenantConfig(user, ADMISSION_CONFIG);
        AdmissionConfigDTO admissionConfig = ObjectUtils.convertObject(admissionConfigStr, AdmissionConfigDTO.class);
        if (admissionConfig == null) {
            return AdmissionConfigDTO.builder()
                    .channelAccess(channelAccess)
                    .build();
        }
        admissionConfig.setChannelAccess(channelAccess);
        return admissionConfig;
    }

    @Override
    public String fetchOpenChannelAccess(User user) {
        String openChannelAccess = configService.findTenantConfig(user, OPEN_CHANNEL_ACCESS);
        return StringUtils.isNotBlank(openChannelAccess) ? openChannelAccess : "close";

    }
}
