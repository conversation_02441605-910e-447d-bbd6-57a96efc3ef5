package com.facishare.crm.sfa.lto.accountreerelation;

import com.facishare.crm.sfa.lto.accountreerelation.models.AccountTreeRelationModels.CreateAccountTreeRelationArg;
import com.facishare.crm.sfa.lto.accountreerelation.models.AccountTreeRelationModels.RelationshipsMapping;
import com.facishare.crm.sfa.lto.accountreerelation.models.EquityRelationModels;
import com.facishare.crm.sfa.lto.utils.CommonTreeRelationUtil;
import com.facishare.crm.sfa.lto.utils.ListsUtil;
import com.facishare.crm.sfa.lto.utils.ObjectDataUtil;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.lto.accountreerelation.models.AccountTreeRelationConstants.*;

@Component
@Slf4j
public class CommonTreeRelationLogService {
    @Autowired
    private ServiceFacade serviceFacade;

    private static final int BACH_SIZE = 100;

    public void logTreeRelation(User user, EquityRelationModels.LogType logType, List<CommonTreeRelationUtil.Updated> updatedList) {
        if(CollectionUtils.empty(updatedList)) {
            return;
        }
        //获取当前数据的describeApiName
        String describeApiName = updatedList.get(0).getData().getDescribeApiName();
        RelationshipsMapping relationshipsMapping = CommonTreeRelationUtil.getMappingInfoByLinkDescribeApiName(describeApiName);

        List<IObjectData> logDataList = Lists.newArrayList();
        updatedList.forEach(updated -> {
            IObjectData logData = convert2LogObjectData(user, logType, updated.getPre(), updated.getData(),relationshipsMapping);
            logDataList.add(logData);
        });

        bulkSaveData(user, logDataList);
    }

    public void logTreeRelation(User user, String rootId, EquityRelationModels.ConflictDataInfo conflictDataInfo, CreateAccountTreeRelationArg arg) {
        if("Y".equals(conflictDataInfo.getNode_type()) && rootId.equals(conflictDataInfo.getConflict_root_id())) {
            return;
        }
        RelationshipsMapping relationshipsMapping = CommonTreeRelationUtil.getRelationshipsMapping(arg.getDescribeApiName());
        String tenantId = user.getTenantId();
        String conflictRootId = conflictDataInfo.getConflict_root_id();
        List<String> rootIds = Lists.newArrayList(rootId, conflictRootId);
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(100);
        searchQuery.setOffset(0);
        searchQuery.setPermissionType(0);
        searchQuery.setNeedReturnCountNum(false);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, "tenant_id", tenantId);
        SearchUtil.fillFilterEq(filters, "object_describe_api_name",relationshipsMapping.getLinkDescribeApiName());
        SearchUtil.fillFilterEq(filters, ENTERPRISE_ID, conflictDataInfo.getEnterprise_id());
        SearchUtil.fillFilterIn(filters, ROOT_ID, rootIds);
        SearchUtil.fillFilterEq(filters, IS_LEAF, true);
        searchQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = ObjectDataUtil.findDataBySearchQuery(user, relationshipsMapping.getLinkDescribeApiName(), searchQuery, Lists.newArrayList());
        if(queryResult == null || CollectionUtils.empty(queryResult.getData())) {
            return;
        }
        List<IObjectData> objectDataList = Lists.newArrayList();
        if("E".equals(conflictDataInfo.getNode_type()) && rootId.equals(conflictRootId)) {
            IObjectData objectData = queryResult.getData().get(0);
            for(int index = 1; index < queryResult.getData().size(); ++index) {
                IObjectData logData = convert2LogObjectData(user, EquityRelationModels.LogType.GENERATE_TREE, queryResult.getData().get(index), objectData,relationshipsMapping);
                logData.set("conflict_type", "same_with_self");
                objectDataList.add(logData);
            }
        } else {
            List<IObjectData> lstData = queryResult.getData().stream().filter(x -> rootId.equals(x.get(ROOT_ID))).collect(Collectors.toList());
            List<IObjectData> lstConflictData = queryResult.getData().stream().filter(x -> conflictRootId.equals(x.get(ROOT_ID))).collect(Collectors.toList());
            lstData.forEach(objectData ->
                lstConflictData.forEach(preData -> {
                    IObjectData logData = convert2LogObjectData(user, EquityRelationModels.LogType.GENERATE_TREE, preData, objectData,relationshipsMapping);
                    logData.set("conflict_type", "E".equals(conflictDataInfo.getNode_type()) ? "same_with_other" : "larger_than_other");
                    objectDataList.add(logData);
                })
            );
        }
        bulkSaveData(user, objectDataList);
    }

    @NotNull
    private IObjectData convert2LogObjectData(User user, EquityRelationModels.LogType logType, IObjectData preObjectData, IObjectData objectData,RelationshipsMapping relationshipsMapping) {
        IObjectData logObjectData = ObjectDataUtil.createBaseObjectData(user);
        logObjectData.setId(serviceFacade.generateId());
        logObjectData.setDescribeApiName(relationshipsMapping.getLinkDescribeLogApiName());
        logObjectData.setName(objectData.getName());
        logObjectData.set("pre_account_tree_relation_id", preObjectData.getId());
        logObjectData.set("account_tree_relation_id", objectData.getId());
        logObjectData.set("pre_level", preObjectData.get("level"));
        logObjectData.set("level", objectData.get("level"));
        logObjectData.set("pre_enterprise_id", preObjectData.get(ENTERPRISE_ID));
        logObjectData.set(ENTERPRISE_ID, objectData.get(ENTERPRISE_ID));
        logObjectData.set("pre_"+relationshipsMapping.getLinkFieldApiName(), preObjectData.get(relationshipsMapping.getLinkFieldApiName()));
        logObjectData.set(relationshipsMapping.getLinkFieldApiName(), objectData.get(relationshipsMapping.getLinkFieldApiName()));
        logObjectData.set("pre_parent_id", preObjectData.get(PARENT_ID));
        logObjectData.set(PARENT_ID, objectData.get(PARENT_ID));
        logObjectData.set("pre_parent_enterprise_id", preObjectData.get(PARENT_ENTERPRISE_ID));
        logObjectData.set(PARENT_ENTERPRISE_ID, objectData.get(PARENT_ENTERPRISE_ID));
        logObjectData.set("pre_"+CommonTreeRelationUtil.PARENT_REF_PREFIX+relationshipsMapping.getLinkFieldApiName(), preObjectData.get(CommonTreeRelationUtil.PARENT_REF_PREFIX+relationshipsMapping.getLinkFieldApiName()));
        logObjectData.set(CommonTreeRelationUtil.PARENT_REF_PREFIX+relationshipsMapping.getLinkFieldApiName(), objectData.get(CommonTreeRelationUtil.PARENT_REF_PREFIX+relationshipsMapping.getLinkFieldApiName()));
        logObjectData.set("pre_root_id", preObjectData.get(ROOT_ID));
        logObjectData.set(ROOT_ID, objectData.get(ROOT_ID));
        logObjectData.set("pre_root_enterprise_id", preObjectData.get(ROOT_ENTERPRISE_ID));
        logObjectData.set(ROOT_ENTERPRISE_ID, objectData.get(ROOT_ENTERPRISE_ID));
        logObjectData.set("pre_"+CommonTreeRelationUtil.ROOT_REF_PREFIX+relationshipsMapping.getLinkFieldApiName(), preObjectData.get(CommonTreeRelationUtil.ROOT_REF_PREFIX+relationshipsMapping.getLinkFieldApiName()));
        logObjectData.set(CommonTreeRelationUtil.ROOT_REF_PREFIX+relationshipsMapping.getLinkFieldApiName(), objectData.get(CommonTreeRelationUtil.ROOT_REF_PREFIX+relationshipsMapping.getLinkFieldApiName()));
        logObjectData.set("pre_tree_path", preObjectData.get(TREE_PATH));
        logObjectData.set(TREE_PATH, objectData.get(TREE_PATH));
        logObjectData.set("pre_is_statistics_include", preObjectData.get(IS_STATISTICS_INCLUDE));
        logObjectData.set(IS_STATISTICS_INCLUDE, objectData.get(IS_STATISTICS_INCLUDE));
        logObjectData.set("pre_is_manual_add", preObjectData.get("is_manual_add"));
        logObjectData.set("is_manual_add", objectData.get("is_manual_add"));
        logObjectData.set("pre_is_auto_update", preObjectData.get("is_auto_update"));
        logObjectData.set("is_auto_update", objectData.get("is_auto_update"));
        logObjectData.set("pre_is_root", preObjectData.get(IS_ROOT));
        logObjectData.set(IS_ROOT, objectData.get(IS_ROOT));
        logObjectData.set("pre_is_leaf", preObjectData.get(IS_LEAF));
        logObjectData.set(IS_LEAF, objectData.get(IS_LEAF));
        logObjectData.set("pre_is_branch", preObjectData.get(IS_BRANCH));
        logObjectData.set(IS_BRANCH, objectData.get(IS_BRANCH));
        logObjectData.set("log_type", logType.toString().toLowerCase());
        return logObjectData;
    }

    private void bulkSaveData(User user, List<IObjectData> objectDataList) {
        if(CollectionUtils.empty(objectDataList)) {
            return;
        }
        if(objectDataList.size() > BACH_SIZE) {
            List<List<IObjectData>> splitList = ListsUtil.splitList(Lists.newArrayList(objectDataList), BACH_SIZE);
            splitList.forEach(listData -> serviceFacade.bulkSaveObjectData(listData, user, true, true,
                    x -> ActionContextExt.of(user, RequestContextManager.getContext()).setNotValidate(true).getContext()));
        } else {
            serviceFacade.bulkSaveObjectData(objectDataList, user, true, true,
                    x -> ActionContextExt.of(user, RequestContextManager.getContext()).setNotValidate(true).getContext());
        }
    }
}
