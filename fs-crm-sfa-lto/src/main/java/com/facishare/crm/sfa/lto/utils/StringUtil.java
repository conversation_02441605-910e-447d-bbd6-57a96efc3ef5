package com.facishare.crm.sfa.lto.utils;

import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

/**
 * 字符串相关工具
 *
 * <AUTHOR>
 */
public class StringUtil {

  private static final int[] CONVERT = new int[127];

  // 限制创建实例
  private StringUtil() {
  }

  /**
   * 判断是否是空字符串
   *
   * @param s 待检查的字符串
   * @return 空指针或者长度为0返回true，否则返回false
   */
  public static boolean isEmpty(String s) {
    return s == null || s.length() == 0;
  }

  public static boolean isNotEmpty(String s) {
    return !isEmpty(s);
  }

  /**
   * 判断字符是否为字母
   *
   * @param c 待判断的字符
   * @return 字母返回true
   */
  public static boolean isLetter(char c) {
    return c < 127 && CONVERT[c] > 1;
  }

  /**
   * 判断字符是否为数字
   *
   * @param c 待判断的字符
   * @return 数字返回true
   */
  public static boolean isDigit(char c) {
    return c >= '0' && c <= '9';
  }

  /**
   * 判断字符是否为字母或数字
   *
   * @param c 待判断的字符
   * @return 字母或数字返回true
   */
  public static boolean isLetterOrDigit(char c) {
    return c < 127 && CONVERT[c] > 0;
  }

  /**
   * 判断字符是否包含字母或数字
   *
   * @param word 待判断的字符串
   * @return 只包含字母或数字返回true
   */
  public static boolean hasLetterAndDigit(CharSequence word) {
    return hasLetterAndDigit(word, 0, word.length());
  }

  /**
   * 判断字符是否包含字母或数字
   *
   * @param word  待判断的字符串
   * @param start 起始位置
   * @return 只包含字母或数字返回true
   */
  public static boolean hasLetterAndDigit(CharSequence word, int start, int stop) {
    int val = 0;
    for (int i = start; i < stop; i++) {
      char c = word.charAt(i);
      if (c < 127) {
        val |= CONVERT[c];
      }
    }
    return val > 1 && (val % 2 == 1);
  }

  /**
   * 判断字符是否只包含数字
   *
   * @param word 待判断的字符串
   * @return 只包含数字返回true
   */
  public static boolean hasOnlyDigit(CharSequence word) {
    return hasOnlyDigit(word, 0, word.length());
  }

  /**
   * 判断字符是否只包含数字
   *
   * @param word  待判断的字符串
   * @param start 起始位置
   * @return 只包含数字返回true
   */
  public static boolean hasOnlyDigit(CharSequence word, int start, int stop) {
    int val = 0;
    for (int i = start; i < stop; i++) {
      char c = word.charAt(i);
      if (c < 127) {
        val |= CONVERT[c];
      }
    }
    return val == 1;
  }

  /**
   * 判断字符是否包含字母
   *
   * @param word 待判断的字符串
   * @return 只包含字母返回true
   */
  public static boolean hasOnlyLetter(CharSequence word) {
    return hasOnlyLetter(word, 0, word.length());
  }

  /**
   * 判断字符是否只包含字母
   *
   * @param word  待判断的字符串
   * @param start 起始位置
   * @return 只包含字母返回true
   */
  public static boolean hasOnlyLetter(CharSequence word, int start, int stop) {
    int val = 0;
    for (int i = start; i < stop; i++) {
      char c = word.charAt(i);
      if (c < 127) {
        val |= CONVERT[c];
      }
    }
    return val == 2 || val == 4;
  }

  /**
   * 判断字符串是否以一个字符串开头
   *
   * @param sbd   待判断的字符串
   * @param start 起始位置
   * @param str   开头的字符串
   * @return 找到返回true
   */
  public static boolean startsWith(StringBuilder sbd, int start, String str) {
    final int length = str.length();
    if (start + length > sbd.length()) {
      return false;
    }
    for (int i = 0; i < length; i++) {
      if (sbd.charAt(i + start) != str.charAt(i)) {
        return false;
      }
    }
    return true;
  }

  /**
   * 只替换第一个
   *
   * @param s   原始字符串
   * @param src 要替换字符串
   * @param dst 替换目标字符串
   * @return 处理后的字符串
   */
  public static String replaceFirst(String s, String src, String dst) {
    if (paramsNull(s, src, dst)) {
      return s;
    }
    int pos = s.indexOf(src);
    if (pos < 0) {
      return s;
    }
    StringBuilder sb = new StringBuilder(s.length() - src.length() + dst.length());
    sb.append(s, 0, pos);
    sb.append(dst);
    sb.append(s, pos + src.length(), s.length());
    return sb.toString();
  }

  private static boolean paramsNull(String s, String src, String dst){
    return s == null || src == null || dst == null || src.length() == 0;
  }

  /**
   * 字符串全量替换
   *
   * @param s   原始字符串
   * @param src 要替换的字符串
   * @param dst 替换目标字符串
   * @return 处理后的字符串
   */
  public static String replaceAll(String s, String src, String dst) {
    if (paramsNull(s, src, dst)) {
      return s;
    }
    int pos = s.indexOf(src); // 查找第一个替换的位置
    if (pos < 0) {
      return s;
    }
    int capacity = dst.length() > src.length() ? s.length() * 2 : s.length();
    StringBuilder sb = new StringBuilder(capacity);
    int written = 0;
    for (; pos >= 0; ) {
      sb.append(s, written, pos); // append 原字符串不需替换部分
      sb.append(dst); // append 新字符串
      written = pos + src.length(); // 忽略原字符串需要替换部分
      pos = s.indexOf(src, written); // 查找下一个替换位置
    }
    sb.append(s, written, s.length()); // 替换剩下的原字符串
    return sb.toString();
  }

  public static String removeAll(String s, String src) {
    return replaceAll(s, src, "");
  }

  /**
   * 去除字符串头尾空格，并不会去掉[\t\r\n]等空白符
   *
   * @param s 待检查的字符串
   * @return 如果为空指针，则返回空串，否则返回去掉头尾空格后的串
   */
  public static String trim(String s) {
    if (s == null) {
      return "";
    }
    return s.trim();
  }

  /**
   * 对StringBuilder做trim操作
   *
   * @param sb 待处理的StringBuilder实例
   * @return 去掉头尾空格后的字符串
   */
  public static String trim(StringBuilder sb) {
    if (sb == null) {
      return "";
    }
    int len = sb.length();
    int st = 0;
    while ((st < len) && (sb.charAt(st) <= ' ')) {
      st++;
    }
    while ((st < len) && (sb.charAt(len - 1) <= ' ')) {
      len--;
    }
    return ((st > 0) || (len < sb.length())) ? sb.substring(st, len) : sb.toString();
  }

  /**
   * 分割字符串
   *
   * @param line      原始字符串
   * @param separator 分隔符
   * @return 分割结果
   */
  public static List<String> split(String line, String separator) {
    return split(line, separator, false);
  }

  /**
   * 分割字符串
   *
   * @param line      原始字符串
   * @param separator 分隔符
   * @param trimSpace 是否去除每项的头尾空格
   * @return 分割结果
   */
  public static List<String> split(String line, String separator, boolean trimSpace) {
		if (line == null || separator == null || separator.length() == 0) {
			return null;
		}
    ArrayList<String> list = new ArrayList<String>();
    int pos1 = 0;
    int pos2;
    for (; ; ) {
      pos2 = line.indexOf(separator, pos1);
      if (pos2 < 0) {
        String s = line.substring(pos1);
        list.add(trimSpace ? s.trim() : s);
        break;
      }
      String s = line.substring(pos1, pos2);
      list.add(trimSpace ? s.trim() : s);
      pos1 = pos2 + separator.length();
    }
    // 去掉末尾的空串，和String.split行为保持一致
    for (int i = list.size() - 1; i >= 0 && list.get(i).length() == 0; --i) {
      list.remove(i);
    }
    return list;
  }

  /**
   * 用特定字符分割一个字符串，并且把分割后的字符串做trim，避免每个再做trim
   *
   * @param str        要分割的字符串
   * @param separator  分割字符， 避免忘记填同时也能提升性能
   * @param separators 更多分割字符
   * @return 分割后的字符串列表
   */
  public static List<String> split(String str, char separator, char... separators) {
    return split(str, 0, str.length(), separator, separators);
  }

  public static List<String> split(String str, int start, int stop, char separator,
      char... separators) {
    List<String> items = new ArrayList<String>();
    StringBuilder sb = new StringBuilder();
    for (int i = start; i < stop; i++) {
      char c = str.charAt(i);
      if (c == separator || contains(separators, c)) {
        items.add(trim(sb));
        sb.setLength(0);
      } else {
        sb.append(c);
      }
    }
    items.add(trim(sb));
    // 去掉末尾的空串，和String.split行为保持一致
    for (int i = items.size() - 1; i >= 0 && items.get(i).length() == 0; --i) {
      items.remove(i);
    }
    return items;
  }

  public static String[] splitLines(String str) {
    return splitLines(str, false);
  }

  public static String[] splitLines(String str, boolean removeEmpty) {
		if (str == null) {
			return null;
		}

    String[] array = str.split("\r\n|\r|\n");
		if (!removeEmpty) {
			return array;
		}

    List<String> trimedList = new ArrayList();
    for (String seg : array) {
			if (!seg.equals("")) {
				trimedList.add(seg);
			}
    }
    return trimedList.toArray(new String[trimedList.size()]);
  }

  /**
   * 把字符串分割成KV对
   *
   * @param str       待分割的字符串
   * @param splitPos 分割位置
   * @return KV串
   */
  public static Pair<String, String> splitKV(String str, int splitPos) {
    if (str == null) {
      return null;
    }
    if(str.length() > splitPos){
      return Pair.build(str.substring(0, splitPos).trim(), str.substring(splitPos).trim());
    }else{
      return Pair.build("", "");
    }
  }

  /**
   * 判断haystack中是否包含字符c
   *
   * @param haystack 从中查找字符
   * @param c        要查找的字符
   * @return 找到返回true
   */
  public static boolean contains(char[] haystack, char c) {
    for (char i : haystack) {
      if (i == c) {
        return true;
      }
    }
    return false;
  }

  /**
   * 将字符串截断到maxLen长度
   *
   * @param s      待截断字符串
   * @param maxLen 字符串最大长度
   * @return 截断后的字符串
   */
  public static String truncate(String s, int maxLen) {
    return truncate(s, maxLen, null);
  }

  /**
   * 把字符串截断到maxLen长度并以tail结尾
   *
   * @param s       待处理字符串
   * @param maxLen  截断长度
   * @param postfix 截断后缀
   * @return 截取后的字符串
   */
  public static String truncate(String s, int maxLen, String postfix) {
    if (s == null || maxLen < 0) {
      return "";
    }
    if (s.length() <= maxLen) {
      return s;
    }
    if (postfix == null || postfix.length() == 0) {
      return s.substring(0, maxLen);
    } else {
      final int end = maxLen - postfix.length();
      if (end < 0) {
        return s.substring(0, maxLen);
      }
      return s.substring(0, end) + postfix;
    }
  }

  /**
   * 获取字符型参数，若输入字符串为null，则返回设定的默认值
   *
   * @param str      输入字符串
   * @param defaults 默认值
   * @return 字符串参数
   */
  public static String convertString(String str, String defaults) {
    if (str == null) {
      return defaults;
    } else {
      return str;
    }
  }

  /**
   * 获取int参数，若输入字符串为null或不能转为int，则返回设定的默认值
   *
   * @param str      输入字符串
   * @param defaults 默认值
   * @return int参数
   */
  public static int convertInt(String str, int defaults) {
    if (str == null) {
      return defaults;
    }
    try {
      return Integer.parseInt(str);
    } catch (Exception e) {
      return defaults;
    }
  }

  /**
   * 获取long型参数，若输入字符串为null或不能转为long，则返回设定的默认值
   *
   * @param str      输入字符串
   * @param defaults 默认值
   * @return long参数
   */
  public static long convertLong(String str, long defaults) {
    if (str == null) {
      return defaults;
    }
    try {
      return Long.parseLong(str);
    } catch (Exception e) {
      return defaults;
    }
  }

  /**
   * 获取double型参数，若输入字符串为null或不能转为double，则返回设定的默认值
   *
   * @param str      输入字符串
   * @param defaults 默认值
   * @return double型参数
   */
  public static double convertDouble(String str, double defaults) {
    if (str == null) {
      return defaults;
    }
    try {
      return Double.parseDouble(str);
    } catch (Exception e) {
      return defaults;
    }
  }

  /**
   * 获取short型参数，若输入字符串为null或不能转为short，则返回设定的默认值
   *
   * @param str      输入字符串
   * @param defaults 默认值
   * @return short型参数
   */
  public static short convertShort(String str, short defaults) {
    if (str == null) {
      return defaults;
    }
    try {
      return Short.parseShort(str);
    } catch (Exception e) {
      return defaults;
    }
  }

  /**
   * 获取float型参数，若输入字符串为null或不能转为float，则返回设定的默认值
   *
   * @param str      输入字符串
   * @param defaults 默认值
   * @return float型参数
   */
  public static float convertFloat(String str, float defaults) {
    if (str == null) {
      return defaults;
    }
    try {
      return Float.parseFloat(str);
    } catch (Exception e) {
      return defaults;
    }
  }

  /**
   * 获取boolean型参数，若输入字符串为null或不能转为boolean，则返回设定的默认值
   *
   * @param str      输入字符串
   * @param defaults 默认值
   * @return boolean型参数
   */
  public static boolean convertBoolean(String str, boolean defaults) {
    if (str == null) {
      return defaults;
    }
    try {
      return Boolean.parseBoolean(str);
    } catch (Exception e) {
      return defaults;
    }
  }

  public static String join(String separator, String... s) {
    return joinObjects(separator, (Object[]) s);
  }

  public static String join(String separator, int... s) {
		if (s == null || s.length == 0) {
			return "";
		}
    StringBuilder sb = new StringBuilder();
    sb.append(s[0]);

    if (separator.length() == 1) {
      char ch = separator.charAt(0);
      for (int i = 1; i < s.length; ++i) {
        sb.append(ch);
        sb.append(s[i]);
      }
    } else {
      for (int i = 1; i < s.length; ++i) {
        sb.append(separator);
        sb.append(s[i]);
      }
    }
    return sb.toString();
  }

  public static String join(String separator, long... s) {
		if (s == null || s.length == 0) {
			return "";
		}
    StringBuilder sb = new StringBuilder();
    sb.append(s[0]);
    if (separator.length() == 1) {
      char ch = separator.charAt(0);
      for (int i = 1; i < s.length; ++i) {
        sb.append(ch);
        sb.append(s[i]);
      }
    } else {
      for (int i = 1; i < s.length; ++i) {
        sb.append(separator);
        sb.append(s[i]);
      }
    }
    return sb.toString();
  }

  public static String joinObjects(String separator, Object... c) {
		if (c == null || c.length == 0) {
			return "";
		}
    StringBuilder sb = new StringBuilder();
    sb.append(c[0] == null ? '-' : c[0]);
    if (separator.length() == 0) {
      for (int i = 1; i < c.length; ++i) {
        appendObject(sb, c[i]);
      }
    } else if (separator.length() == 1) {
      char ch = separator.charAt(0);
      for (int i = 1; i < c.length; ++i) {
        sb.append(ch);
        appendObject(sb, c[i]);
      }
    } else {
      for (int i = 1; i < c.length; ++i) {
        sb.append(separator);
        appendObject(sb, c[i]);
      }
    }
    return sb.toString();
  }

  private static void appendObject(StringBuilder sb, Object o) {
    if (o == null || ((o instanceof String) && ((String) o).length() == 0)) {
      sb.append('-');
    } else {
      sb.append(o.toString());
    }
  }

  /**
   * 过滤SQL字符串,防止SQL inject
   *
   * @param sql
   * @return String
   */
  public static String encodeSQL(String sql) {
    if (sql == null) {
      return "";
    }
    // 不用正则表达式替换，直接通过循环，节省cpu时间
    final int length = sql.length();
    StringBuilder sb = new StringBuilder(length);
    for (int i = 0; i < length; ++i) {
      char c = sql.charAt(i);
      switch (c) {
        case '\\':
          sb.append("\\\\");
          break;
        case '\r':
          sb.append("\\r");
          break;
        case '\n':
          sb.append("\\n");
          break;
        case '\t':
          sb.append("\\t");
          break;
        case '\b':
          sb.append("\\b");
          break;
        case '\'':
          sb.append("\'\'");
          break;
        case '\"':
          sb.append("\\\"");
          break;
        case '\u200B'://ZERO WIDTH SPACE
        case '\uFEFF'://ZERO WIDTH NO-BREAK SPACE
          break;
        default:
          sb.append(c);
      }
    }
    return sb.toString();
  }

  /**
   * 分割字符串
   *
   * @param line      原始字符串
   * @param seperator 分隔符
   * @return 分割结果
   */
  public static String[] split2Array(String line, String seperator) {
    List<String> list = split(line, seperator);
    if (list != null) {
      return list.toArray(new String[0]);
    }
    return null;
  }

  /**
   * 分割字符串，并转换为int
   *
   * @param line      原始字符串
   * @param seperator 分隔符
   * @param def       默认值
   * @return 分割结果
   */
  public static int[] splitInt(String line, String seperator, int def) {
    String[] ss = split2Array(line, seperator);
    int[] r = new int[ss.length];
    for (int i = 0; i < r.length; ++i) {
      r[i] = convertInt(ss[i], def);
    }
    return r;
  }

  public static String utf8Encode(String str) {
    if (StringUtil.isEmpty(str)) {
      return "";
    } else {
      String r = "";
      try {
        r = URLEncoder.encode(str, "UTF-8");
      } catch (Exception e) {
      }
      return r;
    }
  }
}
