package com.facishare.crm.sfa.prm.platform.infrastructure.statemachine.core;

import com.facishare.crm.sfa.prm.platform.infrastructure.statemachine.exception.StateNotFoundException;
import com.facishare.crm.sfa.prm.platform.infrastructure.statemachine.exception.TransitionNotFoundException;
import com.facishare.crm.sfa.prm.platform.infrastructure.statemachine.transition.StateTransition;

import java.util.Map;
import java.util.concurrent.locks.ReentrantLock;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-04-09
 * ============================================================
 */
public class StateMachine<S, E, C> {
    private final Map<S, Map<E, StateTransition<S, E, C>>> transitions;
    private final ReentrantLock lock = new ReentrantLock();

    public StateMachine(Map<S, Map<E, StateTransition<S, E, C>>> transitions) {
        this.transitions = transitions;
    }

    public S fire(S currentState, E event, C context) {
        Map<E, StateTransition<S, E, C>> stateTransitions = transitions.get(currentState);
        if (stateTransitions == null) {
            throw new StateNotFoundException("No transitions defined for state: " + currentState);
        }
        StateTransition<S, E, C> transition = stateTransitions.get(event);
        if (transition == null) {
            throw new TransitionNotFoundException("No transition defined for event: " + event + " in state: " + currentState);
        }
        lock.lock();
        try {
            transition.getAction().execute(currentState, event, context);
            currentState = transition.getTargetState();
            return currentState;
        } finally {
            lock.unlock();
        }
    }
}
