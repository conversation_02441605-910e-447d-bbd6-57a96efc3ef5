package com.facishare.crm.sfa.prm.api.enums;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import lombok.Getter;

import static com.facishare.crm.sfa.prm.core.constants.PrmI18NConstants.PRM_ENUM_PARAM_TYPE_ERROR;

/**
 * ============================================================
 *
 * @Description: 渠道准入，签约状态
 * @CreatedBy: Sundy on 2025-02-27
 * ============================================================
 */
@Getter
public enum SignStatus {
    /**
     * 待签约
     */
    PENDING_SIGNATURE("pending_signature"),
    /**
     * 已签约
     */
    SIGNED("signed"),
    /**
     * 已驳回
     */
    REJECT("reject"),
    /**
     * 已过期
     */
    INVALID("invalid"),
    /**
     * 已续签
     */
    RENEWAL("renewal"),
    /**
     * 待续约
     */
    PENDING_RENEWAL("pending_renewal"),
    /**
     * 初始化状态
     */
    INIT("init");

    private final String status;

    SignStatus(String status) {
        this.status = status;
    }

    public static SignStatus from(String status) {
        for (SignStatus e : values()) {
            if (e.status.equals(status)) {
                return e;
            }
        }
        throw new ValidateException(I18N.text(PRM_ENUM_PARAM_TYPE_ERROR, status));
    }

    public static SignStatus find(String status) {
        return find(status, null);
    }

    public static SignStatus find(String status, SignStatus defaultValue) {
        for (SignStatus e : values()) {
            if (e.status.equals(status)) {
                return e;
            }
        }
        return defaultValue;
    }
}
