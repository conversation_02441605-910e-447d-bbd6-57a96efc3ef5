package com.facishare.crm.sfa.prm.rest.client;

import com.facishare.crm.sfa.prm.rest.model.HeaderName;
import com.facishare.crm.sfa.prm.rest.model.TemplateModel;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderParam;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-03-17
 * ============================================================
 */
@RestResource(value = "CRMTemplate", desc = "打印模版服务", contentType = "application/json")
public interface EmailTemplateClient {
    @POST(value = "/v1/email/fillEmail", desc = "填充邮箱模版")
    TemplateModel.Result fetchEmailInfoFromTemplate(@HeaderParam(HeaderName.X_FS_EI) String tenantId,
                                                    @HeaderParam(HeaderName.X_FS_USER_INFO) String userId,
                                                    @Body TemplateModel.Arg arg);
}
