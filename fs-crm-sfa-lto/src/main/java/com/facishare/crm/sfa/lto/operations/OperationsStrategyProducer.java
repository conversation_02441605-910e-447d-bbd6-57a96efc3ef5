package com.facishare.crm.sfa.lto.operations;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.lto.operations.task.execution.TaskExecutionFrequency;
import com.facishare.crm.sfa.lto.operations.task.execution.TaskExecutionUtil;
import com.facishare.crm.sfa.lto.utils.GrayUtil;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.Tenantable;
import com.fxiaoke.paas.gnomon.api.NomonProducer;
import com.fxiaoke.paas.gnomon.api.entity.NomonDeleteMessage;
import com.fxiaoke.paas.gnomon.api.entity.NomonMessage;
import com.github.autoconf.ConfigFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Optional;
import java.util.TimeZone;

@Component
@Slf4j
public class OperationsStrategyProducer implements InitializingBean, ApplicationListener<ContextRefreshedEvent> {
    public static final String TAGS = "fs-crm-sfa-operations-instance-create";
    private LocalTime localTime = LocalTime.of(2, 0);
    private final NomonProducer nomonProducer;

    public OperationsStrategyProducer(NomonProducer nomonProducer) {
        this.nomonProducer = nomonProducer;
    }

    @Override
    public void afterPropertiesSet() {
        init();
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        init();
    }

    public void sendEnableMessage(IObjectData strategy, TimeZone timeZone) {
        Optional<TaskExecutionFrequency> frequency = TaskExecutionFrequency.from(strategy.get(OperationsConstant.TASK_EXECUTION_FREQUENCY, String.class));
        if (frequency.isPresent()) {
            sendEnableMessage(strategy, frequency.get(), timeZone, new Date());
        } else {
            log.error("sendEnableMessage: T={}, ID={}, frequency is not present", strategy.getTenantId(), strategy.getId());
        }
    }

    public void sendEnableMessage(IObjectData strategy, TaskExecutionFrequency frequency, TimeZone timeZone, Date after) {
        String tenantId = strategy.getTenantId();
        String dataId = strategy.getId();
        if (tenantId == null || dataId == null) {
            log.error("sendEnableMessage: tenantId or dataId is null, T={}, ID={}", tenantId, dataId);
            return;
        }
        Date createDate = nextCreateTimeAfter(strategy, frequency, after);
        if (createDate == null) {
            log.error("sendEnableMessage: get next create time failed after={}", after);
            return;
        }
        Date executeTime;
        if (GrayUtil.isGrayEnable(OperationsGrayKey.OPERATIONS_INSTANCE_CREATE_IMMEDIATELY, tenantId)) {
            executeTime = new Date();
        } else {
            executeTime = nextExecuteTimeAfter(strategy, frequency, timeZone, after);
        }
        long createTime = createDate.getTime();
        log.info("sendEnableMessage: T={}, ID={}, CT={}, ET={}", tenantId, dataId, createTime, executeTime);
        upsertMessage(tenantId, dataId, executeTime, buildCallArg(tenantId, dataId, createTime));
    }

    // **WARNING** 不要把立即启用的逻辑加到这个方法里
    public void resendEnableMessage(IObjectData strategy, TaskExecutionFrequency frequency, TimeZone timeZone, Date after) {
        String tenantId = strategy.getTenantId();
        String dataId = strategy.getId();
        Date createDate = nextCreateTimeAfter(strategy, frequency, after);
        if (createDate == null) {
            log.error("resendEnableMessage: get next create time failed after={}", after);
            return;
        }
        long createTime = createDate.getTime();
        Date executeTime = nextExecuteTimeAfter(strategy, frequency, timeZone, after);
        log.info("resendEnableMessage: T={}, ID={}, CT={}, ET={}", tenantId, dataId, createTime, executeTime);
        upsertMessage(tenantId, dataId, executeTime, buildCallArg(tenantId, dataId, createTime));
    }

    private static String buildCallArg(String tenantId, String dataId, long createTime) {
        JSONObject callArg = new JSONObject();
        callArg.put(Tenantable.TENANT_ID, tenantId);
        callArg.put(OperationsConstant.OPERATIONS_STRATEGY_ID, dataId);
        callArg.put(DBRecord.CREATE_TIME, createTime);
        return callArg.toJSONString();
    }

    public Optional<Pair<Date, Date>> nextInfoAfter(IObjectData strategy, TimeZone timeZone, Date after) {
        Optional<TaskExecutionFrequency> optional = TaskExecutionFrequency.from(strategy.get(OperationsConstant.TASK_EXECUTION_FREQUENCY, String.class));
        if (optional.isPresent()) {
            TaskExecutionFrequency frequency = optional.get();
            Date executeTime = nextExecuteTimeAfter(strategy, frequency, timeZone, after);
            if (executeTime == null || (TaskExecutionFrequency.REBOOT == frequency && after.after(executeTime))) {
                return Optional.empty();
            }
            Date createTime = nextCreateTimeAfter(strategy, frequency, after);
            return Optional.of(new ImmutablePair<>(executeTime, createTime));
        }
        return Optional.empty();
    }

    private Date nextExecuteTimeAfter(IObjectData strategy, TaskExecutionFrequency frequency, TimeZone timeZone, Date after) {
        String exp = strategy.get(OperationsConstant.TASK_EXECUTION_TIME, String.class);
        return TaskExecutionUtil.getNextTimeAfter(frequency, exp, localTime, timeZone, after);
    }

    /**
     * 此方法用作提供批次号，固定执行时间 00:00:00，固定时区 GMT+8
     *
     * @param strategy  策略
     * @param frequency 执行频率
     * @param after     参照时间
     * @return 在参照时间之后下一次的执行时间
     */
    private Date nextCreateTimeAfter(IObjectData strategy, TaskExecutionFrequency frequency, Date after) {
        String exp = strategy.get(OperationsConstant.TASK_EXECUTION_TIME, String.class);
        return TaskExecutionUtil.getNextTimeAfter(frequency, exp, LocalTime.of(0, 0), TimeZone.getTimeZone("GMT+8"), after);
    }

    private void init() {
        ConfigFactory.getConfig("fs-gray-sfa", iConfig -> {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
            localTime = LocalTime.from(formatter.parse(iConfig.get(OperationsGrayKey.OPERATIONS_INSTANCE_CREATE_LOCAL_TIME, "02:00")));
        });
    }

    public void sendDisableMessage(IObjectData operationsStrategy) {
        String tenantId = operationsStrategy.getTenantId();
        String dataId = operationsStrategy.getId();
        if (tenantId == null || dataId == null) {
            log.error("sendDisableMessage: tenantId or dataId is null, T={}, ID={}", tenantId, dataId);
            return;
        }
        log.info("sendDisableMessage: T={}, ID={}", tenantId, dataId);
        deleteMessage(tenantId, dataId);
    }

    private void upsertMessage(String tenantId, String dataId, Date executeTime, String callArg) {
        NomonMessage message = NomonMessage
                .builder()
                .biz(TAGS)
                .tenantId(tenantId)
                .dataId(dataId)
                .executeTime(executeTime)
                .callArg(String.format(callArg, tenantId, dataId))
                .build();
        nomonProducer.send(message);
    }

    private void deleteMessage(String tenantId, String dataId) {
        NomonDeleteMessage message = NomonDeleteMessage
                .builder()
                .biz(TAGS)
                .tenantId(tenantId)
                .dataId(dataId)
                .build();
        nomonProducer.send(message);
    }
}