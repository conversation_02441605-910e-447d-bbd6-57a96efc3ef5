package com.facishare.crm.sfa.lto.quality.models;

/**
 * <AUTHOR>
 * @Date 2023/02/14 17:44
 * @Version 1.0
 **/
public interface QualityInspectionConstants {
    class QualityInspectionConf {
        public static final String TABLE_QUALITY_INSPECTION_RULE = "biz_quality_inspection_rule";
        public static final String TABLE_QUALITY_INSPECTION = "biz_quality_inspection";
        public static final String OBJ_QUALITY_INSPECTION_RULE = "QualityInspectionRuleObj";
        public static final String OBJ_QUALITY_INSPECTION = "QualityInspectionObj";

        public static final String QI_TYPE= "type";  //质检类型：敏感词，会话特征，行为质检
        public static final String QI_MONITORS = "monitors";  //监听成员
        public static final String QI_DIRTY_WORDS = "dirty_words"; //敏感词json
        public static final String QI_SESSION_FEATURE = "session_feature"; //会话特征：超时回复
        public static final String QI_SESSION_FEATURE_RULE = "session_feature_rule"; //规则：回复时长
        public static final String QI_SESSION_ACTION = "session_action";  //行为类型：删除客户，被客户删除
        public static final String QI_TARGET_TYPE = "target_type"; //质检对象：企微客户，员工
        public static final String QI_SESSION_TYPE = "session_type"; //会话场景：单聊，群聊
        public static final String QI_MSG_PUSH = "msg_push";  //消息通知开启(选中)，扩展定时设置
        public static final String QI_MSG_USER = "msg_user";  //消息接收人
    }

    class CommonConstants {
        public static final String _ID = "_id";
        public static final String ID = "id";
        public static final String NAME = "name";
        public static final String IS_DELETED = "is_deleted";
        public static final String TENANT_ID = "tenant_id";
        public static final String CREATE_TIME = "create_time";
        public static final String CREATED_BY = "created_by";
        public static final String LAST_MODIFIED_TIME = "last_modified_time";
        public static final String OWNER = "owner";
        public static final String LAST_MODIFIED_BY = "last_modified_by";
        public static final String OBJECT_DESCRIBE_API_NAME = "object_describe_api_name";
    }

    class RuleMember {
        public static final String TABLE_NAME = "biz_quality_inspection_rule_member";
        public static final String MEMBER_ID = "member_id";
        public static final String MEMBER_TYPE = "member_type";
        public static final String M_TYPE = "mtype";
        public static final String RULE_ID = "rule_id";
    }
}
