package com.facishare.crm.sfa.lto.integral.core.service;

import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;

/**
 * 行为动作服务类
 * <AUTHOR>
 */
public interface BehaviorActionService {
    IObjectData getBehaviorActionByApiName(String tenantId, String categoryApiName, String actionApiName);

    IObjectData getBehaviorActionByLabel(String tenantId, String categoryApiName, String actionLabel);

    List<IObjectData> getBehaviorActionList(String tenantId);
}
