package com.facishare.crm.sfa.lto.utils;

import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class RichTextUtil {
    public static IObjectData buildObjectData2(String fieldName, String text, List<Object> contentList) {
        if (Strings.isNullOrEmpty(fieldName) || CollectionUtils.isEmpty(contentList)) {
            return null;
        }

        Map<String, Object> doc = Maps.newHashMap();
        doc.put("type", "doc");
        doc.put("content", contentList);

        Map<String, Object> json = Maps.newHashMap();
        json.put("__json", doc);

        Map<String, Object> map = Maps.newHashMap();
        map.put("__xt", json);
        map.put("text", text);

        IObjectData objectData = new ObjectData();
        objectData.set(fieldName, map);
        return objectData;
    }

    public static IObjectData buildObjectData(String fieldName, String text, List<Object> marks) {
        if (Strings.isNullOrEmpty(fieldName) || Strings.isNullOrEmpty(text)) {
            return null;
        }

        IObjectData objectData = new ObjectData();
        objectData.set(fieldName, buildCompleteRich(text,marks));
        return objectData;
    }

    public static Map<String, Object> buildCompleteRich(String text, List<Object> marks){
        List<Object> contentList = Lists.newArrayList();
        contentList.add(buildContent(text, marks));

        Map<String, Object> doc = Maps.newHashMap();
        doc.put("type", "doc");
        doc.put("content", contentList);

        Map<String, Object> json = Maps.newHashMap();
        json.put("__json", doc);

        Map<String, Object> map = Maps.newHashMap();
        map.put("__xt", json);
        map.put("text", text);
        return map;
    }

    public static List<Object> buildMarks(String typeValue) {
        if (Strings.isNullOrEmpty(typeValue)) {
            return null;
        }

        Map<String, Object> type = new HashMap<>();
        type.put("type", typeValue);

        List<Object> marks = Lists.newArrayList();
        marks.add(type);
        return marks;
    }

    public static Map<String, Object> buildContent(String text, List<Object> marks) {
        Map<String, Object> contentMap = new HashMap<>();
        contentMap.put("type", "text");
        contentMap.put("text", text);

        if (!CollectionUtils.isEmpty(marks)) {
            contentMap.put("marks", marks);
        }

        List<Object> contentList = Lists.newArrayList();
        contentList.add(contentMap);

        Map<String, Object> attrs = new HashMap<>();
        attrs.put("textAlign", "left");

        Map<String, Object> paragraph = new HashMap<>();
        paragraph.put("type", "paragraph");
        paragraph.put("attrs", attrs);
        paragraph.put("content", contentList);
        return paragraph;
    }
}
