package com.facishare.crm.sfa.lto.qywx;

import com.facishare.crm.sfa.lto.qywx.proxy.models.GroupDetailModel;
import com.facishare.crm.sfa.lto.qywx.proxy.models.QueryContactModel;
import com.facishare.crm.sfa.lto.utils.Safes;

import java.util.Map;
import java.util.Optional;

public class WecomUtils {


    public static GroupDetailModel.ChatDetail fillCRMIDForChatDetail(Map<String, String> employeeIdMap, GroupDetailModel.QueryResult detailResult) {
        GroupDetailModel.ChatDetail chatDetail = detailResult.getData();
        if (chatDetail != null && chatDetail.getGroup_chat() != null) {
            chatDetail.getGroup_chat().setFxUserId(employeeIdMap.get(chatDetail.getGroup_chat().getOwner()));
            for (GroupDetailModel.MemberList member : Safes.of(chatDetail.getGroup_chat().getMember_list())) {
                member.setFxUserId(employeeIdMap.get(member.getUserid()));
                Optional.ofNullable(member.getInvitor())
                        .ifPresent(i -> i.setFxUserId(employeeIdMap.get(i.getUserid())));
            }
            Safes.of(chatDetail.getGroup_chat().getAdmin_list())
                    .forEach(admin -> admin.setFxUserId(employeeIdMap.get(admin.getUserid())));
        }
        return chatDetail;
    }

    public static QueryContactModel.ContactInfoResult fillCRMIDForContactInfo(Map<String, String> employeeIdMap, QueryContactModel.QueryResult queryDetailResult) {
        QueryContactModel.ContactInfoResult contactInfoResult = queryDetailResult.getData();
        if (Safes.isNotEmpty(contactInfoResult.getFollow_user())) {
            for (QueryContactModel.FollowUser followUser : contactInfoResult.getFollow_user()) {
                followUser.setFxUserId(employeeIdMap.get(followUser.getUserid()));
            }
        }
        return contactInfoResult;
    }


}
