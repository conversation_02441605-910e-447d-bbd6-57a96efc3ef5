package com.facishare.crm.sfa.prm.api.enums;

import com.facishare.paas.appframework.core.exception.ValidateException;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-03-11
 * ============================================================
 */
public enum NotificationType {
    /**
     * 邮件
     */
    EMAIL("email"),
    /**
     * 短信
     */
    SMS("sms"),
    /**
     * CRM 提醒
     */
    CRM_NOTICE("crm"),
    /**
     * 企信通知
     */
    QI_XIN("qi_xin"),
    /**
     * 代理通下游弹窗通知
     */
    PRM_ALERT("prm_alert"),
    /**
     * 代理通 CRM 通知
     */
    PRM_CRM("prm_crm");


    private final String type;

    NotificationType(String type) {
        this.type = type;
    }

    public static NotificationType of(String type) {
        for (NotificationType e : values()) {
            if (e.type.equals(type)) {
                return e;
            }
        }
        throw new ValidateException("parse MessageTypeEnums error");
    }
}
