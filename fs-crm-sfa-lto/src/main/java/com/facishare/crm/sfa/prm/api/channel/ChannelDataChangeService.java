package com.facishare.crm.sfa.prm.api.channel;

import com.facishare.crm.sfa.prm.api.enums.SignStatus;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;


/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-02-27
 * ============================================================
 */
public interface ChannelDataChangeService {

    IObjectData changeSignStatus(User user, IObjectData objectData, SignStatus signStatus);
    IObjectData renewExpiredTime(User user, String objectApiName, IObjectData objectData, Long expiredTimestamp);
}
