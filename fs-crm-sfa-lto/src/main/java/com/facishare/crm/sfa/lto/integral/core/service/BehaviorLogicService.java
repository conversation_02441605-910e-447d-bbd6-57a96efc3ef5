package com.facishare.crm.sfa.lto.integral.core.service;

import com.facishare.crm.sfa.lto.integral.core.service.dto.FindBehaviorList;
import com.facishare.crm.sfa.lto.integral.core.service.dto.RegisterAction;
import com.facishare.crm.sfa.lto.integral.core.service.dto.RegisterCategory;
import com.facishare.crm.sfa.lto.integral.core.service.dto.RegisterMaterial;
import com.facishare.crm.sfa.lto.integral.core.service.dto.RemoveAction;
import com.facishare.crm.sfa.lto.integral.core.service.dto.RemoveCategory;
import com.facishare.crm.sfa.lto.integral.core.service.dto.RemoveMaterial;
import com.facishare.paas.appframework.core.model.RequestContext;

public interface BehaviorLogicService {

    /**
     * 获取行为分类列表
     * @param tenantId
     * @return
     */
    FindBehaviorList.Result findBehaviorList(String tenantId);

    /**
     * 注册行为分类
     * @param arg
     * @param context
     * @return
     */
    RegisterCategory.Result registerCategory(RegisterCategory.Arg arg, RequestContext context);

    /**
     * 解除行为分类
     * @param arg
     * @param context
     * @return
     */
    RemoveCategory.Result removeCategory(RemoveCategory.Arg arg, RequestContext context);

    /**
     * 注册行为动作
     * @param arg
     * @param context
     * @return
     */
    RegisterAction.Result registerAction(RegisterAction.Arg arg, RequestContext context);

    /**
     * 解除行为动作
     * @param arg
     * @param context
     * @return
     */
    RemoveAction.Result removeAction(RemoveAction.Arg arg, RequestContext context);

    /**
     * 注册物料
     * @param arg
     * @param context
     * @return
     */
    RegisterMaterial.Result registerMaterial(RegisterMaterial.Arg arg, RequestContext context);

    /**
     * 解除物料
     * @param arg
     * @param context
     * @return
     */
    RemoveMaterial.Result removeMaterial(RemoveMaterial.Arg arg, RequestContext context);
}
