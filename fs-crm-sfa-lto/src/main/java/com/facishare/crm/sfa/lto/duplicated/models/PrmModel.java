package com.facishare.crm.sfa.lto.duplicated.models;

import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/3/10 10:47
 */
public interface PrmModel {
    @Data
    class MatchOutOwnerArg {
        private String objectApiName;
        private List<String> objectDataIds;
    }

    @Data
    class MatchOutOwnerResult {
        private Boolean allMatch;
        private List<String> notMatchIds;
    }

    @Data
    class OutOwnerResult{
        List<OutOwner> outOwners;
    }

    @Data
    class OutOwner {
        private String objectDataId;
        /**
         * 下游企业外部账号
         */
        private Integer downstreamOuterTenantId;
        /**
         * 下游企业对接负责人外部Id
         */
        private Long relationOwnerOuterUid;

        public OutOwner(String objectDataId, Integer downstreamOuterTenantId, Long relationOwnerOuterUid) {
            this.objectDataId = objectDataId;
            this.downstreamOuterTenantId = downstreamOuterTenantId;
            this.relationOwnerOuterUid = relationOwnerOuterUid;
        }
    }
}
