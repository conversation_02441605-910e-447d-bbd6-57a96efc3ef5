package com.facishare.crm.sfa.lto.utils;

import com.facishare.crm.sfa.lto.accountreerelation.AccountTreeRelationLogService;
import com.facishare.crm.sfa.lto.accountreerelation.models.EquityRelationModels;
import com.facishare.crm.sfa.lto.equityrelationship.model.EquityRelationshipDataModel;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ObjectDefNotFoundError;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseImportAction;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.payment.dto.PaymentRecord;
import com.facishare.paas.license.arg.JudgeModuleArg;
import com.facishare.paas.license.common.LicenseContext;
import com.facishare.paas.license.common.Result;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.paas.license.pojo.JudgeModulePojo;
import com.facishare.paas.license.pojo.ModuleFlag;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.JsonCompatible;
import com.facishare.paas.metadata.api.action.ActionContextKey;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.IGroupByParameter;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.GroupByParameter;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.lto.accountreerelation.models.AccountTreeRelationConstants.*;

@Slf4j
public class AccountTreeRelationUtil {

    private static final String API_NAME = ACCOUNT_TREE_RELATION_API_NAME;
    private static final String PARENT_REF_PREFIX = "parent_";
    private static final String ROOT_REF_PREFIX = "root_";
    private static final int ROLLUP_LIMIT = 100;

    private AccountTreeRelationUtil() {

    }

    public static boolean hasLicense(String tenantId, LicenseClient licenseClient) {
        JudgeModuleArg arg = new JudgeModuleArg();
        LicenseContext licenseContext = new LicenseContext();
        licenseContext.setAppId("CRM");
        licenseContext.setTenantId(tenantId);
        licenseContext.setUserId("-10000");
        arg.setContext(licenseContext);
        arg.setModuleCodes(Lists.newArrayList("account_tree_app"));
        @SuppressWarnings("unchecked")
        Result<JudgeModulePojo> result = licenseClient.judgeModule(arg);
        return Optional.of(result).map(Result::getResult).map(JudgeModulePojo::getModuleFlags).map(flags -> flags.get(0)).map(ModuleFlag::isFlag).orElse(false);
    }

    public static List<IObjectData> beforeInsert(User user, List<IObjectData> dataList, IObjectDescribe describe, ServiceFacade serviceFacade) {
        return beforeInsert(user, dataList, null, describe, serviceFacade);
    }

    /**
     * 前置处理传参，后置更新{@link #updateTreeFields}
     *
     * @param user          user
     * @param dataList      dataList
     * @param serviceFacade serviceFacade
     */
    public static List<IObjectData> beforeInsert(User user, List<IObjectData> dataList, List<BaseImportAction.ImportError> errorList, IObjectDescribe describe, ServiceFacade serviceFacade) {
        serviceFacade.fillObjectDataWithRefObject(describe, dataList, user);
        Set<String> parentIds = new HashSet<>();
        for (IObjectData data : dataList) {
            if (data.getName() == null) {
                data.setName(data.get(FieldDescribeExt.getLookupNameByFieldName(ENTERPRISE_ID), String.class));
            }
            String parentId = data.get(PARENT_ID, String.class);
            if (StringUtils.isEmpty(parentId)) {
                setRoot(data);
                data.set(ROOT_ID, null); // **WARNING** 不要set自关联下的root_id
            } else {
                parentIds.add(parentId);
            }
        }
        setParent(user, dataList, parentIds, serviceFacade);
        validate(dataList, errorList);
        List<IObjectData> updateDataList = new ArrayList<>();
        for (IObjectData data : dataList) {
            if (data.get(ROOT_ID) == null) {
                IObjectData update = ObjectDataExt.of(data).copy();
                update.set(ROOT_ID, data.getId()); // root_id在action里的after赋值->自关联
                updateDataList.add(update);
            }
        }
        return updateDataList;
    }

    public static AfterUpdate beforeUpdate(User user, List<IObjectData> dataList, List<IObjectData> preList, ServiceFacade serviceFacade) {
        return beforeUpdate(user, dataList, preList, null, serviceFacade);
    }

    /**
     * 前置处理传参，后置更新{@link #updateTreeFields}
     *
     * @param user          user
     * @param dataList      dataList
     * @param preList       preList
     * @param serviceFacade serviceFacade
     */
    public static AfterUpdate beforeUpdate(User user, List<IObjectData> dataList, List<IObjectData> preList, List<BaseImportAction.ImportError> errorList, ServiceFacade serviceFacade) {
        AfterUpdate afterUpdate = new AfterUpdate();
        Set<String> parentIds = new HashSet<>();
        Map<String, IObjectData> preMap = preList.stream().collect(Collectors.toMap(IObjectData::getId, Function.identity()));
        for (IObjectData data : dataList) {
            String parentId = data.get(PARENT_ID, String.class);
            if (StringUtils.isEmpty(parentId)) {
                setRoot(data);
            } else {
                parentIds.add(parentId);
            }
        }
        setParent(user, dataList, parentIds, serviceFacade);
        validate(dataList, errorList);
        List<Updated> updatedList = new ArrayList<>();
        Set<String> preParentIds = new HashSet<>();
        for (IObjectData data : dataList) {
            IObjectData pre = preMap.get(data.getId());
            updatedList.add(new Updated(data, pre));
            String preParentId = pre.get(PARENT_ID, String.class);
            if (preParentId != null) {
                preParentIds.add(preParentId);
            }
        }
        afterUpdate.setPreParentIds(preParentIds);
        afterUpdate.setUpdatedList(updatedList);
        return afterUpdate;
    }

    public static void validate(List<IObjectData> dataList, List<BaseImportAction.ImportError> errorList) {
        for (int i = dataList.size() - 1; i >= 0; i--) {
            IObjectData data = dataList.get(i);
            try {
                validateTreePath(data);
            } catch (ValidateException e) {
                if (errorList != null) {
                    dataList.remove(i);
                    errorList.add(new BaseImportAction.ImportError(data.get("row_no", Integer.class), e.getMessage()));
                } else {
                    throw e;
                }
            }
        }
    }

    private static void validateTreePath(IObjectData data) {
        String treePath = data.get(TREE_PATH, String.class);
        String id = data.getId();
        if (treePath.indexOf(id) != treePath.lastIndexOf(id)) {
            throw new ValidateException(I18N.text(SfaLtoI18NKeyUtil.SFA_MARKETING_EVENT_CHILDISFATHER));
        }
    }

    public static void afterUpdateAsync(User user, AfterUpdate afterUpdate, ServiceFacade serviceFacade, IObjectDataService objectDataService) {
        ParallelUtils.createBackgroundTask().submit(() -> afterUpdate(user, afterUpdate, serviceFacade, objectDataService)).run();
    }

    @SuppressWarnings("unused")
    public static void afterUpdate(User user, AfterUpdate afterUpdate, ServiceFacade serviceFacade, IObjectDataService objectDataService) {
        try {
            if (afterUpdate == null) {
                return;
            }
            if (afterUpdate.getUpdatingList() != null) {
                serviceFacade.batchUpdateByFields(user, afterUpdate.getUpdatingList(), treeFields());
            }
            if (afterUpdate.getUpdatedList() != null) {
                updateTreePathAndRef(user, afterUpdate, serviceFacade);
            }
//            if (afterUpdate.getPreParentIds() != null) {
//                List<IObjectData> updateIsLeafList = AccountTreeRelationUtil.findRelevantIsLeafUpdating(user, afterUpdate.getPreParentIds(), objectDataService);
//                serviceFacade.batchUpdateByFields(user, updateIsLeafList, Lists.newArrayList(IS_LEAF));
//            }
        } catch (Exception e) {
            log.error("After update error", e);
        }
    }

    public static void afterAccountMainDataDeleted(User user, List<String> accountMainDataIds, ServiceFacade serviceFacade) {
        try {
            int offset = 0;
            List<IObjectData> updated = updateAfterAccountMainDataDeleted(user, accountMainDataIds, offset, serviceFacade);
            while (updated.size() == ROLLUP_LIMIT) {
                offset += ROLLUP_LIMIT;
                updated = updateAfterAccountMainDataDeleted(user, accountMainDataIds, offset, serviceFacade);
            }
        } catch (ObjectDefNotFoundError e) {
            log.warn("After account main data deleted failed: object def not found");
        } catch (Exception e) {
            log.error("After account main data deleted error", e);
        }
    }

    private static List<IObjectData> updateAfterAccountMainDataDeleted(User user, List<String> ids, int offset, ServiceFacade serviceFacade) {
        List<IObjectData> nodes = rollupNodeByAccountMainDataIds(user, ids, offset, serviceFacade);
        if (!nodes.isEmpty()) {
            Map<String, IObjectData> diffRefMap = new HashMap<>();
            for (IObjectData node : nodes) {
                node.set(ACCOUNT_MAIN_DATA_ID, null);
                if (Objects.equals(node.getId(), node.get(ROOT_ID))) {
                    node.set(ROOT_ACCOUNT_MAIN_DATA_ID, null);
                }
                diffRefMap.put(node.getId(), node);
            }
            serviceFacade.batchUpdateByFields(user, nodes, Collections.singletonList(ACCOUNT_MAIN_DATA_ID));
            updateRef(user, diffRefMap, PARENT_REF_PREFIX, null, serviceFacade);
            updateRef(user, diffRefMap, ROOT_REF_PREFIX, null, serviceFacade);
        }
        return nodes;
    }

    public static void afterEnterpriseNameChanged(User user, Map<String, String> diffMap, ServiceFacade serviceFacade) {
        if (diffMap.isEmpty()) {
            return;
        }
        try {
            Set<String> ids = diffMap.keySet();
            List<IObjectData> nodes = findNodeByEnterpriseIds(user, ids, serviceFacade);
            if (nodes.isEmpty()) {
                return;
            }
            for (IObjectData node : nodes) {
                String name = diffMap.get(node.get(ENTERPRISE_ID, String.class));
                if (name != null) {
                    node.setName(name);
                }
            }
            serviceFacade.batchUpdateByFields(user, nodes, Collections.singletonList(NAME));
        } catch (ObjectDefNotFoundError e) {
            log.warn("After enterprise name changed failed: object def not found");
        } catch (Exception e) {
            log.error("After enterprise name changed error", e);
        }
    }

    private static void updateTreePathAndRef(User user, AfterUpdate afterUpdate, ServiceFacade serviceFacade) {
        Map<String, IObjectData> diffParentRefMap = new HashMap<>();
        Map<String, IObjectData> diffRootRefMap = new HashMap<>();
        List<Updated> diffTreePathList = new ArrayList<>();
        for (Updated updated : afterUpdate.getUpdatedList()) {
            String id = updated.getId();
            IObjectData data = updated.getData();
            IObjectData pre = updated.getPre();
            if (isDiffParentRef(data, pre)) {
                diffParentRefMap.put(id, data);
            }
            if (isDiffRootRef(data, pre)) {
                diffRootRefMap.put(data.get(ROOT_ID, String.class), data);
            }
            if (isDiffTreePath(data, pre)) {
                diffTreePathList.add(updated);
            }
        }
        if (!diffTreePathList.isEmpty()) { // !!先后顺序不要变!!
            updateTreePath(user, diffTreePathList, afterUpdate.getLogType(), serviceFacade);
        }
        if (!diffRootRefMap.isEmpty()) { // !!先后顺序不要变!!
            updateRef(user, diffRootRefMap, ROOT_REF_PREFIX, afterUpdate.getLogType(), serviceFacade);
        }
        if (!diffParentRefMap.isEmpty()) {
            updateRef(user, diffParentRefMap, PARENT_REF_PREFIX, afterUpdate.getLogType(), serviceFacade);
        }
    }

    private static boolean isDiffParentRef(IObjectData data, IObjectData pre) {
        return !Objects.equals(data.get(PARENT_ID), pre.get(PARENT_ID))
                || !Objects.equals(data.get(ACCOUNT_MAIN_DATA_ID), pre.get(ACCOUNT_MAIN_DATA_ID))
                || !Objects.equals(data.get(ENTERPRISE_ID), pre.get(ENTERPRISE_ID));
    }

    private static boolean isDiffRootRef(IObjectData data, IObjectData pre) {
        return !Objects.equals(data.get(ROOT_ID), pre.get(ROOT_ID))
                || !Objects.equals(data.get(ACCOUNT_MAIN_DATA_ID), pre.get(ACCOUNT_MAIN_DATA_ID))
                || !Objects.equals(data.get(ENTERPRISE_ID), pre.get(ENTERPRISE_ID));
    }

    private static boolean isDiffTreePath(IObjectData data, IObjectData pre) {
        return !Objects.equals(data.get(TREE_PATH), pre.get(TREE_PATH));
    }

    private static void updateRef(User user, Map<String, IObjectData> diffRefDataMap, String refPrefix, EquityRelationModels.LogType logType, ServiceFacade serviceFacade) {
        Set<String> ids = diffRefDataMap.keySet();
        int offset = 0;
        List<IObjectData> nodes = rollupNodeByRefPrefix(user, refPrefix, ids, offset, serviceFacade);
        doUpdateRef(user, nodes, diffRefDataMap, refPrefix, logType, serviceFacade);
        while (nodes.size() == ROLLUP_LIMIT) {
            offset += ROLLUP_LIMIT;
            nodes = rollupNodeByRefPrefix(user, refPrefix, ids, offset, serviceFacade);
            doUpdateRef(user, nodes, diffRefDataMap, refPrefix, logType, serviceFacade);
        }
    }

    private static void doUpdateRef(User user, List<IObjectData> nodes, Map<String, IObjectData> diffRefDataMap, String refPrefix, EquityRelationModels.LogType logType, ServiceFacade serviceFacade) {
        List<Updated> list = new ArrayList<>();
        for (IObjectData node : nodes) {
            IObjectData diffRefData = diffRefDataMap.get(node.get(refPrefix + "id", String.class));
            if (diffRefData != null && (diffRefData.containsField(ACCOUNT_MAIN_DATA_ID) || diffRefData.containsField(ENTERPRISE_ID))) {
                Updated updated = new Updated(node);
                setRefUpdate(refPrefix, diffRefData, updated);
                list.add(updated);
            }
        }
        if (!list.isEmpty()) {
            User updateUser = User.systemUser(user.getTenantId());
            List<String> fields = new ArrayList<>();
            fields.add(refPrefix + ACCOUNT_MAIN_DATA_ID);
            fields.add(refPrefix + ENTERPRISE_ID);
            serviceFacade.batchUpdateByFields(updateUser, list.stream().map(Updated::getData).collect(Collectors.toList()), fields);
            if (logType != null) {
                logUpdate(updateUser, logType, list, serviceFacade);
            }
        }
    }

    private static void setRefUpdate(String refPrefix, IObjectData diffRefData, Updated updated) {
        if (diffRefData.containsField(ACCOUNT_MAIN_DATA_ID)) {
            String refField = refPrefix + ACCOUNT_MAIN_DATA_ID;
            updated.set(refField, ROOT_REF_PREFIX.equals(refPrefix) ? diffRefData.get(refField) : diffRefData.get(ACCOUNT_MAIN_DATA_ID));
        }
        if (diffRefData.containsField(ENTERPRISE_ID)) {
            String refField = refPrefix + ENTERPRISE_ID;
            updated.set(refField, ROOT_REF_PREFIX.equals(refPrefix) ? diffRefData.get(refField) : diffRefData.get(ENTERPRISE_ID));
        }
    }

    private static List<IObjectData> rollupNodeByRefPrefix(User user, String refPrefix, Collection<String> ids, int offset, ServiceFacade serviceFacade) {
        List<IFilter> filters = new ArrayList<>();
        SearchUtil.fillFilterIn(filters, refPrefix + "id", new ArrayList<>(ids));
        return rollupNode(user, filters, offset, serviceFacade);
    }

    private static List<IObjectData> rollupNodeByAccountMainDataIds(User user, Collection<String> ids, int offset, ServiceFacade serviceFacade) {
        List<IFilter> filters = new ArrayList<>();
        SearchUtil.fillFilterIn(filters, ACCOUNT_MAIN_DATA_ID, new ArrayList<>(ids));
        return rollupNode(user, filters, offset, serviceFacade);
    }

    private static List<IObjectData> findNodeByEnterpriseIds(User user, Collection<String> ids, ServiceFacade serviceFacade) {
        List<IFilter> filters = new ArrayList<>();
        SearchUtil.fillFilterIn(filters, ENTERPRISE_ID, new ArrayList<>(ids));
        return findNode(user, filters, 0, AppFrameworkConfig.getMaxQueryLimit(), serviceFacade);
    }

    private static void updateTreePath(User user, List<Updated> diffTreePathList, EquityRelationModels.LogType logType, ServiceFacade serviceFacade) {
        for (Updated updated : diffTreePathList) {
            updateTreePath(user, updated.getData(), updated.getPre(), logType, serviceFacade);
        }
    }

    private static void updateTreePath(User user, IObjectData data, IObjectData pre, EquityRelationModels.LogType logType, ServiceFacade serviceFacade) {
        User systemUser = User.systemUser(user.getTenantId());
        int offset = 0;
        while (true){
            try {
                List<IObjectData> nodes = rollupNodeByTreePath(user, pre.get(TREE_PATH, String.class), 0, serviceFacade);
                if(CollectionUtils.empty(nodes)){
                    break;
                }
                log.warn("updateTreePath nodes.size:{},offset:{}",nodes.size(),offset);
                doUpdateTreePath(systemUser, nodes, data, pre, logType, serviceFacade);
                if(nodes.size()<ROLLUP_LIMIT){
                    break;
                }
                offset += ROLLUP_LIMIT;
                if(offset>=1000000){
                    log.warn("updateTreePath offset is too many forced return!!");
                    break;
                }
            }catch (Exception e){
                log.warn("AccountTreeRelationUtil updateTreePath error e",e);
                offset += ROLLUP_LIMIT;
                if(offset>=1000000){
                    log.warn("updateTreePath Exception offset is too many forced return!!");
                    break;
                }
            }
        }
    }

    private static void doUpdateTreePath(User user, List<IObjectData> nodes, IObjectData data, IObjectData pre, EquityRelationModels.LogType logType, ServiceFacade serviceFacade) {
        if (nodes.isEmpty()) {
            return;
        }
        List<IObjectData> updateDataList = new ArrayList<>();
        List<Updated> updatedList = new ArrayList<>();
        String curTreePath = data.get(TREE_PATH, String.class);
        String preTreePath = pre.get(TREE_PATH, String.class);
        for (IObjectData node : nodes) {
            Updated updated = new Updated(node);
            String treePath = node.get(TREE_PATH, String.class);
            treePath = treePath.replace(preTreePath, curTreePath);
            updated.set(TREE_PATH, treePath);
            String[] split = treePath.split("\\.");
            updated.set(LEVEL, split.length); // 基于treePath
            updated.set(IS_ROOT, split.length == 1); // 基于treePath
            updated.set(ROOT_ID, split[0]); // 基于treePath
            updated.set(IS_AUTO_UPDATE, false);
            updateDataList.add(node);
            updatedList.add(updated);
        }
        serviceFacade.batchUpdateByFields(user, updateDataList, updateTreeFields());
        logUpdate(user, logType, updatedList, serviceFacade);
    }

    private static List<IObjectData> rollupNodeByTreePath(User user, String treePath, int offset, ServiceFacade serviceFacade) {
        List<IFilter> filters = new ArrayList<>();
        SearchUtil.fillFilterMatch(filters, TREE_PATH, treePath + ".*{1,}");
        return rollupNode(user, filters, offset, serviceFacade);
    }

    private static List<IObjectData> rollupNode(User user, List<IFilter> filters, int offset, ServiceFacade serviceFacade) {
        return findNode(user, filters, offset, ROLLUP_LIMIT, serviceFacade);
    }

    private static List<IObjectData> findNode(User user, List<IFilter> filters, int offset, int limit, ServiceFacade serviceFacade) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setPermissionType(0);
        query.setFilters(filters);
        query.setNeedReturnCountNum(Boolean.FALSE);
        query.setOffset(offset);
        query.setLimit(limit);
        query.setOrders(Lists.newArrayList(new OrderBy(DBRecord.ID, false)));
        return serviceFacade.findBySearchQuery(TreePathUtils.buildActionContext(user), API_NAME, query).getData();
    }

    public static void logUpdate(User user, EquityRelationModels.LogType logType, List<Updated> updatedList, ServiceFacade serviceFacade) {
        try {
            AccountTreeRelationLogService bean = serviceFacade.getBean(AccountTreeRelationLogService.class);
            if (bean != null) {
                bean.logAccountTreeRelation(user, logType, updatedList);
            }
        } catch (Exception e) {
            log.error("log after update error", e);
        }
    }

    @SuppressWarnings("unused")
    private static List<IObjectData> findRelevantIsLeafUpdating(User user, Set<String> parentIds, IObjectDataService objectDataService) {
        List<IObjectData> updateDataList = new ArrayList<>();
        if (parentIds.isEmpty()) {
            return updateDataList;
        }
        Map<String, IObjectData> countMap = getChildrenCount(user, parentIds, objectDataService);
        for (String parentId : parentIds) {
            IObjectData count = countMap.get(parentId);
            String tenantId = user.getTenantId();
            IObjectData updateData = new ObjectData();
            updateData.setId(parentId);
            updateData.setTenantId(tenantId);
            updateData.setDescribeApiName(API_NAME);
            updateData.set(IS_LEAF, isZero(count));
            updateDataList.add(updateData);
        }
        return updateDataList;
    }

    private static boolean isZero(IObjectData count) {
        return count == null || BigDecimal.ZERO.compareTo(new BigDecimal(count.get("groupbycount").toString())) == 0;
    }

    @SuppressWarnings("unchecked")
    private static Map<String, IObjectData> getChildrenCount(User user, Set<String> ids, IObjectDataService objectDataService) {
        List<IFilter> filters = new ArrayList<>();
        SearchUtil.fillFilterHasAnyOf(filters, PARENT_ID, ids);
        IGroupByParameter groupByParameter = new GroupByParameter();
        groupByParameter.setGroupBy(Lists.newArrayList(PARENT_ID));
        SearchTemplateQuery aggQuery = new SearchTemplateQuery();
        aggQuery.setGroupByParameter(groupByParameter);
        aggQuery.setFilters(filters);
        aggQuery.setPermissionType(0);
        IActionContext actionContext = ActionContextExt.of(user).getContext();
        actionContext.put(ActionContextKey.ES_SEARCH_SKIP_RECENT_UPDATE_CHECK, true);
        try {
            List<IObjectData> res = objectDataService.aggregateFindBySearchQuery(actionContext, aggQuery, API_NAME);
            return res.stream().collect(Collectors.toMap(e -> e.get(PARENT_ID, String.class), Function.identity()));
        } catch (MetadataServiceException e) {
            log.error("Get parent count error", e);
            return Collections.emptyMap();
        }
    }

    /**
     * 对齐父节点上的相关信息
     *
     * @param user          user
     * @param dataList      更新的节点
     * @param parentIds     更新节点的父节点
     * @param serviceFacade serviceFacade
     */
    private static void setParent(User user, List<IObjectData> dataList, Set<String> parentIds, ServiceFacade serviceFacade) {
        if (dataList.isEmpty() || parentIds.isEmpty()) {
            return;
        }
        List<IObjectData> parentList = serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), new ArrayList<>(parentIds), API_NAME);
        Set<String> rootIds = new HashSet<>();
        Map<String, IObjectData> parentMap = new HashMap<>();
        for (IObjectData parent : parentList) {
            rootIds.add(parent.get(ROOT_ID, String.class));
            parentMap.put(parent.getId(), parent);
        }
        List<IObjectData> rootList = serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), new ArrayList<>(rootIds), API_NAME);
        Map<String, IObjectData> rootMap = rootList.stream().collect(Collectors.toMap(IObjectData::getId, Function.identity()));
        for (IObjectData data : dataList) {
            String parentId = data.get(PARENT_ID, String.class);
            if (StringUtils.isNotEmpty(parentId)) {
                IObjectData parent = parentMap.get(parentId);
                String rootId = parent.get(ROOT_ID, String.class);
                IObjectData root = rootMap.get(rootId);
                setTree(data, parent, root);
            }
        }
    }

    private static void setRoot(IObjectData data) {
        String id = data.getId();
        data.set(LEVEL, 1);
        data.set(TREE_PATH, id);
        data.set(PARENT_ACCOUNT_MAIN_DATA_ID, null);
        data.set(PARENT_ENTERPRISE_ID, null);
        data.set(ROOT_ID, id);
        data.set(ROOT_ENTERPRISE_ID, data.get(ENTERPRISE_ID));
        data.set(ROOT_ACCOUNT_MAIN_DATA_ID, data.get(ACCOUNT_MAIN_DATA_ID));
        data.set(IS_ROOT, true);
    }

    private static void setTree(IObjectData data, IObjectData parent, IObjectData root) {
        String id = data.getId();
        data.set(PARENT_ENTERPRISE_ID, parent.get(ENTERPRISE_ID));
        data.set(PARENT_ACCOUNT_MAIN_DATA_ID, parent.get(ACCOUNT_MAIN_DATA_ID));
        data.set(ROOT_ID, root.getId());
        data.set(ROOT_ENTERPRISE_ID, root.get(ENTERPRISE_ID));
        data.set(ROOT_ACCOUNT_MAIN_DATA_ID, root.get(ACCOUNT_MAIN_DATA_ID));
        data.set(LEVEL, parent.get(LEVEL, Integer.class, 1) + 1);
        String treePath = parent.get(TREE_PATH, String.class);
        data.set(TREE_PATH, treePath + '.' + id);
        data.set(IS_AUTO_UPDATE, false);
        data.set(IS_ROOT, false);
    }

    private static List<String> updateTreeFields() {
        List<String> list = new ArrayList<>();
        list.add(ROOT_ID);
        list.add(TREE_PATH);
        list.add(IS_ROOT);
        list.add(LEVEL);
        list.add(IS_AUTO_UPDATE);
        return list;
    }

    public static List<String> treeFields() {
        List<String> list = new ArrayList<>();
        list.add(ROOT_ID);
        list.add(PARENT_ID);
        list.add(TREE_PATH);
        list.add(IS_ROOT);
//        list.add(IS_LEAF);
        list.add(LEVEL);
        list.add(IS_AUTO_UPDATE);
        return list;
    }

    public static Set<String> removeFromImportFields() {
        String[] fields = new String[]{
                PARENT_ENTERPRISE_ID, PARENT_ACCOUNT_MAIN_DATA_ID, ROOT_ENTERPRISE_ID, ROOT_ACCOUNT_MAIN_DATA_ID,
                TREE_PATH, IS_LEAF, LEVEL,
                IS_MANUAL_ADD, IS_BRANCH, PERCENT, ROOT_PERCENT, TOTAL_ROOT_PERCENT, RELATION_VERSION,
                IObjectData.EXTEND_OBJ_DATA_ID
        };
        return new HashSet<>(Arrays.asList(fields));
    }

    @Data
    public static class AfterUpdate {
        List<IObjectData> updatingList;
        List<Updated> updatedList;
        Set<String> preParentIds;
        EquityRelationModels.LogType logType;
    }

    @Getter
    public static class Updated implements JsonCompatible {

        private static final long serialVersionUID = 20230728L;

        private final IObjectData data;
        private final IObjectData pre;
        private final Map<String, Object> diff = new HashMap<>();

        Updated(IObjectData data) {
            this.data = data;
            this.pre = ObjectDataExt.of(data).copy();
        }

        public Updated(IObjectData data, IObjectData pre) {
            this.data = data;
            this.pre = pre;
        }

        public String getId() {
            return get(DBRecord.ID, String.class);
        }

        @Override
        public void set(String key, Object value) {
            Object old = data.get(key);
            if (!Objects.equals(old, value)) {
                diff.put(key, value);
            }
            data.set(key, value);
        }

        @Override
        public String toJsonString() {
            return data.toJsonString();
        }

        @Override
        public void fromJsonString(String jsonString) {
            data.fromJsonString(jsonString);
        }

        @Override
        public <T> T get(String key, Class<T> clazz) {
            return data.get(key, clazz);
        }

        @Override
        public Object get(String key) {
            return data.get(key);
        }

    }

    public static IObjectData ofCompanyDetail(EquityRelationshipDataModel.CompanyDetail companyDetail) {
        IObjectData data = new ObjectData();
        data.setName(companyDetail.getName());
        data.setId(companyDetail.getId());
        Field[] fields = companyDetail.getClass().getDeclaredFields();

        for (Field field : fields) {
            field.setAccessible(true);
            try {
                data.set(field.getName(), field.get(companyDetail));
            } catch (IllegalAccessException e) {
              log.error("ofCompanyDetail e,",e);
            }
        }
        return data;
    }
}
