package com.facishare.crm.sfa.lto.integral.core.service;

import com.facishare.crm.sfa.lto.integral.common.constant.IntegralObject;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class BehaviorActionServiceImpl extends BehaviorObjectService implements BehaviorActionService {
    @Autowired
    DataLogicService dataLogicService;

    @Override
    public IObjectData getBehaviorActionByApiName(String tenantId, String categoryApiName, String actionApiName) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, IntegralObject.FIELD_CATEGORY_API_NAME, categoryApiName);
        SearchUtil.fillFilterEq(filters, IntegralObject.FIELD_ACTION_API_NAME, actionApiName);
        query.addFilters(filters);
        query.setLimit(1);
        query.setNeedReturnCountNum(false);

        List<IObjectData> objectDataList = dataLogicService.findBySearchQuery(tenantId, IntegralObject.BEHAVIOR_ACTION_API_NAME, query);
        return CollectionUtils.notEmpty(objectDataList) ? objectDataList.get(0) : null;
    }

    @Override
    public IObjectData getBehaviorActionByLabel(String tenantId, String categoryApiName, String actionLabel) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, IntegralObject.FIELD_CATEGORY_API_NAME, categoryApiName);
        SearchUtil.fillFilterEq(filters, IntegralObject.FIELD_ACTION_LABEL, actionLabel);
        query.addFilters(filters);
        query.setLimit(1);
        query.setNeedReturnCountNum(false);

        List<IObjectData> objectDataList =  dataLogicService.findBySearchQuery(tenantId, IntegralObject.BEHAVIOR_ACTION_API_NAME, query);
        return CollectionUtils.notEmpty(objectDataList) ? objectDataList.get(0) : null;
    }

    @Override
    public List<IObjectData> getBehaviorActionList(String tenantId) {
        return getBehaviorObjectList(tenantId, IntegralObject.BEHAVIOR_ACTION_API_NAME);
    }
}
