package com.facishare.crm.sfa.lto.integral.core.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.sfa.lto.integral.core.entity.IntegralRuleDocument;
import com.facishare.crm.sfa.lto.integral.core.rest.dto.GetRuleList;
import com.facishare.paas.appframework.common.service.dto.UserInfo;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public interface FindRuleList {

    @Data
    class Arg {

        @JSONField(name = "search_query")
		@JsonProperty("search_query")
        private PageInfo pageInfo;
    }

    @Data
    class PageInfo {

        private int limit;

        @JSONField(name = "page")
		@JsonProperty("page")
        private int pageNumber;

        @JSONField(name = "obj_api_name")
		@JsonProperty("obj_api_name")
        private String objectDescribeApiName;

        @JSONField(name = "label")
		@JsonProperty("label")
        private String label;
    }

    @Data
    class Result {

        @JSONField(name = "rule_list")
		@JsonProperty("rule_list")
        private List<IntegralRuleDocument> ruleList;

        @JSONField(name = "limit")
		@JsonProperty("limit")
        private int limit;

        @JSONField(name = "page_number")
		@JsonProperty("page_number")
        private int pageNumber;

        @JSONField(name = "total")
		@JsonProperty("total")
        private int total;

        public static Result build(GetRuleList.Result temp, Map<String, IObjectDescribe> objectDescribeMap
                , Map<String, UserInfo> userInfoMap) {
            Result result = new Result();
            if (Objects.isNull(temp)) {
                return result;
            }
            GetRuleList.resultData tempData = temp.getResult();
            result.setTotal(tempData.getPageInfo().getTotal());
            result.setPageNumber(tempData.getPageInfo().getCurrentPage());
            result.setLimit(tempData.getPageInfo().getPageSize());

            List<GetRuleList.RuleListInfo> tempList = tempData.getContent();
            List<IntegralRuleDocument> ruleDocumentList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(tempList)) {
                for (GetRuleList.RuleListInfo tempRule : tempList) {
                    IntegralRuleDocument document = new IntegralRuleDocument();
                    document.put(IntegralRuleDocument.LABEL, tempRule.getName());
                    document.put(IntegralRuleDocument.CREATE_TIME, tempRule.getCreateTime());
                    document.put(IntegralRuleDocument.LAST_MODIFY_TIME, tempRule.getLastModifiedTime());
                    document.put(IntegralRuleDocument.REMARK, tempRule.getRemark());
                    Boolean status = tempRule.getStatus() == IntegralRuleDocument.STATUS_ACTIVE;
                    document.put(IntegralRuleDocument.ACTIVE, status);
                    document.put(IntegralRuleDocument.OBJECT_API_NAME, tempRule.getObjectApiName());
                    document.put(IntegralRuleDocument.API_NAME, tempRule.getRuleApiName());
                    document.put(IntegralRuleDocument.CREATE_USER, tempRule.getCreatedUserId());
                    document.put(IntegralRuleDocument.LAST_MODIFY_USER, tempRule.getLastModifiedUserId());

                    //补充对象信息
                    IObjectDescribe describe = objectDescribeMap.get(tempRule.getObjectApiName());
                    if (Objects.isNull(describe)) {
                        document.put(IntegralRuleDocument.OBJECT_DELETE, true);
                    } else {
                        document.put(IntegralRuleDocument.OBJECT_DELETE, false);
                        document.put(IntegralRuleDocument.OBJECT_NAME, describe.getDisplayName());
                        document.put(IntegralRuleDocument.OBJECT_ACTIVE, describe.isActive());

                    }
                    //补充user信息
                    UserInfo createUserInfo = userInfoMap.get(tempRule.getCreatedUserId());
                    UserInfo modifyUserInfo = userInfoMap.get(tempRule.getLastModifiedUserId());
                    document.put(IntegralRuleDocument.CREATE_USER_NAME, createUserInfo.getName());
                    document.put(IntegralRuleDocument.LAST_MODIFY_USER_NAME, modifyUserInfo.getName());

                    ruleDocumentList.add(document);
                }
            }
            result.setRuleList(ruleDocumentList);
            return result;
        }
    }

}
