package com.facishare.crm.sfa.prm.platform.infrastructure.statemachine.rule;

import com.facishare.crm.sfa.prm.platform.infrastructure.statemachine.transition.StateTransition;

import java.util.Map;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-04-09
 * ============================================================
 */
public interface TransitionRule<S, E, C> {
    String getTransitionKey();

    Map<S, Map<E, StateTransition<S, E, C>>> getTransitionRules();
}
