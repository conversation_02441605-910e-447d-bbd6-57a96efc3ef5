package com.facishare.crm.sfa.lto.activity.service;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.sfa.lto.activity.enums.ActivityResourceType;
import com.facishare.crm.sfa.lto.activity.producer.ActivityResourceUsageProducer;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.CountFieldDescribe;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.github.jedis.support.MergeJedisCmd;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import redis.clients.jedis.params.SetParams;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class ActivityResourceUsageService {
    private static final long CACHE_EXPIRE = TimeUnit.MINUTES.toSeconds(15);
    private static final String CACHE_PREFIX = "sfa.Activity.res.usage:";
    private static final String RESOURCE_TYPE = "resource_type";
    private static final String DATA_ID = "data_id";
    private static final String DATA_API_NAME = "data_api_name";
    private static final String RESOURCE_USAGE = "resource_usage";
    private static final String OBJECT_API_NAME = "ActivityResourceUsageDetailObj";
    private final ActivityResourceUsageProducer producer;
    private final MergeJedisCmd mergeJedisCmd;
    private final ServiceFacade serviceFacade;

    public ActivityResourceUsageService(ActivityResourceUsageProducer producer, @Qualifier("SFAJedisCmd") MergeJedisCmd mergeJedisCmd, ServiceFacade serviceFacade) {
        this.producer = producer;
        this.mergeJedisCmd = mergeJedisCmd;
        this.serviceFacade = serviceFacade;
    }

    /**
     * 先读后写
     * <p>理论上一条销售记录在一个资源类型下只有一次使用量产生，因此本接口未保证原子性
     *
     * @param usage 使用量，单位：秒
     */
    public void update(String tenantId, String dataId, String dataApiName, long usage) {
        if (hasNoGray(tenantId)) {
            return;
        }
        Objects.requireNonNull(tenantId);
        Objects.requireNonNull(dataId);
        Objects.requireNonNull(dataApiName);
        User user = User.systemUser(tenantId);
        List<IObjectData> dataList = getFromDB(user, dataId, dataApiName);
        if (!dataList.isEmpty()) {
            update0(tenantId, usage, dataList, user);
        }
    }

    /**
     * 先读后写，保证幂等，跳过usage>0
     * <p>理论上一条销售记录在一个资源类型下只有一次使用量产生，因此本接口未保证原子性
     *
     * @param usage 使用量，单位：秒
     */
    public void updateZeroUsage(String tenantId, String dataId, String dataApiName, long usage) {
        if (hasNoGray(tenantId)) {
            return;
        }
        Objects.requireNonNull(tenantId);
        Objects.requireNonNull(dataId);
        Objects.requireNonNull(dataApiName);
        User user = User.systemUser(tenantId);
        List<IObjectData> dataList = getFromDB(user, dataId, dataApiName);
        for (int i = dataList.size() - 1; i >= 0; i--) {
            IObjectData data = dataList.get(i);
            if (data.get(RESOURCE_USAGE, Long.class, 0L) != 0) {
                dataList.remove(i);
            }
        }
        if (!dataList.isEmpty()) {
            update0(tenantId, usage, dataList, user);
        }
    }

    private void update0(String tenantId, long usage, List<IObjectData> dataList, User user) {
        Map<String, String> updateLicenseMap = new HashMap<>();
        for (IObjectData data : dataList) {
            data.setVersion(data.getVersion() + 1);
            data.set(RESOURCE_USAGE, data.get(RESOURCE_USAGE, Long.class, 0L) + usage);
            ActivityResourceType.valueFrom(data.get(RESOURCE_TYPE, String.class))
                    .ifPresent(type -> {
                        Long allUsage = incrAllUsage(tenantId, type, usage);
                        BigDecimal allUsageHours = new BigDecimal(allUsage).divide(new BigDecimal(3600), 1, RoundingMode.HALF_UP);
                        updateLicenseMap.put(type.getParaKey(), allUsageHours.toString());
                    });
        }
        serviceFacade.batchUpdateByFields(user, dataList, Lists.newArrayList(DBRecord.VERSION, RESOURCE_USAGE));
        producer.updateLicenseUsageTo(tenantId, updateLicenseMap);
    }

    public void add(String tenantId, String dataId, String dataApiName, List<ActivityResourceType> types, long usage) {
        if (hasNoGray(tenantId)) {
            return;
        }
        List<IObjectData> exist = getFromDB(User.systemUser(tenantId), dataId, dataApiName);
        if (!exist.isEmpty()) {
            return;
        }
        List<IObjectData> bizDataList = serviceFacade.findObjectDataByIds(tenantId, Collections.singletonList(dataId), dataApiName);
        if (bizDataList.isEmpty()) {
            return;
        }
        String createdBy = bizDataList.get(0).getCreatedBy();
        if (createdBy == null) {
            createdBy = User.SUPPER_ADMIN_USER_ID;
        }
        List<String> userId = Collections.singletonList(createdBy);
        List<IObjectData> dataList = new ArrayList<>(types.size());
        for (ActivityResourceType type : types) {
            IObjectData data = new ObjectData();
            data.setTenantId(tenantId);
            data.setDescribeApiName(OBJECT_API_NAME);
            data.set("user_id", userId);
            data.set(DATA_ID, dataId);
            data.set(DATA_API_NAME, dataApiName);
            data.set(RESOURCE_TYPE, type.getValue());
            data.set(RESOURCE_USAGE, usage);
            dataList.add(data);
        }
        serviceFacade.bulkSaveObjectData(dataList, User.systemUser(tenantId));
    }

    public List<IObjectData> getFromDB(User user, String dataId, String dataApiName) {
        if (hasNoGray(user.getTenantId())) {
            return Collections.emptyList();
        }
        List<IFilter> filters = new ArrayList<>();
        SearchUtil.fillFilterEq(filters, DATA_ID, dataId);
        SearchUtil.fillFilterEq(filters, DATA_API_NAME, dataApiName);
        SearchTemplateQuery query = newSearchTemplateQuery();
        query.setFilters(filters);
        return serviceFacade.findBySearchQueryIgnoreAll(user, OBJECT_API_NAME, query).getData();
    }

    public void updateAllUsage(String tenantId, ActivityResourceType type, long used) {
        if (hasNoGray(tenantId)) {
            return;
        }
        Long allUsage = incrAllUsage(tenantId, type, used);
        producer.updateLicenseUsageTo(tenantId, type.getParaKey(), allUsage.toString());
    }

    private Long incrAllUsage(String tenantId, ActivityResourceType type, long used) {
        String cacheKey = getCacheKey(tenantId, type);
        if (getAllUsageFromCache(cacheKey) == null) {
            long allUsageFromDB = getAllUsageFromDB(tenantId, type);
            saveUsageToCache(cacheKey, allUsageFromDB);
        }
        return mergeJedisCmd.incrBy(cacheKey, used);
    }

    public long getAllUsage(String tenantId, ActivityResourceType type) {
        if (hasNoGray(tenantId)) {
            return 0;
        }
        String cacheKey = getCacheKey(tenantId, type);
        Long cacheUsage = getAllUsageFromCache(cacheKey);
        if (cacheUsage != null) {
            return cacheUsage;
        }
        long allUsageFromDB = getAllUsageFromDB(tenantId, type);
        saveUsageToCache(cacheKey, allUsageFromDB);
        return allUsageFromDB;
    }

    public static Duration parseDuration(String str) {
        if (str == null) {
            return Duration.ZERO;
        }
        str = str.substring(0, str.lastIndexOf("."));
        String[] colonParts = str.split(":");
        if (colonParts.length == 1) {
            return Duration.ofSeconds(Long.parseLong(colonParts[0]));
        }
        if (colonParts.length == 2) {
            return Duration.ofSeconds(Long.parseLong(colonParts[1])).plusMinutes(Long.parseLong(colonParts[0]));
        }
        if (colonParts.length == 3) {
            return Duration.ofSeconds(Long.parseLong(colonParts[2])).plusMinutes(Long.parseLong(colonParts[1])).plusHours(Long.parseLong(colonParts[0]));
        }
        return Duration.ZERO;
    }

    private void saveUsageToCache(String cacheKey, long usage) {
        SetParams setParams = new SetParams();
        setParams.ex(CACHE_EXPIRE); // Set the specified expire time, in seconds (a positive integer).
        setParams.nx(); // Only set the key if it does not already exist.
        mergeJedisCmd.set(cacheKey, Long.toString(usage), setParams);
    }

    private Long getAllUsageFromCache(String cacheKey) {
        String usage = mergeJedisCmd.get(cacheKey);
        if (usage == null) {
            return null;
        } else {
            mergeJedisCmd.expire(cacheKey, CACHE_EXPIRE);
            return Long.valueOf(usage);
        }
    }

    private long getAllUsageFromDB(String tenantId, ActivityResourceType type) {
        List<IFilter> filters = new ArrayList<>();
        SearchUtil.fillFilterEq(filters, RESOURCE_TYPE, type.getValue());
        SearchTemplateQuery query = newSearchTemplateQuery();
        query.setFilters(filters);
        Count count = new CountFieldDescribe();
        count.setCountType(Count.TYPE_SUM);
        count.setSubObjectDescribeApiName(OBJECT_API_NAME);
        count.setCountFieldApiName(RESOURCE_USAGE);
        count.setDecimalPlaces(0);
        Object countValue = serviceFacade.getCountValue(tenantId, count, query);
        if (countValue == null) {
            return 0L;
        } else {
            return Long.parseLong(countValue.toString());
        }
    }

    private static SearchTemplateQuery newSearchTemplateQuery() {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setPermissionType(0);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        return query;
    }

    private static String getCacheKey(String tenantId, ActivityResourceType type) {
        return CACHE_PREFIX + tenantId + "." + type.getValue();
    }

    private boolean hasNoGray(String tenantId) {
        try {
            return serviceFacade.findObject(tenantId, OBJECT_API_NAME) == null;
        } catch (Exception ignore) {
            return true;
        }
    }
}
