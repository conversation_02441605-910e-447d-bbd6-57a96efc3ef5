package com.facishare.crm.sfa.lto.business.models;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> lik
 * @date : 2022/12/5 22:06
 */

public interface PlugInitCreateModels {
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class TaskArg {
        private String tenantId;
        private String actionCode;
        private String fieldApiName;
        private String id;
        private String objectApiName;
        private String pluginApiName;
    }
}
