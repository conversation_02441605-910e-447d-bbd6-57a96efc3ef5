package com.facishare.crm.sfa.lto.objectpool;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.lto.common.models.LtoAccountFieldApiConstants;
import com.facishare.crm.sfa.lto.utils.ObjectDataUtil;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Maps;
import org.jetbrains.annotations.NotNull;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/3/3 11:10
 */
public class PoolEmptyRule {
    private static final String IS_RECYCLING_TEAM_MEMBER = "is_recycling_team_member";
    private static final String IS_RECYCLING_OUT_TEAM_MEMBER = "is_recycling_out_owner";
    private static final String IS_RECYCLING_OUT_ORDINARY_TEAM_MEMBER = "is_recycling_out_ordinary_team_member";
    private static final String IS_CLEAN_OWNER = "is_clean_owner";
    private static final String IS_CLEAN_OUT_OWNER = "is_clean_out_owner";

    private Map<String, EmptyRule> emptyRuleMap;

    private PoolEmptyRule() {
        this.emptyRuleMap = Maps.newHashMap();
    }

    public Map<String, EmptyRule> getEmptyRuleMap() {
        return emptyRuleMap;
    }

    public String getPoolIdByObjectData(IObjectData objectData) {
        String describeApiName = objectData.getDescribeApiName();
        if (Utils.HIGHSEAS_API_NAME.equals(describeApiName)) {
            return ObjectDataUtil.getStringValue(objectData, LtoAccountFieldApiConstants.HIGH_SEAS_ID, "");

        } else if (Utils.LEADS_POOL_API_NAME.equals(describeApiName)) {
            return ObjectDataUtil.getStringValue(objectData, "leads_pool_id", "");
        } else {
            return null;
        }
    }

    public EmptyRule getEmptyRuleById(String poolId) {
        return this.emptyRuleMap.get(poolId);
    }

    public EmptyRule getEmptyRuleById(IObjectData data) {
        String poolId = getPoolIdByObjectData(data);
        if (poolId == null) {
            return null;
        }
        return emptyRuleMap.get(poolId);
    }

    /**
     * 详情页和列表页
     */
    public void fillEmptyRule() {
        Collection<EmptyRule> emptyRules = getEmptyRuleMap().values();
        for (EmptyRule emptyRule : emptyRules) {
            IObjectData poolData = emptyRule.getPoolData();
            poolData.set(IS_RECYCLING_OUT_TEAM_MEMBER, emptyRule.getRecyclingOutTeamMember());
            poolData.set(IS_RECYCLING_OUT_ORDINARY_TEAM_MEMBER, emptyRule.getRecyclingOutOrdinaryMember());
            poolData.set(IS_CLEAN_OUT_OWNER, emptyRule.getCleanOutOwner());
        }
    }

    public static class Builder {
        private List<IObjectData> poolDataList;

        public Builder poolData(@NotNull List<IObjectData> poolDataList) {
            this.poolDataList = poolDataList;
            return this;
        }

        public PoolEmptyRule build() {
            PoolEmptyRule poolEmptyRule = new PoolEmptyRule();
            for (IObjectData poolData : poolDataList) {
                Boolean recyclingTeamMembers = ObjectDataUtil.getBooleanValue(poolData, IS_RECYCLING_TEAM_MEMBER, null);
                Boolean recyclingOutOwnerMember = ObjectDataUtil.getBooleanValue(poolData, IS_RECYCLING_OUT_TEAM_MEMBER, null);
                Boolean recyclingOutOrdinaryMembers = ObjectDataUtil.getBooleanValue(poolData, IS_RECYCLING_OUT_ORDINARY_TEAM_MEMBER, null);
                Boolean cleanOwner = ObjectDataUtil.getBooleanValue(poolData, IS_CLEAN_OWNER, null);
                Boolean cleanOutOwner = ObjectDataUtil.getBooleanValue(poolData, IS_CLEAN_OUT_OWNER, null);
                //兼容历史数据处理逻辑
                if (recyclingOutOwnerMember == null || recyclingOutOrdinaryMembers == null) {
                    if (Boolean.TRUE.equals(recyclingTeamMembers)) {
                        recyclingOutOwnerMember = true;
                        recyclingOutOrdinaryMembers = true;
                    } else {
                        recyclingOutOwnerMember = false;
                        recyclingOutOrdinaryMembers = false;
                    }
                }
                if (cleanOutOwner == null) {
                    cleanOutOwner = Boolean.TRUE.equals(cleanOwner);
                }
                EmptyRule emptyRule = new EmptyRule(poolData.getId(),
                        recyclingTeamMembers,
                        recyclingOutOwnerMember,
                        recyclingOutOrdinaryMembers,
                        cleanOwner,
                        cleanOutOwner,
                        poolData,
                        poolData.getDescribeApiName());
                poolEmptyRule.getEmptyRuleMap().put(emptyRule.poolId, emptyRule);
            }
            return poolEmptyRule;
        }
    }

    public static class EmptyRule {
        private String poolId;
        private Boolean recyclingTeamMember;
        private Boolean recyclingOutTeamMember;
        private Boolean recyclingOutOrdinaryMember;
        private Boolean cleanOwner;
        private Boolean cleanOutOwner;
        private IObjectData poolData;
        private String poolApiName;

        public EmptyRule(String poolId,
                         Boolean recyclingTeamMember,
                         Boolean recyclingOutTeamMember,
                         Boolean recyclingOutOrdinaryMember,
                         Boolean cleanOwner,
                         Boolean cleanOutOwner,
                         IObjectData poolData,
                         String poolApiName) {
            this.recyclingTeamMember = recyclingTeamMember;
            this.recyclingOutTeamMember = recyclingOutTeamMember;
            this.recyclingOutOrdinaryMember = recyclingOutOrdinaryMember;
            this.cleanOwner = cleanOwner;
            this.cleanOutOwner = cleanOutOwner;
            this.poolData = poolData;
            this.poolId = poolId;
            this.poolApiName = poolApiName;
        }

        public Boolean getRecyclingTeamMember() {
            return recyclingTeamMember;
        }

        public String getPoolId() {
            return poolId;
        }

        public Boolean getRecyclingOutTeamMember() {
            return recyclingOutTeamMember;
        }

        public Boolean getRecyclingOutOrdinaryMember() {
            return recyclingOutOrdinaryMember;
        }

        public Boolean getCleanOwner() {
            return cleanOwner;
        }

        public Boolean getCleanOutOwner() {
            return cleanOutOwner;
        }

        public IObjectData getPoolData() {
            return poolData;
        }

        public String getPoolApiName() {
            return poolApiName;
        }
    }
}