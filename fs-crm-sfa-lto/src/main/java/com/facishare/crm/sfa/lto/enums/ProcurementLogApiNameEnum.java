package com.facishare.crm.sfa.lto.enums;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/12/19 19:23
 */
public enum ProcurementLogApiNameEnum {
    LEADS_OBJ("leads_id","LeadsObj"),

    ACCOUNT_OBJ("account_id","AccountObj"),
    COMPETITOR_OBJ("competitor_id","CompetitorObj"),

    ENTERPRISE_INFO_OBJ("enterprise_info_id","EnterpriseInfoObj"),
    PartnerObj("partner_id","PartnerObj"),

    ;

    private String fieldName;

    private String apiName;

    public String getFieldName() {
        return fieldName;
    }

    public String getApiName() {
        return apiName;
    }

    public void setApiName(String apiName) {
        this.apiName = apiName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    ProcurementLogApiNameEnum(String fieldName, String apiName) {
        this.fieldName = fieldName;
        this.apiName = apiName;
    }



    public static String getFieldApiName(String apiName){
        if (apiName == null){
            return "";
        }
        for (ProcurementLogApiNameEnum value : ProcurementLogApiNameEnum.values()) {
            if (value.getApiName().equals(apiName)){
                return value.getFieldName();
            }
        }
        return "";
    }

}
