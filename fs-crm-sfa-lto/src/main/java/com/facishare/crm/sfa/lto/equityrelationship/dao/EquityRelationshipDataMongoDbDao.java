package com.facishare.crm.sfa.lto.equityrelationship.dao;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.lto.equityrelationship.model.EquityRelationshipData;
import com.facishare.crm.sfa.lto.utils.constants.EquityRelationshipDataConstants;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.github.mongo.support.DatastoreExt;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.mongodb.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.bson.types.ObjectId;
import org.mongodb.morphia.Morphia;
import org.mongodb.morphia.mapping.Mapper;
import org.mongodb.morphia.query.Query;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.mongodb.util.JSON.parse;


/**
 * <AUTHOR> lik
 * @date : 2023/6/30 14:08
 */

@Component
@Slf4j
public class EquityRelationshipDataMongoDbDao {
    private DatastoreExt datastore;
    private final Morphia morphia = new Morphia(new Mapper());

    private final static String COLLECTION_PREFIX = "biz_equity_relationship";
    // 定义分页参数
    private static final int pageSizeDefault = 10; // 每页的文档数量
    private static final int pageNoDefault = 1;// 页码，从1开始

    public static final String MOULD_TENANT_ID ="";



    public void setDatastore(DatastoreExt datastore) {
        this.datastore = datastore;
    }

    private DBCollection getTenantDBCollection(String tenantId) {
        return datastore.getDB().getCollection(COLLECTION_PREFIX.concat(tenantId));
    }

    public WriteResult save(String tenantId, EquityRelationshipData.EquityRelationship equityRelationship){
        equityRelationship.setCreate_time(new Date().getTime());
        equityRelationship.setUpdate_time(new Date().getTime());
        equityRelationship.setLast_update_time(new Date().getTime());
        equityRelationship.setPercent(null);
        equityRelationship.setInvestment(null);
        DBCollection dbCollection =getTenantDBCollection(tenantId);
        Gson gson=new Gson();
        DBObject dbObject = (DBObject) parse(gson.toJson(equityRelationship));
        return dbCollection.insert(dbObject);
    }

    public WriteResult save(String tenantId, List<EquityRelationshipData.EquityRelationship> equityRelationshipData){
        DBCollection dbCollection =getTenantDBCollection(tenantId);
        Gson gson=new Gson();
        List<DBObject> documentList = new ArrayList<>();
        int index = 0;
        for(EquityRelationshipData.EquityRelationship x : equityRelationshipData){
            x.setCreate_time(new Date().getTime());
            x.setUpdate_time(new Date().getTime());
            x.setLast_update_time(new Date().getTime());
            x.setPercent(null);
            x.setInvestment(null);
            index++;
            documentList.add((DBObject) parse(gson.toJson(x)));
            if(index >= 200){
                dbCollection.insert(documentList);
                index=0;
                documentList.clear();
            }
        }
        if(CollectionUtils.notEmpty(documentList)){
            return dbCollection.insert(documentList);
        }
        return null;
    }
    public WriteResult save(String tenantId, Map<String, EquityRelationshipData.EquityRelationship> parentNodeMap){
        return save(tenantId, Lists.newArrayList(parentNodeMap.values()));
    }

    public Query<EquityRelationshipData.EquityRelationship> createTenantQuery(String tenantId) {
        DBCollection collection = getTenantDBCollection(tenantId);
        MongoClient mongo = datastore.getMongo();
        String dbName = datastore.getDB().getName();
        // DatastoreExt 是代理类，里面有强转不能用，所以重新create一个
        return datastore.getQueryFactory().createQuery(morphia.createDatastore(mongo, dbName), collection, EquityRelationshipData.EquityRelationship.class);
    }

    public List<EquityRelationshipData.EquityRelationship> queryListByUniqueId(String tenantId, List<String> uniqueId) {

        Query<EquityRelationshipData.EquityRelationship> query = this.createTenantQuery(tenantId);
        if(CollectionUtils.notEmpty(uniqueId)){
            query.criteria(EquityRelationshipDataConstants.Param.UNIQUENESS_DATA_RELATIONSHIP).in(uniqueId);
        }
        return query.asList();
    }
    public List<EquityRelationshipData.EquityRelationship> queryListByEnterprisId(String tenantId, List<String> eIds) {

        Query<EquityRelationshipData.EquityRelationship> query = this.createTenantQuery(tenantId);
        query.criteria(EquityRelationshipDataConstants.Param.ENTERPRISE_ID).in(eIds);
        return query.asList();
    }
    public List<EquityRelationshipData.EquityRelationship> queryListByEnterprisName(String tenantId, String name) {

        Query<EquityRelationshipData.EquityRelationship> query = this.createTenantQuery(tenantId);
        query.criteria(EquityRelationshipDataConstants.Param.ENTERPRISE_NAME).equal(name);
        return query.asList();
    }
    public List<EquityRelationshipData.EquityRelationship> queryListByIds(String tenantId, List<ObjectId> eIds) {

        Query<EquityRelationshipData.EquityRelationship> query = this.createTenantQuery(tenantId);
        query.criteria(EquityRelationshipDataConstants.Param._ID).in(eIds);
        return query.asList();
    }
    public List<EquityRelationshipData.EquityRelationship> queryListByGteTimeStamp(String tenantId, long time,Integer pageNo,Integer pageSize) {

        DBCollection dbCollection =getTenantDBCollection(tenantId);
        // 定义查询条件
        BasicDBObject query = new BasicDBObject(EquityRelationshipDataConstants.Param.LAST_UPDATE_TIME, new BasicDBObject("$gte", time));
        // 定义排序条件
        BasicDBObject sort = new BasicDBObject(EquityRelationshipDataConstants.Param.LAST_UPDATE_TIME, 1); // 按时间升序排序，-1为降序排序
        if(ObjectUtils.isEmpty(pageNo)){
            pageNo = pageNoDefault;
        }
        if(ObjectUtils.isEmpty(pageSize)){
            pageSize = pageSizeDefault;
        }
        // 计算要跳过的文档数量
        int skip = (pageNo - 1) * pageSize;

        // 执行分页查询
        DBCursor cursor = dbCollection.find(query).sort(sort).skip(skip).limit(pageSize);
        List<EquityRelationshipData.EquityRelationship> list =new ArrayList<>();
        // 遍历查询结果
        while (cursor.hasNext()) {
            // 获取下一个文档
            DBObject document = cursor.next();
            EquityRelationshipData.EquityRelationship data = JSONObject.parseObject(JSONObject.toJSONString(document),EquityRelationshipData.EquityRelationship.class);
            data.setId(document.get(EquityRelationshipDataConstants.Param._ID).toString());
            list.add(data);
        }
        return list;
    }
    public List<EquityRelationshipData.EquityRelationship> queryListByParamPaging(String tenantId,Map<String,Object> param,Integer pageNo,Integer pageSize) {
        DBCollection dbCollection =getTenantDBCollection(tenantId);
        // 定义查询条件
        BasicDBObject query = new BasicDBObject();
        if(ObjectUtils.isNotEmpty(param)){
            // 替换为你的查询条件
            query.putAll(param);
        }
        // 定义排序条件
        BasicDBObject sort = new BasicDBObject();
        sort.put(EquityRelationshipDataConstants.Param.LAST_UPDATE_TIME, 1); // 按时间升序排序，-1为降序排序
        if(ObjectUtils.isEmpty(pageNo)){
            pageNo = pageNoDefault;
        }
        if(ObjectUtils.isEmpty(pageSize)){
            pageSize = pageSizeDefault;
        }
        // 计算要跳过的文档数量
        int skip = (pageNo - 1) * pageSize;

        // 执行分页查询
        DBCursor cursor = dbCollection.find(query).sort(sort).skip(skip).limit(pageSize);
        List<EquityRelationshipData.EquityRelationship> list =new ArrayList<>();
        // 遍历查询结果
        while (cursor.hasNext()) {
            // 获取下一个文档
            DBObject document = cursor.next();
            EquityRelationshipData.EquityRelationship data = JSONObject.parseObject(JSONObject.toJSONString(document),EquityRelationshipData.EquityRelationship.class);
            data.setId(document.get(EquityRelationshipDataConstants.Param._ID).toString());
            list.add(data);
        }
        return list;
    } public List<EquityRelationshipData.EquityRelationship> queryListByParamPaging(String tenantId,Map<String,Object> param) {
        return queryListByParamPaging(tenantId,param,null,null);
    }

    public void batchUpdateByList(String tenantId,List<EquityRelationshipData.EquityRelationship> equityRelationshipData){
        DBCollection dbCollection =getTenantDBCollection(tenantId);
        BulkWriteOperation bulkWriteOperation = dbCollection.initializeUnorderedBulkOperation();
        Gson gson=new Gson();
        equityRelationshipData.forEach(x->{
            x.setUpdate_time(new Date().getTime());
            x.setLast_update_time(new Date().getTime());
            BasicDBObject basicDBObject = new BasicDBObject();
            basicDBObject.put(EquityRelationshipDataConstants.Param._ID,new ObjectId(x.getId()));
            bulkWriteOperation.find(basicDBObject).upsert()
                    .update(new BasicDBObject("$set", parse(gson.toJson(x))));
        });
        bulkWriteOperation.execute();
    }

    public void deleteAll(String tenantId){
        DBCollection dbCollection =getTenantDBCollection(tenantId);
        List<EquityRelationshipData.EquityRelationship> list =  queryListByUniqueId(tenantId,null);
        List<ObjectId> ids = list.stream().map(this::idTransferObjectId).collect(Collectors.toList());
        Map<String, List<ObjectId>> map = new HashMap<String, List<ObjectId>>();
        map.put("$in", ids);
        DBObject dbObject = new BasicDBObject();
        dbObject.put(EquityRelationshipDataConstants.Param._ID, map);
        dbCollection.remove(dbObject);
    }
    public void deleteByObjectId(String tenantId,List<String> id){
        DBCollection dbCollection =getTenantDBCollection(tenantId);
        Map<String, List<ObjectId>> map = new HashMap<String, List<ObjectId>>();
        map.put("$in", id.stream().map(this::idTransferObjectId).collect(Collectors.toList()));
        DBObject dbObject = new BasicDBObject();
        dbObject.put(EquityRelationshipDataConstants.Param._ID, map);
        dbCollection.remove(dbObject);
    }
    public void deleteByEIdList(String tenantId,List<String> eId){
        DBCollection dbCollection =getTenantDBCollection(tenantId);
        Map<String, List<String>> map = new HashMap<String, List<String>>();
        map.put("$in", eId);
        DBObject dbObject = new BasicDBObject();
        dbObject.put(EquityRelationshipDataConstants.Param.ENTERPRISE_ID, map);
        dbCollection.remove(dbObject);
    }
    public void createIndexOfMongoDB(String tenantId,Map<String,Object> map){
        DBCollection dbCollection =getTenantDBCollection(tenantId);
        Gson gson=new Gson();
        DBObject object =  (DBObject) parse(gson.toJson(map));
        log.error(JSONObject.toJSONString(object));
        dbCollection.createIndex(object);
    }

    public ObjectId idTransferObjectId(EquityRelationshipData.EquityRelationship x){
        return new ObjectId(x.getId());
    }
    public ObjectId idTransferObjectId(String x){
        return new ObjectId(x);
    }
}
