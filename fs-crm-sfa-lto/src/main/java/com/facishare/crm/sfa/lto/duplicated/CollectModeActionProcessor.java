package com.facishare.crm.sfa.lto.duplicated;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.lto.duplicated.models.DuplicatedModels;
import com.facishare.crm.sfa.lto.utils.*;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.AsyncLogSender;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.dto.InternationalItem;
import com.facishare.paas.appframework.log.dto.LogInfo;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.util.GetI18nKeyUtil;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.facishare.crm.sfa.lto.duplicated.models.DuplicatedConstants.*;
import static com.facishare.crm.sfa.lto.utils.SfaLtoI18NKeyUtil.SFA_LEADS_AUTO_COLLECTED_TO;
import static com.facishare.crm.sfa.lto.utils.SfaLtoI18NKeyUtil.SFA_LEADS_AUTO_COLLECT_TO;
import static com.facishare.crm.sfa.lto.utils.SfaLtoI18NKeyUtil.SFA_LEADS_COLLECTED_TO;
import static com.facishare.crm.sfa.lto.utils.SfaLtoI18NKeyUtil.SFA_LEADS_COLLECT_TO;

@Component
@Slf4j
public class CollectModeActionProcessor extends AbstractModeActionProcessor {

	@Autowired
	private AsyncLogSender asyncLogSender;
	@Autowired
	private OrgService orgService;

    @Override
    public DuplicatedModels.ModeAction getModelAction() {
        return DuplicatedModels.ModeAction.COLLECT;
    }

    @Override
    public void process(User user, DuplicatedModels.DuplicatedProcessingMode processingMode,
                                                  IObjectData freshData, List<IObjectData> duplicatedDataList,
                                                  DuplicatedModels.TriggerAction triggerAction) {
        collect(user, freshData, duplicatedDataList, processingMode);
    }

    private void collect(User user, IObjectData freshData, List<IObjectData> duplicatedDataList,
                                      DuplicatedModels.DuplicatedProcessingMode processingMode) {
        IObjectData collectedToLeads = getCollectedToLeads(processingMode, duplicatedDataList, freshData);
        if(collectedToLeads == null) {
            return;
        }
        collect(user, Lists.newArrayList(freshData), collectedToLeads);
    }

    /**
     * 归集线索
     *
     * @param user
     * @param objectDataList   要归集的数据列表
     * @param collectedToLeads 归集到的线索
     * @return
     */
    public void collect(User user, List<IObjectData> objectDataList, IObjectData collectedToLeads) {
        objectDataList.forEach(d -> {
            if(collectedToLeads.getId().equals(d.getId())) {
                d.set(IS_COLLECTED, false);
                d.set(COLLECTED_TO, null);
            } else {
                d.set(IS_COLLECTED, true);
                d.set(COLLECTED_TO, collectedToLeads.getId());
            }
        });

        List<List<IObjectData>> splitList = ListsUtil.splitList(objectDataList, batchSize);
        List<String> updateFieldList = Lists.newArrayList(IS_COLLECTED, COLLECTED_TO);
        for(List<IObjectData> splitDataList : splitList) {
            ObjectDataUtil.updateFields(user, splitDataList, updateFieldList);
        }

        if (CollectionUtils.isEmpty(objectDataList)) {
            return;
        }
        boolean isAuto = User.SUPPER_ADMIN_USER_ID.equals(user.getUserId());
        if (isAuto) {
			addLinkCollectLog(user, objectDataList.get(0), collectedToLeads, true);
        } else {
            for (IObjectData objectData : objectDataList) {
				addLinkCollectLog(user, objectData, collectedToLeads, false);
            }
        }
    }

    private void addCollectLog(User user, IObjectData objectData, IObjectData collectedTo, boolean isAuto) {
        IObjectDescribe describe = serviceFacade.findObject(user.getTenantId(), Utils.LEADS_API_NAME);
        String msg = getLogMsg(objectData, collectedTo, isAuto);
        serviceFacade.logCustomMessageOnly(user, EventType.MODIFY, ActionType.COLLECTED_TO, describe, objectData,
                msg);
    }

    private String getLogMsg(IObjectData objectData, IObjectData collectedTo, boolean isAuto) {
        String collectedObjectName = collectedTo.getName();
        if (isAuto) {
            return String.format(I18N.text(SFA_LEADS_AUTO_COLLECTED_TO), I18N.text("LeadsObj.attribute.self.display_name"), objectData.getName(), collectedObjectName);
        } else {
            return String.format(I18N.text(SFA_LEADS_COLLECTED_TO), I18N.text("LeadsObj.attribute.self.display_name"), objectData.getName(), collectedObjectName);
        }
    }

	private void addLinkCollectLog(User user, IObjectData objectData, IObjectData collectedTo, boolean isAuto) {
        if(objectData.getId().equals(collectedTo.getId())){
            return;
        }
		String leadsI18nKey = GetI18nKeyUtil.getDescribeDisplayNameKey(Utils.LEADS_API_NAME);
        String defMsg = String.format("归集 %s %s 归集到 %s", I18N.text(leadsI18nKey), objectData.getName(), collectedTo.getName()); // ignoreI18n
        IObjectDescribe describe = serviceFacade.findObject(user.getTenantId(), Utils.LEADS_API_NAME);
        InternationalItem item;
        if (isAuto) {
            defMsg = "自动" + I18N.text(SFA_LEADS_AUTO_COLLECT_TO);
            item = InternationalItem.builder()
                    .defaultInternationalValue(defMsg)
                    .internationalKey("sfa.leads.collect.to.msg") // {0} {1} [{2}] 归集到 [{3}]
                    .internationalParameters(Lists.newArrayList(SFA_LEADS_AUTO_COLLECT_TO, leadsI18nKey, objectData.getName(), collectedTo.getName()))
                    .build();
        } else {
            item = InternationalItem.builder()
                    .defaultInternationalValue(defMsg)
                    .internationalKey("sfa.leads.collect.to.msg")
                    .internationalParameters(Lists.newArrayList(I18NKey.action_collect_to, leadsI18nKey, objectData.getName(), collectedTo.getName()))
                    .build();
        }
        serviceFacade.logWithInternationalCustomMessage(user, EventType.MODIFY, ActionType.COLLECTED_TO, describe, objectData, defMsg, item);
	}

}