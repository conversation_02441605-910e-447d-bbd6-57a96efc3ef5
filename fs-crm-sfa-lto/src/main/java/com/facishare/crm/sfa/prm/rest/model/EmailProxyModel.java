package com.facishare.crm.sfa.prm.rest.model;

import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @time Sundy on 2025-03-11
 * @Description
 */
public interface EmailProxyModel {
    @Data
    class EmailArg {
        private Email arg1;
    }

    @Data
    class Email {
        private String ea;
        private List<String> userIdList;
        private String sender;
        private Set<String> toList;
        private String subject;
        private String content;
        private List<String> attachments;
    }
}
