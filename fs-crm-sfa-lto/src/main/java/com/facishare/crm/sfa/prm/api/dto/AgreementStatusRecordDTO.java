package com.facishare.crm.sfa.prm.api.dto;

import com.facishare.crm.sfa.prm.api.enums.SignStatus;
import com.facishare.paas.appframework.core.util.Lang;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-03-20
 * ============================================================
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgreementStatusRecordDTO {
    private SignStatus changeBeforeStatus;
    private SignStatus changeAfterStatus;
    private String changeRationale;
    private String objectApiName;
    private String dataId;
    private Lang lang;
    @Builder.Default
    private Long changeStatusTime = System.currentTimeMillis();
}
