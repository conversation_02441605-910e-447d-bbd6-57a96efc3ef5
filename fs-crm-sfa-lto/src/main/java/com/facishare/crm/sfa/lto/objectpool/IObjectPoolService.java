package com.facishare.crm.sfa.lto.objectpool;

import com.facishare.crm.sfa.lto.objectpool.models.ObjectPoolActionModels;
import com.facishare.crm.sfa.lto.objectpool.models.ObjectPoolModels;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;
import java.util.Map;

public interface IObjectPoolService {
    String getObjectPoolApiName();

    String getObjectApiName();

    IObjectData getObjectPoolById(String tenantId, String objectId);

    List<IObjectData> getObjectPoolByIds(String tenantId, List<String> objectIds);

    ObjectPoolModels.ObjectPoolPermissions getPoolPermission(String tenantId, String userId, String objectPoolId, String outTenantId);

    Map<String, ObjectPoolModels.ObjectPoolPermissions> getPoolPermission(String tenantId, String userId, List<String> objectPoolIds, String outTenantId);

    List<String> getPoolAdminById(User user, String poolId);

    Map<String, List<String>> getPoolAdminByIds(User user, List<String> poolIds);

    Map<String, List<String>> getInAndOutPoolAdminById(User user, String poolId);

    List<IObjectData> getAllObjectPoolList(String tenantId, String userId, String outTenantId);

    List<IObjectData> getAllVisiblePoolList(String tenantId, String userId, String outTenantId);

    Map<String, List<String>> getPoolMembersByIds(User user, List<String> poolIds);

    List<IObjectData> getObjectPoolAdminByUser(User user);

    List<IObjectData> getObjectPoolMemberByUser(User user);

    List<IObjectData> getAllObjectPools(User user);
    void replacePoolId(ObjectPoolActionModels.ReplacePoolIdMsg msg);
    void deleteAllPoolData(ObjectPoolActionModels.ReplacePoolIdMsg msg);
}
