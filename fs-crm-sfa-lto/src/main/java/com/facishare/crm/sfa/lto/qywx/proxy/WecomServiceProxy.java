package com.facishare.crm.sfa.lto.qywx.proxy;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.lto.qywx.proxy.models.*;
import com.facishare.crm.sfa.lto.qywx.proxy.models.QueryMessageModel.QueryMessageArg;
import com.facishare.crm.sfa.lto.qywx.proxy.models.QueryMessageModel.QueryMessageResult;
import com.facishare.rest.core.annotation.*;

import java.util.List;
import java.util.Map;

@RestResource(value = "QYWX_GATEWAY", desc = "深研企业微信服务网关", contentType = "application/json",
        codec = "com.facishare.paas.appframework.metadata.util.CRMRestServiceCodec")
public interface WecomServiceProxy {

    //    @GET(value = "/open/qyweixin/message/autRetentionCorpBatch", desc = "分页查询开启了自动同步功能的企业")
//    EnterpriseWeChatResult<List<String>> batchQuerySyncEnterprise(@QueryParam("pageNum") String pageNum,
//                                                                  @QueryParam("pageSize") String pageSize);
//
    @GET(value = "/open/qyweixin/message/externalContactEmployeeId", desc = "根据ea查询开启了外部联系功能的员工id")
    EnterpriseWeChatResult<Map<String, String>> queryEmployeeId(@QueryParam("ea") String ea);

    @POST(value = "/open/qyweixin/restProxy/getExternalContactList2", desc = "批量查询客户id列表")
    EnterpriseWeChatResult<List<String>> queryExternalContactIdList2(@QueryParam("fsEa") String fsEa, @QueryParam("outUserId") String outUserId);

    @POST(value = "/qyweixin/fsIds2OpenIds", desc = "员工id绑定关系转换")
    EnterpriseWeChatResult<JSONArray> fsIds2OpenIds(@Body QywxIdMapModel.Arg arg);

    @POST(value = "/open/qyweixin/restProxy/getExternalDetail", desc = "查询企微客户详情")
    QueryContactModel.QueryResult queryExternalContact(@QueryParam("fsEa") String fsEa, @QueryParam("externalUserId") String externalUserId);

    /**
     * <a href="https://developer.work.weixin.qq.com/document/path/93414">批量查询群id</a>
     */
    @POST(value = "/open/qyweixin/restProxy/GetGroupChat", desc = "批量查询群id")
    QueryChatGroupIdModel.QueryResult queryGroupIdList(@Body QueryChatGroupIdModel.QueryArg arg);

    /**
     * <a href="https://developer.work.weixin.qq.com/document/path/92707">查询群详情</a>
     */
    @POST(value = "/open/qyweixin/restProxy/getExternalChat", desc = "查询群详情")
    GroupDetailModel.QueryResult queryGroupDetail(@QueryParam("fsEa") String fsEa, @QueryParam("chatId") String chatId);
//
//    @POST(value = "/open/qyweixin/message/externalContact", desc = "查询客户详情")
//    EnterpriseWeChatResult<JSONObject> queryExternalContact(@Body EnterpriseWeChatProxyModel.QueryExternalContactArg arg);
//
//    @GET(value = "/open/qyweixin/message/getAccountSyncConfig", desc = "查询企业的同步配置信息")
//    EnterpriseWeChatResult<JSONArray> getSyncConfig(@QueryParam("fsEa") String fsEa);

    @POST(value = "/open/qyweixin/restProxy/transferExternalContact", desc = "同步负责人变更信息")
    EnterpriseWeChatResult<List<EnterpriseWechatChangeOwnerSyncModel.SyncResult>> syncChangeOwner(@Body EnterpriseWechatChangeOwnerSyncModel.Arg arg);

    @GET(value = "/open/qyweixin/restProxy/batchGetEmployeeMapping", desc = "批量获取员工的绑定关系")
    EnterpriseWeChatResult<JSONArray> batchGetEmployeeMapping(@QueryParam("fsEa") String fsEa);

    @POST(value = "/open/qyweixin/restProxy/batchGetEmployeeInfo", desc = "批量获取企微用户的详情")
    EnterpriseWeChatResult<JSONArray> batchGetEmployeeInfo(@Body QueryQywxEmployeeArg arg);

    @POST(value = "/open/qyweixin/restProxy/getOpenIds", desc = "明文id转密文id")
    EnterpriseWeChatResult<JSONArray> convertCipherId(@Body ConvertCipherIdModel.Arg arg);

//    @GET(value = "/open/qyweixin/message/queryAllSetting", desc = "查询所有开启了会话存档的企业")
//    EnterpriseWeChatResult<List<String>> queryAllEnableConversionEaList();

    /**
     * 参考企微接口文档
     * https://wiki.firstshare.cn/pages/viewpage.action?pageId=231356579
     */
    @POST(value = "/open/qyweixin/restProxy/externalContactTransferResult", desc = "查询在职客户接替状态")
    EnterpriseWeChatResult<JSONObject> externalContactTransferResult(@Body QywxTransferStatusModel.Arg arg);

    @POST(value = "/open/qyweixin/restProxy/resignedTransferResult", desc = "查询离职客户接替状态")
    EnterpriseWeChatResult<JSONObject> resignedTransferResult(@Body QywxTransferStatusModel.Arg arg);

    @POST(value = "/open/qyweixin/restProxy/getDepartmentInfo", desc = "查询部门信息")
    EnterpriseWeChatResult<JSONObject> getDepartmentInfo(@Body QueryQywxDepartmentArg arg);

    @POST(value = "/open/qyweixin/message/getMessageStorageLocation", desc = "查询会话存档是否开启")
    EnterpriseWeChatResult<JSONObject> getMessageStorageLocation(@QueryParam("fsEa") String fsEa,
                                                                 @QueryParam("serviceKey") String serviceKey,
                                                                 @QueryParam("version") Integer version);

    /**
     * 参考企微接口文档
     * https://wiki.firstshare.cn/pages/viewpage.action?pageId=231356579
     */
    @POST(value = "/open/qyweixin/restProxy/externalContactTransferCustomer", desc = "分配在职成员的客户")
    EnterpriseWeChatResult<JSONArray> externalContactTransferCustomer(@Body QywxTransferModel.Arg arg);

    @POST(value = "/open/qyweixin/restProxy/resignedTransferCustomer", desc = "分配离职成员的客户")
    EnterpriseWeChatResult<JSONArray> resignedTransferCustomer(@Body QywxTransferModel.Arg arg);

    @POST(value = "/open/qyweixin/restProxy/unassignedExternalContact", desc = "获取待分配的离职成员列表")
    EnterpriseWeChatResult<JSONObject> unassignedExternalContact(@Body QywxUnassignedExternalContactModel.Arg arg);

    @POST(value = "/open/qyweixin/restProxy/externalContactTransferGroupChat", desc = "分配在职成员的客户群")
    EnterpriseWeChatResult<JSONArray> externalContactTransferGroupChat(@Body QywxTransferGroupModel.Arg arg);

    @POST(value = "/open/qyweixin/restProxy/resignedTransferGroupChat", desc = "分配离职成员的客户群")
    EnterpriseWeChatResult<JSONArray> resignedTransferGroupChat(@Body QywxTransferGroupModel.Arg arg);

    @POST(value = "/open/qyweixin/restProxy/batchGetEmployeeInfo2", desc = "批量获取企微用户详细信息")
    EnterpriseWeChatResult<JSONArray> batchGetEmployeeInfo2(@Body BatchGetEmployeeInfo.Arg arg);


    @POST(value = "/open/qyweixin/message/switchExternalContactEmployeeId", desc = "转换客户id")
    EnterpriseWechatConvertModel.Result batchConvertId(@Body EnterpriseWechatConvertModel.Arg arg);

    @POST(value = "/erpdss/API/v1/rest/inner/qyweixin/restProxy/queryMessage", desc = "查询会话消息")
    QueryMessageResult queryMessage(@Body QueryMessageArg arg);

    @POST(value = "/erpdss/API/v1/rest/inner/qyweixin/restProxy/rule/create_rule", desc = "质检规则创建")
    QualityInspectionRuleResult createRule(@Body QualityInspectionRule arg);

    @POST(value = "/erpdss/API/v1/rest/inner/qyweixin/restProxy/rule/update_rule", desc = "质检规则更新")
    QualityInspectionRuleResult updateRule(@Body QualityInspectionRule arg);

    @POST(value = "/erpdss/API/v1/rest/inner/qyweixin/restProxy/rule/delete_rule", desc = "质检规则删除")
    QualityInspectionRuleResult delRule(@Body QualityInspectionRuleDel arg);

}
