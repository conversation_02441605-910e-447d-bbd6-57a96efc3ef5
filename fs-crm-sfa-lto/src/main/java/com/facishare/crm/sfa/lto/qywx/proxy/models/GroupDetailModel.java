package com.facishare.crm.sfa.lto.qywx.proxy.models;

import lombok.Data;

import java.util.List;

public interface GroupDetailModel {


	@Data
	class QueryArg {
		private String fsEa;
		private String chatId;
	}

	@Data
	class ChatDetail {
		private GroupChat group_chat;
	}

	@Data
	class QueryResult extends BaseQywxResult {
		private ChatDetail data;
	}


	@Data
	class GroupChat {
		private String chat_id;
		private String name;
		private String owner;
		private String fxUserId;
		private Long create_time;
		private String notice;
		private List<MemberList> member_list;
		private List<UserIdModel> admin_list;
	}


	@Data
	class MemberList {
		private String userid;
		private String fxUserId;
		private Integer type;
		private Long join_time;
		private Integer join_scene;
		private Invitor invitor;
		private String group_nickname;
		private String name;
		private String avatar;//群没有这个字段，手动添加进去

	}

	@Data
	class Invitor {
		private String userid;
		private String fxUserId;
	}

	@Data
	class UserIdModel {
		private String userid;
		private String fxUserId;
	}
}
