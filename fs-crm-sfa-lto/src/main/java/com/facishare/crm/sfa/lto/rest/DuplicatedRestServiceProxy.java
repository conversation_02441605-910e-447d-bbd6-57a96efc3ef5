package com.facishare.crm.sfa.lto.rest;

import com.facishare.crm.sfa.lto.rest.models.DuplicatedModel;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

/**
 * <AUTHOR> lik
 * @date : 2022/7/12 11:59
 */
@RestResource(value = "FS_CRM_SFA_INNER", desc = "CRM Rest API Call", contentType = "application/json",
        codec = "com.facishare.paas.appframework.metadata.util.CRMRestServiceCodec")
public interface DuplicatedRestServiceProxy {
    /**
     * 联合查询
     * @param headers 请求头
     * @param data 请求参数
     * @return 请求结果
     */
    @POST(value = "/LeadsObj/controller/RelatedDuplicateSearch", desc = "线索联合查询客户")
    DuplicatedModel.Result leadsDuplicateSearch(@HeaderMap Map<String, String> headers,
                                                @Body DuplicatedModel.Arg data);

    /**
     * 联合查询
     * @param headers 请求头
     * @param data 请求参数
     * @return 请求结果
     */
    @POST(value = "/AccountObj/controller/RelatedDuplicateSearch", desc = "客户联合查询线索")
    DuplicatedModel.Result accountDuplicateSearch(@HeaderMap Map<String, String> headers,
                                                  @Body DuplicatedModel.Arg data);
}
