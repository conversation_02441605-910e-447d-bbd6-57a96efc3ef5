package com.facishare.crm.sfa.lto.objectpool.models;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface ObjectPoolActionModels {
    @Data
    @NoArgsConstructor
    class Arg {
        private List<String> objectIDs;
        private  String objectPoolId;
        private boolean skipTriggerApprovalFlow;
        private boolean skipFunctionCheck;
        private boolean skipPreAction;
        private boolean skipButtonConditions;
        private ObjectDataDocument args;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        private List<String> successList;
        private List<String> failedList;
        private List<String> errorList;
        private BaseObjectSaveAction.ValidationMessage validationMessage;
    }

    @Data
    @Builder
    class ObjectPoolClaimLog {
        String poolId;
        String objectId;
        String employeeId;
        Long claimTime;
    }

    @Data
    class ObjectOperateMsg {
        private String tenantId;
        private String userId;
        private List<String> accountIds;
        private String owner;
        private String dataOwnerDpt;
        private boolean cleanTeamMember;
        private boolean followConfig;;
        private String actionCode;
    }

    @Data
    class ReplacePoolIdMsg {
        private String tenantId;
        private String userId;
        private String sourcePoolId;
        private String targetPoolId;
        private String objectPoolKeyName;
        private String redisRequestId;
        private String objectPoolApiName;;
        private String objectApiName;
    }
}
