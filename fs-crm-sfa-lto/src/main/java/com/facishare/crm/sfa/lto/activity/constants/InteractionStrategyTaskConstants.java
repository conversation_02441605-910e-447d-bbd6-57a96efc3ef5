package com.facishare.crm.sfa.lto.activity.constants;

/**
 * 交互策略任务常量类
 */
public class InteractionStrategyTaskConstants {

    /**
     * 交互策略任务字段
     */
    public static class TaskFields {
        public static final String ID = "id";
        public static final String TENANT_ID = "tenantId";
        public static final String STRATEGY_ID = "strategyId";
        public static final String STATUS = "status";
        public static final String FAIL_REASON = "failReason";
        public static final String SUBMIT_TOTAL = "submitTotal";
        public static final String RUNNING_TOTAL = "runningTotal";
        public static final String BATCH_TOTAL = "batchTotal";
        public static final String RUNNING_BATCH_TOTAL = "runningBatchTotal";
        public static final String CREATE_BY = "createBy";
        public static final String CREATE_TIME = "createTime";
        public static final String LAST_MODIFY_TIME = "lastModifyTime";
    }
    
    /**
     * 任务状态
     */
    public static class TaskStatus {
        public static final String INIT = "init";
        public static final String RUNNING = "running";
        public static final String FINISHED = "finished";
        public static final String FAIL = "fail";
    }
}