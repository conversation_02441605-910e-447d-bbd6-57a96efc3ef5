package com.facishare.crm.sfa.prm.core.service;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.prm.api.client.EnterpriseRelationServiceAdapter;
import com.facishare.crm.sfa.prm.api.dto.MainOutUser;
import com.facishare.crm.sfa.prm.api.dto.ResponseDTO;
import com.facishare.enterprise.common.result.ResultCode;
import com.facishare.paas.appframework.core.model.User;
import com.fxiaoke.enterpriserelation2.arg.BatchGetRelationDownstreamAndOwnerArg;
import com.fxiaoke.enterpriserelation2.arg.UpstreamAndDownstreamOuterTenantIdOutArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.result.RelationDownstreamResult;
import com.fxiaoke.enterpriserelation2.service.EnterpriseRelationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-02-27
 * ============================================================
 */
@Service
@Slf4j
public class EnterpriseRelationServiceAdapterImpl implements EnterpriseRelationServiceAdapter {
    @Autowired
    private EnterpriseRelationService enterpriseRelationService;
    @Resource(name = "eieaConverterImpl")
    private EIEAConverter eieaConverter;

    @Override
    public String fetchOutTenantId(User user, String objectApiName, String objectDataId) {
        HeaderObj header = HeaderObj.newInstance(user.getTenantIdInt());
        BatchGetRelationDownstreamAndOwnerArg arg = new BatchGetRelationDownstreamAndOwnerArg();
        arg.setUpstreamTenantId(user.getTenantIdInt());
        arg.setObjectApiName(objectApiName);
        arg.setCrmDataIds(Lists.newArrayList(objectDataId));
        RestResult<Map<String, RelationDownstreamResult>> restResult = enterpriseRelationService.batchGetRelationDownstreamAndOwner(header, arg);
        if (restResult == null || restResult.getData() == null) {
            log.warn("fetchOutTenantId warn, restResult is null, objectApiName:{}, objectDataId:{}", objectApiName, objectDataId);
            return null;
        }
        RelationDownstreamResult downstreamAccount = restResult.getData().get(objectDataId);
        if (downstreamAccount == null || downstreamAccount.getDownstreamOuterTenantId() == null) {
            log.warn("fetchOutTenantId warn, downstreamAccount is null, objectApiName:{}, objectDataId:{}", objectApiName, objectDataId);
            return null;
        }
        return String.valueOf(downstreamAccount.getDownstreamOuterTenantId());
    }

    @Override
    public String fetchCrmMapperObjectDataId(User user, String objectApiName, String outTenantId) {
        if (StringUtils.isAnyBlank(objectApiName, outTenantId)) {
            log.warn("fetchCrmMapperObjectDataId warn, objectApiName:{}, outTenantId:{}", objectApiName, outTenantId);
            return null;
        }
        String ea = eieaConverter.enterpriseIdToAccount(user.getTenantIdInt());
        if (StringUtils.isBlank(ea)) {
            log.warn("fetchCrmMapperObjectDataId warn, ea is null, objectApiName:{}, outTenantId:{}", objectApiName, outTenantId);
            return null;
        }
        HeaderObj header = HeaderObj.newInstance(user.getTenantIdInt());
        UpstreamAndDownstreamOuterTenantIdOutArg arg = new UpstreamAndDownstreamOuterTenantIdOutArg();
        arg.setObjectApiName(objectApiName);
        arg.setDownstreamOuterTenantId(Long.valueOf(outTenantId));
        arg.setUpstreamEa(ea);
        RestResult<String> mapperObject = enterpriseRelationService.getMapperObjectId(header, arg);
        if (mapperObject == null || !mapperObject.isSuccess()) {
            log.warn("fetchCrmMapperObjectDataId warn, mapperObject is null, objectApiName:{}, outTenantId:{}", objectApiName, outTenantId);
            return null;
        }
        return mapperObject.getData();
    }

    @Override
    public String fetchCrmMapperObjectDataId(User user, String objectApiName) {
        return fetchCrmMapperObjectDataId(user, objectApiName, user.getOutTenantId());
    }

    @Override
    public ResponseDTO<MainOutUser> fetchOutMainUser(User user, String objectApiName, String mapperDataId) {
        ResponseDTO<MainOutUser> responseDTO = new ResponseDTO<>();
        responseDTO.setSuccess(false);
        BatchGetRelationDownstreamAndOwnerArg arg = new BatchGetRelationDownstreamAndOwnerArg();
        arg.setCrmDataIds(Lists.newArrayList(mapperDataId));
        arg.setUpstreamTenantId(Integer.parseInt(user.getTenantId()));
        arg.setObjectApiName(objectApiName);
        RestResult<Map<String, RelationDownstreamResult>> result = enterpriseRelationService.batchGetRelationDownstreamAndOwner(HeaderObj.newInstance(Integer.parseInt(user.getTenantId())), arg);
        if (result == null) {
            log.warn("EnterpriseRelationServiceAdapterImpl#fetchOutMainUser: 调用互联 batchGetRelationDownstreamAndOwner 接口失败, tenantId:{}", user.getTenantId());
            responseDTO.setReason("result is null");
            return responseDTO;
        }
        if (result.getErrCode() != ResultCode.SUCCESS.getErrorCode()) {
            //如果请求失败时
            log.warn("EnterpriseRelationServiceAdapterImpl#fetchOutMainUser error,tenantId:{},errorCode:{},errorMsg:{},errorDescription:{}",
                    user.getTenantId(), result.getErrCode(), result.getErrMsg(), result.getErrMsg());
            responseDTO.setReason(result.getErrMsg());
            return responseDTO;
        }
        Map<String, RelationDownstreamResult> dataMapping = result.getData();
        if (dataMapping == null || dataMapping.isEmpty() || dataMapping.get(mapperDataId) == null) {
            log.warn("根据 mapperDataId，没有查到外部负责人信息。tenantId:{}, mapperDataId:{}", user.getTenantId(), mapperDataId);
            responseDTO.setReason(result.getErrMsg());
            return responseDTO;
        }
        MainOutUser mainOutUser = MainOutUser.builder()
                .outTenantId(dataMapping.get(mapperDataId).getDownstreamOuterTenantId())
                .outUserId(dataMapping.get(mapperDataId).getRelationOwnerOuterUid())
                .build();
        responseDTO.setSuccess(true);
        responseDTO.setResult(mainOutUser);
        return responseDTO;
    }
}
