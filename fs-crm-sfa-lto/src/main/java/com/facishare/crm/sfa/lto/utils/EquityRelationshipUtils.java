package com.facishare.crm.sfa.lto.utils;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.lto.equityrelationship.model.EquityRelationshipData;
import com.facishare.crm.sfa.lto.utils.constants.EquityRelationshipDataConstants;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.ServiceFacadeImpl;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.ObjectUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.lto.common.models.LtoFieldApiConstants.IS_DELETED;

/**
 * <AUTHOR> lik
 * @date : 2023/9/7 16:38
 */

public class EquityRelationshipUtils {
    private static final ServiceFacade serviceFacade = SpringUtil.getContext().getBean(ServiceFacadeImpl.class);

    public static IObjectData getObjectDataOfEquityRelationshipObj(User user, EquityRelationshipData.EquityRelationship data){
        IObjectData objectData = new ObjectData();
        objectData.set("tenant_id", user.getTenantId());
        objectData.set("name", ObjectDataUtil.getNameCode());
        objectData.set("record_type", "default__c");
        //objectData.set("_id", data.getId());
        objectData.set(EquityRelationshipDataConstants.Param.ENTERPRISE_ID,data.getEnterprise_id());
        objectData.set(EquityRelationshipDataConstants.Param.ENTERPRISE_NAME,data.getEnterprise_name());
        objectData.set(EquityRelationshipDataConstants.Param.ENTERPRISE_TYPE, data.getEnterprise_type());
        if(ObjectUtils.isNotEmpty(data.getRoot_node_path())){
            objectData.set(EquityRelationshipDataConstants.Param.ROOT_NODE_PATH, data.getRoot_node_path());
        }
        if(ObjectUtils.isNotEmpty(data.getParent_stock_proportion())){
            objectData.set(EquityRelationshipDataConstants.Param.PARENT_STOCK_PROPORTION,data.getParent_stock_proportion());
        }
        if(ObjectUtils.isNotEmpty(data.getParent_investment())){
            objectData.set(EquityRelationshipDataConstants.Param.PARENT_INVESTMENT, data.getParent_investment());
        }
        objectData.set(EquityRelationshipDataConstants.Param.RELATIONSHIP_TYPE, data.getRelationship_type());
        objectData.set(EquityRelationshipDataConstants.Param.PARENT_ENTERPRISE_ID, data.getParent_enterprise_id());
        objectData.set(EquityRelationshipDataConstants.Param.PARENT_ENTERPRISE_NAME, data.getParent_enterprise_name());
        objectData.set(EquityRelationshipDataConstants.Param.RELATIONSHIP_PATH, data.getRelationship_path());
        objectData.set(EquityRelationshipDataConstants.Param.IS_NORMAL, data.is_normal());
        objectData.set(EquityRelationshipDataConstants.Param.RELATIONSHIP_LEVEL, data.getRelationship_level());
        objectData.set(EquityRelationshipDataConstants.Param.UNIQUENESS_DATA_RELATIONSHIP, data.getUniqueness_data_relationship());
        objectData.set(EquityRelationshipDataConstants.Param.ACCOUNT_MAIN_DATA_ID, data.getAccount_main_data_id());
        objectData.set(EquityRelationshipDataConstants.Param.FIND_BY_DATA_ID, data.getFind_by_data_id());
        objectData.set("owner", Lists.newArrayList(user.getUserId()));
        objectData.set("life_status", "normal");
        objectData.set("lock_status", "0");
        objectData.set("package", "CRM");
        objectData.set("object_describe_api_name", Utils.EQUITY_RELATIONSHIP_API_NAME);
        objectData.set("is_deleted", false);
        return objectData;
    }


    public static void bindingAccountMainData(User user, List<EquityRelationshipData.EquityRelationship> list, List<IObjectData> existDataList,Map<String,Object> tempParamMap){
        Set<String> nameList = new HashSet<>();
        if(CollectionUtils.notEmpty(existDataList)){
            nameList = existDataList.stream().map(x->x.get(EquityRelationshipDataConstants.Param.ENTERPRISE_NAME).toString()).collect(Collectors.toSet());
        }else{
            nameList = list.stream().map(EquityRelationshipData.EquityRelationship::getEnterprise_name).collect(Collectors.toSet());
        }
        if(CollectionUtils.empty(nameList)){
            return;
        }
        SearchTemplateQuery param = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, "name", Lists.newArrayList(nameList));
        SearchUtil.fillFilterEq(filters, IS_DELETED, "0");
        param.setFilters(filters);
        param.setFindExplicitTotalNum(false);
        param.setNeedReturnCountNum(false);
        param.setPermissionType(0);
        param.setLimit(1000);

        List<IObjectData> dataList = serviceFacade.findBySearchQuery(user, EquityRelationshipDataConstants.AccountMainDataObj, param).getData();
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        Map<String,String> dataListMap = dataList.stream().collect(Collectors.toMap(IObjectData::getName,IObjectData::getId,(v1, v2)->v1));
        if(CollectionUtils.notEmpty(existDataList)){
            existDataList.forEach(x->{
                //先按名称匹配
                if(dataListMap.containsKey(x.get(EquityRelationshipDataConstants.Param.ENTERPRISE_NAME).toString())){
                    x.set(EquityRelationshipDataConstants.Param.ACCOUNT_MAIN_DATA_ID,dataListMap.get(x.get(EquityRelationshipDataConstants.Param.ENTERPRISE_NAME).toString()));
                }
                //查找是否有指定的主数据
                if(ObjectUtils.isNotEmpty(tempParamMap) && tempParamMap.containsKey(EquityRelationshipDataConstants.Param.DATA_NAME) && ObjectUtils.isNotEmpty(tempParamMap.get(EquityRelationshipDataConstants.Param.DATA_NAME)) ){
                    String dataName = tempParamMap.get(EquityRelationshipDataConstants.Param.DATA_NAME).toString();
                    if(dataName.equals(x.get(EquityRelationshipDataConstants.Param.ENTERPRISE_NAME).toString())){
                        x.set(EquityRelationshipDataConstants.Param.ACCOUNT_MAIN_DATA_ID,tempParamMap.get(EquityRelationshipDataConstants.Param.DATA_ID).toString());
                    }
                }
            });
        }else{
            list.forEach(x->{
                //先按名称匹配
                if(dataListMap.containsKey(x.getEnterprise_name())){
                    x.setAccount_main_data_id(dataListMap.get(x.getEnterprise_name()));
                }
                //看是否有指定的客户主数据id
                if(ObjectUtils.isNotEmpty(tempParamMap) && tempParamMap.containsKey(EquityRelationshipDataConstants.Param.DATA_NAME) && ObjectUtils.isNotEmpty(tempParamMap.get(EquityRelationshipDataConstants.Param.DATA_NAME)) ){
                    String dataName = tempParamMap.get(EquityRelationshipDataConstants.Param.DATA_NAME).toString();
                    if(dataName.equals(x.getEnterprise_name())){
                        x.setAccount_main_data_id(tempParamMap.get(EquityRelationshipDataConstants.Param.DATA_ID).toString());
                    }
                }
            });
        }
    }

    public static Map<String,IObjectData> getEquityRelationshipDataByUniquenessToMap(User user,List<String> uniquenessDataRelationships){
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setPermissionType(0);
        searchTemplateQuery.setNeedReturnCountNum(Boolean.FALSE);
        searchTemplateQuery.setNeedReturnQuote(Boolean.FALSE);

        List<IFilter> filters1 = new ArrayList<>();
        SearchUtil.fillFilterIn(filters1, EquityRelationshipDataConstants.Param.UNIQUENESS_DATA_RELATIONSHIP, uniquenessDataRelationships);
        searchTemplateQuery.setFilters(filters1);
        searchTemplateQuery.setLimit(1000);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, Utils.EQUITY_RELATIONSHIP_API_NAME, searchTemplateQuery);
        if(ObjectUtils.isEmpty(queryResult) || CollectionUtils.empty(queryResult.getData())){
            return null;
        }
        return queryResult.getData().stream().collect(Collectors.toMap(x->x.get(EquityRelationshipDataConstants.Param.UNIQUENESS_DATA_RELATIONSHIP).toString(), Function.identity(),(v1, v2)->v1));
    }
    public static List<IObjectData> getEquityRelationshipDataByUniqueness(User user,List<String> uniquenessDataRelationships){
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setPermissionType(0);
        searchTemplateQuery.setNeedReturnCountNum(Boolean.FALSE);
        searchTemplateQuery.setNeedReturnQuote(Boolean.FALSE);

        List<IFilter> filters1 = new ArrayList<>();
        SearchUtil.fillFilterIn(filters1, EquityRelationshipDataConstants.Param.UNIQUENESS_DATA_RELATIONSHIP, uniquenessDataRelationships);
        searchTemplateQuery.setFilters(filters1);
        searchTemplateQuery.setLimit(1000);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, Utils.EQUITY_RELATIONSHIP_API_NAME, searchTemplateQuery);
        if(ObjectUtils.isEmpty(queryResult) || CollectionUtils.empty(queryResult.getData())){
            return null;
        }
        return queryResult.getData();
    }

}
