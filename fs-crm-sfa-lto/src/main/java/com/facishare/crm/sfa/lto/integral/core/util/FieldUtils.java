package com.facishare.crm.sfa.lto.integral.core.util;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Constructor;
import java.util.List;
import java.util.Objects;

@Slf4j
public class FieldUtils {

    /** 字段类型 */
    public static final String TRUE_OR_FALSE = "true_or_false";
    public static final String NUMBER = "number";
    public static final String SELECT_ONE = "select_one";
    public static final String SELECT_MANY = "select_many";
    public static final String CURRENCY = "currency";
    public static final String PERCENT = "percentile";

    /** 字段状态: 禁用 */
    public static final String INVALID = "invalid";
    /** 字段状态: 删除 */
    public static final String DELETED = "deleted";

    public static List<Object> batchConvert(String fieldType, List<Object> value) {

        try {
            if (TRUE_OR_FALSE.equals(fieldType)) {
                    return batchConvert(Boolean.class, value);
            }
        } catch (Exception e) {
            log.error("字段类型转换异常: FieldType:{}, values: {}", fieldType, value, e);
            return value;
        }
        return value;
    }


    private static <T> List<Object> batchConvert(Class<T> clazz, List<Object> value) throws Exception{
        List<Object> result = Lists.newArrayList();
        if (Objects.isNull(value)) {
            return result;
        }
        Constructor constructor = clazz.getConstructor(String.class);
        for (Object v : value) {
            result.add(constructor.newInstance(v));
        }
        return result;
    }

}
