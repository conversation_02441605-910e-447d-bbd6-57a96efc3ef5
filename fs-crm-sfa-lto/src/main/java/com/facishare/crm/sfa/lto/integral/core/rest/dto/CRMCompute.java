package com.facishare.crm.sfa.lto.integral.core.rest.dto;

import com.google.common.collect.Maps;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface CRMCompute {

    @EqualsAndHashCode(callSuper = true)
	@Data
    class Arg extends BaseEngine.Arg {
        @SerializedName("dataIds")
        Set<String> dataIds;
        @SerializedName("macroGroupEntityId")
        String macroGroupEntityId;
        @SerializedName("ruleGroupEntityId")
        String ruleGroupEntityId;

        public static Arg build(String ruleGroupEntityId, String macroGroupEntityId, Set<String> dataIds, String tenantId, String userId) {
            Arg out = new Arg();
            BaseEngine.Context context = new BaseEngine.Context();
            context.setUserId(userId);
            context.setTenantId(tenantId);
            out.setContext(context);
            out.setRuleGroupEntityId(ruleGroupEntityId);
            out.setMacroGroupEntityId(macroGroupEntityId);
            out.setDataIds(dataIds);
            return out;
        }
    }

    @Data
    class CRMComputedInfo {
        /**
         * 被更新对象的apiName，例如：销售线索: LeadsObj
         */
        String macroGroupEntityId;

        /***
         * 规则引擎不care此值内容，写进来是什么，则返回什么
         */
        String macroGroupResult;

        /**
         * macro group api name
         */
        String macroGroupApiName;

        /***
         * macro group name
         */
        String macroGroupName;
        Map<String, Set<SimpleGroupPojo>> dataIdSimpleResultSet = Maps.newHashMap();
    }

//    @Data
//    class behaviorRst {
//        String ruleCodeWithName;
//        Double score;
//    }

    @Data
    class RulePojo {
        private String id;
        private String ruleCode;
        private String fieldName;
        /**
         * 枚举
         */
        private String operate;
        private List<String> fieldValue;
    }

    @Data
    class SimpleGroupPojo {
        /***
         * 匹配规则的(rule_rule_group.rule_code)
         */
        String ruleCode;

        /***
         * 匹配规则的名称（如：行为分类等于公众号 且行为动作等于订阅 且物料名称等于纷享销客CRM）
         */
        String ruleName;
        /***
         * 得分
         */
        String score;
        private Set<RulePojo> rules;
    }

    @EqualsAndHashCode(callSuper = true)
	@Data
    class Result extends BaseEngine.Result<CRMComputedInfo> {

    }

}
