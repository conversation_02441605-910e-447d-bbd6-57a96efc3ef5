package com.facishare.crm.sfa.lto.integral.core.rest.dto;

import com.facishare.crm.sfa.lto.integral.common.constant.IntegralObject;
import com.facishare.crm.sfa.lto.integral.common.constant.RuleDataConstant;
import com.facishare.crm.sfa.lto.integral.core.service.dto.CreateRuleOrUpdate;
import com.facishare.crm.sfa.lto.integral.core.service.dto.RuleDetail;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.metadata.impl.search.Operator;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.annotations.SerializedName;
import joptsimple.internal.Strings;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public interface SaveRuleOrUpdate {
    @EqualsAndHashCode(callSuper = true)
	@Data
    class Arg extends BaseEngine.Arg {
        RuleBasicInfo ruleMacroGroup;
        List<RuleGroup> ruleGroups;

        public static Arg build(CreateRuleOrUpdate.Arg in, RequestContext requestContext) {
            Arg out = new Arg();
            if (Objects.isNull(in)) {
                return out;
            }
            //context
            BaseEngine.Context context = new BaseEngine.Context();
            context.setTenantId(requestContext.getTenantId());
            context.setUserId(requestContext.getUser().getUserId());
            out.setContext(context);

            //规则基本信息
            RuleBasicInfo basicInfo = new RuleBasicInfo();
            basicInfo.setApiName(in.getApiName());
            basicInfo.setName(in.getLabel());
            basicInfo.setObjectApiName(in.getObjectApiName());
            basicInfo.setRemark(in.getRemark());
            basicInfo.setStatus(in.isActive() ? 1 : 0);
            basicInfo.setRuleResult(new Gson().toJson(in.getRuleResults()));
            out.setRuleMacroGroup(basicInfo);

            //ruleGroups
            List<RuleGroup> ruleGroups = new ArrayList<>();
            for (RuleDetail.RuleItem rule : in.getRuleItems()) {
                int priority = 1;
                for (RuleDetail.RuleItemBranch branch : rule.getBranches()) {
                    RuleGroup ruleGroup = new RuleGroup();
                    ruleGroup.setObjectApiName(IntegralObject.BEHAVIOR_DETAIL_API_NAME);
                    ruleGroup.setPriority(priority++);
                    ruleGroup.setFieldApiName(rule.getCategoryApiName());
                    String score = branch.getCalculateType() == RuleDetail.ADD ?
                            String.valueOf(branch.getCalculateScore()) : String.valueOf(0 - branch.getCalculateScore());
                    ruleGroup.setCalculateScore(score);
                    List<Rule> rules = buildRules(rule, branch);
                    ruleGroup.setRules(rules);
                    ruleGroup.setRuleParse(generateRuleParse(rules.size()));

                    ruleGroups.add(ruleGroup);
                }
            }
            out.setRuleGroups(ruleGroups);
            return out;
        }

        private static String generateRuleParse(Integer ruleCount) {
            List<String> indexList = Lists.newArrayList();
            for (int x = 1; x <= ruleCount; x = x + 1) {
                indexList.add(String.valueOf(x));
            }
            return String.format("(%s)",Strings.join(indexList, " and "));
        }

        private static List<Rule> buildRules(RuleDetail.RuleItem rule, RuleDetail.RuleItemBranch branch) {
            List<Rule> rules = new ArrayList<>();
            rules.add(Rule.builder()
                    .objectApiName(IntegralObject.BEHAVIOR_DETAIL_API_NAME)
                    .fieldApiName(IntegralObject.FIELD_CATEGORY_API_NAME)
                    .operate(Operator.EQ.toString())
                    .fieldValue(Lists.newArrayList(rule.getCategoryApiName()))
                    .ruleOrder(1)
                    .build());

            rules.add(Rule.builder()
                    .objectApiName(IntegralObject.BEHAVIOR_DETAIL_API_NAME)
                    .fieldApiName(IntegralObject.FIELD_ACTION_API_NAME)
                    .operate(Operator.EQ.toString())
                    .fieldValue(Lists.newArrayList(branch.getActionApiName()))
                    .ruleOrder(2)
                    .build());

            if (!isAnyOne(branch.getMaterialApiNames())) {
                rules.add(Rule.builder()
                        .objectApiName(IntegralObject.BEHAVIOR_DETAIL_API_NAME)
                        .fieldApiName(IntegralObject.FIELD_MATERIAL_API_NAME)
                        .operate(branch.getMaterialApiNames().size() > 1 ? Operator.IN.toString() : Operator.EQ.toString())
                        .fieldValue(branch.getMaterialApiNames())
                        .ruleOrder(3)
                        .build());
            }

            return rules;
        }

        private static Boolean isAnyOne(List<String> materialApiNames) {
            return CollectionUtils.isNotEmpty(materialApiNames) && RuleDataConstant.MATERIAL_API_NAME_ANY_ONE.equals(String.valueOf(materialApiNames.get(0)));
        }
    }

    @Data
    class RuleBasicInfo {
        @SerializedName("entityId")
        String objectApiName;
        String apiName;
        String name;
        @SerializedName("isRealTime")
        boolean realTimeUpdate;
        /**
         * 规则状态: 1启用，0停用
         */
        int status;
        String remark;
        @SerializedName("result")
        /**
         * 结果规则，传json字符串
         */
        String ruleResult;
    }

    @Data
    class RuleGroup {
        @SerializedName("entityId")
        String objectApiName;
        List actions = new ArrayList();
        /**
         * 加减多少分
         */
        @SerializedName("result")
        String calculateScore;
        @SerializedName("category")
        String fieldApiName;
        int shouldStopIfMatched = 0;
        String ruleParse = "(1)";
        /**
         * 优先级
         */
        int priority;
        List<Rule> rules;
    }

    @Data
    @Builder
    class Rule {
        @SerializedName("entityId")
        private String objectApiName;
        int ruleOrder = 1;
        @SerializedName("fieldName")
        String fieldApiName;
        String operate;
        List<String> fieldValue;
    }


    @EqualsAndHashCode(callSuper = true)
	@Data
    class Result extends BaseEngine.Result<String> {
    }
}
