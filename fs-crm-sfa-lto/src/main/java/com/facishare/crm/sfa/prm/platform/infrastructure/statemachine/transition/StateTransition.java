package com.facishare.crm.sfa.prm.platform.infrastructure.statemachine.transition;

import com.facishare.crm.sfa.prm.platform.infrastructure.statemachine.core.StateAction;

/**
 * ============================================================
 *
 * @param <S> 状态类型
 * @param <E> 事件类型
 * @param <C> 上下文类型
 * @Description: 状态转移
 * @CreatedBy: Sundy on 2025-04-09
 * ============================================================
 */
public class StateTransition<S, E, C> {
    private final S sourceState;
    private final E event;
    private final S targetState;
    private final StateAction<S, E, C> action;

    public StateTransition(S sourceState, E event, S targetState, StateAction<S, E, C> action) {
        this.sourceState = sourceState;
        this.event = event;
        this.targetState = targetState;
        this.action = action;
    }

    public S getSourceState() {
        return sourceState;
    }

    public E getEvent() {
        return event;
    }

    public S getTargetState() {
        return targetState;
    }

    public StateAction<S, E, C> getAction() {
        return action;
    }
}
