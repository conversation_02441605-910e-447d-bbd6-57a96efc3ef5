package com.facishare.crm.sfa.lto.common;

import com.github.autoconf.ConfigFactory;
import com.google.common.util.concurrent.RateLimiter;
import lombok.Data;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
@Data
public class LtoRateLimiterService {
    private double leadsInvalidLimit;
    private RateLimiter leadsInvalidLimiter;

    private double leadsInvalidSlowLimit;
    private RateLimiter leadsInvalidSlowLimiter;

    private double rfmActionDataProcessLimit;
    private RateLimiter rfmActionDataProcessLimiter;

    private double rfmSourceDataProcessLimit;
    private RateLimiter rfmSourceDataProcessLimiter;

    private double dealDataRelationshipLimit;
    private RateLimiter dealDataRelationshipLimiter;

    private double dealDataRelationshipMainSwitchLimit;
    private RateLimiter dealDataRelationshipMainSwitchLimiter;

    private double objectLimitObjectDataLimit;
    private RateLimiter objectLimitObjectDataLimiter;

    private double leadsDuplicatedNormalMsgLimit;
    private RateLimiter leadsDuplicatedNormalMsgLimiter;

    private double leadsDuplicatedSlowMsgLimit;
    private RateLimiter leadsDuplicatedSlowMsgLimiter;

    private double equityRelationshipDataSyncLimit;
    private RateLimiter equityRelationshipDataSyncLimiter;

    @PostConstruct
    public void init() {
        ConfigFactory.getConfig("fs-crm-task-sfa-rate-limit", config -> {
            leadsInvalidLimit = config.getDouble("leadsInvalidLimit",8);
            leadsInvalidLimiter = RateLimiter.create(leadsInvalidLimit);

            leadsInvalidSlowLimit = config.getDouble("leadsInvalidSlowLimit",3);
            leadsInvalidSlowLimiter = RateLimiter.create(leadsInvalidSlowLimit);

            rfmActionDataProcessLimit = config.getDouble("rfmActionDataProcessLimit",20);
            rfmActionDataProcessLimiter = RateLimiter.create(rfmActionDataProcessLimit);

            rfmSourceDataProcessLimit = config.getDouble("rfmSourceDataProcessLimit",30);
            rfmSourceDataProcessLimiter = RateLimiter.create(rfmSourceDataProcessLimit);

            dealDataRelationshipLimit = config.getDouble("dealDataRelationshipLimit",60);
            dealDataRelationshipLimiter = RateLimiter.create(dealDataRelationshipLimit);

            dealDataRelationshipMainSwitchLimit = config.getDouble("dealDataRelationshipMainSwitchLimit",20);
            dealDataRelationshipMainSwitchLimiter = RateLimiter.create(dealDataRelationshipMainSwitchLimit);

            objectLimitObjectDataLimit = config.getDouble("objectLimitObjectDataLimit", 60);
            objectLimitObjectDataLimiter = RateLimiter.create(objectLimitObjectDataLimit);

            leadsDuplicatedNormalMsgLimit = config.getDouble("leadsDuplicatedNormalMsgLimit",20);
            leadsDuplicatedNormalMsgLimiter = RateLimiter.create(leadsDuplicatedNormalMsgLimit);

            leadsDuplicatedSlowMsgLimit = config.getDouble("leadsDuplicatedSlowMsgLimit",1);
            leadsDuplicatedSlowMsgLimiter = RateLimiter.create(leadsDuplicatedSlowMsgLimit);

            equityRelationshipDataSyncLimit = config.getDouble("equityRelationshipDataSyncLimit",60);
            equityRelationshipDataSyncLimiter = RateLimiter.create(equityRelationshipDataSyncLimit);

        });
    }
}