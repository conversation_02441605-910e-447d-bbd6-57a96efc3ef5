package com.facishare.crm.sfa.lto.integral.core.proxy;

import com.facishare.crm.sfa.lto.integral.core.rest.dto.CRMCompute;
import com.facishare.crm.sfa.lto.integral.core.rest.dto.DeleteRuleFromEngine;
import com.facishare.crm.sfa.lto.integral.core.rest.dto.GetInUsedDescribe;
import com.facishare.crm.sfa.lto.integral.core.rest.dto.GetRuleDetail;
import com.facishare.crm.sfa.lto.integral.core.rest.dto.GetRuleList;
import com.facishare.crm.sfa.lto.integral.core.rest.dto.QueryEntityFields;
import com.facishare.crm.sfa.lto.integral.core.rest.dto.QueryResultList;
import com.facishare.crm.sfa.lto.integral.core.rest.dto.SaveRuleOrUpdate;
import com.facishare.crm.sfa.lto.integral.core.rest.dto.UpdateStatus;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

@RestResource(value = "RuleEngine", desc = "规则引擎接口", contentType = "application/json")
public interface IntegralRuleEngineRestProxy {
    @POST(value = "fs-paas-rule/api/rule_macro_group/create", desc = "新建规则")
    SaveRuleOrUpdate.Result saveRule(@HeaderMap Map<String, String> headers, @Body SaveRuleOrUpdate.Arg body);

    @POST(value = "fs-paas-rule/api/rule_macro_group/update", desc = "更新规则")
    SaveRuleOrUpdate.Result updateRule(@HeaderMap Map<String, String> headers, @Body SaveRuleOrUpdate.Arg body);

    @POST(value= "fs-paas-rule/api/rule_macro_group/query_macro_groups", desc = "分页获取规则列表")
    GetRuleList.Result getRuleListByPage(@HeaderMap Map<String, String> headers, @Body GetRuleList.Arg body);

    @POST(value = "fs-paas-rule/api/rule_macro_group/detail", desc = "查询规则详情")
    GetRuleDetail.Result getRuleDetail(@HeaderMap Map<String, String> headers, @Body GetRuleDetail.Arg arg);

    @POST(value = "fs-paas-rule/api/rule_macro_group/delete", desc = "删除规则")
    DeleteRuleFromEngine.Result deleteRule(@HeaderMap Map<String, String> headers, @Body DeleteRuleFromEngine.Arg arg);

    @POST(value = "fs-paas-rule/api/rule_macro_group/update_status", desc = "更新规则状态")
    UpdateStatus.Result updateRuleStatus(@HeaderMap Map<String, String> headers, @Body UpdateStatus.Arg arg);

    @POST(value = "fs-paas-rule/api/rule_macro_group/query_entity_fields", desc = "根据所给对象的apiName查询被引用的字段列表")
    QueryEntityFields.Result queryEntityFields(@HeaderMap Map<String, String> headers, @Body QueryEntityFields.Arg arg);

    @POST(value = "fs-paas-rule/api/rule_macro_group/query_entity_id_list", desc = "查询已使用的对象api")
    GetInUsedDescribe.Result getInUsedDescribeList(@HeaderMap Map<String, String> headers, @Body GetInUsedDescribe.Arg body);

    @POST(value = "fs-paas-rule/api/rule_macro_group/query_result_list", desc = "查询规则结果配置")
    QueryResultList.Result queryResultList(@HeaderMap Map<String, String> headers, @Body QueryResultList.Arg body);

    @POST(value = "fs-paas-rule/api/rule_macro_group/crm_compute", desc = "(行为积分）计算所给数据（id）得分")
    CRMCompute.Result dataCompute(@HeaderMap Map<String, String> headers, @Body CRMCompute.Arg arg);
}
