package com.facishare.crm.sfa.lto.loyalty.task;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.lto.loyalty.constants.LoyaltyConstants;
import com.facishare.crm.sfa.lto.loyalty.model.Loyalty;
import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.fxiaoke.rocketmq.producer.DefaultTopicMessage;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class LoyaltyMqProducer {

    @Resource(name = "loyaltyMq")
    AutoConfMQProducer mqProducer;

    /**
     * 计算成本组织积分
     */
    public void asyncUpdateOrg(String tenantId, String poolId) {
        Loyalty.OrgTask orgTask = new Loyalty.OrgTask();
        orgTask.setTenantId(tenantId);
        orgTask.setPoolId(poolId);
        DefaultTopicMessage msg = new DefaultTopicMessage("org", JSON.toJSONBytes(orgTask));
        mqProducer.send(msg);
    }

    /**
     * 会员触发升级后逻辑
     */
    public void asyncMemberUpgrade(IObjectData record) {
        String changeType = record.get(LoyaltyConstants.LoyaltyMemberChangeRecords.LOY_CHANGE_TYPE, String.class);
        if (!"SET_LEVEL".equals(changeType) && !"CHANGE_LEVEL".equals(changeType) && !"SET_LEVEL_BY_SYS".equals(changeType)) {
            return;
        }
        Loyalty.MemberUpgrade memberUpgrade = new Loyalty.MemberUpgrade();
        memberUpgrade.setTenantId(record.getTenantId());
        memberUpgrade.setMemberId(record.get(LoyaltyConstants.LoyaltyMemberChangeRecords.MEMBER_ID, String.class));
        memberUpgrade.setBeforeTierId(record.get(LoyaltyConstants.LoyaltyMemberChangeRecords.LOY_CHANGE_TIER_BEFORE, String.class));
        memberUpgrade.setAfterTierId(record.get(LoyaltyConstants.LoyaltyMemberChangeRecords.LOY_CHANGE_TIER_AFTER, String.class));
        DefaultTopicMessage msg = new DefaultTopicMessage("memberUpgrade", JSON.toJSONBytes(memberUpgrade));
        mqProducer.send(msg);
    }

    public void asyncClearPointsDetail(Loyalty.ClearPointsDetail clearPointsDetail) {
        DefaultTopicMessage msg = new DefaultTopicMessage("delPointsDetailByEvaluationDate", JSON.toJSONBytes(clearPointsDetail));
        mqProducer.send(msg);
    }
}
