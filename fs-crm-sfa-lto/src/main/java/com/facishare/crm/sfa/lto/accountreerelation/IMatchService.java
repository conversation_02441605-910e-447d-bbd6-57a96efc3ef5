package com.facishare.crm.sfa.lto.accountreerelation;

import com.facishare.crm.sfa.lto.accountreerelation.models.AccountTreeRelationModels.*;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> lik
 * @date : 2024/2/26 16:44
 */

public interface IMatchService {
    void matchData(String tenantId, Map<String, String> accountMainDataMap, List<String> nameList,CreateAccountTreeRelationArg arg);
    List<IObjectData> getMatchedData(String tenantId, List<String> objectIds,String describeApiName);

    String getMatchRuleName(String objectApiName);
}
