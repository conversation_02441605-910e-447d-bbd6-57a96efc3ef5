package com.facishare.crm.sfa.lto.qywx.proxy.models;

import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface QywxTransferModel {

  @Data
	@Builder
	class Arg {
    /**
     * 必填
     */
    private String ea;
    /**
     * 必填，原跟进成员的userid，纷享的员工id
     */
    private String handoverUserId;
    /**
     * 必填，接替成员的userid，纷享的员工id
     */
    private String takeoverUserId;
    /**
     * 必填，客户的external_userid列表，每次最多分配100个客户
     */
    private List<String> externalUserId;
    /**
     * 非必填，转移成功后发给客户的消息，200个字符，不填则使用默认文案
     */
      private String transferSuccessMsg;
  }

  @Data
  class TransferCustomerResult {
    private String externalUserId;//客户的external_userid
    private String errCode;//对此客户进行分配的结果, 具体可参考全局错误码,0表示成功发起接替,待24小时后自动接替,并不代表最终接替成功
  }
}