package com.facishare.crm.sfa.prm.api.enums;

import lombok.Getter;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2024-11-29
 * ============================================================
 */
@Getter
public enum SmsChannelType {
    MARKETING_SIGN(1, "营销通"),
    SERVICE_SIGN(2, "服务通"),
    MARKETING_SIGN_AUTOMATION(3, "营销通(自动化流程)"),
    MARKETING_SIGN_MEETING(4, "营销通(会议通知)"),
    PRM(5, "PRM"),
    FLOW(6, "流程"),
    SIGN_ELECTRONIC_SEAL(7, "电子签章"),
    CRM_VERIFY_CODE(8, "CRM验证码"),
    ENTERPRISE_INTERNET(9, "企业互联"),
    SMS_VERIFY_CODE(10, "短信验证码"),
    CUSTOM_FUNCTION(11, "自定义函数"),
    SFA(12, "SFA"),
    INTEGRATION_PLATFORM(13, "集成平台");

    private final Integer type;
    private final String desc;

    SmsChannelType(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
