package com.facishare.crm.sfa.lto.qywx.proxy.models;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class QualityInspectionHitArg {

    private String fsEa;
    private String qywxCropId;
    private QualityInspectionHitDetailArg data;

    @Data
    public static class QualityInspectionHitDetailArg {
        private String cursor;
        private String token;
        /**
         * 是否需要消息详情，默认为否
         */
        @JSONField(name = "need_detail")
        private Integer needDetail;
        private Integer limit;
    }
}
