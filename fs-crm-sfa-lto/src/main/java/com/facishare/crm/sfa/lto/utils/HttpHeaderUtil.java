package com.facishare.crm.sfa.lto.utils;
import com.facishare.paas.appframework.core.model.User;
import com.google.common.collect.Maps;
import java.util.Map;

/**
 * <AUTHOR> lik
 * @date : 2022/7/12 13:07
 */

public class HttpHeaderUtil {
    private static final String FS_EMPLOYEE = "x-fs-Employee-Id";
    private static final String FS_ENTERPRISE = "x-fs-Enterprise-Id";
    private static final String FS_EI = "x-fs-ei";
    private static final String FS_USERINFO = "x-fs-userInfo";

    private HttpHeaderUtil() {
        throw new IllegalStateException("Utility class");
    }

    public static Map<String, String> getHeaders(User user) {
        Map<String, String> header = Maps.newHashMap();
        header.put(FS_EMPLOYEE, user.getUserId());
        header.put(FS_ENTERPRISE, user.getTenantId());
        header.put(FS_EI, user.getTenantId());
        header.put(FS_USERINFO, user.getUserId());
        return header;
    }

    public static Map<String, String> getHeaders(String tenantId, String userId) {
        Map<String, String> header = Maps.newHashMap();
        header.put(FS_EMPLOYEE, userId);
        header.put(FS_ENTERPRISE, tenantId);
        header.put(FS_EI, tenantId);
        header.put(FS_USERINFO, userId);
        return header;
    }

    public static Map<String, String> getHeaders(User user, String appID) {
        Map<String, String> header = Maps.newHashMap();
        header.put(FS_EMPLOYEE, user.getUserId());
        header.put(FS_ENTERPRISE, user.getTenantId());
        header.put(FS_EI, user.getTenantId());
        header.put(FS_USERINFO, user.getUserId());
        if (user.isOutUser()) {
            header.put("x-out-user-id", user.getOutUserId());
            header.put("x-out-tenant-id", user.getOutTenantId());
            header.put("x-app-id", appID);
        }
        return header;
    }

    public static Map<String, String> getHeaders(String tenantId) {
        return getHeaders(tenantId, User.SUPPER_ADMIN_USER_ID);
    }
}

