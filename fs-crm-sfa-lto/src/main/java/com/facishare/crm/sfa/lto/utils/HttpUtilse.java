package com.facishare.crm.sfa.lto.utils;

import com.facishare.paas.appframework.core.exception.ValidateException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;

import java.io.BufferedReader;
import java.io.Closeable;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URI;
import java.net.URL;
import java.net.URLConnection;
import java.util.Map;
import java.util.Set;

/**
 * 通用http发送方法
 *
 */
@Slf4j
public class HttpUtilse {
    public static final int DEFAULT_TIMEOUT = 15000; // 默认超时时间，单位：毫秒
    public static final int DEFAULT_TIMEOUT_CONNEC = 60000; // 默认超时时间，单位：毫秒

    /**
     * 拼接url对于get请求
     * @param domainName
     * @param methodName
     * @return
     */
    public static String splitJointURLOfGet(String domainName,String methodName,Map<String,String> headerParam){
        if(ObjectUtils.isEmpty(headerParam)){
            return domainName + methodName;
        }
        StringBuilder result = new StringBuilder();
        Set<Map.Entry<String, String>> entrySet = headerParam.entrySet();
        for (Map.Entry<String, String> entry : entrySet) {
            result.append(entry.getKey()).append("=").append( entry.getValue()).append("&");
        }
        if(result.toString().endsWith("&")){
            result.deleteCharAt(result.length() - 1);
        }
        return domainName + methodName + "?"+result.toString();
    }

}