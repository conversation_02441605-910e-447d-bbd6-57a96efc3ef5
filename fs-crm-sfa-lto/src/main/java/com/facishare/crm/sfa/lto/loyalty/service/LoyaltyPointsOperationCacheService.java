package com.facishare.crm.sfa.lto.loyalty.service;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.lto.loyalty.constants.LoyaltyConstants;
import com.facishare.crm.sfa.lto.loyalty.model.Loyalty;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.function.Supplier;

@Service
public class LoyaltyPointsOperationCacheService {

    @Resource
    private ServiceFacade serviceFacade;

    public IObjectData getProgram(Loyalty.PointsOperationParam param) {
        String tenantId = param.getTenantId();
        String memberId = param.getMemberId();
        //入参没有计划id,这里使用会员id去缓存计划,查询的时候也用会员id查询计划。一个会员只有一个计划
        return cacheIfAbsent(param, LoyaltyConstants.LoyaltyProgram.API_NAME, memberId, () -> {
            IObjectData member = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), memberId, LoyaltyConstants.LoyaltyMember.API_NAME);
            return serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), member.get(LoyaltyConstants.LoyaltyMember.PROGRAM_ID, String.class), LoyaltyConstants.LoyaltyProgram.API_NAME);
        });
    }

    public IObjectData getPointPool(Loyalty.PointsOperationParam param) {
        String tenantId = param.getTenantId();
        String pointPoolId = param.getPointPoolId();
        return cacheIfAbsent(param, LoyaltyConstants.LoyaltyPointPool.API_NAME, pointPoolId,
                () -> serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), pointPoolId, LoyaltyConstants.LoyaltyPointPool.API_NAME));
    }

    public IObjectData getOrg(Loyalty.PointsOperationParam param) {
        String tenantId = param.getTenantId();
        IObjectData pointPool = getPointPool(param);
        String orgId = pointPool.get(LoyaltyConstants.LoyaltyPointPool.ORG_ID, String.class);
        return cacheIfAbsent(param, LoyaltyConstants.LoyaltyPointOrg.API_NAME, orgId,
                () -> serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), orgId, LoyaltyConstants.LoyaltyPointOrg.API_NAME));
    }

    public IObjectData getPointsType(Loyalty.PointsOperationParam param) {
        String tenantId = param.getTenantId();
        String pointTypeId = param.getPointTypeId();
        return cacheIfAbsent(param, LoyaltyConstants.LoyaltyPointType.API_NAME, pointTypeId,
                () -> serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), pointTypeId, LoyaltyConstants.LoyaltyPointType.API_NAME));
    }

    private IObjectData cacheIfAbsent(Loyalty.PointsOperationParam param, String apiName, String dataId, Supplier<IObjectData> queryFunc) {
        if (param.getContext() == null) {
            param.setContext(new HashMap<>());
        }
        JSONObject context = new JSONObject(param.getContext());
        JSONObject dataMap = context.getJSONObject(apiName);
        if (dataMap == null) {
            dataMap = new JSONObject();
        }
        if (dataMap.containsKey(dataId)) {
            return ObjectDataExt.of(dataMap.getJSONObject(dataId)).getObjectData();
        }
        IObjectData data = queryFunc.get();
        dataMap.put(dataId, ObjectDataExt.toMap(data));
        context.put(apiName, dataMap);
        return data;
    }

}
