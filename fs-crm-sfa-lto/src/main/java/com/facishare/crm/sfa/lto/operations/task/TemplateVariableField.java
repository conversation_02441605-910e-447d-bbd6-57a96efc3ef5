package com.facishare.crm.sfa.lto.operations.task;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

interface TemplateVariableField {
    String VARIABLE_REGEX = "\\$\\{(.+?)}";

    String raw();

    Set<String> variable();

    Object replace(Map<String, Value> valueMap);

    <V> BiConsumer<OperationsTask, V> setter();

    default boolean hasVariable() {
        Set<String> variable = variable();
        return variable != null && !variable.isEmpty();
    }

    default <V> void setValue(OperationsTask task, V value) {
        setter().accept(task, value);
    }

    /**
     * @param str AccountObj.a.b
     * @return [AccountObj, a, b]
     */
    static List<String> spiltWithDot(String str) {
        return Arrays.asList(str.split("\\."));
    }

    /**
     * @param str ${ContractObj.owner}+${ContractObj.AccountObj.last_modified_by}+30
     * @return [AccountObj.lookup.field1, AccountObj.lookup.field2, AccountObj.lookup.field3]
     */
    static Set<String> getVariable(String str) {
        Set<String> value = new HashSet<>();
        Pattern pattern = Pattern.compile(VARIABLE_REGEX);
        Matcher matcher = pattern.matcher(str);
        while (matcher.find()) {
            value.add(matcher.group(1));
        }
        return value;
    }

    /**
     * "${ContractObj.owner}"
     *
     * @return "ContractObj.owner"
     */
    static String eraseVariableRegex(String raw) {
        Pattern pattern = Pattern.compile(VARIABLE_REGEX);
        Matcher matcher = pattern.matcher(raw);
        while (matcher.find()) {
            String name = matcher.group(1);
            raw = raw.replaceAll("\\$\\{" + name + "}", name);
        }
        return raw;
    }

    /**
     * "${ContractObj.owner}"
     *
     * @return replacement
     */
    static String replaceVariableRegex(String raw, String replacement) {
        Pattern pattern = Pattern.compile(VARIABLE_REGEX);
        Matcher matcher = pattern.matcher(raw);
        while (matcher.find()) {
            String name = matcher.group(1);
            raw = raw.replaceAll("\\$\\{" + name + "}", replacement);
        }
        return raw;
    }
}
