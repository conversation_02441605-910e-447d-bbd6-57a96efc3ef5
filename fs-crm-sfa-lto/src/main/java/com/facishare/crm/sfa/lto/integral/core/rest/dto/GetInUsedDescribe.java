package com.facishare.crm.sfa.lto.integral.core.rest.dto;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

public interface GetInUsedDescribe {

    @EqualsAndHashCode(callSuper = true)
	@Data
    class Arg extends BaseEngine.Arg{
        @SerializedName("entityId")
        String describeApiName;

        public static Arg build(String describeApiName, RequestContext requestContext){

            Arg request = new Arg();

            BaseEngine.Context context = new BaseEngine.Context();
            context.setTenantId(requestContext.getTenantId());
            context.setUserId(requestContext.getUser().getUserId());
            request.setContext(context);
            request.setDescribeApiName(describeApiName);

            return request;
        }

        public static Arg build(String tenantId, String userId){
            Arg request = new Arg();

            BaseEngine.Context context = new BaseEngine.Context();
            context.setTenantId(tenantId);
            context.setUserId(userId);
            request.setContext(context);

            return request;
        }

    }

    @EqualsAndHashCode(callSuper = true)
	@Data
    class Result extends BaseEngine.Result<List<String>>{ }
}
