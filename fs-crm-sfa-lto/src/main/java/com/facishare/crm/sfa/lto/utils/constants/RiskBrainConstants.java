package com.facishare.crm.sfa.lto.utils.constants;

/**
 * <AUTHOR> lik
 * @date : 2023/2/1 10:31
 */

public interface RiskBrainConstants {

    String KEY = "biz_risk_brain_user_key";

    class Field {
        public static final String ID = "id";
        public static final String TENANT_ID = "tenant_id";
        public static final String VALUE_JSON = "value_json";
    }


    enum PushType {
        ADD("add","关注"),
        DELETE("delete","取消关注");

        private final String code;
        private final String name;

        PushType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }
}
