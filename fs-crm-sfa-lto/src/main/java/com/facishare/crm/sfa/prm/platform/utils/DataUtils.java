package com.facishare.crm.sfa.prm.platform.utils;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.Optional;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-02-27
 * ============================================================
 */
public class DataUtils {
    public static <T> T getValue(IObjectData data, String key, Class<T> type, T defaultValue) {
        if (data == null) {
            return defaultValue;
        }
        Object o = data.get(key);
        if (o == null) {
            return defaultValue;
        }
        return Optional.ofNullable(data.get(key, type)).orElse(defaultValue);
    }

    public static <T> T getValue(ObjectDataDocument dataDocument, String key, Class<T> type) {
        return getValue(dataDocument, key, type, null);
    }

    public static <T> T getValue(IObjectData data, String key, Class<T> type) {
        return getValue(data, key, type, null);
    }

    public static <T> T getValue(ObjectDataDocument dataDocument, String key, Class<T> type, T defaultValue) {
        if (dataDocument == null || dataDocument.toObjectData() == null) {
            return defaultValue;
        }
        return getValue(dataDocument.toObjectData(), key, type, defaultValue);
    }

}
