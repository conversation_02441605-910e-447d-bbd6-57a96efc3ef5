package com.facishare.crm.sfa.lto.duplicated.models;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

public interface DuplicatedModels {
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class DuplicatedProcessing {
        private String id;
        private String name;
        private String description;
        @JSONField(name = "trigger_actions")
        @JsonProperty("trigger_actions")
        private List<String> triggerActions;
        private Integer priority;
        @JSONField(name = "filters")
        @JsonProperty("filters")
        List<Filter> filters;
        private Integer status;
        @JSONField(name = "duplicated_search_rules")
        @JsonProperty("duplicated_search_rules")
        private List<DuplicatedSearchRule> duplicatedSearchRules;
        @JSONField(name = "duplicated_processing_modes")
        @JsonProperty("duplicated_processing_modes")
        private List<DuplicatedProcessingMode> duplicatedProcessingModes;
        @JSONField(name = "refresh_version")
        @JsonProperty("refresh_version")
        private int refreshVersion;
        @JSONField(name = "last_modified_time")
        @JsonProperty("last_modified_time")
        private long lastModifiedTime;
        @JSONField(name = "last_modified_by")
        @JsonProperty("last_modified_by")
        private String lastModifiedBy;
        @JSONField(name = "task_execute_time")
        @JsonProperty("task_execute_time")
        private String taskExecuteTime;
    }

    @Data
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Filter {
        private List<Condition> conditions;
        private Connector connector;
    }

    @Data
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Condition {
        private String operator;
        @JSONField(name = "operator_name")
        @JsonProperty("operator_name")
        private String operatorName;
        @JSONField(name = "field_name")
        @JsonProperty("field_name")
        private String fieldName;
        @JSONField(name = "field_values")
        @JsonProperty("field_values")
        private List<String> fieldValues;
        private Connector connector;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class DuplicatedSearchRule {
        private String id;
        private String name;
        private String description;
        @JsonProperty("object_api_name")
        @JSONField(name = "object_api_name")
        private String objectApiName;
        @JsonProperty("object_name")
        @JSONField(name = "object_name")
        private String objectName;
        @JsonProperty("show_fields")
        @JSONField(name = "show_fields")
        private List<String> showFields;
        @JsonProperty("usable_rules")
        @JSONField(name = "usable_rules")
        private List<UsableRule> usableRules;
        private Integer status;
        @JsonProperty("map_full_fields")
        @JSONField(name = "map_full_fields")
        private boolean mapFullFields;
        @JSONField(name = "task_execute_time")
        @JsonProperty("task_execute_time")
        private String taskExecuteTime;
    }

    @Data
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class UsableRule {
        private Connector connector;
        private List<SearchCondition> conditions;
    }

    @JsonIgnoreProperties(
            ignoreUnknown = true
    )
    @Data
    class SearchCondition {
        private Connector connector;
        @JsonProperty("field_name")
        @JSONField(name = "field_name")
        private String fieldName;
        @JsonIgnore
        private Policy policy;
    }

    @Data
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class SearchTemplate {
        private int limit = 100;
        private int offset = 0;
        private Boolean needCountNum;
        private String tableName;
        private List<WhereParam> whereParams;
        private List<OrderBy> orders;
    }

    @Data
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class WhereParam {
        private String operator;
        private String fieldName;
        private List<Object> values;
    }

    @Data
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class OrderBy {
        private String fieldName;
        private String value;
        private String indexName;
        private Boolean isNullLast;
        private Boolean isAsc;
        private Boolean isReference;
    }

    enum Connector {
        AND,
        OR
    }

    enum Policy {
        PRECISE,
        FUZZY
    }

    @Data
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class DuplicatedProcessingMode {
        private String id;
        private Integer priority;
        @JsonProperty("duplicated_processing_id")
        @JSONField(name = "duplicated_processing_id")
        private String duplicatedProcessingId;
        private List<Filter> filters;
        @JsonProperty("mode_action")
        @JSONField(name = "mode_action")
        private ModeAction modeAction;
        @JsonProperty("mode_rule")
        @JSONField(name = "mode_rule")
        private ModeRule modeRule;

    }

    enum ModeAction {
        NONE,
        MARK,
        COLLECT,
        ADD_INTERACTIVE_RECORDS,
        CUSTOM_FUNCTION
    }

    @Data
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class ModeRule {
        @JsonProperty("selection_strategy")
        @JSONField(name = "selection_strategy")
        private SelectionStrategy selectionStrategy;
        @JsonProperty("custom_function")
        @JSONField(name = "custom_function")
        private CustomFunction customFunction;
        /*
          目前就这三个选项，后续有新规则可以在这里扩展
          1.多行文字字段类型更新方式   multi_line_update_mode:skip（跳过），cover（覆盖），splicing（拼接）
          2.非多行文字字段类型更新方式  other_update_mode:skip（跳过），cover（覆盖）
          3.是否自动更新已有线索  auto_update：true
        */
        @JsonProperty("extend_rules")
        @JSONField(name = "extend_rules")
        private ExtendRules extendRules;
    }

    @Data
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class SelectionStrategy {
        @JsonProperty("field_name")
        @JSONField(name = "field_name")
        private String fieldName;
        @JsonProperty("sort_direction")
        @JSONField(name = "sort_direction")
        private SortDirection sortDirection;
    }

    @Data
    class CustomFunction {
        @JsonProperty("function_api_name")
        @JSONField(name = "function_api_name")
        private String functionApiName;
        @JsonProperty("binding_object_api_name")
        @JSONField(name = "binding_object_api_name")
        private String bindingObjectApiName;
        @JsonProperty("function_name")
        @JSONField(name = "function_name")
        private String functionName;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class ExtendRules {
        @JsonProperty("auto_update")
        @JSONField(name = "auto_update")
        private boolean autoUpdate;
        /**
         * SKIP（跳过），COVER（全部覆盖），NULL_VALUE_COVER（仅空值覆盖），SPLICING（拼接）
         */
        @JsonProperty("multi_line_update_mode")
        @JSONField(name = "multi_line_update_mode")
        private UpdateMode multiLineUpdateMode;

        /**
         * SKIP（跳过），COVER（全部覆盖），NULL_VALUE_COVER（仅空值覆盖），SPLICING（拼接）
         */
        @JsonProperty("many_select_update_mode")
        @JSONField(name = "many_select_update_mode")
        private UpdateMode manySelectUpdateMode;

        /**
         * SKIP（跳过），COVER（全部覆盖），NULL_VALUE_COVER（仅空值覆盖），
         */
        @JsonProperty("other_update_mode")
        @JSONField(name = "other_update_mode")
        private UpdateMode otherUpdateMode;
    }

    enum UpdateMode {
        SKIP,
        COVER,
        NULL_VALUE_COVER,
        SPLICING
    }

    @Data
    class ProcessArg {
        @JsonProperty("refresh_version")
        @JSONField(name = "refresh_version")
        int refreshVersion;
        @JsonProperty("object_data_id_list")
        @JSONField(name = "object_data_id_list")
        List<String> objectDataIdList;
        String source;
    }

    @Data
    class ProcessResult {
        @JsonProperty("error_code")
        @JSONField(name = "error_code")
        private String errorCode;
        private String message;
    }

    @Data
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class ProcessSingleResult {
        @JsonProperty("leads_data")
        @JSONField(name = "leads_data")
        private ObjectDataDocument leadsData;
        @JsonProperty("refresh_version")
        @JSONField(name = "refresh_version")
        private int refreshVersion;
        @JsonProperty("duplicated_processing_id")
        @JSONField(name = "duplicated_processing_id")
        private String duplicatedProcessingId;
        @JsonProperty("mode_action")
        @JSONField(name = "mode_action")
        private ModeAction modeAction;
        @JSONField(name = "need_confirm")
        private boolean needConfirm;
        @JsonProperty("extras")
        @JSONField(name = "extras")
        private Map<String, Object> extras;
    }

    enum SortDirection {
        ASC,
        DESC
    }

    enum TriggerAction {
        NONE,
        ADD,
        IMPORT,
        SMART_FORM,
        OPEN_API,
        OPEN_API_ADD,
        OPEN_API_EDIT,
        EDIT,
        RECOVER,
        MARKETING_ADD,
        MERGE,
        INVALID,
        RULE_CHANGE,
        CLEAR_DUPLICATED,
        CHANGE_OWNER,
        DIRECT_PROCESSOR;

        public static TriggerAction getTriggerAction(String action) {
            switch (action) {
                case "ADD":
                    return ADD;
                case "IMPORT":
                    return IMPORT;
                case "SMART_FORM":
                    return SMART_FORM;
                case "OPEN_API_ADD":
                    return OPEN_API_ADD;
                case "OPEN_API_EDIT":
                    return OPEN_API_EDIT;
                case "EDIT":
                    return EDIT;
                case "RECOVER":
                    return RECOVER;
                case "MARKETING_ADD":
                    return MARKETING_ADD;
                case "MERGE":
                    return MERGE;
                case "INVALID":
                    return INVALID;
                case "RULE_CHANGE":
                    return RULE_CHANGE;
                case "CLEAR_DUPLICATED":
                    return CLEAR_DUPLICATED;
                case "CHANGE_OWNER":
                    return CHANGE_OWNER;
                case "DIRECT_PROCESSOR":
                    return DIRECT_PROCESSOR;
                default:
                    return NONE;
            }
        }
    }

    enum SqlOperator {
        EQ,
        NEQ,
        IN,
        NIN,
        IS,
        ISN,
        LIKE;

        SqlOperator() {
        }

        public static String findSQLOperator(SqlOperator operator) {
            switch (operator) {
                case EQ:
                    return "=";
                case NEQ:
                    return "!=";
                case LIKE:
                    return " like ";
                default:
                    throw new ValidateException("operator is not support !");
            }
        }
    }
}