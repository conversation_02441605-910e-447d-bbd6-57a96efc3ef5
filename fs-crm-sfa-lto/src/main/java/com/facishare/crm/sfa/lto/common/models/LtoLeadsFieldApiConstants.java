package com.facishare.crm.sfa.lto.common.models;

public final class LtoLeadsFieldApiConstants {
    public static final String ASSIGNER_ID = "assigner_id";
    public static final String ASSIGNED_TIME = "assigned_time";
    public static final String LEADS_POOL_ID = "leads_pool_id";
    public static final String OWNER_CHANGE_TIME = "owner_change_time";
    public static final String IS_OVERTIME = "is_overtime";
    public static final String LEADS_STATUS = "leads_status";
    public static final String IS_DUPLICATED = "is_duplicated";
    public static final String IS_COLLECTED = "is_collected";
    public static final String LAST_FOLLOWED_TIME = "last_followed_time";
    public static final String COLLECTED_TO = "collected_to";
    public static final String REFRESH_DUPLICATED_VERSION = "refresh_duplicated_version";
    public static final String BACK_REASON = "back_reason";
    public static final String RETURNED_TIME = "returned_time";
    public static final String RESALE_COUNT = "resale_count";

    private LtoLeadsFieldApiConstants() {
        throw new IllegalStateException("Utility class");
    }

    public enum Status {
        UN_ALLOCATE(1, "未分配"),
        UN_DEAL(2, "待处理"),
        TO_CUSTOMER(3,"已转换"),
        DEALED(4,"跟进中"),
        CLOSED(5,"无效"),
        INEFFECTIVE(6,"未生效"),
        AUDITING(7,"审核中"),
        CHANGING(8,"变更中"),
        ABOLISHED(99,"已作废");

        private  int code;
        private  String text;

        Status(int code, String text) {
            this.code = code;
            this.text = text;
        }
        public int getCode() {
            return code;
        }
        public String getValue()
        {
            return String.valueOf(code);
        }
        public String getText()
        {
            return text;
        }
    }

    public enum BizStatus {
        UN_ASSIGNED("un_assigned", "未分配"),
        UN_PROCESSED("un_processed", "待处理"),
        TRANSFORMED("transformed","已转换"),
        PROCESSED("processed","跟进中"),
        CLOSED("closed","无效");

        private final String code;
        private final String text;

        BizStatus(String code, String text) {
            this.code = code;
            this.text = text;
        }

        public String getCode() {
            return code;
        }
        public String getValue()
        {
            return String.valueOf(code);
        }
        public String getText()
        {
            return text;
        }
    }
}
