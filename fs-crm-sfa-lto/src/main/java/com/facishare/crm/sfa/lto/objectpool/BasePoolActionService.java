package com.facishare.crm.sfa.lto.objectpool;

import com.facishare.crm.sfa.lto.common.LtoOrgCommonService;
import com.facishare.crm.sfa.lto.common.LtoTeamMemberService;
import com.facishare.crm.sfa.lto.common.models.LtoFieldApiConstants;
import com.facishare.crm.sfa.lto.exception.ExceptionUtil;
import com.facishare.crm.sfa.lto.objectpool.models.ObjectPoolActionModels;
import com.facishare.crm.sfa.lto.utils.CommonSqlUtil;
import com.facishare.crm.sfa.lto.utils.ObjectDataUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.Tenantable;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
public abstract class BasePoolActionService implements IObjectPoolActionService {
    protected static final String CLAIM_LOG_TABLE_NAME = "biz_data_claim_log";
    @Autowired
    protected ServiceFacade serviceFacade;

    @Autowired
    protected LtoOrgCommonService ltoOrgCommonService;

    @Autowired
    protected LtoTeamMemberService teamMemberService;

    @Autowired
    protected ConfigService configService;

    @Autowired
    protected ObjectPoolFactory objectPoolFactory;

    public String getObjectApiName() {
        return null;
    }

    public String getObjectPoolKeyName() {
        return null;
    }

    protected IObjectData getObjectPoolById(String tenantId, String objectPoolId) {
        IObjectPoolService objectPoolService = objectPoolFactory.getObjectPoolService(getObjectApiName());
        return objectPoolService.getObjectPoolById(tenantId, objectPoolId);
    }

    public List<String> getPoolIds(List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return Lists.newArrayList();
        }
        Set<String> ids = objectDataList.stream().filter(x -> StringUtils.isNotBlank(ObjectDataUtil.getStringValue(x, getObjectPoolKeyName(), "")))
                .map(x -> ObjectDataUtil.getStringValue(x, getObjectPoolKeyName(), "")).collect(Collectors.toSet());
        return Lists.newArrayList(ids);
    }

    public List<IObjectData> getRemoveOutOwnerDataWhenCarry2Pool(PoolEmptyRule.EmptyRule emptyRule, List<IObjectData> objectDataList, ObjectAction action) {
        List<IObjectData> removedOutOwnerDataList = Lists.newArrayList();
        if (CollectionUtils.empty(objectDataList)) {
            return removedOutOwnerDataList;
        }
        switch (action) {
            case MOVE:
                if (Boolean.TRUE.equals(emptyRule.getCleanOutOwner())) {
                    removedOutOwnerDataList.addAll(objectDataList);
                }
                break;
            case RETURN:
            case TAKE_BACK:
                if (Boolean.TRUE.equals(emptyRule.getRecyclingOutTeamMember())) {
                    removedOutOwnerDataList.addAll(objectDataList);
                }
                break;
            default:
                throw new ValidateException( I18N.text("sfa.unknown.pool.operation"));
        }
        return removedOutOwnerDataList;
    }

    protected void savePoolClaimLog(User user, String objectPoolId, List<IObjectData> objectDataList, boolean isOutUser,
                                    boolean isTakeBack, String actionCode) {
        List<ObjectPoolActionModels.ObjectPoolClaimLog> objectPoolClaimLogs = Lists.newArrayList();
        ObjectPoolActionModels.ObjectPoolClaimLog claimLog;
        for (IObjectData objectData : objectDataList) {
            if (isOutUser && CollectionUtils.notEmpty(objectData.getOutOwner())) {
                claimLog = ObjectPoolActionModels.ObjectPoolClaimLog.builder()
                        .claimTime(System.currentTimeMillis()).objectId(objectData.getId())
                        .employeeId(String.valueOf(objectData.getOutOwner().get(0))).poolId(objectPoolId).build();
                objectPoolClaimLogs.add(claimLog);
            } else if (!isOutUser && StringUtils.isNotEmpty(ObjectDataUtil.getOwner(objectData))) {
                claimLog = ObjectPoolActionModels.ObjectPoolClaimLog.builder()
                        .claimTime(System.currentTimeMillis()).objectId(objectData.getId())
                        .employeeId(ObjectDataUtil.getOwner(objectData)).poolId(objectPoolId).build();
                objectPoolClaimLogs.add(claimLog);
            }
            if (isTakeBack && CollectionUtils.notEmpty(objectData.getOutOwner())) {
                claimLog = ObjectPoolActionModels.ObjectPoolClaimLog.builder()
                        .claimTime(System.currentTimeMillis()).objectId(objectData.getId())
                        .employeeId(String.valueOf(objectData.getOutOwner().get(0))).poolId(objectPoolId).build();
                objectPoolClaimLogs.add(claimLog);
            }
        }
        List<Map<String, Object>> insertMap = Lists.newArrayList();
        List<Map<String, Object>> deleteMap = Lists.newArrayList();
        for (ObjectPoolActionModels.ObjectPoolClaimLog objectPoolClaimLog : objectPoolClaimLogs) {
            String poolId = objectPoolClaimLog.getPoolId();
            if (StringUtils.isBlank(poolId)) {
                continue;
            }
            Map<String, Object> insertData = Maps.newHashMap();
            String id = serviceFacade.generateId();
            insertData.put(LtoFieldApiConstants.ID, id);
            insertData.put(LtoFieldApiConstants.NAME, id);
            insertData.put(Tenantable.TENANT_ID, user.getTenantId());
            insertData.put(LtoFieldApiConstants.POOL_ID, poolId);
            insertData.put(LtoFieldApiConstants.OBJECT_ID, objectPoolClaimLog.getObjectId());
            insertData.put("employee_id", objectPoolClaimLog.getEmployeeId());
            insertData.put("operation_time", objectPoolClaimLog.getClaimTime());
            insertData.put(LtoFieldApiConstants.API_NAME, getObjectApiName());
            insertData.put(LtoFieldApiConstants.CREATED_BY, user.getUpstreamOwnerIdOrUserId());
            insertData.put(LtoFieldApiConstants.CREATE_TIME, System.currentTimeMillis());
            insertData.put(LtoFieldApiConstants.LAST_MODIFIED_BY, user.getUpstreamOwnerIdOrUserId());
            insertData.put(LtoFieldApiConstants.LAST_MODIFIED_TIME, System.currentTimeMillis());
            insertData.put("action_code", actionCode);
            insertMap.add(insertData);
            Map<String, Object> deleteData = Maps.newHashMap();
            deleteData.put(Tenantable.TENANT_ID, user.getTenantId());
            deleteData.put(LtoFieldApiConstants.API_NAME, getObjectApiName());
            deleteData.put(LtoFieldApiConstants.POOL_ID, objectPoolClaimLog.getPoolId());
            deleteData.put(LtoFieldApiConstants.OBJECT_ID, objectPoolClaimLog.getObjectId());
            deleteData.put("employee_id", objectPoolClaimLog.getEmployeeId());
            deleteMap.add(deleteData);
        }
        try {
            CommonSqlUtil.batchDeleteData(user.getTenantId(), CLAIM_LOG_TABLE_NAME, deleteMap, Lists.newArrayList(LtoFieldApiConstants.ID, Tenantable.TENANT_ID));
            CommonSqlUtil.insertData(user.getTenantId(), CLAIM_LOG_TABLE_NAME, insertMap);
        } catch (Exception e) {
            log.error("savePoolClaimLog error");
            ExceptionUtil.throwCommonBusinessException();
        }
    }

}
