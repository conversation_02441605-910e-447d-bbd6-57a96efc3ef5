package com.facishare.crm.sfa.lto.activity.model;

import com.beust.jcommander.internal.Lists;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import lombok.Data;

import java.util.List;

public interface SFANewBusinessModel {
    List<String> ACCOUNT_PREF_FILEDS= Lists.newArrayList("name","tel","email","url","address","remark");
    List<String> OPPORTUNITY_PREF_FILEDS= Lists.newArrayList("name","amount","close_date","opp_discount");
    List<String> CONTACTS_PREF_FILEDS= Lists.newArrayList("name","job_title","department","gender","tel1","mobile1","email","add","primary_contact","influence_level","contact_competitor_strength","contact_our_strength","remark");
    //"account_position_id"
    List<String> DEPARTMENT_PREF_FILEDS= Lists.newArrayList("name","contact_id");

    class Field {
        public static final String ACCOUNT_ABSTRACT = "account_abstract";
        public static final String NEW_OPPORTUNITY_ABSTRACT = "new_opportunity_abstract";
        public static final String CONTACT_ABSTRACT = "contact_abstract";
        public static final String ACCOUNT_DEPARTMENT_ABSTRACT = "account_department_abstract";
    }
    @Data
    class Result{
        private String id;
        private String name;
        private List<Abstracts> abstracts;
        private ObjectDataDocument needUpdObjectData;
        private ObjectDataDocument dbObjectData;
        private List<String> columns ;
        private String creatTime;
    }

    @Data
    class Abstracts{

        private String uniqueSign;
        private String label;
        private String context;
    }

     interface AbstractsFieldEnum {
        String getCode();
        String getDesc();
    }


    enum AccountAbstractsFieldEnum implements AbstractsFieldEnum{
        business_require_changes("business_require_changes", "业务需求变化"),
        account_addr_changes("account_addr_changes", "客户联系地址变化"),
        account_coontact_changes("account_coontact_changes", "客户方主要联系人或决策人变化"),
        account_other_changes("account_other_changes", "与客户相关的其他信息变动");

        private String code;
        private String desc;

        AccountAbstractsFieldEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static String getDescByCode(String code) {
            for (AccountAbstractsFieldEnum feedTypeEnum : AccountAbstractsFieldEnum.values()) {
                if (feedTypeEnum.getCode().equals(code)) {
                    return feedTypeEnum.getDesc();
                }
            }
            return "";
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }


    }
    enum OpportunityAbstractsFieldEnum implements AbstractsFieldEnum{
        business_require_changes("business_require_changes", "业务需求描述"),
        product_info("product_info", "产品或服务信息"),
        budget_amount("budget_amount", "采购预算金额"),
        payment_plan("payment_plan", "项目时间节点和回款计划"),
        main_contact("main_contact", "主要联系人和决策人"),
        related_info("related_info", "与商机相关的其他信息变动");

        private String code;
        private String desc;

        OpportunityAbstractsFieldEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static String getDescByCode(String code) {
            for (OpportunityAbstractsFieldEnum feedTypeEnum : OpportunityAbstractsFieldEnum.values()) {
                if (feedTypeEnum.getCode().equals(code)) {
                    return feedTypeEnum.getDesc();
                }
            }
            return "";
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }
    enum ContactAbstractsFieldEnum implements AbstractsFieldEnum{
        contact_name("name", "联系人姓名"),
        contact_info("mobile1", "联系方式信息"),
        position_info("job_title", "职务信息"),
        join_identity("join_identity", "联系人参与身份"),
        departments("departments", "所属部门"),
        business_needs("business_needs", "关注的业务需求"),
        evaluate("evaluate", "对我方和竞争对手的对比评价"),
        other_info("other_info", "与联系人相关的其他信息");

        private String code;
        private String desc;

        ContactAbstractsFieldEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static String getDescByCode(String code) {
            for (ContactAbstractsFieldEnum feedTypeEnum : ContactAbstractsFieldEnum.values()) {
                if (feedTypeEnum.getCode().equals(code)) {
                    return feedTypeEnum.getDesc();
                }
            }
            return "";
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }
    enum DepartmentAbstractsFieldEnum implements AbstractsFieldEnum{
        departments_info("departments_info", "客户的组织架构和部门信息"),
        position_change("position_change", "部门负责人及职责变动"),
        other_info("other_info", "与客户部门相关的其他信息");

        private String code;
        private String desc;

        DepartmentAbstractsFieldEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static String getDescByCode(String code) {
            for (DepartmentAbstractsFieldEnum feedTypeEnum : DepartmentAbstractsFieldEnum.values()) {
                if (feedTypeEnum.getCode().equals(code)) {
                    return feedTypeEnum.getDesc();
                }
            }
            return "";
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }
}
