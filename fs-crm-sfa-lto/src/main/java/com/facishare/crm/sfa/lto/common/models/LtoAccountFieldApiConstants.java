package com.facishare.crm.sfa.lto.common.models;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.google.common.base.Strings;

public final class LtoAccountFieldApiConstants {
    public static final String CLAIMED_TIME = "claimed_time";
    public static final String ACCOUNT_STATUS = "account_status";
    public static final String HIGH_SEAS_ID = "high_seas_id";
    public static final String OWNER_MODIFIED_TIME = "owner_modified_time";
    public static final String TRANSFER_COUNT = "transfer_count";
    public static final String RECYCLED_REASON = "recycled_reason";
    public static final String RETURNED_TIME = "returned_time";
    public static final String BACK_REASON = "back_reason";
    public static final String LAST_FOLLOWED_TIME = "last_followed_time";
    public static final String OWNER_CHANGED_TIME = "owner_changed_time";

    private LtoAccountFieldApiConstants() {
        throw new IllegalStateException("Utility class");
    }

    public enum BizStatus {
        UN_ALLOCATED("unallocated", "未分配"),
        ALLOCATED("allocated", "已分配");

        private final String value;
        private final String label;

        BizStatus(String value, String label) {
            this.value = value;
            this.label = label;
        }

        public String getValue() {
            return this.value;
        }

        public String getLabel() {
            return this.label;
        }

        public static BizStatus of(String accountStatusValue) {
            if (Strings.isNullOrEmpty(accountStatusValue)) {
                throw new ValidateException(I18N.text("paas.udobj.unknow_life_status", accountStatusValue));
            } else {
                BizStatus[] var2 = values();
                int var3 = var2.length;

                for (int var4 = 0; var4 < var3; ++var4) {
                    BizStatus accountStatus = var2[var4];
                    if (accountStatus.getValue().equals(accountStatusValue)) {
                        return accountStatus;
                    }
                }

                throw new ValidateException(I18N.text("paas.udobj.unknow_life_status", accountStatusValue));
            }
        }
    }

}
