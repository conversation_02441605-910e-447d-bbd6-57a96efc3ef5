package com.facishare.crm.sfa.prm.platform.infrastructure.execution;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * ============================================================
 *
 * @Description: step 执行器
 * @CreatedBy: Sundy on 2025-02-28
 * ============================================================
 */
@Component
@Slf4j
public class InitProcessor implements ApplicationContextAware {
    private Map<String, List<InitStep<?>>> stepMapping;

    @Override
    @SuppressWarnings("unchecked")
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, InitStep> springBeanMapping = applicationContext.getBeansOfType(InitStep.class);
        this.stepMapping = springBeanMapping.values()
                .stream()
                .map(step -> (InitStep<?>) step)  // 显式类型转换
                .collect(Collectors.groupingBy(
                        InitStep::getTaskName,
                        Collectors.mapping(
                                step -> step,
                                Collectors.collectingAndThen(
                                        Collectors.toList(),
                                        this::sortSteps
                                )
                        )
                ));
    }

    private List<InitStep<?>> sortSteps(List<InitStep<?>> steps) {
        return steps.stream()
                .sorted(Comparator.comparingInt(InitStep::getOrder))
                .collect(Collectors.toList());
    }

    public <T> void executeTask(String taskName, T param) {
        List<InitStep<?>> steps = stepMapping.get(taskName);
        if (steps == null || steps.isEmpty()) {
            log.warn("未找到任务组：{}", taskName);
            return;
        }

        for (InitStep<?> step : steps) {
            @SuppressWarnings("unchecked")
            InitStep<T> typedTask = (InitStep<T>) step;
            boolean success = executeWithRetry(typedTask, param);
            if (!success && !step.allowFailure()) {
                log.warn("任务 [{}] 执行失败且不允许失败，终止执行！taskName:{}",
                        step.getClass().getSimpleName(), taskName);
                return;
            }
        }
        log.info("任务组 [{}] 全部执行完毕！", taskName);
    }

    private <T> boolean executeWithRetry(InitStep<T> step, T param) {
        int retries = 0;
        while (retries <= step.getMaxRetries()) {
            try {
                boolean result = step.execute(param);
                if (result) {
                    step.onSuccess(param);
                    return true;
                }
            } catch (Exception e) {
                log.error("任务执行异常: {}", e.getMessage(), e);
                step.onFailure(param, e);
            }

            retries++;
            if (retries <= step.getMaxRetries()) {
                try {
                    Thread.sleep(step.getRetryInterval());
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return false;
                }
                log.warn("任务 [{}] 第{}次重试", step.getClass().getSimpleName(), retries);
            }
        }
        return false;
    }
}