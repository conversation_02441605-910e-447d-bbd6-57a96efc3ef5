package com.facishare.crm.sfa.lto.utils;

import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> lik
 * @date : 2024/4/8 11:17
 */
@Slf4j
public class LeadsPoolUtil {
    private static final FsGrayReleaseBiz gray = FsGrayRelease.getInstance("sfa");
    public static final String DAY="day";
    public static final String MONTH="month";
    public static final String chooseKey="sfa:GrayTimeClaimLimit:tenant:%s:user:%s:poolid:%s:choose:%s";
    public static final String allocateKey="sfa:GrayTimeClaimLimit:tenant:%s:user:%s:poolid:%s:allocate:%s";
    public static boolean isGrayLeadsAllocateLimit(String tenantId) {
        return gray.isAllow("sfa_leads_allocate_limit_tenantId", tenantId);
    }
    public static boolean isGraySfaAllocateContinue(String tenantId) {
        return gray.isAllow("sfa_allocate_continue", tenantId);
    }

    /**
     * 线索分配，跳过非工作日灰度
     * @param tenantId
     * @return
     */
    public static boolean isGraySfaAllocateSkipWorkFree(String tenantId) {
        return gray.isAllow("sfa_allocate_skip_work_free", tenantId);
    }

    /**
     * 导入生成行为记录 activeRecords
     */

    public static boolean isGraySfaImportBehaviorActiveRecords(String tenantId) {
        return gray.isAllow("sfa_import_behavior_active_records", tenantId);
    }

    /**
     * 互联企业批量新建公海线索池 SfaOutTenantPoolBatchAdd
     */

    public static boolean isGraySfaOutTenantPoolBatchAdd(String tenantId) {
        return gray.isAllow("sfa_out_tenant_pool_batch_add", tenantId);
    }

    /**
     * 线索查重匹配空值
     */
    public static boolean isGraySfaLeadsDuplicateEmpty(String tenantId) {
        return gray.isAllow("sfa_leads_duplicate_empty", tenantId);
    }

}
