package com.facishare.crm.sfa.prm.platform.utils;

import com.facishare.paas.appframework.common.service.dto.InternationalItem;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-03-05
 * ============================================================
 */
public class I18NUtils {
    public static <T> T getDataI18nFieldValue(IObjectData data, String fieldName, Class<T> clazz) {
        String i18nKey = fieldName + "__r";
        T value = data.get(i18nKey, clazz);
        if (value == null) {
            return data.get(fieldName, clazz);
        }
        if (value instanceof String && StringUtils.isBlank((String) value)) {
            return data.get(fieldName, clazz);
        }
        return value;
    }
    public static String getDataI18nName(@NotNull IObjectData data) {
        return getDataI18nFieldValue(data, IObjectData.NAME, String.class);
    }

    /**
     * 参数定义 <a href="https://wiki.firstshare.cn/pages/viewpage.action?pageId=86769948">多语接口参考链接</a>
     */
    public static InternationalItem buildInternationalItem(String i18nKey, List<String> i18nParameters, Map<String, String> defaultI18nParameterValuesMapping) {
        return InternationalItem.builder()
                .internationalKey(i18nKey)
                .internationalParameters(i18nParameters)
                .defaultParameterValues(defaultI18nParameterValuesMapping)
                .build();
    }

    public static InternationalItem buildInternationalItem(String i18nKey, List<String> i18nParameters) {
        return buildInternationalItem(i18nKey, i18nParameters, Maps.newHashMap());
    }

    public static InternationalItem buildInternationalItem(String i18nKey) {
        return buildInternationalItem(i18nKey, Lists.newArrayList(), Maps.newHashMap());
    }

    public static String getObjectDisplayNameI18nKey(String objectApiName) {
        if (StringUtils.isBlank(objectApiName)) {
            return null;
        }
        return String.format("#I18N#%s.attribute.self.display_name", objectApiName);
    }
    public static String getLanguage() {
        return Optional.ofNullable(RequestContextManager.getContext())
                .map(RequestContext::getLang)
                .map(Lang::getValue)
                .orElse(Lang.zh_CN.getValue());
    }

    public static Lang getLang() {
        return Optional.ofNullable(RequestContextManager.getContext())
                .map(RequestContext::getLang)
                .orElse(Lang.zh_CN);
    }
}
