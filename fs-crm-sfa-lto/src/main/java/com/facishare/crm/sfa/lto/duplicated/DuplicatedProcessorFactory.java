package com.facishare.crm.sfa.lto.duplicated;

import com.facishare.crm.sfa.lto.duplicated.models.DuplicatedModels;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

@Component
@Slf4j
public class DuplicatedProcessorFactory implements ApplicationContextAware {
    private Map<String, DuplicatedDataProcessor> duplicatedProcessorMap = Maps.newHashMap();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        initDuplicatedProcessorMap(applicationContext);
    }

    private void initDuplicatedProcessorMap(ApplicationContext applicationContext) {
        Map<String, DuplicatedDataProcessor> springBeanMap = applicationContext.getBeansOfType(DuplicatedDataProcessor.class);
        springBeanMap.values().forEach(provider -> {
            if (StringUtils.isNotEmpty(provider.getProcessorModel())) {
                duplicatedProcessorMap.put(provider.getProcessorModel(), provider);
            }
        });
    }

    public DuplicatedDataProcessor getDuplicatedDataProcessor(DuplicatedModels.TriggerAction action) {
        Optional<DuplicatedDataProcessor> processor = duplicatedProcessorMap.values().stream().filter(x -> x.matchProcessor(action)).findFirst();
        return processor.isPresent() ? processor.get() : duplicatedProcessorMap.getOrDefault("DEFAULT_PROCESSOR", null);
    }
}
