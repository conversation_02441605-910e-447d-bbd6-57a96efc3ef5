package com.facishare.crm.sfa.lto.activity.mongo;

import lombok.Data;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Entity;

import java.io.Serializable;
import java.util.List;

@Data
@Entity(value = "interactive_paragraph_", noClassnameStored = true)
public class ParagraphDocument implements Serializable {

    @BsonId
    private ObjectId id;

    // 企业id
    private String tenantId;
    
    // 段落总结
    private String summary;
    
    // 标记
    private List<String> tags;
    
    // 原始语料
    private List<String> documentIdList;
    
    // 涉及到的发言user
    private List<String> activityUserIdList;
    
    // 存储对象apiName
    private String objectApiName;
    
    // 存储对象id
    private String objectId;
    
    // 序列号
    private Integer seqNo;
    
    // 开始时间
    private String startTime;
    
    // 结束时间
    private String endTime;
    
    // 是否删除
    private Boolean isDeleted;
    
    // 客户
    private String accountId;
    
    // 线索
    private String leadsId;
    
    // 商机
    private String newOpportunityId;
    
    // 创建时间
    private Long createTime;
    
    // 最后更新时间
    private Long lastUpdateTime;

    //mongo,text
    private String type;

    //打标签的原因
    private String reason;

    //没打标签的原因
    private String noReason;
}
