package com.facishare.crm.sfa.prm.core.constants;

/**
 * ============================================================
 *
 * @Description: I18n key
 * @CreatedBy: Sundy on 2025-02-27
 * ============================================================
 */
public interface PrmI18NConstants {
    /**
     * 无法匹配到参数:{0} 对应的值类型。
     */
    String PRM_ENUM_PARAM_TYPE_ERROR = "prm.enum.param.type.error";
    /**
     * 渠道管理
     */
    String SFA_CHANNEL_MANAGEMENT_TITLE = "sfa.channel.management.title";
    /**
     * 渠道准入状态变更记录
     */
    String SFA_CHANNEL_STATUS_RECORD_TITLE = "sfa.channel.status.record.title";
    /**
     * 时间必须大于等于 0。
     */
    String PRM_CHANNEL_TIME_MUST_GREATER_THAN_ZERO = "prm.channel.time.must.greater.than.zero";
}
