package com.facishare.crm.sfa.lto.operations.task;

import com.facishare.crm.sfa.lto.operations.OperationsConstant;
import com.facishare.crm.sfa.lto.utils.ActionContextUtil;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.api.service.ICommonSqlService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.CommonSqlOperator;
import com.facishare.paas.metadata.impl.search.WhereParam;
import com.facishare.paas.metadata.util.IdUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
public class OperationsTaskTemplateUtil {
    private OperationsTaskTemplateUtil() {}

    private static final String TABLE_NAME = "biz_setting_rule_member";
    private static final String RULE_ID = "rule_id";
    private static final String MEMBER_ID = "member_id";
    private static final String MEMBER_TYPE = "member_type";
    private static final String TASK_CC_API_NAME = OperationsConstant.OPERATION_STRATEGY_TASK_TEMPLATE_OBJECT_API_NAME + "." + OperationsConstant.TASK_CC;
    private static final String TASK_EXECUTORS_API_NAME = OperationsConstant.OPERATION_STRATEGY_TASK_TEMPLATE_OBJECT_API_NAME + "." + OperationsConstant.TASK_EXECUTORS;

    public static void getMemberField(IObjectData taskTemplate, ICommonSqlService commonSqlService) {
        List<Map<String, Object>> ruleValueList;
        try {
            List<WhereParam> whereParams = buildAllWhereParams(taskTemplate.getTenantId(), Collections.singletonList(taskTemplate));
            ruleValueList = select0(taskTemplate.getTenantId(), whereParams, commonSqlService);
        } catch (MetadataServiceException e) {
            log.error("Query " + TABLE_NAME + " error", e);
            return;
        }
        setMemberField(taskTemplate, ruleValueList);
    }

    public static void getMemberField(List<IObjectData> taskTemplateList, ICommonSqlService commonSqlService) {
        String tenantId = taskTemplateList.get(0).getTenantId();
        List<Map<String, Object>> rulesValueList;
        try {
            List<WhereParam> whereParams = buildAllWhereParams(tenantId, taskTemplateList);
            rulesValueList = select0(tenantId, whereParams, commonSqlService);
        } catch (MetadataServiceException e) {
            log.error("Query " + TABLE_NAME + " error", e);
            return;
        }
        Map<String, List<Map<String, Object>>> ruleValueGroup = rulesValueList.stream().collect(Collectors.groupingBy(map -> String.valueOf(map.get(RULE_ID))));
        for (IObjectData taskTemplate : taskTemplateList) {
            setMemberField(taskTemplate, ruleValueGroup.getOrDefault(taskTemplate.getId(), Collections.emptyList()));
        }
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    private static List<Map<String, Object>> select0(String tenantId, List<WhereParam> whereParams, ICommonSqlService commonSqlService) throws MetadataServiceException {
        List<Map> select = commonSqlService.select(TABLE_NAME, whereParams, ActionContextUtil.createActionContext(tenantId));
        return select.stream().map(e -> (Map<String, Object>) e).collect(Collectors.toList());
    }

    private static void setMemberField(IObjectData taskTemplate, List<Map<String, Object>> ruleValueList) {
        Map<String, List<String>> taskCC = new HashMap<>();
        Map<String, List<String>> taskExecutors = new HashMap<>();
        for (Map<String, Object> map : ruleValueList) {
            if (TASK_CC_API_NAME.equals(map.get(IObjectData.DESCRIBE_API_NAME))) {
                convertToFieldValue(map, taskCC);
            }
            if (TASK_EXECUTORS_API_NAME.equals(map.get(IObjectData.DESCRIBE_API_NAME))) {
                convertToFieldValue(map, taskExecutors);
            }
        }
        taskTemplate.set(OperationsConstant.TASK_CC, taskCC);
        taskTemplate.set(OperationsConstant.TASK_EXECUTORS, taskExecutors);
    }

    @SuppressWarnings("unchecked")
    public static void saveMemberField(IObjectData taskTemplate, ICommonSqlService commonSqlService) {
        if (isInvalid(taskTemplate)) {
            return;
        }
        String dataId = taskTemplate.getId();
        String tenantId = taskTemplate.getTenantId();
        List<Map<String, Object>> dataList = new ArrayList<>();
        Object taskCCObj = taskTemplate.get(OperationsConstant.TASK_CC);
        if (taskCCObj instanceof Map) {
            covertToRuleValue(dataId, tenantId, TASK_CC_API_NAME, (Map<String, List<String>>) taskCCObj, dataList);
        }
        Object taskExecutorsObj = taskTemplate.get(OperationsConstant.TASK_EXECUTORS);
        if (taskExecutorsObj instanceof Map) {
            covertToRuleValue(dataId, tenantId, TASK_EXECUTORS_API_NAME, (Map<String, List<String>>) taskExecutorsObj, dataList);
        }
        try {
            commonSqlService.insert(TABLE_NAME, dataList, ActionContextUtil.createActionContext(tenantId));
        } catch (MetadataServiceException e) {
            log.error("Insert " + TABLE_NAME + " error", e);
        }
    }

    public static int deleteMemberField(IObjectData taskTemplate, ICommonSqlService commonSqlService) {
        if (isInvalid(taskTemplate)) {
            return 0;
        }
        try {
            List<WhereParam> whereParams = buildAllWhereParams(taskTemplate.getTenantId(), Collections.singletonList(taskTemplate));
            return commonSqlService.delete(TABLE_NAME, whereParams, ActionContextUtil.createActionContext(taskTemplate.getTenantId()));
        } catch (MetadataServiceException e) {
            log.error("Delete " + TABLE_NAME + " error", e);
        }
        return 0;
    }

    private static boolean isInvalid(IObjectData taskTemplate) {
        String dataId = taskTemplate.getId();
        String tenantId = taskTemplate.getTenantId();
        if (dataId == null || tenantId == null) {
            log.error("Template id or tenant id is null");
            return true;
        }
        return false;
    }

    private static List<Object> allMemberFieldApiName() {
        List<Object> list = new ArrayList<>();
        list.add(TASK_CC_API_NAME);
        list.add(TASK_EXECUTORS_API_NAME);
        return list;
    }

    private static List<WhereParam> buildAllWhereParams(String tenantId, List<IObjectData> taskTemplateList) {
        WhereParam wp1 = new WhereParam();
        wp1.setOperator(CommonSqlOperator.IN);
        wp1.setColumn(RULE_ID);
        wp1.setValue(taskTemplateList.stream().map(DBRecord::getId).collect(Collectors.toList()));
        WhereParam wp2 = new WhereParam();
        wp2.setOperator(CommonSqlOperator.EQ);
        wp2.setColumn(Tenantable.TENANT_ID);
        wp2.setValue(Collections.singletonList(tenantId));
        WhereParam wp3 = new WhereParam();
        wp3.setOperator(CommonSqlOperator.IN);
        wp3.setColumn(IObjectData.DESCRIBE_API_NAME);
        wp3.setValue(allMemberFieldApiName());
        List<WhereParam> whereParams = new ArrayList<>();
        whereParams.add(wp1);
        whereParams.add(wp2);
        whereParams.add(wp3);
        return whereParams;
    }

    private static void convertToFieldValue(Map<String, Object> ruleValue, Map<String, List<String>> fieldValue) {
        Optional.of(ruleValue).map(m -> m.get(MEMBER_TYPE)).flatMap(MemberType::fromType).ifPresent(type -> {
            if (type == MemberType.USER) {
                fieldValue.computeIfAbsent(type.key, k -> new ArrayList<>()).add(String.valueOf(ruleValue.get(MEMBER_ID)));
            }
            if (type == MemberType.EXTRA) {
                List<String> list = fieldValue.computeIfAbsent(type.key, k -> new ArrayList<>());
                MemberTypeExtra.from(ruleValue.get(MEMBER_ID)).map(MemberTypeExtra::toString).ifPresent(list::add);
            }
        });
    }

    private static void covertToRuleValue(String dataId, String tenantId, String apiName, Map<String, List<String>> fieldValue, List<Map<String, Object>> dataList) {
        for (Map.Entry<String, List<String>> entry : fieldValue.entrySet()) {
            MemberType.fromKey(entry.getKey()).ifPresent(type -> {
                for (String value : entry.getValue()) {
                    Map<String, Object> data = new HashMap<>();
                    data.put("id", IdUtil.generateId());
                    data.put(Tenantable.TENANT_ID, tenantId);
                    data.put(RULE_ID, dataId);
                    data.put(IObjectData.DESCRIBE_API_NAME, apiName);
                    data.put(MEMBER_TYPE, type.type);
                    data.put(MEMBER_ID, value);
                    data.put(DBRecord.IS_DELETED, 0);
                    data.put(DBRecord.CREATE_TIME, System.currentTimeMillis());
                    data.put(DBRecord.CREATED_BY, User.SUPPER_ADMIN_USER_ID);
                    dataList.add(data);
                }
            });
        }
    }
}
