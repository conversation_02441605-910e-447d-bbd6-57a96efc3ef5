package com.facishare.crm.sfa.lto.integral.common.constant;

import com.facishare.paas.I18N;

public class RuleDataConstant {
    /** 规则状态：启用*/
    public static final int RULE_STATUS_ACTIVE = 1;
    /** 规则状态：停用*/
    public static final int RULE_STATUS_STOP = 0;

    public static final String MATERIAL_API_NAME_ANY_ONE = "any-one";

    public static final String MATERIAL_LABEL_ANY_ONE = "sfa.behavior.integral.material.anyone";

    /** 最多可设置10组规则*/
    public static final int MAX_RULE_GROUP = 10;

    /** 每组内最多可设置50条规则*/
    public static final int MAX_RULE_ITEM = 50;

    public static final String CRM_BEHAVIOR_INTEGRAL = "CRM_BEHAVIOR_INTEGRAL";

    public static final String LICENSE_BEHAVIOR_INTEGRAL_APP = "behavior_integral_app";

    public static String getAnyOneLabel(){
        return I18N.text(MATERIAL_LABEL_ANY_ONE);
    }
}
