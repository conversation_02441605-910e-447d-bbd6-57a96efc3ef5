package com.facishare.crm.sfa.lto.accountreerelation.factory;

import com.facishare.crm.sfa.lto.accountreerelation.IMatchService;
import com.facishare.crm.sfa.lto.accountreerelation.MatchServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> lik
 * @date : 2024/2/29 18:14
 */
@Component
public class MatchFactory {

    @Autowired
    private MatchServiceImpl matchService;

    public static final Map<String, IMatchService> REMIND_SERVICE = new HashMap<>();

    @PostConstruct
    public void init(){
        REMIND_SERVICE.put("common", matchService);
    }

    public IMatchService getMatchService(String describeApiName){
        if(REMIND_SERVICE.containsKey(describeApiName)){
            return REMIND_SERVICE.get(describeApiName);
        }
        return REMIND_SERVICE.get("common");
    }
}
