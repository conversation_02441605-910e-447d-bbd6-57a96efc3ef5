package com.facishare.crm.sfa.lto.procurement;

import com.facishare.crm.sfa.lto.procurement.models.LtoProcurementConstants.CommonConstants;
import com.facishare.crm.sfa.lto.procurement.models.LtoProcurementConstants.ComparisonConf;
import com.facishare.crm.sfa.lto.procurement.models.LtoProcurementConstants.ProcurementEnterprise;
import com.facishare.crm.sfa.lto.procurement.models.LtoProcurementConstants.ProcurementInfo;
import com.facishare.crm.sfa.lto.procurement.models.LtoProcurementModel.ProcurementMessage;
import com.facishare.enterprise.common.util.CollectionUtil;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.util.List;
import java.util.Map;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName com.facishare.crm.sfa.lto.procurement.ProcurementEnterpriseService
 * @Description
 * <AUTHOR>
 * @Date 2022/11/08 18:20
 * @Version 1.0
 **/
@Slf4j
@Component
public class LtoProcurementEnterpriseService {
    @Autowired
    private LtoProcurementDataService ltoProcurementDataService;

    public void loadCodeAndNameMsg(IObjectData iod, ProcurementMessage message, String tenantId){
        if(iod == null) return;

        message.setSrcId(StringUtil.toSafeString(iod.get(CommonConstants._ID)));

        List<String> callerIds = iod.get(ProcurementInfo.CALLER_ENTERPRISE, List.class);
        log.info("callerIds:{}", callerIds);
        if(!CollectionUtil.isEmpty(callerIds)){
            Map<String, List<String>> dataMap = callerCreditCodeAndName(callerIds, tenantId);
            if(!CollectionUtil.isEmpty(dataMap)){
                message.setCallerCreditCode(dataMap.get(ComparisonConf.TABLE_FIELD_CREDIT_ID));
                //招标单位与线索比对，通过name
                message.setCallerName(dataMap.get(ComparisonConf.TABLE_FIELD_NAME));
            }
        }
        List<String> winnerIds = iod.get(ProcurementInfo.WINNER_ENTERPRISE, List.class);
        log.info("winnerIds:{}", winnerIds);
        if(!CollectionUtil.isEmpty(winnerIds) ){
            Map<String, List<String>> dataMap = callerCreditCodeAndName(winnerIds, tenantId);
            if(!CollectionUtil.isEmpty(dataMap)){
                message.setWinnerCreditCode(dataMap.get(ComparisonConf.TABLE_FIELD_CREDIT_ID));
                message.setWinnerName(dataMap.get(ComparisonConf.TABLE_FIELD_NAME));
            }
        }
    }

    private Map<String, List<String>> callerCreditCodeAndName(List<String> ids, String tenantId){
        List<IObjectData> iObjectDataList = ltoProcurementDataService.findByIdList(ids, tenantId, ProcurementEnterprise.NAME_OBJ);
        log.info("callerCreditCodeAndName {}", iObjectDataList);
        if(!CollectionUtil.isEmpty(iObjectDataList)){
            List<String> codes = Lists.newArrayList();
            List<String> names = Lists.newArrayList();

            Map<String , List<String>> dataMap = Maps.newHashMap();
            for(IObjectData iod : iObjectDataList){
                String creditCode = (String)iod.get(ComparisonConf.TABLE_FIELD_CREDIT_ID);
                if(StringUtil.isNotEmpty(creditCode)){
                    codes.add(creditCode);
                }

                String name = (String) iod.get(ComparisonConf.TABLE_FIELD_NAME);
                if (StringUtil.isNotEmpty(name)) {
                    names.add(name);
                }

            }
            if(!CollectionUtil.isEmpty(codes)){
                dataMap.put(ComparisonConf.TABLE_FIELD_CREDIT_ID, codes);
            }
            if(!CollectionUtil.isEmpty(names)){
                dataMap.put(ComparisonConf.TABLE_FIELD_NAME, names);
            }
            return dataMap;
        }
        return Maps.newHashMap();
    }
}