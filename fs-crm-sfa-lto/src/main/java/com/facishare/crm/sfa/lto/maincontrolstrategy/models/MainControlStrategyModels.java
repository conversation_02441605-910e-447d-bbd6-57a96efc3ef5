package com.facishare.crm.sfa.lto.maincontrolstrategy.models;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> lik
 * @date : 2023/4/11 15:28
 */

public interface MainControlStrategyModels {
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class TaskArg {
        private String tenantId;
        private String name;
        private String op;
        private String entityId;
        private String dataSource;
        private List<Map> body;
    }

    class Field {
        public static final String OBJECT_DESCRIBE_API_NAME = "object_describe_api_name";
        public static final String FIELDS = "fields";
        public static final String FIELD_API_NAME = "field_api_name";
        public static final String FIELD_TYPE = "field_type";
        public static final String TARGET_DESCRIBE_API_NAME = "target_describe_api_name";

    }
}
