package com.facishare.crm.sfa.lto.riskbrain.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> lik
 * @date : 2023/1/31 19:35
 */

public interface RiskBrainModel {
    @Data
    class Arg {
        @JSONField(name = "tenantId")
        @JsonProperty("tenantId")
        private String tenantId;
        @JSONField(name = "userName")
        @JsonProperty("userName")
        private String userName;
        @JSONField(name = "password")
        @JsonProperty("password")
        private String password;
        @JSONField(name = "originalTenantId")
        @JsonProperty("originalTenantId")
        private String originalTenantId;
        @JSONField(name = "tenantCode")
        @JsonProperty("tenantCode")
        private String tenantCode;
        @JsonProperty("name")
        private String name;
    }

    @Data
    class PushArg {
        @JSONField(name = "tenantId")
        @JsonProperty("tenantId")
        private String tenantId;
        @JsonProperty("name")
        private String name;
        @JsonProperty("uniformSocialCreditCode")
        private List<String> uniformSocialCreditCode;
        @JsonProperty("pushType")
        private String pushType;
    }

    @Data
    class Result {
        private String token;
        private String companyld;
        private String msg;
    }

    @Data
    class Message {
        private String tenantId; //企业ID
        private String licenseVersion; //产品编码，不同类型产品按照指定编码返回（主版本，行业套件，应用，资源包）
        private String licenseCode; //
        private String orderNumber; //订单标号
        private long createTime; //产品创建时间
        private long startTime; //产品开始时间
        private long expiredTime; //产品结束时间
    }

    @Data
    class AccountCreateApplyResult {
        String queryName;
        AccountCreateApplyQueryResult queryResult;
    }

    @Data
    class AccountCreateApplyQueryResult {
        String orderId;
        String accountName;
        String encryptedPassword;
        String tenantCode;
    }

}
