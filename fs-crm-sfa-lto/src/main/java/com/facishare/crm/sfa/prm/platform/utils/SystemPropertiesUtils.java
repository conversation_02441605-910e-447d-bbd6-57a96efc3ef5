package com.facishare.crm.sfa.prm.platform.utils;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.net.InetAddress;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-03-05
 * ============================================================
 */
@Slf4j
public class SystemPropertiesUtils {
    private static String appName = "unknown";
    private static String hostIp = "unknown";

    static {
        try {
            Object processName = System.getProperties().getOrDefault("process.name", System.getProperty("user.name"));
            if (processName != null) {
                appName = processName.toString();
            }
            InetAddress address = InetAddress.getLocalHost();
            if (address != null && StringUtils.isNotBlank(address.getHostAddress())) {
                hostIp = address.getHostAddress();
            }
        } catch (Exception e) {
            log.error("SystemPropertiesUtils#init SystemProperties fail", e);
        }
    }

    public static String getHostIp() {
        return hostIp;
    }

    public static String getAppName() {
        return appName;
    }
}
