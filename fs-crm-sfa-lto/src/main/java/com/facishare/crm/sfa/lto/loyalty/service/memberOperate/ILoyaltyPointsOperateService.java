package com.facishare.crm.sfa.lto.loyalty.service.memberOperate;


import com.facishare.crm.sfa.lto.loyalty.model.Loyalty;

import java.util.Set;

public interface ILoyaltyPointsOperateService {

    long STOP_WATCH_TIME = 200L;

    Loyalty.PointsOperationParam.Type type();

    void action(Loyalty.PointsOperationParam param);

    void fallback(Loyalty.FallbackUpdateData updateData, Loyalty.FallbackInfo fallbackInfo);

    Set<String> lockMemberIds(Loyalty.PointsOperationParam param);
}
