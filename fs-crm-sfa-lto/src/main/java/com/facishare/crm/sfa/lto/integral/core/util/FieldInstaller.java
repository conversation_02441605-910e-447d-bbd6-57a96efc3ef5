package com.facishare.crm.sfa.lto.integral.core.util;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FieldLayoutPojo;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.LayoutLogicService;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.ui.layout.ILayout;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 字段装配器
 * <AUTHOR>
 */
@Slf4j
@Builder
public class FieldInstaller {
    private IObjectDescribeService objectDescribeService;
    private LayoutLogicService layoutLogicService;
    private String tenantId;
    private IObjectDescribe objectDescribe;
    private List<IFieldDescribe> fieldDescribes;

    public void install() {
        try {
            installDescribe();
        } catch (MetadataServiceException e) {
            log.error("installDescribe error,tenantId:{}", tenantId, e);
        }
        installLayout();
    }

    private void installDescribe() throws MetadataServiceException {
        objectDescribeService.addCustomFieldDescribe(objectDescribe, fieldDescribes);
    }

    private void installLayout() {
        List<ILayout> detailLayouts = layoutLogicService.getDetailLayouts(tenantId, objectDescribe);
        detailLayouts.forEach(layout -> {
            fieldDescribes.forEach(fieldDescribe ->
                    LayoutExt.of(layout).addField(fieldDescribe, getFieldLayoutPojo(fieldDescribe)));
            User user = User.builder()
                    .tenantId(tenantId)
                    .userId(User.SUPPER_ADMIN_USER_ID)
                    .build();
            layoutLogicService.updateLayout(user, layout);
        });
    }

    private FieldLayoutPojo getFieldLayoutPojo(IFieldDescribe fieldDescribe) {
        FieldLayoutPojo fieldLayoutPojo = new FieldLayoutPojo();
        //后续若需要初始化多个字段时，Readonly需要动态获取
        fieldLayoutPojo.setReadonly(true);
        fieldLayoutPojo.setRequired(fieldDescribe.isRequired());
        fieldLayoutPojo.setRenderType(fieldDescribe.getType());
        return fieldLayoutPojo;
    }
}
