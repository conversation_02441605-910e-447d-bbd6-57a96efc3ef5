package com.facishare.crm.sfa.lto.integral.core.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

public interface FindSupportObjects {

    @Data
    class Result {

        @JSONField(name = "objectList")
		@JsonProperty("objectList")
        private List<SimpleObjectDescribe> objectList;

        public static Result build(List<IObjectDescribe> describeList, List<String> usedApiNames) {
            Result result = new Result();
            List<SimpleObjectDescribe> simpleObjectDescribes = describeList.stream().map(x -> {
                Boolean used = usedApiNames.contains(x.getApiName());
                return SimpleObjectDescribe.builder()
                        .describeApiName(x.getApiName())
                        .objectName(x.getDisplayName())
                        .rule(used)
                        .build();
            }).collect(Collectors.toList());
            result.setObjectList(simpleObjectDescribes);

            return result;
        }
    }

    @Data
    @Builder
    class SimpleObjectDescribe {

        @JSONField(name = "object_name")
		@JsonProperty("object_name")
        private String objectName;

        @JSONField(name = "obj_api_name")
		@JsonProperty("obj_api_name")
        private String describeApiName;

        @JSONField(name = "is_ruled")
		@JsonProperty("is_ruled")
        private boolean rule;
    }
}
