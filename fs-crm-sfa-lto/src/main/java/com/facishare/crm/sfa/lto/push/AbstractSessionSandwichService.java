package com.facishare.crm.sfa.lto.push;

import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.support.GDSHandler;
import com.facishare.qixin.plugin.model.arg.BatchSessionSandwichUpdatedArg;
import com.facishare.qixin.plugin.model.arg.SessionSandwichUpdatedArg;
import com.facishare.qixin.plugin.service.SessionSandwichApiService;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public abstract class AbstractSessionSandwichService implements SessionSandwichService {
    @Autowired
    private GDSHandler gdsHandler;
    @Autowired
    private SessionSandwichApiService sessionSandwichApiService;

    /**
     * 获取SessionSandwichUpdateTypeEnum
     *
     */
    protected abstract SessionSandwichUpdateTypeEnum getSessionSandwichUpdateType();

    /**
     * 获取消息内容
     *
     */
    protected abstract String getContent();

    private void batchSessionSandwichUpdated(SessionSandwichUpdateTypeEnum type, String tenantId,
                                             List<String> userIds, String content) {
        BatchSessionSandwichUpdatedArg arg = new BatchSessionSandwichUpdatedArg();
        String enterpriseAccount = gdsHandler.getEAByEI(tenantId);
        arg.setEnterpriseAccount(enterpriseAccount);
        List<SessionSandwichUpdatedArg> args = Lists.newArrayList();
        userIds.forEach(userId -> {
            SessionSandwichUpdatedArg sessionSandwichUpdatedArg = new SessionSandwichUpdatedArg();
            sessionSandwichUpdatedArg.setEmployeeId(Integer.parseInt(userId));
            sessionSandwichUpdatedArg.setEnterpriseAccount(enterpriseAccount);
            sessionSandwichUpdatedArg.setType(type.getValue());
            sessionSandwichUpdatedArg.setContent(content);
            args.add(sessionSandwichUpdatedArg);
        });
        arg.setArgs(args);
        log.info("ContactChangeNotes：" + JSONObject.toJSONString(args));
        sessionSandwichApiService.batchSessionSandwichUpdated(arg);
    }

    @Override
    public void push(String tenantId, String userId) {
        if (Strings.isNullOrEmpty(tenantId) || Strings.isNullOrEmpty(userId)) {
            return;
        }
        push(tenantId, Lists.newArrayList(userId));
    }

    @Override
    public void push(String tenantId, List<String> userIds) {
        if (Strings.isNullOrEmpty(tenantId) || CollectionUtils.empty(userIds)) {
            return;
        }
        batchSessionSandwichUpdated(getSessionSandwichUpdateType(), tenantId, userIds, getContent());
    }
}
