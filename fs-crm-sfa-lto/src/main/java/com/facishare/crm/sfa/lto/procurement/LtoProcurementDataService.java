package com.facishare.crm.sfa.lto.procurement;

import com.facishare.crm.sfa.lto.procurement.models.LtoProcurementConstants.CommonConstants;
import com.facishare.crm.sfa.lto.procurement.models.LtoProcurementConstants.ComparisonConf;
import com.facishare.crm.sfa.lto.procurement.models.LtoProcurementConstants.ProcurementInfo;
import com.facishare.crm.sfa.lto.procurement.models.LtoProcurementModel.OPJApiEnum;
import com.facishare.crm.sfa.lto.utils.CommonUtil;
import com.facishare.crm.sfa.lto.utils.ObjectDataUtil;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.enterprise.common.util.CollectionUtil;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.facishare.crm.sfa.lto.procurement.models.LtoProcurementConstants.ProcurementLog.LOG_TYPE;
import static com.facishare.crm.sfa.lto.procurement.models.LtoProcurementConstants.ProcurementLog.PROCUREMENT_INFO_ID;

/**
 * @ClassName com.facishare.crm.sfa.lto.procurement.ProcurementDataService
 * @Description
 * <AUTHOR>
 * @Date 2022/10/18 18:06
 * @Version 1.0
 **/
@Slf4j
@Component
public class LtoProcurementDataService {
    @Autowired
    private ObjectDataServiceImpl objectDataService;

    public Map getComparisonRule(String tenantId){
        List<Map> rules = getComparisonRules(tenantId);
        if(!CollectionUtil.isEmpty(rules)){
            return rules.get(0);
        }
        return Maps.newHashMap();
    }

    /**
     * select caller_info, winner_info, join_info from biz_procurement_comparison where is_deleted = 0 AND tenant_id='82337';
     * @param tenantId
     * @return
     */
    private List<Map> getComparisonRules(String tenantId){
        String sql = String.format("select %s, %s, %s, default_rule", ComparisonConf.RULE_CALLER_INFO, ComparisonConf.RULE_WINNER_INFO, ComparisonConf.RULE_JOIN_INFO)
            + String.format(" from %s", ComparisonConf.TABLE_PROCUREMENT_COMPARISON)
            + String.format(" where tenant_id = '%s' and is_deleted = 0 ", tenantId);
        return findBySQL(tenantId, sql);
    }

    /**
     * 企业库 table_name : biz_enterprise，比对字段：uniform_social_credit_code
     * String sql = "select *" + String.format(" from biz_enterprise where uniform_social_credit_code = '%s' and is_deleted = 0 ", creditCode);
     * @param creditCode :统一社会信用代码
     * @return
     */
    public List<Map> getEnterpriseInfo(String tenantId, String creditCode){
        return findBySQL(tenantId, joinSql(ComparisonConf.TABLE_ENTERPRISE, tenantId, ComparisonConf.TABLE_FIELD_CREDIT_CODE, creditCode));
    }

    public List<IObjectData> getEnterpriseInfo(String tenantId, List<String> creditCode, List<String> names){
        return getDBInfo(tenantId, creditCode, names, OPJApiEnum.Enterprise.apiName);
    }

    /**
     * 客户 table_name : biz_account，比对字段：uniform_social_credit_code
     * String sql = "select *" + String.format(" from biz_account where uniform_social_credit_code = '%s' and is_deleted = 0 ", creditCode);
     * @param creditCode :统一社会信用代码
     * @return
     */
    public List<Map> getAccountInfo(String tenantId, String creditCode){
        return findBySQL(tenantId, joinSql(ComparisonConf.TABLE_ACCOUNT, tenantId, ComparisonConf.TABLE_FIELD_CREDIT_CODE, creditCode));
    }

    public List<IObjectData> getAccountInfo(String tenantId, List<String> creditCode, List<String> names){
        return getDBInfo(tenantId, creditCode, names, OPJApiEnum.Account.apiName);
    }

    /**
     * 销售线索 table_name : biz_leads，比对字段：company
     * String sql = "select *" + String.format(" from biz_leads where company = '%s' and is_deleted = 0 ", fullName);
     * @param fullName :全称
     * @return
     */
    public List<Map> getLeadsInfo(String tenantId, String fullName){
        return findBySQL(tenantId, joinSql(ComparisonConf.TABLE_LEADS, tenantId, ComparisonConf.TABLE_FIELD_COMPANY, fullName));
    }

    public List<IObjectData> getLeadsInfo(String tenantId, List<String> fullName){
        return getDBInfo(tenantId, null, fullName, OPJApiEnum.Leads.apiName);
    }

    /**
     * 竞争对手，table_name : biz_competitor，比对字段：uniform_social_credit_code
     * String sql = "select *" + String.format(" from biz_competitor where uniform_social_credit_code = '%s' and is_deleted = 0 ", creditCode);
     * @param creditCode :统一社会信用代码
     * @return
     */
    public List<Map> getCompetitorInfo(String tenantId, String creditCode){
        return findBySQL(tenantId, joinSql(ComparisonConf.TABLE_COMPETITOR, tenantId, ComparisonConf.TABLE_FIELD_CREDIT_CODE, creditCode));
    }

    public List<IObjectData> getCompetitorInfo(String tenantId, List<String> creditCode, List<String> names){
        return getDBInfo(tenantId, creditCode, names, OPJApiEnum.Competitor.apiName);
    }

    /**
     * 合作伙伴 table_name : partner，比对字段：uniform_social_credit_code
     * String sql = "select *" + String.format(" from partner where uniform_social_credit_code = '%s' and is_deleted = 0 ", creditCode);
     * @param creditCode :统一社会信用代码
     * @return
     */
    public List<Map> getPartnerInfo(String tenantId, String creditCode){
        return findBySQL(tenantId, joinSql(ComparisonConf.TABLE_PARTNER, tenantId, ComparisonConf.TABLE_FIELD_CREDIT_CODE, creditCode));
    }

    public List<IObjectData> getPartnerInfo(String tenantId, List<String> creditCode, List<String> names){
        return getDBInfo(tenantId, creditCode, names, OPJApiEnum.Partner.apiName);
    }

    private String joinSql(String tableName, String tenantId, String whereFieldName, String whereFieldValue){
        return String.format("select id, tenant_id, name from %s where tenant_id = '%s' and %s = '%s' and is_deleted = 0 ", tableName, tenantId, whereFieldName, whereFieldValue);
    }

    private List<Map> findBySQL(String tenantId, String querySql){
        List<Map> queryResult = Lists.newArrayList();
        if( StringUtil.isNotEmpty(querySql) ){
            try {
                queryResult = objectDataService.findBySql(tenantId, querySql);
            } catch (MetadataServiceException e) {
                log.error("findBySql:{} ", querySql, e);
            }
        }
        return queryResult;
    }

    private List<IObjectData> getDBInfo(String tenantId, List<String> creditCode, List<String> names, final String api_name){
        List<Wheres> wheres = Lists.newArrayList();
        if(!CollectionUtil.isEmpty(creditCode)) {
            List<IFilter> idFilters = Lists.newArrayList();
            SearchUtil.fillFilterEq(idFilters, CommonConstants.TENANT_ID, tenantId);
            SearchUtil.fillFilterIn(idFilters, ComparisonConf.TABLE_FIELD_CREDIT_CODE, creditCode);
            SearchUtil.fillFilterEq(idFilters, CommonConstants.IS_DELETED, 0);

            Wheres idWhere = new Wheres();
            idWhere.setFilters(idFilters);
            wheres.add(idWhere);
        }

        if(!CollectionUtil.isEmpty(names)){
            List<IFilter> nameFilters = Lists.newArrayList();
            SearchUtil.fillFilterEq(nameFilters, CommonConstants.TENANT_ID, tenantId);
            if(OPJApiEnum.Leads.apiName.equals(api_name)){
                SearchUtil.fillFilterIn(nameFilters, ComparisonConf.TABLE_FIELD_COMPANY, names);
            }else {
                SearchUtil.fillFilterIn(nameFilters, ComparisonConf.TABLE_FIELD_NAME, names);
            }
            SearchUtil.fillFilterEq(nameFilters, CommonConstants.IS_DELETED, 0);

            Wheres nameWhere = new Wheres();
            nameWhere.setFilters(nameFilters);
            wheres.add(nameWhere);
        }

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setWheres(wheres);

        query.setLimit(10);
        query.setFindExplicitTotalNum(false);
        query.setNeedReturnCountNum(false);
        query.setPermissionType(0);
        List<OrderBy> orders = new ArrayList<>();
        orders.add(new OrderBy("last_modify_time", Boolean.FALSE));
        query.setOrders(orders);

        QueryResult<IObjectData> queryResult = ObjectDataUtil.findDataBySearchQuery(CommonUtil.buildUser(tenantId), api_name, query, null);
        if(queryResult != null && CollectionUtils.isNotEmpty(queryResult.getData())){
            return queryResult.getData();
        }
        return Lists.newArrayList();
    }


    public List<IObjectData> getDBLog(String tenantId, String procurementInfoId, List<String> fieldIds, String apiName){
        List<Wheres> wheres = Lists.newArrayList();
        if(CollectionUtil.isEmpty(fieldIds)) {
            return Lists.newArrayList();
        }
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, CommonConstants.TENANT_ID, tenantId);
        SearchUtil.fillFilterIn(filters, apiName, fieldIds);
        SearchUtil.fillFilterEq(filters, PROCUREMENT_INFO_ID, procurementInfoId);
        SearchUtil.fillFilterEq(filters, LOG_TYPE, procurementInfoId);
        SearchUtil.fillFilterEq(filters, CommonConstants.IS_DELETED, 0);

        Wheres nameWhere = new Wheres();
        nameWhere.setFilters(filters);
        wheres.add(nameWhere);

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setWheres(wheres);

        query.setLimit(10);
        query.setFindExplicitTotalNum(false);
        query.setNeedReturnCountNum(false);
        query.setPermissionType(0);
        List<OrderBy> orders = new ArrayList<>();
        orders.add(new OrderBy("last_modify_time", Boolean.FALSE));
        query.setOrders(orders);

        try {
            QueryResult<IObjectData> queryResult = ObjectDataUtil.findDataBySearchQuery(CommonUtil.buildUser(tenantId), "ProcurementLogObj", query, null);
            if(queryResult != null && CollectionUtils.isNotEmpty(queryResult.getData())){
                return queryResult.getData();
            }
        }catch (com.facishare.paas.appframework.core.exception.ValidateException e){
            log.warn("findDataBySearchQuery:{}", e.getMessage());
        }
        return Lists.newArrayList();
    }

    public List<IObjectData> findByIdList(List<String> ids, String tenantId, final String NAME_OBJ) {
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, CommonConstants._ID, ids);
        SearchUtil.fillFilterEq(filters, CommonConstants.TENANT_ID, tenantId);
        SearchUtil.fillFilterEq(filters, CommonConstants.IS_DELETED, 0);

        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setFilters(filters);

        QueryResult<IObjectData> queryResult = ObjectDataUtil.findDataBySearchQuery(CommonUtil.buildUser(tenantId), NAME_OBJ, searchTemplateQuery, null);
        if(queryResult != null && queryResult.getTotalNumber() > 0){
            return queryResult.getData();
        }
        return Lists.newArrayList();
    }

    /**
     * select * from biz_procurement_info
     * where ('636e193740d579000104c5df' = any( caller_enterprise ) or '636e0ce240d579000104b8f0' = any( winner_enterprise) )
     * and tenant_id = '82337' and is_deleted = 0
     * @param enterpriseId
     * @param tenantId
     * @return
     */
    public List<Map> searchInfoByEnterpriseId(String enterpriseId, String tenantId) {
        String sql = String.format("select * from %s", ProcurementInfo.TABLE_NAME)
            + String.format(" where ('%s' = any( %s ) or '%s' = any( %s ) )", enterpriseId, ProcurementInfo.CALLER_ENTERPRISE, enterpriseId, ProcurementInfo.WINNER_ENTERPRISE)
            + String.format(" and tenant_id = '%s' and is_deleted = 0 ", tenantId);
        return findBySQL(tenantId, sql);
    }
}