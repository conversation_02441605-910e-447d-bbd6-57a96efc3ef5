package com.facishare.crm.sfa.lto.rfm.models;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.lto.utils.ObjectDataUtil;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.Wheres;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public interface RFMModels {
    @Data
    class RFMFieldResultSetting {
        private String object_apiname;
        private String field_name;
        private List<RFMSingleFieldResultSetting> list;
    }

    @Data
    class RFMSingleFieldResultSetting {
        private List<Integer> recency;
        private List<Integer> monetary;
        private List<Integer> frequency;
        private String field_value;
    }

    @Data
    class RFMScoreSetting {
        private Integer score;
        private String condition;
        private String object_apiname;

        public List<Wheres> getWheresList() {
            List<Wheres> wheresList = Lists.newArrayList();
            if(StringUtils.isNotBlank(condition) && condition.startsWith("[") && condition.endsWith("]")) {
                List<Wheres> tmpWheresList = JSON.parseArray(condition, Wheres.class);
                wheresList.addAll(tmpWheresList);
            }
            return wheresList;
        }
    }

    @Data
    @Builder
    class RFMScoreResult {
        private GetScoreByConditionResult recencyScore;
        private GetScoreByConditionResult frequencyScore;
        private GetScoreByConditionResult monetaryScore;
        private String resultFieldApiName;
        private String resultFieldValue;
        private IObjectData ruleData;
        private IObjectData actionData;

        public String getActionObjectApiName() {
            return ObjectDataUtil.getStringValue(ruleData, "action_object_api_name", "");
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class CalculateRFMTaskArg {
        private String tenantId;
        private String objectApiName;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class RecalculateRFMByIdsArg {
        private String tenantId;
        private List<String> objectIds;
        private String objectApiName;
        /**操作人id*/
        private String operator;
    }

    @Data
    @Builder
    class GetScoreByConditionResult {
        private RFMModels.RFMScoreSetting scoreSetting;
        private Double evaluateValue;
        private Long recencyDateTime;

        public int getScore() {
            if(scoreSetting == null) {
                return 0;
            }
            return scoreSetting.getScore();
        }
    }

    @Data
    @Builder
    class TimeField {
        private List<String> actionObjectApiNameTimeField;
        private List<String> sourceObjectApiNameTimeField;
        private String recencyFieldApiName;
    }

    @Data
    @Builder
    class SourceResult {
        private QueryResult<IObjectData> queryResult;
        private double frequencyTotal;
        private double monetaryTotal;
    }
}
