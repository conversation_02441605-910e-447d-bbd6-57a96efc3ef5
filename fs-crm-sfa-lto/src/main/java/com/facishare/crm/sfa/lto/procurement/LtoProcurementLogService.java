package com.facishare.crm.sfa.lto.procurement;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * @ClassName {@LtoProcurementLogService}
 * @Description
 * <AUTHOR>
 * @Date 2022/10/28 18:20
 * @Version 1.0
 **/
@Slf4j
@Component
public class LtoProcurementLogService {

    @Autowired
    protected ServiceFacade serviceFacade;

    /**
     * 调用triggerAction 来创建数据
     * @param user
     * @param apiName 对象apiName
     * @param objectData
     * @return
     */
    public BaseObjectSaveAction.Result triggerAction(User user, String apiName, ObjectDataDocument objectData) {
        BaseObjectSaveAction.Arg arg = new BaseObjectSaveAction.Arg();
        RequestContext requestContext = RequestContext.builder().user(user).build();
        ActionContext actionContext = new ActionContext(requestContext, apiName, ObjectAction.CREATE.getActionCode());
        arg.setObjectData(objectData);
        BaseObjectSaveAction.OptionInfo optionInfo = new BaseObjectSaveAction.OptionInfo();
        optionInfo.setIsDuplicateSearch(false);
        optionInfo.setUseValidationRule(false);
        optionInfo.setSkipFuncValidate(false);
        optionInfo.setFromMapping(false);
        arg.setOptionInfo(optionInfo);
        String body = JSON.toJSONString(arg);
        log.debug("LtoProcurementLogService > triggerAction()  body{}: ", body);
        BaseObjectSaveAction.Result saveResult = serviceFacade.triggerAction(actionContext, arg, BaseObjectSaveAction.Result.class);
        log.debug("LtoProcurementLogService > triggerAction()  result{}: ", saveResult);
        return saveResult;
    }
}