package com.facishare.crm.sfa.lto.todo.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 2023-02-08
 * @instruction
 */
public interface LtoSaleActionModel {
    /**
     * 商机流程关联实体类
     * <AUTHOR>
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class OpportunitySaleAction {
        private String sale_action_id;
        private String opportunity_id;
        private String pre_sale_stage_id;
        private String after_sale_stage_id;
        private Integer pre_status;
        private Integer after_status;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class OpportunitySaleActionStage {
        private String sale_action_stage_id;
        private String opportunity_id;
        private Date start_time;
        private Date end_time;
        private Boolean is_over_time;
        private Integer type;
        private Integer status;
        private Date update_time;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class OpportunitySaleActionConfirm {
        private String opportunity_sale_action_confirm_id;
        private String sale_action_stage_id;
        private String opportunity_id;
        private String sale_action_id;
        private String target_sale_action_stage_id;
        private String data_id;//订单id
        private String order_name;//订单name
        private Integer employee_id;
        private Integer creator_id;
        private Date update_time;
        private Boolean is_unread;
        private Integer target_opportunity_status;
        private Integer status;
        private Integer lose_type;
        private String lost_reason__r;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class SaleActionStage {
        private Integer ei;
        private String sale_action_stage_id;
        private String sale_action_id;
        private String name;
        private String description;
        private Integer stage_order;
        private Boolean is_timeout_remind;//订单id
        private Integer remain_days;//订单name
        private Boolean is_leader_confirm;
        private Boolean is_finish_by_contacts;
        private Integer contact_count;
        private Integer type;
        private Boolean as_is_that_day_remind;
        private Integer as_that_remain_days;
        private Boolean as_is_timeout_remind;
        private Integer as_remain_days;
        private Date update_time;
        private Integer weight;
        private Integer stage_flag;
    }

    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    class UserDefinedField {
        @JSONField(name = "sale_action_stage_id")
        @SerializedName("sale_action_stage_id")
        private String sale_action_stage_id;

        @JSONField(name = "field_caption")
        @SerializedName("field_caption")
        private String field_caption;

        @JSONField(name = "select_options")
        @SerializedName("select_options")
        private String select_options;

        @JSONField(name = "sale_action_id")
        @SerializedName("sale_action_id")
        private String sale_action_id;

        @JSONField(name = "is_not_null")
        @SerializedName("is_not_null")
        private Boolean is_not_null;

        @JSONField(name = "is_watermark")
        @SerializedName("is_watermark")
        private Boolean is_watermark;

        @JSONField(name = "field_order")
        @SerializedName("field_order")
        private Integer field_order;

        @JSONField(name = "field_belong")
        @SerializedName("field_belong")
        private String field_belong;

        @JSONField(name = "field_type")
        @SerializedName("field_type")
        private String field_type;

        @JSONField(name = "field_api_name")
        @SerializedName("field_api_name")
        private String field_api_name;

        @JSONField(name = "decimal_digits")
        @SerializedName("decimal_digits")
        private String decimal_digits;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class FeedContentModel {
        private String text;
        private List<Map> images;
        private List<Map> attachments;
    }
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class FeedContentImageAndFileModel {
        private String name;
        private String path;
        private String size;
        private String ext;
        private String filename;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class EnumObject {
        private String EnumName;
        private String ItemCode;
        private String ItemName;
        private String ItemOrder;
        private List<EnumObject> Children;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class OpportunitySaleActionSaleStageModel {
        private String opportunityId;
        private String preStageId;
        private String afterStageId;

        private String saleActionId;
        private String saleActionName;
        private Integer afterStageStatus;
        private Integer probability;
        private String oppoStatus;
    }
}
