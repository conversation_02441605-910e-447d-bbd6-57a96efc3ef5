package com.facishare.crm.sfa.lto.procurement.models;

public interface LtoProcurementConstants {

    /**
     * 比对规则对象
     */
    class ComparisonConf {
        public static final String TABLE_PROCUREMENT_COMPARISON = "biz_procurement_comparison";
        public static final String TABLE_ENTERPRISE = "biz_enterprise";
        public static final String TABLE_ACCOUNT = "biz_account";
        public static final String TABLE_LEADS = "biz_leads";
        public static final String TABLE_COMPETITOR = "biz_competitor";
        public static final String TABLE_PARTNER = "partner";

        public static final String TABLE_FIELD_CREDIT_CODE = "uniform_social_credit_code";
        public static final String TABLE_FIELD_CREDIT_ID = "credit_code";
        public static final String TABLE_FIELD_NAME = "name";
        public static final String TABLE_FIELD_COMPANY = "company";

        //招标单位json
        public static final String RULE_CALLER_INFO = "caller_info";
        //中标单位json
        public static final String RULE_WINNER_INFO = "winner_info";
        //拟定参标单位json
        public static final String RULE_JOIN_INFO = "join_info";


        public static final String DEFAULT_RULE = "default_rule";

        //消息操作类型
        public static final Integer CREATE_ACTION = 1;
        public static final Integer TRANSFER_ACTION = 2;

    }

    class CommonConstants {
        public static final String _ID = "_id";
        public static final String ID = "id";
        public static final String BID_ID = "bid_id";
        public static final String IS_DELETED = "is_deleted";
        public static final String TENANT_ID = "tenant_id";
        public static final String CREATE_TIME = "create_time";
        public static final String CREATED_BY = "created_by";
        public static final String LAST_MODIFIED_TIME = "last_modified_time";
        public static final String OWNER = "owner";
        public static final String LAST_MODIFIED_BY = "last_modified_by";
        public static final String OBJECT_DESCRIBE_API_NAME = "object_describe_api_name";
    }

    class ProcurementInfo {
        public static final String TABLE_NAME = "biz_procurement_info";
        public static final String CALLER_STATUS = "caller_status";
        public static final String WINNER_STATUS = "winner_status";
        public static final String NAME_OBJ = "ProcurementInfoObj";

        public static final String CALLER_ENTERPRISE = "caller_enterprise";
        public static final String CALLER_CONTACT = "caller_contact";
        public static final String WINNER_ENTERPRISE = "winner_enterprise";
        public static final String WINNER_CONTACT = "winner_contact";
    }

    class ProcurementEnterprise {
        public static final String TABLE_NAME = "biz_procurement_enterprise";
        public static final String NAME_OBJ = "ProcurementEnterpriseObj";
    }

    class ProcurementTransfer {
        public static final String TABLE_NAME = "biz_procurement_transfer_log";
        public static final String ENTERPRISE_INFO_ID = "enterprise_info_id"; //查找关联【企业库】
        public static final String ACCOUNT_ID = "account_id"; //查找关联【客户】
        public static final String LEADS_ID = "leads_id"; //查找关联【销售线索】
        public static final String NEW_OPPORTUNITY_ID = "new_opportunity_id"; //查找关联【商机2.0】
        public static final String COMPETITOR_ID = "competitor_id"; //查找关联【竞争对手】
        public static final String PARTNER_ID = "partner_id"; //查找关联【合作伙伴】

        public static final String PROCUREMENT_INFO_ID = "procurement_info_id"; //查找关联【招投标公告信息】
        public static final String INFO_TYPE = "info_type"; //引用【招投标公告信息】-公告类型

        public static final String PROCUREMENT_ENTERPRISE_ID = "procurement_enterprise_id"; //【招投标主体信息】
        public static final String ENTERPRISE_TYPE = "enterprise_type"; //【招投标主体信息】主体类型

        public static final String INFO_ID = "info_id";
        public static final String OBJ_NAME = "ProcurementTransferLogObj";
    }

    class ProcurementLog{
        public static final String TABLE_NAME = "biz_procurement_log";
        public static final String PROCUREMENT_INFO_ID = "procurement_info_id";

        public static final String LOG_TYPE = "log_type";
    }
}
