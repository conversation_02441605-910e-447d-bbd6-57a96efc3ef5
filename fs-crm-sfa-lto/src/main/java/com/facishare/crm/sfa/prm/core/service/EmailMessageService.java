package com.facishare.crm.sfa.prm.core.service;

import com.facishare.converter.EIEAConverter;
import com.facishare.crm.sfa.prm.api.enums.NotificationType;
import com.facishare.crm.sfa.prm.api.notification.MessageService;
import com.facishare.crm.sfa.prm.model.EmailMessageContent;
import com.facishare.crm.sfa.prm.model.MessageContent;
import com.facishare.crm.sfa.prm.platform.model.RestResponse;
import com.facishare.crm.sfa.prm.rest.client.EmailClient;
import com.facishare.crm.sfa.prm.rest.model.EmailProxyModel;
import com.facishare.crm.sfa.prm.rest.model.TemplateModel;
import com.facishare.crm.sfa.prm.rest.proxy.EmailTemplateServiceProxy;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.util.SFAHeaderUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @time 2024-06-29 16:18
 * @Description
 */
@Service
@Slf4j
public class EmailMessageService implements MessageService {
    @Resource
    private EmailClient emailClient;
    @Resource
    private EIEAConverter converter;
    @Resource
    private EmailTemplateServiceProxy emailTemplateServiceProxy;

    @Override
    public NotificationType getMessageType() {
        return NotificationType.EMAIL;
    }

    @Override
    public boolean sendMessage(User user, MessageContent content) {
        if (!(content instanceof EmailMessageContent)) {
            throw new IllegalArgumentException("Invalid content type for EmailMessageService");
        }
        EmailMessageContent emailMessageContent = (EmailMessageContent) content;
        populateEmailFromTemplate(user, emailMessageContent);
        EmailProxyModel.EmailArg emailArg = buildEmailArg(user, emailMessageContent);
        try {
            RestResponse restResponse = emailClient.sendEmail(SFAHeaderUtil.getHeaders(user), emailArg);
            return restResponse != null && restResponse.isSuccess();
        } catch (Exception e) {
            log.warn("EmailMessageService#sendMessage failed, tenant:{}, emailArg:{}", user.getTenantId(), emailArg, e);
        }
        return false;
    }

    private void populateEmailFromTemplate(User user, EmailMessageContent emailMessageContent) {
        TemplateModel.Result templateResult = emailTemplateServiceProxy.fetchEmailFromTemplate(user, emailMessageContent.getTemplateId(), emailMessageContent.getObjectDataId());
        if (templateResult == null) {
            return;
        }
        emailMessageContent.setSubject(templateResult.getSubject());
        emailMessageContent.setContent(templateResult.getTemplate());
    }

    private EmailProxyModel.EmailArg buildEmailArg(User user, EmailMessageContent emailMessageContent) {
        String ea = converter.enterpriseIdToAccount(user.getTenantIdInt());
        EmailProxyModel.EmailArg emailArg = new EmailProxyModel.EmailArg();
        EmailProxyModel.Email email = new EmailProxyModel.Email();
        email.setEa(ea);
        email.setUserIdList(emailMessageContent.getUserIdList());
        email.setSender(emailMessageContent.getSender());
        email.setToList(emailMessageContent.getRecipients());
        email.setSubject(emailMessageContent.getSubject());
        email.setContent(emailMessageContent.getContent());
        email.setAttachments(emailMessageContent.getAttachments());
        emailArg.setArg1(email);
        return emailArg;
    }
}
