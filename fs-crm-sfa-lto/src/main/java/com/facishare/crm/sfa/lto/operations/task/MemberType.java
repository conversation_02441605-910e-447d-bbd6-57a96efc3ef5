package com.facishare.crm.sfa.lto.operations.task;

import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

enum MemberType {
    USER("person", 1),
    EXTRA("ext_process", 0),
    ;
    final String key;
    @Getter
    final int type;

    MemberType(String key, int type) {
        this.key = key;
        this.type = type;
    }

    public static Optional<MemberType> fromType(int type) {
        return Arrays.stream(values()).filter(v -> v.type == type).findFirst();
    }

    public static Optional<MemberType> fromType(Object type) {
        if (type instanceof Integer) {
            return fromType((int) type);
        }
        if (type != null) {
            return fromType(Integer.parseInt(type.toString()));
        }
        return Optional.empty();
    }

    public static Optional<MemberType> fromKey(Object key) {
        return Arrays.stream(values()).filter(v -> v.key.equals(key)).findFirst();
    }
}