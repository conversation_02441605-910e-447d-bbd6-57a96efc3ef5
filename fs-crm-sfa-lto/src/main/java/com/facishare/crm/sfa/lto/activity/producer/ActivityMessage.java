package com.facishare.crm.sfa.lto.activity.producer;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/12/9 13:58
 * @description:
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActivityMessage {
    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 对象 ID（销售记录）
     */
    private String objectId;
    /**
     * 对象 ApiName (一般是销售记录)
     */
    private String objectApiName;

    /**
     * 互动类型，比如：call, meeting, chat, email, visit, others
     */
    private String interactiveTypes;

    /**
     * 销售记录来源ID（比如：邮件 ID）
     */
    private String sourceId;

    /**
     * 销售记录来源 API 名称（比如：邮件 MailObj）
     */
    private String sourceApiName;

    /**
     * 操作人 ID
     */
    private String opId;

    /**
     *   Add, Delete,
     */
    private String actionCode;

    /**
     *  AI 阶段，比如：realtime2text, file2text
     */
    private String stage;

    /**
     * 用户当前语言
     */
    private String language;
}
