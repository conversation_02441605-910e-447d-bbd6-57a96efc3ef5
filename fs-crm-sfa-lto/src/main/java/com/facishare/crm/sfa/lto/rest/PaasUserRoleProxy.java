package com.facishare.crm.sfa.lto.rest;

import com.facishare.crm.sfa.lto.rest.models.UserRoleModel;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

/**
    *<AUTHOR> lik
    *@date : 2022/7/13 15:55
 */

@RestResource(value = "PAAS-PRIVILEGE", desc = "pass user role proxy ", contentType = "application/json")
public interface PaasUserRoleProxy {
    @POST(value = "/queryRoleUsersByRoles", desc = "批量查询角色下的用户")
    UserRoleModel.UserRoleMapResult queryRoleUsersByRoles(@HeaderMap Map<String, String> headers, @Body UserRoleModel.UserRoleListWithContextArg body);

    @POST(value = "/queryUserRoleCodesByUsers", desc = "批量查询用户分配的角色")
    UserRoleModel.UserRoleMapResult queryUserRoleCodesByUsers(@HeaderMap Map<String, String> headers, @Body UserRoleModel.UserRoleUsersWithContextArg body);

}
