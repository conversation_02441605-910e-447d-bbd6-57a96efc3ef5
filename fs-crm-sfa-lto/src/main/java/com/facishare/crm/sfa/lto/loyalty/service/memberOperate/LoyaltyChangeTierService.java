package com.facishare.crm.sfa.lto.loyalty.service.memberOperate;

import com.facishare.crm.sfa.lto.loyalty.constants.LoyaltyConstants;
import com.facishare.crm.sfa.lto.loyalty.model.Loyalty;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.common.StopWatch;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class LoyaltyChangeTierService extends AbstractLoyaltyPointsOperateService {
    @Override
    public Loyalty.PointsOperationParam.Type type() {
        return Loyalty.PointsOperationParam.Type.CHANGE_LEVEL;
    }

    @Transactional
    @Override
    public void action(Loyalty.PointsOperationParam param) {
        String tenantId = param.getTenantId();
        String memberId = param.getMemberId();
        StopWatch stopWatch = StopWatch.createStarted("会员等级动态调整");
        IObjectData memberInfo = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), memberId, LoyaltyConstants.LoyaltyMember.API_NAME);
        String changeTierBefore = memberInfo.get(LoyaltyConstants.LoyaltyMember.TIER_ID, String.class);
        String afterTierId = getNextTier(tenantId, changeTierBefore, param.getValue().intValue());
        stopWatch.lap("计算变化后等级");
        Map<String, Object> memberUpdateFields = new HashMap<>();
        memberUpdateFields.put(LoyaltyConstants.LoyaltyMember.TIER_ID, afterTierId);
        memberUpdateFields.put(LoyaltyConstants.LoyaltyMember.TIER_START_TIME, System.currentTimeMillis());
        serviceFacade.updateWithMap(User.systemUser(tenantId), memberInfo, memberUpdateFields);
        stopWatch.lap("更新员工");
        ObjectDataDocument recordDataDocument = new ObjectDataDocument();
        recordDataDocument.put(LoyaltyConstants.LoyaltyMemberChangeRecords.LOY_CHANGE_TYPE, Loyalty.PointsOperationParam.Type.CHANGE_LEVEL.toString());
        recordDataDocument.put(LoyaltyConstants.LoyaltyMemberChangeRecords.LOY_CHANGE_TIER_BEFORE, changeTierBefore);
        recordDataDocument.put(LoyaltyConstants.LoyaltyMemberChangeRecords.LOY_CHANGE_TIER_AFTER, afterTierId);
        saveMemberChangeRecord(param, Lists.newArrayList(recordDataDocument));
        stopWatch.lap("插入记录");
        stopWatch.logSlow(STOP_WATCH_TIME);
    }

    @Override
    public void fallback(Loyalty.FallbackUpdateData updateData, Loyalty.FallbackInfo fallbackInfo) {
        //暂不支持等级回退
    }

    /**
     * 找到相对变化等级
     *
     * @param relativePosition 较当前等级的相对位置，如升两级，2   减两级，-2
     */
    private String getNextTier(String tenantId, String tierId, int relativePosition) {
        IObjectData tierInfo = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), tierId, LoyaltyConstants.LoyaltyTier.API_NAME);
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, LoyaltyConstants.LoyaltyTier.TIER_CLASS_ID, tierInfo.get(LoyaltyConstants.LoyaltyTier.TIER_CLASS_ID, String.class));
        query.setFilters(filters);
        query.setOrders(Lists.newArrayList(new OrderBy(LoyaltyConstants.LoyaltyTier.POINTS_REQUIRED, true)));
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(User.systemUser(tenantId), LoyaltyConstants.LoyaltyTier.API_NAME, query);
        if (queryResult == null || CollectionUtils.isEmpty(queryResult.getData())) {
            return tierId;
        }
        List<String> idList = queryResult.getData().stream().map(IObjectData::getId).collect(Collectors.toList());
        int afterPosition = idList.indexOf(tierId) + relativePosition;
        if (afterPosition <= 0) {
            return idList.get(0);
        } else if (afterPosition >= idList.size() - 1) {
            return idList.get(idList.size() - 1);
        } else {
            return idList.get(afterPosition);
        }
    }
}
