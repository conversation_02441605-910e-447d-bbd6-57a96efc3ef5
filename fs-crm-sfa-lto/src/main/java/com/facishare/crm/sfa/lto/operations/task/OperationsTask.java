package com.facishare.crm.sfa.lto.operations.task;

import com.facishare.paas.metadata.api.IObjectData;
import lombok.Getter;
import lombok.Setter;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Getter
public class OperationsTask {
    @Setter
    private IObjectData data;
    @Setter
    private String name;
    @Setter
    private String description;
    @Setter
    private String createdBy;
    @Setter
    private List<String> reminds;
    private Long deadline;
    private final Set<String> cc = new HashSet<>();
    private final Set<String> executors = new HashSet<>();

    public void setDeadline(Object deadline) {
        if (deadline instanceof Long) {
            this.deadline = (Long) deadline;
        } else {
            try {
                this.deadline = Long.parseLong(deadline.toString());
            } catch (Exception ignore) {
                this.deadline = 0L; // 无截止时间
            }
        }
    }

    public void addExecutors(List<String> executors) {
        this.executors.addAll(executors);
    }

    public void addCC(List<String> cc) {
        this.cc.addAll(cc);
    }
}
