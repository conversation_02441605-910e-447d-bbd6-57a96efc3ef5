package com.facishare.crm.sfa.lto.activity.mongo;

import org.bson.types.ObjectId;
import java.util.List;

/**
 * 交互策略任务数据访问接口
 */
public interface InteractionStrategyTaskDao {

    /**
     * 保存或更新交互策略任务
     *
     * @param taskDocument 任务文档
     * @return 保存或更新后的任务文档
     */
    InteractionStrategyTaskDocument saveOrUpdate(InteractionStrategyTaskDocument taskDocument);

    /**
     * 根据ID查询交互策略任务
     *
     * @param id 任务ID
     * @return 交互策略任务
     */
    InteractionStrategyTaskDocument findById(ObjectId id);

    /**
     * 根据租户ID和策略ID查询交互策略任务
     *
     * @param tenantId 租户ID
     * @param strategyId 策略ID
     * @param statusList 状态列表
     * @return 交互策略任务
     */
    InteractionStrategyTaskDocument findByTenantIdAndStrategyId(String tenantId, String strategyId, List<String> statusList);

    /**
     * 更新任务状态
     *
     * @param id 任务ID
     * @param status 任务状态
     */
    void updateStatus(ObjectId id, String status);

    /**
     * 更新任务失败原因
     *
     * @param id 任务ID
     * @param failReason 失败原因
     */
    void updateFailReason(ObjectId id, String failReason);

    /**
     * 更新运行批次
     *
     * @param id 任务ID
     * @param runningBatchTotal 运行批次
     */
    void updateRunningBatchTotal(ObjectId id, Integer runningBatchTotal);

    /**
     * 删除任务
     *
     * @param id 任务ID
     */
    void delete(ObjectId id);

    /**
     * 根据创建时间范围查询状态为init和running的任务，并按创建时间升序排序
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 任务列表
     */
    List<InteractionStrategyTaskDocument> findByStatusAndCreateTimeBetween(Long startTime, Long endTime);

    /**
     * 根据创建时间范围查询状态为init和running的任务，并按创建时间升序排序，带分页
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 任务列表
     */
    List<InteractionStrategyTaskDocument> findByStatusAndCreateTimeBetween(Long startTime, Long endTime, Integer offset, Integer limit);
}