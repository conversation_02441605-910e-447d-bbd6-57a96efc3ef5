package com.facishare.crm.sfa.lto.utils;

import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;

public class GrayUtil {
    private static final FsGrayReleaseBiz gray = FsGrayRelease.getInstance("sfa");

    private GrayUtil() {
        throw new IllegalStateException("Utility class");
    }

    public static boolean isGrayLeadsInvalidLimit(String tenantId) {
        return isGrayEnable("leads_invalid_limit_enable", tenantId);
    }

    public static boolean isGrayEnable(String grayKey, String tenantId) {
        return gray.isAllow(grayKey, tenantId);
    }


    /**
     * 是否发送mq，计算关系
     * @param tenantId
     * @return
     */
    public static boolean skipMemberRelationshipProcess(String tenantId) {
        return gray.isAllow("skip_member_relationship_process", tenantId);
    }
    public static boolean skipRelationshiServiceSkipTenantId(String tenantId) {
        return gray.isAllow("relationshi_service_skip_tenant_id", tenantId);
    }

    public static boolean isGraySfaCommonTreeSetQuotaTenant(String tenantId) {
        return gray.isAllow("sfa_common_tree_set_quota_tenant", tenantId);
    }
    public static boolean isGraySfaCommonTreeAddBranchTenant(String tenantId) {
        return gray.isAllow("sfa_common_tree_add_branch_tenant", tenantId);
    }



    /**
     * 客户评级跳过
     *
     * @param tenantId
     * @return
     */
    public static boolean skipSfaGradeTenantId(String tenantId) {
        return gray.isAllow("skip_sfa_grade_tenantId", tenantId);
    }


    /**
     *
     * 待办里面屏蔽代理通线索
     * @return
     */
    public static boolean isGrayLeadsTodoListFilter(String tenantId) {
        return gray.isAllow("leads_todo_list_filter", tenantId);
    }
}