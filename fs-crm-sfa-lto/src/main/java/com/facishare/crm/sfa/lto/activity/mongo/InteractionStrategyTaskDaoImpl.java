package com.facishare.crm.sfa.lto.activity.mongo;

import com.facishare.crm.sfa.lto.activity.constants.InteractionStrategyTaskConstants;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.types.ObjectId;
import org.mongodb.morphia.Datastore;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.mongodb.morphia.query.UpdateResults;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * 交互策略任务数据访问实现类
 */
@Repository
@Slf4j
public class InteractionStrategyTaskDaoImpl implements InteractionStrategyTaskDao {

    @Autowired
    @Qualifier("activityDataMongoDbStore")
    private Datastore customerDatastore;

    @Override
    public InteractionStrategyTaskDocument saveOrUpdate(InteractionStrategyTaskDocument taskDocument) {
        if (taskDocument.getId() != null) {
            // 更新操作
            Query<InteractionStrategyTaskDocument> query = customerDatastore.createQuery(InteractionStrategyTaskDocument.class)
                    .field(InteractionStrategyTaskConstants.TaskFields.ID).equal(taskDocument.getId())
                    .field(InteractionStrategyTaskConstants.TaskFields.TENANT_ID).equal(taskDocument.getTenantId());

            UpdateOperations<InteractionStrategyTaskDocument> ops = customerDatastore.createUpdateOperations(InteractionStrategyTaskDocument.class);
            // 只在非空时设置字段
            if (taskDocument.getStrategyId() != null) {
                ops.set(InteractionStrategyTaskConstants.TaskFields.STRATEGY_ID, taskDocument.getStrategyId());
            }
            if (taskDocument.getStatus() != null) {
                ops.set(InteractionStrategyTaskConstants.TaskFields.STATUS, taskDocument.getStatus());
            }
            if (taskDocument.getFailReason() != null) {
                ops.set(InteractionStrategyTaskConstants.TaskFields.FAIL_REASON, taskDocument.getFailReason());
            }
            if (taskDocument.getSubmitTotal() != null) {
                ops.set(InteractionStrategyTaskConstants.TaskFields.SUBMIT_TOTAL, taskDocument.getSubmitTotal());
            }
            if (taskDocument.getRunningTotal() != null) {
                ops.set(InteractionStrategyTaskConstants.TaskFields.RUNNING_TOTAL, taskDocument.getRunningTotal());
            }
            if (taskDocument.getBatchTotal() != null) {
                ops.set(InteractionStrategyTaskConstants.TaskFields.BATCH_TOTAL, taskDocument.getBatchTotal());
            }
            if (taskDocument.getRunningBatchTotal() != null) {
                ops.set(InteractionStrategyTaskConstants.TaskFields.RUNNING_BATCH_TOTAL, taskDocument.getRunningBatchTotal());
            }
            if (taskDocument.getCreateBy() != null) {
                ops.set(InteractionStrategyTaskConstants.TaskFields.CREATE_BY, taskDocument.getCreateBy());
            }
            
            ops.set(InteractionStrategyTaskConstants.TaskFields.LAST_MODIFY_TIME, System.currentTimeMillis());
            
            try {
                UpdateResults results = customerDatastore.update(query, ops);
                if (results.getUpdatedCount() == 0) {
                    log.warn("更新交互策略任务失败，未找到记录, id:{}", taskDocument.getId());
                }
            } catch (Exception e) {
                log.error("更新交互策略任务异常, id:{}", taskDocument.getId(), e);
                throw e;
            }
            return taskDocument;
        } else {
            // 新增操作
            taskDocument.setCreateTime(System.currentTimeMillis());
            taskDocument.setLastModifyTime(System.currentTimeMillis());
            try {
                customerDatastore.save(taskDocument);
                log.info("新增交互策略任务成功, id:{}", taskDocument.getId());
            } catch (Exception e) {
                log.error("新增交互策略任务异常", e);
                throw e;
            }
            return taskDocument;
        }
    }

    @Override
    public InteractionStrategyTaskDocument findById(ObjectId id) {
        return customerDatastore.createQuery(InteractionStrategyTaskDocument.class)
                .field(InteractionStrategyTaskConstants.TaskFields.ID).equal(id)
                .get();
    }

    @Override
    public InteractionStrategyTaskDocument findByTenantIdAndStrategyId(String tenantId, String strategyId, List<String> statusList) {
        Query<InteractionStrategyTaskDocument> query = customerDatastore.createQuery(InteractionStrategyTaskDocument.class)
                .field(InteractionStrategyTaskConstants.TaskFields.TENANT_ID).equal(tenantId)
                .field(InteractionStrategyTaskConstants.TaskFields.STRATEGY_ID).equal(strategyId);
        
        // 如果状态列表不为空，添加状态条件
        if (CollectionUtils.isNotEmpty(statusList)) {
            query.field(InteractionStrategyTaskConstants.TaskFields.STATUS).in(statusList);
        }
        
        return query.get();
    }

    @Override
    public void updateStatus(ObjectId id, String status) {
        Query<InteractionStrategyTaskDocument> query = customerDatastore.createQuery(InteractionStrategyTaskDocument.class)
                .field(InteractionStrategyTaskConstants.TaskFields.ID).equal(id);
        UpdateOperations<InteractionStrategyTaskDocument> ops = customerDatastore.createUpdateOperations(InteractionStrategyTaskDocument.class)
                .set(InteractionStrategyTaskConstants.TaskFields.STATUS, status)
                .set(InteractionStrategyTaskConstants.TaskFields.LAST_MODIFY_TIME, System.currentTimeMillis());
        try {
            customerDatastore.update(query, ops);
            log.info("更新交互策略任务状态成功, id:{}, status:{}", id, status);
        } catch (Exception e) {
            log.error("更新交互策略任务状态异常, id:{}, status:{}", id, status, e);
            throw e;
        }
    }

    @Override
    public void updateFailReason(ObjectId id, String failReason) {
        Query<InteractionStrategyTaskDocument> query = customerDatastore.createQuery(InteractionStrategyTaskDocument.class)
                .field(InteractionStrategyTaskConstants.TaskFields.ID).equal(id);
        UpdateOperations<InteractionStrategyTaskDocument> ops = customerDatastore.createUpdateOperations(InteractionStrategyTaskDocument.class)
                .set(InteractionStrategyTaskConstants.TaskFields.FAIL_REASON, failReason)
                .set(InteractionStrategyTaskConstants.TaskFields.LAST_MODIFY_TIME, System.currentTimeMillis());
        try {
            customerDatastore.update(query, ops);
            log.info("更新交互策略任务失败原因成功, id:{}", id);
        } catch (Exception e) {
            log.error("更新交互策略任务失败原因异常, id:{}", id, e);
            throw e;
        }
    }

    @Override
    public void updateRunningBatchTotal(ObjectId id, Integer runningBatchTotal) {
        Query<InteractionStrategyTaskDocument> query = customerDatastore.createQuery(InteractionStrategyTaskDocument.class)
                .field(InteractionStrategyTaskConstants.TaskFields.ID).equal(id);
        UpdateOperations<InteractionStrategyTaskDocument> ops = customerDatastore.createUpdateOperations(InteractionStrategyTaskDocument.class)
                .set(InteractionStrategyTaskConstants.TaskFields.RUNNING_BATCH_TOTAL, runningBatchTotal)
                .set(InteractionStrategyTaskConstants.TaskFields.LAST_MODIFY_TIME, System.currentTimeMillis());
        try {
            customerDatastore.update(query, ops);
            log.info("更新交互策略任务运行批次成功, id:{}, runningBatchTotal:{}", id, runningBatchTotal);
        } catch (Exception e) {
            log.error("更新交互策略任务运行批次异常, id:{}, runningBatchTotal:{}", id, runningBatchTotal, e);
            throw e;
        }
    }

    @Override
    public void delete(ObjectId id) {
        try {
            customerDatastore.delete(InteractionStrategyTaskDocument.class, id);
            log.info("删除交互策略任务成功, id:{}", id);
        } catch (Exception e) {
            log.error("删除交互策略任务异常, id:{}", id, e);
            throw e;
        }
    }
    
    @Override
    public List<InteractionStrategyTaskDocument> findByStatusAndCreateTimeBetween(Long startTime, Long endTime) {
        // 构建状态列表，包含 init 和 running
        List<String> statusList = Lists.newArrayList(
                InteractionStrategyTaskConstants.TaskStatus.INIT,
                InteractionStrategyTaskConstants.TaskStatus.RUNNING
        );
        
        try {
            // 创建查询
            Query<InteractionStrategyTaskDocument> query = customerDatastore.createQuery(InteractionStrategyTaskDocument.class)
                    .field(InteractionStrategyTaskConstants.TaskFields.STATUS).in(statusList)
                    .field(InteractionStrategyTaskConstants.TaskFields.CREATE_TIME).greaterThanOrEq(startTime)
                    .field(InteractionStrategyTaskConstants.TaskFields.CREATE_TIME).lessThanOrEq(endTime)
                    .order(InteractionStrategyTaskConstants.TaskFields.CREATE_TIME); // 按创建时间升序排序
            
            return query.asList();
        } catch (Exception e) {
            log.error("查询交互策略任务异常, startTime:{}, endTime:{}", startTime, endTime, e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<InteractionStrategyTaskDocument> findByStatusAndCreateTimeBetween(Long startTime, Long endTime, Integer offset, Integer limit) {
        // 构建状态列表，包含 init 和 running
        List<String> statusList = Lists.newArrayList(
                InteractionStrategyTaskConstants.TaskStatus.INIT,
                InteractionStrategyTaskConstants.TaskStatus.RUNNING
        );
        
        try {
            // 创建查询
            Query<InteractionStrategyTaskDocument> query = customerDatastore.createQuery(InteractionStrategyTaskDocument.class)
                    .field(InteractionStrategyTaskConstants.TaskFields.STATUS).in(statusList)
                    .field(InteractionStrategyTaskConstants.TaskFields.CREATE_TIME).greaterThanOrEq(startTime)
                    .field(InteractionStrategyTaskConstants.TaskFields.CREATE_TIME).lessThanOrEq(endTime)
                    .order(InteractionStrategyTaskConstants.TaskFields.CREATE_TIME) // 按创建时间升序排序
                    .offset(offset)
                    .limit(limit);
            
            return query.asList();
        } catch (Exception e) {
            log.error("查询交互策略任务异常, startTime:{}, endTime:{}, offset:{}, limit:{}", startTime, endTime, offset, limit, e);
            return new ArrayList<>();
        }
    }
} 