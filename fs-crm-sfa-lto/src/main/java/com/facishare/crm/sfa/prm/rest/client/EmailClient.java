package com.facishare.crm.sfa.prm.rest.client;

import com.facishare.crm.sfa.prm.platform.model.RestResponse;
import com.facishare.crm.sfa.prm.rest.model.EmailProxyModel;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-03-11
 * ============================================================
 */
@RestResource(value = "FsEmail", desc = "FsEmail Rest API Call", contentType = "application/json")
public interface EmailClient {
    @POST(value = "/com.facishare.open.emailproxy.task.api.service.BpmEmailService/sendEmail/1.0", desc = "发送邮件")
    RestResponse sendEmail(@HeaderMap Map<String, String> headers, @Body EmailProxyModel.EmailArg arg);
}
