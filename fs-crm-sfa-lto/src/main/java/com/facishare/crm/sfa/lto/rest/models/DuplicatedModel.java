package com.facishare.crm.sfa.lto.rest.models;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.service.dto.duplicatedSearch.GetResult;
import com.facishare.paas.metadata.api.data.IDuplicatedSearch;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> lik
 * @date : 2022/7/12 12:41
 */

public interface DuplicatedModel {
    @Data
    @Builder
    class Arg {
         String describe_api_name;
         IDuplicatedSearch.Type type;
         ObjectDataDocument object_data;
         Boolean is_need_duplicate;
         Integer page_size;
         Integer page_number;
    }

    @Data
    class Result {
        private int errCode;
        private String errMessage;
        private Map<String,List<DataResult>> result;
    }
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class DataResult {
        private List<ObjectDataDocument> data_list;
        private ObjectDescribeDocument object_describe;
        private List<GetResult.RelatedSearchInfo> related_search_infos;
        private GetResult.ButtonInfo button_info;
        private List<String> include_fields;
        private boolean is_keep_save;
        private boolean create_permission;
        private GetResult.Page page;
        private IDuplicatedSearch.Policy match_type;
        private List<String> admin_ids;
    }
}
