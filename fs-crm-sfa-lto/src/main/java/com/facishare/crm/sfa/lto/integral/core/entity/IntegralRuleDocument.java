package com.facishare.crm.sfa.lto.integral.core.entity;

import com.facishare.crm.sfa.lto.integral.core.rest.dto.GetRuleList;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class IntegralRuleDocument extends HashMap<String,Object> implements Serializable {

    public static final int STATUS_ACTIVE = 1;
    public static final int STATUS_FORBID = 0;

    /** 打分规则名称*/
    public static final String LABEL = "label";
    /** 创建人*/
    public static final String CREATE_USER_NAME = "create_by__r";
    /** 对象名称*/
    public static final String OBJECT_NAME = "obj_display_name";
    /** 对象ApiName*/
    public static final String OBJECT_API_NAME = "obj_api_name";
    /** 创建时间*/
    public static final String CREATE_TIME = "create_time";
    /** 修改人*/
    public static final String LAST_MODIFY_USER_NAME = "last_modify_user__r";
    /** 修改时间*/
    public static final String LAST_MODIFY_TIME = "last_modify_time";
    /** 备注*/
    public static final String REMARK = "remark";
    /** 状态:是否启用*/
    public static final String ACTIVE = "is_active";
    /** 对象状态: 是否启用*/
    public static final String OBJECT_ACTIVE = "is_obj_active";
    /** 对象是否删除*/
    public static final String OBJECT_DELETE = "is_obj_delete";
    /** 计算状态:是否计算中*/
    public static final String CALCULATING = "is_calculating";

    /** 打分规则ID*/
    public static final String ID = "id";
    /** 打分规则ApiName*/
    public static final String API_NAME = "api_name";
    /** 启用实时更新*/
    public static final String REALTIME_UPDATE = "is_real_time_update";
    /** 适用范围*/
    public static final String SCOPE = "scope";
    /** 创建人ID*/
    public static final String CREATE_USER = "create_by";
    /** 上次修改人ID*/
    public static final String LAST_MODIFY_USER = "last_modify_user";
    /** 企业ID*/
    public static final String TENANT_ID = "tenant_id";

    /** 指标规则*/
    public static final String RULE_ITEMS = "rule_items";
    /** 结果规则*/
    public static final String RULE_RESULTS = "rule_results";

    /**
     * 根据引擎数据组装IntegralRuleDocument
     *
     * @return IntegralRuleDocument
     */
    public static IntegralRuleDocument of(GetRuleList.RuleListInfo info) {
        IntegralRuleDocument scoringRuleDocument = new IntegralRuleDocument();
        if (Objects.isNull(info)) {
            return scoringRuleDocument;
        }
        scoringRuleDocument.put(LABEL, info.getName());
        scoringRuleDocument.put(CREATE_USER, info.getCreatedUserId());
        scoringRuleDocument.put(LAST_MODIFY_USER, info.getLastModifiedUserId());
        scoringRuleDocument.put(OBJECT_API_NAME, info.getObjectApiName());
        scoringRuleDocument.put(REMARK, info.getRemark());
        scoringRuleDocument.put(API_NAME, info.getRuleApiName());
        scoringRuleDocument.put(ID, info.getId());
        scoringRuleDocument.put(RULE_RESULTS, info.getResultRules());
        scoringRuleDocument.put(TENANT_ID, info.getTenantId());
        scoringRuleDocument.put(CREATE_TIME, info.getCreateTime());
        scoringRuleDocument.put(LAST_MODIFY_TIME, info.getLastModifiedTime());
        scoringRuleDocument.put(ACTIVE, info.getStatus() == STATUS_ACTIVE);
        return scoringRuleDocument;
    }

    public static List<IntegralRuleDocument> ofList(List<GetRuleList.RuleListInfo> infoList) {
        List<IntegralRuleDocument> integralRuleDocuments = Lists.newArrayList();
        for (GetRuleList.RuleListInfo info : infoList) {
            integralRuleDocuments.add(of(info));
        }
        return integralRuleDocuments;
    }

    /**
     * 获取规则关联对象ApiName
     * @return 关联对象ApiName
     */
    public String getObjectApiName() {
        return (String) get(OBJECT_API_NAME);
    }

    /**
     * 获取规则的ApiName
     * @return 规则的ApiName
     */
    public String getApiName() {
        return (String) get(API_NAME);
    }

    /**
     * 获取规则名称
     * @return 规则名称
     */
    public String getName() {
        return (String) get(LABEL);
    }

    /**
     * 获取创建人ID
     * @return 创建人ID
     */
    public String getCreateUserId() {
        return (String) get(CREATE_USER);
    }

    /**
     * 获取修改人ID
     * @return 修改人ID
     */
    public String getLastModifyUserId() {
        return (String) get(LAST_MODIFY_USER);
    }

    public void setRuleItems() {
        //测试
        RuleItem item = new RuleItem();
        item.setFieldName("销售线索");
        List<Map> branches = new ArrayList<>();
        Map<String,Object> map1 = new HashMap<>();
        map1.put("operator","EQ");
        map1.put("field_value", new String[]{"ABC","DJKJ"});
        map1.put("calculate_type", 0);
        map1.put("calculate_score", 1);
        branches.add(map1);
        item.setBranches(branches);
        List<RuleItem> items = new ArrayList<>();
        items.add(item);
        put(RULE_ITEMS, items);
    }


    /**
     * 指标规则
     */
    @Data
    private static class RuleItem {

        @SerializedName("filed_name")
        private String fieldName;
        @SerializedName("branches")
        private List<Map> branches;
        private RuleItem() { }
    }

    /**
     * 结果规则
     */
    @Data
    private static class RuleResult {

    }
}
