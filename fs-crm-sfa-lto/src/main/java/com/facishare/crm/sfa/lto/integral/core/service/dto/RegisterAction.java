package com.facishare.crm.sfa.lto.integral.core.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

public interface RegisterAction {
    @Data
    class Arg {
        @JSONField(name="category_api_name")
		@JsonProperty("category_api_name")
        private String categoryApiName;
        @JSONField(name="action_api_name")
		@JsonProperty("action_api_name")
        String actionApiName;
        @JSONField(name="action_label")
		@JsonProperty("action_label")
        String actionLabel;
    }

    @Data
    @Builder
    class Result {
        private boolean success;
    }
}
