package com.facishare.crm.sfa.lto.rest.models;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/3/26 16:20
 * @description:
 */
public interface IndustryCompanyAdvance {

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Result{
        private int pageSize;
        private int pageNumber;
        private int total;
        private List<IndustryCompany> data;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class CompanyAdvanceResult{
        private Integer code ;
        private String Status;
        private String Message;
        private Result Result;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class IndustryCompany{
        private String companyId;
        private String companyName;
        private String companyType;
        private String companyTypeLabel;
        private String labels;
        private String logo;
        private String simpleName;
        private String legalPerson;
        private String startedDate;
        private String regCapital;
        private String employeeNum;
        private String province;
        private String city;
        private String district;
        private String categoryFirst;
        private String categorySecond;
        private String categoryThird;
        private String categoryFourth;
        private List<Elements> phones;
        private List<Elements> emails;
        private List<Elements> websites;
        private List<Address> addresses;
        private List<String> products;
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Address{
        private String element;
        private String reportYear;
        private String type;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg {
        /**
         * 企业名称等搜索关键字
         */
        private String keyword;
        /**
         * 登记状态
         */
        private String companyStatus;
        /**
         * 主题类型
         */
        private String companyType;
        /**
         * 注册资本币种
         */
        private String currency;
        /**
         * 注册资本最小值
         */
        private Long regCapitalMin;
        /**
         * 注册资本最大值
         */
        private Long regCapitalMax;
        /**
         * 一级行业
         */
        private String categoryFirst;
        /**
         * 二级行业
         */
        private String categorySecond;
        /**
         * 三级行业
         */
        private String categoryThird;
        /**
         * 四级行业
         */
        private String categoryFourth;
        /**
         * 省
         */
        private String province;
        /**
         * 市
         */
        private String city;
        /**
         * 区
         */
        private String district;
        /**
         * 员工数量最小值
         */
        private Long employeeNumMin;
        /**
         * 员工数量最大值
         */
        private Long employeeNumMax;
        /**
         * 是否有联系电话
         */
        private Integer hasPhone;
        /**
         * 是否有邮箱
         */
        private Integer hasEmail;
        /**
         * 上市类型
         */
        private String stockType;
        /**
         * 是否有注册海关
         */
        private Integer hasRegHg;
        /**
         * 是否有投标信息
         */
        private Integer hasBid;
        /**
         * 是否有产品信息
         */
        private Integer hasProduct;
        /**
         * 是否有行政处罚信息
         */
        private Integer hasPunish;
        /**
         * 是否有被执行人
         */
        private Integer hasZhixing;
        /**
         * 是否有限销信息
         */
        private Integer hasXianxiao;
        /**
         * 是否有异常信息
         */
        private Integer hasAbnormal;
        /**
         * 排序方式（0：默认，1：成立日期从早到晚，2：成立日期从晚到早，3：注册资本从高到低，4：注册资本从低到高）
         */
        private Integer sortWay;
        /**
         * 分页大小
         */
        private Integer pageSize;
        /**
         * 当前页
         */
        private Integer pageNumber;
    }

    /**
     * 手机，邮箱，网址 相关元素的信息
     *  ["{\"reportYear\":\"2020\",\"type\":\"email\",\"element\":\"<EMAIL>\"}","{\"reportYear\":\"2019\",\"type\":\"email\",\"element\":\"<EMAIL>\"}"]
     */

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Elements{
        private String reportYear;
        private String type;
        private String element;
    }
}
