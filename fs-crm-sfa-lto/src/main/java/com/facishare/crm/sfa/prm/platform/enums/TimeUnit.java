package com.facishare.crm.sfa.prm.platform.enums;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import lombok.Getter;

import static com.facishare.crm.sfa.prm.core.constants.PrmI18NConstants.PRM_ENUM_PARAM_TYPE_ERROR;

/**
 * ============================================================
 *
 * @Description: 时间单位
 * @CreatedBy: Sundy on 2025-04-22
 * ============================================================
 */
@Getter
public enum TimeUnit {
    YEAR("year", "time.unit.year"),
    QUARTER("quarter", "time.unit.quarter"),
    MONTH("month", "time.unit.month"),
    WEEK("week", "time.unit.week"),
    DAY("day", "time.unit.day");

    private final String unit;
    private final String i18n;

    TimeUnit(String unit, String i18n) {
        this.unit = unit;
        this.i18n = i18n;
    }

    public static TimeUnit find(String unit) {
        return find(unit, null);
    }

    public static TimeUnit find(String unit, TimeUnit defaultUnit) {
        for (TimeUnit e : values()) {
            if (e.unit.equals(unit)) {
                return e;
            }
        }
        return defaultUnit;
    }

    public static TimeUnit from(String unit) {
        for (TimeUnit e : values()) {
            if (e.unit.equals(unit)) {
                return e;
            }
        }
        throw new ValidateException(I18N.text(PRM_ENUM_PARAM_TYPE_ERROR, unit));
    }
}
