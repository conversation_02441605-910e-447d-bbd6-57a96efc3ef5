package com.facishare.crm.sfa.lto.rest;

import com.facishare.crm.sfa.lto.rest.models.UserGroupModel;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

/**
 *PAAS用户组
 */
@RestResource(value = "PAAS-ORG", desc = "pass user group proxy ", contentType = "application/json")
public interface PaasUserGroupProxy {
    @POST(value = "/org/group/members", desc = "获取用户组下用户")
    UserGroupModel.UserGroupListResult getUserGroupMembers(@HeaderMap Map<String, String> headers, @Body UserGroupModel.UserGroupListWithContextArg body);

    @POST(value = "/org/group/own", desc = "批量获取用户所属用户组")
    UserGroupModel.UserGroupListPageResult getUserGroupByMemberIds(@HeaderMap Map<String, String> headers, @Body UserGroupModel.UserGroupUserListArg body);

    @POST(value = "/org/group/list", desc = "根据用户组ID获取用户组")
    UserGroupModel.UserGroupPageResult getUserGroupByIds(@HeaderMap Map<String, String> headers, @Body UserGroupModel.UserGroupListArg body);

    @POST(value = "/org/group/mem/batch", desc = "批量获取用户组下用户")
    UserGroupModel.GetUserGroupMembersResult batchGetUserGroupMembers(@HeaderMap Map<String, String> headers, @Body UserGroupModel.GetUserGroupMembersArg body);
}
