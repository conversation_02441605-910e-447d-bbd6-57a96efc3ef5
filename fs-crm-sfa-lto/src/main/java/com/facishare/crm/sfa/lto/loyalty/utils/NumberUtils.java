package com.facishare.crm.sfa.lto.loyalty.utils;

import com.facishare.paas.metadata.api.IObjectData;

import java.math.BigDecimal;

public class NumberUtils {

    public static long getLong(IObjectData data, String field) {
        Long number = data.get(field, Long.class);
        if (number == null) {
            number = 0L;
        }
        return number;
    }

    public static int getInt(IObjectData data, String field) {
        Integer number = data.get(field, Integer.class);
        if (number == null) {
            number = 0;
        }
        return number;
    }

    public static boolean notEnoughToReduce(IObjectData data, String field, long decrement) {
        return getLong(data, field) < decrement;
    }

    public static void sum(IObjectData data, String field, Long value) {
        if (value == null) {
            value = 0L;
        }
        long current = getLong(data, field);
        data.set(field, current + value);
    }

    public static BigDecimal min(BigDecimal a, BigDecimal b) {
        return a.compareTo(b) < 0 ? a : b;
    }

}
