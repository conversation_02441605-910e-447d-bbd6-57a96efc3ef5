package com.facishare.crm.sfa.lto.integral.core.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.sfa.lto.integral.common.constant.IntegralObject;
import com.facishare.crm.sfa.lto.integral.common.constant.RuleDataConstant;
import com.facishare.crm.sfa.lto.integral.core.entity.IntegralRuleDocument;
import com.facishare.crm.sfa.lto.integral.core.entity.ObjectDescribeDocument;
import com.facishare.crm.sfa.lto.integral.core.rest.dto.GetRuleDetail;
import com.facishare.paas.appframework.common.service.dto.UserInfo;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Table;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

public interface FindDetailByApiName {

    @Data
    class Arg {

        @JSONField(name = "api_name")
		@JsonProperty("api_name")
        private String apiName;
        @JSONField(name = "obj_api_name")
		@JsonProperty("obj_api_name")
        String objectDescribeApiName;
    }

    @Data
    class Result {

        @JSONField(name = "rule")
		@JsonProperty("rule")
		IntegralRuleDocument rule;
        @JSONField(name = "describe")
		@JsonProperty("describe")
		ObjectDescribeDocument describe;

        public static Result build(GetRuleDetail.RuleDetail ruleDetail, IObjectDescribe describe,
                                   List<UserInfo> userInfos, Table<String, String, String> categoryApiNameToMaterialApiNameToLabel) {
            Result result = new Result();
            IntegralRuleDocument integralRuleDocument = new IntegralRuleDocument();
            //解析引擎响应规则
            GetRuleDetail.BasicInfo basicInfo = ruleDetail.getBasicInfo();

            //规则基本信息
            integralRuleDocument.put(IntegralRuleDocument.ID, basicInfo.getId());
            integralRuleDocument.put(IntegralRuleDocument.API_NAME, basicInfo.getRuleApiName());
            integralRuleDocument.put(IntegralRuleDocument.LABEL, basicInfo.getLabel());
            integralRuleDocument.put(IntegralRuleDocument.OBJECT_API_NAME, basicInfo.getObjectDescribeApiName());
            integralRuleDocument.put(IntegralRuleDocument.REALTIME_UPDATE, basicInfo.isRealTimeUpdate());
            integralRuleDocument.put(IntegralRuleDocument.REMARK, basicInfo.getRemark());
            integralRuleDocument.put(IntegralRuleDocument.ACTIVE, basicInfo.getStatus() == IntegralRuleDocument.STATUS_ACTIVE);
            integralRuleDocument.put(IntegralRuleDocument.CREATE_TIME, basicInfo.getCreateTime());
            integralRuleDocument.put(IntegralRuleDocument.CREATE_USER, basicInfo.getCreatedBy());
            integralRuleDocument.put(IntegralRuleDocument.LAST_MODIFY_USER, basicInfo.getLastModifiedBy());
            integralRuleDocument.put(IntegralRuleDocument.LAST_MODIFY_TIME, basicInfo.getLastModifiedTime());
            integralRuleDocument.put(IntegralRuleDocument.TENANT_ID, basicInfo.getTenantId());

            //指标规则设置
            List<RuleDetail.RuleItem> ruleItems = Lists.newArrayList();
            List<GetRuleDetail.RuleGroup> ruleGroups = ruleDetail.getRuleGroups();
            //解析引擎指标规则，一个字段代表一个ruleItem
            Map<String, List<GetRuleDetail.RuleGroup>> ruleGroupMap = ruleGroups.stream().collect(
                    Collectors.groupingBy(x -> x.getFieldApiName(), LinkedHashMap::new, Collectors.toList()));
            ruleGroupMap.forEach((x, y) -> {
                RuleDetail.RuleItem ruleItem = new RuleDetail.RuleItem();
                ruleItem.setCategoryApiName(x);
                //按照priority从小到大排序RuleGroup
                y.sort(new Comparator<GetRuleDetail.RuleGroup>() {
                    @Override
                    public int compare(GetRuleDetail.RuleGroup r1, GetRuleDetail.RuleGroup r2) {
                        return r1.getPriority() - r2.getPriority();
                    }
                });

                List<RuleDetail.RuleItemBranch> branches = Lists.newArrayList();
                for (GetRuleDetail.RuleGroup g : y) {
                    RuleDetail.RuleItemBranch branch = new RuleDetail.RuleItemBranch();
                    double calculateScore = Double.parseDouble(g.getCalculateScore());
                    if (calculateScore < 0) {
                        branch.setCalculateType(RuleDetail.SUBTRACT);
                        calculateScore = Math.abs(calculateScore);
                    } else {
                        branch.setCalculateType(RuleDetail.ADD);
                    }
                    branch.setCalculateScore(calculateScore);
                    g.getRules().stream()
                            .filter(rule -> IntegralObject.FIELD_ACTION_API_NAME.equals(rule.getFieldName()))
                            .findAny().ifPresent(rule -> branch.setActionApiName(rule.getFieldValue().get(0)));
                    Optional<GetRuleDetail.Rule> materialRule = g.getRules().stream()
                            .filter(rule -> IntegralObject.FIELD_MATERIAL_API_NAME.equals(rule.getFieldName()))
                            .findAny();
                    if (materialRule.isPresent()) {
                        branch.setMaterialApiNames(materialRule.get().getFieldValue());
                    } else {
                        branch.setMaterialApiNames(Lists.newArrayList(RuleDataConstant.MATERIAL_API_NAME_ANY_ONE));
                    }
                    if (branch.getMaterialApiNameMap() == null) {
                        branch.setMaterialApiNameMap(Maps.newHashMap());
                    }
                    branch.getMaterialApiNames().forEach(materialApiName -> {
                        //如果是任意一个，直接赋值并返回
                        if (RuleDataConstant.MATERIAL_API_NAME_ANY_ONE.equals(materialApiName)) {
                            branch.getMaterialApiNameMap().put(materialApiName, RuleDataConstant.getAnyOneLabel());
                            return;
                        }
                        String label = categoryApiNameToMaterialApiNameToLabel.get(x, materialApiName);
                        //如果label是空，则赋值空字符串
                        if (StringUtils.isEmpty(label)) {
                            label = "";
                        }
                        branch.getMaterialApiNameMap().put(materialApiName, label);
                    });
                    branches.add(branch);
                }
                ruleItem.setBranches(branches);
                ruleItems.add(ruleItem);
            });
            integralRuleDocument.put(IntegralRuleDocument.RULE_ITEMS, ruleItems);
            //结果规则解析 (历史插入使用的gson)
            List<RuleDetail.RuleResult> ruleResultList = new Gson().fromJson(basicInfo.getRuleResult(), new TypeToken<List<RuleDetail.RuleResult>>() {}.getType());
            integralRuleDocument.put(IntegralRuleDocument.RULE_RESULTS, ruleResultList);
            integralRuleDocument.put(IntegralRuleDocument.OBJECT_NAME, describe.getDisplayName());
            //创建人和修改人
            for (UserInfo userInfo : userInfos) {
                if (userInfo.getId().equals(basicInfo.getCreatedBy())) {
                    integralRuleDocument.put(IntegralRuleDocument.CREATE_USER_NAME, userInfo.getName());
                }

                if (userInfo.getId().equals(basicInfo.getLastModifiedBy())) {
                    integralRuleDocument.put(IntegralRuleDocument.LAST_MODIFY_USER_NAME, userInfo.getName());
                }
            }

            result.setRule(integralRuleDocument);
            result.setDescribe(ObjectDescribeDocument.of(describe));

            return result;
        }
    }

}
