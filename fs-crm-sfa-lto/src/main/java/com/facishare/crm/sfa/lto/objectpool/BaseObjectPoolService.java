package com.facishare.crm.sfa.lto.objectpool;

import com.facishare.crm.sfa.lto.common.LtoOrgCommonService;
import com.facishare.crm.sfa.lto.common.SFALtoRedisService;
import com.facishare.crm.sfa.lto.exception.ExceptionUtil;
import com.facishare.crm.sfa.lto.objectpool.models.ObjectPoolActionModels;
import com.facishare.crm.sfa.lto.objectpool.models.ObjectPoolModels;
import com.facishare.crm.sfa.lto.utils.CommonUtil;
import com.facishare.crm.sfa.lto.utils.ObjectDataUtil;
import com.facishare.crm.sfa.lto.utils.SFALtoBizLogUtil;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.fxiaoke.common.SqlEscaper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.Strings;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
public abstract class BaseObjectPoolService implements IObjectPoolService {
    @Autowired
    protected ServiceFacade serviceFacade;

    @Autowired
    protected LtoOrgCommonService ltoOrgCommonService;

    @Autowired
    protected ObjectDataServiceImpl objectDataService;
    @Autowired
    private SFALtoRedisService sfaLtoRedisService;

    @Override
    public String getObjectPoolApiName() {
        return null;
    }

    @Override
    public String getObjectApiName() {
        return null;
    }

    @Override
    public IObjectData getObjectPoolById(String tenantId, String objectId) {
        if (StringUtils.isEmpty(objectId)) {
            return null;
        }
        List<IObjectData> objectDataList = getObjectPoolByIds(tenantId, Lists.newArrayList(objectId));
        if (CollectionUtils.notEmpty(objectDataList)) {
            return objectDataList.get(0);
        }
        return null;
    }

    @Override
    public List<IObjectData> getObjectPoolByIds(String tenantId, List<String> objectIds) {
        if (CollectionUtils.empty(objectIds)) {
            return Lists.newArrayList();
        }
        return serviceFacade.findObjectDataByIds(tenantId, objectIds, getObjectPoolApiName());
    }

    @Override
    public ObjectPoolModels.ObjectPoolPermissions getPoolPermission(String tenantId, String userId, String objectPoolId, String outTenantId) {
        ObjectPoolModels.ObjectPoolPermissions result = ObjectPoolModels.ObjectPoolPermissions.NO_PERMISSION;
        List<Map> queryResult = getObjectPoolPermissionByPoolIds(tenantId, Lists.newArrayList(objectPoolId), ObjectPoolModels.ObjectPoolPermissions.POOL_ALL);
        if (CollectionUtils.empty(queryResult)) {
            return result;
        }

        boolean isAdmin;
        boolean isMember;

        List<Map> admins = queryResult.stream().filter(this::getObjectPoolPermissionIsAdmin)
                .collect(Collectors.toList());

        List<String> deptIds = ltoOrgCommonService.getNDeptPathByUserId(tenantId, userId);
        List<String> userGroupIds = ltoOrgCommonService.getUserGroupIdsByMemberId(tenantId, userId);
        List<String> userRoleIds = ltoOrgCommonService.getUserRoleIdsByUserId(tenantId, userId);

        isAdmin = admins.stream().anyMatch(x -> userId.equals(getObjectPoolPermissionDataId(x))
                && (ObjectPoolModels.ObjectPoolMemberType.EMPLOYEE.getValue().equals(getObjectPoolPermissionType(x))
                || ObjectPoolModels.ObjectPoolMemberType.OUTER_EMPLOYEE.getValue().equals(getObjectPoolPermissionType(x))));

        if (!isAdmin && CollectionUtils.notEmpty(deptIds)) {
            isAdmin = admins.stream().anyMatch(x -> ObjectPoolModels.ObjectPoolMemberType.CIRCLE.getValue().equals(getObjectPoolPermissionType(x))
                    && deptIds.contains(getObjectPoolPermissionDataId(x)));
        }

        if (!isAdmin && CollectionUtils.notEmpty(userGroupIds)) {
            isAdmin = admins.stream().anyMatch(x -> ObjectPoolModels.ObjectPoolMemberType.USERGROUP.getValue().equals(getObjectPoolPermissionType(x))
                    && userGroupIds.contains(getObjectPoolPermissionDataId(x)));
        }

        if (!isAdmin && CollectionUtils.notEmpty(userRoleIds)) {
            isAdmin = admins.stream().anyMatch(x -> ObjectPoolModels.ObjectPoolMemberType.USERROLE.getValue().equals(getObjectPoolPermissionType(x))
                    && userRoleIds.contains(getObjectPoolPermissionDataId(x)));
        }

        List<Map> members = queryResult.stream().filter(x -> !getObjectPoolPermissionIsAdmin(x))
                .collect(Collectors.toList());
        isMember = members.stream().anyMatch(x -> userId.equals(getObjectPoolPermissionDataId(x))
                && (ObjectPoolModels.ObjectPoolMemberType.EMPLOYEE.getValue().equals(getObjectPoolPermissionType(x))
                || ObjectPoolModels.ObjectPoolMemberType.OUTER_EMPLOYEE.getValue().equals(getObjectPoolPermissionType(x))));

        if (!isMember && CollectionUtils.notEmpty(deptIds)) {
            isMember = members.stream().anyMatch(x -> ObjectPoolModels.ObjectPoolMemberType.CIRCLE.getValue().equals(getObjectPoolPermissionType(x))
                    && deptIds.contains(getObjectPoolPermissionDataId(x)));
        }

        if (!isMember && CollectionUtils.notEmpty(userGroupIds)) {
            isMember = members.stream().anyMatch(x -> ObjectPoolModels.ObjectPoolMemberType.USERGROUP.getValue().equals(getObjectPoolPermissionType(x))
                    && userGroupIds.contains(getObjectPoolPermissionDataId(x)));
        }

        if (!isMember && CollectionUtils.notEmpty(userRoleIds)) {
            isMember = members.stream().anyMatch(x -> ObjectPoolModels.ObjectPoolMemberType.USERROLE.getValue().equals(getObjectPoolPermissionType(x))
                    && userRoleIds.contains(getObjectPoolPermissionDataId(x)));
        }

        if (!isMember && !Strings.isNullOrEmpty(outTenantId)) {
            isMember = members.stream().anyMatch(x -> outTenantId.equals(getObjectPoolPermissionDataId(x))
                    && ObjectPoolModels.ObjectPoolMemberType.OUTER_ENTERPRISE.getValue().equals(getObjectPoolPermissionType(x)));
        }

        if (isAdmin && isMember) {
            return ObjectPoolModels.ObjectPoolPermissions.POOL_ALL;
        } else if (isAdmin) {
            return ObjectPoolModels.ObjectPoolPermissions.POOL_ADMIN;
        } else if (isMember) {
            return ObjectPoolModels.ObjectPoolPermissions.POOL_MEMBER;
        } else {
            return ObjectPoolModels.ObjectPoolPermissions.NO_PERMISSION;
        }
    }

    @Override
    public Map<String,ObjectPoolModels.ObjectPoolPermissions> getPoolPermission(String tenantId, String userId, List<String> objectPoolIds, String outTenantId) {
        Map<String,ObjectPoolModels.ObjectPoolPermissions> result = Maps.newHashMap();
        objectPoolIds.forEach(x -> result.put(x, ObjectPoolModels.ObjectPoolPermissions.NO_PERMISSION));
        List<Map> queryResult = getObjectPoolPermissionByPoolIds(tenantId, objectPoolIds, ObjectPoolModels.ObjectPoolPermissions.POOL_ALL);
        if (CollectionUtils.empty(queryResult)) {
            return result;
        }

        for(String poolId : objectPoolIds) {
            ObjectPoolModels.ObjectPoolPermissions objectPoolPermissions;
            boolean isAdmin;
            boolean isMember;

            List<Map> poolPermissionsList = queryResult.stream().filter(x -> poolId.equals(String.valueOf(x.get("pool_id")))).collect(Collectors.toList());
            List<Map> admins = poolPermissionsList.stream().filter(this::getObjectPoolPermissionIsAdmin)
                    .collect(Collectors.toList());

            List<String> deptIds = ltoOrgCommonService.getNDeptPathByUserId(tenantId, userId);
            List<String> userGroupIds = ltoOrgCommonService.getUserGroupIdsByMemberId(tenantId, userId);
            List<String> userRoleIds = ltoOrgCommonService.getUserRoleIdsByUserId(tenantId, userId);

            isAdmin = admins.stream().anyMatch(x -> userId.equals(getObjectPoolPermissionDataId(x))
                    && (ObjectPoolModels.ObjectPoolMemberType.EMPLOYEE.getValue().equals(getObjectPoolPermissionType(x))
                    || ObjectPoolModels.ObjectPoolMemberType.OUTER_EMPLOYEE.getValue().equals(getObjectPoolPermissionType(x))));

            if (!isAdmin && CollectionUtils.notEmpty(deptIds)) {
                isAdmin = admins.stream().anyMatch(x -> ObjectPoolModels.ObjectPoolMemberType.CIRCLE.getValue().equals(getObjectPoolPermissionType(x))
                        && deptIds.contains(getObjectPoolPermissionDataId(x)));
            }

            if (!isAdmin && CollectionUtils.notEmpty(userGroupIds)) {
                isAdmin = admins.stream().anyMatch(x -> ObjectPoolModels.ObjectPoolMemberType.USERGROUP.getValue().equals(getObjectPoolPermissionType(x))
                        && userGroupIds.contains(getObjectPoolPermissionDataId(x)));
            }

            if (!isAdmin && CollectionUtils.notEmpty(userRoleIds)) {
                isAdmin = admins.stream().anyMatch(x -> ObjectPoolModels.ObjectPoolMemberType.USERROLE.getValue().equals(getObjectPoolPermissionType(x))
                        && userRoleIds.contains(getObjectPoolPermissionDataId(x)));
            }

            List<Map> members = poolPermissionsList.stream().filter(x -> !getObjectPoolPermissionIsAdmin(x))
                    .collect(Collectors.toList());
            isMember = members.stream().anyMatch(x -> userId.equals(getObjectPoolPermissionDataId(x))
                    && (ObjectPoolModels.ObjectPoolMemberType.EMPLOYEE.getValue().equals(getObjectPoolPermissionType(x))
                    || ObjectPoolModels.ObjectPoolMemberType.OUTER_EMPLOYEE.getValue().equals(getObjectPoolPermissionType(x))));

            if (!isMember && CollectionUtils.notEmpty(deptIds)) {
                isMember = members.stream().anyMatch(x -> ObjectPoolModels.ObjectPoolMemberType.CIRCLE.getValue().equals(getObjectPoolPermissionType(x))
                        && deptIds.contains(getObjectPoolPermissionDataId(x)));
            }

            if (!isMember && CollectionUtils.notEmpty(userGroupIds)) {
                isMember = members.stream().anyMatch(x -> ObjectPoolModels.ObjectPoolMemberType.USERGROUP.getValue().equals(getObjectPoolPermissionType(x))
                        && userGroupIds.contains(getObjectPoolPermissionDataId(x)));
            }

            if (!isMember && CollectionUtils.notEmpty(userRoleIds)) {
                isMember = members.stream().anyMatch(x -> ObjectPoolModels.ObjectPoolMemberType.USERROLE.getValue().equals(getObjectPoolPermissionType(x))
                        && userRoleIds.contains(getObjectPoolPermissionDataId(x)));
            }

            if (!isMember && !Strings.isNullOrEmpty(outTenantId)) {
                isMember = members.stream().anyMatch(x -> outTenantId.equals(getObjectPoolPermissionDataId(x))
                        && ObjectPoolModels.ObjectPoolMemberType.OUTER_ENTERPRISE.getValue().equals(getObjectPoolPermissionType(x)));
            }

            if (isAdmin && isMember) {
                objectPoolPermissions = ObjectPoolModels.ObjectPoolPermissions.POOL_ALL;
            } else if (isAdmin) {
                objectPoolPermissions = ObjectPoolModels.ObjectPoolPermissions.POOL_ADMIN;
            } else if (isMember) {
                objectPoolPermissions = ObjectPoolModels.ObjectPoolPermissions.POOL_MEMBER;
            } else {
                objectPoolPermissions = ObjectPoolModels.ObjectPoolPermissions.NO_PERMISSION;
            }

            result.put(poolId, objectPoolPermissions);
        }

        return result;
    }

    @Override
    public List<String> getPoolAdminById(User user, String poolId) {
        List<String> adminIds = Lists.newArrayList();
        Map<String, List<String>> poolAdminMap = getPoolAdminByIds(user, Lists.newArrayList(poolId));
        if (CollectionUtils.notEmpty(poolAdminMap) && poolAdminMap.containsKey(poolId)) {
            adminIds.addAll(poolAdminMap.get(poolId));
        }
        return adminIds;
    }

    @Override
    public Map<String, List<String>> getPoolAdminByIds(User user, List<String> poolIds) {
        if (CollectionUtils.empty(poolIds)) {
            return Maps.newHashMap();
        }
        return getPoolMemberByPoolIds(user.getTenantId(), poolIds,
                ObjectPoolModels.ObjectPoolPermissions.POOL_ADMIN);
    }

    @Override
    public Map<String, List<String>> getInAndOutPoolAdminById(User user, String poolId) {
        Map<String, List<String>> inAndOutPoolAdminList = Maps.newHashMap();
        String tenantId = user.getTenantId();
        List<Map> queryResult = getObjectPoolPermissionByPoolIds(tenantId, Lists.newArrayList(poolId),
                ObjectPoolModels.ObjectPoolPermissions.POOL_ADMIN);
        if (CollectionUtils.empty(queryResult)) {
            return inAndOutPoolAdminList;
        }

        Set<String> inAdminIds = getPoolPermissionDataIdsByTypes(queryResult,
                Lists.newArrayList(ObjectPoolModels.ObjectPoolMemberType.EMPLOYEE.getValue()));

        Set<String> circleIds = getPoolPermissionDataIdsByTypes(queryResult,
                Lists.newArrayList(ObjectPoolModels.ObjectPoolMemberType.CIRCLE.getValue()));
        List<String> memberIds = ltoOrgCommonService.getMembersByDeptIds(tenantId, Lists.newArrayList(circleIds), false);
        if (CollectionUtils.notEmpty(memberIds)) {
            inAdminIds.addAll(memberIds);
        }

        Set<String> userGroupIds = getPoolPermissionDataIdsByTypes(queryResult,
                Lists.newArrayList(ObjectPoolModels.ObjectPoolMemberType.USERGROUP.getValue()));
        List<String> userGroupMembers = ltoOrgCommonService.batchGetMembersByUserGroupIds(user.getTenantId(), Lists.newArrayList(userGroupIds), false);
        if (CollectionUtils.notEmpty(userGroupMembers)) {
            inAdminIds.addAll(userGroupMembers);
        }

        Set<String> userRoleIds = getPoolPermissionDataIdsByTypes(queryResult,
                Lists.newArrayList(ObjectPoolModels.ObjectPoolMemberType.USERROLE.getValue()));
        List<String> userRoleMembers = ltoOrgCommonService.batchGetRoleUsersByRoleIds(user.getTenantId(), Lists.newArrayList(userRoleIds), false);
        if (CollectionUtils.notEmpty(userRoleMembers)) {
            inAdminIds.addAll(userRoleMembers);
        }

        if (!CollectionUtils.empty(inAdminIds)) {
            inAndOutPoolAdminList.put("in", new ArrayList<>(inAdminIds));
        }

        Set<String> outAdminIds = getPoolPermissionDataIdsByTypes(queryResult,
                Lists.newArrayList(ObjectPoolModels.ObjectPoolMemberType.OUTER_EMPLOYEE.getValue()));
        if (!CollectionUtils.empty(outAdminIds)) {
            inAndOutPoolAdminList.put("out", new ArrayList<>(outAdminIds));
        }
        return inAndOutPoolAdminList;
    }

    @Override
    public List<IObjectData> getAllObjectPoolList(String tenantId, String userId, String outTenantId) {
        List<String> poolIds = getObjectPoolIdsByUser(tenantId, userId, outTenantId,
                ObjectPoolModels.ObjectPoolPermissions.POOL_ALL);
        if (CollectionUtils.empty(poolIds)) {
            return Lists.newArrayList();
        }
        return getObjectPoolByIds(tenantId, poolIds);
    }

    @Override
    public List<IObjectData> getAllVisiblePoolList(String tenantId, String userId, String outTenantId) {
        Set<IObjectData> result = Sets.newHashSet();
        List<String> adminPoolIds = getObjectPoolIdsByUser(tenantId, userId, outTenantId,
                ObjectPoolModels.ObjectPoolPermissions.POOL_ADMIN);
        List<IObjectData> adminPoolList = getObjectPoolByIds(tenantId, adminPoolIds);
        if (CollectionUtils.notEmpty(adminPoolList)) {
            result.addAll(adminPoolList);
        }
        List<IObjectData> allInvolvedPoolList = getAllObjectPoolList(tenantId, userId, outTenantId);
        if (CollectionUtils.notEmpty(allInvolvedPoolList)) {
            List<IObjectData> visiblePoolList = allInvolvedPoolList.stream()
                    .filter(objectData -> ObjectDataUtil.getBooleanValue(objectData, "is_visible_to_member", false))
                    .collect(Collectors.toList());
            if (CollectionUtils.notEmpty(visiblePoolList)) {
                result.addAll(visiblePoolList);
            }
        }
        return Lists.newArrayList(result);
    }

    @Override
    public Map<String, List<String>> getPoolMembersByIds(User user, List<String> poolIds) {
        if (CollectionUtils.empty(poolIds)) {
            return Maps.newHashMap();
        }
        return getPoolMemberByPoolIds(user.getTenantId(), poolIds, ObjectPoolModels.ObjectPoolPermissions.POOL_MEMBER);
    }

    @Override
    public List<IObjectData> getObjectPoolAdminByUser(User user) {
        List<String> poolIds = getObjectPoolIdsByUser(user, ObjectPoolModels.ObjectPoolPermissions.POOL_ADMIN);
        if (CollectionUtils.empty(poolIds)) {
            return Lists.newArrayList();
        }
        return getObjectPoolByIds(user.getTenantId(), poolIds);
    }

    @Override
    public List<IObjectData> getObjectPoolMemberByUser(User user) {
        List<String> poolIds = getObjectPoolIdsByUser(user, ObjectPoolModels.ObjectPoolPermissions.POOL_MEMBER);
        if (CollectionUtils.empty(poolIds)) {
            return Lists.newArrayList();
        }
        return getObjectPoolByIds(user.getTenantId(), poolIds);
    }

    @Override
    public List<IObjectData> getAllObjectPools(User user) {
        return null;
    }

    protected Map<String, List<String>> getPoolMemberByPoolIds(String tenantId, List<String> poolIds,
                                                               ObjectPoolModels.ObjectPoolPermissions poolPermissions) {
        Map<String, List<String>> poolMemberMap = Maps.newHashMap();
        if (CollectionUtils.empty(poolIds)) {
            return poolMemberMap;
        }

        List<Map> queryResult = getObjectPoolPermissionByPoolIds(tenantId, poolIds, poolPermissions);
        if (CollectionUtils.empty(queryResult)) {
            return poolMemberMap;
        }

        poolIds.forEach(m -> {
            List<Map> poolPermissionList = queryResult.stream().filter(x -> m.equals(getObjectPoolPermissionPoolId(x)))
                    .collect(Collectors.toList());

            Set<String> memberIds = getPoolPermissionDataIdsByTypes(poolPermissionList,
                    Lists.newArrayList(ObjectPoolModels.ObjectPoolMemberType.EMPLOYEE.getValue(),
                            ObjectPoolModels.ObjectPoolMemberType.OUTER_EMPLOYEE.getValue(),
                            ObjectPoolModels.ObjectPoolMemberType.OUTER_ENTERPRISE.getValue()));

            Set<String> circleIds = getPoolPermissionDataIdsByTypes(poolPermissionList,
                    Lists.newArrayList(ObjectPoolModels.ObjectPoolMemberType.CIRCLE.getValue()));
            List<String> deptMemberIds = ltoOrgCommonService.getMembersByDeptIds(tenantId, Lists.newArrayList(circleIds), false);
            if (CollectionUtils.notEmpty(deptMemberIds)) {
                memberIds.addAll(deptMemberIds);
            }

            Set<String> userGroupIds = getPoolPermissionDataIdsByTypes(poolPermissionList,
                    Lists.newArrayList(ObjectPoolModels.ObjectPoolMemberType.USERGROUP.getValue()));
            List<String> userGroupMembers = ltoOrgCommonService.batchGetMembersByUserGroupIds(tenantId, Lists.newArrayList(userGroupIds), false);
            if (CollectionUtils.notEmpty(userGroupMembers)) {
                memberIds.addAll(userGroupMembers);
            }

            Set<String> userRoleIds = getPoolPermissionDataIdsByTypes(poolPermissionList,
                    Lists.newArrayList(ObjectPoolModels.ObjectPoolMemberType.USERROLE.getValue()));
            List<String> userRoleMembers = ltoOrgCommonService.batchGetRoleUsersByRoleIds(tenantId, Lists.newArrayList(userRoleIds), false);
            if (CollectionUtils.notEmpty(userRoleMembers)) {
                memberIds.addAll(userRoleMembers);
            }

            poolMemberMap.put(m, Lists.newArrayList(memberIds));
        });
        return poolMemberMap;
    }

    protected List<Map> getObjectPoolPermissionByPoolIds(String tenantId, List<String> poolIds,
                                                         ObjectPoolModels.ObjectPoolPermissions poolPermissions) {
        List<Map> result = Lists.newArrayList();
        try {
            poolIds = poolIds.stream().distinct().collect(Collectors.toList());
            String querySql = String.format("SELECT pool_id,type,data_id,is_admin FROM biz_pool_permission " +
                            "WHERE tenant_id='%s' AND object_api_name='%s' AND is_deleted=0 AND pool_id = %s ",
                    SqlEscaper.pg_escape(tenantId), getObjectPoolApiName(), SqlEscaper.any_clause(poolIds));
            if (poolPermissions.getValue().equals(ObjectPoolModels.ObjectPoolPermissions.POOL_ADMIN.getValue())) {
                querySql += " AND is_admin='t' ";
            } else if (poolPermissions.getValue().equals(ObjectPoolModels.ObjectPoolPermissions.POOL_MEMBER.getValue())) {
                querySql += " AND is_admin='f' ";
            }

            result = objectDataService.findBySql(tenantId, querySql);
        } catch (Exception e) {
            log.error("getObjectPoolPermissionByPoolIds error {}", tenantId, e);
            ExceptionUtil.throwCommonBusinessException();
        }
        return result;
    }

    protected String getObjectPoolPermissionPoolId(Map x) {
        return ObjectDataUtil.getStringValue(x, "pool_id", "");
    }

    protected String getObjectPoolPermissionType(Map x) {
        return ObjectDataUtil.getStringValue(x, "type", "");
    }

    protected String getObjectPoolPermissionDataId(Map x) {
        return ObjectDataUtil.getStringValue(x, "data_id", "");
    }

    protected boolean getObjectPoolPermissionIsAdmin(Map x) {
        return ObjectDataUtil.getBooleanValue(x, "is_admin", false);
    }

    @NotNull
    protected Set<String> getPoolPermissionDataIdsByTypes(List<Map> queryResult, List<String> typeList) {
        if (CollectionUtils.empty(queryResult)) {
            return Sets.newHashSet();
        }
        return queryResult.stream().filter(x -> typeList.contains(getObjectPoolPermissionType(x)))
                .map(this::getObjectPoolPermissionDataId)
                .collect(Collectors.toSet());
    }

    protected List<String> getObjectPoolIdsByUser(String tenantId, String userId, String outTenantId,
                                                  ObjectPoolModels.ObjectPoolPermissions poolPermissions) {
        List<String> result = Lists.newArrayList();

        try {
            List<Map> queryResult = getObjectPoolPermissionByUser(tenantId, userId, outTenantId, poolPermissions);
            if (CollectionUtils.empty(queryResult)) {
                return result;
            }
            Set<String> poolIds = queryResult.stream().map(this::getObjectPoolPermissionPoolId).collect(Collectors.toSet());
            result.addAll(poolIds);
        } catch (Exception e) {
            log.error("getObjectPoolByUser error", e);
            ExceptionUtil.throwCommonBusinessException();
        }

        return result;
    }

    protected List<Map> getObjectPoolPermissionByUser(String tenantId, String userId, String outTenantId,
                                                      ObjectPoolModels.ObjectPoolPermissions poolPermissions) {
        List<Map> result = Lists.newArrayList();
        String queryString = String.format("SELECT pool_id,type,data_id,is_admin FROM biz_pool_permission WHERE tenant_id='%s' " +
                "AND object_api_name='%s' AND is_deleted=0 ", SqlEscaper.pg_escape(tenantId), getObjectPoolApiName());
        if (poolPermissions.getValue().equals(ObjectPoolModels.ObjectPoolPermissions.POOL_ADMIN.getValue())) {
            queryString += " AND is_admin='t' ";
        } else if (poolPermissions.getValue().equals(ObjectPoolModels.ObjectPoolPermissions.POOL_MEMBER.getValue())) {
            queryString += " AND is_admin='f' ";
        }

        List<String> userFilters = Lists.newArrayList();
        userFilters.add(String.format("(data_id='%s' AND type IN ('%S', '%s'))", userId,
                ObjectPoolModels.ObjectPoolMemberType.EMPLOYEE.getValue(), ObjectPoolModels.ObjectPoolMemberType.OUTER_EMPLOYEE.getValue()));

        if (StringUtils.isNotEmpty(outTenantId)) {
            userFilters.add(String.format("(data_id='%s' AND type='%s')", outTenantId,
                    ObjectPoolModels.ObjectPoolMemberType.OUTER_ENTERPRISE.getValue()));
        }

        List<String> deptIds = ltoOrgCommonService.getNDeptPathByUserId(tenantId, userId);
        if (CollectionUtils.notEmpty(deptIds)) {
            String idString = CommonUtil.buildSqlInString(deptIds);
            userFilters.add(String.format("(data_id IN (%s) AND type='%s')", idString,
                    ObjectPoolModels.ObjectPoolMemberType.CIRCLE.getValue()));
        }

        List<String> userGroupIds = ltoOrgCommonService.getUserGroupIdsByMemberId(tenantId, userId);
        if (CollectionUtils.notEmpty(userGroupIds)) {
            String idString = CommonUtil.buildSqlInString(userGroupIds);
            userFilters.add(String.format("(data_id IN (%s) AND type='%s')", idString,
                    ObjectPoolModels.ObjectPoolMemberType.USERGROUP.getValue()));
        }

        List<String> userRoleIds = ltoOrgCommonService.getUserRoleIdsByUserId(tenantId, userId);
        if (CollectionUtils.notEmpty(userRoleIds)) {
            String idString = CommonUtil.buildSqlInString(userRoleIds);
            userFilters.add(String.format("(data_id IN (%s) AND type='%s')", idString,
                    ObjectPoolModels.ObjectPoolMemberType.USERROLE.getValue()));
        }

        String userFilter = String.join(" OR ", userFilters);

        if (StringUtils.isNotBlank(userFilter)) {
            queryString += String.format(" AND (%s)", userFilter);
        }
        try {
            List<Map> queryResult = objectDataService.findBySql(tenantId, queryString);
            if (CollectionUtils.empty(queryResult)) {
                return result;
            }
            result.addAll(queryResult);
        } catch (Exception e) {
            log.error("getObjectPoolPermissionByUser error", e);
            ExceptionUtil.throwCommonBusinessException();
        }

        return result;
    }

    protected List<String> getObjectPoolIdsByUser(User user,
                                                  ObjectPoolModels.ObjectPoolPermissions poolPermissions) {
        String tenantId = user.getTenantId();
        String userId = user.isOutUser() ? user.getOutUserId() : user.getUpstreamOwnerIdOrUserId();
        String outTenantId = user.getOutTenantId();

        return getObjectPoolIdsByUser(tenantId, userId, outTenantId, poolPermissions);
    }

    @Override
    public void replacePoolId(ObjectPoolActionModels.ReplacePoolIdMsg msg) {
        try {
            User user = new User(msg.getTenantId(), msg.getUserId(), "", "");
            String objectApiName = msg.getObjectApiName();
            for(int executeCount = 0, maxExecuteCount = 10000 ; executeCount < maxExecuteCount ; executeCount++){
                if (executeCount >= maxExecuteCount - 1) {
                    log.warn("BaseObjectPoolService#movePoolObjects reaches loop limit, limit:{}", executeCount); //日志打印
                    //上报audit_log
                    SFALtoBizLogUtil.sendAuditLog(SFALtoBizLogUtil.Arg.builder()
                            .action("sfa_loop_limit")
                            .objectApiNames(objectApiName)
                            .message("BaseObjectPoolService.movePoolObjects").build(), user);
                    break;
                }

                SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
                searchTemplateQuery.setLimit(200);
                List<IFilter> filters = Lists.newArrayList();
                SearchUtil.fillFilterEq(filters, msg.getObjectPoolKeyName(), msg.getSourcePoolId());
                SearchUtil.fillFilterGTE(filters, "is_deleted", 0);
                searchTemplateQuery.setFilters(filters);
                searchTemplateQuery.setNeedReturnCountNum(false);
                searchTemplateQuery.setPermissionType(0);
                QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, objectApiName, searchTemplateQuery);
                if (queryResult == null || CollectionUtils.empty(queryResult.getData())) {
                    log.warn("BaseObjectPoolService#movePoolObjects queryResult is null ");
                    break;
                }
                List<IObjectData> dataList = queryResult.getData();
                dataList.forEach(x -> x.set(msg.getObjectPoolKeyName(), msg.getTargetPoolId()));
                serviceFacade.batchUpdateByFields(user, dataList, Lists.newArrayList(msg.getObjectPoolKeyName()));
            }
        } catch (Exception e) {
            log.error("BaseObjectPoolService replacePoolId error", e);
        } finally {
            if (!StringUtils.isEmpty(msg.getRedisRequestId())) {
                sfaLtoRedisService.releaseLock(msg.getTenantId(), msg.getObjectPoolApiName(), ObjectAction.UPDATE.getActionCode(), msg.getSourcePoolId(), msg.getRedisRequestId());
            }
        }
    }


    @Override
    public void deleteAllPoolData(ObjectPoolActionModels.ReplacePoolIdMsg msg){
        User user = new User(msg.getTenantId(), msg.getUserId(), "", "");
        try {
            log.info("BaseObjectPoolService deleteAllPoolData msg:{}",msg);
            for(int executeCount = 0, maxExecuteCount = 10000 ; executeCount < maxExecuteCount ; executeCount++){
                    if (executeCount >= maxExecuteCount - 1) {
                        log.warn("BaseObjectPoolService#deleteAllPoolData reaches loop limit, limit:{}", executeCount); //日志打印
                        //上报audit_log
                        SFALtoBizLogUtil.sendAuditLog(SFALtoBizLogUtil.Arg.builder()
                                .action("sfa_loop_limit")
                                .objectApiNames(msg.getObjectPoolApiName())
                                .message("BaseObjectPoolService.deleteAllPoolData").build(), user);
                        return;
                    }

                    SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
                    searchTemplateQuery.setLimit(500);
                    searchTemplateQuery.setPermissionType(0);
                    searchTemplateQuery.setNeedReturnCountNum(false);
                    List filters = Lists.newLinkedList();
                    SearchUtil.fillFilterEq(filters, msg.getObjectPoolKeyName(), msg.getTargetPoolId());
                    SearchUtil.fillFilterGTE(filters, DBRecord.IS_DELETED, 0);
                    SearchUtil.fillFilterEq(filters, Tenantable.TENANT_ID, user.getTenantId());
                    searchTemplateQuery.setFilters(filters);
                    QueryResult<IObjectData> dataList = serviceFacade.findBySearchQuery(user, msg.getObjectApiName(), searchTemplateQuery);
                    if (CollectionUtils.empty(dataList.getData())) {
                        log.warn("BaseObjectPoolService deleteAllPoolData dataList is null");
                        return;
                    }
                    serviceFacade.bulkInvalidAndDeleteWithSuperPrivilege(dataList.getData(), user);

                    try {
                        //删除从对象
                        IObjectDescribe objectDescribe = this.serviceFacade.findObject(user.getTenantId(), msg.getObjectApiName());
                        ObjectDataUtil.dealWithDetail(dataList.getData(), user, objectDescribe);
                    } catch (Exception e) {
                        log.error("BaseObjectPoolService dealWithDetail error:", e);
                    }
            }
            } catch (Exception e) {
                log.error("BaseObjectPoolService deleteAllPoolData error", e);
                throw e;
            } finally {
                if (!StringUtils.isEmpty(msg.getRedisRequestId())) {
                    sfaLtoRedisService.releaseLock(user.getTenantId(), msg.getObjectApiName(), ObjectAction.UPDATE.getActionCode(), msg.getTargetPoolId(), msg.getRedisRequestId());
                }
            }
        }
}
