package com.facishare.crm.sfa.lto.integral.core.entity;

import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.DocumentBasedBean;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.google.common.collect.Maps;
import lombok.ToString;
import lombok.experimental.Delegate;
import org.bson.Document;

import java.util.Map;
import java.util.Objects;

@ToString
public class ObjectDescribeDocument implements Map<String, Object> {
    @Delegate
    protected Map<String, Object> data;

    public ObjectDescribeDocument() {
        this.data = Maps.newLinkedHashMap();
    }

    public ObjectDescribeDocument(Map<String,Object> data){
        Objects.requireNonNull(data);
        this.data = data;
    }

    public IObjectDescribe toObjectDescribe() {
        return new ObjectDescribe(new Document(data));
    }

    public static ObjectDescribeDocument of(IObjectDescribe objectDescribe) {
        if (objectDescribe == null) {
            return null;
        }
        return new ObjectDescribeDocument(((DocumentBasedBean) objectDescribe).getContainerDocument());
    }
}
