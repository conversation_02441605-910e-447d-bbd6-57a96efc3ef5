package com.facishare.crm.sfa.lto.objectpool;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.lto.common.models.LtoFieldApiConstants;
import com.facishare.crm.sfa.lto.common.models.LtoLeadsFieldApiConstants;
import com.facishare.crm.sfa.lto.exception.ExceptionUtil;
import com.facishare.crm.sfa.lto.objectpool.models.ObjectPoolActionModels;
import com.facishare.crm.sfa.lto.utils.ActionContextUtil;
import com.facishare.crm.sfa.lto.utils.ObjectDataUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class LeadsPoolActionServiceImpl extends BasePoolActionService{
    private static List<String> editExceptFields = Lists.newArrayList(LtoLeadsFieldApiConstants.IS_DUPLICATED, LtoLeadsFieldApiConstants.IS_COLLECTED,
            LtoLeadsFieldApiConstants.COLLECTED_TO, LtoLeadsFieldApiConstants.REFRESH_DUPLICATED_VERSION
    );

    @Autowired
    private SpecialTableMapper specialTableMapper;

    @Override
    public String getObjectApiName() {
        return Utils.LEADS_API_NAME;
    }

    @Override
    public String getObjectPoolKeyName() {
        return LtoLeadsFieldApiConstants.LEADS_POOL_ID;
    }

    @Override
    public ObjectPoolActionModels.Result choose(User user, String objectPoolId, List<String> objectIds, String eventId, String partnerId) {
        ObjectPoolActionModels.Result result = ObjectPoolActionModels.Result.builder().build();
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIds(user.getTenantId(), objectIds, getObjectApiName());
        if(CollectionUtils.empty(objectDataList)) {
            return result;
        }
        IObjectData objectPoolData = getObjectPoolById(user.getTenantId(), objectPoolId);
        boolean cleanTeamMember = ObjectDataUtil.getBooleanValue(objectPoolData, "is_recycling_team_member", false);
        String ownerDeptId = ltoOrgCommonService.getMainDepartment(user.getTenantId(), user.getUpstreamOwnerIdOrUserId());
        List<IObjectData> oldDataList = ObjectDataExt.copyList(objectDataList);
        for (IObjectData objectData : objectDataList) {
            objectData.set(LtoLeadsFieldApiConstants.LAST_FOLLOWED_TIME, System.currentTimeMillis());
            objectData.set(LtoLeadsFieldApiConstants.OWNER_CHANGE_TIME, System.currentTimeMillis());
            objectData.set(LtoFieldApiConstants.LAST_MODIFIED_TIME, System.currentTimeMillis());
            objectData.set(LtoLeadsFieldApiConstants.LEADS_STATUS,  LtoLeadsFieldApiConstants.Status.UN_DEAL.getCode());
            objectData.set(LtoLeadsFieldApiConstants.ASSIGNER_ID, Lists.newArrayList());
            objectData.set(LtoLeadsFieldApiConstants.ASSIGNED_TIME, System.currentTimeMillis());
            objectData.set(LtoFieldApiConstants.OWNER, Lists.newArrayList(user.getUpstreamOwnerIdOrUserId()));
            objectData.set(LtoFieldApiConstants.DATA_OWN_DEPARTMENT, StringUtils.isBlank(ownerDeptId) ? Lists.newArrayList() : Lists.newArrayList(ownerDeptId));
            objectData.set(LtoFieldApiConstants.BIZ_STATUS, LtoLeadsFieldApiConstants.BizStatus.UN_PROCESSED.getCode());
            objectData.set(LtoLeadsFieldApiConstants.IS_OVERTIME, false);
            if (StringUtils.isNotBlank(partnerId)) {
                objectData.set(LtoFieldApiConstants.PARTNER_ID, partnerId);
            }
            if (StringUtils.isNotBlank(user.getOutUserId())) {
                objectData.setOutOwner(Lists.newArrayList(user.getOutUserId()));
                objectData.setOutTenantId(user.getOutTenantId());
            }
        }
        List<String> updateFieldList = Lists.newArrayList(LtoLeadsFieldApiConstants.LAST_FOLLOWED_TIME, LtoLeadsFieldApiConstants.OWNER_CHANGE_TIME, LtoFieldApiConstants.LAST_MODIFIED_TIME, LtoLeadsFieldApiConstants.LEADS_STATUS,LtoLeadsFieldApiConstants.ASSIGNER_ID, LtoLeadsFieldApiConstants.ASSIGNED_TIME, LtoFieldApiConstants.OWNER, LtoFieldApiConstants.DATA_OWN_DEPARTMENT, LtoFieldApiConstants.BIZ_STATUS, LtoLeadsFieldApiConstants.IS_OVERTIME);
        if (StringUtils.isNotBlank(partnerId)) {
            updateFieldList.add(LtoFieldApiConstants.PARTNER_ID);
        }
        String outTenant = null;
        String outOwner = null;
        if (StringUtils.isNotBlank(user.getOutUserId())) {
            updateFieldList.add(LtoFieldApiConstants.OUT_OWNER);
            updateFieldList.add(LtoFieldApiConstants.OUT_TENANT_ID);
            outTenant = user.getOutTenantId();
            outOwner = user.getOutUserId();
        }
        saveLeadsData(user, objectDataList, updateFieldList, eventId);
        if (cleanTeamMember) {
            teamMemberService.removeObjectAllInnerTeamMember(user, objectDataList, oldDataList, false);
        }
        if (StringUtils.isNotBlank(user.getOutUserId())) {
            teamMemberService.changeOwner(user, user.getUpstreamOwnerIdOrUserId(), objectDataList, outTenant, outOwner);
        } else {
            teamMemberService.changeInnerOwner(user, user.getUpstreamOwnerIdOrUserId(), objectDataList);
        }
        result.setSuccessList(objectIds);
        return result;
    }

    @Override
    public ObjectPoolActionModels.Result move(User user, String objectPoolId, List<String> objectIds, String eventId) {
        ObjectPoolActionModels.Result result = ObjectPoolActionModels.Result.builder().build();
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIds(user.getTenantId(), objectIds, getObjectApiName());
        if(CollectionUtils.empty(objectDataList)) {
            return result;
        }
        List<IObjectData> oldDataList = ObjectDataExt.copyList(objectDataList);
        IObjectData leadsPool = getObjectPoolById(user.getTenantId(), objectPoolId);
        boolean cleanOwner = ObjectDataUtil.getBooleanValue(leadsPool, "is_clean_owner", false);
        boolean cleanTeamMember = ObjectDataUtil.getBooleanValue(leadsPool, "is_recycling_team_member", false);
        PoolEmptyRule.EmptyRule emptyRule = new PoolEmptyRule.Builder().poolData(Lists.newArrayList(leadsPool)).build().getEmptyRuleById(leadsPool.getId());

        // 被移除外部负责人的数据
        List<IObjectData> removedOutOwnerDataList = getRemoveOutOwnerDataWhenCarry2Pool(emptyRule, objectDataList, ObjectAction.MOVE);

        for (IObjectData objectData : objectDataList) {
            objectData.set(LtoLeadsFieldApiConstants.LEADS_POOL_ID, objectPoolId);
            objectData.setLastModifiedBy(user.getUpstreamOwnerIdOrUserId());
            objectData.setLastModifiedTime(System.currentTimeMillis());
            String bizStatus = ObjectDataUtil.getStringValue(objectData, LtoFieldApiConstants.BIZ_STATUS, "");
            if (cleanOwner && !LtoLeadsFieldApiConstants.BizStatus.TRANSFORMED.getCode().equals(bizStatus)) {
                objectData.set(LtoLeadsFieldApiConstants.OWNER_CHANGE_TIME, System.currentTimeMillis());
                objectData.set(LtoLeadsFieldApiConstants.LEADS_STATUS, LtoLeadsFieldApiConstants.Status.UN_ALLOCATE.getCode());
                objectData.set(LtoFieldApiConstants.OWNER, Lists.newArrayList());
                objectData.set(LtoFieldApiConstants.DATA_OWN_DEPARTMENT, Lists.newArrayList());
                objectData.set(LtoFieldApiConstants.BIZ_STATUS, LtoLeadsFieldApiConstants.BizStatus.UN_ASSIGNED.getCode());
                objectData.set(LtoLeadsFieldApiConstants.IS_OVERTIME, false);
            }
        }

        List<String> updateFieldList = Lists.newArrayList(LtoLeadsFieldApiConstants.LEADS_POOL_ID, LtoFieldApiConstants.LAST_MODIFIED_BY, LtoFieldApiConstants.LAST_MODIFIED_TIME);
        if (cleanOwner) {
            updateFieldList.add(LtoFieldApiConstants.OWNER);
            updateFieldList.add(LtoLeadsFieldApiConstants.LEADS_STATUS);
            updateFieldList.add(LtoFieldApiConstants.DATA_OWN_DEPARTMENT);
            updateFieldList.add(LtoLeadsFieldApiConstants.OWNER_CHANGE_TIME);
            updateFieldList.add(LtoFieldApiConstants.BIZ_STATUS);
            updateFieldList.add(LtoLeadsFieldApiConstants.IS_OVERTIME);
        }
        boolean update = teamMemberService.removeObjectPartnerByOutOwnerNotUpdate(user, removedOutOwnerDataList);
        if (update){
            updateFieldList.add(LtoFieldApiConstants.PARTNER_ID);
        }
        saveLeadsData(user, objectDataList, updateFieldList, eventId);
        objectDataList.removeIf(x -> LtoLeadsFieldApiConstants.BizStatus.TRANSFORMED.getCode().equals(ObjectDataUtil.getStringValue(x, LtoFieldApiConstants.BIZ_STATUS, "")));
        if (cleanTeamMember) {
            teamMemberService.removeObjectAllInnerTeamMember(user, objectDataList, oldDataList, true);
        }
        if (cleanOwner) {
            teamMemberService.removeObjectInnerOwner(user, objectDataList);
        }
        if (CollectionUtils.notEmpty(removedOutOwnerDataList)) {
            teamMemberService.moveActionOutTeamHandle(user, emptyRule, removedOutOwnerDataList);
        }
        result.setSuccessList(objectIds);
        return result;
    }

    @Override
    public ObjectPoolActionModels.Result back(User user, String objectPoolId, List<String> objectIds, Integer operationType,
                                           String backReason, String backReasonOther, String eventId, boolean isPrmOpen) {
        ObjectPoolActionModels.Result result = ObjectPoolActionModels.Result.builder().build();
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIds(user.getTenantId(), objectIds, getObjectApiName());
        if (CollectionUtils.empty(objectDataList)) {
            return result;
        }
        IObjectData objectPoolData = getObjectPoolById(user.getTenantId(), objectPoolId);
        boolean cleaTeamMember = ObjectDataUtil.getBooleanValue(objectPoolData, "is_recycling_team_member", false);
        PoolEmptyRule.EmptyRule emptyRule = new PoolEmptyRule.Builder().poolData(Lists.newArrayList(objectPoolData)).build().getEmptyRuleById(objectPoolId);

        List<IObjectData> oldDataList = ObjectDataExt.copyList(objectDataList);
        List<IObjectData> removedOutOwnerDataList = getRemoveOutOwnerDataWhenCarry2Pool(emptyRule, objectDataList, ObjectAction.RETURN);

        for (IObjectData objectData : objectDataList) {
            setBackObjectData(user, objectData, objectPoolId);
            String backReasonAll = ("other".equals(backReason) && StringUtils.isNotBlank(backReasonOther)) ?
                    backReason + backReasonOther : backReason;
            objectData.set(LtoLeadsFieldApiConstants.BACK_REASON, backReasonAll);
        }

        List<String> updateFieldList = Lists.newArrayList(LtoFieldApiConstants.OWNER, LtoFieldApiConstants.DATA_OWN_DEPARTMENT, LtoLeadsFieldApiConstants.LEADS_STATUS,
                LtoLeadsFieldApiConstants.ASSIGNER_ID, LtoLeadsFieldApiConstants.ASSIGNED_TIME, LtoFieldApiConstants.LAST_MODIFIED_BY, LtoFieldApiConstants.LAST_MODIFIED_TIME
                , LtoLeadsFieldApiConstants.OWNER_CHANGE_TIME, LtoLeadsFieldApiConstants.IS_OVERTIME, LtoLeadsFieldApiConstants.LEADS_POOL_ID, LtoLeadsFieldApiConstants.BACK_REASON, LtoFieldApiConstants.BIZ_STATUS,LtoLeadsFieldApiConstants.RETURNED_TIME);
        if (CollectionUtils.notEmpty(removedOutOwnerDataList)) {
            boolean update = teamMemberService.removeObjectPartnerByOutOwnerNotUpdate(user, removedOutOwnerDataList);
            if (update){
                updateFieldList.add(LtoFieldApiConstants.PARTNER_ID);
            }
        }
        try {
            serviceFacade.batchUpdateByFields(user, objectDataList, updateFieldList);
            objectDataList.removeIf(x -> LtoLeadsFieldApiConstants.BizStatus.TRANSFORMED.getCode().equals(ObjectDataUtil.getStringValue(x, LtoFieldApiConstants.BIZ_STATUS, "")));
            if (cleaTeamMember) {
                teamMemberService.removeObjectAllInnerTeamMember(user, objectDataList, oldDataList, false);
            }
            teamMemberService.removeObjectInnerOwner(user, objectDataList);
            if (CollectionUtils.notEmpty(removedOutOwnerDataList)) {
                teamMemberService.backActionOutTeamHandle(user, emptyRule, removedOutOwnerDataList);
            }
            savePoolClaimLog(user, objectPoolId, oldDataList, user.isOutUser(), false, ObjectAction.RETURN.getActionCode());
            result.setSuccessList(objectIds);
        } catch (Exception e) {
            log.error("back Leads error {}", user.getTenantId(), e);
            ExceptionUtil.throwCommonBusinessException();
        }
        return result;
    }

    @Override
    public ObjectPoolActionModels.Result takeBack(User user, String objectPoolId, List<String> objectIds, String eventId, boolean isPrmOpen) {
        ObjectPoolActionModels.Result result = ObjectPoolActionModels.Result.builder().build();
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIds(user.getTenantId(), objectIds, getObjectApiName());
        if (CollectionUtils.empty(objectDataList)) {
            return result;
        }
        IObjectData objectPoolData = getObjectPoolById(user.getTenantId(), objectPoolId);
        boolean cleaTeamMember = ObjectDataUtil.getBooleanValue(objectPoolData, "is_recycling_team_member", false);
        PoolEmptyRule.EmptyRule emptyRule = new PoolEmptyRule.Builder().poolData(Lists.newArrayList(objectPoolData)).build().getEmptyRuleById(objectPoolId);
        List<IObjectData> oldDataList = ObjectDataExt.copyList(objectDataList);
        List<IObjectData> removedOutOwnerDataList = getRemoveOutOwnerDataWhenCarry2Pool(emptyRule, objectDataList, ObjectAction.TAKE_BACK);
        for (IObjectData objectData : objectDataList) {
            setBackObjectData(user, objectData, objectPoolId);
        }
        List<String> updateFieldList = Lists.newArrayList(LtoFieldApiConstants.OWNER, LtoFieldApiConstants.DATA_OWN_DEPARTMENT, LtoLeadsFieldApiConstants.LEADS_STATUS,
                LtoLeadsFieldApiConstants.ASSIGNER_ID, LtoLeadsFieldApiConstants.ASSIGNED_TIME, LtoFieldApiConstants.LAST_MODIFIED_BY, LtoFieldApiConstants.LAST_MODIFIED_TIME
                , LtoLeadsFieldApiConstants.OWNER_CHANGE_TIME, LtoLeadsFieldApiConstants.IS_OVERTIME, LtoLeadsFieldApiConstants.LEADS_POOL_ID, LtoFieldApiConstants.BIZ_STATUS, LtoLeadsFieldApiConstants.RETURNED_TIME);
        if (CollectionUtils.notEmpty(removedOutOwnerDataList)) {
            boolean update = teamMemberService.removeObjectPartnerByOutOwnerNotUpdate(user, removedOutOwnerDataList);
            if (update){
                updateFieldList.add(LtoFieldApiConstants.PARTNER_ID);
            }
        }
        try {
            serviceFacade.batchUpdateByFields(user, objectDataList, updateFieldList);
            if (cleaTeamMember) {
                teamMemberService.removeObjectAllInnerTeamMember(user, objectDataList, oldDataList, false);
            }
            teamMemberService.removeObjectInnerOwner(user, objectDataList);
            if (CollectionUtils.notEmpty(removedOutOwnerDataList)) {
                teamMemberService.backActionOutTeamHandle(user, emptyRule, removedOutOwnerDataList);
            }
            savePoolClaimLog(user, objectPoolId, oldDataList, user.isOutUser(), false, ObjectAction.TAKE_BACK.getActionCode());
            result.setSuccessList(objectIds);
        } catch (Exception e) {
            log.error("takeBack Leads error {}", user.getTenantId(), e);
            ExceptionUtil.throwCommonBusinessException();
        }
        return result;
    }

    @Override
    public ObjectPoolActionModels.Result allocate(User user, String objectPoolId, List<String> objectIds, String ownerId, String eventId,
                                               Long outTenantId, Long outOwnerId, String partnerId) {
        ObjectPoolActionModels.Result result = ObjectPoolActionModels.Result.builder().build();
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIds(user.getTenantId(), objectIds, getObjectApiName());
        if(CollectionUtils.empty(objectDataList)) {
            return result;
        }
        String ownerDeptId = ltoOrgCommonService.getMainDepartment(user.getTenantId(), ownerId);
        boolean hasOutTenantId = false;
        boolean hasOutOwnerId = false;
        boolean hasPartnerId = false;
        String outTenant = null;
        String outOwner = null;
        IObjectData objectPoolData = getObjectPoolById(user.getTenantId(), objectPoolId);
        boolean cleaTeamMember = ObjectDataUtil.getBooleanValue(objectPoolData, "is_recycling_team_member", false);
        if (outTenantId != null && outTenantId > 0) {
            hasOutTenantId = true;
            outTenant = String.valueOf(outTenantId);
        }
        if (outOwnerId != null && outOwnerId > 0) {
            hasOutOwnerId = true;
            outOwner = String.valueOf(outOwnerId);
        }
        if (StringUtils.isNotBlank(partnerId)) {
            hasPartnerId = true;
        }
        for (IObjectData objectData : objectDataList) {
            objectData.set(LtoFieldApiConstants.OWNER, Lists.newArrayList(ownerId));
            objectData.set(LtoFieldApiConstants.DATA_OWN_DEPARTMENT, StringUtils.isBlank(ownerDeptId) ? Lists.newArrayList() : Lists.newArrayList(ownerDeptId));
            objectData.set(LtoLeadsFieldApiConstants.LEADS_STATUS, LtoLeadsFieldApiConstants.Status.UN_DEAL.getCode());
            objectData.set(LtoLeadsFieldApiConstants.ASSIGNER_ID, Lists.newArrayList(user.getUpstreamOwnerIdOrUserId()));
            objectData.set(LtoLeadsFieldApiConstants.ASSIGNED_TIME, System.currentTimeMillis());
            int resaleCount = ObjectDataUtil.getIntegerValue(objectData, LtoLeadsFieldApiConstants.RESALE_COUNT, 0);
            objectData.set(LtoLeadsFieldApiConstants.RESALE_COUNT, resaleCount + 1);
            objectData.setLastModifiedBy(user.getUpstreamOwnerIdOrUserId());
            objectData.setLastModifiedTime(System.currentTimeMillis());
            objectData.set(LtoLeadsFieldApiConstants.OWNER_CHANGE_TIME, System.currentTimeMillis());
            objectData.set(LtoLeadsFieldApiConstants.IS_OVERTIME, false);
            objectData.set(LtoFieldApiConstants.BIZ_STATUS, LtoLeadsFieldApiConstants.BizStatus.UN_PROCESSED.getCode());
            if (hasOutTenantId) {
                objectData.setOutTenantId(outTenant);
            }
            if (hasOutOwnerId) {
                objectData.setOutOwner(Lists.newArrayList(outOwner));
            }
            if (hasPartnerId) {
                objectData.set(LtoFieldApiConstants.PARTNER_ID, partnerId);
            }
        }
        List<String> updateFieldList = Lists.newArrayList(LtoFieldApiConstants.OWNER, LtoFieldApiConstants.DATA_OWN_DEPARTMENT, LtoLeadsFieldApiConstants.LEADS_STATUS,
                LtoLeadsFieldApiConstants.ASSIGNED_TIME, LtoLeadsFieldApiConstants.ASSIGNER_ID, LtoLeadsFieldApiConstants.RESALE_COUNT, LtoFieldApiConstants.LAST_MODIFIED_BY,
                LtoFieldApiConstants.LAST_MODIFIED_TIME, LtoLeadsFieldApiConstants.OWNER_CHANGE_TIME, LtoLeadsFieldApiConstants.IS_OVERTIME, LtoFieldApiConstants.BIZ_STATUS);
        if (hasOutTenantId) {
            updateFieldList.add(LtoFieldApiConstants.OUT_TENANT_ID);
        }
        if (hasOutOwnerId) {
            updateFieldList.add(LtoFieldApiConstants.OUT_OWNER);
        }
        if (hasPartnerId) {
            updateFieldList.add(LtoFieldApiConstants.PARTNER_ID);
        }
        saveLeadsData(user, objectDataList, updateFieldList, eventId);
        if (cleaTeamMember) {
            teamMemberService.removeObjectAllInnerTeamMember(user, objectDataList);
        }
        if (hasOutOwnerId) {
            teamMemberService.changeOwner(user, ownerId, objectDataList, outTenant, outOwner);
        } else {
            teamMemberService.changeInnerOwner(user, ownerId, objectDataList);
        }
        return result;
    }

    @Override
    public ObjectPoolActionModels.Result remove(User user, List<IObjectData> objectDataList, String owner, boolean isKeepOwner, String eventId) {
        ExceptionUtil.throwUnSupportedOperationException();
        return null;
    }

    private void saveLeadsData(User user, List<IObjectData> objectDataList, List<String> updateFieldList, String eventId) {
        try {
            List<IObjectData> dataList = Lists.newArrayList();
            for (IObjectData objectData : objectDataList) {
                ObjectDataExt data = ObjectDataExt.of(objectData);
                for (String field : editExceptFields) {
                    data.remove(field);
                }
                dataList.add(data.getObjectData());
            }
            ActionContextUtil.ActionContextOp actionContextOp = ActionContextUtil.ActionContextOp.builder()
                    .updateLastModifyTime(true).skipVersionChange(false).eventId(eventId)
                    .allowUpdateInvalid(true).notValidate(false).batch(true).privilegeCheck(false)
                    .build();
            ObjectDataUtil.updateFields(user, objectDataList, updateFieldList, actionContextOp);
        } catch (Exception e) {
            log.error("saveLeadsData error {}", user.getTenantId(), e);
            ExceptionUtil.throwCommonBusinessException();
        }
    }

    private void setBackObjectData(User user, IObjectData objectData, String objectPoolId) {
        String bizStatus = ObjectDataUtil.getStringValue(objectData, LtoFieldApiConstants.BIZ_STATUS, "");
        if (!LtoLeadsFieldApiConstants.BizStatus.TRANSFORMED.getCode().equals(bizStatus)) {
            objectData.set(LtoFieldApiConstants.OWNER, Lists.newArrayList());
            objectData.set(LtoFieldApiConstants.DATA_OWN_DEPARTMENT, Lists.newArrayList());
            objectData.set(LtoLeadsFieldApiConstants.LEADS_STATUS, LtoLeadsFieldApiConstants.Status.UN_ALLOCATE.getCode());
            objectData.set(LtoLeadsFieldApiConstants.ASSIGNER_ID, Lists.newArrayList());
            objectData.set(LtoLeadsFieldApiConstants.ASSIGNED_TIME, null);
            objectData.set(LtoLeadsFieldApiConstants.OWNER_CHANGE_TIME, System.currentTimeMillis());
            objectData.set(LtoLeadsFieldApiConstants.IS_OVERTIME, false);
            objectData.set(LtoFieldApiConstants.BIZ_STATUS, LtoLeadsFieldApiConstants.BizStatus.UN_ASSIGNED.getCode());
        }
        objectData.set(LtoLeadsFieldApiConstants.LEADS_POOL_ID, objectPoolId);
        objectData.setLastModifiedBy(user.getUpstreamOwnerIdOrUserId());
        objectData.setLastModifiedTime(System.currentTimeMillis());
        objectData.set(LtoLeadsFieldApiConstants.RETURNED_TIME, System.currentTimeMillis());
    }
}
