package com.facishare.crm.sfa.lto.relationship.models;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> lik
 * @date : 2022/7/11 16:57
 */

public interface RelationshipModels {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class TaskArg {
        private String tenantId;
        private String objectApiName;
        private List<String> dataIds;
        private String operationType;
        private List<String> transferObjName;
        private List<String> phoneList;
        private String userId;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class ContactMemberRelationshipArg {
        private List<String> telList;
        private String accountId;
    }


    @Data
    @Builder
    class Result{
        private String accountId;
        private String objectApiName;
        private String dataId;
        private String member;
        private String relationshipType;
        private String memberType;
    }
}
