package com.facishare.crm.sfa.lto.integral.core.rest.dto;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

public interface UpdateStatus {

    @EqualsAndHashCode(callSuper = true)
	@Data
    class Arg extends BaseEngine.Arg {
        List<String> apiNames;
        /** 规则状态: 1启用，0停用*/
        int status;

        public static Arg build(int status, RequestContext requestContext, String... apiNames) {
            Arg out = new Arg();
            BaseEngine.Context context = new BaseEngine.Context();
            context.setUserId(requestContext.getUser().getUserId());
            context.setTenantId(requestContext.getTenantId());

            List<String> apiNameList = Lists.newArrayList();
            for (String name : apiNames) {
                apiNameList.add(name);
            }

            out.setContext(context);
            out.setApiNames(apiNameList);
            out.setStatus(status);

            return out;
        }
    }

    @EqualsAndHashCode(callSuper = true)
	@Data
    class Result extends BaseEngine.Result<Object> {

    }

}
