package com.facishare.crm.sfa.lto.equityrelationship.service;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.lto.common.LtoRateLimiterService;
import com.facishare.crm.sfa.lto.equityrelationship.dao.EquityRelationshipDataMongoDbDao;
import com.facishare.crm.sfa.lto.equityrelationship.model.EquityRelationshipData;
import com.facishare.crm.sfa.lto.equityrelationship.model.EquityRelationshipDataModel;
import com.facishare.crm.sfa.lto.utils.CommonSqlUtil;
import com.facishare.crm.sfa.lto.utils.EquityRelationshipUtils;
import com.facishare.crm.sfa.lto.utils.ObjectDataUtil;
import com.facishare.crm.sfa.lto.utils.constants.EquityRelationshipDataConstants;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.support.CountryAreaService;
import com.fxiaoke.common.SqlEscaper;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> lik
 * @date : 2023/7/8 15:53
 */

@Component
@Slf4j
public class EquityRelationshipDataSyncService {
    private static  String tenantIdsStr ="";
    static {
        ConfigFactory.getConfig("crm-sfa-equity-relationship-config", config -> {
            tenantIdsStr = config.get("tenantIdsStr", "");
        });
    }
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private EquityRelationshipDataMongoDbDao equityRelationshipDataMongoDbDao;
    @Autowired
    private LtoRateLimiterService rateLimiterService;
    @Autowired
    private EquityRelationshipService equityRelationshipService;

    public void execute(EquityRelationshipDataModel.DataSyncArg arg){
//        if(ObjectUtils.isEmpty(arg.getKeys()) || ObjectUtils.isEmpty(arg.getKeys().getId()) || ObjectUtils.isEmpty(arg.getKeys().getTenant_id()) ){
//            return;
//        }
//        EquityRelationshipDataModel.Keys keys = arg.getKeys();
//        String findSql = String.format("SELECT enterprise_id,enterprise_name from biz_equity_relationship where tenant_id = %s and id = %s and is_deleted = 0 limit 1;",
//                SqlEscaper.pg_quote(keys.getTenant_id()),SqlEscaper.pg_quote(keys.getId()));
//
//        List<Map> list = CommonSqlUtil.findBySql(keys.getTenant_id(),findSql);
//        if(CollectionUtils.empty(list)){
//            log.warn("EquityRelationshipDataSyncService list is null  biz_equity_relationship findSql:{}",findSql);
//            return;
//        }
//        Map map = list.get(0);
//        List<IObjectData> dataList = serviceFacade.findObjectDataByIds(keys.getTenant_id(), Lists.newArrayList(map.get(EquityRelationshipDataConstants.Param.ENTERPRISE_ID).toString()), Utils.ENTERPRISE_INFO_API_NAME);
//        if(CollectionUtils.notEmpty(dataList)){
//            return;
//        }
//        User user = new User(keys.getTenant_id(), User.SUPPER_ADMIN_USER_ID);
//        IObjectData objectData = getObjectDataOfEnterpriseInfoObj(user,
//                map.get(EquityRelationshipDataConstants.Param.ENTERPRISE_ID).toString(),
//                map.get(EquityRelationshipDataConstants.Param.ENTERPRISE_NAME).toString());
//
//        serviceFacade.bulkSaveObjectData(Lists.newArrayList(objectData), user, true, true, p -> {
//            return ActionContextExt.of(user)
//                    .setNotValidate(true)
//                    .getContext();
//        });
    }

    /**
     * 一次性的将mongodb的数据同步到pg
     * @param tenantId
     */
    @Async
    public void dataSyncPgExecute(String tenantId){
        log.warn("EquityRelationshipDataSyncService dataSyncPgExecute start time:{}",System.currentTimeMillis());
        int index = 1;
        User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
        while (true){
            rateLimiterService.getEquityRelationshipDataSyncLimiter().acquire();
            //查询mongoDB
            List<EquityRelationshipData.EquityRelationship> list = equityRelationshipDataMongoDbDao.queryListByParamPaging(EquityRelationshipDataMongoDbDao.MOULD_TENANT_ID,null,index,200);
            if(CollectionUtils.empty(list)){
                break;
            }
            //保存企业库
            saveEnterpriseObj(user,list);

            //过滤出已经在pg中存在的数据
            List<String> ids = list.stream().map(EquityRelationshipData.EquityRelationship::getId).collect(Collectors.toList());
            List<Map> pgList = getListByIds(user,ids);
            if (CollectionUtils.notEmpty(pgList)){
                List<String> existIds =  pgList.stream().map(x->x.get("id").toString()).collect(Collectors.toList());
                list = list.stream().filter(x->!existIds.contains(x.getId())).collect(Collectors.toList());
            }

            if (CollectionUtils.empty(list)){
                index++;
                continue;
            }
            //绑定客户主数据
            EquityRelationshipUtils.bindingAccountMainData(user,list,null,null);
            //保存 工商关系对象
            saveEquityRelationshipObj(user,list);

            if(index>*********){
                break;
            }
            index++;

            if(index%100==0){
                log.warn("EquityRelationshipDataSyncService dataSyncPgExecute in progress time:{},index:{}",System.currentTimeMillis(),index);
            }
        }
        log.warn("EquityRelationshipDataSyncService dataSyncPgExecute end time:{},index:{}",System.currentTimeMillis(),index);
    }

    /**
     * 实时更新Pg数据库
     * @param updateIds
     */
    @Async
    public void realTimeUpdatePG(List<ObjectId> updateIds){
        log.warn("EquityRelationshipDataSyncService RealTimeUpdatePG start time:{}",System.currentTimeMillis());
        try {
            if (ObjectUtils.isEmpty(tenantIdsStr)) {
                log.warn("EquityRelationshipDataSyncService RealTimeUpdatePG tenantIds is null  ");
                return;
            }
            List<EquityRelationshipData.EquityRelationship> list = equityRelationshipDataMongoDbDao.queryListByIds(equityRelationshipDataMongoDbDao.MOULD_TENANT_ID,updateIds);
            if (CollectionUtils.empty(list)) {
                log.warn("EquityRelationshipDataSyncService RealTimeUpdatePG list is null  ids:{}", JSONObject.toJSONString(updateIds));
                return;
            }
            List<String> tenantIdList = Lists.newArrayList(tenantIdsStr.split(";"));

            List<String> uniquenessList = list.stream().map(EquityRelationshipData.EquityRelationship::getUniqueness_data_relationship).collect(Collectors.toList());
            Map<String,EquityRelationshipData.EquityRelationship> map = list.stream().collect(Collectors.toMap(EquityRelationshipData.EquityRelationship::getUniqueness_data_relationship, Function.identity(),(v1,v2)->v1));
            for(String tenantId : tenantIdList){
                rateLimiterService.getEquityRelationshipDataSyncLimiter().acquire();
                try {
                    User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
                    //保存企业库
                    saveEnterpriseObj(user,list);

                    List<IObjectData> existDataList = EquityRelationshipUtils.getEquityRelationshipDataByUniqueness(user,uniquenessList);
                    List<EquityRelationshipData.EquityRelationship> insertData = new ArrayList<>();
                    insertData.addAll(list);
                    if(CollectionUtils.notEmpty(existDataList)){
                        //更新
                        existDataList.forEach(x->{
                            EquityRelationshipData.EquityRelationship data = map.get(x.get(EquityRelationshipDataConstants.Param.UNIQUENESS_DATA_RELATIONSHIP).toString());
                            x.set(EquityRelationshipDataConstants.Param.ROOT_NODE_PATH, data.getRoot_node_path());
                            x.set(EquityRelationshipDataConstants.Param.PARENT_STOCK_PROPORTION,data.getParent_stock_proportion());
                            x.set(EquityRelationshipDataConstants.Param.PARENT_INVESTMENT, data.getParent_investment());
                            x.set(EquityRelationshipDataConstants.Param.RELATIONSHIP_LEVEL, data.getRelationship_level());
                        });
                        EquityRelationshipUtils.bindingAccountMainData(user,null,existDataList,null);
                        serviceFacade.batchUpdate(ActionContextExt.of(user)
                                .setNotValidate(true)
                                .getContext(),existDataList, user);
                        List<String> existIds = existDataList.stream().map(x->x.get(EquityRelationshipDataConstants.Param.UNIQUENESS_DATA_RELATIONSHIP).toString()).collect(Collectors.toList());
                        insertData.removeIf(x->existIds.contains(x.getUniqueness_data_relationship()));
                    }
                    //绑定客户主数据
                    EquityRelationshipUtils.bindingAccountMainData(user,list,null,null);
                    //保存
                    saveEquityRelationshipObj(user,insertData);
                }catch (Exception e){
                    log.error("EquityRelationshipDataSyncService RealTimeUpdatePG error tenantId:{},e:",tenantId,e);
                }

            }
        }catch (Exception e){
            log.error("EquityRelationshipDataSyncService RealTimeUpdatePG error ,e:",e);
        }

    }
    public void realTimeUpdatePGByTime(String tenantId,long time,Map<String,Object> tempParamMap){
        log.warn("EquityRelationshipDataSyncService realTimeUpdatePGByTime start time:{}",time);
        try {
            int index = 1;
            int pageSize = 200;
            while(true){
                List<EquityRelationshipData.EquityRelationship> list = equityRelationshipDataMongoDbDao.queryListByGteTimeStamp(equityRelationshipDataMongoDbDao.MOULD_TENANT_ID,time,index,pageSize);
                if (CollectionUtils.empty(list)) {
                    log.warn("EquityRelationshipDataSyncService realTimeUpdatePGByTime list is null  tenantId:{},time:{}", tenantId,time);
                    break;
                }

                Map<String,EquityRelationshipData.EquityRelationship> map = list.stream().collect(Collectors.toMap(EquityRelationshipData.EquityRelationship::getUniqueness_data_relationship, Function.identity(),(v1,v2)->v1));

                rateLimiterService.getEquityRelationshipDataSyncLimiter().acquire();
                try {
                    User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
                    //保存企业库
                    saveEnterpriseObj(user,list);

                    List<String> uniquenessList = list.stream().map(EquityRelationshipData.EquityRelationship::getUniqueness_data_relationship).collect(Collectors.toList());
                    List<IObjectData> existDataList = EquityRelationshipUtils.getEquityRelationshipDataByUniqueness(user,uniquenessList);

                    List<EquityRelationshipData.EquityRelationship> insertData = new ArrayList<>();
                    insertData.addAll(list);
                    if(CollectionUtils.notEmpty(existDataList)){
                        //更新
                        existDataList.forEach(x->{
                            EquityRelationshipData.EquityRelationship data = map.get(x.get(EquityRelationshipDataConstants.Param.UNIQUENESS_DATA_RELATIONSHIP).toString());
                            if(ObjectUtils.isNotEmpty(data.getRoot_node_path())){
                                x.set(EquityRelationshipDataConstants.Param.ROOT_NODE_PATH, data.getRoot_node_path());
                            }
                            if(ObjectUtils.isNotEmpty(data.getParent_stock_proportion())){
                                x.set(EquityRelationshipDataConstants.Param.PARENT_STOCK_PROPORTION,data.getParent_stock_proportion());
                            }
                            if(ObjectUtils.isNotEmpty(data.getParent_investment())){
                                x.set(EquityRelationshipDataConstants.Param.PARENT_INVESTMENT, data.getParent_investment());
                            }
                            x.set(EquityRelationshipDataConstants.Param.RELATIONSHIP_LEVEL, data.getRelationship_level());
                            x.set(EquityRelationshipDataConstants.Param.FIND_BY_DATA_ID,data.getFind_by_data_id());
                            x.set(EquityRelationshipDataConstants.Param.UNIQUENESS_DATA_RELATIONSHIP,data.getUniqueness_data_relationship());
                        });
                        //绑定客户主数据
                        EquityRelationshipUtils.bindingAccountMainData(user,null,existDataList,tempParamMap);
                        serviceFacade.batchUpdate(ActionContextExt.of(user)
                                    .setNotValidate(true)
                                    .getContext(),existDataList, user);
                        List<String> existIds = existDataList.stream().map(x->x.get(EquityRelationshipDataConstants.Param.UNIQUENESS_DATA_RELATIONSHIP).toString()).collect(Collectors.toList());
                        insertData.removeIf(x->existIds.contains(x.getUniqueness_data_relationship()));
                    }
                    //绑定客户主数据
                    EquityRelationshipUtils.bindingAccountMainData(user,insertData,null,tempParamMap);
                    //保存
                    saveEquityRelationshipObj(user,insertData);
                }catch (Exception e){
                    log.error("EquityRelationshipDataSyncService realTimeUpdatePGByTime error tenantId:{},e:",tenantId,e);
                }
            index++;
            if(list.size()<pageSize){
                log.warn("EquityRelationshipDataSyncService realTimeUpdatePGByTime finish tenantId:{},list.size()<pageSize,index:{}",tenantId,index);
                break;
            }
            if(index>100000){
                log.warn("EquityRelationshipDataSyncService realTimeUpdatePGByTime finish tenantId:{},index>100000",tenantId);
                break;
            }
            }

        }catch (Exception e){
            log.error("EquityRelationshipDataSyncService realTimeUpdatePGByTime error ,e:",e);
        }

    }

    public void saveEquityRelationshipObj(User user,List<EquityRelationshipData.EquityRelationship> list){
        //将数据保存在pg
        List<IObjectData> dataList = new ArrayList<>();
        list.forEach(x->{
            dataList.add(EquityRelationshipUtils.getObjectDataOfEquityRelationshipObj(user,x));
        });
        if(CollectionUtils.notEmpty(dataList)){
            serviceFacade.bulkSaveObjectData(dataList, user, true, true, p -> {
                return ActionContextExt.of(user)
                        .setNotValidate(true)
                        .getContext();
            });
        }
    }

    /**
     * 将叶子节点保存企业库对象中
     * @param user
     * @param list
     */
    public void saveEnterpriseObj(User user,List<EquityRelationshipData.EquityRelationship> list){
        if(CollectionUtils.empty(list)){
            return;
        }
        //找出所有的企业ids，判断这些企业是否在企业库中存在
        List<String> eIds = list.stream().map(EquityRelationshipData.EquityRelationship::getEnterprise_id).collect(Collectors.toList());
        String findSql = String.format("SELECT id from biz_enterprise where tenant_id = %s and id = %s ;",
                SqlEscaper.pg_quote(user.getTenantId()),SqlEscaper.any_clause(eIds));
        List<Map> enterpriseList = CommonSqlUtil.findBySql(user.getTenantId(),findSql);

        List<EquityRelationshipData.EquityRelationship> insertDataList =  new ArrayList<>();
        insertDataList.addAll(list);

        if(CollectionUtils.notEmpty(enterpriseList)){
            //过滤出不存在的企业id
            List<String> existEIds = enterpriseList.stream().map(x->x.get("id").toString()).collect(Collectors.toList());
            insertDataList = insertDataList.stream().filter(x->!existEIds.contains(x.getEnterprise_id())).collect(Collectors.toList());
        }
       if(CollectionUtils.empty(insertDataList)){
            return;
        }
        //找出所有的企业name，按着名称字段判断这些企业是否在企业库中存在
        List<String> names = list.stream().map(EquityRelationshipData.EquityRelationship::getEnterprise_name).collect(Collectors.toList());
        String findNameSql = String.format("SELECT name from biz_enterprise where tenant_id = %s and name = %s  and is_deleted = 0;",
                SqlEscaper.pg_quote(user.getTenantId()),SqlEscaper.any_clause(names));
        enterpriseList = CommonSqlUtil.findBySql(user.getTenantId(),findNameSql);
        if(CollectionUtils.notEmpty(enterpriseList)){
            //过滤出不存在的企业id
            List<String> existEIds = enterpriseList.stream().map(x->x.get("name").toString()).collect(Collectors.toList());
            insertDataList = insertDataList.stream().filter(x->!existEIds.contains(x.getEnterprise_name())).collect(Collectors.toList());
        }

        equityRelationshipService.saveEnterpriseObjToPG(user,insertDataList,true);
    }



    public IObjectData getObjectDataOfEnterpriseInfoObj(User user,String id,String name){
        IObjectData objectData = ObjectDataUtil.createBaseObjectData(user);
        objectData.setId(id);
        objectData.setName(name);
        objectData.set("object_describe_api_name", Utils.ENTERPRISE_INFO_API_NAME);
        objectData.set("biz_reg_name", true);
        return objectData;
    }


    public List<Map> getListByIds(User user,List<String> ids){
        String findSql = String.format("SELECT id from biz_equity_relationship where tenant_id = %s and id = %s ;",
                SqlEscaper.pg_quote(user.getTenantId()),SqlEscaper.any_clause(ids));
        List<Map> list = CommonSqlUtil.findBySql(user.getTenantId(),findSql);
        return list;
    }
}
