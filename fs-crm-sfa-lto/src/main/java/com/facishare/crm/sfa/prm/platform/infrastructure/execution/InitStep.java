package com.facishare.crm.sfa.prm.platform.infrastructure.execution;

import java.util.Map;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-02-28
 * ============================================================
 */
public interface InitStep<T> {
    /**
     * 执行任务
     *
     * @param param 任务参数
     * @return 执行结果
     */
    boolean execute(T param);

    /**
     * 获取任务名称，同一任务名称的 step 为一组
     */
    String getTaskName();

    /**
     * 获取执行顺序
     */
    int getOrder();

    /**
     * 是否允许失败
     */
    default boolean allowFailure() {
        return false;
    }

    /**
     * 获取最大重试次数
     */
    default int getMaxRetries() {
        return 3;
    }

    /**
     * 获取重试间隔(毫秒)
     */
    default long getRetryInterval() {
        return 1000L;
    }

    /**
     * 任务失败回调
     */
    default void onFailure(T param, Exception e) {
    }

    /**
     * 任务成功回调
     */
    default void onSuccess(T param) {
    }
}