package com.facishare.crm.sfa.lto.accountreerelation.models;

import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface EquityRelationModels {
    @Data
    @Builder
    class ProcessedEquityRelation {
        private String entityId;
        private ProcessedEquityInfo currentNode;
        private List<ProcessedEquityRelation> parentNodes;
    }

    @Data
    @Builder
    class ProcessedEquityInfo {
        private String id ;
        private String eid ;
        private String amount;
        private String name;
        private String type;
        private String has_problem;
        private String percent;
    }

    @Data
    @Builder
    class GenerateTreeRelationRule {
        private List<ConfigEntity> config;
    }

    @Data
    @Builder
    class ConfigEntity {
        private String config_key;
        private String config_value;
        private Boolean is_enable;
    }

    @Data
    @Builder
    class ConflictDataInfo {
        private String conflict_root_id;
        private String root_id;
        private String enterprise_id;
        private String node_type;
    }

    enum LogType {
        EDIT,
        CHANGE_PARENT,
        CHANGE_ROOT,
        GENERATE_TREE,
        REMOVE_CHILDREN
    }
}
