package com.facishare.crm.sfa.lto.operations.task;

import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;
import java.util.function.BiConsumer;

interface TemplateMemberField {

    List<String> getMember(IObjectData bizData);

    BiConsumer<OperationsTask, List<String>> setter();

    default void setValue(OperationsTask task) {
        setter().accept(task, getMember(task.getData()));
    }
}
