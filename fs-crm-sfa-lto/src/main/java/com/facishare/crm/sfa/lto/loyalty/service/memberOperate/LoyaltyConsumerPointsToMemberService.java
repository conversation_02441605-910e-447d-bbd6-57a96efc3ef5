package com.facishare.crm.sfa.lto.loyalty.service.memberOperate;

import com.facishare.crm.sfa.lto.loyalty.constants.LoyaltyConstants;
import com.facishare.crm.sfa.lto.loyalty.i18n.LoyaltyI18nException;
import com.facishare.crm.sfa.lto.loyalty.model.Loyalty;
import com.facishare.crm.sfa.lto.loyalty.service.LoyaltyMemberService;
import com.facishare.crm.sfa.lto.loyalty.service.LoyaltyPointsDetailService;
import com.facishare.crm.sfa.lto.loyalty.service.LoyaltyPointsOperationCacheService;
import com.facishare.crm.sfa.lto.loyalty.task.LoyaltyMqProducer;
import com.facishare.crm.sfa.lto.loyalty.utils.LoyaltyI18nKey;
import com.facishare.crm.sfa.lto.loyalty.utils.NumberUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.common.StopWatch;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

@Service
public class LoyaltyConsumerPointsToMemberService extends AbstractLoyaltyPointsOperateService {

    @Resource
    LoyaltyPointsOperationCacheService loyaltyPointsOperationCacheService;
    @Resource
    LoyaltyMqProducer loyaltyMqProducer;
    @Resource
    LoyaltyMemberService loyaltyMemberService;

    @Override
    public Loyalty.PointsOperationParam.Type type() {
        return Loyalty.PointsOperationParam.Type.CONSUMER_POINTS_TO_MEMBER;
    }

    @Transactional
    @Override
    public void action(Loyalty.PointsOperationParam param) {
        String tenantId = param.getTenantId();
        String memberId = param.getMemberId();
        StopWatch stopWatch = StopWatch.createStarted("增加消费积分");
        if (StringUtils.isEmpty(param.getPointPoolId())) {
            throw LoyaltyI18nException.buildMissingParametersException("pointPoolId");
        }
        IObjectData pointPool = loyaltyPointsOperationCacheService.getPointPool(param);
        if (!"enable".equals(pointPool.get(LoyaltyConstants.LoyaltyPointPool.ENABLE_STATUS, String.class))) {
            throw LoyaltyI18nException.build(LoyaltyI18nKey.NOT_ENABLED, LoyaltyConstants.LoyaltyPointPool.API_NAME);
        }
        stopWatch.lap("验证积分池有效性");
        param.setPointTypeId(pointPool.get(LoyaltyConstants.LoyaltyPointPool.POINT_TYPE_ID, String.class));
        IObjectData org = loyaltyPointsOperationCacheService.getOrg(param);
        if (!"enable".equals(org.get(LoyaltyConstants.LoyaltyPointOrg.ENABLE_STATUS, String.class))) {
            throw LoyaltyI18nException.build(LoyaltyI18nKey.NOT_ENABLED, LoyaltyConstants.LoyaltyPointOrg.API_NAME);
        }
        stopWatch.lap("验证成本组织有效性");
        IObjectData pointsType = loyaltyPointsOperationCacheService.getPointsType(param);
        if (!"enable".equals(pointsType.get("enable_status", String.class))) {
            throw LoyaltyI18nException.build(LoyaltyI18nKey.NOT_ENABLED, LoyaltyConstants.LoyaltyPointType.API_NAME);
        }
        stopWatch.lap("验证积分分类有效性");
        ObjectDataDocument pointsDetail = loyaltyPointsDetailService.saveLoyaltyPointsDetail(param);
        stopWatch.lap("插入积分明细");
        ObjectDataDocument recordDataDocument = new ObjectDataDocument();
        recordDataDocument.put(LoyaltyConstants.LoyaltyMemberChangeRecords.MEMBER_POINTS_DETAIL_ID, pointsDetail.getId());
        recordDataDocument.put(LoyaltyConstants.LoyaltyMemberChangeRecords.POINT_POOL_ID, param.getPointPoolId());
        recordDataDocument.put(LoyaltyConstants.LoyaltyMemberChangeRecords.LOY_CHANGE_VALUE, param.getValue());
        recordDataDocument.put(LoyaltyConstants.LoyaltyMemberChangeRecords.LOY_CHANGE_TYPE, Loyalty.PointsOperationParam.Type.CONSUMER_POINTS_TO_MEMBER.toString());
        saveMemberChangeRecord(param, Lists.newArrayList(recordDataDocument));
        stopWatch.lap("插入操作记录");
        loyaltyMemberService.unLockUpdateMemberPoints(tenantId, memberId);
        stopWatch.lap("会员重算积分");
        loyaltyMqProducer.asyncUpdateOrg(tenantId, param.getPointPoolId());
        stopWatch.lap("触发成本组织计算以及阈值提醒");
        decreasePool(param.getTenantId(), param.getPointPoolId(), param.getValue(), LoyaltyConstants.LoyaltyPointPool.REMAINING_QUANTITY);
        stopWatch.lap("扣减积分池");
        stopWatch.logSlow(STOP_WATCH_TIME);
    }

    @Override
    public void fallback(Loyalty.FallbackUpdateData updateData, Loyalty.FallbackInfo fallbackInfo) {
        IObjectData record = fallbackInfo.getRecord();
        String tenantId = record.get(IObjectData.TENANT_ID, String.class);
        String poolId = record.get(LoyaltyConstants.LoyaltyMemberChangeRecords.POINT_POOL_ID, String.class);
        IObjectData pointsDetail = fallbackInfo.getPointsDetail();
        if (pointsDetail == null) {
            throw LoyaltyI18nException.build(LoyaltyI18nKey.NOT_FOUND, LoyaltyConstants.LoyaltyPointsDetail.API_NAME);
        }
        long changePointsValue = NumberUtils.getLong(record, LoyaltyConstants.LoyaltyMemberChangeRecords.LOY_CHANGE_VALUE);
        //积分明细扣分
        if (NumberUtils.notEnoughToReduce(pointsDetail, LoyaltyConstants.LoyaltyPointsDetail.AVAILABLE_POINTS, changePointsValue)) {
            throw LoyaltyI18nException.build(LoyaltyI18nKey.INSUFFICIENT_POINTS, LoyaltyConstants.LoyaltyPointsDetail.API_NAME);
        }
        NumberUtils.sum(pointsDetail, LoyaltyConstants.LoyaltyPointsDetail.AVAILABLE_POINTS, -changePointsValue);
        LoyaltyPointsDetailService.setPointsStatus(pointsDetail, LoyaltyConstants.LoyaltyPointsDetail.PointsTrend.decrease);
        //积分池加分
        increasePool(tenantId, poolId, changePointsValue, LoyaltyConstants.LoyaltyPointPool.REMAINING_QUANTITY);
        //更新
        record.set(LoyaltyConstants.LoyaltyMemberChangeRecords.IS_FALLBACK, true);
        updateData.getUpdatePointsDetailList().add(pointsDetail);
    }

}