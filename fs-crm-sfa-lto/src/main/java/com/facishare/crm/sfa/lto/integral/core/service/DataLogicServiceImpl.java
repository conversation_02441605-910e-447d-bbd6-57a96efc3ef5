package com.facishare.crm.sfa.lto.integral.core.service;

import com.facishare.crm.sfa.lto.integral.common.constant.IntegralErrorCode;
import com.facishare.crm.sfa.lto.integral.core.calculate.Computing;
import com.facishare.crm.sfa.lto.integral.core.service.dto.RuleDetail;
import com.facishare.crm.sfa.lto.integral.core.util.FieldUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.StringMessage;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.MetaDataActionService;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DataLogicServiceImpl extends AbstractLogicService implements DataLogicService {

    @Autowired
    private IObjectDataService objectDataPgService;
    @Autowired
    private MetaDataFindService metaDataFindService;
    @Autowired
    private MetaDataActionService metaDataActionService;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private LogService logService;

    @Override
    public final IObjectData saveData(IObjectData objectData) {
        try {
            return objectDataPgService.create(objectData, getActionContext());
        } catch (MetadataServiceException e) {
            log.warn("objectDataPgService.create error");
            throw new ValidateException(e.getMessage());
        }
    }

    @Override
    public List<IObjectData> findBySearchQuery(String tenantId, String objectApiName, SearchTemplateQuery query) {
        try {
            QueryResult<IObjectData> result = objectDataPgService.findBySearchQuery(tenantId,
                    objectApiName, query, getActionContext());
            return result.getData();
        } catch (MetadataServiceException e) {
            log.warn("findBySearchQuery error,tenantId:{}", tenantId);
            throw new ValidateException(e.getMessage(), e.getErrCode());
        }
    }

    @Override
    public IObjectData findById(String tenantId, String id, String objectApiName) {
        try {
            IObjectData result = objectDataPgService.findById(id, tenantId, getActionContext(), objectApiName);
            return result;
        } catch (MetadataServiceException e) {
            log.warn("findById error,tenantId:{}", tenantId);
            throw new ValidateException(e.getMessage(), e.getErrCode());
        }
    }

    @Override
    public List<IObjectData> batchUpdateIgnoreOther(List<IObjectData> objectDataList, List<String> validFieldApiNames) {
        try {
            return objectDataPgService.batchUpdateIgnoreOther(objectDataList, validFieldApiNames, getActionContext());
        } catch (MetadataServiceException e) {
            log.warn("objectDataPgService.batchUpdateIgnoreOther error");
            throw new ValidateException(e.getMessage());
        }
    }

    @Override
    public void updateIgnoreOtherAndLog(String tenantId, String userId, String apiName, String ruleName, List<IObjectData> objectDataList, List<String> validFieldApiNames) {
        List<String> dataIdList = objectDataList.stream().map(x -> x.getId()).collect(Collectors.toList());
        List<IObjectData> oldObjectDataList = metaDataFindService.findObjectDataByIds(tenantId, dataIdList, apiName);
        final List<IObjectData> newObjectDataList = batchUpdateIgnoreOther(objectDataList,validFieldApiNames);
        IObjectDescribe objectDescribe = describeLogicService.findObject(tenantId, apiName);
        dataIdList.forEach((dataId) -> {
            StringBuilder builder = new StringBuilder();
            validFieldApiNames.forEach(f -> {
                if (Objects.nonNull(objectDescribe.getFieldDescribe(f))) {
                    builder.append(I18N.text(I18NKey.DATA_CHANGED_BY_SCORE_RULE_LOG, ruleName, objectDescribe.getFieldDescribe(f).getLabel()));
                    builder.append(",");
                }
            });
            if (builder.length() > 0) {
                builder.deleteCharAt(builder.length() - 1);
                ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
                parallelTask.submit(() -> {
                    Map<String, IObjectData> objectDataMap = newObjectDataList.stream().collect(Collectors.toMap(DBRecord::getId, x -> x));
                    Map<String, Map<String, Object>> updateFieldMap = Maps.newHashMap();
                    oldObjectDataList.forEach(x -> {
                        if (Objects.isNull(objectDataMap.get(x.getId()))) {
                            return;
                        }
                        updateFieldMap.put(x.getId(), ObjectDataExt.of(x).diff(objectDataMap.get(x.getId()), objectDescribe));
                    });

                    if (Objects.isNull(objectDataMap.get(dataId))) {
                        return;
                    }

                    logService.logWithCustomMessage(new User(tenantId, userId), EventType.MODIFY, ActionType.Modify, objectDescribe, Lists.newArrayList(objectDataMap.get(dataId)),
                            updateFieldMap, oldObjectDataList, "fs-crm-integral", ruleName, builder.toString());
                });
                parallelTask.run();
            }
        });
    }

    @Override
    public List<IObjectData> bulkSaveData(List<IObjectData> objectDataList) {

        try {
            return objectDataPgService.bulkCreate(objectDataList, true, getActionContext());
        } catch (MetadataServiceException ex) {
            throw new ValidateException(StringMessage.getString(
                    IntegralErrorCode.API_NAME_DUPLICATED.getMessage()));
        }
    }

    @Override
    public List<IObjectData> bulkSaveDataWithAutoNumber(String tenangtId, String userId, List<IObjectData> objectDataList) {
        return metaDataActionService.bulkSaveObjectData(objectDataList, new User(tenangtId, userId), true, false);
    }

    @Override
    public Map<String, Object> computeResults(Double score,
                                              List<RuleDetail.RuleResult> ruleResults) {
        Map<String, Object> fieldMap = Maps.newHashMap();
        ruleResults.forEach(rule -> {
            if (RuleDetail.DIRECT == rule.getAssignmentMethod()) {
                //直接赋值
                //结果字段为百分数，赋值x100
                if (FieldUtils.PERCENT.equals(rule.getFieldType())) {
                    fieldMap.put(rule.getFieldName(), score * 100);
                } else {
                    fieldMap.put(rule.getFieldName(), score);
                }
            } else if (RuleDetail.RANGE == rule.getAssignmentMethod()) {
                //区间赋值
                computeResultValue(score, rule, fieldMap);
            }
        });
        return fieldMap;
    }

    /**
     * 区间赋值，计算赋值结果
     */
    private void computeResultValue(Double score, RuleDetail.RuleResult rule, Map<String, Object> fieldMap) {
        List<RuleDetail.RuleResultBranch> branches = rule.getBranches();
        String fieldType = rule.getFieldType();
        for (int i = 0; i < branches.size(); i++) {
            String op = branches.get(i).getOperator();
            List<Object> values = branches.get(i).getFieldValue();
            List<Object> answers = branches.get(i).getResultValue();
            String otherValue = branches.get(i).getOtherValue();
            List<Object> result = Computing.compute(op, score, values, answers);
            if (!CollectionUtils.isEmpty(result)) {
                //结果
                if (FieldUtils.SELECT_MANY.equals(fieldType)) {
                    fieldMap.put(rule.getFieldName(), result);
                    if (!Objects.isNull(otherValue)
                            && !"".equals(otherValue.trim())) {
                        fieldMap.put(rule.getFieldName() + "__o", otherValue);
                    }
                } else if (FieldUtils.SELECT_ONE.equals(fieldType)) {
                    fieldMap.put(rule.getFieldName(), result.get(0));
                    if (!Objects.isNull(otherValue)
                            && !"".equals(otherValue.trim())) {
                        fieldMap.put(rule.getFieldName() + "__o", otherValue);
                    }
                } else {
                    fieldMap.put(rule.getFieldName(), result.get(0));
                }
                return;
            }

        }
    }

}
