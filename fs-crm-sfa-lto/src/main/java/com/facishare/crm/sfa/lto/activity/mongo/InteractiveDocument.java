package com.facishare.crm.sfa.lto.activity.mongo;

import lombok.Data;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Entity;

import java.io.Serializable;

@Data
@Entity(value = "interactive_document_", noClassnameStored = true)
public class InteractiveDocument implements Serializable {

    @BsonId
    private ObjectId id;

    private String tenantId;
    // 文档标题
    private String title;
    // 对象ApiName(销售记录)
    private String objectApiName;
    // 数据ID(销售记录ID)
    private String objectId;
    // 文档类型（比如：speech, pdf, word等）
    private String type;
    // 创建时间
    private Long createTime;
    // 文档状态（比如：processing, completed等）
    private String status;
    //原始用户名称 (不可修改，用于固定大模型识别后的用户头像等信息)
    private String originalUserName;
    // 用户名称（可手动修改）
    private String userName;
     // 用户ID
    private String userId;
    // API名称或来源
    private String userApiName;
    // 用户头像
    private Integer nameAvaId;
    // 段落内容
    private String content;
    // 段落内容
    private String oldContent;
    // 翻译后内容
    private String translateContent;
    // 翻译后内容
    private String oldTranslateContent;
    // 发言时间
    private Long speakTime;
    // 开始时间（存储毫秒值）
    private String startTime;
    // 结束时间（存储毫秒值）
    private String endTime;
     // 排序序号
    private Long seq;
    // 是否删除
    private boolean isDeleted;
    //  biz_activity_user 表ID
    private String activityUserId;
}
