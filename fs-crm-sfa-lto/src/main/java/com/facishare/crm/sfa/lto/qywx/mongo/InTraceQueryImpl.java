package com.facishare.crm.sfa.lto.qywx.mongo;

import com.mongodb.DBCollection;
import lombok.extern.slf4j.Slf4j;
import org.mongodb.morphia.Datastore;
import org.mongodb.morphia.query.QueryImpl;

import java.util.List;

@Slf4j
public class InTraceQueryImpl<T> extends QueryImpl<T> {
    /**
     * Creates a Query for the given type and collection
     *
     * @param clazz the type to return
     * @param coll  the collection to query
     * @param ds    the Datastore to use
     */
    public InTraceQueryImpl(Class<T> clazz, DBCollection coll, Datastore ds) {
        super(clazz, coll, ds);
    }


    @Override
    public T get() {
        log.info("Mongo get query:{}", this);
        return super.get();
    }

    @Override
    public long countAll() {
        log.info("Mongo countAll query:{}", this);
        return super.countAll();
    }

    @Override
    public List<T> asList() {
        log.info("Mongo asList query:{}", this);
        return super.asList();
    }
}
