package com.facishare.crm.sfa.lto.duplicated;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.lto.common.models.LtoFieldApiConstants;
import com.facishare.crm.sfa.lto.duplicated.models.DuplicatedModels;
import com.facishare.crm.sfa.lto.utils.GrayUtil;
import com.facishare.paas.appframework.core.exception.AcceptableValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.*;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Slf4j
public class DuplicatedProcessingService {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private DuplicatedProcessorFactory processorFactory;
    @Autowired
    protected DuplicatedProcessingRuleService duplicatedProcessingRuleService;

    public DuplicatedModels.ProcessResult process(User user, DuplicatedModels.ProcessArg arg) {
        DuplicatedModels.ProcessResult processResult = new DuplicatedModels.ProcessResult();
        processResult.setErrorCode(LtoFieldApiConstants.SUCCESS);
        processResult.setMessage("成功");
        if (skipLeadsDuplicated(user.getTenantId()) || CollectionUtils.isEmpty(arg.getObjectDataIdList())) {
            return processResult;
        }
        DuplicatedModels.TriggerAction triggerAction = DuplicatedModels.TriggerAction.NONE;
        if ("TASK".equals(arg.getSource())) {
            triggerAction = DuplicatedModels.TriggerAction.RULE_CHANGE;
        } else if(arg.getSource().startsWith("TASK_")) {
            String source = arg.getSource().replace("TASK_", "");
            triggerAction = DuplicatedModels.TriggerAction.getTriggerAction(source);
        }
        boolean needInvalidData = false;
        if(DuplicatedModels.TriggerAction.INVALID.equals(triggerAction)) {
            needInvalidData = true;
        }
        List<IObjectData> dataList = getUnRefreshDataList(user, arg.getObjectDataIdList(), needInvalidData);
        try {
            DuplicatedDataProcessor processor = processorFactory.getDuplicatedDataProcessor(triggerAction);
            if(processor == null) {
                return processResult;
            }
            if(DuplicatedModels.TriggerAction.EDIT.equals(triggerAction) || DuplicatedModels.TriggerAction.MERGE.equals(triggerAction)
                    || DuplicatedModels.TriggerAction.OPEN_API_EDIT.equals(triggerAction)) {
                List<DuplicatedModels.DuplicatedProcessing> processingRuleList = duplicatedProcessingRuleService.getDuplicatedProcessingListByTriggerAction(user, triggerAction);
                if(CollectionUtils.isEmpty(processingRuleList)) {
                    return processResult;
                }
                DuplicatedModels.TriggerAction clearDuplicatedTriggerAction = DuplicatedModels.TriggerAction.CLEAR_DUPLICATED;
                DuplicatedDataProcessor clearDuplicatedProcessor = processorFactory.getDuplicatedDataProcessor(clearDuplicatedTriggerAction);
                if(clearDuplicatedProcessor != null) {
                    DuplicatedDataProcessor.DuplicatedDataProcessArg clearDuplicatedProcessArg = DuplicatedDataProcessor.DuplicatedDataProcessArg
                            .builder().dataList(dataList).user(user).action(clearDuplicatedTriggerAction)
                            .refreshVersion(arg.getRefreshVersion()).build();
                    clearDuplicatedProcessor.process(clearDuplicatedProcessArg);
                }
            }
            DuplicatedDataProcessor.DuplicatedDataProcessArg processArg = DuplicatedDataProcessor.DuplicatedDataProcessArg
                    .builder().dataList(dataList).user(user).action(triggerAction)
                    .refreshVersion(arg.getRefreshVersion()).build();
            processor.process(processArg);
        } catch (AcceptableValidateException acceptableValidateException) {
            log.info("process leads duplicated AcceptableValidateException");
        } catch (Exception ex) {
            log.error("process leads duplicated error", ex);
            processResult.setErrorCode(LtoFieldApiConstants.FAILED);
            processResult.setMessage("失败");
        }
        return processResult;
    }

    private List<IObjectData> getUnRefreshDataList(User user, List<String> dataIds, boolean needInvalidData) {
        List<IObjectData> result;
        if(needInvalidData) {
            result = serviceFacade.findObjectDataByIdsIncludeDeleted(user, dataIds, Utils.LEADS_API_NAME);
        } else {
            result = serviceFacade.findObjectDataByIds(user.getTenantId(), dataIds, Utils.LEADS_API_NAME);
        }
        return result;
    }

    public void directProcess(User user, IObjectData objectData, DuplicatedModels.TriggerAction triggerAction) {
        try {
            DuplicatedDataProcessor processor = processorFactory.getDuplicatedDataProcessor(DuplicatedModels.TriggerAction.DIRECT_PROCESSOR);
            if(processor == null) {
                return;
            }
            DuplicatedDataProcessor.DuplicatedDataProcessArg processArg = DuplicatedDataProcessor.DuplicatedDataProcessArg
                    .builder().dataList(Lists.newArrayList(objectData)).user(user).action(triggerAction)
                    .refreshVersion(0).build();
            processor.process(processArg);
        } catch (AcceptableValidateException acceptableValidateException) {
            throw acceptableValidateException;
        } catch(Exception ex) {
            log.error("process leads duplicated error", ex);
            throw ex;
        }
    }

    private boolean skipLeadsDuplicated(String tenantId) {
        return GrayUtil.isGrayEnable("skip_leads_duplicated_tenant_id", tenantId);
    }
}