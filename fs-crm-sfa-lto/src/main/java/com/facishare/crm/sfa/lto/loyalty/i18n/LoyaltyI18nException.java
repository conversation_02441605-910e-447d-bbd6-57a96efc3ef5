package com.facishare.crm.sfa.lto.loyalty.i18n;

import com.facishare.crm.sfa.lto.loyalty.utils.LoyaltyI18nKey;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.metadata.util.GetI18nKeyUtil;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Getter
public class LoyaltyI18nException extends ValidateException {

    private String key;

    private List<I18nParam> paramList;

    public LoyaltyI18nException(String message) {
        super(message);
    }

    public LoyaltyI18nException(String message, String key, List<I18nParam> paramList) {
        super(message);
        this.key = key;
        this.paramList = paramList;
    }

    public LoyaltyI18nException(String message, int errorCode, String key, List<I18nParam> paramList) {
        super(message, errorCode);
        this.key = key;
        this.paramList = paramList;
    }

    @Data
    public static class I18nParam {
        private String key;
        private ParamType type;

        public I18nParam(String key) {
            this.key = key;
            this.type = ParamType.constant;
        }

        public I18nParam(String key, ParamType type) {
            this.key = key;
            this.type = type;
        }
    }

    public enum ParamType {
        constant, variable
    }

    public static LoyaltyI18nException build(String key) {
        return build(key, Lists.newArrayList());
    }

    public static LoyaltyI18nException build(int errorCode, String key) {
        return build(errorCode, key, Lists.newArrayList());
    }

    public static LoyaltyI18nException build(String key, String apiName) {
        return build(key, Lists.newArrayList(new I18nParam(GetI18nKeyUtil.getDescribeDisplayNameKey(apiName), ParamType.variable)));
    }

    public static LoyaltyI18nException build(int errorCode, String key, String apiName) {
        return build(errorCode, key, Lists.newArrayList(new I18nParam(GetI18nKeyUtil.getDescribeDisplayNameKey(apiName), ParamType.variable)));
    }

    /**
     * 国际化key格式
     * sfa_loyalty_parameters_%s
     * 以下为使用到的参数
     * tenantId
     * uniqueId
     * memberId
     * pointPoolId
     * tierId
     * pointTypeId
     */
    public static LoyaltyI18nException buildMissingParametersException(String fieldName) {
        return build(LoyaltyI18nKey.MISSING_PARAMETERS, Lists.newArrayList(new I18nParam("sfa_loyalty_parameters_" + fieldName, ParamType.variable)));
    }

    public static LoyaltyI18nException buildMissingParametersException(String describeApiName, String fieldApiName) {
        return build(LoyaltyI18nKey.MISSING_PARAMETERS, Lists.newArrayList(new I18nParam(GetI18nKeyUtil.getFieldLabelKey(describeApiName, fieldApiName), ParamType.variable)));
    }

    public static LoyaltyI18nException buildInvalidParametersException(String describeApiName, String fieldApiName) {
        return build(LoyaltyI18nKey.NOT_FOUND, Lists.newArrayList(new I18nParam(GetI18nKeyUtil.getFieldLabelKey(describeApiName, fieldApiName), ParamType.variable)));
    }

    public static LoyaltyI18nException build(String key, List<I18nParam> paramList) {
        String message = getMessageForLanguage(key, paramList);
        return new LoyaltyI18nException(message, key, paramList);
    }

    public static LoyaltyI18nException build(int errorCode, String key, List<I18nParam> paramList) {
        String message = getMessageForLanguage(key, paramList);
        return new LoyaltyI18nException(message, errorCode, key, paramList);
    }

    public static String getMessageForLanguage(String key, List<I18nParam> paramList) {
        if (paramList == null || paramList.isEmpty()) {
            return I18N.text(key);
        }
        List<String> params = new ArrayList<>();
        for (I18nParam param : paramList) {
            String p;
            if (param.getType() == ParamType.variable) {
                p = I18N.text(param.getKey());
            } else {
                p = param.getKey();
            }
            params.add(p);
        }
        return I18N.text(key, params);
    }

    public List<String> getCrmRecordParameters() {
        return getParamList().stream().map(param -> {
            if (param.getType() == ParamType.variable) {
                return getI18ParameterKey(param.getKey());
            }
            return param.getKey();
        }).collect(Collectors.toList());
    }

    public String getMessageForLanguage(String tenantId, String language) {
        try {
            I18N.setContext(tenantId, language);
            return getMessageForLanguage(this.key, this.paramList);
        } finally {
            I18N.clearContext();
        }
    }

    public static String getI18ParameterKey(String key) {
        return String.format("#I18N#%s", key);
    }
}
