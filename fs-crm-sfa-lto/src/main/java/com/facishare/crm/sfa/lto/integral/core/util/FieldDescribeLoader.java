package com.facishare.crm.sfa.lto.integral.core.util;

import com.facishare.paas.appframework.metadata.ObjectRelateMapping;
import org.apache.commons.io.IOUtils;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 */
public class FieldDescribeLoader {

    public static String getFieldDescribeJson(String fieldApiName) {
        try {
            String resourceName = "/field_describe/" + fieldApiName + ".json";
            InputStream inputStream = ObjectRelateMapping.class.getResourceAsStream(resourceName);
            return IOUtils.toString(inputStream, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException("getFieldDescribeJson failed", e);
        }
    }
}
