package com.facishare.crm.sfa.lto.loyalty.service.memberTierStrategy;

import com.facishare.paas.metadata.api.IObjectData;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

public interface IMemberTierStrategy {

    String name();

    /**
     * @return 需要更新的字段值, 不会自动更新
     */
    Map<String, Object> upgrades(String tenantId, Param param);

    /**
     * @return 需要更新的字段值, 不会自动更新
     */
    Map<String, Object> task(String tenantId, Param param);

    enum Operator {
        /**
         * 等级变更触发普通升级
         */
        upgrades,
        /**
         * 评定日触发定级变更
         */
        task
    }

    @Data
    @NoArgsConstructor
    class Param {
        private IObjectData member;
        private IObjectData tierClassInfo;
        private IObjectData currentTier;
        private boolean notChangeTierClass;

        public Param(IObjectData member, IObjectData tierClassInfo, IObjectData currentTier) {
            this.member = member;
            this.tierClassInfo = tierClassInfo;
            this.currentTier = currentTier;
        }
    }
}
