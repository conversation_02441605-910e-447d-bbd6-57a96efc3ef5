package com.facishare.crm.sfa.lto.accountreerelation;

import com.facishare.crm.sfa.lto.accountreerelation.models.AccountTreeRelationModels.*;
import com.facishare.crm.sfa.lto.equityrelationship.model.EquityRelationshipDataModel.*;
import com.facishare.paas.appframework.core.model.User;

public interface IEquityRelationDataService {
    RelationshipModel getEquityRelationInfo(User user, String companyName);
    CompanyDetail queryCompanyDetailIsExistByName(User user, String companyName);

    void processEquityRelationData(User user, RelationshipModel equityRelationInfo, CreateAccountTreeRelationArg arg);
}
