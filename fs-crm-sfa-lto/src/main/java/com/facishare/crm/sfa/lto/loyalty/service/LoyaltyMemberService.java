package com.facishare.crm.sfa.lto.loyalty.service;

import com.facishare.crm.sfa.lto.loyalty.constants.LoyaltyConstants;
import com.facishare.crm.sfa.lto.loyalty.i18n.LoyaltyI18nException;
import com.facishare.crm.sfa.lto.loyalty.service.memberTierStrategy.IMemberTierStrategy;
import com.facishare.crm.sfa.lto.loyalty.utils.LoyaltyI18nKey;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.cache.RedissonService;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

@Slf4j
@Service
public class LoyaltyMemberService {

    @Resource
    private RedissonService redissonService;
    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private LoyaltyMemberTierService loyaltyMemberTierService;
    @Resource
    private LoyaltyPointsDetailService loyaltyPointsDetailService;

    public static String getMemberLockKey(String tenantId, String memberId) {
        return "loyalty_points_" + tenantId + "_" + memberId;
    }

    public void tryMemberLock(String tenantId, String memberId, Consumer<RLock> handler) {
        RLock lock = redissonService.tryLock(3, 60, TimeUnit.SECONDS, getMemberLockKey(tenantId, memberId));
        if (null == lock) {
            throw LoyaltyI18nException.build(LoyaltyI18nKey.ErrorStatus.TryLockFailed.getErrorCode(), LoyaltyI18nKey.PROCESSING);
        }
        try {
            handler.accept(lock);
        } finally {
            redissonService.unlock(lock);
        }
    }

    public RLock tryLockMultipleMember(String tenantId, Set<String> memberIdList) {
        String[] keys = memberIdList.stream().map(memberId -> getMemberLockKey(tenantId, memberId)).toArray(String[]::new);
        RLock lock = this.redissonService.tryMultiLock(1, 10, TimeUnit.SECONDS, keys);
        if (lock == null) {
            throw LoyaltyI18nException.build(LoyaltyI18nKey.ErrorStatus.TryLockFailed.getErrorCode(), LoyaltyI18nKey.PROCESSING);
        }
        return lock;
    }

    public void unLockMultipleMember(RLock rLock) {
        redissonService.unlock(rLock);
    }

    public void unLockUpdateMemberPointsAndTier(String tenantId, String memberId, IMemberTierStrategy.Operator operator) {
        IObjectData member = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), memberId, LoyaltyConstants.LoyaltyMember.API_NAME);
        unLockUpdateMemberPointsAndTier(tenantId, member, operator, new HashMap<>());
    }

    public void unLockUpdateMemberPointsAndTier(String tenantId, IObjectData member, IMemberTierStrategy.Operator operator, Map<String, Object> updateFields) {
        Map<String, Object> updatePointsFields = loyaltyPointsDetailService.fillPoints(tenantId, member.getId());
        updateFields.putAll(updatePointsFields);
        for (Map.Entry<String, Object> entry : updatePointsFields.entrySet()) {
            member.set(entry.getKey(), entry.getValue());
        }
        updateFields.putAll(loyaltyMemberTierService.run(tenantId, member, operator));
        if (!CollectionUtils.isEmpty(updateFields)) {
            serviceFacade.updateWithMap(User.systemUser(tenantId), member, updateFields);
        }
    }

    public void unLockUpdateMemberPoints(String tenantId, String memberId) {
        IObjectData member = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), memberId, LoyaltyConstants.LoyaltyMember.API_NAME);
        unLockUpdateMemberPoints(tenantId, member, new HashMap<>());
    }

    public void unLockUpdateMemberPoints(String tenantId, IObjectData member, Map<String, Object> updateFields) {
        updateFields.putAll(loyaltyPointsDetailService.fillPoints(tenantId, member.getId()));
        if (!CollectionUtils.isEmpty(updateFields)) {
            serviceFacade.updateWithMap(User.systemUser(tenantId), member, updateFields);
        }
    }
}
