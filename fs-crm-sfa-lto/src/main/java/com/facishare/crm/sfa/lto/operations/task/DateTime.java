package com.facishare.crm.sfa.lto.operations.task;

import com.facishare.paas.expression.ExpressionService;
import com.facishare.paas.expression.type.PDateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.function.BiConsumer;

class DateTime implements TemplateVariableField {
    private final String raw;
    private final Set<String> variable;
    private final BiConsumer<OperationsTask, Object> setter;
    private final ExpressionService expressionService;

    DateTime(String raw, BiConsumer<OperationsTask, Object> setter, ExpressionService expressionService) {
        this.raw = raw;
        this.setter = setter;
        this.expressionService = expressionService;
        this.variable = TemplateVariableField.getVariable(raw);
    }

    @Override
    public String raw() {
        return raw;
    }

    @Override
    public Set<String> variable() {
        return variable;
    }

    @Override
    public boolean hasVariable() {
        return true;
    }

    @Override
    public Object replace(Map<String, Value> valueMap) {
        Map<String, Object> temp = valueMap.entrySet().stream().collect(HashMap::new, (map, entry) -> map.put(entry.getKey(), entry.getValue().raw()), HashMap::putAll);
        Map<String, Object> nestedMap = convertIntoNestedMap(temp);
        Object evaluate = expressionService.evaluate(TemplateVariableField.eraseVariableRegex(raw), nestedMap);
        if (evaluate instanceof PDateTime) {
            return ((PDateTime) evaluate).toTimeStamp();
        }
        return evaluate;
    }

    @Override
    @SuppressWarnings("unchecked")
    public BiConsumer<OperationsTask, Object> setter() {
        return setter;
    }

    private static Map<String, Object> convertIntoNestedMap(Map<String, Object> valueMap) {
        Map<String, Object> res = new HashMap<>();
        for (Map.Entry<String, Object> entry : valueMap.entrySet()) {
            convert(entry, res);
        }
        return res;
    }

    @SuppressWarnings("unchecked")
    private static void convert(Map.Entry<String, Object> entry, Map<String, Object> res) {
        String[] keys = entry.getKey().split("\\.");
        Map<String, Object> currentMap = res;
        for (int i = 0; i < keys.length; i++) {
            String key = keys[i];
            if (i == keys.length - 1) {
                // If it's the last key, set the value directly
                convertValue(entry);
                currentMap.put(key, entry.getValue());
            } else {
                Map<String, Object> nextMap;
                Object next = currentMap.get(key);
                if (next instanceof Map) {
                    nextMap = (Map<String, Object>) next;
                } else {
                    nextMap = new HashMap<>();
                    currentMap.put(key, nextMap);
                }
                currentMap = nextMap;
            }
        }
    }

    @SuppressWarnings("unchecked")
    private static void convertValue(Map.Entry<String, Object> entry) {
        Object value = entry.getValue();
        doConvertValue(entry, value);
        if (value instanceof Map) {
            doConvertValue((Map<String, Object>) value);
        }
    }

    private static void doConvertValue(Map<String, Object> map) {
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            doConvertValue(entry, entry.getValue());
        }
    }

    private static void doConvertValue(Map.Entry<String, Object> entry, Object value) {
        if (value instanceof Long) {
            entry.setValue(PDateTime.of((Long) value));
        }
        if (value instanceof String) {
            try {
                entry.setValue(PDateTime.of((String) value));
            } catch (Exception ignored) {
                // ignored
            }
        }
    }
}
