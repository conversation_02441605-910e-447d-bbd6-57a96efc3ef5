package com.facishare.crm.sfa.lto.common;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.lto.common.models.LtoLicenseModel;
import com.facishare.crm.sfa.lto.utils.CollectionUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.license.Result.LicenseVersionResult;
import com.facishare.paas.license.Result.ModuleInfoResult;
import com.facishare.paas.license.Result.ParaInfoResult;
import com.facishare.paas.license.arg.QueryModuleArg;
import com.facishare.paas.license.arg.QueryModuleParaArg;
import com.facishare.paas.license.arg.QueryProductArg;
import com.facishare.paas.license.common.LicenseContext;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.paas.license.pojo.ModuleInfoPojo;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/*
license相关wiki page: http://wiki.firstshare.cn/pages/viewpage.action?pageId=90509404
 */
@ServiceModule("sfa_license_service")
@Component
@Slf4j
public class LtoLicenseService {
    @Autowired
    private LicenseClient licenseClient;

    @ServiceMethod("check_license_exist")
    public LtoLicenseModel.CheckModuleLicenseExistResult checkModuleLicenseExist(ServiceContext context, LtoLicenseModel.CheckModuleLicenseExistArg arg) {
        boolean hasLicense = checkModuleLicenseExist(context.getTenantId(), arg.getPackageName());
        return LtoLicenseModel.CheckModuleLicenseExistResult.builder().hasLicense(hasLicense).build();
    }

    public boolean checkModuleLicenseExist(String tenantId, String packageName) {
        QueryModuleArg queryModuleArg = new QueryModuleArg();
        LicenseContext licenseContext = getLicenseContext(tenantId);
        queryModuleArg.setLicenseContext(licenseContext);
        ModuleInfoResult result = licenseClient.queryModule(queryModuleArg);
        if (result == null) {
            return false;
        }
        List<ModuleInfoPojo> modules = result.getResult();
        return (modules != null && modules.stream().anyMatch(module -> Objects.equals(module.getModuleCode(), packageName)));
    }

    @ServiceMethod("check_modules_license_exist")
    public LtoLicenseModel.CheckModulesLicenseExistResult checkModuleLicenseExist(ServiceContext context, LtoLicenseModel.CheckModulesLicenseExistArg arg) {
        if (arg == null || CollectionUtils.isEmpty(arg.getPackageNameList())) {
            throw new ValidateException(I18N.text(I18NKey.REQUEST_PARAM_IS_NULL));
        }
        QueryModuleArg queryModuleArg = new QueryModuleArg();
        LicenseContext licenseContext = getLicenseContext(context.getTenantId());
        queryModuleArg.setLicenseContext(licenseContext);
        ModuleInfoResult result = licenseClient.queryModule(queryModuleArg);
        LtoLicenseModel.CheckModulesLicenseExistResult checkModulesLicenseExistResult = LtoLicenseModel.CheckModulesLicenseExistResult.builder().build();
        Map<String, Boolean> licenseMap = Maps.newHashMap();
        arg.getPackageNameList().forEach(x -> licenseMap.put(x, false));
        checkModulesLicenseExistResult.setLicenseResult(licenseMap);
        if (result == null) {
            return checkModulesLicenseExistResult;
        }
        List<ModuleInfoPojo> modules = result.getResult();
        if (modules != null) {
            arg.getPackageNameList().forEach(moduleCode -> {
                if (modules.stream().anyMatch(module -> Objects.equals(module.getModuleCode(), moduleCode))) {
                    licenseMap.put(moduleCode, true);
                }
            });
        }
        return checkModulesLicenseExistResult;
    }

    @ServiceMethod("get_crm_product_version")
    public LicenseVersionResult getCRMProductVersion(ServiceContext context) {
        return getCRMProductVersion(context.getTenantId());
    }

    @NotNull
    private LicenseContext getLicenseContext(String tenantId) {
        LicenseContext licenseContext = new LicenseContext();
        licenseContext.setAppId("CRM");
        licenseContext.setTenantId(tenantId);
        licenseContext.setUserId(User.SUPPER_ADMIN_USER_ID);
        return licenseContext;
    }

    private LicenseVersionResult getCRMProductVersion(String tenantId) {
        QueryProductArg queryProductArg = new QueryProductArg();
        LicenseContext licenseContext = getLicenseContext(tenantId);
        queryProductArg.setLicenseContext(licenseContext);

        return licenseClient.queryProductVersion(queryProductArg);
    }

    /**
     * 查询license的配额数量
     * @param user
     * @param licenseName
     * @return
     */
    public Integer getLicenseQuota(User user,String moduleCode, String licenseName){
        QueryModuleParaArg arg = new QueryModuleParaArg();
        arg.setContext(getLicenseContext(user.getTenantId()));
        arg.setModuleCode(moduleCode);
        arg.setParaKeys(Sets.newHashSet(licenseName));
        ParaInfoResult result = licenseClient.queryModulePara(arg);
        if(ObjectUtils.isEmpty(result) || CollectionUtil.isEmpty(result.getResult()) ||  ObjectUtils.isEmpty(result.getResult().get(0).getParaValue())){
            log.error("getLicenseQuota result:{}", JSONObject.toJSONString(result));
            throw new ValidateException(licenseName+"当前应用没有配额");
        }
        return Integer.valueOf(result.getResult().get(0).getParaValue());
    }
}