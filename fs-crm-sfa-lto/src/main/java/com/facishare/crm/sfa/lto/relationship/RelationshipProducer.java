package com.facishare.crm.sfa.lto.relationship;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.lto.common.LtoTaskService;
import com.facishare.crm.sfa.lto.exception.ExceptionUtil;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 联系人、线索成员关系图
 *
 * <AUTHOR> lik
 * @date : 2022/7/14 13:48
 */
@Component
public class RelationshipProducer {
    public enum OperationType {
        ADD("add", "新建"),
        EDIT("edit", "新建"),
        CHANGE_OWNER("changeOwner", "修改负责人"),
        ALLOCATE("allocate", "分配"),
        CHOOSE("choose", "领取"),
        BULK_RETURN("bulkReturn", "批量退回"),
        RESULT_ONE("resultOne", "退回"),
        ADD_TEAM_MEMBER("addTeamMember", "添加相关成员"),
        EDIT_TEAM_MEMBER("editTeamMember", "修改相关成员"),
        TAKE_BACK("takeBack", "收回"),
        TRANSFER("transfer", "转换"),
        BULK_DELETE("BulkDelete", "删除"),
        INSERT_IMPORT("InsertImport", "上传保存"),
        UPDATE_IMPORT("updateImport", "上传更新"),
        BULK_ASSOCIATE("BulkAssociate", "关联");
        private String value;
        private String label;

        OperationType(String value, String label) {
            this.value = value;
            this.label = label;
        }

        public String getValue() {
            return this.value;
        }

        public static String operationTypeOf(String value) {
            for (OperationType v : values()) {
                if (v.getValue().equals(value)) {
                    return v.label;
                }
            }
            return null;
        }
    }

    private static final String MQ_PRODUCER_CONFIG_NAME = "fs-crm-task-sfa-mq.ini";
    private static final String MQ_RFM_PRODUCER_CONFIG_SECTION_NAME = "nomon-callback-producer";
    //计算联系人、线索tags
    private static final String DEAL_MEMBER_RELATIONSHIP_TAG = "deal_member_relationship_tag";
    //第一次处理历史数据
    public static final String HISTORY_RELATIONSHIP_TAGS = "history_relationship_tags";
    //通讯录上传
    public static final String ADDRESS_BOOK_RELATIONSHIP_TAG = "address_book_relationship_tag";
    private AutoConfMQProducer producer;
    @Autowired
    LtoTaskService taskService;

    @PostConstruct
    public void init() {
        producer = new AutoConfMQProducer(MQ_PRODUCER_CONFIG_NAME, MQ_RFM_PRODUCER_CONFIG_SECTION_NAME);
    }

    @PreDestroy
    public void destroy() {
        producer.close();
    }

    public void setMsgToSend(String tenantId, List<String> dataId, String operationType, String objectApiName) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("tenantId", tenantId);
        jsonObject.put("objectApiName", objectApiName);
        jsonObject.put("dataIds", dataId);
        jsonObject.put("operationType", operationType);
        sendMQ(jsonObject, tenantId, DEAL_MEMBER_RELATIONSHIP_TAG);
    }

    public void setMsgToSend(String tenantId, List<String> dataId, String operationType, String objectApiName, List<String> transferObjName) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("tenantId", tenantId);
        jsonObject.put("objectApiName", objectApiName);
        jsonObject.put("dataIds", dataId);
        jsonObject.put("operationType", operationType);
        jsonObject.put("transferObjName", transferObjName);
        sendMQ(jsonObject, tenantId, DEAL_MEMBER_RELATIONSHIP_TAG);
    }

    /**
     * It sends a message to the MQ recycling.rule.topic .
     *
     * @param messageObject The message object to be sent.
     * @param tenantId      the tenantId of the message
     */
    public void sendMQ(JSONObject messageObject, String tenantId, String tags) {
        String messageString = JSON.toJSONString(messageObject);
        Message message = new Message(producer.getDefaultTopic(), tags, messageString.getBytes());
        message.putUserProperty("x-fs-ei", tenantId);
        message.setTags(tags);

        SendResult sendResult = producer.send(message);

        if (sendResult.getSendStatus() != SendStatus.SEND_OK) {
            ExceptionUtil.throwCommonBusinessException();
        }
    }

    public void sendTaskMq(String tenantId, JSONObject jsonObject, String taskBiz, String userId) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        Date executeTime = cal.getTime();
        String dataId = taskBiz;
        if (ObjectUtils.isNotEmpty(userId)) {
            dataId = dataId + "-" + userId;
        }
        taskService.createOrUpdateTask(taskBiz, tenantId, dataId, executeTime, jsonObject.toJSONString(), null);
    }
}
