package com.facishare.crm.sfa.lto.quality.models;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2023/02/14 17:44
 * @Version 1.0
 **/
public interface QualityInspectionModel {
    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class QualityInspectionRule{
        private String id;
        private String tenantId;
        private int type;  //质检类型：敏感词，会话特征，行为质检
        private String name;  //质检名称
        private List<String> monitors;  //监听成员
        private String dirtyWords; //敏感词json
        private List<String> sessionFeature; //会话特征：超时回复
        private String sessionFeatureRule; //规则：回复时长
        private List<String> sessionAction;  //行为类型：删除客户，被客户删除
        private List<String> targetType; //质检对象：企微客户，员工
        private List<String> sessionType; //会话场景：单聊，群聊
        private long msgPush;  //消息通知开启(选中)，扩展定时设置
        private List<String> msgUser;  //消息接收人
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class DirtyWords{
        private List<String> precise;  //任一命中
        private List<String> combined; //多词命中
    }
}
