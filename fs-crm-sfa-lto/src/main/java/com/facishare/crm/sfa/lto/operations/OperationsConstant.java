package com.facishare.crm.sfa.lto.operations;

public class OperationsConstant {
    public static final String MODULE_CODE = "operations_workbench_app";
    public static final String OPERATION_STRATEGY_OBJECT_API_NAME = "OperationsStrategyObj";
    public static final String OPERATION_STRATEGY_ACTIVITY_TEMPLATE_OBJECT_API_NAME = "OperationsActivityTemplateObj";
    public static final String OPERATION_STRATEGY_TASK_TEMPLATE_OBJECT_API_NAME = "OperationsTaskTemplateObj";
    public static final String OPERATION_STRATEGY_ACTIVITY_OBJECT_API_NAME = "OperationsActivityObj";
    public static final String OPERATION_STRATEGY_TASK_OBJECT_API_NAME = "OperationsTaskObj";
    public static final String OPERATIONS_STRATEGY_ID = "operations_strategy_id";
    public static final String OPERATIONS_ACTIVITY_ID = "operations_activity_id";
    public static final String OPERATIONS_ACTIVITY_TEMPLATE_ID = "operations_activity_template_id";
    public static final String OPERATIONS_TASK_TEMPLATE_ID = "operations_task_template_id";
    public static final String DESCRIPTION = "description";
    public static final String BIZ_OBJECT_API_NAME = "biz_object_api_name";
    public static final String BIZ_DATA = "biz_data";
    public static final String BIZ_DATA_FILTERS = "biz_data_filters";
    public static final String BIZ_DATA_ID = "biz_data_id";
    public static final String TASK_EXECUTION_FREQUENCY = "task_execution_frequency";
    public static final String TASK_EXECUTION_START = "task_execution_start";
    public static final String TASK_EXECUTION_END = "task_execution_end";
    public static final String TASK_EXECUTION_TIME = "task_execution_time";
    public static final String SUMMARY_TABLE = "summary_table";
    public static final String VISIBILITY = "visibility";
    public static final String BIZ_STATUS = "biz_status";
    public static final String TASK_ID = "task_id";
    public static final String TASK_NAME = "task_name";
    public static final String TASK_DESCRIPTION = "task_description";
    public static final String TASK_DEAD_LINE = "task_dead_line";
    public static final String TASK_EXECUTORS = "task_executors";
    public static final String TASK_CC = "task_cc";
    public static final String TASK_REMINDS = "task_reminds";
    public static final String OPERATIONS_INSTANCE_CREATE_TIME = "operations_instance_create_time";

    private OperationsConstant() {}
}
