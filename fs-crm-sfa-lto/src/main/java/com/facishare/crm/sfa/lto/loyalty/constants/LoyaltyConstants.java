package com.facishare.crm.sfa.lto.loyalty.constants;

public interface LoyaltyConstants {

    /**
     * 会员计划
     */
    class LoyaltyProgram {
        public static String API_NAME = "LoyaltyProgramObj";
        public static String ENABLE_STATUS = "enable_status";
    }

    /**
     * 成本组织
     */
    class LoyaltyPointOrg {
        public static String API_NAME = "LoyaltyPointOrgObj";

        public static String ENABLE_STATUS = "enable_status";
        public static String TOTAL_POINTS = "total_points";
        /**
         * 剩余积分
         */
        public static String REMAINING_POINTS = "remaining_points";
        /**
         * 预警积分
         */
        public static String REMAINING_ALARM_POINTS = "remaining_alarm_points";
        /**
         * 是否异业
         */
        public static String IS_BUSINESS_PARTNER = "is_business_partner";
        public static String COST_ORGANIZATION_LEADER = "cost_organization_leader";
        public static String EXTERNAL_ORGANIZATION_LEADER = "external_organization_leader";
        public static String EXTERNAL_CONTACT_NUMBER = "external_contact_number";
    }

    /**
     * 积分分类
     */
    class LoyaltyPointType {
        public static String API_NAME = "LoyaltyPointTypeObj";
        public static String PROGRAM_ID = "program_id";
        public static String ACTIVATION_METHOD = "activation_method";
        public static String FREEZE_PERIOD_DAYS = "freeze_period_days";
        public static String EXPIRY_METHOD = "expiry_method";
        public static String EXPIRY_DATE = "expiry_date";
        public static String VALIDITY_PERIOD_LENGTH = "validity_period_length";
        public static String VALIDITY_PERIOD_UNIT = "validity_period_unit";
    }

    /**
     * 积分池
     */
    class LoyaltyPointPool {
        public static String API_NAME = "LoyaltyPointPoolObj";
        public static String ORG_ID = "org_id";
        public static String POINT_TYPE_ID = "point_type_id";
        /**
         * 原始积分数量
         */
        public static String ORIGINAL_QUANTITY = "original_quantity";
        /**
         * 剩余积分
         */
        public static String REMAINING_QUANTITY = "remaining_quantity";
        public static String REMAINING_POINTS_WARNING_VALUE = "remaining_points_warning_value";

        /**
         * 回收积分
         */
        public static String RECOVERY_QUANTITY = "recovery_quantity";
        public static String ENABLE_STATUS = "enable_status";
        public static String IS_DEFAULT = "is_default";
    }

    /**
     * 会员等级分类
     */
    class LoyaltyTierClass {
        public static String API_NAME = "LoyaltyTierClassObj";
        public static String PROGRAM_ID = "program_id";
        public static String TIER_CLASS_STATUS = "tier_class_status";

        /**
         * 顺序号，生效优先级
         */
        public static String SEQ = "seq";
        /**
         * 等级模式
         */
        public static String LEVEL_MODE = "level_mode";
        /**
         * 评定日是否清空积分
         */
        public static String CLEAR_POINTS = "clear_points";
        public static String MINIMUM_LEVEL_DURATION = "minimum_level_duration";
        public static String MINIMUM_LEVEL_DURATION_UNIT = "minimum_level_duration_unit";
        /**
         * 评定频率
         */
        public static String ASSESSMENT_FREQUENCY = "assessment_frequency";
        /**
         * 等级开始月份
         */
        public static String LEVEL_START_MONTH = "level_start_month";
        /**
         * 等级开始日
         */
        public static String LEVEL_START_DAY = "level_start_day";
    }

    /**
     * 会员等级详情
     */
    class LoyaltyTier {
        public static String API_NAME = "LoyaltyTierObj";
        public static String TIER_CLASS_ID = "tier_class_id";
        /**
         * 是否为主等级
         */
        public static String PRIMARY_TIER = "primary_tier";
        /**
         * 当前等级最低积分
         */
        public static String POINTS_REQUIRED = "points_required";
        /**
         * 当前等级最高积分
         */
        public static String POINTS_TO_NEXT_TIER = "points_to_next_tier";
        /**
         * 等级状态
         */
        public static String TIER_STATUS = "tier_status";
    }

    /**
     * 会员管理
     */
    class LoyaltyMember {
        public static String API_NAME = "LoyaltyMemberObj";
        public static String PROGRAM_ID = "program_id";
        public static String TIER_ID = "tier_id";
        /**
         * 注册时间
         */
        public static String REGISTRATION_TIME = "registration_time";
        /**
         * 当前等级开始时间
         */
        public static String TIER_START_TIME = "tier_start_time";
        public static String CONSUMER_POINTS = "consumer_points";
        public static String GRADING_POINTS = "grading_points";
        public static String FROZEN_POINTS = "frozen_points";
        public static String FROZEN_GRADING_POINTS = "frozen_grading_points";
        /**
         * 下次评定日
         */
        public static String EVALUATION_DATE = "evaluation_date";

        public static String MEMBER_STATUS = "member_status";
    }

    /**
     * 会员积分明细
     */
    class LoyaltyPointsDetail {
        public static String API_NAME = "LoyaltyPointsDetailObj";
        public static String PROGRAM_ID = "program_id";
        public static String POINTS_TYPE_ID = "points_type_id";
        /**
         * 是否定级积分,查询关联对象字段
         */
        public static String IS_QUALIFYING = "points_type_id.is_qualifying";
        /**
         * 是否允许赠送,查询关联对象字段
         */
        public static String ALLOW_GIFTING = "points_type_id.allow_gifting";
        public static String MEMBER_ID = "member_id";
        public static String MEMBER_BEHAVIOR = "member_behavior";
        public static String POINTS_QUANTITY = "points_quantity";
        /**
         * 可用积分
         */
        public static String AVAILABLE_POINTS = "available_points";
        public static String EFFECTIVE_TIME = "effective_time";
        public static String EXPIRY_TIME = "expiry_time";
        /**
         * 积分状态 :
         * Available 可用
         * Frozen 冻结
         * Expired 过期
         * Voided 已作废
         * Exhausted 已用完
         * Clear 升降级积分清零
         */
        public static String POINTS_STATUS = "points_status";

        public enum PointsStatus {
            /**
             * 可用
             */
            Available,
            /**
             * 冻结
             */
            Frozen,
            /**
             * 过期
             */
            Expired,
            /**
             * 作废
             */
            Voided,
            /**
             * 已用完
             */
            Exhausted,
            /**
             * 评定日作废
             */
            Clear
        }

        public enum PointsTrend {
            increase, decrease
        }
    }

    /**
     * 会员信息变动记录
     */
    class LoyaltyMemberChangeRecords {
        public static String API_NAME = "LoyaltyMemberChangeRecordsObj";
        public static String PROGRAM_ID = "program_id";
        public static String MEMBER_ID = "member_id";
        public static String MEMBER_BEHAVIOR = "member_behavior";
        public static String MEMBER_POINTS_DETAIL_ID = "member_points_detail_id";
        public static String POINT_POOL_ID = "point_pool_id";
        public static String LOY_CHANGE_VALUE = "loy_change_value";
        public static String LOY_CHANGE_TYPE = "loy_change_type";
        public static String LOY_CHANGE_TIER_BEFORE = "loy_change_tier_before";
        public static String LOY_CHANGE_TIER_AFTER = "loy_change_tier_after";
        public static String IS_FALLBACK = "is_fallback";
        public static String OPERATOR = "operator";
        public static String INCENTIVE_POLICY_ID = "incentive_policy_id";
        public static String INCENTIVE_POLICY_RULE_ID = "incentive_policy_rule_id";
        public static String TRANSACTION_EVENT = "transaction_event";
        public static String SOURCE_API_NAME = "source_api_name";
        public static String SOURCE_ID = "source_id";

        /**
         * 转入积分的目标会员ID
         */
        public static String TRANSFER_INTO_MEMBER = "transfer_into_member";
        /**
         * 转入积分明细ID
         */
        public static String TRANSFER_INTO_POINTS_DETAIL = "transfer_into_points_detail";
        /**
         * 转出积分的目标会员ID
         */
        public static String TRANSFER_OUT_MEMBER = "transfer_out_member";
        /**
         * 转出积分明细ID
         */
        public static String TRANSFER_OUT_POINTS_DETAIL = "transfer_out_points_detail";
    }

}
