package com.facishare.crm.sfa.prm.model;

import com.facishare.paas.appframework.common.service.dto.InternationalItem;
import lombok.*;

import java.util.UUID;

/**
 * <AUTHOR>
 * @time 2024-06-29 17:28
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CrmNoticeMessageContent extends CommonMessageContent {
    @Builder.Default
    private int type = 92;
    @Builder.Default
    private String sourceId = UUID.randomUUID().toString().replace("-", "");
    @Builder.Default
    private boolean remindSender = false;
    @Builder.Default
    private String title = "CRM Notice";
    private InternationalItem i18nTitle;
    private InternationalItem i18nContent;
    private String appId;
}
