package com.facishare.crm.sfa.lto.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.privilege.swagger.ApiException;
import com.facishare.crm.privilege.swagger.model.AuthContext;
import com.facishare.crm.privilege.swagger.model.BaseResult;
import com.facishare.crm.privilege.swagger.model.CreateFuncArg;
import com.facishare.crm.privilege.swagger.model.FunctionPojo;
import com.facishare.crm.privilege.util.SwaggerApiUtil;
import com.facishare.crm.sfa.lto.business.models.CreateRuleModel;
import com.facishare.crm.sfa.lto.business.models.DescribeChangeData;
import com.facishare.crm.sfa.lto.business.models.PlugInitCreateModels;
import com.facishare.crm.sfa.lto.utils.ObjectDataUtil;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.service.ObjectDesignerService;
import com.facishare.paas.appframework.core.predef.service.ObjectDomainPluginService;
import com.facishare.paas.appframework.core.predef.service.ObjectValueMappingService;
import com.facishare.paas.appframework.core.predef.service.dto.domain.CreateOrUpdatePluginInstance;
import com.facishare.paas.appframework.core.predef.service.dto.domain.FindPluginInstanceDetail;
import com.facishare.paas.appframework.core.predef.service.dto.objectDescribe.AddDescribeCustomField;
import com.facishare.paas.appframework.core.predef.service.dto.objectMapping.CreateRule;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.repository.model.DomainPluginInstance;
import com.facishare.paas.appframework.metadata.repository.model.DomainPluginParam;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.util.IdUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR> lik  business_circles_plug
 * @date : 2022/12/5 22:05
 */
@Component
@Slf4j
public class BusinessCirclesPlugService implements PlugInitServer{


    @Autowired
    private SpecialTableMapper specialTableMapper;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private ObjectValueMappingService objectValueMappingService;
    @Autowired
    private ObjectDomainPluginService objectDomainPluginService;
    @Autowired
    private IObjectDescribeService iObjectDescribeService;

    @Autowired
    private ObjectDesignerService objectDesignerService;

    @Autowired
    private ServiceFacade serviceFacade;

    //插件定义FIELD_MAPPING中字段的标识
    /** 工商扩展*/
    public static final String INDUSTRY_EXT_FIELD_MAPPING_PREFIX = "industryExtFieldApiName";
    /** 工商来源*/
    public static final String BIZ_REG_NAME_FIELD_MAPPING_PREFIX = "bizRegNameFieldApiName";

    //自定义对象新增两个自定义字段的ApiName
    /** 工商扩展*/
    public static final String CUSTOM_OBJ_INDUSTRY_EXT_FIELD_API_NAME = "field_industry_ext__c";
    /** 工商来源*/
    public static final String CUSTOM_OBJ_BIZ_REG_NAME_FIELD_API_NAME = "field_biz_reg_name__c";

    @Override
    public void execute(PlugInitCreateModels.TaskArg arg) {
        if(!arg.getActionCode().equals("Add")){
            return;
        }
        IObjectDescribe describe = describeLogicService.findObject(arg.getTenantId(),arg.getObjectApiName());
        if(ObjectUtils.isEmpty(describe)){
            log.error("BusinessCirclesPlugService.execute error describe is null arg:{}",JSONObject.toJSONString(arg));
            return;
        }

        ServiceContext serviceContext = new ServiceContext(RequestContext.builder().tenantId(arg.getTenantId())
                .user(new User(arg.getTenantId(), "-10000")).build(), null, null);
        FindPluginInstanceDetail.Arg plugArg = new FindPluginInstanceDetail.Arg();
        plugArg.setId(arg.getId());
        FindPluginInstanceDetail.Result result = objectDomainPluginService.findPluginInstanceDetail(plugArg,serviceContext);
        if(ObjectUtils.isEmpty(result) || ObjectUtils.isEmpty(result.getPluginInstance())){
            log.error("BusinessCirclesPlugService.findPluginInstanceDetail is null arg:{},plugArg:{}",JSONObject.toJSONString(arg),JSONObject.toJSONString(plugArg));
            return;
        }

        /**
         * 自定义对象应用了工商插件之后
         * 1、初始化功能权限
         * 2、预置工商回填映射关系
         * 3、新增两个字段“工商扩展”、“工商注册”
         * 4、将工商回填映射关系的ApiName回填到插件中
         */
        String objectName = arg.getObjectApiName();
        if(objectName.length()>15){
            objectName = objectName.substring(0,8)+ObjectDataUtil.safeGetRandomInteger(1000);
        }
        String mappingApiName = String.format("button_bizqueryobj2%s__c",objectName);
        DomainPluginInstance domainPluginInstance =result.getPluginInstance();
        try {
            //1、初始化功能权限
            initFuncOfCustomObject(arg);
            //2、预置映射字段
            initMappingOfCustomObject(arg,describe,mappingApiName,serviceContext,domainPluginInstance,objectName);
            //3、新增两个字段“工商扩展”、“工商注册”
            initAddFieldOfCustomObject(arg,domainPluginInstance,serviceContext);
            //4\将工商回填映射关系的ApiName回填到插件中
            updatePlugOfParam(arg,domainPluginInstance,mappingApiName,serviceContext);
        }catch (Exception e){
            log.error("initFuncOfCustomObject error arg:{},e", JSONObject.toJSONString(arg),e);
        }
    }

    private void initFuncOfCustomObject(PlugInitCreateModels.TaskArg arg) throws ApiException {
        String findSql = String.format(" SELECT tenant_id,func_code from fc_func where tenant_id='%s' AND func_code in ('AccountObj||EyeCommercialInforQuery','AccountObj||DnbCommercialInforQuery','AccountObj||QichachaCommercialInforQuery','AccountObj||BasicCommercialInforQuery') AND is_deleted = false",arg.getTenantId());
        List<Map> resultFind =  specialTableMapper.setTenantId(arg.getTenantId()).findBySql(findSql);
        if(CollectionUtils.isEmpty(resultFind)){
            return;
        }
        try {

            Map<String, String> funCodeMap = new HashMap<>();
            resultFind.stream().forEach(result->{
                if(result.get("func_code").toString().contains("EyeCommercialInforQuery")){
                    funCodeMap.put(arg.getObjectApiName()+"||EyeCommercialInforQuery", "天眼查工商查询");
                }else if(result.get("func_code").toString().contains("DnbCommercialInforQuery")){
                    funCodeMap.put(arg.getObjectApiName()+"||DnbCommercialInforQuery", "邓白氏工商查询");
                }else if(result.get("func_code").toString().contains("QichachaCommercialInforQuery")){
                    funCodeMap.put(arg.getObjectApiName()+"||QichachaCommercialInforQuery", "企查查工商查询");
                }else if(result.get("func_code").toString().contains("BasicCommercialInforQuery")){
                    funCodeMap.put(arg.getObjectApiName()+"||BasicCommercialInforQuery", "工商查询");
                }else{
                    log.error("initFuncOfCustomObject error arg:{}",JSON.toJSONString(arg));
                }
            });
            String tenantId = arg.getTenantId();
            CreateFuncArg createFuncArg = new CreateFuncArg();
            AuthContext authContext = new AuthContext();
            authContext.setTenantId(tenantId);
            authContext.setAppId("CRM");
            authContext.setUserId("1000");

            List<FunctionPojo> functionPojoList1 = Lists.newArrayList();
            createFuncArg.setAuthContext(authContext);
            createFuncArg.setFunctionPojoList(functionPojoList1);

            for (Map.Entry<String, String> vo : funCodeMap.entrySet()) {
                FunctionPojo functionPojo = new FunctionPojo();
                functionPojo.setAppId("CRM");
                functionPojo.setFuncCode(vo.getKey());
                functionPojo.setFuncName(vo.getValue());
                functionPojo.setFuncType(1);
                functionPojo.setIsEnabled(true);
                functionPojo.setParentCode("00000000000000000000000000000000");
                functionPojo.setTenantId(tenantId);
                functionPojoList1.add(functionPojo);
            }
            BaseResult baseResult = SwaggerApiUtil.getFuncPermissionApi().addFunc(createFuncArg);
            if (baseResult.getSuccess()) {
                for (Map.Entry<String, String> vo : funCodeMap.entrySet()) {

                    StringBuilder funcAccessSqlValues = new StringBuilder();
                    funcAccessSqlValues.append("INSERT INTO fc_func_access\n" +
                            "(id, tenant_id, app_id, role_code, func_code, created_by, create_time,last_modified_by, last_modified_time, is_deleted)" +
                            " values ");
                    funcAccessSqlValues.append("(");
                    funcAccessSqlValues.append("'" + IdUtil.generateId() + "',");
                    funcAccessSqlValues.append("'" + tenantId + "',");
                    funcAccessSqlValues.append("'CRM',");
                    funcAccessSqlValues.append("'00000000000000000000000000000006',");
                    funcAccessSqlValues.append("'"+vo.getKey()+"',");
                    funcAccessSqlValues.append("'-10000',");
                    funcAccessSqlValues.append(System.currentTimeMillis() + ",");
                    funcAccessSqlValues.append("'-10000',");
                    funcAccessSqlValues.append(System.currentTimeMillis() + ",");
                    funcAccessSqlValues.append("'f')");
                    String insertValues = funcAccessSqlValues.toString();
                    specialTableMapper.setTenantId(tenantId).insertBySql(insertValues);
                }
                log.info("initFuncOfCustomObject finish arg:{}", JSONObject.toJSONString(arg));
                return;
            }
            log.error("initFuncOfCustomObject error arg:{}", JSONObject.toJSONString(arg));
        }catch (Exception e){
            log.error("BusinessCirclesPlugService.initFuncOfCustomObject error arg:{},e",JSONObject.toJSONString(arg),e);
        }

    }

    private void initMappingOfCustomObject(PlugInitCreateModels.TaskArg arg,IObjectDescribe describe,String mappingApiName,ServiceContext serviceContext,DomainPluginInstance domainPluginInstance,String objectName){


        String fieldApiName = domainPluginInstance.getPluginParam().getFieldMapping().get("fieldApiName");
        String mappingString = "{\n" +
                "  \"describe_api_name\":\"BizQueryObj\",\n" +
                "  \"rule_list\":[{\n" +
                "    \"rule_api_name\":\"rule_bizqueryobj2%s__c\",\n" +
                "    \"rule_name\":\"工商信息回填%s字段映射规则\",\n" +
                "    \"source_api_name\":\"BizQueryObj\",\n" +
                "    \"target_api_name\":\"%s\",\n" +
                "    \"master_rule_api_name\":\"\",\n" +
                "    \"master_api_name\":\"\",\n" +
                "    \"md_type\":0,\n" +
                "    \"package\":\"CRM\",\n" +
                "    \"define_type\":\"system\",\n" +
                "    \"field_mapping\":[{\n" +
                "      \"source_field_api_name\":\"Name\",\n" +
                "      \"target_field_api_name\":\"%s\"\n" +
                "    }\n" +
                "    ]\n" +
                "  }],\n" +
                "  \"button\":{\n" +
                "    \"api_name\":\"%s\",\n" +
                "    \"label\":\"工商信息回填%s字段映射规则\",\n" +
                "    \"description\":\"\",\n" +
                "    \"describe_api_name\":\"BizQueryObj\",\n" +
                "    \"use_pages\":\"create\",\n" +
                "    \"button_type\":\"convert\",\n" +
                "    \"is_active\":true\n" +
                "  }\n" +
                "}\n";
        try {
            mappingString = String.format(mappingString,objectName,
                    objectName,
                    arg.getObjectApiName(),
                    fieldApiName,
                    mappingApiName,
                    objectName);
            CreateRuleModel.Arg mappingArg = JSON.parseObject(mappingString, CreateRuleModel.Arg.class);

            CreateRule.Arg arg1 = new CreateRule.Arg();
            BeanUtils.copyProperties(mappingArg,arg1);

            CreateRule.Result result = objectValueMappingService.createRule(arg1,serviceContext);

        }catch (Exception e){
            log.error("BusinessCirclesPlugService.initMappingOfCustomObject error arg:{},e",JSONObject.toJSONString(arg),e);
        }
    }

    private void initFieldOfCustomObject(PlugInitCreateModels.TaskArg arg,IObjectDescribe describe){

        String changeJson = "{\n" +
                "  \"object_api_name\": \"%s\",\n" +
                "  \"delete_fields\": [],\n" +
                "  \"add_fields\": [\n" +
                "    {\n" +
                "      \"industry_ext\": {\n" +
                "        \"pattern\": \"\",\n" +
                "        \"description\": \"工商扩展\",\n" +
                "        \"is_unique\": false,\n" +
                "        \"type\": \"long_text\",\n" +
                "        \"is_required\": false,\n" +
                "        \"define_type\": \"system\",\n" +
                "        \"is_single\": false,\n" +
                "        \"max_length\": 2000,\n" +
                "        \"is_index\": true,\n" +
                "        \"is_active\": true,\n" +
                "        \"is_encrypted\": false,\n" +
                "        \"label\": \"工商扩展\",\n" +
                "        \"is_need_convert\": false,\n" +
                "        \"api_name\": \"industry_ext\",\n" +
                "        \"is_index_field\": false,\n" +
                "        \"help_text\": \"工商扩展\",\n" +
                "        \"status\": \"released\"\n" +
                "      },\n" +
                "      \"biz_reg_name\": {\n" +
                "        \"description\": \"工商注册\",\n" +
                "        \"is_unique\": false,\n" +
                "        \"type\": \"true_or_false\",\n" +
                "        \"is_required\": false,\n" +
                "        \"options\": [{\n" +
                "          \"label\": \"否\",\n" +
                "          \"value\": false\n" +
                "        },\n" +
                "          {\n" +
                "            \"label\": \"是\",\n" +
                "            \"value\": true\n" +
                "          }\n" +
                "        ],\n" +
                "        \"define_type\": \"package\",\n" +
                "        \"is_single\": false,\n" +
                "        \"index_name\": \"b_1\",\n" +
                "        \"is_index\": true,\n" +
                "        \"is_active\": true,\n" +
                "        \"is_encrypted\": false,\n" +
                "        \"default_value\": false,\n" +
                "        \"label\": \"工商注册\",\n" +
                "        \"api_name\": \"biz_reg_name\",\n" +
                "        \"is_index_field\": false,\n" +
                "        \"status\": \"new\"\n" +
                "      }\n" +
                "    }\n" +
                "  ],\n" +
                "  \"update_fields\": [\n" +
                "  ]\n" +
                "}";
        try {
            changeJson = String.format(changeJson,arg.getObjectApiName());
            DescribeChangeData changeModel = JSON.parseObject(changeJson, DescribeChangeData.class);

            List<IFieldDescribe> fieldDescribeList = Lists.newArrayList();
            if (!CollectionUtils.isEmpty(changeModel.getAdd_fields())) {
                Map<String, IFieldDescribe> fieldDescribeMap = describe.getFieldDescribeMap();
                for (Map<String, Map> addField : changeModel.getAdd_fields()) {
                    addField.forEach((k, v) -> {
                        IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(v);
                        if (!fieldDescribeMap.containsKey(fieldDescribe.getApiName())) {
                            fieldDescribeList.add(fieldDescribe);
                        }
                    });
                }
            }

            if (CollectionUtils.isNotEmpty(fieldDescribeList)) {
                iObjectDescribeService.updateOrInsertFieldsForOnline(arg.getTenantId(), arg.getObjectApiName(), fieldDescribeList);
            }
        }catch (Exception e){
            log.error("BusinessCirclesPlugService.initFieldOfCustomObject error arg:{},e",JSONObject.toJSONString(arg),e);
        }
    }

    private void updatePlugOfParam(PlugInitCreateModels.TaskArg arg,DomainPluginInstance pluginInstance,String mappingApiName, ServiceContext serviceContext){
        //回填插件
        DomainPluginParam domainPluginParam = pluginInstance.getPluginParam();

        if(ObjectUtils.isNotEmpty(domainPluginParam) && ObjectUtils.isNotEmpty(domainPluginParam.getFieldMapping())
                && ObjectUtils.isNotEmpty(domainPluginParam.getFieldMapping().get(INDUSTRY_EXT_FIELD_MAPPING_PREFIX))){
            log.error("BusinessCirclesPlugService updatePlugOfParam FieldMapping is exist domainPluginParam:{}",JSONObject.toJSONString(domainPluginParam) );
        }else {
            Map<String, String> fieldMapping = domainPluginParam.getFieldMapping();
            fieldMapping.put(INDUSTRY_EXT_FIELD_MAPPING_PREFIX,CUSTOM_OBJ_INDUSTRY_EXT_FIELD_API_NAME);
            fieldMapping.put(BIZ_REG_NAME_FIELD_MAPPING_PREFIX,CUSTOM_OBJ_BIZ_REG_NAME_FIELD_API_NAME);
            domainPluginParam.setFieldMapping(fieldMapping);
            pluginInstance.setPluginParam(domainPluginParam);
        }

       try {
           Map<String, Object> customParam = new HashMap<>();
           domainPluginParam = new DomainPluginParam();
           if(ObjectUtils.isNotEmpty(pluginInstance.getPluginParam())){
               domainPluginParam = pluginInstance.getPluginParam();
           }
           if(ObjectUtils.isNotEmpty(domainPluginParam) && ObjectUtils.isNotEmpty(domainPluginParam.getCustomParam())){
               customParam = pluginInstance.getPluginParam().getCustomParam();
           }
           customParam.put("mapping_rule_api_name",mappingApiName);
           domainPluginParam.setCustomParam(customParam);
           pluginInstance.setPluginParam(domainPluginParam);
           pluginInstance.setRefObjectApiName(arg.getObjectApiName());
           CreateOrUpdatePluginInstance.Arg updateArg = new CreateOrUpdatePluginInstance.Arg();
           updateArg.setPluginInstance(pluginInstance);
           objectDomainPluginService.updatePluginInstance(updateArg,serviceContext);
           log.info("BusinessCirclesPlugService.updatePlugOfParam finish updateArg:{}",JSONObject.toJSONString(updateArg));
       }catch (Exception e){
           log.error("BusinessCirclesPlugService.updatePlugOfParam error domainPluginInstance:{},e",JSONObject.toJSONString(pluginInstance),e);
       }
     }

    public void initAddFieldOfCustomObject(PlugInitCreateModels.TaskArg arg, DomainPluginInstance domainPluginInstance, ServiceContext serviceContext){
        //判断工商字段是否已经有了，如果有了，不做处理
        DomainPluginParam domainPluginParam = domainPluginInstance.getPluginParam();
        if(ObjectUtils.isNotEmpty(domainPluginParam) && ObjectUtils.isNotEmpty(domainPluginParam.getFieldMapping())
                && ObjectUtils.isNotEmpty(domainPluginParam.getFieldMapping().get(INDUSTRY_EXT_FIELD_MAPPING_PREFIX))){
            return;
        }

        try {
            AddDescribeCustomField.Arg addDieldArg = new AddDescribeCustomField.Arg();
            addDieldArg.setDescribeAPIName(arg.getObjectApiName());
            addDieldArg.setGroup_fields("[]");
            addDieldArg.setField_describe("{\"is_extend\":true,\"type\":\"text\",\"define_type\":\"custom\",\"api_name\":\""+CUSTOM_OBJ_INDUSTRY_EXT_FIELD_API_NAME+"\",\"label\":\"工商扩展\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"status\":\"new\",\"default_value\":\"\",\"default_is_expression\":false,\"default_to_zero\":false,\"max_length\":1000,\"input_mode\":\"\",\"is_show_mask\":false,\"remove_mask_roles\":{}}");
            objectDesignerService.addDescribeCustomField(addDieldArg,serviceContext);
            addDieldArg = new AddDescribeCustomField.Arg();
            addDieldArg.setDescribeAPIName(arg.getObjectApiName());
            addDieldArg.setGroup_fields("[]");
            addDieldArg.setField_describe("{\"is_extend\":true,\"type\":\"true_or_false\",\"define_type\":\"custom\",\"api_name\":\""+CUSTOM_OBJ_BIZ_REG_NAME_FIELD_API_NAME+"\",\"label\":\"工商注册\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"status\":\"new\",\"default_value\":false,\"options\":[{\"label\":\"是\",\"value\":true},{\"label\":\"否\",\"value\":false}]}");
            Map<String,Object> field = new HashMap<>();
            field.put("show_tag",false);
            field.put("api_name",CUSTOM_OBJ_BIZ_REG_NAME_FIELD_API_NAME);
            field.put("type","true_or_false");
            Map<String,Object> fields = new HashMap<>();
            fields.put(CUSTOM_OBJ_BIZ_REG_NAME_FIELD_API_NAME,field);
            Map<String,Object> describeExtra = new HashMap<>();
            describeExtra.put("fields",fields);
            addDieldArg.setDescribeExtra(ObjectDescribeDocument.of(describeExtra));
            objectDesignerService.addDescribeCustomField(addDieldArg,serviceContext);

        }catch (Exception e){
            log.error("BusinessCirclesPlugService.initFieldOfCustomObject error arg:{},e",JSONObject.toJSONString(arg),e);
        }
    }
}
