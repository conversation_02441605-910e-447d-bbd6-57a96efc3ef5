package com.facishare.crm.sfa.prm.rest.proxy;

import com.facishare.crm.sfa.lto.utils.StringUtil;
import com.facishare.crm.sfa.prm.rest.client.EmailTemplateClient;
import com.facishare.crm.sfa.prm.rest.model.TemplateModel;
import com.facishare.paas.appframework.core.model.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-03-17
 * ============================================================
 */
@Service
@Slf4j
public class EmailTemplateServiceProxy {
    @Resource
    private EmailTemplateClient emailTemplateClient;

    public TemplateModel.Result fetchEmailFromTemplate(User user, String templateId, String objectDataId) {
        if (StringUtils.isAnyBlank(templateId, objectDataId)) {
            log.warn("Template id or object data id is blank, tenantId:{}", user.getTenantId());
            return TemplateModel.Result.builder().build();
        }
        TemplateModel.Arg arg = new TemplateModel.Arg();
        arg.setTemplateId(templateId);
        arg.setObjectId(objectDataId);
        TemplateModel.Result result = emailTemplateClient.fetchEmailInfoFromTemplate(user.getTenantId(), user.getUserId(), arg);
        if (result == null) {
            log.warn("TemplateModel.result is null, tenantId:{}", user.getTenantId());
            return TemplateModel.Result.builder().build();
        }
        if (StringUtils.isAnyBlank(result.getTemplate(), result.getSubject())) {
            log.warn("邮件消息：邮件 模版id 或主题为空！, tenant:{}, objectDataId:{}，templateId：{}, subject:{}",
                    user.getTenantId(), objectDataId, result.getTemplate(), result.getSubject());
        }
        return result;
    }
}
