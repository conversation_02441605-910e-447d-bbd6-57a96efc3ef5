package com.facishare.crm.sfa.lto.exception;

import com.facishare.paas.I18N;

import static com.facishare.crm.sfa.lto.utils.SfaLtoI18NKeyUtil.SFA_SYS_ERROR_MSG;
import static com.facishare.paas.appframework.core.i18n.I18NKey.UNSUPPORTED_OPERATION;

public class ExceptionUtil {
    public static void throwCommonBusinessException() {
        throw new SfaLtoBusinessException(5000, I18N.text(SFA_SYS_ERROR_MSG));
    }

    public static void throwUnSupportedOperationException() {
        throw new SfaLtoBusinessException(5000, I18N.text(UNSUPPORTED_OPERATION));
    }

}
