package com.facishare.crm.sfa.lto.qywx.proxy.models;

import lombok.Data;

@Data
public class QywxEmployeeMappingInfo {

	private String source;    //合作方渠道：qywx
	/**
	 * 应用ID.默认CRM应用ID
	 */
	private String appId;
	private String fsAccount; //员工的纷享账号 如E.56305.1000
	private String outAccount;//员工的企业微信账号, qywx是userId.
	private String outEa;     //员工的企业id
	private int status;      //0-正常 1-停用
	private String isvAccount;//员工的明文企业微信的账号

	public boolean isNormal () {
		return this.status == 0;
	}
}
