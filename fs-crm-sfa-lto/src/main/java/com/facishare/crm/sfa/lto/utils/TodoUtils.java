package com.facishare.crm.sfa.lto.utils;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.lto.enums.LeadsBizStatusEnum;
import com.facishare.crm.sfa.lto.objectpool.LeadsPoolServiceImpl;
import com.facishare.crm.sfa.lto.todo.enums.LtoLogisticsStatusEnum;
import com.facishare.crm.sfa.lto.todo.model.LtoFindEmployeeIdsByEmployeeAndRolesModel;
import com.facishare.crm.sfa.lto.todo.model.LtoGetTobeConfirmOpportunityIDsModel;
import com.facishare.crm.sfa.lto.todo.model.LtoSaleActionModel;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.privilege.util.PrivilegeConstants;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.util.SpringUtil;
import com.facishare.privilege.api.RoleUserDepartmentServiceClient;
import com.facishare.privilege.api.module.PrivilegeContext;
import com.facishare.privilege.api.module.roleuserdepartment.FindEmployeeIdsByEmployeeAndRolesBO;
import com.facishare.rest.core.exception.RestProxyInvokeException;
import com.fxiaoke.common.SqlEscaper;
import com.fxiaoke.helper.CollectionHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class TodoUtils {
    private static final ConfigService configService = SpringUtil.getContext().getBean(ConfigService.class);
    private static final LeadsPoolServiceImpl poolService = SpringUtil.getContext().getBean(LeadsPoolServiceImpl.class);
    private static final ServiceFacade serviceFacade = SpringUtil.getContext().getBean(ServiceFacade.class);
    private static final ObjectDataServiceImpl objectDataService = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);


    public static void handleAccountSearchQuery(SearchTemplateQuery query, User user) {
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, "life_status", ObjectLifeStatus.UNDER_REVIEW.getCode());
        SearchUtil.fillFilterEq(filters, "filling_checker_id", user.getUpstreamOwnerIdOrUserId());
        if (!isOpenAccountFillingCheck(user)) {
            IFilter filter = new Filter();
            filter.setFieldName("name");
            filter.setFieldValues(Lists.newArrayList());
            filter.setOperator(Operator.IS);
            filters.add(filter);
        }
        query.setFilters(filters);
        query.setPermissionType(0);
    }

    public static boolean isOpenAccountFillingCheck(User user) {
        String configValue = configService.findTenantConfig(user, "3");
        if(ObjectUtils.isEmpty(configValue)){
            return false;
        }
        return "1".equals(configValue);
    }


    public static void handleLeadsSearchQuery(SearchTemplateQuery query, User user, Integer sessionBOCItemKey){
        boolean isCrmAdmin = isCrmAdmin(user);
        if (sessionBOCItemKey == 401) {
            List<IObjectData> poolDataList = poolService.getObjectPoolAdminByUser(user);
            List<IObjectData> needNotifyPoolList = poolDataList.stream()
                    .filter(x -> ObjectDataUtil.getBooleanValue(x, "is_new_to_notify_admin", false)).collect(Collectors.toList());

            List<IFilter> filters = Lists.newArrayList();
            if (org.springframework.util.CollectionUtils.isEmpty(needNotifyPoolList)) {
                SearchUtil.fillFilterEq(filters, "name", String.format("%s%s", "LeadsObj-", serviceFacade.generateId()));
                query.setFilters(filters);
                return;
            }
            List<String> poolIds = needNotifyPoolList.stream().map(x -> x.getId()).collect(Collectors.toList());
            SearchUtil.fillFilterEq(filters, "biz_status", LeadsBizStatusEnum.UN_ASSIGNED.getValue());
            SearchUtil.fillFilterIn(filters, "leads_pool_id", poolIds);
            query.setFilters(filters);
        }
        if (sessionBOCItemKey == 402) {
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, "biz_status", LeadsBizStatusEnum.UN_PROCESSED.getValue());
            if (GrayUtil.isGrayLeadsTodoListFilter(user.getTenantId())) {
                SearchUtil.fillFilterNotEq(filters, "out_resources", "partner");
            }
            if (user.isOutUser()) {
                SearchUtil.fillFilterEq(filters, "out_owner", Lists.newArrayList(user.getOutUserId()));
            } else {
                SearchUtil.fillFilterEq(filters, "owner", Lists.newArrayList(user.getUpstreamOwnerIdOrUserId()));
            }
            query.setFilters(filters);
        }
        query.setDataRightsParameter(null);
        if (!user.isSupperAdmin() && !isCrmAdmin) {
            List<IFilter> filters = query.getFilters();
            SearchUtil.fillFilterNotEq(filters, "life_status", ObjectLifeStatus.INVALID.getCode());
        }
    }

    public static boolean isCrmAdmin(User user) {
        if (!user.getIsCrmAdmin().isPresent()) {
            user.setIsCrmAdmin(Optional.of(serviceFacade.isAdmin(user)));
        }
        return user.getIsCrmAdmin().get();
    }




    public static List<IObjectData> getTobeConfirmOpportunityData(Integer status, User user,String apiName) {
        LtoGetTobeConfirmOpportunityIDsModel.Arg getTobeConfirmOpportunityIDsModel = new LtoGetTobeConfirmOpportunityIDsModel.Arg();
        getTobeConfirmOpportunityIDsModel.setEmployeeId(Integer.valueOf(user.getUpstreamOwnerIdOrUserId()));
        getTobeConfirmOpportunityIDsModel.setStatus(status);
        List<IObjectData> dataList = new ArrayList<>();
        try {
            log.info("GetTobeConfirmOpportunityIDsModel>arg={}" + JsonUtil.toJsonWithNullValues(getTobeConfirmOpportunityIDsModel));
            List<String> tobeConfirmIds = getTobeConfirmOpportunityIds(user);
            if (!tobeConfirmIds.isEmpty()) {
                List<IObjectData> objectDataList = serviceFacade.findObjectDataByIdsIgnoreFormula(user.getTenantId(), tobeConfirmIds, apiName);
                dataList = objectDataList.stream()
                        .filter(r -> !r.isDeleted()).collect(Collectors.toList());
            }

            return dataList;
        } catch (RestProxyInvokeException e) {
            log.error("RestProxyInvokeException error {}", getTobeConfirmOpportunityIDsModel,e);
            throw new RestProxyInvokeException(e);
        }
    }

    public static List<String> getTobeConfirmOpportunityIds(User user) {
        List<String> result = new ArrayList<>();
        List<LtoSaleActionModel.OpportunitySaleActionConfirm> opportunitySaleActionConfirms
                = getTobeConfirmOpportunityIDs(user);
        if (CollectionUtils.empty(opportunitySaleActionConfirms)) {
            return result;
        }
        List<String> opportunityIds = opportunitySaleActionConfirms.stream()
                .map(LtoSaleActionModel.OpportunitySaleActionConfirm::getOpportunity_id)
                .collect(Collectors.toList());
        if (CollectionUtils.empty(opportunityIds)) {
            return result;
        }
        List<IObjectData> opportunitys = serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), opportunityIds
                , Utils.OPPORTUNITY_API_NAME);
        if (CollectionUtils.empty(opportunitys)) {
            return result;
        }
        return opportunitys.stream()
                .map(IObjectData::getId)
                .collect(Collectors.toList());
    }
    public static List<LtoSaleActionModel.OpportunitySaleActionConfirm> getTobeConfirmOpportunityIDs(User user) {
        LtoSaleActionModel.OpportunitySaleActionConfirm opportunitySaleActionConfirm = null;
        try {
            String querySql = String.format("select * from opportunity_sale_action_confirm " +
                            "where ei=%s and employee_id = %s and status = %s",
                    SqlEscaper.pg_escape(user.getTenantId()), SqlEscaper.pg_escape(user.getUserId())
                    , SqlEscaper.pg_escape("1"));

            List<Map> maps = objectDataService.findBySql(user.getTenantId(), querySql);
            List<LtoSaleActionModel.OpportunitySaleActionConfirm> opportunitySaleActionConfirms
                    = buildOpportunitySaleActionConfirmModel(maps);
            return opportunitySaleActionConfirms;
        } catch (MetadataServiceException e) {
            throw new MetaDataBusinessException(e.getMessage());
        }
    }

    private static List<LtoSaleActionModel.OpportunitySaleActionConfirm> buildOpportunitySaleActionConfirmModel(List<Map> maps) {
        List<LtoSaleActionModel.OpportunitySaleActionConfirm> opportunitySaleActionConfirms = new ArrayList<>();
        if (CollectionUtils.notEmpty(maps)) {
            maps.forEach(map -> {
                String opportunity_sale_action_confirm_id = map.get("opportunity_sale_action_confirm_id").toString();
                String sale_action_stage_id = map.get("sale_action_stage_id").toString();
                String opportunity_id = map.get("opportunity_id").toString();
                String sale_action_id = map.get("sale_action_id").toString();
                Integer employee_id = 0;
                if (!Objects.isNull(map.get("employee_id"))) {
                    employee_id = Integer.parseInt(map.get("employee_id").toString());
                }
                Integer status = 0;
                if (!Objects.isNull(map.get("status"))) {
                    status = Integer.parseInt(map.get("status").toString());
                }
                Date update_time = DateUtil.getDateNow();
                if (!Objects.isNull(map.get("update_time"))) {
                    update_time = DateUtil.formatStrToDate(map.get("update_time").toString());
                }
                Boolean is_unread = false;
                if (!Objects.isNull(map.get("is_unread"))) {
                    is_unread = Boolean.parseBoolean(map.get("is_unread").toString());
                }
                Integer creator_id = 0;
                if (!Objects.isNull(map.get("creator_id"))) {
                    creator_id = Integer.parseInt(map.get("creator_id").toString());
                }
                String target_sale_action_stage_id = Strings.EMPTY;
                if (!Objects.isNull(map.get("target_sale_action_stage_id"))) {
                    target_sale_action_stage_id = map.get("target_sale_action_stage_id").toString();
                }
                Integer target_opportunity_status = 0;
                if (!Objects.isNull(map.get("target_opportunity_status"))) {
                    target_opportunity_status = Integer.parseInt(map.get("target_opportunity_status").toString());
                }
                String data_id = Strings.EMPTY;
                if (!Objects.isNull(map.get("data_id"))) {
                    data_id = map.get("data_id").toString();
                }
                Integer lose_type = 0;
                if (!Objects.isNull(map.get("lose_type"))) {
                    lose_type = Integer.parseInt(map.get("lose_type").toString());
                }
                LtoSaleActionModel.OpportunitySaleActionConfirm opportunitySaleActionConfirm
                        = LtoSaleActionModel.OpportunitySaleActionConfirm.builder()
                        .opportunity_sale_action_confirm_id(opportunity_sale_action_confirm_id)
                        .sale_action_stage_id(sale_action_stage_id)
                        .opportunity_id(opportunity_id)
                        .sale_action_id(sale_action_id)
                        .data_id(data_id)
                        .update_time(update_time)
                        .is_unread(is_unread)
                        .creator_id(creator_id)
                        .target_sale_action_stage_id(target_sale_action_stage_id)
                        .target_opportunity_status(target_opportunity_status)
                        .lose_type(lose_type)
                        .employee_id(employee_id)
                        .status(status).build();

                opportunitySaleActionConfirms.add(opportunitySaleActionConfirm);
            });
        }
        return opportunitySaleActionConfirms;
    }




    private static final RoleUserDepartmentServiceClient roleUserDepartmentServiceClient = SpringUtil.getContext().getBean(RoleUserDepartmentServiceClient.class);


    public static void handleSearchQuery(SearchTemplateQuery query, User user) {
        List<String> owners = getObjectOwnerIdList(user);
        List<IFilter> filters = Lists.newArrayList();
        IFilter filter = new Filter();
        filter.setFieldName("life_status");
        List<String> fieldValues = Lists.newArrayList();
        filter.setFieldValues(fieldValues);
        fieldValues.add(ObjectLifeStatus.UNDER_REVIEW.getCode());

        filter.setOperator(Operator.IN);
        if (owners.isEmpty()) {
            owners.add("");
        }
        filters.add(filter);
        IFilter filterIsFixedFlow = new Filter();
        filterIsFixedFlow.setFieldName("is_fixed_flow");
        List<String> filedValueIsFixedFlow = Lists.newArrayList();
        filedValueIsFixedFlow.add("false");
        filterIsFixedFlow.setFieldValues(filedValueIsFixedFlow);
        filterIsFixedFlow.setOperator(Operator.EQ);
        filters.add(filterIsFixedFlow);
        SearchUtil.fillFilterIn(filters, "owner", new ArrayList<>(owners));
        query.setFilters(filters);
    }

    public static List<String> getObjectOwnerIdList(User user){
        List<String> rst = Lists.newArrayList();
        if (com.google.common.base.Strings.isNullOrEmpty(user.getUpstreamOwnerIdOrUserId())) {
            return rst;
        }
        List<Integer> employeeIds = getViewScopeEmployeeIDs(user,
                Integer.parseInt(user.getUpstreamOwnerIdOrUserId()), PrivilegeConstants.INVOICE_FINANCE_CODE);
        if(CollectionUtils.notEmpty(employeeIds)){
            rst.addAll(employeeIds.stream().map(Objects::toString).collect(Collectors.toList()));
        }
        return rst;
    }

    public static List<Integer> getViewScopeEmployeeIDs(User user,int employeeId,String roleCode){
        List<Integer> result = Lists.newArrayList();
        Map<String, List<Integer>> map = getViewScopeEmployeeIDs(user, employeeId, Lists.newArrayList(roleCode));
        if (CollectionUtils.notEmpty(map)) {
            return map.getOrDefault(roleCode, Lists.newArrayList());
        }
        return result;
    }

    public static Map<String, List<Integer>> getViewScopeEmployeeIDs(User user, int employeeId, List<String> roleCodes){
        Map<String, List<Integer>> result = Maps.newHashMap();
        LtoFindEmployeeIdsByEmployeeAndRolesModel.Arg arg = LtoFindEmployeeIdsByEmployeeAndRolesModel.Arg.builder()
                .employeeId(String.valueOf(employeeId))
                .roleIds(roleCodes)
                .build();
        RequestContext requestContext = RequestContext.builder().user(user).tenantId(user.getTenantId()).build();
        ServiceContext serviceContext = new ServiceContext(requestContext,"ApprovalPermission", "listEmployeeIdsByEmployeeAndRoleIds");
        Optional<LtoFindEmployeeIdsByEmployeeAndRolesModel.Result> scopeResultOpt = Optional.ofNullable(findDepartmentIdsByEmployeeAndRoleIds(serviceContext, arg));
        scopeResultOpt.map(LtoFindEmployeeIdsByEmployeeAndRolesModel.Result::getRoleIdEmployeeIdsMap).ifPresent(dataMap ->{
            for (Map.Entry<String, List<String>> entry : dataMap.entrySet()) {
                if (CollectionUtils.empty(entry.getValue())) {
                    result.put(entry.getKey(), Lists.newArrayList());
                } else {
                    result.put(entry.getKey(), entry.getValue().stream().map(Integer::valueOf).collect(Collectors.toList()));
                }
            }
        });
        return result;
    }

    public static LtoFindEmployeeIdsByEmployeeAndRolesModel.Result findDepartmentIdsByEmployeeAndRoleIds(ServiceContext context,
                                                                                                         LtoFindEmployeeIdsByEmployeeAndRolesModel.Arg arg) {
        String tenantId = context.getTenantId();
        String userId = context.getUser().getUpstreamOwnerIdOrUserId();
        User user = new User(tenantId, userId);
        if (CollectionHelper.isEmpty(arg.getRoleIds())) {
            return LtoFindEmployeeIdsByEmployeeAndRolesModel.Result.empty();
        }
        LtoFindEmployeeIdsByEmployeeAndRolesModel.Result result = LtoFindEmployeeIdsByEmployeeAndRolesModel.Result.empty();
//        if (GrayUtil.isApprovalDepartmentMigration(user.getTenantId())) {
        PrivilegeContext privilegeContext = PrivilegeContext.builder()
                .appId("CRM")
                .tenantId(Integer.valueOf(user.getTenantId()))
                .operatorId(Integer.valueOf(user.getUserId()))
                .build();
        FindEmployeeIdsByEmployeeAndRolesBO.Arg newArg = new FindEmployeeIdsByEmployeeAndRolesBO.Arg();
        newArg.setEmployeeId(arg.getEmployeeId());
        newArg.setRoleCodes(arg.getRoleIds());
        newArg.setBizType("ApprovalDepartment");
        com.facishare.privilege.api.module.roleuserdepartment.FindEmployeeIdsByEmployeeAndRolesBO.Result employeeResult = roleUserDepartmentServiceClient.listEmployeeIdsByEmployeeAndRoleIds(privilegeContext, newArg);
        if (null != employeeResult && CollectionHelper.isNotEmpty(employeeResult.getRoleCodeEmployeeIdsMap())) {
            employeeResult.getRoleCodeEmployeeIdsMap().forEach((k, v) -> result.getRoleIdEmployeeIdsMap().put(k, v));
        }
        return result;
//        }
    }

    public static List<IFilter> getDeliveryFilters(User user, boolean hasDeliveryRole) {
        List<IFilter> filters = Lists.newArrayList();
        if (hasDeliveryRole) {
            IFilter lifeStatusFilter = new Filter();
            lifeStatusFilter.setFieldName("life_status");
            List<String> fieldValues = Lists.newArrayList();
            fieldValues.add(ObjectLifeStatus.NORMAL.getCode());
            lifeStatusFilter.setFieldValues(fieldValues);
            lifeStatusFilter.setOperator(Operator.EQ);
            filters.add(lifeStatusFilter);
            IFilter logisticsStatusFilter = new Filter();
            logisticsStatusFilter.setFieldName("logistics_status");
            List<String> logisticsStatusFieldValues = Lists.newArrayList();
            logisticsStatusFieldValues.add(LtoLogisticsStatusEnum.TOBESHIPPED.getStatus());
            logisticsStatusFieldValues.add(LtoLogisticsStatusEnum.PARTIALSHIPMENT.getStatus());
            logisticsStatusFieldValues.add(LtoLogisticsStatusEnum.PARTIALCOLLECTION.getStatus());
            logisticsStatusFilter.setFieldValues(logisticsStatusFieldValues);
            logisticsStatusFilter.setOperator(Operator.IN);
            filters.add(logisticsStatusFilter);
            List<String> ownerIdList = getVisibleOwnerIdList(user);
            if (CollectionUtils.empty(ownerIdList)) {
                ownerIdList.add("");
            }
            SearchUtil.fillFilterIn(filters, "owner", Lists.newArrayList(ownerIdList));
        } else {
            IFilter lifeStatusFilter = new Filter();
            lifeStatusFilter.setFieldName("life_status");
            List<String> fieldValues = Lists.newArrayList();
            fieldValues.add("1");
            lifeStatusFilter.setFieldValues(fieldValues);
            lifeStatusFilter.setOperator(Operator.EQ);
            filters.add(lifeStatusFilter);
        }

        return filters;
    }
    public static List<String> getVisibleOwnerIdList(User user) {
        List<String> ownerIdList = Lists.newArrayList();
        if (com.google.common.base.Strings.isNullOrEmpty(user.getUpstreamOwnerIdOrUserId())) {
            return ownerIdList;
        }
        List<Integer> employeeIds = getViewScopeEmployeeIDs(user,
                Integer.parseInt(user.getUpstreamOwnerIdOrUserId()), "00000000000000000000000000000020");
        if (CollectionUtils.notEmpty(employeeIds)) {
            ownerIdList.addAll(employeeIds.stream().map(Objects::toString).collect(Collectors.toList()));
        }
        return ownerIdList;
    }
}
