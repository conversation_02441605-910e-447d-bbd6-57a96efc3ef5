package com.facishare.crm.sfa.lto.integral.core.service;

import java.util.List;

/**
 * 引用服务
 */
public interface ReferenceLogicService {
    /**
     * 批量创建引用
     * @param describeApiName 对象ApiName
     * @param fieldApiNames 字段ApiName
     */
    void batchCreateReferenceWithFields(String tenantId,
                                        String describeApiName, String ruleApiName, List<String> fieldApiNames);

    /**
     * 删除一条引用
     * @param tenantId 租户ID
     * @param describeApiName 对象ApiName
     * @param ruleApiName 规则ApiName
     * @param fieldApiName 字段ApiName
     */
    void deleteReference(String tenantId, String describeApiName, String ruleApiName, String fieldApiName);

    /**
     * 批量删除引用
     * @param tenantId 租户ID
     * @param ruleApiName 规则ApiName
     */
    void deleteReferenceByRuleApiName(String tenantId, String ruleApiName);

}
