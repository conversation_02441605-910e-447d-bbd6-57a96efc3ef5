package com.facishare.crm.sfa.lto.integral.core.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

public interface FindMaterial {
    @Data
    class Arg {
        @JSONField(name = "category_api_name")
        @JsonProperty("category_api_name")
        private String categoryApiName;
    }

    @Data
    class Result {
        @JSONField(name = "behavior_material_list")
        @JsonProperty("behavior_material_list")
        private List<BehaviorInfo.BehaviorMaterial> behaviorMaterialList;
    }
}
