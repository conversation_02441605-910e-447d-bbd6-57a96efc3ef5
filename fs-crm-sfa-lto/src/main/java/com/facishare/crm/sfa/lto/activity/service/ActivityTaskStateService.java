package com.facishare.crm.sfa.lto.activity.service;

import com.facishare.crm.sfa.lto.activity.enums.TaskStatusEnum;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * Activity任务状态存储
 */
@Service
@Slf4j
public class ActivityTaskStateService {

    public static final String ACTIVITY_AI_TASK_API_NAME = "ActivityAiTaskObj";
    public static final String TARGET_API_NAME = "target_api_name";
    public static final String TARGET_DATA_ID = "target_data_id";
    public static final String TASK_STATUS = "task_status";
    public static final String TASK_ID = "task_id";

    @Resource
    private ServiceFacade serviceFacade;

    /**
     * 更新ai任务记录，当记录不存在时，会创建
     *
     * @param tenantId      tenantId
     * @param targetApiName 业务标识，字符串类型，可以是组件的apiName，也可以是自定义的标识
     * @param dataId        数据id
     * @param status        当前数据ai的状态
     * @param taskId        拓展字段，任务id，可以为null
     */
    @Deprecated
    public IObjectData insOrUpdate(String tenantId, String targetApiName, String dataId, TaskStatusEnum status, String taskId) {
        IObjectData objectData = getObjectData(tenantId, targetApiName, dataId, taskId);
        if (ObjectUtils.isEmpty(objectData)) {
            objectData = new ObjectData();
            objectData.set(TARGET_API_NAME, targetApiName);
            objectData.set(TARGET_DATA_ID, dataId);
            objectData.set(TASK_STATUS, status.getValue());
            if (taskId != null) {
                objectData.set(TASK_ID, taskId);
            }
            objectData.setTenantId(tenantId);
            objectData.setDescribeApiName(ACTIVITY_AI_TASK_API_NAME);
            return serviceFacade.saveObjectData(User.systemUser(tenantId), objectData);
        }
        objectData.set(TASK_STATUS, status.getValue());
        return serviceFacade.updateObjectData(User.systemUser(tenantId), objectData);
    }


    /**
     * 更新ai任务记录，当记录不存在时，会创建
     *
     * @param tenantId      tenantId
     * @param           userId       操作人id，字符串类型, 空则认为是系统
     * @param targetApiName 业务标识，字符串类型，可以是组件的apiName，也可以是自定义的标识
     * @param dataId        数据id
     * @param status        当前数据ai的状态
     * @param taskId        拓展字段，任务id，可以为null
     */
    public IObjectData insOrUpdate(String tenantId, String userId, String targetApiName, String dataId, TaskStatusEnum status, String taskId) {
        IObjectData objectData = getObjectData(tenantId, targetApiName, dataId, taskId);
        User user = StringUtils.isBlank(userId) ? User.systemUser(tenantId) : new User(tenantId, userId);
        if (ObjectUtils.isEmpty(objectData)) {
            objectData = new ObjectData();
            objectData.set(TARGET_API_NAME, targetApiName);
            objectData.set(TARGET_DATA_ID, dataId);
            objectData.set(TASK_STATUS, status.getValue());
            if (taskId != null) {
                objectData.set(TASK_ID, taskId);
            }
            objectData.setTenantId(tenantId);
            objectData.setDescribeApiName(ACTIVITY_AI_TASK_API_NAME);
            return serviceFacade.saveObjectData(user, objectData);
        }
        objectData.set(TASK_STATUS, status.getValue());
        return serviceFacade.updateObjectData(user, objectData);
    }

    /**
     * 更新ai任务记录，当记录不存在时，会创建
     *
     * @param tenantId      tenantId
     * @param targetApiName 业务标识，字符串类型，可以是组件的apiName，也可以是自定义的标识
     * @param dataId        数据id
     * @param taskId        拓展字段，任务id，可以为null
     * @return ai任务状态，当状态为空时返回null
     */
    public TaskStatusEnum getState(String tenantId, String targetApiName, String dataId, String taskId) {
        IObjectData objectData = getObjectData(tenantId, targetApiName, dataId, taskId);
        if (ObjectUtils.isEmpty(objectData) || ObjectUtils.isEmpty(objectData.get(TASK_STATUS))) {
            return null;
        }
        return TaskStatusEnum.fromValue(objectData.get(TASK_STATUS, String.class));
    }

    /**
     * 查询任务数据
     *
     * @param tenantId      tenantId
     * @param targetApiName 业务标识，字符串类型，可以是组件的apiName，也可以是自定义的标识
     * @param dataId        数据id
     * @param taskId        拓展字段，任务id，可以为null
     */
    public IObjectData getObjectData(String tenantId, String targetApiName, String dataId, String taskId) {
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, IObjectData.IS_DELETED, "0");
        SearchUtil.fillFilterEq(filters, IObjectData.TENANT_ID, tenantId);
        SearchUtil.fillFilterEq(filters, TARGET_API_NAME, targetApiName);
        SearchUtil.fillFilterEq(filters, TARGET_DATA_ID, dataId);
        if (taskId != null) {
            SearchUtil.fillFilterEq(filters, TASK_ID, taskId);
        }
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setFilters(filters);
        searchTemplateQuery.setNeedReturnQuote(false);
        searchTemplateQuery.setLimit(10);
        searchTemplateQuery.setOffset(0);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(User.systemUser(tenantId), ACTIVITY_AI_TASK_API_NAME, searchTemplateQuery);
        if (ObjectUtils.isEmpty(queryResult) || ObjectUtils.isEmpty(queryResult.getData())) {
            return null;
        }
        return queryResult.getData().get(0);
    }


    /**
     * 根据任务 id 查询任务数据，file_parse 中，MQ 回调只有 jobId(存储在了 taskId中)，所以需要根据 taskId 查询
     *
     * @param tenantId      tenantId
     * @param targetApiName 业务标识，字符串类型，可以是组件的apiName，也可以是自定义的标识
     * @param taskId        拓展字段，任务id，不可以为null
     */
    public IObjectData getObjectData(String tenantId, String targetApiName, String taskId) {
        if (ObjectUtils.isEmpty(taskId)) {
            return null;
        }
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, IObjectData.IS_DELETED, "0");
        SearchUtil.fillFilterEq(filters, IObjectData.TENANT_ID, tenantId);
        SearchUtil.fillFilterEq(filters, TARGET_API_NAME, targetApiName);
        SearchUtil.fillFilterEq(filters, TASK_ID, taskId);
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setFilters(filters);
        searchTemplateQuery.setNeedReturnQuote(false);
        searchTemplateQuery.setLimit(10);
        searchTemplateQuery.setOffset(0);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(User.systemUser(tenantId), ACTIVITY_AI_TASK_API_NAME, searchTemplateQuery);
        if (ObjectUtils.isEmpty(queryResult) || ObjectUtils.isEmpty(queryResult.getData())) {
            return null;
        }
        return queryResult.getData().get(0);
    }

}
