package com.facishare.crm.sfa.lto.qywx.proxy.models;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import lombok.extern.slf4j.Slf4j;

import static com.facishare.crm.sfa.lto.utils.SfaLtoI18NKeyUtil.SFA_WECOM_GATEWAY_SERVICE_ERROR;

@Slf4j
public abstract class BaseQywxResult {
    //0-成功， other-失败
    private String errorCode;
    private String errorMsg;

    public void checkSuccess() {
        if (!"s120050000".equals(errorCode) && !"0".equals(errorCode)) {
            log.error("errorCode:{},errorMsg:{}", errorCode, errorMsg);
            throw new ValidateException(I18N.text(SFA_WECOM_GATEWAY_SERVICE_ERROR));
        }
    }

    public boolean success() {
        return "s120050000".equals(errorCode) || "0".equals(errorCode);
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }
}
