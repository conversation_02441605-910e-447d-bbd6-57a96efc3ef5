package com.facishare.crm.sfa.lto.duplicated;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.lto.duplicated.models.DuplicatedModels;
import com.facishare.crm.sfa.lto.utils.CommonSqlUtil;
import com.facishare.crm.sfa.lto.utils.ObjectDataUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectMappingService;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.foundation.boot.utility.JSON;
import com.facishare.paas.metadata.api.IObjectMappingRuleDetailInfo;
import com.facishare.paas.metadata.api.IObjectMappingRuleInfo;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class DuplicatedProcessingRuleService {
    @Autowired
    private ObjectMappingService objectMappingService;

    private static String executeTime;
    private static final String DUPLICATED_PROCESSING_TABLE = "biz_leads_duplicated_processing";
    private static final String DUPLICATED_PROCESSING_SEARCH_RELATION_TABLE = "biz_leads_duplicated_processing_search_relation";
    private static final String DUPLICATED_SEARCH_TABLE = "biz_leads_duplicated_search";
    private static final String DUPLICATED_PROCESSING_MODE_TABLE = "biz_leads_duplicated_processing_mode";
    private static final String PRIORITY_API_NAME = "priority";
    private static final String STATUS_API_NAME = "status";
    private static final String PROCESSING_ID_API_NAME = "duplicated_processing_id";

    static {
        ConfigFactory.getConfig("fs-crm-sales-config", config -> executeTime = config.get("duplicated_process_task_execute_time", "20:00:00"));
    }

    /**
     * 获取所有线索处理规则
     *
     * @param user
     * @param needPopulate 是否填充 查重规则 ，处理方式
     * @return
     */
    public List<DuplicatedModels.DuplicatedProcessing> getDuplicatedProcessingList(User user, boolean needPopulate) {
        DuplicatedModels.SearchTemplate searchTemplate = new DuplicatedModels.SearchTemplate();
        List<DuplicatedModels.WhereParam> whereParamList = Lists.newArrayList();
        whereParamList.add(getStatusWhereParam());
        searchTemplate.setWhereParams(whereParamList);
        DuplicatedModels.OrderBy orderBy = new DuplicatedModels.OrderBy();
        orderBy.setFieldName(PRIORITY_API_NAME);
        orderBy.setIsAsc(true);
        searchTemplate.setOrders(Lists.newArrayList(orderBy));
        searchTemplate.setTableName(DUPLICATED_PROCESSING_TABLE);
        return getDuplicatedProcessingList(user, searchTemplate, needPopulate);
    }

    @NotNull
    private DuplicatedModels.WhereParam getStatusWhereParam() {
        return DuplicatedUtil.getWhereParam(STATUS_API_NAME, DuplicatedModels.SqlOperator.EQ,
                Lists.newArrayList(1));
    }

    //根据触发动作获取重复识别处理规则
    public List<DuplicatedModels.DuplicatedProcessing> getDuplicatedProcessingListByTriggerAction(User user, DuplicatedModels.TriggerAction triggerAction) {
        DuplicatedModels.SearchTemplate searchTemplate = new DuplicatedModels.SearchTemplate();
        DuplicatedModels.OrderBy orderBy = new DuplicatedModels.OrderBy();
        orderBy.setFieldName(PRIORITY_API_NAME);
        orderBy.setIsAsc(true);
        searchTemplate.setOrders(Lists.newArrayList(orderBy));
        List<DuplicatedModels.WhereParam> whereParamList = Lists.newArrayList();
        String triggerActionStr = triggerAction.toString();
        if (DuplicatedModels.TriggerAction.OPEN_API_ADD.equals(triggerAction) || DuplicatedModels.TriggerAction.OPEN_API_EDIT.equals(triggerAction)) {
            triggerActionStr = "OPEN_API";
        }
        DuplicatedModels.WhereParam whereParam = DuplicatedUtil.getWhereParam("trigger_action",
                DuplicatedModels.SqlOperator.LIKE, Lists.newArrayList("#" + triggerActionStr + "#"));
        whereParamList.add(whereParam);
        whereParamList.add(getStatusWhereParam());
        searchTemplate.setWhereParams(whereParamList);
        searchTemplate.setTableName(DUPLICATED_PROCESSING_TABLE);
        return getDuplicatedProcessingList(user, searchTemplate, true);
    }

    private List<DuplicatedModels.DuplicatedProcessing> getDuplicatedProcessingList(User user, DuplicatedModels.SearchTemplate searchTemplate, boolean needPopulate) {
        List<DuplicatedModels.DuplicatedProcessing> duplicatedProcessingList = Lists.newArrayList();
        List<Map> selectedMaps = DuplicatedUtil.select(user.getTenantId(), searchTemplate);
        if(CollectionUtils.isEmpty(selectedMaps)) {
            return duplicatedProcessingList;
        }
        for (Map map : selectedMaps) {
            String processingId = String.valueOf(map.get("id"));
            if ("null".equals(processingId) || StringUtils.isBlank(processingId)) {
                continue;
            }
            DuplicatedModels.DuplicatedProcessing processing = getDuplicatedProcessingById(user, processingId, needPopulate);
            if (processing != null) {
                processing.setTaskExecuteTime(executeTime);
                duplicatedProcessingList.add(processing);
            }
        }
        return duplicatedProcessingList;
    }

    //根据规则ID列表取规则
    public List<DuplicatedModels.DuplicatedProcessing> getDuplicatedProcessingListByIds(User user, List<String> processingIds) {
        if(CollectionUtils.isEmpty(processingIds)) {
            return Lists.newArrayList();
        }
        List<DuplicatedModels.DuplicatedProcessing> duplicatedProcessingList = Lists.newArrayList();
        processingIds.forEach(processingId -> {
            DuplicatedModels.DuplicatedProcessing processing = getDuplicatedProcessingById(user, processingId, true);
            if (processing != null) {
                processing.setTaskExecuteTime(executeTime);
                duplicatedProcessingList.add(processing);
            }
        });
        return duplicatedProcessingList;
    }

    //根据重复识别处理规则ID获取规则数据
    public DuplicatedModels.DuplicatedProcessing getDuplicatedProcessingById(User user, String processingId, boolean needPopulate) {
        DuplicatedModels.DuplicatedProcessing result = null;
        if (StringUtils.isEmpty(processingId)) {
            return null;
        }
        try {
            Map<String, Object> wheres = Maps.newHashMap();
            wheres.put("id", processingId);
            List<Map> selectedMaps = DuplicatedUtil.doSelect(user, DUPLICATED_PROCESSING_TABLE, wheres);
            Map<String, Object> selected = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(selectedMaps)) {
                selected = selectedMaps.get(0);
            }
            if (MapUtils.isNotEmpty(selected)) {
                String id = String.valueOf(selected.get("id"));
                String name = String.valueOf(selected.get("name"));
                String description = String.valueOf(selected.get("description"));
                Integer status = ObjectDataUtil.getIntegerValue(selected, STATUS_API_NAME, 0);
                String str = String.valueOf(selected.get("trigger_action"));
                List<String> triggerActions = Arrays.asList(str.split("#"));
                triggerActions = triggerActions.stream().filter(StringUtils::isNotEmpty).collect(Collectors.toList());
                Integer priority = ObjectDataUtil.getIntegerValue(selected, PRIORITY_API_NAME, 10000);
                List<DuplicatedModels.Filter> filters = Arrays.asList(JSON.deserializeAsArray(ObjectDataUtil.getStringValue(selected, "filters", "[]"),
                        DuplicatedModels.Filter.class));
                String lastModifiedBy = String.valueOf(selected.get("last_modified_by"));
                long lastModifiedTime =ObjectDataUtil.getLongValue(selected, "last_modified_time", 0L);
                result = DuplicatedModels.DuplicatedProcessing.builder()
                        .id(id)
                        .name(name)
                        .description(description)
                        .triggerActions(triggerActions)
                        .priority(priority)
                        .status(status)
                        .filters(filters)
                        .lastModifiedBy(lastModifiedBy)
                        .lastModifiedTime(lastModifiedTime)
                        .build();
                if (needPopulate) { //是否需要填充查重规则和处理方式
                    List<DuplicatedModels.DuplicatedSearchRule> searchRules = getDuplicatedSearchRulesByProcessingId(user, processingId);
                    result.setDuplicatedSearchRules(searchRules);
                    List<DuplicatedModels.DuplicatedProcessingMode> processingModes = getDuplicatedProcessingModes(user, processingId);
                    result.setDuplicatedProcessingModes(processingModes);
                }
            }
        } catch (Exception e) {
            log.error("DuplicatedProcessingService getDuplicatedProcessing error", e);
            throw new MetaDataException(e.getMessage());
        }
        return result;
    }

    public List<DuplicatedModels.DuplicatedSearchRule> getDuplicatedSearchRulesByProcessingId(User user, String processingId) {
        List<DuplicatedModels.DuplicatedSearchRule> result = Lists.newArrayList();
        if (StringUtils.isEmpty(processingId)) {
            return result;
        }
        Map<String, Object> wheres = Maps.newHashMap();
        wheres.put(PROCESSING_ID_API_NAME, processingId);
        List<Map> selectedMaps = DuplicatedUtil.doSelect(user, DUPLICATED_PROCESSING_SEARCH_RELATION_TABLE, wheres);
        if (CollectionUtils.isEmpty(selectedMaps)) {
            return result;
        }
        List<String> searchRuleIds = selectedMaps.stream().map(s -> String.valueOf(s.get("duplicated_search_id")))
                .collect(Collectors.toList());
        result = getDuplicatedSearchRuleByIds(user, searchRuleIds);
        return result;
    }

    private List<DuplicatedModels.DuplicatedSearchRule> getDuplicatedSearchRuleByIds(User user, List<String> searchRuleIds) {
        List<DuplicatedModels.DuplicatedSearchRule> result = Lists.newArrayList();
        Map<String, Object> wheres = Maps.newHashMap();
        wheres.put("id", searchRuleIds);
        wheres.put(STATUS_API_NAME, 1);
        List<Map> selectedMaps = DuplicatedUtil.doSelect(user, DUPLICATED_SEARCH_TABLE, wheres);
        if (CollectionUtils.isEmpty(selectedMaps)) {
            return result;
        }
        result = convertMapsToEntities(user, selectedMaps);
        return result;
    }

    private List<DuplicatedModels.DuplicatedSearchRule> convertMapsToEntities(User user, List<Map> selectedMaps) {
        List<DuplicatedModels.DuplicatedSearchRule> result = Lists.newArrayList();
        for (Map map : selectedMaps) {
            String id = String.valueOf(map.get("id"));
            String name = String.valueOf(map.get("name"));
            String description = String.valueOf(map.get("description"));
            String objectApiName = String.valueOf(map.get("object_api_name"));
            String objectName = null;
            if (Utils.LEADS_API_NAME.equals(objectApiName)) {
                objectName = I18N.text("LeadsObj.attribute.self.display_name");
            } else if (Utils.ACCOUNT_API_NAME.equals(objectApiName)) {
                objectName = I18N.text("AccountObj.attribute.self.display_name");
            } else if (Utils.CONTACT_API_NAME.equals(objectApiName)) {
                objectName = I18N.text("ContactObj.attribute.self.display_name");
            }
            List<String> showFields = Arrays.asList(JSON.deserializeAsArray(ObjectDataUtil.getStringValue(map, "show_fields", "[]"), String.class));
            List<DuplicatedModels.UsableRule> usableRules = Arrays.asList(JSON.deserializeAsArray(ObjectDataUtil.getStringValue(map, "usable_rules", "[]"),
                    DuplicatedModels.UsableRule.class));
            Integer status = ObjectDataUtil.getIntegerValue(map, STATUS_API_NAME, 0);
            DuplicatedModels.DuplicatedSearchRule duplicatedSearchRule = DuplicatedModels.DuplicatedSearchRule.builder()
                    .id(id)
                    .name(name)
                    .description(description)
                    .objectApiName(objectApiName)
                    .objectName(objectName)
                    .status(status)
                    .showFields(showFields)
                    .usableRules(usableRules)
                    .taskExecuteTime(executeTime)
                    .build();
            result.add(duplicatedSearchRule);
        }
        checkFullFieldsIsMapping(user, result);
        return result;
    }

    private void checkFullFieldsIsMapping(User user, List<DuplicatedModels.DuplicatedSearchRule> ruleList) {
        if (CollectionUtils.isEmpty(ruleList)) {
            return;
        }
        for (DuplicatedModels.DuplicatedSearchRule searchRule : ruleList) {
            if (searchRule == null || CollectionUtils.isEmpty(searchRule.getUsableRules())) {
                continue;
            }
            List<DuplicatedModels.UsableRule> usableRuleList = searchRule.getUsableRules();
            String objectApiName = searchRule.getObjectApiName();
            if (Utils.LEADS_API_NAME.equals(objectApiName)) {
                searchRule.setMapFullFields(true);
                continue;
            }
            Set<String> fieldList = Sets.newHashSet();
            usableRuleList.stream()
                    .filter(r -> CollectionUtils.isNotEmpty(r.getConditions()))
                    .map(r -> r.getConditions().stream()
                            .map(DuplicatedModels.SearchCondition::getFieldName)
                            .collect(Collectors.toList()))
                    .forEach(a -> {
                        if (CollectionUtils.isNotEmpty(a)) {
                            fieldList.addAll(a);
                        }
                    });
            String defaultMappingRuleApiName = "";
            if (Utils.ACCOUNT_API_NAME.equals(objectApiName)) {
                defaultMappingRuleApiName = "rule_leadsobj2accountobj__c";
            } else if (Utils.CONTACT_API_NAME.equals(objectApiName)) {
                defaultMappingRuleApiName = "rule_leadsobj2contactobj__c";
            }
            searchRule.setMapFullFields(false);
            if (StringUtils.isEmpty(defaultMappingRuleApiName)) {
                continue;
            }
            List<IObjectMappingRuleInfo> mappingRuleInfos = objectMappingService.findByApiName(user, defaultMappingRuleApiName);
            if (CollectionUtils.isNotEmpty(mappingRuleInfos)) {
                String ruleApiName = defaultMappingRuleApiName;
                Optional<IObjectMappingRuleInfo> mappingRuleInfoOp = mappingRuleInfos.stream().filter(x -> ruleApiName.equals(x.getRuleApiName())).findFirst();
                if(!mappingRuleInfoOp.isPresent()) {
                    continue;
                }
                IObjectMappingRuleInfo mappingRuleInfo = mappingRuleInfoOp.get();
                List<String> targetFieldNames = mappingRuleInfo.getFieldMapping().stream()
                        .map(IObjectMappingRuleDetailInfo::getTargetFieldName).collect(Collectors.toList());
                boolean isMatch = targetFieldNames.containsAll(fieldList);
                if (Utils.CONTACT_API_NAME.equals(objectApiName)) {
                    isMatch = checkContactTelAndMobile(fieldList, targetFieldNames, isMatch);
                }
                searchRule.setMapFullFields(isMatch);
            }
        }
    }

    private boolean checkContactTelAndMobile(Set<String> fieldList, List<String> targetFieldNames, boolean isMatch) {
        if (fieldList.contains("mobile")) {
            List<String> mobileList = Lists.newArrayList("mobile", "mobile1", "mobile2", "mobile3", "mobile4", "mobile5");
            if (mobileList.stream().anyMatch(targetFieldNames::contains)) {
                isMatch = true;
            }
        }
        if (fieldList.contains("tel")) {
            List<String> mobileList = Lists.newArrayList("tel", "tel1", "tel2", "tel3", "tel4", "tel5");
            if (mobileList.stream().anyMatch(targetFieldNames::contains)) {
                isMatch = true;
            }
        }
        return isMatch;
    }

    private List<DuplicatedModels.DuplicatedProcessingMode> getDuplicatedProcessingModes(User user, String processingId) {
        List<DuplicatedModels.DuplicatedProcessingMode> result = Lists.newArrayList();
        if (StringUtils.isBlank(processingId)) {
            return result;
        }
        Map<String, Object> wheres = Maps.newHashMap();
        wheres.put(PROCESSING_ID_API_NAME, processingId);
        List<Map> selectedMaps = DuplicatedUtil.doSelect(user, DUPLICATED_PROCESSING_MODE_TABLE, wheres);
        if (CollectionUtils.isNotEmpty(selectedMaps)) {
            for (Map selected : selectedMaps) {
                if (MapUtils.isEmpty(selected)) {
                    continue;
                }
                String id = String.valueOf(selected.get("id"));
                String duplicatedProcessingId = String.valueOf(selected.get(PROCESSING_ID_API_NAME));
                Integer priority = ObjectDataUtil.getIntegerValue(selected, PRIORITY_API_NAME, 10000);
                DuplicatedModels.ModeAction modeAction = DuplicatedModels.ModeAction
                        .valueOf(String.valueOf(selected.get("mode_action")));
                List<DuplicatedModels.Filter> filters = Arrays.asList(JSON.deserializeAsArray(ObjectDataUtil.getStringValue(selected,"filters", "[]"),
                        DuplicatedModels.Filter.class));
                DuplicatedModels.ModeRule modeRule = JSON.deserialize(String.valueOf(selected.get("mode_rule")),
                        DuplicatedModels.ModeRule.class);
                if (!DuplicatedModels.ModeAction.CUSTOM_FUNCTION.equals(modeAction)) {
                    modeRule.setCustomFunction(null);
                }
                if (!DuplicatedModels.ModeAction.ADD_INTERACTIVE_RECORDS.equals(modeAction)) {
                    modeRule.setExtendRules(null);
                }
                DuplicatedModels.DuplicatedProcessingMode processingMode = DuplicatedModels.DuplicatedProcessingMode.builder()
                        .id(id)
                        .duplicatedProcessingId(duplicatedProcessingId)
                        .priority(priority)
                        .filters(filters)
                        .modeAction(modeAction)
                        .modeRule(modeRule)
                        .build();
                result.add(processingMode);
            }
        }
        result = result.stream().sorted(Comparator.comparing(DuplicatedModels.DuplicatedProcessingMode::getPriority)).collect(Collectors.toList());
        return result;
    }

    public int getRefreshVersion(User user) {
        int maxVersion = 0;
        String sql = String.format("select COALESCE(max(refresh_version),0) as refresh_version " +
                "from biz_leads_duplicated_processing where tenant_id='%s'", user.getTenantId());
        List<Map> queryResult = CommonSqlUtil.findBySql(user.getTenantId(), sql);
        if (CollectionUtils.isNotEmpty(queryResult)) {
            maxVersion = ObjectDataUtil.getIntegerValue(queryResult.get(0), "refresh_version", 0);
        }
        return maxVersion;
    }
}
