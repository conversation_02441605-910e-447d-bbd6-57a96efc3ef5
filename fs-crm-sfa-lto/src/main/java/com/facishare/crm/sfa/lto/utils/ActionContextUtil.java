package com.facishare.crm.sfa.lto.utils;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.action.ActionContextKey;
import com.facishare.paas.metadata.api.action.IActionContext;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

public class ActionContextUtil {
    private ActionContextUtil() {
        throw new IllegalStateException("Utility class");
    }

    public static IActionContext createActionContext(String tenantId) {
        User user = new  User(tenantId, User.SUPPER_ADMIN_USER_ID);
        ActionContextOp op = ActionContextOp.builder()
                .updateLastModifyTime(true).skipVersionChange(false).allowUpdateInvalid(true).notValidate(true)
                .build();
        return createActionContext(user, op);
    }

    public static IActionContext createActionContext(String tenantId, boolean forceESSearch) {
        IActionContext actionContext = createActionContext(tenantId);
        actionContext.put(ActionContextKey.ES_SEARCH_SKIP_RECENT_UPDATE_CHECK, forceESSearch);
        return actionContext;
    }

    public static IActionContext createSearchActionContext(User user, boolean skipRelevantTeam, boolean needDeepQuote) {
        IActionContext actionContext = ActionContextExt.of(user).getContext();
        actionContext.put("skip_relevantTeam", skipRelevantTeam);
        actionContext.put("need_deep_quote", needDeepQuote);
        return actionContext;
    }

    public static IActionContext createSearchActionContext(User user, SearchActionContextOp op) {
        ActionContextExt actionContextExt = ActionContextExt.of(user);
        actionContextExt.setCalculateFormula(op.calculateFormula);
        actionContextExt.setSkipRelevantTeam(op.skipRelevantTeam);
        if(op.esRedisRecentUpdateCheck) {
            actionContextExt.esRedisRecentUpdateCheck();
        }
        actionContextExt.set("need_deep_quote", op.needDeepQuote);
        actionContextExt.setPrivilegeCheck(op.privilegeCheck);
        actionContextExt.paginationOptimization();
        if(StringUtils.isNotBlank(op.dbType)) {
            actionContextExt.setDbType(op.dbType);
        }
        if(op.forceESSearch) {
            actionContextExt.set(ActionContextKey.ES_SEARCH_SKIP_RECENT_UPDATE_CHECK, true);
        }
        return actionContextExt.getContext();
    }

    public static IActionContext createActionContext(User user, ActionContextOp op) {
        ActionContextExt actionContextExt = ActionContextExt.of(user);
        actionContextExt.setAllowUpdateInvalid(op.allowUpdateInvalid);
        actionContextExt.setPrivilegeCheck(op.privilegeCheck);
        actionContextExt.setNotValidate(op.notValidate);
        actionContextExt.setIsSpecifyTime(op.updateLastModifyTime);
        actionContextExt.setBatch(op.batch);
        if(op.skipVersionChange) {
            actionContextExt.doSkipVersionChange();
        }
        if(StringUtils.isNotBlank(op.dbType)) {
            actionContextExt.setDbType(op.dbType);
        }
        if(StringUtils.isNotBlank(op.dbType)) {
            actionContextExt.setEventId(op.eventId);
        }
        return actionContextExt.getContext();
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SearchActionContextOp {
        boolean skipRelevantTeam;
        boolean calculateFormula;
        boolean needDeepQuote;
        boolean esRedisRecentUpdateCheck;
        boolean privilegeCheck;
        boolean forceESSearch;
        String dbType;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ActionContextOp {
        boolean updateLastModifyTime;
        boolean skipVersionChange;
        boolean allowUpdateInvalid;
        boolean privilegeCheck;
        boolean notValidate;
        String dbType;
        String eventId;
        boolean batch;
    }
}
