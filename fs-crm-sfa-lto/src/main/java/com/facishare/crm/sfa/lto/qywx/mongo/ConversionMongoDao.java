package com.facishare.crm.sfa.lto.qywx.mongo;

import com.facishare.crm.sfa.lto.utils.DateUtil;
import com.github.mongo.support.DatastoreExt;
import org.mongodb.morphia.query.CriteriaContainer;
import org.mongodb.morphia.query.Query;

import java.util.List;


public class ConversionMongoDao {

    private DatastoreExt datastore;

	private final static String COLLECTION_PREFIX = "message_save_";

	private final static String DESC_PREFIX = "-";

	public void setDatastore(DatastoreExt datastore) {
		datastore.setQueryFactory(new InTraceQueryFactory());
		this.datastore = datastore;
	}

	public Query<WechatConversion> createTenantQuery(String tenantId) {
		return datastore.createQuery(getCollectionName(tenantId), WechatConversion.class);
	}

	private static String getCollectionName(String tenantId) {
		return COLLECTION_PREFIX.concat(tenantId);
	}


	public WechatConversion getLastConversionByFromToId(String tenantId, String fromId, String toId) {
		Query<WechatConversion> query = this.createTenantQuery(tenantId);

		fillFromToCriteria(fromId, toId, query);
		query.field(WechatConversion.FIELD_DESCRY_KEY).doesNotExist();

		query.order(DESC_PREFIX.concat(WechatConversion.FIELD_SEQ));
		query.limit(1);
		return query.get();
	}

	public void fillFromToCriteria(String fromId, String toId, Query<WechatConversion> query) {
		// ((from_user_cipher = fromCipherId and to_list_cipher = toCipherId) or
		// 	(from_user_cipher = toCipherId and to_list_cipher = fromCipherId) )

		CriteriaContainer fromToCriteria = query.or(
				query.and(
						query.criteria(WechatConversion.FIELD_FROM_USER).equal(fromId),
						query.criteria(WechatConversion.FIELD_TO_LIST).equal(toId)
				),
				query.and(
						query.criteria(WechatConversion.FIELD_FROM_USER).equal(toId),
						query.criteria(WechatConversion.FIELD_TO_LIST).equal(fromId)
				)
		);

		CriteriaContainer roomIdCriteria = query.and(query.criteria(WechatConversion.FIELD_ROOM_ID).equal(null));
		query.and(fromToCriteria, roomIdCriteria);
	}

	public WechatConversion getLastConversionByRoomId(String tenantId, String roomId) {
		Query<WechatConversion> query = this.createTenantQuery(tenantId);
		query.field(WechatConversion.FIELD_ROOM_ID).equal(roomId);
		query.field(WechatConversion.FIELD_DESCRY_KEY).doesNotExist();
		query.order(DESC_PREFIX.concat(WechatConversion.FIELD_SEQ));
		query.limit(1);
		return query.get();
	}

	public long countMsgByFromToId(String tenantId, String fromId, String toId) {
		Query<WechatConversion> query = this.createTenantQuery(tenantId);
		query.field(WechatConversion.FIELD_FROM_USER).equal(fromId);
		query.field(WechatConversion.FIELD_TO_LIST).equal(toId);
		query.field(WechatConversion.FIELD_ROOM_ID).equal(null);
		query.field(WechatConversion.FIELD_DESCRY_KEY).doesNotExist();
		return query.countAll();
	}

	public WechatConversion getFirstMsgByFromToId(String tenantId, String fromId, String toId) {
		Query<WechatConversion> query = this.createTenantQuery(tenantId);
		query.field(WechatConversion.FIELD_FROM_USER).equal(fromId);
		query.field(WechatConversion.FIELD_TO_LIST).equal(toId);
		query.field(WechatConversion.FIELD_ROOM_ID).equal(null);
		query.field(WechatConversion.FIELD_DESCRY_KEY).doesNotExist();
		query.order(WechatConversion.FIELD_SEQ);
		query.limit(1);
		return query.get();
	}

	public long countDailyMsgByFromToId(String tenantId, String fromId, String toId) {
		Query<WechatConversion> query = this.createTenantQuery(tenantId);
		fillFromToCriteria(fromId, toId, query);
		CriteriaContainer msgTimeCriteria = query.and(
				query.criteria(WechatConversion.FIELD_MSG_TIME).greaterThanOrEq(DateUtil.getCurrentDateTimeStamp()));
		CriteriaContainer oldDataCriteria = query.and(query.criteria(WechatConversion.FIELD_DESCRY_KEY).doesNotExist());
		query.and(msgTimeCriteria, oldDataCriteria);
		return query.countAll();
	}

	public long countDailyMsgByRoomId(String tenantId, String roomId) {
		Query<WechatConversion> query = this.createTenantQuery(tenantId);
		query.field(WechatConversion.FIELD_ROOM_ID).equal(roomId);
		query.field(WechatConversion.FIELD_MSG_TIME).greaterThanOrEq(DateUtil.getCurrentDateTimeStamp());
		query.field(WechatConversion.FIELD_DESCRY_KEY).doesNotExist();
		return query.countAll();
	}

	public long countMsgRoomId(String tenantId, String roomId) {
		Query<WechatConversion> query = this.createTenantQuery(tenantId);
		query.field(WechatConversion.FIELD_ROOM_ID).equal(roomId);
		query.field(WechatConversion.FIELD_DESCRY_KEY).doesNotExist();
		return query.countAll();
	}

	public long countRoomMsgByEmployeeId(String tenantId, String roomId, List<String> employeeIdList) {
		Query<WechatConversion> query = this.createTenantQuery(tenantId);
		query.field(WechatConversion.FIELD_ROOM_ID).equal(roomId);
		query.field(WechatConversion.FIELD_FROM_USER).in(employeeIdList);
		query.field(WechatConversion.FIELD_DESCRY_KEY).doesNotExist();
		return query.countAll();
	}

	public List<WechatConversion> getConversionsByFromToId(String tenantId, String fromId, String toId, int startSeq, int endSeq) {
		Query<WechatConversion> query = this.createTenantQuery(tenantId);
		fillFromToCriteria(fromId, toId, query);
		query.field(WechatConversion.FIELD_SEQ).greaterThan(startSeq);
		query.field(WechatConversion.FIELD_SEQ).lessThanOrEq(endSeq);
		query.field(WechatConversion.FIELD_DESCRY_KEY).doesNotExist();
		query.order(WechatConversion.FIELD_MSG_TIME); //升序
		query.limit(100);
		return query.asList();
	}

	public List<WechatConversion> getConversionsByRoomId(String tenantId, String roomId, int startSeq, int endSeq) {
		Query<WechatConversion> query = this.createTenantQuery(tenantId);
		query.field(WechatConversion.FIELD_ROOM_ID).equal(roomId);
		query.field(WechatConversion.FIELD_SEQ).greaterThan(startSeq);
		query.field(WechatConversion.FIELD_SEQ).lessThanOrEq(endSeq);
		query.field(WechatConversion.FIELD_DESCRY_KEY).doesNotExist();
		//query.order(WechatConversion.FIELD_MSG_TIME); //升序
		query.limit(100);
		return query.asList();
	}

	public List<WechatConversion> getAnalysisConversionsByFromToId(String tenantId, String fromId, String toId, long startTime, long endTime) {
		Query<WechatConversion> query = this.createTenantQuery(tenantId);
		fillFromToCriteria(fromId, toId, query);
		query.field(WechatConversion.FIELD_DESCRY_KEY).doesNotExist();
		query.field(WechatConversion.FIELD_MSG_TIME).greaterThan(startTime);
		query.field(WechatConversion.FIELD_MSG_TIME).lessThanOrEq(endTime);
		query.order(WechatConversion.FIELD_MSG_TIME); //升序
		return query.asList();
	}

	public List<WechatConversion> getAnalysisConversionsByRoomId(String tenantId, String roomId, long startTime, long endTime) {
		Query<WechatConversion> query = this.createTenantQuery(tenantId);
		query.field(WechatConversion.FIELD_ROOM_ID).equal(roomId);
		query.field(WechatConversion.FIELD_DESCRY_KEY).doesNotExist();
		query.field(WechatConversion.FIELD_MSG_TIME).greaterThan(startTime);
		query.field(WechatConversion.FIELD_MSG_TIME).lessThanOrEq(endTime);
		query.order(WechatConversion.FIELD_MSG_TIME); //升序
		return query.asList();
	}
}
