package com.facishare.crm.sfa.lto.objectpool;

import com.facishare.crm.sfa.lto.objectpool.models.ObjectPoolActionModels;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;

public interface IObjectPoolActionService {
    ObjectPoolActionModels.Result choose(User user, String objectPoolId, List<String> objectIds, String eventId, String partnerId);

    ObjectPoolActionModels.Result move(User user, String objectPoolId, List<String> objectIds, String eventId);

    ObjectPoolActionModels.Result back(User user, String objectPoolId, List<String> objectIds, Integer operationType, String backReason, String backReasonOther, String eventId, boolean isPrmOpen);

    ObjectPoolActionModels.Result allocate(User user, String objectPoolId, List<String> objectIds, String owner, String eventId, Long outTenantId, Long outOwnerId, String partnerId);

    ObjectPoolActionModels.Result remove(User user, List<IObjectData> objectDataList, String owner, boolean isKeepOwner, String eventId);

    ObjectPoolActionModels.Result takeBack(User user, String objectPoolId, List<String> objectIds, String eventId, boolean isPrmOpen);
}
