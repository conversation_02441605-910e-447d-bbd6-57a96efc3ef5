package com.facishare.crm.sfa.lto.activity.enums;

public interface AttendeesInsightType {

    // 态度
    String ATTITUDE = "attitude";
    // 关注点
    String FOCUS_POINT = "focus_point";
    // 会议总结
    String MEETING_SUMMARY = "meeting_summary";
    // 隐形担忧
    String INVISIBLE_CONCERN = "invisible_concern";

    // 问题回答表现
    String QUESTION_ANSWER_PERFORMANCE = "question_answer_performance";
    // 对话技巧表现
    String DIALOGUE_SKILL_PERFORMANCE = "dialogue_skill_performance";
    // SOP 覆盖率
    String SOP_COVERAGE = "sop_coverage";
    // 销售技巧评价
    String SALES_SKILL_EVALUATION = "sales_skill_evaluation";
    // 改进建议
    String IMPROVEMENT_SUGGESTION = "improvement_suggestion";
}
