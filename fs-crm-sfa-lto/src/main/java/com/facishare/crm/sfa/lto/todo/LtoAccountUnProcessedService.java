package com.facishare.crm.sfa.lto.todo;

import com.facishare.crm.sfa.lto.todo.enums.LtoSessionBOCItemKeys;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.crm.sfa.lto.utils.TodoUtils;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Objects;

/**
 * Demo class
 *
 * <AUTHOR>
 * @date 2019/10/23
 */
@Service
@Slf4j
public class LtoAccountUnProcessedService implements LtoIUnProcessedService {
    @Autowired
    private ServiceFacade serviceFacade;
    @Override
    public String getObjectApiName() {
        return "AccountObj";
    }

    @Override
    public List<IObjectData> getTotalUnProcessedData(User user, LtoSessionBOCItemKeys sessionBOCItemKey) {
        List<IObjectData> remindData = getTobeRemindData(user, sessionBOCItemKey);
        return remindData;
    }

    private List<IObjectData> getTobeRemindData(User user, LtoSessionBOCItemKeys sessionBOCItemKey) {
        if (!TodoUtils.isOpenAccountFillingCheck(user)) {
            return Lists.newArrayList();
        }
        List<IObjectData> result = Lists.newArrayList();
        SearchTemplateQuery query = new SearchTemplateQuery();
        TodoUtils.handleAccountSearchQuery(query, user);
        query.setOffset(0);
        query.setLimit(150);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, getObjectApiName(), query);
        if (!Objects.isNull(queryResult)) {
            result = queryResult.getData();
        }
        return result;
    }
}
