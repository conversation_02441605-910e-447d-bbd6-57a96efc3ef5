package com.facishare.crm.sfa.prm.api.enums;

import lombok.Getter;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-05-08
 * ============================================================
 */
@Getter
public enum ChannelStage {
    REGISTERED("registered"),
    SIGNED("signed"),
    RENEWAL("renewed");

    private final String status;

    ChannelStage(String status) {
        this.status = status;
    }

    public static ChannelStage find(String status) {
        return find(status, null);
    }

    public static ChannelStage find(String status, ChannelStage defaultValue) {
        for (ChannelStage e : values()) {
            if (e.status.equals(status)) {
                return e;
            }
        }
        return defaultValue;
    }
}
