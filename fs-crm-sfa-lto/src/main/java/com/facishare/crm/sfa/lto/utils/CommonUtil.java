package com.facishare.crm.sfa.lto.utils;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;

import java.util.List;
import java.util.stream.Collectors;

public class CommonUtil {
    private CommonUtil() {
        throw new IllegalStateException("Utility class");
    }

    public static String buildSqlInString(List<String> ids) {
        if (CollectionUtils.empty(ids)) {
            return "''";
        }
        ids = ids.stream().distinct().collect(Collectors.toList());
        StringBuilder builder = new StringBuilder();
        for (String id : ids) {
            builder.append("'").append(id).append("',");
        }
        String idString = builder.toString();
        idString = idString.substring(0, idString.length() - 1);
        return idString;
    }

    public static User buildUser(String tenantId) {
        return buildUser(tenantId, User.SUPPER_ADMIN_USER_ID, "", "");
    }

    public static User buildUser(String tenantId, String userId) {
        return buildUser(tenantId, userId, "", "");
    }

    private static User buildUser(String tenantId, String userId, String outUserId, String outTenantId) {
        return new User(tenantId, userId, outUserId, outTenantId);
    }
}