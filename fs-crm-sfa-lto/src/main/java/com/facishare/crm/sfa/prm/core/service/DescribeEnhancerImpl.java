package com.facishare.crm.sfa.prm.core.service;

import com.facishare.crm.sfa.prm.api.enhancer.DescribeEnhancer;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-03-05
 * ============================================================
 */
@Service
@Slf4j
public class DescribeEnhancerImpl implements DescribeEnhancer {
    @Resource
    private DescribeLogicService describeLogicService;

    @Override
    public IObjectDescribe fetchObject(User user, String objectApiName) {
        RequestContext context = RequestContextManager.getContext();
        return fetchObject(context, user, objectApiName);
    }

    @Override
    public IObjectDescribe fetchObject(RequestContext context, User user, String objectApiName) {
        if (StringUtils.isBlank(objectApiName)) {
            return null;
        }
        if (context != null) {
            context.setAttribute(RequestContext.DIRECT_KEY, true);
        }
        try {
            return describeLogicService.findObject(user.getTenantId(), objectApiName);
        } catch (Exception e) {
            log.warn("DescribeEnhancerImpl#fetchObject failed, tenant:{}, objectApiName:{}", user.getTenantId(), objectApiName, e);
            return null;
        }
    }

    @Override
    public Map<String, IObjectDescribe> fetchObjects(User user, Collection<String> apiNames) {
        RequestContext context = RequestContextManager.getContext();
        return fetchObjects(context, user, apiNames);
    }

    @Override
    public Map<String, IObjectDescribe> fetchObjects(RequestContext context, User user, Collection<String> apiNames) {
        if (CollectionUtils.isEmpty(apiNames)) {
            return new HashMap<>();
        }
        if (context != null) {
            context.setAttribute(RequestContext.DIRECT_KEY, true);
        }
        try {
            return describeLogicService.findObjects(user.getTenantId(), apiNames);
        } catch (Exception e) {
            log.warn("DescribeEnhancerImpl#fetchObjects failed, tenant:{}, objectApiName:{}", user.getTenantId(), apiNames, e);
            return new HashMap<>();
        }
    }
}
