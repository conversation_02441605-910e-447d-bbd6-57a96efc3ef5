package com.facishare.crm.sfa.lto.integral.core.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;
import java.util.Map;

public interface RuleDetail {

    int ADD = 0;
    int SUBTRACT = 1;

    /**
     * 区间赋值
     */
    int RANGE = 0;
    /**
     * 直接赋值
     */
    int DIRECT = 1;

    @Data
    class RuleItem {
        @JSONField(name = "category_api_name")
		@JsonProperty("category_api_name")
        private String categoryApiName;
        @JSONField(name = "branches")
		@JsonProperty("branches")
        private List<RuleItemBranch> branches;
    }

    @Data
    class RuleItemBranch {
        @JSONField(name = "action_api_name")
		@JsonProperty("action_api_name")
        String actionApiName;
        @JSONField(name = "material_api_names")
		@JsonProperty("material_api_names")
        List<String> materialApiNames;
        @JSONField(name = "material_api_name_map")
        @JsonProperty("material_api_name_map")
        Map<String, String> materialApiNameMap;
        @JSONField(name = "calculate_type")
		@JsonProperty("calculate_type")
        int calculateType;
        @JSONField(name = "calculate_score")
		@JsonProperty("calculate_score")
        double calculateScore;
    }


    @Data
    class RuleResult {
        @JSONField(name = "field_name")
		@JsonProperty("field_name")
        private String fieldName;
        @JSONField(name = "field_type")
		@JsonProperty("field_type")
        private String fieldType;
        @JSONField(name = "assignment_method")
		@JsonProperty("assignment_method")
        int assignmentMethod;
        @JSONField(name = "branches")
		@JsonProperty("branches")
        private List<RuleResultBranch> branches;
    }

    @Data
    class RuleResultBranch {
        @JSONField(name = "operator")
		@JsonProperty("operator")
        String operator;
        @JSONField(name = "field_values")
		@JsonProperty("field_values")
        List<Object> fieldValue;
        @JSONField(name = "result_value")
		@JsonProperty("result_value")
        List<Object> resultValue;
        @SerializedName("other_value")
		@JsonProperty("other_value")
        String otherValue;
    }
}
