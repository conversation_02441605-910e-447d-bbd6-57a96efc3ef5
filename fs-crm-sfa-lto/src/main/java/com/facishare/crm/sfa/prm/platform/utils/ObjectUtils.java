package com.facishare.crm.sfa.prm.platform.utils;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-02-28
 * ============================================================
 */
@Slf4j
public class ObjectUtils {
    public static <T> T convertObject(String jsonObject, Class<T> clazz) {
        if (StringUtils.isBlank(jsonObject) || clazz == null) {
            log.warn("convertObject jsonObject or class is null, jsonObject:{}, clazz:{}", jsonObject, clazz);
            return null;
        }
        try {
            return JSON.parseObject(jsonObject, clazz);
        } catch (Exception e) {
            log.warn("ObjectUtils#convertObject json error, jsonObject:{}", jsonObject, e);
        }
        return null;
    }
}
