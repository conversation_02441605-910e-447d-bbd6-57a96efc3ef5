package com.facishare.crm.sfa.lto.qywx.proxy.models;

import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface QueryChatGroupIdModel {


	@Data
	@Builder
	class QueryArg {
		private String ea;//必填
		private Integer statusFilter;//客户群跟进状态过滤。0 - 所有列表(即不过滤)，1 - 离职待继承，2 - 离职继承中，3 - 离职继承完成，默认为0
		private QyweixinOwnerFilter ownerFilter;//群主过滤。如果不填，表示获取应用可见范围内全部群主的数据（但是不建议这么用，如果可见范围人数超过1000人，为了防止数据包过大，会报错 81017）
		private String cursor;//用于分页查询的游标，字符串类型，由上一次调用返回，首次调用不填
		private Integer limit = 10;//分页，预期请求的数据量，取值范围 1 ~ 1000 必填

	}

	@Data
	@Builder
	class QyweixinOwnerFilter {
		private List<String> userIdList;//用户ID列表。最多100个
	}

	@Data
	class QueryResult extends BaseQywxResult {
		private QyweixinGroupChatResult data;
	}

	@Data
	class QyweixinGroupChatResult {
		private List<QyweixinGroupChat> groupChatList;//客户群列表
		private String nextCursor;//分页游标，下次请求时填写以获取之后分页的记录。如果该字段返回空则表示已没有更多数据
	}

	@Data
	class QyweixinGroupChat {
		private String chatId;//客户群ID
		private Integer status;//客户群跟进状态。0 - 跟进人正常，1 - 跟进人离职，2 - 离职继承中，3 - 离职继承完成
	}


}
