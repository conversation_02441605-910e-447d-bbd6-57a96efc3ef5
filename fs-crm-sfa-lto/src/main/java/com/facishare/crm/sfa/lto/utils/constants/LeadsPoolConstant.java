package com.facishare.crm.sfa.lto.utils.constants;

public interface LeadsPoolConstant {
    class Field{
        public static final String Name = "name";
        public static final String Id = "id";
        public static final String Object_Describe_Api_Name = "object_describe_api_name";
        public static final String Claim_Limit_Num = "claim_limit_num";
        public static final String Claim_Interval_Days = "claim_interval_days";
        public static final String Is_Visible_To_Member = "is_visible_to_member";
        public static final String Allow_Member_Relation = "allow_member_relation";
        public static final String Allow_Member_View_Feed = "allow_member_view_feed";
        public static final String Allow_Member_Send_Feed = "allow_member_send_feed";
        public static final String Is_Claim_Limit_Include_Dealed_Customers = "is_claim_limit_include_dealed_customers";
        public static final String Is_Recycling_Team_Member = "is_recycling_team_member";
        public static final String Allow_Member_Move = "allow_member_move";
        public static final String Is_Clean_Owner = "is_clean_owner";
        public static final String Allow_Member_View_Log = "allow_member_view_log";
        public static final String Pool_Type = "pool_type";
        public static final String Partner_Id = "partner_id";
        public static final String Limit_Type = "limit_type";
        public static final String Limit_Count = "limit_count";
        public static final String OverTime_Hours = "overtime_hours";
        public static final String OverTime_Minutes = "overtime_minutes";
        public static final String Is_Deleted = "is_deleted";
        public static final String Leads_Count = "leads_count";
        public static final String Allocate_OverTime_Hours = "allocate_overtime_hours";
        public static final String Allocate_OverTime_Minutes = "allocate_overtime_minutes";
        public static final String TIME_CLAIM_LIMIT_TYPE = "time_claim_limit_type";
        public static final String TIME_CLAIM_LIMIT_NUM = "time_claim_limit_num";
        public static final String TIME_ALLOCATE_LIMIT_TYPE = "time_allocate_limit_type";
        public static final String TIME_ALLOCATE_LIMIT_NUM = "time_allocate_limit_num";
    }

    class TableName{
        public static final String Recycling_Rule = "biz_recycling_rule";
        public static final String Pool_Permission_Template = "biz_pool_permission_field";
        public static final String Pool_Permission = "biz_pool_permission";
        public static final String Pool_Allocate_Rule = "biz_pool_allocate_rule";
        public static final String Pool_Allocate_Rule_Member = "biz_pool_allocate_rule_member";
        public static final String BIZ_POOL_ALLOCATE_RULE_MEMBER_WEIGHT = "biz_pool_allocate_rule_member_weight";
		public static final String BIZ_POOL_ALLOCATE_RULE_MEMBER_RECORD = "biz_pool_allocate_rule_member_record";
        public static final String Biz_Leads_Pool = "biz_leads_pool";
        public static final String Biz_Leads = "biz_leads";
        public static final String Recycling_Remind_Rule = "biz_recycling_remind_rule";
        public static final String BIZ_POOL_ALLOCATE_RULE_MEMBER_RULE = "biz_pool_allocate_rule_member_rule";
    }
}
