package com.facishare.crm.sfa.lto.loyalty.service;

import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.model.User;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

@Service
public class LoyaltyCommonSwitchService {

    @Resource
    ConfigService configService;

    public static String getTierPointsKey() {
        return "loyalty_tier_points_strategy";
    }

    public boolean isOpenTierPoints(String tenantId) {
        String value = configService.findTenantConfig(User.systemUser(tenantId), getTierPointsKey());
        return StringUtils.isEmpty(value) || "opened".equals(value);
    }

}
