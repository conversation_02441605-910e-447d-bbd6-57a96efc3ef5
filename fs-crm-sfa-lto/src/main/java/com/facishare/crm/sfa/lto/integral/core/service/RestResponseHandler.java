package com.facishare.crm.sfa.lto.integral.core.service;

import com.facishare.crm.sfa.lto.integral.common.constant.IntegralErrorCode;
import com.facishare.crm.sfa.lto.integral.core.rest.dto.BaseEngine;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.util.StringMessage;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class RestResponseHandler {
    /**
     * 引擎响应处理
     */
    public static void checkEngineResponse(BaseEngine.Result<?> response, RequestContext context) {
        int errCode = response.getErrCode();
        if (errCode == IntegralErrorCode.SUCCESS) {
            return;
        }

        if (IntegralErrorCode.ERROR_API_DUPLICATE == errCode) {
            log.warn("tenantId:{}, userId:{}, 规则ApiName重复", context.getTenantId(), context.getUser().getUserId());
            throw new ValidateException(StringMessage.getString(
                    IntegralErrorCode.API_NAME_DUPLICATED.getMessage()));
        } else if (IntegralErrorCode.ERROR_NAME_DUPLICATE == errCode) {
            log.warn("tenantId:{}, userId:{}, 规则名称重复", context.getTenantId(), context.getUser().getUserId());
            throw new ValidateException(StringMessage.getString(
                    IntegralErrorCode.LABEL_DUPLICATED.getMessage()));
        } else if (IntegralErrorCode.ERROR_API_NOT_FOUND == errCode) {
            log.warn("tenantId:{}, userId:{}, 规则不存在或已被删除", context.getTenantId(), context.getUser().getUserId());
            throw new ValidateException(StringMessage.getString(
                    IntegralErrorCode.RULE_NOT_FOUND.getMessage()));
        } else {
            log.error("tenantId:{}, userId:{}, 规则引擎响应异常", context.getTenantId(), context.getUser().getUserId());
            throw new ValidateException(response.getErrMessage(), response.getErrCode());
        }
    }

    public static void checkEngineResponse(BaseEngine.Result<?> response, String tenantId, String userId) {
        int errCode = response.getErrCode();
        if (errCode == IntegralErrorCode.SUCCESS) {
            return;
        }

        if (IntegralErrorCode.ERROR_API_DUPLICATE == errCode) {
            log.warn("tenantId:{}, userId:{}, 规则ApiName重复", tenantId, userId);
            throw new ValidateException(StringMessage.getString(
                    IntegralErrorCode.API_NAME_DUPLICATED.getMessage()));
        } else if (IntegralErrorCode.ERROR_NAME_DUPLICATE == errCode) {
            log.warn("tenantId:{}, userId:{}, 规则名称重复", tenantId, userId);
            throw new ValidateException(StringMessage.getString(
                    IntegralErrorCode.LABEL_DUPLICATED.getMessage()));
        } else if (IntegralErrorCode.ERROR_API_NOT_FOUND == errCode) {
            log.warn("tenantId:{}, userId:{}, 规则不存在或已被删除", tenantId, userId);
            throw new ValidateException(StringMessage.getString(
                    IntegralErrorCode.RULE_NOT_FOUND.getMessage()));
        } else {
            log.error("tenantId:{}, userId:{}, 规则引擎响应异常", tenantId, userId);
            throw new ValidateException(response.getErrMessage(), response.getErrCode());
        }
    }
}
