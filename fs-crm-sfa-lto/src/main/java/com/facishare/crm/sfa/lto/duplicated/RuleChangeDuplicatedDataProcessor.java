package com.facishare.crm.sfa.lto.duplicated;

import com.facishare.crm.sfa.lto.duplicated.models.DuplicatedModels;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class RuleChangeDuplicatedDataProcessor extends AbstractDuplicatedDataProcessor {
    @Override
    public String getProcessorModel() {
        return "RULE_CHANGE_PROCESSOR";
    }

    @Override
    public boolean matchProcessor(DuplicatedModels.TriggerAction triggerAction) {
        return DuplicatedModels.TriggerAction.RULE_CHANGE.equals(triggerAction)
                || DuplicatedModels.TriggerAction.NONE.equals(triggerAction);
    }

    @Override
    public void process(DuplicatedDataProcessArg processArg) {
        List<IObjectData> objectDataList = processArg.getDataList();
        User user = processArg.getUser();
        if(skipLeadsDuplicated(processArg.getUser().getTenantId())) {
            return;
        }
        int refreshVersion = duplicatedProcessingRuleService.getRefreshVersion(user);
        if (CollectionUtils.isEmpty(objectDataList) || processArg.getRefreshVersion() < refreshVersion) {
            return;
        }

        List<DuplicatedModels.DuplicatedProcessing> processingRuleList =
                duplicatedProcessingRuleService.getDuplicatedProcessingList(user, true);
        process(user, objectDataList, processingRuleList, processArg.getAction(), refreshVersion);
    }
}