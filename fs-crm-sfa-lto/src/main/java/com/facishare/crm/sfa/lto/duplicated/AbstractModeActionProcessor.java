package com.facishare.crm.sfa.lto.duplicated;

import com.facishare.crm.sfa.lto.common.models.LtoFieldApiConstants;
import com.facishare.crm.sfa.lto.duplicated.models.DuplicatedModels;
import com.facishare.paas.appframework.core.model.InfraServiceFacade;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public abstract class AbstractModeActionProcessor implements ModeActionProcessor {
    @Autowired
    protected ServiceFacade serviceFacade;
    @Autowired
    protected InfraServiceFacade infraServiceFacade;

    protected static int batchSize = 100;
    static {
        ConfigFactory.getConfig("fs-crm-sales-config", config -> batchSize = config.getInt("duplicated_process_batch_size", 100));
    }

    @Override
    public DuplicatedModels.ModeAction getModelAction() {
        return DuplicatedModels.ModeAction.NONE;
    }

    @Override
    public void process(User user, DuplicatedModels.DuplicatedProcessingMode processingMode,
                                                  IObjectData freshData, List<IObjectData> duplicatedDataList,
                                                  DuplicatedModels.TriggerAction triggerAction) {
    }

    /*
    根据处理规则策略取归集到线索或生成行为记录的线索，默认取创建时间最早的线索
     */
    protected IObjectData getCollectedToLeads(DuplicatedModels.DuplicatedProcessingMode processingMode,
                                              List<IObjectData> duplicatedDataList, IObjectData refreshData) {
        DuplicatedModels.ModeRule modeRule = processingMode.getModeRule();
        String fieldName = LtoFieldApiConstants.CREATE_TIME;
        DuplicatedModels.SortDirection sortDirection = DuplicatedModels.SortDirection.ASC;
        if (modeRule != null) {
            DuplicatedModels.SelectionStrategy strategy = modeRule.getSelectionStrategy();
            if (strategy != null) {
                fieldName = strategy.getFieldName();
                sortDirection = strategy.getSortDirection();
            }
        }
        List<IObjectData> oriDataList = Lists.newArrayList(duplicatedDataList);
        if(refreshData != null && oriDataList.stream().noneMatch(x -> x.getId().equals(refreshData.getId()))) {
            oriDataList.add(refreshData);
        }
        List<IObjectData> dataList = sort(fieldName, oriDataList, sortDirection);
        IObjectData collectedToLeads = null;
        if (CollectionUtils.isNotEmpty(dataList)) {
            collectedToLeads = dataList.get(0);
        }
        return collectedToLeads;
    }

    protected List<IObjectData> sort(String fieldName, List<IObjectData> dataList, DuplicatedModels.SortDirection sortDirection) {
        final String f = fieldName;
        dataList = dataList.stream().filter(d -> d.get(f) != null).collect(Collectors.toList());
        Collections.sort(dataList, (o1, o2) -> {
            //目前只有时间字段
            Long c1 = o1.get(f, Long.class);
            if (c1 == null) {
                return 1;
            }
            Long c2 = o2.get(f, Long.class);
            if (c2 == null) {
                return 1;
            }
            if (DuplicatedModels.SortDirection.ASC.equals(sortDirection)) {
                return c1.compareTo(c2);
            } else {
                return c2.compareTo(c1);
            }
        });
        return dataList;
    }
}