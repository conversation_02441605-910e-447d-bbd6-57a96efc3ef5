package com.facishare.crm.sfa.lto.integral.core.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

public interface BehaviorInfo {
    @Data
    class BehaviorCategory {
        @JSONField(name = "category_api_name")
		@JsonProperty("category_api_name")
        String categoryApiName;
        @JSONField(name = "category_label")
		@JsonProperty("category_label")
        String categoryLabel;
        List<BehaviorAction> actions;
        List<BehaviorMaterial> materials;
        Boolean isActive;
    }

    @Data
    class BehaviorAction {
        @JSONField(name = "action_api_name")
		@JsonProperty("action_api_name")
        String actionApiName;
        @JSONField(name = "action_label")
		@JsonProperty("action_label")
        String actionLabel;
    }

    @Data
    class BehaviorMaterial {
        @JSONField(name = "material_api_name")
		@JsonProperty("material_api_name")
        String materialApiName;
        @JSONField(name = "material_label")
		@JsonProperty("material_label")
        String materialLabel;
    }
}
