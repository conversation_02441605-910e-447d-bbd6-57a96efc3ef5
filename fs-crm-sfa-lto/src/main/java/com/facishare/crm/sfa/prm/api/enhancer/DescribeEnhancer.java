package com.facishare.crm.sfa.prm.api.enhancer;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.Collection;
import java.util.Map;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-03-05
 * ============================================================
 */
public interface DescribeEnhancer {
    /**
     * 查询对象描述，不存在返回 null
     *
     * @param user          用户
     * @param objectApiName 对象 ApiName
     * @return 对象描述
     */
    IObjectDescribe fetchObject(User user, String objectApiName);

    /**
     * 查询对象描述，不存在返回 null
     *
     * @param context       上下文
     * @param user          用户
     * @param objectApiName 对象 ApiName
     * @return 对象描述
     */
    IObjectDescribe fetchObject(RequestContext context, User user, String objectApiName);

    /**
     * 查询多个对象描述
     *
     * @param user          用户
     * @param apiNames 对象 ApiName
     * @return 对象描述
     */
    Map<String, IObjectDescribe> fetchObjects(User user, Collection<String> apiNames);

    /**
     * 查询多个对象描述
     *
     * @param context       上下文
     * @param user          用户
     * @param apiNames 对象 ApiName
     * @return 对象描述
     */
    Map<String, IObjectDescribe> fetchObjects(RequestContext context, User user, Collection<String> apiNames);
}
