package com.facishare.crm.sfa.lto.utils;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.ServiceFacadeImpl;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description
 * <AUTHOR>
 * @Date 2021/2/2 16:58
 */
@Slf4j
public class ObjectDescribeUtils {

    private static final ServiceFacade SERVICE_FACADE = SpringUtil.getContext().getBean(ServiceFacadeImpl.class);

    /**
     * 获取描述
     *
     * @return
     */
    public static IObjectDescribe getObjectDescribe(String tenantId,String apiName) {
        IObjectDescribe objectDescribe = SERVICE_FACADE.findObject(tenantId, apiName);
        if (objectDescribe == null) {
            log.warn("ObjectDescribeUtils.getObjectDescribe>获取描述失败={},{}", tenantId, apiName);
            throw new ValidateException(I18N.text(SfaLtoI18NKeyUtil.SFA_ACCOUNT_GETDESCRIPTIONFAILED)); // 获取描述失败！请重新尝试！
        }
        return objectDescribe;
    }
}
