package com.facishare.crm.sfa.lto.loyalty.service;

import com.facishare.crm.sfa.lto.loyalty.constants.LoyaltyConstants;
import com.facishare.crm.sfa.lto.loyalty.model.Loyalty;
import com.facishare.crm.sfa.lto.loyalty.utils.LoyaltyThreshold;
import com.facishare.crm.sfa.lto.loyalty.utils.LoyaltyUtils;
import com.facishare.crm.sfa.lto.loyalty.utils.NumberUtils;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.search.AggFunctionArg;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.IGroupByParameter;
import com.facishare.paas.metadata.impl.search.GroupByParameter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.api.IdGenerator;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class LoyaltyPointsDetailService {

    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private LoyaltyEvaluationService loyaltyEvaluationService;

    /**
     * 根据积分明细统计回填会员积分
     * 返回更新的字段map
     */
    public Map<String, Object> fillPoints(String tenantId, String memberId) {
        Map<String, Object> updateFields = new HashMap<>();
        updateFields.put(LoyaltyConstants.LoyaltyMember.GRADING_POINTS, 0);
        updateFields.put(LoyaltyConstants.LoyaltyMember.CONSUMER_POINTS, 0);
        updateFields.put(LoyaltyConstants.LoyaltyMember.FROZEN_GRADING_POINTS, 0);
        updateFields.put(LoyaltyConstants.LoyaltyMember.FROZEN_POINTS, 0);
        //消费积分回填
        long[] consumerPoints = fillPointsByFilter(tenantId, memberId, false, new ArrayList<>());
        updateFields.put(LoyaltyConstants.LoyaltyMember.CONSUMER_POINTS, consumerPoints[0]);
        updateFields.put(LoyaltyConstants.LoyaltyMember.FROZEN_POINTS, consumerPoints[1]);
        //查询上次评定日,用于过滤上次评定日还未处理的定级积分
        long lastEvaluationDate = loyaltyEvaluationService.getLastEvaluationDate(tenantId, memberId);
        List<IFilter> tierPointsFilters = new ArrayList<>();
        SearchUtil.fillFilterGT(tierPointsFilters, IObjectData.CREATE_TIME, lastEvaluationDate);
        long[] tierPoints = fillPointsByFilter(tenantId, memberId, true, tierPointsFilters);
        updateFields.put(LoyaltyConstants.LoyaltyMember.GRADING_POINTS, tierPoints[0]);
        updateFields.put(LoyaltyConstants.LoyaltyMember.FROZEN_GRADING_POINTS, tierPoints[1]);
        return updateFields;
    }

    /**
     * @param isQualifying 是否定级积分
     * @return 根据参数统计积分总和
     * 返回数组：
     * 第一个返回值代表可用积分总和
     * 第二个返回值代表冻结积分总和
     */
    private long[] fillPointsByFilter(String tenantId, String memberId, boolean isQualifying, List<IFilter> filters) {
        if (CollectionUtils.isEmpty(filters)) {
            filters = Lists.newArrayList();
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchUtil.fillFilterEq(filters, LoyaltyConstants.LoyaltyPointsDetail.MEMBER_ID, memberId);
        SearchUtil.fillFilterIn(filters, LoyaltyConstants.LoyaltyPointsDetail.POINTS_STATUS, Lists.newArrayList(
                        LoyaltyConstants.LoyaltyPointsDetail.PointsStatus.Available.toString(),
                        LoyaltyConstants.LoyaltyPointsDetail.PointsStatus.Frozen.toString()
                )
        );
        IFilter iFilter = SearchUtil.filter(LoyaltyConstants.LoyaltyPointsDetail.IS_QUALIFYING, Operator.EQ, isQualifying);
        iFilter.setIsMasterField(true);
        filters.add(iFilter);
        query.setFilters(filters);
        AggFunctionArg aggFunctionArg = AggFunctionArg.builder()
                .aggFunction(Count.TYPE_SUM)
                .aggField(LoyaltyConstants.LoyaltyPointsDetail.AVAILABLE_POINTS)
                .build();
        IGroupByParameter groupByParameter = new GroupByParameter();
        groupByParameter.setAggFunctions(Lists.newArrayList(aggFunctionArg));
        groupByParameter.setGroupBy(Lists.newArrayList(LoyaltyConstants.LoyaltyPointsDetail.MEMBER_ID, LoyaltyConstants.LoyaltyPointsDetail.POINTS_STATUS));
        query.setGroupByParameter(groupByParameter);
        List<IObjectData> aggregate = serviceFacade.aggregateFindBySearchQuery(User.systemUser(tenantId), query, LoyaltyConstants.LoyaltyPointsDetail.API_NAME);
        long[] res = new long[2];
        for (IObjectData data : aggregate) {
            if ("Available".equals(data.get(LoyaltyConstants.LoyaltyPointsDetail.POINTS_STATUS, String.class))) {
                res[0] = NumberUtils.getLong(data, "sum_available_points");
            } else if ("Frozen".equals(data.get(LoyaltyConstants.LoyaltyPointsDetail.POINTS_STATUS, String.class))) {
                res[1] = NumberUtils.getLong(data, "sum_available_points");
            }
        }
        return res;
    }

    /**
     * 查询员工可用的的全部积分详情
     */
    public List<IObjectData> findMemberPointsDetails(Loyalty.PointsOperationParam param, boolean isQualifying) {
        String tenantId = param.getTenantId();
        String memberId = param.getMemberId();
        List<IFilter> filterList = Lists.newArrayList();
        IFilter iFilter = SearchUtil.filter(LoyaltyConstants.LoyaltyPointsDetail.IS_QUALIFYING, com.facishare.paas.metadata.impl.search.Operator.EQ, isQualifying);
        iFilter.setIsMasterField(true);
        filterList.add(iFilter);
        filterList.addAll(buildFilterByPointsStatus(LoyaltyConstants.LoyaltyPointsDetail.PointsStatus.Available, param.getOperatingTime()));
        return findMemberPointsDetails(tenantId, memberId, filterList);
    }

    public List<IObjectData> findMemberPointsDetails(String tenantId, String memberId, List<IFilter> filterList) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, LoyaltyConstants.LoyaltyPointsDetail.MEMBER_ID, memberId);
        SearchUtil.fillFilterGT(filters, LoyaltyConstants.LoyaltyPointsDetail.AVAILABLE_POINTS, 0L);
        filters.addAll(filterList);
        query.setFilters(filters);
        query.setLimit(LoyaltyThreshold.getPointsDetailsLimit());
        query.setOrders(Lists.newArrayList(new OrderBy(LoyaltyConstants.LoyaltyPointsDetail.EXPIRY_TIME, true), new OrderBy(IObjectData.CREATE_TIME, true)));
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(User.systemUser(tenantId), LoyaltyConstants.LoyaltyPointsDetail.API_NAME, query);
        if (queryResult == null || CollectionUtils.isEmpty(queryResult.getData())) {
            return Lists.newArrayList();
        }
        return queryResult.getData();
    }

    /**
     * 插入积分明细
     */
    public ObjectDataDocument saveLoyaltyPointsDetail(Loyalty.PointsOperationParam param) {
        String tenantId = param.getTenantId();
        IObjectData pointType = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), param.getPointTypeId(), LoyaltyConstants.LoyaltyPointType.API_NAME);
        ObjectDataDocument pointsDetail = new ObjectDataDocument();
        pointsDetail.put(IObjectData.NAME, param.getUniqueId());
        pointsDetail.put(LoyaltyConstants.LoyaltyPointsDetail.PROGRAM_ID, pointType.get(LoyaltyConstants.LoyaltyPointType.PROGRAM_ID, String.class));
        pointsDetail.put(LoyaltyConstants.LoyaltyPointsDetail.POINTS_TYPE_ID, pointType.getId());
        pointsDetail.put(LoyaltyConstants.LoyaltyPointsDetail.MEMBER_ID, param.getMemberId());
        pointsDetail.put(LoyaltyConstants.LoyaltyPointsDetail.MEMBER_BEHAVIOR, param.getDesc());
        pointsDetail.put(LoyaltyConstants.LoyaltyPointsDetail.POINTS_QUANTITY, param.getValue());
        pointsDetail.put(LoyaltyConstants.LoyaltyPointsDetail.AVAILABLE_POINTS, param.getValue());
        Long effectiveTime = LoyaltyUtils.formulaEffectiveTime(System.currentTimeMillis(), pointType);
        pointsDetail.put(LoyaltyConstants.LoyaltyPointsDetail.EFFECTIVE_TIME, effectiveTime);
        pointsDetail.put(LoyaltyConstants.LoyaltyPointsDetail.EXPIRY_TIME, LoyaltyUtils.formulaExpiryTime(effectiveTime, pointType));
        if (System.currentTimeMillis() >= effectiveTime) {
            pointsDetail.put(LoyaltyConstants.LoyaltyPointsDetail.POINTS_STATUS, LoyaltyConstants.LoyaltyPointsDetail.PointsStatus.Available.toString());
        } else {
            pointsDetail.put(LoyaltyConstants.LoyaltyPointsDetail.POINTS_STATUS, LoyaltyConstants.LoyaltyPointsDetail.PointsStatus.Frozen.toString());
        }
        pointsDetail.put(IObjectData.ID, IdGenerator.get());
        pointsDetail.put(IObjectData.DESCRIBE_API_NAME, LoyaltyConstants.LoyaltyPointsDetail.API_NAME);
        pointsDetail.put(IObjectData.TENANT_ID, tenantId);
        pointsDetail.put(IObjectData.RECORD_TYPE, IObjectData.RECORD_TYPE_DEFAULT);
        serviceFacade.saveObjectData(User.systemUser(tenantId), pointsDetail.toObjectData());
        return pointsDetail;
    }

    public static List<IFilter> buildFilterByPointsStatus(LoyaltyConstants.LoyaltyPointsDetail.PointsStatus status, long time) {
        List<IFilter> filters = Lists.newArrayList();
        if (LoyaltyConstants.LoyaltyPointsDetail.PointsStatus.Clear.equals(status)) {
            SearchUtil.fillFilterEq(filters, LoyaltyConstants.LoyaltyPointsDetail.POINTS_STATUS, LoyaltyConstants.LoyaltyPointsDetail.PointsStatus.Clear.toString());
        } else {
            SearchUtil.fillFilterNotEq(filters, LoyaltyConstants.LoyaltyPointsDetail.POINTS_STATUS, LoyaltyConstants.LoyaltyPointsDetail.PointsStatus.Clear.toString());
            if (LoyaltyConstants.LoyaltyPointsDetail.PointsStatus.Expired.equals(status)) {
                SearchUtil.fillFilterLT(filters, LoyaltyConstants.LoyaltyPointsDetail.EXPIRY_TIME, time);
            } else if (LoyaltyConstants.LoyaltyPointsDetail.PointsStatus.Frozen.equals(status)) {
                SearchUtil.fillFilterGT(filters, LoyaltyConstants.LoyaltyPointsDetail.EFFECTIVE_TIME, time);
            } else if (LoyaltyConstants.LoyaltyPointsDetail.PointsStatus.Exhausted.equals(status)) {
                SearchUtil.fillFilterEq(filters, LoyaltyConstants.LoyaltyPointsDetail.AVAILABLE_POINTS, 0);
            } else {
                SearchUtil.fillFilterLTE(filters, LoyaltyConstants.LoyaltyPointsDetail.EFFECTIVE_TIME, time);
                SearchUtil.fillFilterGTE(filters, LoyaltyConstants.LoyaltyPointsDetail.EXPIRY_TIME, time);
                SearchUtil.fillFilterGT(filters, LoyaltyConstants.LoyaltyPointsDetail.AVAILABLE_POINTS, 0);
            }
        }
        return filters;
    }

    /**
     * 1积分增加
     * 2积分减少
     */
    public static void setPointsStatus(IObjectData pointsDetail, LoyaltyConstants.LoyaltyPointsDetail.PointsTrend pointsTrend) {
        String pointsStatus = pointsDetail.get(LoyaltyConstants.LoyaltyPointsDetail.POINTS_STATUS, String.class);
        if (pointsTrend == LoyaltyConstants.LoyaltyPointsDetail.PointsTrend.increase) {
            if (LoyaltyConstants.LoyaltyPointsDetail.PointsStatus.Exhausted.toString().equals(pointsStatus)) {
                pointsDetail.set(LoyaltyConstants.LoyaltyPointsDetail.POINTS_STATUS, LoyaltyConstants.LoyaltyPointsDetail.PointsStatus.Available.toString());
            }
        } else if (pointsTrend == LoyaltyConstants.LoyaltyPointsDetail.PointsTrend.decrease) {
            if (NumberUtils.getLong(pointsDetail, LoyaltyConstants.LoyaltyPointsDetail.AVAILABLE_POINTS) == 0) {
                if (LoyaltyConstants.LoyaltyPointsDetail.PointsStatus.Available.toString().equals(pointsStatus)) {//用完前是可用的
                    pointsDetail.set(LoyaltyConstants.LoyaltyPointsDetail.POINTS_STATUS, LoyaltyConstants.LoyaltyPointsDetail.PointsStatus.Exhausted.toString());
                }
            }
        }
    }

}
