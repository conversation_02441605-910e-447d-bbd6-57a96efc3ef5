package com.facishare.crm.sfa.lto.accountreerelation.models;

public class AccountTreeRelationConstants {
    public static final String EQUITY_TABLE_NAME = "biz_equity_relation_temp";
    public static final String RULE_CONFIG_KEY = "generate_account_relation_tree_rule";
    public static final String COMMON_RULE_CONFIG_KEY = "generate_relation_tree_rule_";
    public static final String ACCOUNT_TREE_RELATION_API_NAME = "AccountTreeRelationObj";
    public static final String ACCOUNT_TREE_RELATION_LOG_API_NAME = "AccountTreeRelationLogObj";
    public static final String ENTERPRISE_ID = "enterprise_id";
    public static final String NODE_TYPE = "node_type";
    public static final String IS_LEAF = "is_leaf";
    public static final String PERCENT = "percent";
    public static final String ROOT_PERCENT = "root_percent";
    public static final String ROOT_ID = "root_id";
    public static final String ID = "id";
    public static final String NAME = "name";
    public static final String PARENT_ID = "parent_id";
    public static final String ACCOUNT_MAIN_DATA_ID = "account_main_data_id";
    public static final String PARENT_ENTERPRISE_ID = "parent_enterprise_id";
    public static final String PARENT_ACCOUNT_MAIN_DATA_ID = "parent_account_main_data_id";
    public static final String IS_STATISTICS_INCLUDE = "is_statistics_include";
    public static final String IS_ROOT = "is_root";
    public static final String IS_BRANCH = "is_branch";
    public static final String TOTAL_ROOT_PERCENT = "total_root_percent";
    public static final String ROOT_ENTERPRISE_ID = "root_enterprise_id";
    public static final String ROOT_ACCOUNT_MAIN_DATA_ID = "root_account_main_data_id";
    public static final String TREE_PATH = "tree_path";
    public static final String LEVEL = "level";
    public static final String IS_AUTO_UPDATE = "is_auto_update";
    public static final String IS_MANUAL_ADD = "is_manual_add";
    public static final String RELATION_VERSION = "relation_version";
    public static final String MATCH_RULE_CONFIG_KEY = "generate_account_relation_tree_match_rule";
    public static final String MATCH_ENTERPRISE_RULE_CONFIG_KEY = "generate_relation_tree_enterprise_match_rule";
    public static final String OBJECT_API_NAME = "object_api_name";


    private AccountTreeRelationConstants() {
        throw new IllegalStateException("Utility class");
    }

}
