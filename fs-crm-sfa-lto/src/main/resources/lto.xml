<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="restServiceProxyFactory" class="com.facishare.rest.core.RestServiceProxyFactory"
          p:configName="fs-paas-appframework-rest" init-method="init"/>

    <bean id="ltoDuplicatedRestServiceProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.lto.rest.DuplicatedRestServiceProxy" >
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>
    <bean id="sfaIndustryInterfaceProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.lto.rest.SFAIndustryInterfaceProxy" >
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>
    <bean id="ltoPaasUserRoleProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.lto.rest.PaasUserRoleProxy">
        <property name="factory" ref="restServiceProxyFactory">
        </property>
    </bean>
    <bean id="ltoPaasUserGroupProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.lto.rest.PaasUserGroupProxy">
        <property name="factory" ref="restServiceProxyFactory">
        </property>
    </bean>
    <bean id="ltoCustomProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.lto.rest.LtoCustomProxy">
        <property name="factory" ref="restServiceProxyFactory">
        </property>
    </bean>
    <bean id="ruleEngineRestProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.lto.integral.core.proxy.IntegralRuleEngineRestProxy">
        <property name="factory" ref="restServiceProxyFactory" />
    </bean>

    <bean id="enterpriseWeChatServiceProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.lto.qywx.proxy.WecomServiceProxy" >
        <property name="factory" ref="restServiceProxyFactory">
        </property>
    </bean>

    <bean id="conversionDataStore" class="com.github.mongo.support.MongoDataStoreFactoryBean">
        <property name="configName" value="fs-open-qywx-message-save"/>
        <property name="sectionNames" value="messageMongo"/>
    </bean>

    <bean id="conversionMongoDao" class="com.facishare.crm.sfa.lto.qywx.mongo.ConversionMongoDao">
        <property name="datastore" ref="conversionDataStore"/>
    </bean>

    <bean id="equityRelationshipDataMongoDbStore" class="com.github.mongo.support.MongoDataStoreFactoryBean">
        <property name="configName" value="fs-crm-follow-crm-action-consumer"/>
        <property name="sectionNames" value="equityRelationshipMongo"/>
    </bean>

    <bean id="equityRelationshipDataMongoDbDao" class="com.facishare.crm.sfa.lto.equityrelationship.dao.EquityRelationshipDataMongoDbDao">
        <property name="datastore" ref="equityRelationshipDataMongoDbStore"/>
    </bean>

    <bean id="activityDataMongoDbStore" class="com.github.mongo.support.MongoDataStoreFactoryBean">
        <property name="configName" value="fs-crm-follow-crm-action-consumer"/>
        <property name="sectionNames" value="activityMongo"/>
    </bean>

    <bean id="httpClientSupport" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean" p:configName="http-spring-support-proxy"/>

    <bean id="SFAJedisCmd" class="com.github.jedis.support.JedisFactoryBean" p:configName="fs-crm-sfa-redis"/>

    <bean id="loyaltyMq" class="com.fxiaoke.rocketmq.producer.AutoConfMQProducer"
          init-method="start" destroy-method="close">
        <constructor-arg name="configName" value="fs-crm-task-sfa-mq.ini"/>
        <constructor-arg name="sectionNames" value="loyalty-member"/>
    </bean>

</beans>
