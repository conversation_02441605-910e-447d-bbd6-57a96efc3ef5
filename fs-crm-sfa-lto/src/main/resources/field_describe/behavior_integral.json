{"api_name": "behavior_integral", "decimal_places": 2, "define_type": "package", "description": "行为积分", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "default_is_expression": false, "default_to_zero": true, "label": "行为积分", "length": 14, "round_mode": 4, "status": "released", "type": "number", "help_text": "通过后台【行为积分】功能自动输出结果", "config": {"edit": 1, "enable": 1, "attrs": {"is_required": 1, "label": 1, "max_length": 1, "decimal_places": 1, "help_text": 1}}}