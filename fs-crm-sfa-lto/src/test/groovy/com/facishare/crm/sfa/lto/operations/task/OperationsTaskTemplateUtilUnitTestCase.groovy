package com.facishare.crm.sfa.lto.operations.task


import com.facishare.crm.sfa.lto.operations.OperationsConstant
import com.facishare.paas.metadata.api.DBRecord
import com.facishare.paas.metadata.api.action.IActionContext
import com.facishare.paas.metadata.api.service.ICommonSqlService
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.search.WhereParam
import spock.lang.Specification

class OperationsTaskTemplateUtilUnitTestCase extends Specification {
    def "saveMemberField"() {
        given:
        def list = new ArrayList<Map<String, Object>>()
        def commonSqlService = Mock(ICommonSqlService) {
            insert(_ as String, _ as List<Map<String, Object>>, _ as IActionContext) >> {
                arguments ->
                    list.addAll((List) arguments.get(1))
                    list.size()
            }
        }
        def t1 = new ObjectData("_id": "1", "tenant_id": "79337", "task_cc": member(), "task_executors": member())
        def t2 = new ObjectData("_id": "1")
        def t3 = new ObjectData("tenant_id": "79337")
        OperationsTaskTemplateUtil.saveMemberField(t1, commonSqlService)
        OperationsTaskTemplateUtil.saveMemberField(t2, commonSqlService)
        OperationsTaskTemplateUtil.saveMemberField(t3, commonSqlService)
        list.forEach { e ->
            e.remove(DBRecord.CREATE_TIME)
            e.remove("id")
        }
        expect:
        list == members()
    }

    def "getMemberField"() {
        given:
        def commonSqlService = Mock(ICommonSqlService) {
            select(_ as String, _ as List<WhereParam>, _ as IActionContext) >> {
                members()
            }
        }
        def t1 = new ObjectData("_id": "1", "tenant_id": "79337")
        def t2 = new ObjectData("_id": "1", "tenant_id": "79337")
        OperationsTaskTemplateUtil.getMemberField(t1, commonSqlService)
        OperationsTaskTemplateUtil.getMemberField([t2], commonSqlService)
        expect:
        t1.get(OperationsConstant.TASK_CC) == member()
        t1.get(OperationsConstant.TASK_EXECUTORS) == member()
        t2.get(OperationsConstant.TASK_CC) == member()
        t2.get(OperationsConstant.TASK_EXECUTORS) == member()
    }

    def "deleteMemberField"() {
        given:
        def params = new ArrayList<WhereParam>()
        def commonSqlService = Mock(ICommonSqlService) {
            delete(_ as String, _ as List<WhereParam>, _ as IActionContext) >> {
                arguments ->
                    params.addAll((List) arguments.get(1))
                    1
            }
        }
        def t1 = new ObjectData("_id": templateId, "tenant_id": tenantId)
        def t2 = new ObjectData("_id": templateId,)
        def t3 = new ObjectData("tenant_id": tenantId)
        OperationsTaskTemplateUtil.deleteMemberField(t1, commonSqlService)
        OperationsTaskTemplateUtil.deleteMemberField(t2, commonSqlService)
        OperationsTaskTemplateUtil.deleteMemberField(t3, commonSqlService)
        expect:
        params.size() == 3
        params.stream().anyMatch { param -> "tenant_id" == param.getColumn() && tenantId == param.getValue().get(0) }
        params.stream().anyMatch { param -> "rule_id" == param.getColumn() && templateId == param.getValue().get(0) }
        where:
        tenantId | templateId
        "79337"   | "1"
    }

    private static Map<String, Object> member() {
        return ["person": ["1076"], "ext_process": ["owner", "data_group", "data_owner_leader"]]
    }

    private static List<Map<String, Object>> members() {
        [['tenant_id': '79337', 'rule_id': '1', 'member_type': 1, 'member_id': '1076', 'is_deleted': 0, 'object_describe_api_name': 'OperationsTaskTemplateObj.task_cc', 'created_by': '-10000'],
         ['tenant_id': '79337', 'rule_id': '1', 'member_type': 0, 'member_id': 'owner', 'is_deleted': 0, 'object_describe_api_name': 'OperationsTaskTemplateObj.task_cc', 'created_by': '-10000'],
         ['tenant_id': '79337', 'rule_id': '1', 'member_type': 0, 'member_id': 'data_group', 'is_deleted': 0, 'object_describe_api_name': 'OperationsTaskTemplateObj.task_cc', 'created_by': '-10000'],
         ['tenant_id': '79337', 'rule_id': '1', 'member_type': 0, 'member_id': 'data_owner_leader', 'is_deleted': 0, 'object_describe_api_name': 'OperationsTaskTemplateObj.task_cc', 'created_by': '-10000'],
         ['tenant_id': '79337', 'rule_id': '1', 'member_type': 1, 'member_id': '1076', 'is_deleted': 0, 'object_describe_api_name': 'OperationsTaskTemplateObj.task_executors', 'created_by': '-10000'],
         ['tenant_id': '79337', 'rule_id': '1', 'member_type': 0, 'member_id': 'owner', 'is_deleted': 0, 'object_describe_api_name': 'OperationsTaskTemplateObj.task_executors', 'created_by': '-10000'],
         ['tenant_id': '79337', 'rule_id': '1', 'member_type': 0, 'member_id': 'data_group', 'is_deleted': 0, 'object_describe_api_name': 'OperationsTaskTemplateObj.task_executors', 'created_by': '-10000'],
         ['tenant_id': '79337', 'rule_id': '1', 'member_type': 0, 'member_id': 'data_owner_leader', 'is_deleted': 0, 'object_describe_api_name': 'OperationsTaskTemplateObj.task_executors', 'created_by': '-10000']] as List<Map<String, Object>>
    }
}
