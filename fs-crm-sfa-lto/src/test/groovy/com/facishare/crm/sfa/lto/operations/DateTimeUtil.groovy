package com.facishare.crm.sfa.lto.operations

import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

class DateTimeUtil {
    static final def TIMEZONE = TimeZone.getTimeZone("GMT+8")
    static final def FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")

    static Date parse(String str) {
        Date.from(LocalDateTime.from(FORMATTER.parse(str)).atZone(TIMEZONE.toZoneId()).toInstant())
    }

    static String format(Date date) {
        FORMATTER.format(LocalDateTime.ofInstant(date.toInstant(), TIMEZONE.toZoneId()))
    }
}
