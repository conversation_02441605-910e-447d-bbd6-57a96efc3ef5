package com.facishare.crm.sfa.lto.operations

import com.facishare.paas.license.arg.JudgeModuleArg
import com.facishare.paas.license.common.Result
import com.facishare.paas.license.http.LicenseClient
import com.facishare.paas.license.pojo.JudgeModulePojo
import spock.lang.Specification

class OperationsUtilUnitTestCase extends Specification {
    def "hasLicense"() {
        def licenseClient = Mock(LicenseClient) {
            judgeModule(_ as JudgeModuleArg) >> new Result<JudgeModulePojo>()
        }
        given:
        def hasLicense = OperationsUtil.hasLicense("1", licenseClient)
        expect:
        !hasLicense
    }
}
