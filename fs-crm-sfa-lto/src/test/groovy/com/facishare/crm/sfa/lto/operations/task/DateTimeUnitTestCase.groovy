package com.facishare.crm.sfa.lto.operations.task

import com.facishare.crm.sfa.lto.operations.DateTimeUtil
import com.facishare.paas.expression.ExpressionServiceImpl
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.TextFieldDescribe
import spock.lang.Specification

class DateTimeUnitTestCase extends Specification {
    def "replace"() {
        given:
        def expressionService = new ExpressionServiceImpl()
        def templateField = new DateTime(raw, OperationsTask.&setDeadline, expressionService)
        def data = new ObjectData("create_time": parse(base))
        def describe = new TextFieldDescribe()
        describe.setApiName("create_time")
        Map<String, Value> valueMap = new HashMap<>()
        valueMap.put("AccountObj.create_time", new Value(describe, data))
        Object replaced = templateField.replace(valueMap)
        expect:
        if (expected instanceof String) {
            replaced == parse(expected)
        } else {
            replaced <= expected
        }
        where:
        base                  | raw                                     | expected
        "2020-01-01 00:00:00" | "AccountObj.create_time"                | "2020-01-01 00:00:00"
        "2020-01-01 00:00:00" | "AccountObj.create_time + DAYS(1)"      | "2020-01-02 00:00:00"
        "2020-01-01 00:00:00" | "AccountObj.create_time + MONTHS(1)"    | "2020-02-01 00:00:00"
        "2020-01-01 00:00:00" | "AccountObj.create_time + YEARS(1)"     | "2021-01-01 00:00:00"
        "2020-01-01 00:00:00" | parse("2020-01-01 00:00:00").toString() | "2020-01-01 00:00:00"
        "2020-01-01 00:00:00" | "NOW() + DAYS(1)"                       | System.currentTimeMillis()
    }

    static long parse(String raw) {
        DateTimeUtil.parse(raw).getTime()
    }
}
