package com.facishare.crm.sfa.lto.operations.task.execution

import com.facishare.crm.sfa.lto.operations.DateTimeUtil
import spock.lang.Specification

import java.time.LocalTime

class TaskExecutionUtilUnitTestCase extends Specification {
    def "getNextTimeAfter"() {
        given:
        def localTime = LocalTime.of(2, 0, 0)
        def next = DateTimeUtil.parse(after)
        def nextAfter = TaskExecutionUtil.getNextTimeAfter(frequency, exp, localTime, DateTimeUtil.TIMEZONE, next)
        expect:
        if (nextAfter != null) {
            DateTimeUtil.format(nextAfter) == expected + " " + localTime.toString() + ":00"
        } else {
            expected == ""
        }
        where:
        frequency                        | exp                        | after                 | expected
        TaskExecutionFrequency.REBOOT    | "1662825600000"            | "2022-09-11 00:00:00" | "2022-09-11"
        TaskExecutionFrequency.REBOOT    | "1711987200000"            | "2024-04-02 15:33:28" | ""
        TaskExecutionFrequency.DAILY     | null                       | "2022-09-11 00:00:00" | "2022-09-11"
        TaskExecutionFrequency.WEEKLY    | "2"                        | "2024-03-27 18:37:00" | "2024-04-01"
        TaskExecutionFrequency.WEEKLY    | "MON"                      | "2024-03-27 18:37:00" | "2024-04-01"
        TaskExecutionFrequency.MONTHLY   | "L"                        | "2022-09-11 00:00:00" | "2022-09-30"
        TaskExecutionFrequency.QUARTERLY | "3|L"                      | "2022-10-11 00:00:00" | "2022-12-31"
        TaskExecutionFrequency.ANNUALLY  | "0 0 0 ? 1,3,2,4,5 2 2025" | "2024-03-27 18:25:00" | "2025-01-06"
        TaskExecutionFrequency.ANNUALLY  | "0 0 0 ? 1,3,2,4,5 2 2023" | "2024-03-27 18:25:00" | "2025-01-06"
    }
}
