package com.facishare.crm.sfa.lto.operations

import com.facishare.crm.sfa.lto.operations.task.execution.TaskExecutionFrequency
import com.facishare.paas.metadata.api.DBRecord
import com.facishare.paas.metadata.impl.ObjectData
import com.fxiaoke.paas.gnomon.api.NomonProducer
import com.fxiaoke.paas.gnomon.api.entity.NomonDeleteMessage
import com.fxiaoke.paas.gnomon.api.entity.NomonMessage
import spock.lang.Specification

import java.time.LocalTime

class OperationsStrategyProducerUnitTestCase extends Specification {

    def "sendEnableMessage"() {
        given:
        NomonMessage nomonMessage = null
        NomonDeleteMessage nomonDeleteMessage = null
        NomonProducer nomonProducer = Mock(NomonProducer) {
            send(_ as NomonMessage) >> { arguments -> nomonMessage = (arguments as List)[0] as NomonMessage }
            send(_ as NomonDeleteMessage) >> { arguments -> nomonDeleteMessage = (arguments as List)[0] as NomonDeleteMessage }
        }
        OperationsStrategyProducer strategyProducer = new OperationsStrategyProducer(nomonProducer)
        def strategy = new ObjectData(tenant_id: tenantId,
                _id: dataId,
                task_execution_frequency: "weekly",
                task_execution_time: "MON,TUE,WED,THU,FRI,SAT,SUN")
        strategyProducer.sendEnableMessage(strategy, TimeZone.getDefault())
        strategyProducer.sendDisableMessage(strategy)
        expect:
        nomonMessage.getDataId() == dataId
        nomonMessage.getTenantId() == tenantId
        nomonMessage.getExecuteTime() != null
        nomonMessage.getCallArg().contains(DBRecord.CREATE_TIME)
        nomonDeleteMessage.getDataId() == dataId
        nomonDeleteMessage.getTenantId() == tenantId
        where:
        tenantId | dataId
        "1"      | "1"
    }

    @SuppressWarnings("GroovyAccessibility")
    def "resendEnableMessage"() {
        given:
        NomonMessage nomonMessage = null
        NomonProducer nomonProducer = Mock(NomonProducer) {
            send(_ as NomonMessage) >> { arguments -> nomonMessage = (arguments as List)[0] as NomonMessage }
        }
        def localTime = LocalTime.of(12, 0, 0)
        OperationsStrategyProducer strategyProducer = new OperationsStrategyProducer(nomonProducer)
        strategyProducer.@localTime = localTime
        def strategy = new ObjectData(tenant_id: tenantId, _id: dataId, task_execution_time: taskExecutionTime)
        def date = DateTimeUtil.parse(after)
        strategyProducer.resendEnableMessage(strategy, frequency as TaskExecutionFrequency, DateTimeUtil.TIMEZONE, date)
        def executionTime = DateTimeUtil.format(nomonMessage.getExecuteTime())
        expect:
        nomonMessage.getDataId() == dataId
        nomonMessage.getTenantId() == tenantId
        executionTime == expected + " " + localTime.toString() + ":00"
        where:
        tenantId = "1"
        dataId = "1"
        frequency = TaskExecutionFrequency.WEEKLY
        taskExecutionTime = "MON"
        after = "2024-03-27 18:37:00"
        expected = "2024-04-01"
    }

    def "nextInfoAfter"() {
        given:
        def strategy = new ObjectData(task_execution_frequency: frequency, task_execution_time: taskExecutionTime)
        OperationsStrategyProducer strategyProducer = new OperationsStrategyProducer(null)
        def info = strategyProducer.nextInfoAfter(strategy, DateTimeUtil.TIMEZONE, DateTimeUtil.parse(after))
        expect:
        info.isPresent()
        info.get().getLeft() == DateTimeUtil.parse(left)
        info.get().getRight() == DateTimeUtil.parse(right)
        where:
        frequency | taskExecutionTime | after                 | left                  | right
        "weekly"  | "MON"             | "2024-03-27 18:37:00" | "2024-04-01 02:00:00" | "2024-04-01 00:00:00"
    }
}
