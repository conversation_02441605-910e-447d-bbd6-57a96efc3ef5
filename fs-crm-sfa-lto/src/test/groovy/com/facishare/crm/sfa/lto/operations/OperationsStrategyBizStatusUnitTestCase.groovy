package com.facishare.crm.sfa.lto.operations


import spock.lang.Specification

class OperationsStrategyBizStatusUnitTestCase extends Specification {
    def "from"() {
        given:
        def op = OperationsStrategyBizStatus.from(value)
        expect:
        if (op.isPresent()) {
            op.get() == bizStatus
            bizStatus.getValue() != null
            bizStatus.getName() != null
        } else {
            bizStatus == null
        }
        where:
        value | bizStatus
        "1"   | OperationsStrategyBizStatus.ENABLE
        "0"   | OperationsStrategyBizStatus.DISABLE
        "-1"  | null
    }
}
