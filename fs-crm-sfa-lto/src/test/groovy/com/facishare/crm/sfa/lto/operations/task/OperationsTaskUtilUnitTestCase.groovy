package com.facishare.crm.sfa.lto.operations.task

import com.facishare.crm.sfa.lto.common.LtoOrgCommonService
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.expression.ExpressionService
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.QueryResult
import com.facishare.paas.metadata.api.action.IActionContext
import com.facishare.paas.metadata.dao.pg.entity.metadata.RelevantTeam
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
import spock.lang.Specification

class OperationsTaskUtilUnitTestCase extends Specification {
    def serviceFacade = Mock(ServiceFacade)
    def ltoOrgCommonService = Mock(LtoOrgCommonService)
    def expressionService = Mock(ExpressionService)

    def setup() {
        serviceFacade.findObject(_ as String, "AccountObj") >>> new ObjectDescribe(
                "tenant_id": "79337",
                "display_name_r": "客户",
                "display_name": "客户",
                "api_name": "AccountObj",
                "fields": new HashMap<>(
                        "create_time": new HashMap<>(
                                "describe_api_name": "AccountObj",
                                "description": "create_time",
                                "label": "创建时间",
                                "type": "date_time",
                                "api_name": "create_time",
                                "define_type": "system",
                                "date_format": "yyyy-MM-dd HH:mm:ss",
                                "status": "released"
                        ),
                        "leads_id": new HashMap<>(
                                "describe_api_name": "AccountObj",
                                "description": "源线索",
                                "type": "object_reference",
                                "relation_outer_data_privilege": "not_related",
                                "related_where_type": "",
                                "is_required": false,
                                "wheres": [],
                                "label_r": "源线索",
                                "default_value": "",
                                "label": "源线索",
                                "target_api_name": "LeadsObj",
                                "target_related_list_name": "leads_account_list",
                                "target_related_list_label": "客户名称",
                                "action_on_target_delete": "cascade_delete",
                                "api_name": "leads_id",
                        ),
                        "deal_status": new HashMap<>(
                                "describe_api_name": "AccountObj",
                                "description": "成交状态",
                                "type": "select_one",
                                "options": [
                                        new HashMap<>(
                                                "font_color": "#ff8000",
                                                "label": "未成交",
                                                "value": "1"
                                        ),
                                        new HashMap<>(
                                                "font_color": "#936de3",
                                                "label": "已成交",
                                                "value": "2"
                                        ),
                                        new HashMap<>(
                                                "font_color": "#30c776",
                                                "label": "多次成交",
                                                "value": "3"
                                        )
                                ],
                                "define_type": "package",
                                "label_r": "成交状态",
                                "field_api_name": "deal_status",
                                "label": "成交状态",
                                "api_name": "deal_status",
                                "_id": "6247dca2ec3194000135e722",
                                "status": "released"
                        )
                )
        )
        serviceFacade.findObject(_ as String, "LeadsObj") >>> new ObjectDescribe(
                "tenant_id": "79337",
                "display_name_r": "销售线索",
                "display_name": "销售线索",
                "api_name": "LeadsObj",
                "fields": new HashMap<>(
                        "tel": new HashMap<>(
                                "describe_api_name": "LeadsObj",
                                "type": "phone_number",
                                "label_r": "电话",
                                "label": "电话",
                                "api_name": "tel",
                        ),
                        "marketing_event_id": new HashMap(
                                "describe_api_name": "LeadsObj",
                                "where_type": "field",
                                "type": "object_reference",
                                "label_r": "市场活动名称",
                                "label": "市场活动名称",
                                "target_api_name": "MarketingEventObj",
                                "target_related_list_name": "marketing_event_leads_list",
                                "target_related_list_label": "销售线索",
                                "api_name": "marketing_event_id",
                        )
                )
        )
        serviceFacade.findBySearchTemplateQueryWithFields(_ as IActionContext, "LeadsObj", _ as SearchTemplateQuery, _ as List<String>) >>> new QueryResult<IObjectData>(
                data: [new ObjectData(
                        "marketing_event_id": "64672adfb4647f0001cfa1f5",
                        "tel": "010-110",
                        "_id": "626b9c993300c7000138281f",
                        "marketing_event_id__r": "胡回家了"
                )])
        serviceFacade.batchFindTeamMember(_ as String, _ as Map<String, List<String>>) >>> [new HashMap<>(
                "AccountObj": new HashMap<>("62fb0416d5dc810001bbb286": [
                        new RelevantTeam(tenantId: "79337", memberType: 4, memberId: "5dc0db28e4b0311c1da8f644"),
                        new RelevantTeam(tenantId: "79337", memberType: 0, memberId: "1002"),
                        new RelevantTeam(tenantId: "79337", memberType: 0, memberId: "1003"),
                ])
        )]
        ltoOrgCommonService.getRoleUsersByRoleIds(_ as String, _ as List<String>, true) >>> [new HashMap<>("5dc0db28e4b0311c1da8f644": ["1001"])]
    }

    def "createTaskByTemplate"() {
        given:
        def util = new OperationsTaskUtil(serviceFacade, expressionService, ltoOrgCommonService)
        def template = new ObjectData(
                "tenant_id": "79337",
                "created_by": ["1000"],
                "task_name": "\${AccountObj.create_time}创建电话：\${AccountObj.leads_id.tel}，市场活动：\${AccountObj.leads_id.marketing_event_id}",
                "task_description": "单选\${AccountObj.deal_status}",
                "task_dead_line": "{AccountObj.create_time} + DAYS(1)",
                "task_reminds": ["8", "3", "4", "5"],
                "task_executors": new HashMap<>("person": ["1002", "1003"], "ext_process": ["owner", "data_group", "data_owner_leader"]),
                "task_cc": new HashMap<>("person": ["2019", "2168"], "ext_process": ["owner", "data_group", "data_owner_leader"]),
                "operations_activity_template_id": "661250265241ce000171206f",
                "name": "**********",
                "_id": "661250d95241ce00017124d0"
        )
        def dataList = [new ObjectData("_id": "62fb0416d5dc810001bbb286", "tenant_id": "79337", "create_time": *************, "deal_status": "2", "leads_id": "626b9c993300c7000138281f", "object_describe_api_name": "AccountObj")]
        expect:
        !util.createQXTaskByTemplate(template, dataList).isEmpty()
    }
}
