package com.facishare.crm.sfa.prm.platform.retry;

import com.facishare.crm.sfa.prm.platform.infrastructure.retry.RetryerFactory;
import org.junit.Test;

import java.io.IOException;
import java.util.concurrent.Callable;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.Assert.*;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-05-26
 * ============================================================
 */
public class RetryerTest {
    @Test
    public void testRetrySuccessAfterFailures() throws Exception {
        RetryerFactory factory = new RetryerFactory();
        AtomicInteger count = new AtomicInteger(0);

        String result = factory.<String>create()
                .maxAttempts(5)
                .retryOn(IOException.class)
                .onRetry(e -> System.out.println("Retrying due to: " + e.getMessage()))
                .execute(new Callable<String>() {
                    @Override
                    public String call() throws Exception {
                        if (count.incrementAndGet() < 3) {
                            throw new IOException("API 调用失败");
                        }
                        return "成功";
                    }
                })
                .get();

        assertEquals("成功", result);
        assertEquals(3, count.get());
    }

    @Test(expected = IOException.class)
    public void testRetryFailAllAttempts() throws Exception {
        RetryerFactory factory = new RetryerFactory();
        AtomicInteger count = new AtomicInteger(0);

        factory.create()
                .maxAttempts(3)
                .retryOn(IOException.class)
                .onRetry(e -> System.out.println("重试中: " + e.getMessage()))
                .execute(() -> {
                    count.incrementAndGet();
                    throw new IOException("一直失败");
                })
                .get();
    }

    @Test
    public void testNoRetryOnUnexpectedException() {
        RetryerFactory factory = new RetryerFactory();
        AtomicInteger count = new AtomicInteger(0);

        try {
            factory.create()
                    .maxAttempts(3)
                    .retryOn(IOException.class)
                    .execute(() -> {
                        count.incrementAndGet();
                        throw new IllegalArgumentException("不会重试这个异常");
                    })
                    .get();
        } catch (Exception e) {
            assertEquals(1, count.get());
            assertEquals("不会重试这个异常", e.getMessage());
        }
    }
}