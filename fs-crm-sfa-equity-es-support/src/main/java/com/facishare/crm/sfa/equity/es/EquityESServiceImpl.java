package com.facishare.crm.sfa.equity.es;

import com.facishare.paas.metadata.dao.pg.entity.metadata.Describe;
import com.facishare.paas.metadata.dao.pg.entity.metadata.Field;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.util.ObjectConvertUtil;
import com.facishare.paas.service.model.ObjectDataDocument;
import com.fxiaoke.elasticsearch.rest.AutoConfESClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.mongodb.util.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.elasticsearch.action.DocWriteResponse;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.delete.DeleteResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.DeleteByQueryRequest;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> gongchunru
 * @date : 2023/6/30 10:22
 * @description:
 */

@Slf4j
@Component
public class EquityESServiceImpl implements EquityESService {
    private AutoConfESClient client;

    @PostConstruct
    void init() {
        client = new AutoConfESClient();
        client.setConfigName("fs-oss-es01");
        client.init();
    }


    @Override
    public void saveSingle(Map<String, Object> data) {

    }

    @Override
    public void saveBatch(BulkRequest bulkRequest) {
        BulkResponse bulkResponse = null;
        try {
            bulkResponse = client.bulk(bulkRequest, RequestOptions.DEFAULT);
            if (bulkResponse.hasFailures()) {
                log.error("saveBatch error: {}", bulkResponse.buildFailureMessage());
            }
        } catch (IOException e) {
            log.error("saveBatch error", e);
        }
    }

    @Override
    public void saveBatch(List<Document> docs) {
        BulkRequest bulkRequest = new BulkRequest();
        for (Document doc : docs) {
            IndexRequest indexRequest = convertToIndexRequest(doc, "account_tree");
            bulkRequest.add(indexRequest);
        }
        saveBatch(bulkRequest);
    }


    @Override
    public void upsertBatch(List<Document> docs) {
        BulkRequest bulkRequest = new BulkRequest();
        for (Document doc : docs) {
            UpdateRequest updateRequest = convertToUpsertRequest(doc, "account_tree");
            bulkRequest.add(updateRequest);
        }
        saveBatch(bulkRequest);
    }

    @Override
    public void deleteDocument(String id) {
        deleteDocument("account_tree", id);
    }

    public void deleteDocument(String index, String id) {
        DeleteRequest request = new DeleteRequest(index, id);
        try {
            DeleteResponse deleteResponse = client.delete(request, RequestOptions.DEFAULT);
            if (deleteResponse.getResult() == DocWriteResponse.Result.NOT_FOUND) {
                log.warn("Document not found: {}", id);
            }
        } catch (IOException e) {
            log.error("deleteDocument error", e);
        }
    }

    /**
     * 根据企业id删除
     * @param index 索引
     * @param enterpriseId 企业id
     */
    public void deleteByEnterpriseId(String index, String enterpriseId) {
        DeleteByQueryRequest request = new DeleteByQueryRequest(index);
        // 添加查询条件
        request.setQuery(QueryBuilders.termQuery("enterprise_id", enterpriseId));
        try {
            BulkByScrollResponse response = client.deleteByQuery(request, RequestOptions.DEFAULT);
            long deletedDocs = response.getDeleted(); // 获取被删除的文档数量
            log.info("deleteByEnterpriseId: {},docs:{}", enterpriseId, deletedDocs);
        } catch (IOException e) {
            log.info("deleteByEnterpriseId error", e);
        }
    }

    private IndexRequest convertToIndexRequest(Document document, String indexName) {
        String id = document.getObjectId("_id").toHexString();
        document.remove("_id");
        String jsonString = document.toJson();
        return new IndexRequest(indexName).id(id).source(jsonString, XContentType.JSON);
    }

    private UpdateRequest convertToUpsertRequest(Document document, String indexName) {
        String id = document.getObjectId("_id").toHexString();
        document.remove("_id");
        String jsonString = document.toJson();
        Document parsed = Document.parse(jsonString);
        String parsedString = JSON.serialize(parsed);
        return new UpdateRequest(indexName, id)
                .doc(parsedString, XContentType.JSON)
                .docAsUpsert(true);
    }

    private void saveData(String index, Map<String, Object> data) throws IOException {
        IndexRequest request = null;
        try {
            request = new IndexRequest(index).source(data);
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }
        IndexResponse response = client.index(request, RequestOptions.DEFAULT);
    }

    @Override
    public List<Map<String, Object>> queryEnterprise(String enterpriseId)  {
        SearchRequest searchRequest = new SearchRequest("account_tree");
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(QueryBuilders.termQuery("enterprise_id.value", enterpriseId));
        searchSourceBuilder.size(0);
        searchSourceBuilder.timeout(TimeValue.timeValueSeconds(30));
        searchRequest.source(searchSourceBuilder);
        // 执行查询请求
        SearchResponse searchResponse = null;
        try {
            searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        List<Map<String, Object>> results = new ArrayList<>();
        for (SearchHit hit : searchResponse.getHits().getHits()) {
            Map<String, Object> sourceAsMap = hit.getSourceAsMap();
            results.add(sourceAsMap);
        }
        return results;
    }


    private SearchResponse executeSearchRequest(SearchRequest searchRequest) {
        try {
            return client.search(searchRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            throw new RuntimeException("Failed to execute search request.", e);
        }
    }


    /**
     * 根据节点查询父节点
     *
     * @param enterpriseId
     * @return
     * @throws IOException
     */
    @Override
    public List<ObjectDataDocument> queryParentEnterprise(String enterpriseId, Describe objectDescribe)  {
        if (objectDescribe == null) {
            return Collections.emptyList();
        }
        SearchRequest searchRequest = new SearchRequest("account_tree");
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(QueryBuilders.termQuery("enterprise_id.keyword", enterpriseId));
        searchSourceBuilder.size(1000);
        searchSourceBuilder.timeout(TimeValue.timeValueSeconds(30));
        searchRequest.source(searchSourceBuilder);
        // 执行查询请求
        SearchResponse searchResponse = executeSearchRequest(searchRequest);
        List<String> parentIds = Lists.newArrayList();
        for (SearchHit hit : searchResponse.getHits().getHits()) {
            Map<String, Object> sourceAsMap = hit.getSourceAsMap();
            parentIds.add(sourceAsMap.get("parent_enterprise_id").toString());
        }
        searchSourceBuilder.query(QueryBuilders.termsQuery("enterprise_id.keyword", parentIds));
        searchRequest.source(searchSourceBuilder);
        searchResponse = executeSearchRequest(searchRequest);
        List<Map<String, Object>> results = new ArrayList<>();
        for (SearchHit hit : searchResponse.getHits().getHits()) {
            Map<String, Object> sourceAsMap = hit.getSourceAsMap();
            results.add(sourceAsMap);
        }
        Set<String> allFieldKeys = Sets.newHashSet();
        List<Map> newMapList = Lists.newArrayList();
        results.forEach(map -> {
            Map tmpMap = Maps.newHashMap();
            map.forEach((k, v) -> {
                if (v instanceof Map && ((Map<?, ?>) v).containsKey("$numberLong")) {
                    tmpMap.put(k, ((Map<?, ?>) v).get("$numberLong"));
                } else {
                    tmpMap.put(StringUtils.replace((String) k, "$", "."), v);
                }
            });
            newMapList.add(tmpMap);
            allFieldKeys.addAll(tmpMap.keySet());
        });
        List<Field> fields = allFieldKeys.stream().map(field -> objectDescribe.getFieldByApiName(field)).filter(Objects::nonNull).collect(Collectors.toList());
        List<ObjectData> dataList = ObjectConvertUtil.convertMapsToObjectDatas(newMapList, fields, false);
        List<ObjectDataDocument> documents = new ArrayList<>();
        for (ObjectData objectData : dataList) {
            documents.add(ObjectDataDocument.of(objectData));
        }
        return documents;
    }

    /**
     * 根据节点查询子节点
     *
     * @param enterpriseId
     * @return
     */
    @Override
    public List<Map<String, Object>> queryChildEnterprise(String enterpriseId){
        SearchRequest searchRequest = new SearchRequest("account_tree");
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(QueryBuilders.termQuery("parent_enterprise_id.value", enterpriseId));
        searchSourceBuilder.size(1000);
        searchSourceBuilder.timeout(TimeValue.timeValueSeconds(30));
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = null;
        try {
            searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        List<Map<String, Object>> results = new ArrayList<>();
        for (SearchHit hit : searchResponse.getHits().getHits()) {
            Map<String, Object> sourceAsMap = hit.getSourceAsMap();
            results.add(sourceAsMap);
        }
        return results;
    }

    /**
     * 跟进企业id查询路径节点
     *
     * @param enterpriseId
     * @return 路径节点
     */
    @Override
    public List<Map<String, Object>> queryPathEnterprise(String enterpriseId){
        SearchRequest searchRequest = new SearchRequest("account_tree");
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(QueryBuilders.termQuery("root_node_path.keyword", enterpriseId));
        searchSourceBuilder.size(1000);
        searchSourceBuilder.timeout(TimeValue.timeValueSeconds(30));
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = null;
        try {
            searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        List<Map<String, Object>> results = new ArrayList<>();
        for (SearchHit hit : searchResponse.getHits().getHits()) {
            Map<String, Object> sourceAsMap = hit.getSourceAsMap();
            results.add(sourceAsMap);
        }
        return results;
    }


}

