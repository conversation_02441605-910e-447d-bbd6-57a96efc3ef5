package com.facishare.crm.sfa.equity.module;

import com.facishare.paas.service.model.ObjectDataDocument;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> gongchunru
 * @date : 2023/7/13 16:29
 * @description:
 */
public interface EquityRelationShipQueryES {

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    class Arg {
        public String enterpriseId;
        public String tenantId;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    class EquityResult {
        private boolean success;
        private String message;
        private List<ObjectDataDocument> dataList;
        private Integer errorCode;
    }
}
