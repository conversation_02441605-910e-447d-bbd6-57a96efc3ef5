package com.facishare.crm.sfa.equity.es;

import com.facishare.paas.metadata.dao.pg.entity.metadata.Describe;
import com.facishare.paas.service.model.ObjectDataDocument;
import org.bson.Document;
import org.elasticsearch.action.bulk.BulkRequest;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> gongchunru
 * @date : 2023/7/7 20:52
 * @description:
 */
public interface EquityESService {

    void saveSingle(Map<String, Object> data);

    void saveBatch(BulkRequest bulkRequest);

    void saveBatch(List<Document> docs);

    void upsertBatch(List<Document> docs);

    void deleteDocument(String id);

    List<Map<String, Object>> queryEnterprise(String enterpriseId);

    List<ObjectDataDocument> queryParentEnterprise(String enterpriseId, Describe objectDescribe);

    List<Map<String, Object>> queryChildEnterprise(String enterpriseId);

    List<Map<String, Object>> queryPathEnterprise(String enterpriseId);

}
